#!/bin/sh

#PBS -q gpu_h200_pinaki
#PBS -l select=1:ngpus=1
#PBS -l walltime=1440:00:00
#PBS -P gs_spms_psengupta
#PBS -N kitaev-crbm-transformer
#PBS -j oe

# 进入工作目录
cd $PBS_O_WORKDIR || exit $?

# 记录作业开始时间和节点信息
echo "Job start at: $(date)"
echo "Running on node: $(hostname)"
echo "GPU Information:"
nvidia-smi

# ==================== 环境配置 ====================
# conda环境名称
CONDA_ENV="netket"

# GPU设备
export CUDA_VISIBLE_DEVICES="0"

# 模块加载配置
ANACONDA_MODULE="anaconda2025/2025"
UNLOAD_CUDA_MODULE="cuda/12.2"

# 加载必要的模块
echo "Loading modules..."
module load $ANACONDA_MODULE
# 注意：anaconda2025会自动加载cuda/12.2作为依赖
# 如果需要其他CUDA版本，请先卸载cuda/12.2再加载所需版本
module unload $UNLOAD_CUDA_MODULE 2>/dev/null || true

# 初始化conda并激活环境
eval "$(/usr/local/anaconda2025/bin/conda shell.bash hook)"
conda activate $CONDA_ENV

# 设置GPU设备
echo "Using GPU device: $CUDA_VISIBLE_DEVICES"

# 验证环境
echo "Python path: $(which python)"
echo "Python version: $(python --version)"
echo "Current conda environment: $CONDA_DEFAULT_ENV"

echo "==================== cRBM-Transformer 混合架构训练 ===================="
echo "Starting cRBM-Transformer hybrid architecture training..."

# ==================== Checkpoint 配置 ====================
# 是否启用checkpoint保存 (true/false)
ENABLE_CHECKPOINT=true

# checkpoint保存间隔（迭代次数）
CHECKPOINT_INTERVAL=500

# 从checkpoint恢复训练的路径（可选）
# 示例：results/L4x4_K1.01.01.0_J0.0_h0.10.10.1_L0.0/2025-06-17/04-12_pretrain_no_symm/Job_0/checkpoints
# 留空表示从头开始训练
RESUME_FROM_CHECKPOINT=""

# 是否保留历史checkpoint (true/false)
KEEP_CHECKPOINT_HISTORY=true

# 构建checkpoint相关的命令行参数
CHECKPOINT_ARGS=""
if [ "$ENABLE_CHECKPOINT" = "true" ]; then
    CHECKPOINT_ARGS="training.checkpoint.enable=true training.checkpoint.save_interval=$CHECKPOINT_INTERVAL training.checkpoint.keep_history=$KEEP_CHECKPOINT_HISTORY"
    
    if [ -n "$RESUME_FROM_CHECKPOINT" ]; then
        echo "Resuming from checkpoint: $RESUME_FROM_CHECKPOINT"
        CHECKPOINT_ARGS="$CHECKPOINT_ARGS training.checkpoint.resume_from=$RESUME_FROM_CHECKPOINT"
    fi
    
    echo "Checkpoint enabled with interval: $CHECKPOINT_INTERVAL"
else
    CHECKPOINT_ARGS="training.checkpoint.enable=false"
    echo "Checkpoint disabled"
fi

# ==================== 模型配置 ====================
# 可选择的模型类型: cRBM, ViT, CTWF, GNNViT, cRBM_Transformer
MODEL_TYPE=cRBM_Transformer

# 根据模型类型设置实验名称
EXPERIMENT_NAME="crbm_transformer_train"

echo "Using model: $MODEL_TYPE"
echo "Experiment name: $EXPERIMENT_NAME"

# 使用conda环境直接运行训练脚本
python -m src.train --multirun \
    model=$MODEL_TYPE \
    system=kitaev \
    training.experiment_name=$EXPERIMENT_NAME \
    training.gpu_mesh_shape=[1,1] \
    $CHECKPOINT_ARGS

echo "cRBM-Transformer训练完成！混合架构模型已保存。"
echo "Job finished at: $(date)" 