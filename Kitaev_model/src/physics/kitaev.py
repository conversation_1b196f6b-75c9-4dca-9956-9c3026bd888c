import netket as nk
import numpy as np
import jax.numpy as jnp
from netket.utils.group.planar import C
import matplotlib.pyplot as plt

def create_honeycomb_lattice(Lx, Ly):
    """
    创建蜂窝晶格
    
    Args:
        Lx: x方向单元格数量
        Ly: y方向单元格数量
        
    Returns:
        netket晶格对象
    """
    # 定义自定义边
    custom_edges = [
        (0, 1, [0.5, 1.0/(2.0*jnp.sqrt(3.0))], 0),
        (0, 1, [-0.5, 1.0/(2.0*jnp.sqrt(3.0))], 1),
        (0, 1, [0.0, -1.0/(jnp.sqrt(3.0))], 2),
    ]

    # 创建蜂窝晶格
    lattice = nk.graph.Lattice(
        basis_vectors=[[1.0, 0.0], [0.5, jnp.sqrt(3.0)/2.0]], 
        extent=(Lx, Ly),
        site_offsets=[[0.5, 1.0/(2.0*jnp.sqrt(3.0))], [1.0, (1.0/jnp.sqrt(3.0))]], 
        custom_edges=custom_edges,
        pbc=[True, True]
    )
    
    return lattice

def create_kitaev_hamiltonian(lattice, Kx, Ky, Kz, J, hx, hy, hz, Lambda):
    """
    创建Kitaev-Heisenberg模型哈密顿量
    
    Args:
        lattice: 蜂窝晶格
        Kx, Ky, Kz: Kitaev相互作用强度
        J: Heisenberg相互作用强度  
        hx, hy, hz: [111]方向磁场
        Lambda: plaquette算符的拉格朗日乘子
        
    Returns:
        hamiltonian: 哈密顿量
        hilbert: 希尔伯特空间
    """
    # 希尔伯特空间，自旋1/2
    hilbert = nk.hilbert.Spin(s=1/2, N=lattice.n_nodes)

    # 自旋算符
    sigmax = [[0, 0.5], [0.5, 0]]
    sigmay = [[0, -0.5j], [0.5j, 0]] 
    sigmaz = [[0.5, 0], [0, -0.5]]

    sigmax_1 = [[0, 1.0], [1.0, 0]]
    sigmay_1 = [[0, -1.0j], [1.0j, 0]] 
    sigmaz_1 = [[1.0, 0], [0, -1.0]]

    # 两格点相互作用算符
    sigmaxi_sigmaxj = np.kron(sigmax, sigmax)
    sigmayi_sigmayj = np.kron(sigmay, sigmay)
    sigmazi_sigmazj = np.kron(sigmaz, sigmaz)

    # Heisenberg相互作用
    SiSj = sigmaxi_sigmaxj + sigmayi_sigmayj + sigmazi_sigmazj

    # Kitaev相互作用的键算符
    bond_operator = [
        (Kx*sigmaxi_sigmaxj - J*SiSj).tolist(),
        (Ky*sigmayi_sigmayj - J*SiSj).tolist(),
        (Kz*sigmazi_sigmazj - J*SiSj).tolist(),
    ]   

    bond_color = [0, 1, 2]

    # 单格点磁场算符
    site_operator = [(-hx*np.array(sigmax) - hy*np.array(sigmay) - hz*np.array(sigmaz)).tolist()]

    # 主哈密顿量：Kitaev-Heisenberg模型 + 磁场
    H0 = nk.operator.GraphOperator(hilbert, graph=lattice, bond_ops=bond_operator, 
                                   site_ops=site_operator, bond_ops_colors=bond_color)

    # plaquette算符约束
    if Lambda != 0.0:
        Hp = create_plaquette_operator(hilbert, lattice.extent[0], lattice.extent[1])
        # 总哈密顿量 = 主项 - λ*plaquette约束
        H = H0 - Lambda * Hp
    else:
        H = H0
    H = H.to_jax_operator()
    return H, hilbert

def create_plaquette_operator(hilbert, Lx, Ly):
    """
    创建plaquette算符 Hp = ∑_p W_p
    
    Args:
        hilbert: 希尔伯特空间
        Lx, Ly: 格子尺寸
        
    Returns:
        Hp: plaquette算符总和
    """
    # σ^y ⊗ σ^z ⊗ σ^x 乘积
    sigmay_1 = [[0, -1.0j], [1.0j, 0]] 
    sigmaz_1 = [[1.0, 0], [0, -1.0]]
    sigmax_1 = [[0, 1.0], [1.0, 0]]
    
    prodxyz = np.kron(np.kron(sigmay_1, sigmaz_1), sigmax_1)
    prodxyz2 = np.kron(prodxyz, prodxyz)

    Hp = nk.operator.LocalOperator(hilbert, dtype=jnp.complex128)

    for x in range(Lx):
        for y in range(Ly):
            # plaquette中的六个格点索引
            n1 = y + Ly*x
            n2 = y + Ly*((x+1) % Lx)
            n3 = y-1 + Ly*int(y==0) + Ly*((x+1) % Lx)
            n4 = y-1 + Ly*int(y==0) + Ly*x

            # 转换为实际格点索引
            in1 = 2*n1
            jn1 = 2*n1 + 1
            in2 = 2*n2
            jn3 = 2*n3 + 1
            in3 = 2*n3
            jn4 = 2*n4 + 1

            # 添加plaquette算符
            Wp = [prodxyz2]
            sites = [(in1, jn1, in2, jn3, in3, jn4)]
            Hp = Hp + nk.operator.LocalOperator(hilbert, Wp, sites, dtype=jnp.complex128)
    Hp = Hp.to_jax_operator()
    return Hp

def get_symmetries(lattice):
    """
    获取晶格对称性
    
    Args:
        lattice: 蜂窝晶格
        
    Returns:
        symmetries: 对称性群
    """
    # 使用C(2)点群对称性
    symmetries = lattice.space_group(C(2))
    return symmetries

def save_lattice_figure(lattice, filename="Honeycomb_lattice.png"):
    """
    保存晶格图像
    
    Args:
        lattice: 蜂窝晶格
        filename: 保存文件名
    """
    lattice.draw(edge_color='red')
    plt.savefig(filename)
    plt.close()  # 关闭图形以释放内存 