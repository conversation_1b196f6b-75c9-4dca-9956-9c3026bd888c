import numpy as np
import jax
import jax.numpy as jnp
import flax.linen as nn
from typing import Union, Any, Tuple
from netket.utils.types import NNInitFunc
from netket.nn import log_cosh
from jax.nn.initializers import normal, xavier_uniform

# 注意：硬编码的配置类已被移除
# 现在所有配置都从YAML文件读取 (configs/model/cRBM_Transformer.yaml)
# 这确保了配置的一致性和可维护性

default_kernel_init = normal(stddev=0.01)

class cRBMFeatureEncoder(nn.Module):
    """
    cRBM特征编码器
    利用相关受限玻尔兹曼机提取局部特征和最近邻关联子，但不直接输出，而是作为特征编码器
    """
    Lx: int                                      # x方向格点数
    Ly: int                                      # y方向格点数
    param_dtype: Any = jnp.complex128            # 参数数据类型
    activation: Any = log_cosh                   # 激活函数
    alpha: Union[float, int] = 4                 # 隐藏单元比例
    use_hidden_bias: bool = True                 # 是否使用隐藏偏置
    use_visible_bias: bool = True                # 是否使用可见偏置
    kernel_init: NNInitFunc = default_kernel_init
    hidden_bias_init: NNInitFunc = default_kernel_init
    visible_bias_init: NNInitFunc = default_kernel_init

    @nn.compact
    def __call__(self, input):
        """
        提取cRBM特征
        Args:
            input: 输入自旋构型 [batch_size, n_sites]
        Returns:
            features: cRBM特征 [batch_size, feature_dim]
            correlators: 关联子特征 [batch_size, correlator_dim]
        """
        input_b = input
        l_b = jnp.size(input_b, axis=0)  # batch_size
        
        # 重塑为二维格点阵列 [batch_size, Lx, 2*Ly]
        input_c = jnp.reshape(input_b, (l_b, self.Lx, 2 * self.Ly))
        
        # 转置为 [batch_size, 2*Ly, Lx]
        input_d = jnp.transpose(input_c, (0, 2, 1))
        l_d = jnp.size(input_d, axis=0)
        
        # 重塑为 [batch_size, Ly, 2*Lx]
        input_e = jnp.reshape(input_d, (l_d, self.Ly, 2 * self.Lx))
        
        # 对最后一个维度排序
        input_f = jnp.sort(input_e, axis=-1)

        # 计算x方向的最近邻关联子
        input_c_roll = jnp.roll(input_c, -1, axis=-1)
        input_cc_roll = input_c * input_c_roll
        input_cc_roll_final = jnp.reshape(input_cc_roll, (l_b, 2 * self.Lx * self.Ly))

        # 计算y方向的最近邻关联子
        input_f_roll = jnp.roll(input_f, -1, axis=-1)
        input_ff_roll = input_f * input_f_roll
        input_ff_roll_final = jnp.reshape(input_ff_roll, (l_b, 2 * self.Lx * self.Ly))

        # 删除部分关联子以避免重复计数
        input_ff_roll_final_2 = jnp.delete(input_ff_roll_final, slice(None, None, 2), axis=1)

        # 合并所有关联子
        input_final_nn_correlators = jnp.concatenate((input_cc_roll_final, input_ff_roll_final_2), axis=-1)

        # 合并原始自旋和关联子
        input_final = jnp.concatenate((input_b, input_final_nn_correlators), axis=-1)

        # 隐藏层特征提取
        hidden_features = nn.Dense(
            name="cRBM_hidden",
            features=int(self.alpha * input_final.shape[-1]),
            param_dtype=self.param_dtype,
            use_bias=self.use_hidden_bias,
            kernel_init=self.kernel_init,
            bias_init=self.hidden_bias_init,
        )(input_final)
        
        hidden_features = self.activation(hidden_features)
        
        # 返回隐藏特征和关联子特征用于后续处理
        return hidden_features, input_final_nn_correlators

class GeometricEncoder(nn.Module):
    """
    几何结构编码器
    编码蜂窝晶格的几何信息，包括子格位置、键类型、plaquette结构等
    """
    Lx: int
    Ly: int
    d_model: int
    param_dtype: Any = jnp.complex128
    
    def setup(self):
        # 子格位置编码
        self.sublattice_embedding = nn.Embed(
            num_embeddings=2,  # A, B两个子格
            features=self.d_model // 4,
            param_dtype=self.param_dtype,
            dtype=self.param_dtype
        )
        
        # 键类型编码 (三种Kitaev键: x, y, z)
        self.bond_type_embedding = nn.Embed(
            num_embeddings=3,  # 三种键类型
            features=self.d_model // 4,
            param_dtype=self.param_dtype,
            dtype=self.param_dtype
        )
        
        # 位置编码
        self.position_embedding = self.param(
            'position_encoding',
            xavier_uniform(),
            (2 * self.Lx * self.Ly, self.d_model // 2),
            self.param_dtype
        )

    def __call__(self, input):
        """
        Args:
            input: [batch_size, n_sites] 
        Returns:
            geometric_features: [batch_size, n_sites, d_model] 几何编码特征
        """
        batch_size, n_sites = input.shape
        
        # 创建子格指示符 (A=0, B=1交替)
        sublattice_indices = jnp.arange(n_sites) % 2
        sublattice_features = self.sublattice_embedding(sublattice_indices)  # [n_sites, d_model//4]
        
        # 创建键类型指示符 (根据位置确定键类型)
        site_positions = jnp.arange(n_sites)
        x_coords = site_positions // (2 * self.Ly)
        y_coords = (site_positions % (2 * self.Ly)) // 2
        bond_types = (x_coords + y_coords) % 3  # 三种键类型
        bond_features = self.bond_type_embedding(bond_types)  # [n_sites, d_model//4]
        
        # 位置编码
        position_features = self.position_embedding[site_positions]  # [n_sites, d_model//2]
        
        # 合并所有几何特征
        geometric_features = jnp.concatenate([
            sublattice_features, bond_features, position_features
        ], axis=-1)  # [n_sites, d_model]
        
        # 扩展到batch维度
        geometric_features = jnp.tile(geometric_features[None, :, :], (batch_size, 1, 1))
        
        return geometric_features

class PlaquetteAttention(nn.Module):
    """
    Plaquette注意力机制
    专门处理Kitaev模型中的六格点plaquette结构和约束
    """
    Lx: int
    Ly: int
    d_model: int
    num_heads: int = 4
    param_dtype: Any = jnp.complex128
    
    def setup(self):
        assert self.d_model % self.num_heads == 0
        self.head_dim = self.d_model // self.num_heads
        
        # 注意力投影
        self.q_proj = nn.Dense(self.d_model, param_dtype=self.param_dtype, dtype=self.param_dtype, use_bias=False)
        self.k_proj = nn.Dense(self.d_model, param_dtype=self.param_dtype, dtype=self.param_dtype, use_bias=False)
        self.v_proj = nn.Dense(self.d_model, param_dtype=self.param_dtype, dtype=self.param_dtype, use_bias=False)
        self.out_proj = nn.Dense(self.d_model, param_dtype=self.param_dtype, dtype=self.param_dtype, use_bias=False)
        
        # Plaquette掩码矩阵 - 只有同一plaquette内的格点可以互相注意
        self.plaquette_mask = self._create_plaquette_mask()
    
    def _create_plaquette_mask(self):
        """创建plaquette掩码矩阵，只允许同一plaquette内的格点互相注意"""
        n_sites = 2 * self.Lx * self.Ly
        mask = jnp.zeros((n_sites, n_sites), dtype=bool)
        
        for x in range(self.Lx):
            for y in range(self.Ly):
                # 计算plaquette中的六个格点索引 (与kitaev.py中相同)
                n1 = y + self.Ly * x
                n2 = y + self.Ly * ((x + 1) % self.Lx)
                n3 = (y - 1 + self.Ly) % self.Ly + self.Ly * ((x + 1) % self.Lx)
                n4 = (y - 1 + self.Ly) % self.Ly + self.Ly * x
                
                # 转换为实际格点索引
                plaquette_sites = [
                    2 * n1, 2 * n1 + 1,  # 格点对(n1)
                    2 * n2, 2 * n3 + 1,  # 相邻格点
                    2 * n3, 2 * n4 + 1   # 完成六边形
                ]
                
                # 在plaquette内部设置注意力掩码
                for i in plaquette_sites:
                    for j in plaquette_sites:
                        if i < n_sites and j < n_sites:
                            mask = mask.at[i, j].set(True)
        
        return mask
    
    def __call__(self, x):
        """
        Args:
            x: [batch_size, n_sites, d_model]
        Returns:
            attended_features: [batch_size, n_sites, d_model]
        """
        batch_size, n_sites, d_model = x.shape
        
        # 多头投影
        q = self.q_proj(x).reshape(batch_size, n_sites, self.num_heads, self.head_dim)
        k = self.k_proj(x).reshape(batch_size, n_sites, self.num_heads, self.head_dim)
        v = self.v_proj(x).reshape(batch_size, n_sites, self.num_heads, self.head_dim)
        
        # 转置为 [batch_size, num_heads, n_sites, head_dim]
        q = jnp.transpose(q, (0, 2, 1, 3))
        k = jnp.transpose(k, (0, 2, 1, 3))
        v = jnp.transpose(v, (0, 2, 1, 3))
        
        # 计算注意力分数
        scores = jnp.einsum('bhid,bhjd->bhij', q, k) / jnp.sqrt(self.head_dim)
        
        # 应用plaquette掩码 - 只允许同一plaquette内的注意力
        mask_value = -1e9 if self.param_dtype == jnp.float64 else -1e4
        masked_scores = jnp.where(self.plaquette_mask[None, None, :, :], scores, mask_value)
        
        # 应用softmax
        attention_weights = nn.softmax(masked_scores, axis=-1)
        
        # 应用注意力权重
        attended = jnp.einsum('bhij,bhjd->bhid', attention_weights, v)
        
        # 合并多头
        attended = jnp.transpose(attended, (0, 2, 1, 3))
        attended = attended.reshape(batch_size, n_sites, d_model)
        
        return self.out_proj(attended)

class GlobalTransformerLayer(nn.Module):
    """
    全局Transformer层，处理长程相关性
    """
    d_model: int
    num_heads: int
    mlp_hidden_dim: int
    param_dtype: Any = jnp.complex128
    
    def setup(self):
        # 多头自注意力
        self.self_attention = nn.MultiHeadDotProductAttention(
            num_heads=self.num_heads,
            dtype=self.param_dtype,
            param_dtype=self.param_dtype,
            kernel_init=xavier_uniform(),
            qkv_features=self.d_model,
            out_features=self.d_model
        )
        
        # MLP
        self.mlp = nn.Sequential([
            nn.Dense(self.mlp_hidden_dim, param_dtype=self.param_dtype, dtype=self.param_dtype),
            lambda x: log_cosh(x),
            nn.Dense(self.d_model, param_dtype=self.param_dtype, dtype=self.param_dtype)
        ])
        
        # 层归一化
        self.layer_norm1 = nn.LayerNorm(dtype=self.param_dtype, param_dtype=self.param_dtype)
        self.layer_norm2 = nn.LayerNorm(dtype=self.param_dtype, param_dtype=self.param_dtype)
    
    def __call__(self, x):
        # 自注意力 + 残差连接
        normed_x1 = self.layer_norm1(x)
        attended_x = self.self_attention(normed_x1)
        x = x + attended_x
        
        # MLP + 残差连接
        normed_x2 = self.layer_norm2(x)
        mlp_x = self.mlp(normed_x2)
        x = x + mlp_x
        
        return x

class PhysicsConstrainedOutput(nn.Module):
    """
    物理约束输出层
    确保输出满足Kitaev模型的物理约束，包括量子态的归一化和对称性
    """
    d_model: int
    param_dtype: Any = jnp.complex128
    
    def setup(self):
        # 投影到复数输出
        self.real_proj = nn.Dense(1, param_dtype=self.param_dtype, dtype=self.param_dtype,
                                  kernel_init=normal(stddev=0.01))
        self.imag_proj = nn.Dense(1, param_dtype=self.param_dtype, dtype=self.param_dtype,
                                  kernel_init=normal(stddev=0.01))
        
        # 全局特征聚合
        self.global_pool = nn.Dense(self.d_model, param_dtype=self.param_dtype, dtype=self.param_dtype)
    
    def __call__(self, x):
        """
        Args:
            x: [batch_size, n_sites, d_model]
        Returns:
            log_psi: [batch_size,] 复数对数波函数
        """
        # 全局特征聚合
        pooled_features = jnp.mean(x, axis=1)  # [batch_size, d_model]
        global_features = self.global_pool(pooled_features)
        global_features = log_cosh(global_features)
        
        # 投影到实部和虚部
        real_part = jnp.squeeze(self.real_proj(global_features), axis=-1)  # [batch_size,]
        imag_part = jnp.squeeze(self.imag_proj(global_features), axis=-1)  # [batch_size,]
        
        # 限制幅度以保证数值稳定性
        real_part = jnp.clip(real_part, -10.0, 10.0)
        imag_part = jnp.clip(imag_part, -10.0, 10.0)
        
        # 组合成复数对数波函数
        log_psi = real_part + 1j * imag_part
        
        return log_psi

class cRBMTransformer(nn.Module):
    """
    cRBM-Transformer混合架构
    
    专门为Kitaev模型设计的神经量子态，结合了：
    1. cRBM的局部特征提取能力
    2. 几何结构编码的物理先验
    3. Plaquette注意力的约束编码
    4. 全局Transformer的长程相关性建模
    5. 物理约束的输出层
    
    架构流程：
    Input → cRBM特征编码 → 几何编码 → 特征融合 → Plaquette注意力 → 全局Transformer层们 → 物理约束输出 → log ψ
    """
    # 物理参数
    Lx: int                                      # x方向格点数
    Ly: int                                      # y方向格点数
    
    # cRBM参数
    alpha: Union[float, int] = 4                 # cRBM隐藏单元比例
    
    # Transformer参数  
    d_model: int = 64                            # 模型维度
    num_transformer_layers: int = 4              # Transformer层数
    num_heads: int = 8                           # 注意力头数
    mlp_hidden_dim: int = 256                    # MLP隐藏层维度
    
    # 通用参数
    param_dtype: Any = jnp.complex128            # 参数数据类型
    use_hidden_bias: bool = True                 # cRBM隐藏偏置
    use_visible_bias: bool = True                # cRBM可见偏置
    
    def setup(self):
        # cRBM特征编码器
        self.crbm_encoder = cRBMFeatureEncoder(
            Lx=self.Lx,
            Ly=self.Ly,
            param_dtype=self.param_dtype,
            alpha=self.alpha,
            use_hidden_bias=self.use_hidden_bias,
            use_visible_bias=self.use_visible_bias
        )
        
        # 几何结构编码器
        self.geometric_encoder = GeometricEncoder(
            Lx=self.Lx,
            Ly=self.Ly,
            d_model=self.d_model,
            param_dtype=self.param_dtype
        )
        
        # 特征融合层
        self.feature_fusion = nn.Dense(
            self.d_model,
            param_dtype=self.param_dtype,
            dtype=self.param_dtype,
            kernel_init=xavier_uniform()
        )
        
        # Plaquette注意力层
        self.plaquette_attention = PlaquetteAttention(
            Lx=self.Lx,
            Ly=self.Ly,
            d_model=self.d_model,
            num_heads=min(self.num_heads, 4),  # Plaquette注意力使用较少头数
            param_dtype=self.param_dtype
        )
        
        # 多层全局Transformer
        self.transformer_layers = [
            GlobalTransformerLayer(
                d_model=self.d_model,
                num_heads=self.num_heads,
                mlp_hidden_dim=self.mlp_hidden_dim,
                param_dtype=self.param_dtype
            ) for _ in range(self.num_transformer_layers)
        ]
        
        # 最终归一化
        self.final_norm = nn.LayerNorm(dtype=self.param_dtype, param_dtype=self.param_dtype)
        
        # 物理约束输出层
        self.output_layer = PhysicsConstrainedOutput(
            d_model=self.d_model,
            param_dtype=self.param_dtype
        )

    def __call__(self, input):
        """
        前向传播
        Args:
            input: 输入自旋构型 [batch_size, n_sites]
        Returns:
            对数波函数值（复数）[batch_size,]
        """
        batch_size, n_sites = input.shape
        
        # 1. cRBM特征提取
        crbm_features, correlators = self.crbm_encoder(input)  # [batch_size, crbm_dim], [batch_size, corr_dim]
        
        # 2. 几何结构编码
        geometric_features = self.geometric_encoder(input)  # [batch_size, n_sites, d_model]
        
        # 3. 特征融合：将cRBM特征投影到每个格点
        # 扩展cRBM特征到每个格点
        crbm_per_site = jnp.tile(crbm_features[:, None, :], (1, n_sites, 1))  # [batch_size, n_sites, crbm_dim]
        correlators_per_site = jnp.tile(correlators[:, None, :], (1, n_sites, 1))  # [batch_size, n_sites, corr_dim]
        
        # 融合所有特征
        combined_features = jnp.concatenate([
            crbm_per_site, correlators_per_site, geometric_features
        ], axis=-1)  # [batch_size, n_sites, total_dim]
        
        # 投影到统一维度
        fused_features = self.feature_fusion(combined_features)  # [batch_size, n_sites, d_model]
        fused_features = log_cosh(fused_features)
        
        # 4. Plaquette注意力处理
        plaquette_features = self.plaquette_attention(fused_features)
        fused_features = fused_features + plaquette_features  # 残差连接
        
        # 5. 全局Transformer层
        x = fused_features
        for transformer_layer in self.transformer_layers:
            x = transformer_layer(x)
        
        # 6. 最终归一化
        x = self.final_norm(x)
        
        # 7. 物理约束输出
        log_psi = self.output_layer(x)
        
        return log_psi

