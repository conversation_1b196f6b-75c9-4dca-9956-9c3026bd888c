import jax
import jax.numpy as jnp
import flax.linen as nn
import math
from netket.nn import log_cosh
from jax._src import dtypes

# 注意：硬编码的配置类已被移除
# 现在所有配置都从YAML文件读取 (configs/model/ViT.yaml)
# 这确保了配置的一致性和可维护性
    


class PatchEmbedding(nn.Module):
    """Patch嵌入层：将格点patch映射为高维向量"""
    d_model: int
    patch_size: int
    param_dtype: jnp.dtype = jnp.float64

    def setup(self):
        self.patch_projection = nn.Dense(
            self.d_model,
            param_dtype=self.param_dtype,
            dtype=self.param_dtype,
            kernel_init=nn.initializers.xavier_uniform()
        )

    def __call__(self, x):
        """
        Args:
            x: [batch_size, n_sites] 格点构型
        Returns:
            [batch_size, n_patches, d_model] patch嵌入后的张量
        """
        batch_size, n_sites = x.shape
        
        # 确保n_sites能被patch_size整除
        assert n_sites % self.patch_size == 0, f"n_sites ({n_sites}) must be divisible by patch_size ({self.patch_size})"
        
        n_patches = n_sites // self.patch_size
        
        # 重塑为patches: [batch_size, n_sites] -> [batch_size, n_patches, patch_size]
        x_patches = x.reshape(batch_size, n_patches, self.patch_size)
        
        # 对每个patch进行嵌入
        embedded = self.patch_projection(x_patches)
        
        return embedded

class RelativePositionalEncoding(nn.Module):
    """相对位置编码模块"""
    n_patches: int
    param_dtype: jnp.dtype = jnp.float64

    def setup(self):
        self.pos_encoding = self.param(
            'pos_encoding',
            nn.initializers.xavier_uniform(),
            (self.n_patches, self.n_patches),
            self.param_dtype
        )

    def __call__(self):
        return self.pos_encoding

class MultiHeadSelfAttention(nn.Module):
    """多头自注意力模块，带相对位置编码和数值稳定性改进"""
    d_model: int
    heads: int
    n_patches: int
    param_dtype: jnp.dtype = jnp.float64

    def setup(self):
        assert self.d_model % self.heads == 0
        self.d_head = self.d_model // self.heads
        
        # Q, K, V 线性变换层 - 使用更保守的初始化
        self.w_q = nn.Dense(self.d_model, param_dtype=self.param_dtype, dtype=self.param_dtype, 
                           use_bias=False, kernel_init=nn.initializers.xavier_uniform(in_axis=-1, out_axis=-1))
        self.w_k = nn.Dense(self.d_model, param_dtype=self.param_dtype, dtype=self.param_dtype, 
                           use_bias=False, kernel_init=nn.initializers.xavier_uniform(in_axis=-1, out_axis=-1))
        self.w_v = nn.Dense(self.d_model, param_dtype=self.param_dtype, dtype=self.param_dtype, 
                           use_bias=False, kernel_init=nn.initializers.xavier_uniform(in_axis=-1, out_axis=-1))
        
        # 相对位置编码
        self.rpe = RelativePositionalEncoding(n_patches=self.n_patches, param_dtype=self.param_dtype)
        
        # 输出投影 - 使用更小的初始化
        self.w_o = nn.Dense(self.d_model, param_dtype=self.param_dtype, dtype=self.param_dtype, 
                           use_bias=False, kernel_init=nn.initializers.xavier_uniform(in_axis=-1, out_axis=-1))

    def __call__(self, x):
        """
        Args:
            x: [batch_size, n_patches, d_model] 输入
        Returns:
            [batch_size, n_patches, d_model] 注意力输出
        """
        batch_size, n_patches, d_model = x.shape
        
        # 生成 Q, K, V
        q = self.w_q(x).reshape(batch_size, n_patches, self.heads, self.d_head)
        k = self.w_k(x).reshape(batch_size, n_patches, self.heads, self.d_head)
        v = self.w_v(x).reshape(batch_size, n_patches, self.heads, self.d_head)
        
        # 转置为 [batch_size, heads, n_patches, d_head]
        q = jnp.transpose(q, (0, 2, 1, 3))
        k = jnp.transpose(k, (0, 2, 1, 3))
        v = jnp.transpose(v, (0, 2, 1, 3))
        
        # 计算注意力分数：Q K^T + 相对位置编码
        scores = jnp.einsum('bhid,bhjd->bhij', q, k) / jnp.sqrt(self.d_head)
        
        # 添加相对位置编码，并进行数值稳定性处理
        rpe_scores = self.rpe()[None, None, :, :]
        # 限制相对位置编码的幅度，避免过大的梯度
        rpe_scores = jnp.clip(rpe_scores, -10.0, 10.0)
        scores = scores + rpe_scores
        
        # 进一步限制注意力分数，避免 softmax 溢出
        scores = jnp.clip(scores, -30.0, 30.0)
        
        # 应用 softmax 和注意力权重
        attention_weights = nn.softmax(scores, axis=-1)
        attention_output = jnp.einsum('bhij,bhjd->bhid', attention_weights, v)
        
        # 重塑并合并所有头
        attention_output = jnp.transpose(attention_output, (0, 2, 1, 3))
        attention_output = attention_output.reshape(batch_size, n_patches, d_model)
        
        # 应用dropout进行正则化（如果在训练模式）
        return self.w_o(attention_output)

class MLP(nn.Module):
    """标准的前馈神经网络（MLP）层"""
    d_model: int
    hidden_dim: int
    param_dtype: jnp.dtype = jnp.float64

    def setup(self):
        # 第一个线性变换：d_model -> hidden_dim
        self.dense1 = nn.Dense(
            self.hidden_dim,
            param_dtype=self.param_dtype,
            dtype=self.param_dtype,
            kernel_init=nn.initializers.xavier_uniform()
        )
        
        # 第二个线性变换：hidden_dim -> d_model
        self.dense2 = nn.Dense(
            self.d_model,
            param_dtype=self.param_dtype,
            dtype=self.param_dtype,
            kernel_init=nn.initializers.xavier_uniform()
        )

    def __call__(self, x):
        """
        Args:
            x: [batch_size, n_patches, d_model] 输入
        Returns:
            [batch_size, n_patches, d_model] MLP输出
        """
        # 第一层 + GELU激活
        x = self.dense1(x)
        x = log_cosh(x)
        
        # 第二层（回到原始维度）
        x = self.dense2(x)
        
        return x

class AttentionLayer(nn.Module):
    """注意力层：多头自注意力 + MLP + 残差连接 + 归一化（标准Transformer Block）"""
    d_model: int
    heads: int
    n_patches: int
    mlp_hidden_dim: int  # MLP隐藏层维度
    param_dtype: jnp.dtype = jnp.float64

    def setup(self):
        self.mhsa = MultiHeadSelfAttention(
            d_model=self.d_model,
            heads=self.heads,
            n_patches=self.n_patches,
            param_dtype=self.param_dtype
        )
        
        # MLP层（前馈网络）
        self.mlp = MLP(
            d_model=self.d_model,
            hidden_dim=self.mlp_hidden_dim,
            param_dtype=self.param_dtype
        )
        
        # 两个LayerNorm：一个用于注意力，一个用于MLP
        self.layer_norm1 = nn.LayerNorm(dtype=self.param_dtype, param_dtype=self.param_dtype)
        self.layer_norm2 = nn.LayerNorm(dtype=self.param_dtype, param_dtype=self.param_dtype)
        
        # 添加输出缩放因子以防止梯度爆炸
        self.attention_scale = 0.1
        self.mlp_scale = 0.1

    def __call__(self, x):
        # 第一个子层：Multi-Head Self-Attention + 残差连接
        normalized_x1 = self.layer_norm1(x)
        attention_output = self.mhsa(normalized_x1)
        x = x + self.attention_scale * attention_output
        
        # 第二个子层：MLP + 残差连接
        normalized_x2 = self.layer_norm2(x)
        mlp_output = self.mlp(normalized_x2)
        x = x + self.mlp_scale * mlp_output
        
        return x

class PairComplexOutput(nn.Module):
    """Pair Complex 输出层 - 改进数值稳定性"""
    d_model: int
    param_dtype: jnp.dtype = jnp.float64

    def setup(self):
        self.final_projection = nn.Dense(
            2,  # 实部和虚部贡献
            param_dtype=self.param_dtype,
            dtype=self.param_dtype,
            # 使用更小的初始化以防止输出过大
            kernel_init=nn.initializers.normal(stddev=0.01)
        )

    def __call__(self, x):
        """
        Args:
            x: [batch_size, n_patches, d_model] 注意力输出
        Returns:
            复数对数波函数值 [batch_size,]
        """
        # 线性投影到实部和虚部贡献
        paired_contrib = self.final_projection(x)  # [batch_size, n_patches, 2]
        
        # 分离并求和
        real_contrib = jnp.sum(paired_contrib[:, :, 0], axis=1)  # [batch_size,]
        imag_contrib = jnp.sum(paired_contrib[:, :, 1], axis=1)  # [batch_size,]
        
        # 限制输出幅度，避免过大的波函数值
        real_contrib = jnp.clip(real_contrib, -20.0, 20.0)
        imag_contrib = jnp.clip(imag_contrib, -20.0, 20.0)
        
        # 组合成复数对数波函数
        return real_contrib + 1j * imag_contrib

class ViTFNQS(nn.Module):
    """
    Vision Transformer for Neural Quantum States
    
    Attention + MLP + Pair Complex 架构的神经量子态模型
    
    架构流程：
    Input → Patch Embedding → Multi-layer (Attention + MLP) Blocks → Pair Complex Output → log ψ
    
    Args:
        num_layers: 注意力层数
        d_model: 嵌入维度和模型维度
        heads: 多头注意力的头数
        patch_size: patch大小
        n_sites: 系统中的格点数
        mlp_hidden_dim: MLP隐藏层维度倍数（实际维度 = d_model * mlp_hidden_dim，通常设为4）
        param_dtype: 参数数据类型
    """
    num_layers: int
    d_model: int
    heads: int
    patch_size: int
    n_sites: int
    mlp_hidden_dim: int = 4  # 默认为4倍扩张
    param_dtype: jnp.dtype = jnp.float64

    def setup(self):
        # 计算实际的MLP隐藏层维度
        actual_mlp_hidden_dim = self.d_model * self.mlp_hidden_dim
            
        # 计算patch数量
        assert self.n_sites % self.patch_size == 0, f"n_sites ({self.n_sites}) must be divisible by patch_size ({self.patch_size})"
        self.n_patches = self.n_sites // self.patch_size
        
        # Patch嵌入层
        self.patch_embedding = PatchEmbedding(
            d_model=self.d_model,
            patch_size=self.patch_size,
            param_dtype=self.param_dtype
        )
        
        # 多层注意力
        self.attention_layers = [
            AttentionLayer(
                d_model=self.d_model,
                heads=self.heads,
                n_patches=self.n_patches,
                mlp_hidden_dim=actual_mlp_hidden_dim,
                param_dtype=self.param_dtype
            ) for _ in range(self.num_layers)
        ]
        
        # 最终归一化
        self.final_norm = nn.LayerNorm(dtype=self.param_dtype, param_dtype=self.param_dtype)
        
        # Pair Complex 输出层
        self.pair_complex_output = PairComplexOutput(
            d_model=self.d_model,
            param_dtype=self.param_dtype
        )

    def __call__(self, input):
        """
        前向传播
        Args:
            input: 输入自旋构型 [batch_size, n_sites]
        Returns:
            对数波函数值（复数）[batch_size,]
        """
        # Patch嵌入
        x = self.patch_embedding(input)
        
        # 多层注意力处理
        for attention_layer in self.attention_layers:
            x = attention_layer(x)
        
        # 最终归一化
        x = self.final_norm(x)
        
        # Pair Complex 输出
        return self.pair_complex_output(x) 