# cRBM-Transformer 混合架构配置
# 专门为Kitaev模型设计的神经量子态架构
name: cRBM_Transformer

# 模型参数
model:
  # cRBM编码器参数
  alpha: 4                      # cRBM隐藏单元比例
  use_hidden_bias: true         # 是否使用cRBM隐藏偏置
  use_visible_bias: true        # 是否使用cRBM可见偏置
  
  # Transformer架构参数
  d_model: 64                   # 模型维度/嵌入维度
  num_transformer_layers: 4     # 全局Transformer层数
  num_heads: 8                  # 多头注意力头数
  mlp_hidden_dim: 256           # MLP隐藏层维度
  
  # 通用参数
  param_dtype: "complex128"     # 参数数据类型（适配Kitaev模型的复数特性）
  use_symmetries: true          # 是否使用对称性

# 训练参数
training:
  seed: 0
  learning_rate: 0.05           # 学习率（混合架构使用中等学习率）
  n_cycles: 6                   # 热重启周期数
  n_train: 1                    # 每次退火的训练步数
  n_samples: 6144               # 样本数量（介于cRBM和ViT之间）
  n_discard_per_chain: 0        # 每条链丢弃的样本数
  chunk_size: 1024              # 批处理大小
  diag_shift: 0.08              # 对角线位移（平衡数值稳定性）
  grad_clip: 0.8                # 梯度裁剪阈值
  use_model_sharding: true      # 默认启用模型分片
  
  # 热重启余弦退火参数
  initial_period: 100           # 初始周期长度
  period_mult: 2.0              # 周期倍数
  max_temperature: 1.0          # 最大温度
  min_temperature: 0.0          # 最小温度

# 架构特定说明
architecture_description: |
  cRBM-Transformer混合架构结合了：
  
  1. cRBM特征编码器：
     - 提取局部相关性和最近邻关联子
     - 利用已验证的cRBM对Kitaev模型的有效性
     - 保持物理先验知识
  
  2. 几何结构编码器：
     - 编码蜂窝晶格的子格信息
     - 编码三种Kitaev键类型(x,y,z)
     - 提供位置编码信息
  
  3. Plaquette注意力机制：
     - 专门处理六格点plaquette结构
     - 编码Kitaev模型的关键约束
     - 保持Z2规范对称性
  
  4. 全局Transformer层：
     - 捕获长程量子相关性
     - 处理拓扑性质和全局特征
     - 多层注意力机制
  
  5. 物理约束输出层：
     - 确保输出满足量子态归一化
     - 保持数值稳定性
     - 生成复数对数波函数

# 物理模型适配性
physics_adaptation: |
  该架构特别针对Kitaev模型的以下特性进行了优化：
  
  - 蜂窝晶格几何：几何编码器保持空间结构信息
  - 键向性相互作用：三种键类型的独立编码
  - Plaquette守恒量：专门的注意力机制处理六格点约束
  - Z2规范对称性：通过掩码注意力和物理约束保持
  - 量子自旋液体态：Transformer捕获长程量子纠缠
  - 拓扑性质：全局注意力机制处理非局域特征
