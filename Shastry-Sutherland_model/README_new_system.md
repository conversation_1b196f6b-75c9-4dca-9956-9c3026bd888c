# 新的统一任务提交系统

## 概述

原来的三个PBS脚本（`train.pbs`, `chain_of_train.pbs`, `analyze.pbs`）已经整合为一个统一的参数化系统：

- **`jobs/config/train.conf`** - 训练任务参数配置文件（只包含训练相关参数）
- **`jobs/config/finetune.conf`** - 链式微调任务参数配置文件（只包含微调相关参数）
- **`jobs/config/analyze.conf`** - 分析任务参数配置文件（只包含分析相关参数）
- **`jobs/submit.pbs`** - 统一任务提交脚本，包含PBS配置、环境配置，支持选择性并行执行不同任务

## 使用流程

### 1. 配置参数

根据任务类型，编辑对应的配置文件：

**训练任务** - 编辑 `jobs/config/train.conf`：
```bash
# 修改系统参数
L_VALUES="5"
J2_VALUES="0.00"
TRAIN_J1_VALUES="0.04"

# 修改训练超参数
TRAIN_LEARNING_RATE=0.015
TRAIN_N_SAMPLES=16384
TRAIN_N_CYCLES=4
```

**微调任务** - 编辑 `config/finetune.conf`：
```bash
# 修改系统参数
L_VALUES="5"
J2_VALUES="0.00"

# 修改微调参数
FINETUNE_START_J1="0.09"
FINETUNE_J1_LEFT_BOUND="0.10"
FINETUNE_J1_RIGHT_BOUND="0.10"
FINETUNE_J1_STEP="0.01"
```

**分析任务** - 编辑 `config/analyze.conf`：
```bash
# 修改分析参数组合
ANALYZE_PARAM_SETS=(
  "5 0.00 0.10"
  "5 0.00 0.11"
)

# 修改采样数目
ANALYZE_N_SAMPLES=1048576  # 2^20，可根据需要调整
```

### 2. 选择任务类型

编辑 `jobs/submit.pbs` 的第15行，选择要执行的任务：

```bash
# 只执行训练
TASKS="TRAIN"

# 只执行微调
TASKS="FINETUNE"

# 只执行分析
TASKS="ANALYZE"

# 先训练后微调
TASKS="TRAIN FINETUNE"

# 执行全流程
TASKS="TRAIN FINETUNE ANALYZE"
```

### 3. 提交任务

```bash
qsub jobs/submit.pbs
```

## 任务类型详解

### TRAIN - 基础训练任务
- 使用 `TRAIN_*` 前缀的参数
- 从头开始训练模型
- 支持checkpoint保存和恢复

### FINETUNE - 链式微调任务
- 使用 `FINETUNE_*` 前缀的参数
- 基于已有checkpoint进行微调
- 支持自动扩张到相邻J1值
- 需要先有 `FINETUNE_START_J1` 对应的checkpoint

### ANALYZE - 分析任务
- 使用 `ANALYZE_*` 前缀的参数
- 分析指定参数组合的checkpoint
- 支持批量处理多个参数组合

## 配置文件说明

### PBS基础配置
```bash
PBS_QUEUE="gpu_h200_pinaki"      # 队列名称
PBS_SELECT="1:ngpus=1"           # 资源配置
PBS_WALLTIME="1440:00:00"        # 最大运行时间
PBS_PROJECT="gs_spms_psengupta"  # 项目ID
```

### 训练参数 (TRAIN_*)
- `TRAIN_J1_VALUES` - J1耦合强度范围
- `TRAIN_LEARNING_RATE` - 学习率
- `TRAIN_N_SAMPLES` - 样本数量
- `TRAIN_N_CYCLES` - 退火周期数
- `TRAIN_CHECKPOINT_INTERVAL` - checkpoint保存间隔

### 微调参数 (FINETUNE_*)
- `FINETUNE_START_J1` - 起始J1值（必须已有checkpoint）
- `FINETUNE_J1_LEFT_BOUND` / `FINETUNE_J1_RIGHT_BOUND` - 扩张边界
- `FINETUNE_J1_STEP` - 扩张步长
- 其他参数通常比训练设置更小的值

### 分析参数 (ANALYZE_*)
- `ANALYZE_PARAM_SETS` - 要分析的参数组合数组
- 格式：`"L J2 J1"` 每个元素一个组合
- `ANALYZE_N_SAMPLES` - 结构因子计算的采样数目

## 示例使用场景

### 场景1：训练新的J1值
```bash
# 1. 修改 config/train.conf
TRAIN_J1_VALUES="0.05"

# 2. 修改 submit.pbs
TASKS="TRAIN"

# 3. 提交
qsub jobs/submit.pbs
```

### 场景2：从0.09扩张到0.10-0.12
```bash
# 1. 修改 config/finetune.conf
FINETUNE_START_J1="0.09"
FINETUNE_J1_LEFT_BOUND="0.10"
FINETUNE_J1_RIGHT_BOUND="0.12"
FINETUNE_J1_STEP="0.01"

# 2. 修改 submit.pbs
TASKS="FINETUNE"

# 3. 提交
qsub jobs/submit.pbs
```

### 场景3：分析多个参数组合
```bash
# 1. 修改 config/analyze.conf
ANALYZE_PARAM_SETS=(
  "5 0.00 0.10"
  "5 0.00 0.11"
  "5 0.00 0.12"
)

# 2. 修改 submit.pbs
TASKS="ANALYZE"

# 3. 提交
qsub jobs/submit.pbs
```

## 优势

1. **配置分离明确** - 每个任务类型有独立的配置文件，避免参数混淆
2. **PBS配置集中** - GPU队列等PBS设置统一在submit.pbs中管理
3. **任务选择灵活** - 可以灵活组合不同任务类型
4. **代码复用** - 避免重复的PBS脚本代码
5. **易于维护** - 只需要维护一个提交脚本和三个清晰的配置文件
6. **参数管理简化** - 每个配置文件只包含相关任务的参数，更易理解和修改
7. **并行高效** - 不同任务类型可以并行执行，提高资源利用率
