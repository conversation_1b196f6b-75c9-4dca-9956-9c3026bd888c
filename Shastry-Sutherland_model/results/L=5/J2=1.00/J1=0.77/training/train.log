[2025-08-27 01:29:17] ✓ 从checkpoint恢复: results/L=5/J2=1.00/J1=0.78/training/checkpoints/final_GCNN.pkl
[2025-08-27 01:29:17]   - 迭代次数: final
[2025-08-27 01:29:17]   - 能量: -43.579441+0.002087j ± 0.009633
[2025-08-27 01:29:17]   - 时间戳: 2025-08-27T01:28:43.110157+08:00
[2025-08-27 01:29:28] ✓ 变分状态参数已从checkpoint恢复
[2025-08-27 01:29:28] ✓ 从final状态恢复, 重置迭代计数为0
[2025-08-27 01:29:28] ==================================================
[2025-08-27 01:29:28] GCNN for Shastry-Sutherland Model
[2025-08-27 01:29:28] ==================================================
[2025-08-27 01:29:28] System parameters:
[2025-08-27 01:29:28]   - System size: L=5, N=100
[2025-08-27 01:29:28]   - System parameters: J1=0.77, J2=1.0, Q=0.0
[2025-08-27 01:29:28] --------------------------------------------------
[2025-08-27 01:29:28] Model parameters:
[2025-08-27 01:29:28]   - Number of layers = 4
[2025-08-27 01:29:28]   - Number of features = 4
[2025-08-27 01:29:28]   - Total parameters = 19628
[2025-08-27 01:29:28] --------------------------------------------------
[2025-08-27 01:29:28] Training parameters:
[2025-08-27 01:29:28]   - Learning rate: 0.015
[2025-08-27 01:29:28]   - Total iterations: 450
[2025-08-27 01:29:28]   - Annealing cycles: 2
[2025-08-27 01:29:28]   - Initial period: 150
[2025-08-27 01:29:28]   - Period multiplier: 2.0
[2025-08-27 01:29:28]   - Temperature range: 0.0-1.0
[2025-08-27 01:29:28]   - Samples: 4096
[2025-08-27 01:29:28]   - Discarded samples: 0
[2025-08-27 01:29:28]   - Chunk size: 2048
[2025-08-27 01:29:28]   - Diagonal shift: 0.2
[2025-08-27 01:29:28]   - Gradient clipping: 1.0
[2025-08-27 01:29:28]   - Checkpoint enabled: interval=50
[2025-08-27 01:29:28]   - Checkpoint directory: results/L=5/J2=1.00/J1=0.77/training/checkpoints
[2025-08-27 01:29:28] --------------------------------------------------
[2025-08-27 01:29:28] Device status:
[2025-08-27 01:29:28]   - Devices model: NVIDIA H200 NVL
[2025-08-27 01:29:28]   - Number of devices: 1
[2025-08-27 01:29:28]   - Sharding: True
[2025-08-27 01:29:28] ============================================================
[2025-08-27 01:30:10] [Iter 1/450] R0[0/150], Temp: 1.0000, Energy: -42.890657+0.009281j
[2025-08-27 01:30:39] [Iter 2/450] R0[1/150], Temp: 0.9999, Energy: -42.944210+0.002108j
[2025-08-27 01:30:51] [Iter 3/450] R0[2/150], Temp: 0.9996, Energy: -42.922749-0.004792j
[2025-08-27 01:31:03] [Iter 4/450] R0[3/150], Temp: 0.9990, Energy: -42.942769+0.003825j
[2025-08-27 01:31:16] [Iter 5/450] R0[4/150], Temp: 0.9982, Energy: -42.935146+0.001780j
[2025-08-27 01:31:28] [Iter 6/450] R0[5/150], Temp: 0.9973, Energy: -42.955397-0.001450j
[2025-08-27 01:31:40] [Iter 7/450] R0[6/150], Temp: 0.9961, Energy: -42.937159+0.000071j
[2025-08-27 01:31:53] [Iter 8/450] R0[7/150], Temp: 0.9946, Energy: -42.930705+0.000840j
[2025-08-27 01:32:05] [Iter 9/450] R0[8/150], Temp: 0.9930, Energy: -42.935243-0.002689j
[2025-08-27 01:32:17] [Iter 10/450] R0[9/150], Temp: 0.9911, Energy: -42.933581-0.001250j
[2025-08-27 01:32:30] [Iter 11/450] R0[10/150], Temp: 0.9891, Energy: -42.933774-0.000899j
[2025-08-27 01:32:42] [Iter 12/450] R0[11/150], Temp: 0.9868, Energy: -42.947253-0.000690j
[2025-08-27 01:32:55] [Iter 13/450] R0[12/150], Temp: 0.9843, Energy: -42.925405-0.005895j
[2025-08-27 01:33:07] [Iter 14/450] R0[13/150], Temp: 0.9816, Energy: -42.965977-0.004221j
[2025-08-27 01:33:19] [Iter 15/450] R0[14/150], Temp: 0.9787, Energy: -42.939190-0.001392j
[2025-08-27 01:33:32] [Iter 16/450] R0[15/150], Temp: 0.9755, Energy: -42.935619+0.000666j
[2025-08-27 01:33:44] [Iter 17/450] R0[16/150], Temp: 0.9722, Energy: -42.951017-0.003950j
[2025-08-27 01:33:56] [Iter 18/450] R0[17/150], Temp: 0.9686, Energy: -42.938958+0.000432j
[2025-08-27 01:34:09] [Iter 19/450] R0[18/150], Temp: 0.9649, Energy: -42.947706+0.000548j
[2025-08-27 01:34:21] [Iter 20/450] R0[19/150], Temp: 0.9609, Energy: -42.932019-0.002503j
[2025-08-27 01:34:33] [Iter 21/450] R0[20/150], Temp: 0.9568, Energy: -42.942333-0.000125j
[2025-08-27 01:34:46] [Iter 22/450] R0[21/150], Temp: 0.9524, Energy: -42.931178+0.002834j
[2025-08-27 01:34:58] [Iter 23/450] R0[22/150], Temp: 0.9479, Energy: -42.918606+0.004472j
[2025-08-27 01:35:11] [Iter 24/450] R0[23/150], Temp: 0.9431, Energy: -42.938300-0.004006j
[2025-08-27 01:35:23] [Iter 25/450] R0[24/150], Temp: 0.9382, Energy: -42.939921-0.001176j
[2025-08-27 01:35:35] [Iter 26/450] R0[25/150], Temp: 0.9330, Energy: -42.952247-0.000842j
[2025-08-27 01:35:48] [Iter 27/450] R0[26/150], Temp: 0.9277, Energy: -42.945220-0.001763j
[2025-08-27 01:36:00] [Iter 28/450] R0[27/150], Temp: 0.9222, Energy: -42.931461+0.002346j
[2025-08-27 01:36:12] [Iter 29/450] R0[28/150], Temp: 0.9165, Energy: -42.945812-0.004601j
[2025-08-27 01:36:25] [Iter 30/450] R0[29/150], Temp: 0.9106, Energy: -42.936565+0.002740j
[2025-08-27 01:36:37] [Iter 31/450] R0[30/150], Temp: 0.9045, Energy: -42.936280+0.000758j
[2025-08-27 01:36:50] [Iter 32/450] R0[31/150], Temp: 0.8983, Energy: -42.928985-0.004036j
[2025-08-27 01:37:02] [Iter 33/450] R0[32/150], Temp: 0.8918, Energy: -42.951840-0.000217j
[2025-08-27 01:37:14] [Iter 34/450] R0[33/150], Temp: 0.8853, Energy: -42.944273+0.002153j
[2025-08-27 01:37:27] [Iter 35/450] R0[34/150], Temp: 0.8785, Energy: -42.943672-0.003215j
[2025-08-27 01:37:39] [Iter 36/450] R0[35/150], Temp: 0.8716, Energy: -42.943654-0.000643j
[2025-08-27 01:37:51] [Iter 37/450] R0[36/150], Temp: 0.8645, Energy: -42.939680-0.003146j
[2025-08-27 01:38:04] [Iter 38/450] R0[37/150], Temp: 0.8572, Energy: -42.946794-0.009429j
[2025-08-27 01:38:16] [Iter 39/450] R0[38/150], Temp: 0.8498, Energy: -42.945420+0.003786j
[2025-08-27 01:38:28] [Iter 40/450] R0[39/150], Temp: 0.8423, Energy: -42.946081-0.000452j
[2025-08-27 01:38:41] [Iter 41/450] R0[40/150], Temp: 0.8346, Energy: -42.942174-0.001006j
[2025-08-27 01:38:53] [Iter 42/450] R0[41/150], Temp: 0.8267, Energy: -42.927794+0.002979j
[2025-08-27 01:39:05] [Iter 43/450] R0[42/150], Temp: 0.8187, Energy: -42.943410-0.000164j
[2025-08-27 01:39:18] [Iter 44/450] R0[43/150], Temp: 0.8106, Energy: -42.950144-0.002395j
[2025-08-27 01:39:30] [Iter 45/450] R0[44/150], Temp: 0.8023, Energy: -42.942477+0.002467j
[2025-08-27 01:39:43] [Iter 46/450] R0[45/150], Temp: 0.7939, Energy: -42.941756-0.001030j
[2025-08-27 01:39:55] [Iter 47/450] R0[46/150], Temp: 0.7854, Energy: -42.928032+0.002474j
[2025-08-27 01:40:07] [Iter 48/450] R0[47/150], Temp: 0.7767, Energy: -42.927708-0.002446j
[2025-08-27 01:40:20] [Iter 49/450] R0[48/150], Temp: 0.7679, Energy: -42.925930-0.003540j
[2025-08-27 01:40:32] [Iter 50/450] R0[49/150], Temp: 0.7590, Energy: -42.925218-0.001820j
[2025-08-27 01:40:32] ✓ Checkpoint saved: checkpoint_iter_000050.pkl
[2025-08-27 01:40:44] [Iter 51/450] R0[50/150], Temp: 0.7500, Energy: -42.938391-0.002718j
[2025-08-27 01:40:57] [Iter 52/450] R0[51/150], Temp: 0.7409, Energy: -42.942559-0.000240j
[2025-08-27 01:41:09] [Iter 53/450] R0[52/150], Temp: 0.7316, Energy: -42.955344+0.006269j
[2025-08-27 01:41:21] [Iter 54/450] R0[53/150], Temp: 0.7223, Energy: -42.941257-0.000308j
[2025-08-27 01:41:34] [Iter 55/450] R0[54/150], Temp: 0.7129, Energy: -42.925445+0.002710j
[2025-08-27 01:41:46] [Iter 56/450] R0[55/150], Temp: 0.7034, Energy: -42.958272-0.000215j
[2025-08-27 01:41:58] [Iter 57/450] R0[56/150], Temp: 0.6938, Energy: -42.929137-0.000903j
[2025-08-27 01:42:11] [Iter 58/450] R0[57/150], Temp: 0.6841, Energy: -42.949088-0.002567j
[2025-08-27 01:42:23] [Iter 59/450] R0[58/150], Temp: 0.6743, Energy: -42.952061-0.004866j
[2025-08-27 01:42:36] [Iter 60/450] R0[59/150], Temp: 0.6644, Energy: -42.938899-0.007976j
[2025-08-27 01:42:48] [Iter 61/450] R0[60/150], Temp: 0.6545, Energy: -42.928341-0.000536j
[2025-08-27 01:43:00] [Iter 62/450] R0[61/150], Temp: 0.6445, Energy: -42.938896+0.002037j
[2025-08-27 01:43:13] [Iter 63/450] R0[62/150], Temp: 0.6345, Energy: -42.926825+0.001625j
[2025-08-27 01:43:25] [Iter 64/450] R0[63/150], Temp: 0.6243, Energy: -42.945529-0.000643j
[2025-08-27 01:43:37] [Iter 65/450] R0[64/150], Temp: 0.6142, Energy: -42.946102-0.000234j
[2025-08-27 01:43:50] [Iter 66/450] R0[65/150], Temp: 0.6040, Energy: -42.954409+0.004760j
[2025-08-27 01:44:02] [Iter 67/450] R0[66/150], Temp: 0.5937, Energy: -42.929366+0.002375j
[2025-08-27 01:44:14] [Iter 68/450] R0[67/150], Temp: 0.5834, Energy: -42.948565-0.008433j
[2025-08-27 01:44:27] [Iter 69/450] R0[68/150], Temp: 0.5730, Energy: -42.929634+0.002472j
[2025-08-27 01:44:39] [Iter 70/450] R0[69/150], Temp: 0.5627, Energy: -42.960015+0.000312j
[2025-08-27 01:44:51] [Iter 71/450] R0[70/150], Temp: 0.5523, Energy: -42.933928-0.002729j
[2025-08-27 01:45:04] [Iter 72/450] R0[71/150], Temp: 0.5418, Energy: -42.922535+0.000826j
[2025-08-27 01:45:16] [Iter 73/450] R0[72/150], Temp: 0.5314, Energy: -42.937190+0.001509j
[2025-08-27 01:45:29] [Iter 74/450] R0[73/150], Temp: 0.5209, Energy: -42.941723+0.000999j
[2025-08-27 01:45:41] [Iter 75/450] R0[74/150], Temp: 0.5105, Energy: -42.936120-0.008121j
[2025-08-27 01:45:53] [Iter 76/450] R0[75/150], Temp: 0.5000, Energy: -42.931127-0.000512j
[2025-08-27 01:46:06] [Iter 77/450] R0[76/150], Temp: 0.4895, Energy: -42.949736+0.002883j
[2025-08-27 01:46:18] [Iter 78/450] R0[77/150], Temp: 0.4791, Energy: -42.935538-0.000519j
[2025-08-27 01:46:30] [Iter 79/450] R0[78/150], Temp: 0.4686, Energy: -42.962856+0.002296j
[2025-08-27 01:46:43] [Iter 80/450] R0[79/150], Temp: 0.4582, Energy: -42.941945-0.005564j
[2025-08-27 01:46:55] [Iter 81/450] R0[80/150], Temp: 0.4477, Energy: -42.938987-0.000011j
[2025-08-27 01:47:07] [Iter 82/450] R0[81/150], Temp: 0.4373, Energy: -42.945036-0.000994j
[2025-08-27 01:47:20] [Iter 83/450] R0[82/150], Temp: 0.4270, Energy: -42.937614-0.001292j
[2025-08-27 01:47:32] [Iter 84/450] R0[83/150], Temp: 0.4166, Energy: -42.922237+0.002070j
[2025-08-27 01:47:45] [Iter 85/450] R0[84/150], Temp: 0.4063, Energy: -42.953103+0.007218j
[2025-08-27 01:47:57] [Iter 86/450] R0[85/150], Temp: 0.3960, Energy: -42.952432+0.001361j
[2025-08-27 01:48:09] [Iter 87/450] R0[86/150], Temp: 0.3858, Energy: -42.951287-0.003019j
[2025-08-27 01:48:22] [Iter 88/450] R0[87/150], Temp: 0.3757, Energy: -42.935613-0.000591j
[2025-08-27 01:48:34] [Iter 89/450] R0[88/150], Temp: 0.3655, Energy: -42.940554+0.004641j
[2025-08-27 01:48:46] [Iter 90/450] R0[89/150], Temp: 0.3555, Energy: -42.943712+0.002060j
[2025-08-27 01:48:59] [Iter 91/450] R0[90/150], Temp: 0.3455, Energy: -42.942172-0.005234j
[2025-08-27 01:49:11] [Iter 92/450] R0[91/150], Temp: 0.3356, Energy: -42.947767+0.002968j
[2025-08-27 01:49:23] [Iter 93/450] R0[92/150], Temp: 0.3257, Energy: -42.944943+0.006924j
[2025-08-27 01:49:36] [Iter 94/450] R0[93/150], Temp: 0.3159, Energy: -42.930405-0.002984j
[2025-08-27 01:49:48] [Iter 95/450] R0[94/150], Temp: 0.3062, Energy: -42.932974-0.000346j
[2025-08-27 01:50:01] [Iter 96/450] R0[95/150], Temp: 0.2966, Energy: -42.941120-0.002721j
[2025-08-27 01:50:13] [Iter 97/450] R0[96/150], Temp: 0.2871, Energy: -42.936660-0.001649j
[2025-08-27 01:50:25] [Iter 98/450] R0[97/150], Temp: 0.2777, Energy: -42.953547-0.004509j
[2025-08-27 01:50:38] [Iter 99/450] R0[98/150], Temp: 0.2684, Energy: -42.944696+0.003593j
[2025-08-27 01:50:50] [Iter 100/450] R0[99/150], Temp: 0.2591, Energy: -42.950289+0.000406j
[2025-08-27 01:50:50] ✓ Checkpoint saved: checkpoint_iter_000100.pkl
[2025-08-27 01:51:02] [Iter 101/450] R0[100/150], Temp: 0.2500, Energy: -42.955790+0.002988j
[2025-08-27 01:51:15] [Iter 102/450] R0[101/150], Temp: 0.2410, Energy: -42.941515-0.000035j
[2025-08-27 01:51:27] [Iter 103/450] R0[102/150], Temp: 0.2321, Energy: -42.939733-0.002330j
[2025-08-27 01:51:40] [Iter 104/450] R0[103/150], Temp: 0.2233, Energy: -42.949416-0.001713j
[2025-08-27 01:51:52] [Iter 105/450] R0[104/150], Temp: 0.2146, Energy: -42.933898-0.001472j
[2025-08-27 01:52:04] [Iter 106/450] R0[105/150], Temp: 0.2061, Energy: -42.932692-0.002063j
[2025-08-27 01:52:17] [Iter 107/450] R0[106/150], Temp: 0.1977, Energy: -42.939765+0.001153j
[2025-08-27 01:52:29] [Iter 108/450] R0[107/150], Temp: 0.1894, Energy: -42.941978-0.000522j
[2025-08-27 01:52:41] [Iter 109/450] R0[108/150], Temp: 0.1813, Energy: -42.956881-0.001593j
[2025-08-27 01:52:54] [Iter 110/450] R0[109/150], Temp: 0.1733, Energy: -42.932289-0.000404j
[2025-08-27 01:53:06] [Iter 111/450] R0[110/150], Temp: 0.1654, Energy: -42.946130-0.002096j
[2025-08-27 01:53:18] [Iter 112/450] R0[111/150], Temp: 0.1577, Energy: -42.934047+0.000319j
[2025-08-27 01:53:31] [Iter 113/450] R0[112/150], Temp: 0.1502, Energy: -42.947142-0.000093j
[2025-08-27 01:53:43] [Iter 114/450] R0[113/150], Temp: 0.1428, Energy: -42.939247-0.003279j
[2025-08-27 01:53:56] [Iter 115/450] R0[114/150], Temp: 0.1355, Energy: -42.941470+0.000474j
[2025-08-27 01:54:08] [Iter 116/450] R0[115/150], Temp: 0.1284, Energy: -42.951817+0.000880j
[2025-08-27 01:54:20] [Iter 117/450] R0[116/150], Temp: 0.1215, Energy: -42.958598+0.001350j
[2025-08-27 01:54:33] [Iter 118/450] R0[117/150], Temp: 0.1147, Energy: -42.938539+0.000950j
[2025-08-27 01:54:45] [Iter 119/450] R0[118/150], Temp: 0.1082, Energy: -42.940511-0.000224j
[2025-08-27 01:54:57] [Iter 120/450] R0[119/150], Temp: 0.1017, Energy: -42.941497-0.001717j
[2025-08-27 01:55:10] [Iter 121/450] R0[120/150], Temp: 0.0955, Energy: -42.928110+0.002565j
[2025-08-27 01:55:22] [Iter 122/450] R0[121/150], Temp: 0.0894, Energy: -42.943839+0.001201j
[2025-08-27 01:55:34] [Iter 123/450] R0[122/150], Temp: 0.0835, Energy: -42.957655+0.005157j
[2025-08-27 01:55:47] [Iter 124/450] R0[123/150], Temp: 0.0778, Energy: -42.945896+0.001735j
[2025-08-27 01:55:59] [Iter 125/450] R0[124/150], Temp: 0.0723, Energy: -42.943019+0.001943j
[2025-08-27 01:56:12] [Iter 126/450] R0[125/150], Temp: 0.0670, Energy: -42.932247+0.000870j
[2025-08-27 01:56:24] [Iter 127/450] R0[126/150], Temp: 0.0618, Energy: -42.920930-0.001541j
[2025-08-27 01:56:36] [Iter 128/450] R0[127/150], Temp: 0.0569, Energy: -42.948030-0.000230j
[2025-08-27 01:56:49] [Iter 129/450] R0[128/150], Temp: 0.0521, Energy: -42.947972-0.000611j
[2025-08-27 01:57:01] [Iter 130/450] R0[129/150], Temp: 0.0476, Energy: -42.941382-0.004828j
[2025-08-27 01:57:13] [Iter 131/450] R0[130/150], Temp: 0.0432, Energy: -42.944285+0.001173j
[2025-08-27 01:57:26] [Iter 132/450] R0[131/150], Temp: 0.0391, Energy: -42.947042+0.005140j
[2025-08-27 01:57:38] [Iter 133/450] R0[132/150], Temp: 0.0351, Energy: -42.933430+0.006955j
[2025-08-27 01:57:51] [Iter 134/450] R0[133/150], Temp: 0.0314, Energy: -42.935891+0.004360j
[2025-08-27 01:58:03] [Iter 135/450] R0[134/150], Temp: 0.0278, Energy: -42.954507+0.003062j
[2025-08-27 01:58:15] [Iter 136/450] R0[135/150], Temp: 0.0245, Energy: -42.943879+0.001437j
[2025-08-27 01:58:28] [Iter 137/450] R0[136/150], Temp: 0.0213, Energy: -42.958871+0.006111j
[2025-08-27 01:58:40] [Iter 138/450] R0[137/150], Temp: 0.0184, Energy: -42.942081-0.001885j
[2025-08-27 01:58:52] [Iter 139/450] R0[138/150], Temp: 0.0157, Energy: -42.937430+0.000205j
[2025-08-27 01:59:05] [Iter 140/450] R0[139/150], Temp: 0.0132, Energy: -42.942259-0.002771j
[2025-08-27 01:59:17] [Iter 141/450] R0[140/150], Temp: 0.0109, Energy: -42.930372+0.001032j
[2025-08-27 01:59:29] [Iter 142/450] R0[141/150], Temp: 0.0089, Energy: -42.935372-0.002475j
[2025-08-27 01:59:42] [Iter 143/450] R0[142/150], Temp: 0.0070, Energy: -42.946043-0.001580j
[2025-08-27 01:59:54] [Iter 144/450] R0[143/150], Temp: 0.0054, Energy: -42.940479-0.000189j
[2025-08-27 02:00:06] [Iter 145/450] R0[144/150], Temp: 0.0039, Energy: -42.939508+0.001267j
[2025-08-27 02:00:19] [Iter 146/450] R0[145/150], Temp: 0.0027, Energy: -42.939091-0.003209j
[2025-08-27 02:00:31] [Iter 147/450] R0[146/150], Temp: 0.0018, Energy: -42.942030+0.005570j
[2025-08-27 02:00:44] [Iter 148/450] R0[147/150], Temp: 0.0010, Energy: -42.939229-0.004880j
[2025-08-27 02:00:56] [Iter 149/450] R0[148/150], Temp: 0.0004, Energy: -42.945257+0.000748j
[2025-08-27 02:01:08] [Iter 150/450] R0[149/150], Temp: 0.0001, Energy: -42.944967+0.001105j
[2025-08-27 02:01:08] ✓ Checkpoint saved: checkpoint_iter_000150.pkl
[2025-08-27 02:01:08] RESTART #1 | Period: 300
[2025-08-27 02:01:21] [Iter 151/450] R1[0/300], Temp: 1.0000, Energy: -42.936283-0.002624j
[2025-08-27 02:01:33] [Iter 152/450] R1[1/300], Temp: 1.0000, Energy: -42.942215+0.000023j
[2025-08-27 02:01:45] [Iter 153/450] R1[2/300], Temp: 0.9999, Energy: -42.933782-0.000523j
[2025-08-27 02:01:58] [Iter 154/450] R1[3/300], Temp: 0.9998, Energy: -42.938753-0.004412j
[2025-08-27 02:02:10] [Iter 155/450] R1[4/300], Temp: 0.9996, Energy: -42.933725+0.001852j
[2025-08-27 02:02:22] [Iter 156/450] R1[5/300], Temp: 0.9993, Energy: -42.939041+0.008795j
[2025-08-27 02:02:35] [Iter 157/450] R1[6/300], Temp: 0.9990, Energy: -42.947166-0.000809j
[2025-08-27 02:02:47] [Iter 158/450] R1[7/300], Temp: 0.9987, Energy: -42.953621+0.006635j
[2025-08-27 02:02:59] [Iter 159/450] R1[8/300], Temp: 0.9982, Energy: -42.946979+0.000275j
[2025-08-27 02:03:12] [Iter 160/450] R1[9/300], Temp: 0.9978, Energy: -42.931582-0.001475j
[2025-08-27 02:03:24] [Iter 161/450] R1[10/300], Temp: 0.9973, Energy: -42.941485-0.000030j
[2025-08-27 02:03:37] [Iter 162/450] R1[11/300], Temp: 0.9967, Energy: -42.945456+0.006668j
[2025-08-27 02:03:49] [Iter 163/450] R1[12/300], Temp: 0.9961, Energy: -42.935510-0.002486j
[2025-08-27 02:04:01] [Iter 164/450] R1[13/300], Temp: 0.9954, Energy: -42.940769-0.003985j
[2025-08-27 02:04:14] [Iter 165/450] R1[14/300], Temp: 0.9946, Energy: -42.943931-0.001647j
[2025-08-27 02:04:26] [Iter 166/450] R1[15/300], Temp: 0.9938, Energy: -42.938047+0.003663j
[2025-08-27 02:04:38] [Iter 167/450] R1[16/300], Temp: 0.9930, Energy: -42.922970-0.002394j
[2025-08-27 02:04:51] [Iter 168/450] R1[17/300], Temp: 0.9921, Energy: -42.933753+0.001030j
[2025-08-27 02:05:03] [Iter 169/450] R1[18/300], Temp: 0.9911, Energy: -42.942796-0.001269j
[2025-08-27 02:05:16] [Iter 170/450] R1[19/300], Temp: 0.9901, Energy: -42.944928+0.003404j
[2025-08-27 02:05:28] [Iter 171/450] R1[20/300], Temp: 0.9891, Energy: -42.948067+0.002548j
[2025-08-27 02:05:40] [Iter 172/450] R1[21/300], Temp: 0.9880, Energy: -42.942313+0.000587j
[2025-08-27 02:05:53] [Iter 173/450] R1[22/300], Temp: 0.9868, Energy: -42.920862+0.000568j
[2025-08-27 02:06:05] [Iter 174/450] R1[23/300], Temp: 0.9856, Energy: -42.956518+0.001789j
[2025-08-27 02:06:17] [Iter 175/450] R1[24/300], Temp: 0.9843, Energy: -42.936429+0.000433j
[2025-08-27 02:06:30] [Iter 176/450] R1[25/300], Temp: 0.9830, Energy: -42.950644+0.003515j
[2025-08-27 02:06:42] [Iter 177/450] R1[26/300], Temp: 0.9816, Energy: -42.949704+0.002172j
[2025-08-27 02:06:54] [Iter 178/450] R1[27/300], Temp: 0.9801, Energy: -42.947167+0.000108j
[2025-08-27 02:07:07] [Iter 179/450] R1[28/300], Temp: 0.9787, Energy: -42.939154+0.003463j
[2025-08-27 02:07:19] [Iter 180/450] R1[29/300], Temp: 0.9771, Energy: -42.950122+0.000582j
[2025-08-27 02:07:32] [Iter 181/450] R1[30/300], Temp: 0.9755, Energy: -42.937684+0.001053j
[2025-08-27 02:07:44] [Iter 182/450] R1[31/300], Temp: 0.9739, Energy: -42.936023-0.002218j
[2025-08-27 02:07:56] [Iter 183/450] R1[32/300], Temp: 0.9722, Energy: -42.938647+0.002938j
[2025-08-27 02:08:09] [Iter 184/450] R1[33/300], Temp: 0.9704, Energy: -42.934893-0.000336j
[2025-08-27 02:08:21] [Iter 185/450] R1[34/300], Temp: 0.9686, Energy: -42.944117+0.006953j
[2025-08-27 02:08:33] [Iter 186/450] R1[35/300], Temp: 0.9668, Energy: -42.926969-0.000883j
[2025-08-27 02:08:46] [Iter 187/450] R1[36/300], Temp: 0.9649, Energy: -42.942139+0.001474j
[2025-08-27 02:08:58] [Iter 188/450] R1[37/300], Temp: 0.9629, Energy: -42.942432+0.001840j
[2025-08-27 02:09:10] [Iter 189/450] R1[38/300], Temp: 0.9609, Energy: -42.952071+0.002540j
[2025-08-27 02:09:23] [Iter 190/450] R1[39/300], Temp: 0.9589, Energy: -42.941490-0.003158j
[2025-08-27 02:09:35] [Iter 191/450] R1[40/300], Temp: 0.9568, Energy: -42.940261-0.000438j
[2025-08-27 02:09:48] [Iter 192/450] R1[41/300], Temp: 0.9546, Energy: -42.952200-0.000234j
[2025-08-27 02:10:00] [Iter 193/450] R1[42/300], Temp: 0.9524, Energy: -42.942598+0.001553j
[2025-08-27 02:10:12] [Iter 194/450] R1[43/300], Temp: 0.9502, Energy: -42.941721-0.002937j
[2025-08-27 02:10:25] [Iter 195/450] R1[44/300], Temp: 0.9479, Energy: -42.931227+0.001855j
[2025-08-27 02:10:37] [Iter 196/450] R1[45/300], Temp: 0.9455, Energy: -42.949422-0.002448j
[2025-08-27 02:10:49] [Iter 197/450] R1[46/300], Temp: 0.9431, Energy: -42.942411+0.000526j
[2025-08-27 02:11:02] [Iter 198/450] R1[47/300], Temp: 0.9407, Energy: -42.942604-0.004031j
[2025-08-27 02:11:14] [Iter 199/450] R1[48/300], Temp: 0.9382, Energy: -42.940706-0.005874j
[2025-08-27 02:11:26] [Iter 200/450] R1[49/300], Temp: 0.9356, Energy: -42.951630+0.001975j
[2025-08-27 02:11:27] ✓ Checkpoint saved: checkpoint_iter_000200.pkl
[2025-08-27 02:11:39] [Iter 201/450] R1[50/300], Temp: 0.9330, Energy: -42.930084-0.005547j
[2025-08-27 02:11:51] [Iter 202/450] R1[51/300], Temp: 0.9304, Energy: -42.965294+0.002093j
[2025-08-27 02:12:04] [Iter 203/450] R1[52/300], Temp: 0.9277, Energy: -42.952250+0.001938j
[2025-08-27 02:12:16] [Iter 204/450] R1[53/300], Temp: 0.9249, Energy: -42.950724-0.004429j
[2025-08-27 02:12:28] [Iter 205/450] R1[54/300], Temp: 0.9222, Energy: -42.942412-0.000356j
[2025-08-27 02:12:41] [Iter 206/450] R1[55/300], Temp: 0.9193, Energy: -42.952612+0.001258j
[2025-08-27 02:12:53] [Iter 207/450] R1[56/300], Temp: 0.9165, Energy: -42.947985-0.004670j
[2025-08-27 02:13:05] [Iter 208/450] R1[57/300], Temp: 0.9135, Energy: -42.942773-0.000135j
[2025-08-27 02:13:18] [Iter 209/450] R1[58/300], Temp: 0.9106, Energy: -42.948918-0.003044j
[2025-08-27 02:13:30] [Iter 210/450] R1[59/300], Temp: 0.9076, Energy: -42.939301-0.000778j
[2025-08-27 02:13:42] [Iter 211/450] R1[60/300], Temp: 0.9045, Energy: -42.956139-0.005535j
[2025-08-27 02:13:55] [Iter 212/450] R1[61/300], Temp: 0.9014, Energy: -42.924835+0.001128j
[2025-08-27 02:14:07] [Iter 213/450] R1[62/300], Temp: 0.8983, Energy: -42.944227+0.000428j
[2025-08-27 02:14:20] [Iter 214/450] R1[63/300], Temp: 0.8951, Energy: -42.941773+0.001929j
[2025-08-27 02:14:32] [Iter 215/450] R1[64/300], Temp: 0.8918, Energy: -42.930745+0.000063j
[2025-08-27 02:14:44] [Iter 216/450] R1[65/300], Temp: 0.8886, Energy: -42.944192+0.002990j
[2025-08-27 02:14:57] [Iter 217/450] R1[66/300], Temp: 0.8853, Energy: -42.928894+0.001238j
[2025-08-27 02:15:09] [Iter 218/450] R1[67/300], Temp: 0.8819, Energy: -42.939267+0.003283j
[2025-08-27 02:15:21] [Iter 219/450] R1[68/300], Temp: 0.8785, Energy: -42.972085-0.001014j
[2025-08-27 02:15:34] [Iter 220/450] R1[69/300], Temp: 0.8751, Energy: -42.941954+0.003771j
[2025-08-27 02:15:46] [Iter 221/450] R1[70/300], Temp: 0.8716, Energy: -42.953163-0.000456j
[2025-08-27 02:15:59] [Iter 222/450] R1[71/300], Temp: 0.8680, Energy: -42.943908+0.000153j
[2025-08-27 02:16:11] [Iter 223/450] R1[72/300], Temp: 0.8645, Energy: -42.947380-0.001023j
[2025-08-27 02:16:23] [Iter 224/450] R1[73/300], Temp: 0.8609, Energy: -42.922240+0.002588j
[2025-08-27 02:16:36] [Iter 225/450] R1[74/300], Temp: 0.8572, Energy: -42.942649+0.002211j
[2025-08-27 02:16:48] [Iter 226/450] R1[75/300], Temp: 0.8536, Energy: -42.947957+0.001090j
[2025-08-27 02:17:00] [Iter 227/450] R1[76/300], Temp: 0.8498, Energy: -42.941160+0.001237j
[2025-08-27 02:17:13] [Iter 228/450] R1[77/300], Temp: 0.8461, Energy: -42.937138+0.001805j
[2025-08-27 02:17:25] [Iter 229/450] R1[78/300], Temp: 0.8423, Energy: -42.941371-0.000407j
[2025-08-27 02:17:37] [Iter 230/450] R1[79/300], Temp: 0.8384, Energy: -42.928173-0.001377j
[2025-08-27 02:17:50] [Iter 231/450] R1[80/300], Temp: 0.8346, Energy: -42.937189+0.000531j
[2025-08-27 02:18:02] [Iter 232/450] R1[81/300], Temp: 0.8307, Energy: -42.955803-0.000008j
[2025-08-27 02:18:14] [Iter 233/450] R1[82/300], Temp: 0.8267, Energy: -42.931284+0.004617j
[2025-08-27 02:18:27] [Iter 234/450] R1[83/300], Temp: 0.8227, Energy: -42.977982-0.003631j
[2025-08-27 02:18:39] [Iter 235/450] R1[84/300], Temp: 0.8187, Energy: -42.945382-0.004469j
[2025-08-27 02:18:52] [Iter 236/450] R1[85/300], Temp: 0.8147, Energy: -42.946253-0.001305j
[2025-08-27 02:19:04] [Iter 237/450] R1[86/300], Temp: 0.8106, Energy: -42.948649-0.004427j
[2025-08-27 02:19:16] [Iter 238/450] R1[87/300], Temp: 0.8065, Energy: -42.930199+0.002940j
[2025-08-27 02:19:29] [Iter 239/450] R1[88/300], Temp: 0.8023, Energy: -42.946685-0.002713j
[2025-08-27 02:19:41] [Iter 240/450] R1[89/300], Temp: 0.7981, Energy: -42.939050-0.000015j
[2025-08-27 02:19:53] [Iter 241/450] R1[90/300], Temp: 0.7939, Energy: -42.953434+0.005849j
[2025-08-27 02:20:06] [Iter 242/450] R1[91/300], Temp: 0.7896, Energy: -42.931905+0.003791j
[2025-08-27 02:20:18] [Iter 243/450] R1[92/300], Temp: 0.7854, Energy: -42.938281+0.002731j
[2025-08-27 02:20:30] [Iter 244/450] R1[93/300], Temp: 0.7810, Energy: -42.942221-0.002214j
[2025-08-27 02:20:43] [Iter 245/450] R1[94/300], Temp: 0.7767, Energy: -42.932341-0.000824j
[2025-08-27 02:20:55] [Iter 246/450] R1[95/300], Temp: 0.7723, Energy: -42.944585+0.000480j
[2025-08-27 02:21:08] [Iter 247/450] R1[96/300], Temp: 0.7679, Energy: -42.936076+0.003135j
[2025-08-27 02:21:20] [Iter 248/450] R1[97/300], Temp: 0.7635, Energy: -42.946661-0.000560j
[2025-08-27 02:21:32] [Iter 249/450] R1[98/300], Temp: 0.7590, Energy: -42.946621+0.003188j
[2025-08-27 02:21:45] [Iter 250/450] R1[99/300], Temp: 0.7545, Energy: -42.942676+0.005982j
[2025-08-27 02:21:45] ✓ Checkpoint saved: checkpoint_iter_000250.pkl
[2025-08-27 02:21:57] [Iter 251/450] R1[100/300], Temp: 0.7500, Energy: -42.954005+0.006352j
[2025-08-27 02:22:09] [Iter 252/450] R1[101/300], Temp: 0.7455, Energy: -42.955097-0.000215j
[2025-08-27 02:22:22] [Iter 253/450] R1[102/300], Temp: 0.7409, Energy: -42.929592+0.001107j
[2025-08-27 02:22:34] [Iter 254/450] R1[103/300], Temp: 0.7363, Energy: -42.955495+0.002714j
[2025-08-27 02:22:46] [Iter 255/450] R1[104/300], Temp: 0.7316, Energy: -42.931246+0.003652j
[2025-08-27 02:22:59] [Iter 256/450] R1[105/300], Temp: 0.7270, Energy: -42.940486-0.002709j
[2025-08-27 02:23:11] [Iter 257/450] R1[106/300], Temp: 0.7223, Energy: -42.953946-0.006182j
[2025-08-27 02:23:24] [Iter 258/450] R1[107/300], Temp: 0.7176, Energy: -42.941184-0.003139j
[2025-08-27 02:23:36] [Iter 259/450] R1[108/300], Temp: 0.7129, Energy: -42.932634-0.001582j
[2025-08-27 02:23:48] [Iter 260/450] R1[109/300], Temp: 0.7081, Energy: -42.942372+0.000723j
[2025-08-27 02:24:01] [Iter 261/450] R1[110/300], Temp: 0.7034, Energy: -42.936973-0.001607j
[2025-08-27 02:24:13] [Iter 262/450] R1[111/300], Temp: 0.6986, Energy: -42.948938-0.000431j
[2025-08-27 02:24:25] [Iter 263/450] R1[112/300], Temp: 0.6938, Energy: -42.935986-0.000146j
[2025-08-27 02:24:38] [Iter 264/450] R1[113/300], Temp: 0.6889, Energy: -42.966011-0.000253j
[2025-08-27 02:24:50] [Iter 265/450] R1[114/300], Temp: 0.6841, Energy: -42.933256-0.000580j
[2025-08-27 02:25:03] [Iter 266/450] R1[115/300], Temp: 0.6792, Energy: -42.928397-0.000388j
[2025-08-27 02:25:15] [Iter 267/450] R1[116/300], Temp: 0.6743, Energy: -42.945339+0.004837j
[2025-08-27 02:25:27] [Iter 268/450] R1[117/300], Temp: 0.6694, Energy: -42.947405+0.001629j
[2025-08-27 02:25:40] [Iter 269/450] R1[118/300], Temp: 0.6644, Energy: -42.946040+0.002058j
[2025-08-27 02:25:52] [Iter 270/450] R1[119/300], Temp: 0.6595, Energy: -42.946850-0.000979j
[2025-08-27 02:26:04] [Iter 271/450] R1[120/300], Temp: 0.6545, Energy: -42.935676+0.002184j
[2025-08-27 02:26:17] [Iter 272/450] R1[121/300], Temp: 0.6495, Energy: -42.932840-0.000450j
[2025-08-27 02:26:29] [Iter 273/450] R1[122/300], Temp: 0.6445, Energy: -42.940906+0.000566j
[2025-08-27 02:26:42] [Iter 274/450] R1[123/300], Temp: 0.6395, Energy: -42.943015+0.002396j
[2025-08-27 02:26:54] [Iter 275/450] R1[124/300], Temp: 0.6345, Energy: -42.944875+0.000357j
[2025-08-27 02:27:06] [Iter 276/450] R1[125/300], Temp: 0.6294, Energy: -42.932874-0.006224j
[2025-08-27 02:27:19] [Iter 277/450] R1[126/300], Temp: 0.6243, Energy: -42.938342-0.000079j
[2025-08-27 02:27:31] [Iter 278/450] R1[127/300], Temp: 0.6193, Energy: -42.949273-0.000773j
[2025-08-27 02:27:43] [Iter 279/450] R1[128/300], Temp: 0.6142, Energy: -42.939077+0.000232j
[2025-08-27 02:27:56] [Iter 280/450] R1[129/300], Temp: 0.6091, Energy: -42.929166+0.002942j
[2025-08-27 02:28:08] [Iter 281/450] R1[130/300], Temp: 0.6040, Energy: -42.936813+0.001040j
[2025-08-27 02:28:20] [Iter 282/450] R1[131/300], Temp: 0.5988, Energy: -42.945526+0.003106j
[2025-08-27 02:28:33] [Iter 283/450] R1[132/300], Temp: 0.5937, Energy: -42.936869-0.003773j
[2025-08-27 02:28:45] [Iter 284/450] R1[133/300], Temp: 0.5885, Energy: -42.945227-0.000946j
[2025-08-27 02:28:57] [Iter 285/450] R1[134/300], Temp: 0.5834, Energy: -42.953182+0.000005j
[2025-08-27 02:29:10] [Iter 286/450] R1[135/300], Temp: 0.5782, Energy: -42.948145-0.000640j
[2025-08-27 02:29:22] [Iter 287/450] R1[136/300], Temp: 0.5730, Energy: -42.950954-0.003743j
[2025-08-27 02:29:35] [Iter 288/450] R1[137/300], Temp: 0.5679, Energy: -42.938599+0.002039j
[2025-08-27 02:29:47] [Iter 289/450] R1[138/300], Temp: 0.5627, Energy: -42.965796-0.000569j
[2025-08-27 02:29:59] [Iter 290/450] R1[139/300], Temp: 0.5575, Energy: -42.940538+0.000131j
[2025-08-27 02:30:12] [Iter 291/450] R1[140/300], Temp: 0.5523, Energy: -42.944223+0.002550j
[2025-08-27 02:30:24] [Iter 292/450] R1[141/300], Temp: 0.5471, Energy: -42.943586-0.000657j
[2025-08-27 02:30:36] [Iter 293/450] R1[142/300], Temp: 0.5418, Energy: -42.950119+0.001009j
[2025-08-27 02:30:49] [Iter 294/450] R1[143/300], Temp: 0.5366, Energy: -42.944599-0.000207j
[2025-08-27 02:31:01] [Iter 295/450] R1[144/300], Temp: 0.5314, Energy: -42.955590+0.001593j
[2025-08-27 02:31:13] [Iter 296/450] R1[145/300], Temp: 0.5262, Energy: -42.956130-0.002984j
[2025-08-27 02:31:26] [Iter 297/450] R1[146/300], Temp: 0.5209, Energy: -42.945337+0.005791j
[2025-08-27 02:31:38] [Iter 298/450] R1[147/300], Temp: 0.5157, Energy: -42.939197+0.002480j
[2025-08-27 02:31:50] [Iter 299/450] R1[148/300], Temp: 0.5105, Energy: -42.926426+0.001893j
[2025-08-27 02:32:03] [Iter 300/450] R1[149/300], Temp: 0.5052, Energy: -42.932002-0.004837j
[2025-08-27 02:32:03] ✓ Checkpoint saved: checkpoint_iter_000300.pkl
[2025-08-27 02:32:15] [Iter 301/450] R1[150/300], Temp: 0.5000, Energy: -42.929364+0.003057j
[2025-08-27 02:32:27] [Iter 302/450] R1[151/300], Temp: 0.4948, Energy: -42.939601+0.000211j
[2025-08-27 02:32:40] [Iter 303/450] R1[152/300], Temp: 0.4895, Energy: -42.945753+0.002705j
[2025-08-27 02:32:52] [Iter 304/450] R1[153/300], Temp: 0.4843, Energy: -42.946177-0.005041j
[2025-08-27 02:33:04] [Iter 305/450] R1[154/300], Temp: 0.4791, Energy: -42.941886+0.000688j
[2025-08-27 02:33:17] [Iter 306/450] R1[155/300], Temp: 0.4738, Energy: -42.941108-0.000022j
[2025-08-27 02:33:29] [Iter 307/450] R1[156/300], Temp: 0.4686, Energy: -42.955958-0.000452j
[2025-08-27 02:33:42] [Iter 308/450] R1[157/300], Temp: 0.4634, Energy: -42.953007+0.003281j
[2025-08-27 02:33:54] [Iter 309/450] R1[158/300], Temp: 0.4582, Energy: -42.930571-0.001231j
[2025-08-27 02:34:06] [Iter 310/450] R1[159/300], Temp: 0.4529, Energy: -42.947205+0.002440j
[2025-08-27 02:34:19] [Iter 311/450] R1[160/300], Temp: 0.4477, Energy: -42.951698+0.000097j
[2025-08-27 02:34:31] [Iter 312/450] R1[161/300], Temp: 0.4425, Energy: -42.934568-0.004802j
[2025-08-27 02:34:43] [Iter 313/450] R1[162/300], Temp: 0.4373, Energy: -42.956569-0.002408j
[2025-08-27 02:34:56] [Iter 314/450] R1[163/300], Temp: 0.4321, Energy: -42.950776-0.000465j
[2025-08-27 02:35:08] [Iter 315/450] R1[164/300], Temp: 0.4270, Energy: -42.939179-0.000176j
[2025-08-27 02:35:20] [Iter 316/450] R1[165/300], Temp: 0.4218, Energy: -42.957753+0.004559j
[2025-08-27 02:35:33] [Iter 317/450] R1[166/300], Temp: 0.4166, Energy: -42.945709+0.000674j
[2025-08-27 02:35:45] [Iter 318/450] R1[167/300], Temp: 0.4115, Energy: -42.949661-0.001465j
[2025-08-27 02:35:58] [Iter 319/450] R1[168/300], Temp: 0.4063, Energy: -42.955503-0.002690j
[2025-08-27 02:36:10] [Iter 320/450] R1[169/300], Temp: 0.4012, Energy: -42.936075-0.000187j
[2025-08-27 02:36:22] [Iter 321/450] R1[170/300], Temp: 0.3960, Energy: -42.957037+0.001770j
[2025-08-27 02:36:35] [Iter 322/450] R1[171/300], Temp: 0.3909, Energy: -42.940013-0.001928j
[2025-08-27 02:36:47] [Iter 323/450] R1[172/300], Temp: 0.3858, Energy: -42.945683-0.002557j
[2025-08-27 02:36:59] [Iter 324/450] R1[173/300], Temp: 0.3807, Energy: -42.954145+0.001415j
[2025-08-27 02:37:12] [Iter 325/450] R1[174/300], Temp: 0.3757, Energy: -42.957046-0.000349j
[2025-08-27 02:37:24] [Iter 326/450] R1[175/300], Temp: 0.3706, Energy: -42.960987-0.004387j
[2025-08-27 02:37:36] [Iter 327/450] R1[176/300], Temp: 0.3655, Energy: -42.948303+0.006084j
[2025-08-27 02:37:49] [Iter 328/450] R1[177/300], Temp: 0.3605, Energy: -42.937170-0.001913j
[2025-08-27 02:38:01] [Iter 329/450] R1[178/300], Temp: 0.3555, Energy: -42.946160-0.001173j
[2025-08-27 02:38:14] [Iter 330/450] R1[179/300], Temp: 0.3505, Energy: -42.942785+0.003569j
[2025-08-27 02:38:26] [Iter 331/450] R1[180/300], Temp: 0.3455, Energy: -42.953284+0.000267j
[2025-08-27 02:38:38] [Iter 332/450] R1[181/300], Temp: 0.3405, Energy: -42.946246-0.003387j
[2025-08-27 02:38:51] [Iter 333/450] R1[182/300], Temp: 0.3356, Energy: -42.951567+0.001258j
[2025-08-27 02:39:03] [Iter 334/450] R1[183/300], Temp: 0.3306, Energy: -42.951067-0.001470j
[2025-08-27 02:39:15] [Iter 335/450] R1[184/300], Temp: 0.3257, Energy: -42.926158-0.000284j
[2025-08-27 02:39:28] [Iter 336/450] R1[185/300], Temp: 0.3208, Energy: -42.939678-0.005786j
[2025-08-27 02:39:40] [Iter 337/450] R1[186/300], Temp: 0.3159, Energy: -42.926168+0.002901j
[2025-08-27 02:39:52] [Iter 338/450] R1[187/300], Temp: 0.3111, Energy: -42.939537+0.000226j
[2025-08-27 02:40:05] [Iter 339/450] R1[188/300], Temp: 0.3062, Energy: -42.960140+0.001362j
[2025-08-27 02:40:17] [Iter 340/450] R1[189/300], Temp: 0.3014, Energy: -42.946441+0.000096j
[2025-08-27 02:40:30] [Iter 341/450] R1[190/300], Temp: 0.2966, Energy: -42.935509+0.002665j
[2025-08-27 02:40:42] [Iter 342/450] R1[191/300], Temp: 0.2919, Energy: -42.949070+0.005431j
[2025-08-27 02:40:54] [Iter 343/450] R1[192/300], Temp: 0.2871, Energy: -42.930820+0.006850j
[2025-08-27 02:41:07] [Iter 344/450] R1[193/300], Temp: 0.2824, Energy: -42.946758+0.003602j
[2025-08-27 02:41:19] [Iter 345/450] R1[194/300], Temp: 0.2777, Energy: -42.939728+0.001759j
[2025-08-27 02:41:31] [Iter 346/450] R1[195/300], Temp: 0.2730, Energy: -42.945109-0.007736j
[2025-08-27 02:41:44] [Iter 347/450] R1[196/300], Temp: 0.2684, Energy: -42.942236-0.002783j
[2025-08-27 02:41:56] [Iter 348/450] R1[197/300], Temp: 0.2637, Energy: -42.951774-0.000843j
[2025-08-27 02:42:08] [Iter 349/450] R1[198/300], Temp: 0.2591, Energy: -42.954653-0.001367j
[2025-08-27 02:42:21] [Iter 350/450] R1[199/300], Temp: 0.2545, Energy: -42.946477+0.004024j
[2025-08-27 02:42:21] ✓ Checkpoint saved: checkpoint_iter_000350.pkl
[2025-08-27 02:42:33] [Iter 351/450] R1[200/300], Temp: 0.2500, Energy: -42.943445+0.001604j
[2025-08-27 02:42:45] [Iter 352/450] R1[201/300], Temp: 0.2455, Energy: -42.935889+0.004568j
[2025-08-27 02:42:58] [Iter 353/450] R1[202/300], Temp: 0.2410, Energy: -42.946822+0.000622j
[2025-08-27 02:43:10] [Iter 354/450] R1[203/300], Temp: 0.2365, Energy: -42.946317+0.000437j
[2025-08-27 02:43:23] [Iter 355/450] R1[204/300], Temp: 0.2321, Energy: -42.946041-0.002314j
[2025-08-27 02:43:35] [Iter 356/450] R1[205/300], Temp: 0.2277, Energy: -42.938968+0.000667j
[2025-08-27 02:43:47] [Iter 357/450] R1[206/300], Temp: 0.2233, Energy: -42.949974+0.000606j
[2025-08-27 02:44:00] [Iter 358/450] R1[207/300], Temp: 0.2190, Energy: -42.939826-0.000223j
[2025-08-27 02:44:12] [Iter 359/450] R1[208/300], Temp: 0.2146, Energy: -42.940873-0.001359j
[2025-08-27 02:44:24] [Iter 360/450] R1[209/300], Temp: 0.2104, Energy: -42.939111+0.000678j
[2025-08-27 02:44:37] [Iter 361/450] R1[210/300], Temp: 0.2061, Energy: -42.941548-0.000238j
[2025-08-27 02:44:49] [Iter 362/450] R1[211/300], Temp: 0.2019, Energy: -42.924535-0.003001j
[2025-08-27 02:45:01] [Iter 363/450] R1[212/300], Temp: 0.1977, Energy: -42.963277-0.000572j
[2025-08-27 02:45:14] [Iter 364/450] R1[213/300], Temp: 0.1935, Energy: -42.938585+0.001662j
[2025-08-27 02:45:26] [Iter 365/450] R1[214/300], Temp: 0.1894, Energy: -42.947886-0.002876j
[2025-08-27 02:45:39] [Iter 366/450] R1[215/300], Temp: 0.1853, Energy: -42.947042-0.001640j
[2025-08-27 02:45:51] [Iter 367/450] R1[216/300], Temp: 0.1813, Energy: -42.943372-0.000036j
[2025-08-27 02:46:03] [Iter 368/450] R1[217/300], Temp: 0.1773, Energy: -42.946036+0.000462j
[2025-08-27 02:46:16] [Iter 369/450] R1[218/300], Temp: 0.1733, Energy: -42.948971-0.003721j
[2025-08-27 02:46:28] [Iter 370/450] R1[219/300], Temp: 0.1693, Energy: -42.953617-0.003226j
[2025-08-27 02:46:40] [Iter 371/450] R1[220/300], Temp: 0.1654, Energy: -42.942241+0.002516j
[2025-08-27 02:46:53] [Iter 372/450] R1[221/300], Temp: 0.1616, Energy: -42.956496+0.000889j
[2025-08-27 02:47:05] [Iter 373/450] R1[222/300], Temp: 0.1577, Energy: -42.945423+0.003929j
[2025-08-27 02:47:17] [Iter 374/450] R1[223/300], Temp: 0.1539, Energy: -42.949470-0.001938j
[2025-08-27 02:47:30] [Iter 375/450] R1[224/300], Temp: 0.1502, Energy: -42.939738+0.001154j
[2025-08-27 02:47:42] [Iter 376/450] R1[225/300], Temp: 0.1464, Energy: -42.958505-0.002914j
[2025-08-27 02:47:55] [Iter 377/450] R1[226/300], Temp: 0.1428, Energy: -42.933210+0.002383j
[2025-08-27 02:48:07] [Iter 378/450] R1[227/300], Temp: 0.1391, Energy: -42.945912-0.000704j
[2025-08-27 02:48:19] [Iter 379/450] R1[228/300], Temp: 0.1355, Energy: -42.932255-0.005216j
[2025-08-27 02:48:32] [Iter 380/450] R1[229/300], Temp: 0.1320, Energy: -42.974218-0.000439j
[2025-08-27 02:48:44] [Iter 381/450] R1[230/300], Temp: 0.1284, Energy: -42.933948+0.004466j
[2025-08-27 02:48:56] [Iter 382/450] R1[231/300], Temp: 0.1249, Energy: -42.972964+0.002741j
[2025-08-27 02:49:09] [Iter 383/450] R1[232/300], Temp: 0.1215, Energy: -42.959083+0.000460j
[2025-08-27 02:49:21] [Iter 384/450] R1[233/300], Temp: 0.1181, Energy: -42.950247-0.001208j
[2025-08-27 02:49:33] [Iter 385/450] R1[234/300], Temp: 0.1147, Energy: -42.954340+0.001678j
[2025-08-27 02:49:46] [Iter 386/450] R1[235/300], Temp: 0.1114, Energy: -42.946506-0.002352j
[2025-08-27 02:49:58] [Iter 387/450] R1[236/300], Temp: 0.1082, Energy: -42.939221+0.003278j
[2025-08-27 02:50:11] [Iter 388/450] R1[237/300], Temp: 0.1049, Energy: -42.946566-0.005042j
[2025-08-27 02:50:23] [Iter 389/450] R1[238/300], Temp: 0.1017, Energy: -42.941325+0.003271j
[2025-08-27 02:50:35] [Iter 390/450] R1[239/300], Temp: 0.0986, Energy: -42.942216-0.001482j
[2025-08-27 02:50:48] [Iter 391/450] R1[240/300], Temp: 0.0955, Energy: -42.942364-0.005904j
[2025-08-27 02:51:00] [Iter 392/450] R1[241/300], Temp: 0.0924, Energy: -42.952048+0.006317j
[2025-08-27 02:51:12] [Iter 393/450] R1[242/300], Temp: 0.0894, Energy: -42.931094+0.003787j
[2025-08-27 02:51:25] [Iter 394/450] R1[243/300], Temp: 0.0865, Energy: -42.962229+0.004696j
[2025-08-27 02:51:37] [Iter 395/450] R1[244/300], Temp: 0.0835, Energy: -42.943512-0.004230j
[2025-08-27 02:51:49] [Iter 396/450] R1[245/300], Temp: 0.0807, Energy: -42.957314-0.000013j
[2025-08-27 02:52:02] [Iter 397/450] R1[246/300], Temp: 0.0778, Energy: -42.945276-0.005148j
[2025-08-27 02:52:14] [Iter 398/450] R1[247/300], Temp: 0.0751, Energy: -42.951459+0.002822j
[2025-08-27 02:52:27] [Iter 399/450] R1[248/300], Temp: 0.0723, Energy: -42.959398+0.001301j
[2025-08-27 02:52:39] [Iter 400/450] R1[249/300], Temp: 0.0696, Energy: -42.953665+0.003370j
[2025-08-27 02:52:39] ✓ Checkpoint saved: checkpoint_iter_000400.pkl
[2025-08-27 02:52:51] [Iter 401/450] R1[250/300], Temp: 0.0670, Energy: -42.943086+0.000650j
[2025-08-27 02:53:04] [Iter 402/450] R1[251/300], Temp: 0.0644, Energy: -42.939150+0.000576j
[2025-08-27 02:53:16] [Iter 403/450] R1[252/300], Temp: 0.0618, Energy: -42.942849-0.002375j
[2025-08-27 02:53:28] [Iter 404/450] R1[253/300], Temp: 0.0593, Energy: -42.934420-0.001808j
[2025-08-27 02:53:41] [Iter 405/450] R1[254/300], Temp: 0.0569, Energy: -42.931650-0.004881j
[2025-08-27 02:53:53] [Iter 406/450] R1[255/300], Temp: 0.0545, Energy: -42.941643-0.002741j
[2025-08-27 02:54:05] [Iter 407/450] R1[256/300], Temp: 0.0521, Energy: -42.916859-0.003905j
[2025-08-27 02:54:18] [Iter 408/450] R1[257/300], Temp: 0.0498, Energy: -42.931569+0.002688j
[2025-08-27 02:54:30] [Iter 409/450] R1[258/300], Temp: 0.0476, Energy: -42.947734+0.002369j
[2025-08-27 02:54:43] [Iter 410/450] R1[259/300], Temp: 0.0454, Energy: -42.942814+0.000063j
[2025-08-27 02:54:55] [Iter 411/450] R1[260/300], Temp: 0.0432, Energy: -42.947888+0.002603j
[2025-08-27 02:55:07] [Iter 412/450] R1[261/300], Temp: 0.0411, Energy: -42.929922+0.000548j
[2025-08-27 02:55:20] [Iter 413/450] R1[262/300], Temp: 0.0391, Energy: -42.937061+0.000796j
[2025-08-27 02:55:32] [Iter 414/450] R1[263/300], Temp: 0.0371, Energy: -42.949158+0.002461j
[2025-08-27 02:55:44] [Iter 415/450] R1[264/300], Temp: 0.0351, Energy: -42.935787+0.003505j
[2025-08-27 02:55:57] [Iter 416/450] R1[265/300], Temp: 0.0332, Energy: -42.959619-0.000098j
[2025-08-27 02:56:09] [Iter 417/450] R1[266/300], Temp: 0.0314, Energy: -42.954814-0.001797j
[2025-08-27 02:56:21] [Iter 418/450] R1[267/300], Temp: 0.0296, Energy: -42.956883-0.000945j
[2025-08-27 02:56:34] [Iter 419/450] R1[268/300], Temp: 0.0278, Energy: -42.947832-0.001075j
[2025-08-27 02:56:46] [Iter 420/450] R1[269/300], Temp: 0.0261, Energy: -42.939778-0.003386j
[2025-08-27 02:56:59] [Iter 421/450] R1[270/300], Temp: 0.0245, Energy: -42.935059-0.001523j
[2025-08-27 02:57:11] [Iter 422/450] R1[271/300], Temp: 0.0229, Energy: -42.940224+0.003994j
[2025-08-27 02:57:23] [Iter 423/450] R1[272/300], Temp: 0.0213, Energy: -42.952236+0.001212j
[2025-08-27 02:57:36] [Iter 424/450] R1[273/300], Temp: 0.0199, Energy: -42.943447-0.001095j
[2025-08-27 02:57:48] [Iter 425/450] R1[274/300], Temp: 0.0184, Energy: -42.945761-0.000670j
[2025-08-27 02:58:00] [Iter 426/450] R1[275/300], Temp: 0.0170, Energy: -42.961587-0.000773j
[2025-08-27 02:58:13] [Iter 427/450] R1[276/300], Temp: 0.0157, Energy: -42.941086-0.003040j
[2025-08-27 02:58:25] [Iter 428/450] R1[277/300], Temp: 0.0144, Energy: -42.932765+0.000238j
[2025-08-27 02:58:37] [Iter 429/450] R1[278/300], Temp: 0.0132, Energy: -42.947164-0.001650j
[2025-08-27 02:58:50] [Iter 430/450] R1[279/300], Temp: 0.0120, Energy: -42.947920-0.004248j
[2025-08-27 02:59:02] [Iter 431/450] R1[280/300], Temp: 0.0109, Energy: -42.949186-0.003904j
[2025-08-27 02:59:15] [Iter 432/450] R1[281/300], Temp: 0.0099, Energy: -42.934659+0.001136j
[2025-08-27 02:59:27] [Iter 433/450] R1[282/300], Temp: 0.0089, Energy: -42.959236+0.002473j
[2025-08-27 02:59:39] [Iter 434/450] R1[283/300], Temp: 0.0079, Energy: -42.934708+0.001895j
[2025-08-27 02:59:52] [Iter 435/450] R1[284/300], Temp: 0.0070, Energy: -42.925470+0.003262j
[2025-08-27 03:00:04] [Iter 436/450] R1[285/300], Temp: 0.0062, Energy: -42.938628-0.001290j
[2025-08-27 03:00:16] [Iter 437/450] R1[286/300], Temp: 0.0054, Energy: -42.965934-0.000735j
[2025-08-27 03:00:29] [Iter 438/450] R1[287/300], Temp: 0.0046, Energy: -42.949705-0.004641j
[2025-08-27 03:00:41] [Iter 439/450] R1[288/300], Temp: 0.0039, Energy: -42.959953-0.002089j
[2025-08-27 03:00:54] [Iter 440/450] R1[289/300], Temp: 0.0033, Energy: -42.935161+0.000438j
[2025-08-27 03:01:06] [Iter 441/450] R1[290/300], Temp: 0.0027, Energy: -42.933249+0.001229j
[2025-08-27 03:01:18] [Iter 442/450] R1[291/300], Temp: 0.0022, Energy: -42.954639-0.006619j
[2025-08-27 03:01:31] [Iter 443/450] R1[292/300], Temp: 0.0018, Energy: -42.940619-0.002602j
[2025-08-27 03:01:43] [Iter 444/450] R1[293/300], Temp: 0.0013, Energy: -42.951274-0.000135j
[2025-08-27 03:01:55] [Iter 445/450] R1[294/300], Temp: 0.0010, Energy: -42.953446-0.002588j
[2025-08-27 03:02:08] [Iter 446/450] R1[295/300], Temp: 0.0007, Energy: -42.938494-0.005635j
[2025-08-27 03:02:20] [Iter 447/450] R1[296/300], Temp: 0.0004, Energy: -42.947767-0.001390j
[2025-08-27 03:02:32] [Iter 448/450] R1[297/300], Temp: 0.0002, Energy: -42.947918+0.003050j
[2025-08-27 03:02:45] [Iter 449/450] R1[298/300], Temp: 0.0001, Energy: -42.950421+0.001357j
[2025-08-27 03:02:57] [Iter 450/450] R1[299/300], Temp: 0.0000, Energy: -42.941273-0.001785j
[2025-08-27 03:02:57] ✓ Checkpoint saved: checkpoint_iter_000450.pkl
[2025-08-27 03:02:57] ✅ Training completed | Restarts: 1
[2025-08-27 03:02:57] ============================================================
[2025-08-27 03:02:57] Training completed | Runtime: 5609.5s
[2025-08-27 03:03:01] ✓ Final state saved: checkpoints/final_GCNN.pkl
[2025-08-27 03:03:01] ============================================================
