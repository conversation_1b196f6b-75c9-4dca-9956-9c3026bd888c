[2025-08-27 01:29:17] ✓ 从checkpoint恢复: results/L=5/J2=1.00/J1=0.82/training/checkpoints/final_GCNN.pkl
[2025-08-27 01:29:17]   - 迭代次数: final
[2025-08-27 01:29:17]   - 能量: -46.138794-0.001425j ± 0.008396
[2025-08-27 01:29:17]   - 时间戳: 2025-08-27T01:28:59.461703+08:00
[2025-08-27 01:29:28] ✓ 变分状态参数已从checkpoint恢复
[2025-08-27 01:29:28] ✓ 从final状态恢复, 重置迭代计数为0
[2025-08-27 01:29:28] ==================================================
[2025-08-27 01:29:28] GCNN for Shastry-Sutherland Model
[2025-08-27 01:29:28] ==================================================
[2025-08-27 01:29:28] System parameters:
[2025-08-27 01:29:28]   - System size: L=5, N=100
[2025-08-27 01:29:28]   - System parameters: J1=0.83, J2=1.0, Q=0.0
[2025-08-27 01:29:28] --------------------------------------------------
[2025-08-27 01:29:28] Model parameters:
[2025-08-27 01:29:28]   - Number of layers = 4
[2025-08-27 01:29:28]   - Number of features = 4
[2025-08-27 01:29:28]   - Total parameters = 19628
[2025-08-27 01:29:28] --------------------------------------------------
[2025-08-27 01:29:28] Training parameters:
[2025-08-27 01:29:28]   - Learning rate: 0.015
[2025-08-27 01:29:28]   - Total iterations: 450
[2025-08-27 01:29:28]   - Annealing cycles: 2
[2025-08-27 01:29:28]   - Initial period: 150
[2025-08-27 01:29:28]   - Period multiplier: 2.0
[2025-08-27 01:29:28]   - Temperature range: 0.0-1.0
[2025-08-27 01:29:28]   - Samples: 4096
[2025-08-27 01:29:28]   - Discarded samples: 0
[2025-08-27 01:29:28]   - Chunk size: 2048
[2025-08-27 01:29:28]   - Diagonal shift: 0.2
[2025-08-27 01:29:28]   - Gradient clipping: 1.0
[2025-08-27 01:29:28]   - Checkpoint enabled: interval=50
[2025-08-27 01:29:28]   - Checkpoint directory: results/L=5/J2=1.00/J1=0.83/training/checkpoints
[2025-08-27 01:29:28] --------------------------------------------------
[2025-08-27 01:29:28] Device status:
[2025-08-27 01:29:28]   - Devices model: NVIDIA H200 NVL
[2025-08-27 01:29:28]   - Number of devices: 1
[2025-08-27 01:29:28]   - Sharding: True
[2025-08-27 01:29:28] ============================================================
[2025-08-27 01:30:10] [Iter 1/450] R0[0/150], Temp: 1.0000, Energy: -46.754077-0.000896j
[2025-08-27 01:30:39] [Iter 2/450] R0[1/150], Temp: 0.9999, Energy: -46.773388+0.002259j
[2025-08-27 01:30:51] [Iter 3/450] R0[2/150], Temp: 0.9996, Energy: -46.757665-0.002626j
[2025-08-27 01:31:04] [Iter 4/450] R0[3/150], Temp: 0.9990, Energy: -46.762759-0.003272j
[2025-08-27 01:31:16] [Iter 5/450] R0[4/150], Temp: 0.9982, Energy: -46.776327-0.001283j
[2025-08-27 01:31:28] [Iter 6/450] R0[5/150], Temp: 0.9973, Energy: -46.772428-0.002768j
[2025-08-27 01:31:41] [Iter 7/450] R0[6/150], Temp: 0.9961, Energy: -46.768702+0.000848j
[2025-08-27 01:31:53] [Iter 8/450] R0[7/150], Temp: 0.9946, Energy: -46.777071+0.002621j
[2025-08-27 01:32:06] [Iter 9/450] R0[8/150], Temp: 0.9930, Energy: -46.795252-0.000042j
[2025-08-27 01:32:18] [Iter 10/450] R0[9/150], Temp: 0.9911, Energy: -46.784718-0.004682j
[2025-08-27 01:32:30] [Iter 11/450] R0[10/150], Temp: 0.9891, Energy: -46.776170-0.001304j
[2025-08-27 01:32:43] [Iter 12/450] R0[11/150], Temp: 0.9868, Energy: -46.775306+0.000292j
[2025-08-27 01:32:55] [Iter 13/450] R0[12/150], Temp: 0.9843, Energy: -46.807972+0.003624j
[2025-08-27 01:33:07] [Iter 14/450] R0[13/150], Temp: 0.9816, Energy: -46.783693-0.002327j
[2025-08-27 01:33:20] [Iter 15/450] R0[14/150], Temp: 0.9787, Energy: -46.792131-0.000903j
[2025-08-27 01:33:32] [Iter 16/450] R0[15/150], Temp: 0.9755, Energy: -46.783439-0.001794j
[2025-08-27 01:33:44] [Iter 17/450] R0[16/150], Temp: 0.9722, Energy: -46.774771+0.001642j
[2025-08-27 01:33:57] [Iter 18/450] R0[17/150], Temp: 0.9686, Energy: -46.776530-0.003157j
[2025-08-27 01:34:09] [Iter 19/450] R0[18/150], Temp: 0.9649, Energy: -46.782224+0.001611j
[2025-08-27 01:34:22] [Iter 20/450] R0[19/150], Temp: 0.9609, Energy: -46.788781+0.001975j
[2025-08-27 01:34:34] [Iter 21/450] R0[20/150], Temp: 0.9568, Energy: -46.774795+0.002448j
[2025-08-27 01:34:46] [Iter 22/450] R0[21/150], Temp: 0.9524, Energy: -46.793250-0.002091j
[2025-08-27 01:34:59] [Iter 23/450] R0[22/150], Temp: 0.9479, Energy: -46.783087-0.002748j
[2025-08-27 01:35:11] [Iter 24/450] R0[23/150], Temp: 0.9431, Energy: -46.793703+0.002653j
[2025-08-27 01:35:23] [Iter 25/450] R0[24/150], Temp: 0.9382, Energy: -46.792374-0.000923j
[2025-08-27 01:35:36] [Iter 26/450] R0[25/150], Temp: 0.9330, Energy: -46.764677+0.001636j
[2025-08-27 01:35:48] [Iter 27/450] R0[26/150], Temp: 0.9277, Energy: -46.785148-0.004053j
[2025-08-27 01:36:01] [Iter 28/450] R0[27/150], Temp: 0.9222, Energy: -46.793101+0.000263j
[2025-08-27 01:36:13] [Iter 29/450] R0[28/150], Temp: 0.9165, Energy: -46.786499-0.000557j
[2025-08-27 01:36:25] [Iter 30/450] R0[29/150], Temp: 0.9106, Energy: -46.786802-0.004625j
[2025-08-27 01:36:38] [Iter 31/450] R0[30/150], Temp: 0.9045, Energy: -46.776924-0.000206j
[2025-08-27 01:36:50] [Iter 32/450] R0[31/150], Temp: 0.8983, Energy: -46.773499+0.002818j
[2025-08-27 01:37:02] [Iter 33/450] R0[32/150], Temp: 0.8918, Energy: -46.779397+0.000047j
[2025-08-27 01:37:15] [Iter 34/450] R0[33/150], Temp: 0.8853, Energy: -46.784485-0.003795j
[2025-08-27 01:37:27] [Iter 35/450] R0[34/150], Temp: 0.8785, Energy: -46.773661-0.002086j
[2025-08-27 01:37:40] [Iter 36/450] R0[35/150], Temp: 0.8716, Energy: -46.783759+0.001821j
[2025-08-27 01:37:52] [Iter 37/450] R0[36/150], Temp: 0.8645, Energy: -46.783915-0.006160j
[2025-08-27 01:38:04] [Iter 38/450] R0[37/150], Temp: 0.8572, Energy: -46.788300+0.002076j
[2025-08-27 01:38:17] [Iter 39/450] R0[38/150], Temp: 0.8498, Energy: -46.790239-0.002319j
[2025-08-27 01:38:29] [Iter 40/450] R0[39/150], Temp: 0.8423, Energy: -46.784859+0.000810j
[2025-08-27 01:38:41] [Iter 41/450] R0[40/150], Temp: 0.8346, Energy: -46.796635+0.001148j
[2025-08-27 01:38:54] [Iter 42/450] R0[41/150], Temp: 0.8267, Energy: -46.795496+0.002653j
[2025-08-27 01:39:06] [Iter 43/450] R0[42/150], Temp: 0.8187, Energy: -46.788749+0.000402j
[2025-08-27 01:39:19] [Iter 44/450] R0[43/150], Temp: 0.8106, Energy: -46.779582+0.000486j
[2025-08-27 01:39:31] [Iter 45/450] R0[44/150], Temp: 0.8023, Energy: -46.786419-0.001003j
[2025-08-27 01:39:43] [Iter 46/450] R0[45/150], Temp: 0.7939, Energy: -46.785152-0.005757j
[2025-08-27 01:39:56] [Iter 47/450] R0[46/150], Temp: 0.7854, Energy: -46.784872+0.002135j
[2025-08-27 01:40:08] [Iter 48/450] R0[47/150], Temp: 0.7767, Energy: -46.784065+0.001403j
[2025-08-27 01:40:20] [Iter 49/450] R0[48/150], Temp: 0.7679, Energy: -46.793008+0.000116j
[2025-08-27 01:40:33] [Iter 50/450] R0[49/150], Temp: 0.7590, Energy: -46.770119+0.002745j
[2025-08-27 01:40:33] ✓ Checkpoint saved: checkpoint_iter_000050.pkl
[2025-08-27 01:40:45] [Iter 51/450] R0[50/150], Temp: 0.7500, Energy: -46.787450+0.000796j
[2025-08-27 01:40:58] [Iter 52/450] R0[51/150], Temp: 0.7409, Energy: -46.789222+0.001561j
[2025-08-27 01:41:10] [Iter 53/450] R0[52/150], Temp: 0.7316, Energy: -46.784642-0.001219j
[2025-08-27 01:41:22] [Iter 54/450] R0[53/150], Temp: 0.7223, Energy: -46.782339-0.002338j
[2025-08-27 01:41:35] [Iter 55/450] R0[54/150], Temp: 0.7129, Energy: -46.774855+0.003536j
[2025-08-27 01:41:47] [Iter 56/450] R0[55/150], Temp: 0.7034, Energy: -46.786194+0.000427j
[2025-08-27 01:41:59] [Iter 57/450] R0[56/150], Temp: 0.6938, Energy: -46.788897-0.000304j
[2025-08-27 01:42:12] [Iter 58/450] R0[57/150], Temp: 0.6841, Energy: -46.778350-0.000346j
[2025-08-27 01:42:24] [Iter 59/450] R0[58/150], Temp: 0.6743, Energy: -46.788379-0.004448j
[2025-08-27 01:42:37] [Iter 60/450] R0[59/150], Temp: 0.6644, Energy: -46.782408-0.000610j
[2025-08-27 01:42:49] [Iter 61/450] R0[60/150], Temp: 0.6545, Energy: -46.788983-0.001356j
[2025-08-27 01:43:01] [Iter 62/450] R0[61/150], Temp: 0.6445, Energy: -46.789037+0.002084j
[2025-08-27 01:43:14] [Iter 63/450] R0[62/150], Temp: 0.6345, Energy: -46.789199+0.000127j
[2025-08-27 01:43:26] [Iter 64/450] R0[63/150], Temp: 0.6243, Energy: -46.785940-0.003148j
[2025-08-27 01:43:38] [Iter 65/450] R0[64/150], Temp: 0.6142, Energy: -46.788606+0.000859j
[2025-08-27 01:43:51] [Iter 66/450] R0[65/150], Temp: 0.6040, Energy: -46.784392-0.002527j
[2025-08-27 01:44:03] [Iter 67/450] R0[66/150], Temp: 0.5937, Energy: -46.786064-0.002258j
[2025-08-27 01:44:15] [Iter 68/450] R0[67/150], Temp: 0.5834, Energy: -46.785599-0.002082j
[2025-08-27 01:44:28] [Iter 69/450] R0[68/150], Temp: 0.5730, Energy: -46.777728-0.001125j
[2025-08-27 01:44:40] [Iter 70/450] R0[69/150], Temp: 0.5627, Energy: -46.803131-0.003953j
[2025-08-27 01:44:53] [Iter 71/450] R0[70/150], Temp: 0.5523, Energy: -46.792568-0.000064j
[2025-08-27 01:45:05] [Iter 72/450] R0[71/150], Temp: 0.5418, Energy: -46.789616-0.001560j
[2025-08-27 01:45:17] [Iter 73/450] R0[72/150], Temp: 0.5314, Energy: -46.779416-0.002695j
[2025-08-27 01:45:30] [Iter 74/450] R0[73/150], Temp: 0.5209, Energy: -46.786046+0.001779j
[2025-08-27 01:45:42] [Iter 75/450] R0[74/150], Temp: 0.5105, Energy: -46.760892+0.002100j
[2025-08-27 01:45:54] [Iter 76/450] R0[75/150], Temp: 0.5000, Energy: -46.776441+0.002437j
[2025-08-27 01:46:07] [Iter 77/450] R0[76/150], Temp: 0.4895, Energy: -46.780411+0.001739j
[2025-08-27 01:46:19] [Iter 78/450] R0[77/150], Temp: 0.4791, Energy: -46.781563-0.001129j
[2025-08-27 01:46:31] [Iter 79/450] R0[78/150], Temp: 0.4686, Energy: -46.785383-0.000128j
[2025-08-27 01:46:44] [Iter 80/450] R0[79/150], Temp: 0.4582, Energy: -46.776531+0.001998j
[2025-08-27 01:46:56] [Iter 81/450] R0[80/150], Temp: 0.4477, Energy: -46.781978+0.000364j
[2025-08-27 01:47:09] [Iter 82/450] R0[81/150], Temp: 0.4373, Energy: -46.779524-0.005952j
[2025-08-27 01:47:21] [Iter 83/450] R0[82/150], Temp: 0.4270, Energy: -46.776915+0.001444j
[2025-08-27 01:47:33] [Iter 84/450] R0[83/150], Temp: 0.4166, Energy: -46.784345+0.002276j
[2025-08-27 01:47:46] [Iter 85/450] R0[84/150], Temp: 0.4063, Energy: -46.778686+0.001206j
[2025-08-27 01:47:58] [Iter 86/450] R0[85/150], Temp: 0.3960, Energy: -46.777353-0.001582j
[2025-08-27 01:48:10] [Iter 87/450] R0[86/150], Temp: 0.3858, Energy: -46.801134+0.003155j
[2025-08-27 01:48:23] [Iter 88/450] R0[87/150], Temp: 0.3757, Energy: -46.785114-0.000098j
[2025-08-27 01:48:35] [Iter 89/450] R0[88/150], Temp: 0.3655, Energy: -46.783681+0.003033j
[2025-08-27 01:48:48] [Iter 90/450] R0[89/150], Temp: 0.3555, Energy: -46.775526+0.000266j
[2025-08-27 01:49:00] [Iter 91/450] R0[90/150], Temp: 0.3455, Energy: -46.777481-0.001833j
[2025-08-27 01:49:12] [Iter 92/450] R0[91/150], Temp: 0.3356, Energy: -46.794043+0.009127j
[2025-08-27 01:49:25] [Iter 93/450] R0[92/150], Temp: 0.3257, Energy: -46.789073-0.002725j
[2025-08-27 01:49:37] [Iter 94/450] R0[93/150], Temp: 0.3159, Energy: -46.793572-0.001347j
[2025-08-27 01:49:49] [Iter 95/450] R0[94/150], Temp: 0.3062, Energy: -46.779572-0.005986j
[2025-08-27 01:50:02] [Iter 96/450] R0[95/150], Temp: 0.2966, Energy: -46.793136-0.000817j
[2025-08-27 01:50:14] [Iter 97/450] R0[96/150], Temp: 0.2871, Energy: -46.780020-0.003370j
[2025-08-27 01:50:26] [Iter 98/450] R0[97/150], Temp: 0.2777, Energy: -46.794709-0.002861j
[2025-08-27 01:50:39] [Iter 99/450] R0[98/150], Temp: 0.2684, Energy: -46.786885+0.001300j
[2025-08-27 01:50:51] [Iter 100/450] R0[99/150], Temp: 0.2591, Energy: -46.784780-0.007085j
[2025-08-27 01:50:51] ✓ Checkpoint saved: checkpoint_iter_000100.pkl
[2025-08-27 01:51:04] [Iter 101/450] R0[100/150], Temp: 0.2500, Energy: -46.787476-0.005043j
[2025-08-27 01:51:16] [Iter 102/450] R0[101/150], Temp: 0.2410, Energy: -46.781971-0.002175j
[2025-08-27 01:51:28] [Iter 103/450] R0[102/150], Temp: 0.2321, Energy: -46.791197-0.001614j
[2025-08-27 01:51:41] [Iter 104/450] R0[103/150], Temp: 0.2233, Energy: -46.781385-0.000951j
[2025-08-27 01:51:53] [Iter 105/450] R0[104/150], Temp: 0.2146, Energy: -46.786127-0.000932j
[2025-08-27 01:52:05] [Iter 106/450] R0[105/150], Temp: 0.2061, Energy: -46.781803+0.004554j
[2025-08-27 01:52:18] [Iter 107/450] R0[106/150], Temp: 0.1977, Energy: -46.781701-0.001682j
[2025-08-27 01:52:30] [Iter 108/450] R0[107/150], Temp: 0.1894, Energy: -46.783584-0.000798j
[2025-08-27 01:52:42] [Iter 109/450] R0[108/150], Temp: 0.1813, Energy: -46.786422+0.000367j
[2025-08-27 01:52:55] [Iter 110/450] R0[109/150], Temp: 0.1733, Energy: -46.788665+0.002375j
[2025-08-27 01:53:07] [Iter 111/450] R0[110/150], Temp: 0.1654, Energy: -46.785778+0.003295j
[2025-08-27 01:53:19] [Iter 112/450] R0[111/150], Temp: 0.1577, Energy: -46.786131-0.001492j
[2025-08-27 01:53:32] [Iter 113/450] R0[112/150], Temp: 0.1502, Energy: -46.773102+0.000662j
[2025-08-27 01:53:44] [Iter 114/450] R0[113/150], Temp: 0.1428, Energy: -46.771848-0.004397j
[2025-08-27 01:53:57] [Iter 115/450] R0[114/150], Temp: 0.1355, Energy: -46.770228+0.002281j
[2025-08-27 01:54:09] [Iter 116/450] R0[115/150], Temp: 0.1284, Energy: -46.782562+0.002361j
[2025-08-27 01:54:21] [Iter 117/450] R0[116/150], Temp: 0.1215, Energy: -46.795296-0.001010j
[2025-08-27 01:54:34] [Iter 118/450] R0[117/150], Temp: 0.1147, Energy: -46.796852+0.001222j
[2025-08-27 01:54:46] [Iter 119/450] R0[118/150], Temp: 0.1082, Energy: -46.785746+0.000661j
[2025-08-27 01:54:58] [Iter 120/450] R0[119/150], Temp: 0.1017, Energy: -46.782668-0.005136j
[2025-08-27 01:55:11] [Iter 121/450] R0[120/150], Temp: 0.0955, Energy: -46.795090+0.001538j
[2025-08-27 01:55:23] [Iter 122/450] R0[121/150], Temp: 0.0894, Energy: -46.788656+0.002496j
[2025-08-27 01:55:35] [Iter 123/450] R0[122/150], Temp: 0.0835, Energy: -46.793851+0.001098j
[2025-08-27 01:55:48] [Iter 124/450] R0[123/150], Temp: 0.0778, Energy: -46.786161+0.000893j
[2025-08-27 01:56:00] [Iter 125/450] R0[124/150], Temp: 0.0723, Energy: -46.792086+0.001175j
[2025-08-27 01:56:12] [Iter 126/450] R0[125/150], Temp: 0.0670, Energy: -46.784424-0.005339j
[2025-08-27 01:56:25] [Iter 127/450] R0[126/150], Temp: 0.0618, Energy: -46.783324+0.001774j
[2025-08-27 01:56:37] [Iter 128/450] R0[127/150], Temp: 0.0569, Energy: -46.780440+0.000769j
[2025-08-27 01:56:50] [Iter 129/450] R0[128/150], Temp: 0.0521, Energy: -46.791095+0.000654j
[2025-08-27 01:57:02] [Iter 130/450] R0[129/150], Temp: 0.0476, Energy: -46.793601+0.001276j
[2025-08-27 01:57:14] [Iter 131/450] R0[130/150], Temp: 0.0432, Energy: -46.775906-0.005427j
[2025-08-27 01:57:27] [Iter 132/450] R0[131/150], Temp: 0.0391, Energy: -46.789839+0.001619j
[2025-08-27 01:57:39] [Iter 133/450] R0[132/150], Temp: 0.0351, Energy: -46.775489-0.003065j
[2025-08-27 01:57:51] [Iter 134/450] R0[133/150], Temp: 0.0314, Energy: -46.780986+0.001759j
[2025-08-27 01:58:04] [Iter 135/450] R0[134/150], Temp: 0.0278, Energy: -46.784102+0.003198j
[2025-08-27 01:58:16] [Iter 136/450] R0[135/150], Temp: 0.0245, Energy: -46.794267+0.002701j
[2025-08-27 01:58:28] [Iter 137/450] R0[136/150], Temp: 0.0213, Energy: -46.787253-0.002806j
[2025-08-27 01:58:41] [Iter 138/450] R0[137/150], Temp: 0.0184, Energy: -46.792797-0.002890j
[2025-08-27 01:58:53] [Iter 139/450] R0[138/150], Temp: 0.0157, Energy: -46.782010-0.004272j
[2025-08-27 01:59:06] [Iter 140/450] R0[139/150], Temp: 0.0132, Energy: -46.780870-0.000451j
[2025-08-27 01:59:18] [Iter 141/450] R0[140/150], Temp: 0.0109, Energy: -46.785542+0.000176j
[2025-08-27 01:59:30] [Iter 142/450] R0[141/150], Temp: 0.0089, Energy: -46.783227-0.000241j
[2025-08-27 01:59:43] [Iter 143/450] R0[142/150], Temp: 0.0070, Energy: -46.790504-0.001775j
[2025-08-27 01:59:55] [Iter 144/450] R0[143/150], Temp: 0.0054, Energy: -46.786054-0.001075j
[2025-08-27 02:00:07] [Iter 145/450] R0[144/150], Temp: 0.0039, Energy: -46.771231-0.000202j
[2025-08-27 02:00:20] [Iter 146/450] R0[145/150], Temp: 0.0027, Energy: -46.781092+0.001754j
[2025-08-27 02:00:32] [Iter 147/450] R0[146/150], Temp: 0.0018, Energy: -46.784956+0.002883j
[2025-08-27 02:00:44] [Iter 148/450] R0[147/150], Temp: 0.0010, Energy: -46.784471-0.003085j
[2025-08-27 02:00:57] [Iter 149/450] R0[148/150], Temp: 0.0004, Energy: -46.793397-0.003977j
[2025-08-27 02:01:09] [Iter 150/450] R0[149/150], Temp: 0.0001, Energy: -46.793130-0.000510j
[2025-08-27 02:01:09] ✓ Checkpoint saved: checkpoint_iter_000150.pkl
[2025-08-27 02:01:09] RESTART #1 | Period: 300
[2025-08-27 02:01:22] [Iter 151/450] R1[0/300], Temp: 1.0000, Energy: -46.782400-0.001039j
[2025-08-27 02:01:34] [Iter 152/450] R1[1/300], Temp: 1.0000, Energy: -46.788580-0.001251j
[2025-08-27 02:01:46] [Iter 153/450] R1[2/300], Temp: 0.9999, Energy: -46.798587+0.001160j
[2025-08-27 02:01:59] [Iter 154/450] R1[3/300], Temp: 0.9998, Energy: -46.789675+0.000067j
[2025-08-27 02:02:11] [Iter 155/450] R1[4/300], Temp: 0.9996, Energy: -46.789822-0.000010j
[2025-08-27 02:02:23] [Iter 156/450] R1[5/300], Temp: 0.9993, Energy: -46.794222+0.003171j
[2025-08-27 02:02:36] [Iter 157/450] R1[6/300], Temp: 0.9990, Energy: -46.794643+0.003023j
[2025-08-27 02:02:48] [Iter 158/450] R1[7/300], Temp: 0.9987, Energy: -46.788072+0.001282j
[2025-08-27 02:03:00] [Iter 159/450] R1[8/300], Temp: 0.9982, Energy: -46.786853-0.000819j
[2025-08-27 02:03:13] [Iter 160/450] R1[9/300], Temp: 0.9978, Energy: -46.789044+0.000987j
[2025-08-27 02:03:25] [Iter 161/450] R1[10/300], Temp: 0.9973, Energy: -46.791560-0.000648j
[2025-08-27 02:03:38] [Iter 162/450] R1[11/300], Temp: 0.9967, Energy: -46.799266-0.001046j
[2025-08-27 02:03:50] [Iter 163/450] R1[12/300], Temp: 0.9961, Energy: -46.794525+0.003432j
[2025-08-27 02:04:02] [Iter 164/450] R1[13/300], Temp: 0.9954, Energy: -46.760221+0.004062j
[2025-08-27 02:04:15] [Iter 165/450] R1[14/300], Temp: 0.9946, Energy: -46.787126+0.001602j
[2025-08-27 02:04:27] [Iter 166/450] R1[15/300], Temp: 0.9938, Energy: -46.784144+0.005684j
[2025-08-27 02:04:39] [Iter 167/450] R1[16/300], Temp: 0.9930, Energy: -46.793766+0.002282j
[2025-08-27 02:04:52] [Iter 168/450] R1[17/300], Temp: 0.9921, Energy: -46.791364+0.002196j
[2025-08-27 02:05:04] [Iter 169/450] R1[18/300], Temp: 0.9911, Energy: -46.795416+0.001199j
[2025-08-27 02:05:16] [Iter 170/450] R1[19/300], Temp: 0.9901, Energy: -46.788520-0.000973j
[2025-08-27 02:05:29] [Iter 171/450] R1[20/300], Temp: 0.9891, Energy: -46.768028+0.000327j
[2025-08-27 02:05:41] [Iter 172/450] R1[21/300], Temp: 0.9880, Energy: -46.788024+0.001906j
[2025-08-27 02:05:54] [Iter 173/450] R1[22/300], Temp: 0.9868, Energy: -46.782444-0.000550j
[2025-08-27 02:06:06] [Iter 174/450] R1[23/300], Temp: 0.9856, Energy: -46.780680+0.001869j
[2025-08-27 02:06:18] [Iter 175/450] R1[24/300], Temp: 0.9843, Energy: -46.781837-0.002050j
[2025-08-27 02:06:31] [Iter 176/450] R1[25/300], Temp: 0.9830, Energy: -46.783581+0.002170j
[2025-08-27 02:06:43] [Iter 177/450] R1[26/300], Temp: 0.9816, Energy: -46.793308-0.001294j
[2025-08-27 02:06:55] [Iter 178/450] R1[27/300], Temp: 0.9801, Energy: -46.784808+0.001266j
[2025-08-27 02:07:08] [Iter 179/450] R1[28/300], Temp: 0.9787, Energy: -46.767369+0.005859j
[2025-08-27 02:07:20] [Iter 180/450] R1[29/300], Temp: 0.9771, Energy: -46.765826-0.001719j
[2025-08-27 02:07:32] [Iter 181/450] R1[30/300], Temp: 0.9755, Energy: -46.767880+0.004095j
[2025-08-27 02:07:45] [Iter 182/450] R1[31/300], Temp: 0.9739, Energy: -46.781876-0.000361j
[2025-08-27 02:07:57] [Iter 183/450] R1[32/300], Temp: 0.9722, Energy: -46.796635-0.000429j
[2025-08-27 02:08:10] [Iter 184/450] R1[33/300], Temp: 0.9704, Energy: -46.787953-0.001299j
[2025-08-27 02:08:22] [Iter 185/450] R1[34/300], Temp: 0.9686, Energy: -46.782502-0.003817j
[2025-08-27 02:08:34] [Iter 186/450] R1[35/300], Temp: 0.9668, Energy: -46.795529-0.001305j
[2025-08-27 02:08:47] [Iter 187/450] R1[36/300], Temp: 0.9649, Energy: -46.796960+0.000673j
[2025-08-27 02:08:59] [Iter 188/450] R1[37/300], Temp: 0.9629, Energy: -46.792614-0.003570j
[2025-08-27 02:09:11] [Iter 189/450] R1[38/300], Temp: 0.9609, Energy: -46.788110-0.001986j
[2025-08-27 02:09:24] [Iter 190/450] R1[39/300], Temp: 0.9589, Energy: -46.773170+0.000259j
[2025-08-27 02:09:36] [Iter 191/450] R1[40/300], Temp: 0.9568, Energy: -46.785903+0.001540j
[2025-08-27 02:09:48] [Iter 192/450] R1[41/300], Temp: 0.9546, Energy: -46.773284-0.002965j
[2025-08-27 02:10:01] [Iter 193/450] R1[42/300], Temp: 0.9524, Energy: -46.788975-0.000434j
[2025-08-27 02:10:13] [Iter 194/450] R1[43/300], Temp: 0.9502, Energy: -46.773216+0.000065j
[2025-08-27 02:10:26] [Iter 195/450] R1[44/300], Temp: 0.9479, Energy: -46.780551+0.000950j
[2025-08-27 02:10:38] [Iter 196/450] R1[45/300], Temp: 0.9455, Energy: -46.780646-0.002685j
[2025-08-27 02:10:50] [Iter 197/450] R1[46/300], Temp: 0.9431, Energy: -46.768785-0.000253j
[2025-08-27 02:11:03] [Iter 198/450] R1[47/300], Temp: 0.9407, Energy: -46.793815-0.000253j
[2025-08-27 02:11:15] [Iter 199/450] R1[48/300], Temp: 0.9382, Energy: -46.777453+0.002464j
[2025-08-27 02:11:27] [Iter 200/450] R1[49/300], Temp: 0.9356, Energy: -46.790209+0.002594j
[2025-08-27 02:11:27] ✓ Checkpoint saved: checkpoint_iter_000200.pkl
[2025-08-27 02:11:40] [Iter 201/450] R1[50/300], Temp: 0.9330, Energy: -46.781915+0.001283j
[2025-08-27 02:11:52] [Iter 202/450] R1[51/300], Temp: 0.9304, Energy: -46.785754+0.001705j
[2025-08-27 02:12:04] [Iter 203/450] R1[52/300], Temp: 0.9277, Energy: -46.788515-0.000430j
[2025-08-27 02:12:17] [Iter 204/450] R1[53/300], Temp: 0.9249, Energy: -46.781775-0.000947j
[2025-08-27 02:12:29] [Iter 205/450] R1[54/300], Temp: 0.9222, Energy: -46.807231+0.002226j
[2025-08-27 02:12:42] [Iter 206/450] R1[55/300], Temp: 0.9193, Energy: -46.781680+0.002896j
[2025-08-27 02:12:54] [Iter 207/450] R1[56/300], Temp: 0.9165, Energy: -46.778484+0.001268j
[2025-08-27 02:13:06] [Iter 208/450] R1[57/300], Temp: 0.9135, Energy: -46.778559+0.001444j
[2025-08-27 02:13:19] [Iter 209/450] R1[58/300], Temp: 0.9106, Energy: -46.782531-0.001751j
[2025-08-27 02:13:31] [Iter 210/450] R1[59/300], Temp: 0.9076, Energy: -46.784304+0.001973j
[2025-08-27 02:13:43] [Iter 211/450] R1[60/300], Temp: 0.9045, Energy: -46.812894+0.005170j
[2025-08-27 02:13:56] [Iter 212/450] R1[61/300], Temp: 0.9014, Energy: -46.777331-0.001444j
[2025-08-27 02:14:08] [Iter 213/450] R1[62/300], Temp: 0.8983, Energy: -46.796489-0.002882j
[2025-08-27 02:14:20] [Iter 214/450] R1[63/300], Temp: 0.8951, Energy: -46.776397+0.005507j
[2025-08-27 02:14:33] [Iter 215/450] R1[64/300], Temp: 0.8918, Energy: -46.781429+0.000437j
[2025-08-27 02:14:45] [Iter 216/450] R1[65/300], Temp: 0.8886, Energy: -46.789090+0.002191j
[2025-08-27 02:14:58] [Iter 217/450] R1[66/300], Temp: 0.8853, Energy: -46.784553-0.003203j
[2025-08-27 02:15:10] [Iter 218/450] R1[67/300], Temp: 0.8819, Energy: -46.788534-0.001344j
[2025-08-27 02:15:22] [Iter 219/450] R1[68/300], Temp: 0.8785, Energy: -46.786164+0.004102j
[2025-08-27 02:15:35] [Iter 220/450] R1[69/300], Temp: 0.8751, Energy: -46.788155-0.001075j
[2025-08-27 02:15:47] [Iter 221/450] R1[70/300], Temp: 0.8716, Energy: -46.788446+0.001861j
[2025-08-27 02:15:59] [Iter 222/450] R1[71/300], Temp: 0.8680, Energy: -46.783752-0.000284j
[2025-08-27 02:16:12] [Iter 223/450] R1[72/300], Temp: 0.8645, Energy: -46.788952-0.002935j
[2025-08-27 02:16:24] [Iter 224/450] R1[73/300], Temp: 0.8609, Energy: -46.776934+0.001349j
[2025-08-27 02:16:36] [Iter 225/450] R1[74/300], Temp: 0.8572, Energy: -46.776591-0.001617j
[2025-08-27 02:16:49] [Iter 226/450] R1[75/300], Temp: 0.8536, Energy: -46.793850-0.001198j
[2025-08-27 02:17:01] [Iter 227/450] R1[76/300], Temp: 0.8498, Energy: -46.797254-0.002480j
[2025-08-27 02:17:14] [Iter 228/450] R1[77/300], Temp: 0.8461, Energy: -46.771896-0.000042j
[2025-08-27 02:17:26] [Iter 229/450] R1[78/300], Temp: 0.8423, Energy: -46.775859-0.002541j
[2025-08-27 02:17:38] [Iter 230/450] R1[79/300], Temp: 0.8384, Energy: -46.785416-0.001987j
[2025-08-27 02:17:51] [Iter 231/450] R1[80/300], Temp: 0.8346, Energy: -46.790447+0.000349j
[2025-08-27 02:18:03] [Iter 232/450] R1[81/300], Temp: 0.8307, Energy: -46.780673+0.001480j
[2025-08-27 02:18:15] [Iter 233/450] R1[82/300], Temp: 0.8267, Energy: -46.787604+0.001358j
[2025-08-27 02:18:28] [Iter 234/450] R1[83/300], Temp: 0.8227, Energy: -46.785763+0.001546j
[2025-08-27 02:18:40] [Iter 235/450] R1[84/300], Temp: 0.8187, Energy: -46.783523+0.001770j
[2025-08-27 02:18:52] [Iter 236/450] R1[85/300], Temp: 0.8147, Energy: -46.774954-0.000241j
[2025-08-27 02:19:05] [Iter 237/450] R1[86/300], Temp: 0.8106, Energy: -46.785517-0.002686j
[2025-08-27 02:19:17] [Iter 238/450] R1[87/300], Temp: 0.8065, Energy: -46.796603-0.001470j
[2025-08-27 02:19:30] [Iter 239/450] R1[88/300], Temp: 0.8023, Energy: -46.785229-0.001037j
[2025-08-27 02:19:42] [Iter 240/450] R1[89/300], Temp: 0.7981, Energy: -46.770540-0.000151j
[2025-08-27 02:19:54] [Iter 241/450] R1[90/300], Temp: 0.7939, Energy: -46.777466-0.004767j
[2025-08-27 02:20:07] [Iter 242/450] R1[91/300], Temp: 0.7896, Energy: -46.784588-0.002893j
[2025-08-27 02:20:19] [Iter 243/450] R1[92/300], Temp: 0.7854, Energy: -46.770822+0.000247j
[2025-08-27 02:20:31] [Iter 244/450] R1[93/300], Temp: 0.7810, Energy: -46.773106-0.004434j
[2025-08-27 02:20:44] [Iter 245/450] R1[94/300], Temp: 0.7767, Energy: -46.801293-0.000436j
[2025-08-27 02:20:56] [Iter 246/450] R1[95/300], Temp: 0.7723, Energy: -46.780191-0.004579j
[2025-08-27 02:21:08] [Iter 247/450] R1[96/300], Temp: 0.7679, Energy: -46.794185+0.001126j
[2025-08-27 02:21:21] [Iter 248/450] R1[97/300], Temp: 0.7635, Energy: -46.785027+0.001060j
[2025-08-27 02:21:33] [Iter 249/450] R1[98/300], Temp: 0.7590, Energy: -46.783940+0.001564j
[2025-08-27 02:21:45] [Iter 250/450] R1[99/300], Temp: 0.7545, Energy: -46.797834-0.000954j
[2025-08-27 02:21:45] ✓ Checkpoint saved: checkpoint_iter_000250.pkl
[2025-08-27 02:21:58] [Iter 251/450] R1[100/300], Temp: 0.7500, Energy: -46.776920+0.002020j
[2025-08-27 02:22:10] [Iter 252/450] R1[101/300], Temp: 0.7455, Energy: -46.789226-0.000348j
[2025-08-27 02:22:23] [Iter 253/450] R1[102/300], Temp: 0.7409, Energy: -46.791371+0.003107j
[2025-08-27 02:22:35] [Iter 254/450] R1[103/300], Temp: 0.7363, Energy: -46.783208-0.001839j
[2025-08-27 02:22:47] [Iter 255/450] R1[104/300], Temp: 0.7316, Energy: -46.791932-0.002867j
[2025-08-27 02:23:00] [Iter 256/450] R1[105/300], Temp: 0.7270, Energy: -46.792953-0.004306j
[2025-08-27 02:23:12] [Iter 257/450] R1[106/300], Temp: 0.7223, Energy: -46.772440-0.000307j
[2025-08-27 02:23:24] [Iter 258/450] R1[107/300], Temp: 0.7176, Energy: -46.787566+0.003506j
[2025-08-27 02:23:37] [Iter 259/450] R1[108/300], Temp: 0.7129, Energy: -46.788512-0.000645j
[2025-08-27 02:23:49] [Iter 260/450] R1[109/300], Temp: 0.7081, Energy: -46.793717-0.000710j
[2025-08-27 02:24:01] [Iter 261/450] R1[110/300], Temp: 0.7034, Energy: -46.799563+0.000609j
[2025-08-27 02:24:14] [Iter 262/450] R1[111/300], Temp: 0.6986, Energy: -46.792364+0.000759j
[2025-08-27 02:24:26] [Iter 263/450] R1[112/300], Temp: 0.6938, Energy: -46.787699+0.001867j
[2025-08-27 02:24:39] [Iter 264/450] R1[113/300], Temp: 0.6889, Energy: -46.783471-0.002307j
[2025-08-27 02:24:51] [Iter 265/450] R1[114/300], Temp: 0.6841, Energy: -46.776582+0.004226j
[2025-08-27 02:25:03] [Iter 266/450] R1[115/300], Temp: 0.6792, Energy: -46.801343-0.001143j
[2025-08-27 02:25:16] [Iter 267/450] R1[116/300], Temp: 0.6743, Energy: -46.779061-0.000290j
[2025-08-27 02:25:28] [Iter 268/450] R1[117/300], Temp: 0.6694, Energy: -46.785992+0.000104j
[2025-08-27 02:25:40] [Iter 269/450] R1[118/300], Temp: 0.6644, Energy: -46.778127-0.003740j
[2025-08-27 02:25:53] [Iter 270/450] R1[119/300], Temp: 0.6595, Energy: -46.782293+0.003775j
[2025-08-27 02:26:05] [Iter 271/450] R1[120/300], Temp: 0.6545, Energy: -46.794169-0.004114j
[2025-08-27 02:26:18] [Iter 272/450] R1[121/300], Temp: 0.6495, Energy: -46.772251+0.001162j
[2025-08-27 02:26:30] [Iter 273/450] R1[122/300], Temp: 0.6445, Energy: -46.784701-0.004590j
[2025-08-27 02:26:42] [Iter 274/450] R1[123/300], Temp: 0.6395, Energy: -46.792403-0.004292j
[2025-08-27 02:26:55] [Iter 275/450] R1[124/300], Temp: 0.6345, Energy: -46.794192+0.001417j
[2025-08-27 02:27:07] [Iter 276/450] R1[125/300], Temp: 0.6294, Energy: -46.793255-0.001790j
[2025-08-27 02:27:19] [Iter 277/450] R1[126/300], Temp: 0.6243, Energy: -46.783542-0.005073j
[2025-08-27 02:27:32] [Iter 278/450] R1[127/300], Temp: 0.6193, Energy: -46.776716-0.002323j
[2025-08-27 02:27:44] [Iter 279/450] R1[128/300], Temp: 0.6142, Energy: -46.798396+0.003114j
[2025-08-27 02:27:56] [Iter 280/450] R1[129/300], Temp: 0.6091, Energy: -46.785073+0.001575j
[2025-08-27 02:28:09] [Iter 281/450] R1[130/300], Temp: 0.6040, Energy: -46.786775+0.001834j
[2025-08-27 02:28:21] [Iter 282/450] R1[131/300], Temp: 0.5988, Energy: -46.800747+0.001349j
[2025-08-27 02:28:34] [Iter 283/450] R1[132/300], Temp: 0.5937, Energy: -46.779516+0.000453j
[2025-08-27 02:28:46] [Iter 284/450] R1[133/300], Temp: 0.5885, Energy: -46.783958-0.001688j
[2025-08-27 02:28:58] [Iter 285/450] R1[134/300], Temp: 0.5834, Energy: -46.796412+0.004576j
[2025-08-27 02:29:11] [Iter 286/450] R1[135/300], Temp: 0.5782, Energy: -46.786692-0.000118j
[2025-08-27 02:29:23] [Iter 287/450] R1[136/300], Temp: 0.5730, Energy: -46.800227-0.001109j
[2025-08-27 02:29:35] [Iter 288/450] R1[137/300], Temp: 0.5679, Energy: -46.776276-0.000224j
[2025-08-27 02:29:48] [Iter 289/450] R1[138/300], Temp: 0.5627, Energy: -46.787632-0.001806j
[2025-08-27 02:30:00] [Iter 290/450] R1[139/300], Temp: 0.5575, Energy: -46.787936-0.002798j
[2025-08-27 02:30:12] [Iter 291/450] R1[140/300], Temp: 0.5523, Energy: -46.783892+0.002660j
[2025-08-27 02:30:25] [Iter 292/450] R1[141/300], Temp: 0.5471, Energy: -46.788102+0.007424j
[2025-08-27 02:30:37] [Iter 293/450] R1[142/300], Temp: 0.5418, Energy: -46.799718+0.003246j
[2025-08-27 02:30:49] [Iter 294/450] R1[143/300], Temp: 0.5366, Energy: -46.791685-0.002756j
[2025-08-27 02:31:02] [Iter 295/450] R1[144/300], Temp: 0.5314, Energy: -46.781777-0.000214j
[2025-08-27 02:31:14] [Iter 296/450] R1[145/300], Temp: 0.5262, Energy: -46.792941-0.001019j
[2025-08-27 02:31:26] [Iter 297/450] R1[146/300], Temp: 0.5209, Energy: -46.781180+0.000482j
[2025-08-27 02:31:39] [Iter 298/450] R1[147/300], Temp: 0.5157, Energy: -46.778672+0.001719j
[2025-08-27 02:31:51] [Iter 299/450] R1[148/300], Temp: 0.5105, Energy: -46.786178+0.003213j
[2025-08-27 02:32:03] [Iter 300/450] R1[149/300], Temp: 0.5052, Energy: -46.807892+0.001830j
[2025-08-27 02:32:04] ✓ Checkpoint saved: checkpoint_iter_000300.pkl
[2025-08-27 02:32:16] [Iter 301/450] R1[150/300], Temp: 0.5000, Energy: -46.786951-0.000927j
[2025-08-27 02:32:28] [Iter 302/450] R1[151/300], Temp: 0.4948, Energy: -46.775877+0.000624j
[2025-08-27 02:32:41] [Iter 303/450] R1[152/300], Temp: 0.4895, Energy: -46.790915-0.000353j
[2025-08-27 02:32:53] [Iter 304/450] R1[153/300], Temp: 0.4843, Energy: -46.789987-0.000963j
[2025-08-27 02:33:05] [Iter 305/450] R1[154/300], Temp: 0.4791, Energy: -46.794382+0.000713j
[2025-08-27 02:33:18] [Iter 306/450] R1[155/300], Temp: 0.4738, Energy: -46.775893-0.000217j
[2025-08-27 02:33:30] [Iter 307/450] R1[156/300], Temp: 0.4686, Energy: -46.777809+0.001590j
[2025-08-27 02:33:42] [Iter 308/450] R1[157/300], Temp: 0.4634, Energy: -46.792033+0.002485j
[2025-08-27 02:33:55] [Iter 309/450] R1[158/300], Temp: 0.4582, Energy: -46.797804+0.001865j
[2025-08-27 02:34:07] [Iter 310/450] R1[159/300], Temp: 0.4529, Energy: -46.795951-0.004335j
[2025-08-27 02:34:20] [Iter 311/450] R1[160/300], Temp: 0.4477, Energy: -46.790280+0.002357j
[2025-08-27 02:34:32] [Iter 312/450] R1[161/300], Temp: 0.4425, Energy: -46.779434-0.001802j
[2025-08-27 02:34:44] [Iter 313/450] R1[162/300], Temp: 0.4373, Energy: -46.780742-0.000700j
[2025-08-27 02:34:57] [Iter 314/450] R1[163/300], Temp: 0.4321, Energy: -46.785170+0.002205j
[2025-08-27 02:35:09] [Iter 315/450] R1[164/300], Temp: 0.4270, Energy: -46.792873+0.002735j
[2025-08-27 02:35:21] [Iter 316/450] R1[165/300], Temp: 0.4218, Energy: -46.777559+0.002758j
[2025-08-27 02:35:34] [Iter 317/450] R1[166/300], Temp: 0.4166, Energy: -46.795421-0.000762j
[2025-08-27 02:35:46] [Iter 318/450] R1[167/300], Temp: 0.4115, Energy: -46.789272+0.000261j
[2025-08-27 02:35:58] [Iter 319/450] R1[168/300], Temp: 0.4063, Energy: -46.777769-0.001281j
[2025-08-27 02:36:11] [Iter 320/450] R1[169/300], Temp: 0.4012, Energy: -46.790587-0.000494j
[2025-08-27 02:36:23] [Iter 321/450] R1[170/300], Temp: 0.3960, Energy: -46.797406+0.000372j
[2025-08-27 02:36:36] [Iter 322/450] R1[171/300], Temp: 0.3909, Energy: -46.780087+0.003567j
[2025-08-27 02:36:48] [Iter 323/450] R1[172/300], Temp: 0.3858, Energy: -46.791280+0.001532j
[2025-08-27 02:37:00] [Iter 324/450] R1[173/300], Temp: 0.3807, Energy: -46.789983+0.004342j
[2025-08-27 02:37:13] [Iter 325/450] R1[174/300], Temp: 0.3757, Energy: -46.798176+0.003493j
[2025-08-27 02:37:25] [Iter 326/450] R1[175/300], Temp: 0.3706, Energy: -46.793317+0.001168j
[2025-08-27 02:37:37] [Iter 327/450] R1[176/300], Temp: 0.3655, Energy: -46.777832-0.004893j
[2025-08-27 02:37:50] [Iter 328/450] R1[177/300], Temp: 0.3605, Energy: -46.796326+0.001585j
[2025-08-27 02:38:02] [Iter 329/450] R1[178/300], Temp: 0.3555, Energy: -46.794111+0.001345j
[2025-08-27 02:38:14] [Iter 330/450] R1[179/300], Temp: 0.3505, Energy: -46.786821-0.001994j
[2025-08-27 02:38:27] [Iter 331/450] R1[180/300], Temp: 0.3455, Energy: -46.771446+0.003316j
[2025-08-27 02:38:39] [Iter 332/450] R1[181/300], Temp: 0.3405, Energy: -46.784216+0.003865j
[2025-08-27 02:38:52] [Iter 333/450] R1[182/300], Temp: 0.3356, Energy: -46.790080+0.002555j
[2025-08-27 02:39:04] [Iter 334/450] R1[183/300], Temp: 0.3306, Energy: -46.797412-0.002276j
[2025-08-27 02:39:16] [Iter 335/450] R1[184/300], Temp: 0.3257, Energy: -46.781369+0.000319j
[2025-08-27 02:39:29] [Iter 336/450] R1[185/300], Temp: 0.3208, Energy: -46.783336-0.000331j
[2025-08-27 02:39:41] [Iter 337/450] R1[186/300], Temp: 0.3159, Energy: -46.784275+0.001668j
[2025-08-27 02:39:53] [Iter 338/450] R1[187/300], Temp: 0.3111, Energy: -46.790864-0.001401j
[2025-08-27 02:40:06] [Iter 339/450] R1[188/300], Temp: 0.3062, Energy: -46.784995+0.001472j
[2025-08-27 02:40:18] [Iter 340/450] R1[189/300], Temp: 0.3014, Energy: -46.769939-0.007867j
[2025-08-27 02:40:30] [Iter 341/450] R1[190/300], Temp: 0.2966, Energy: -46.787152-0.003696j
[2025-08-27 02:40:43] [Iter 342/450] R1[191/300], Temp: 0.2919, Energy: -46.789578+0.000853j
[2025-08-27 02:40:55] [Iter 343/450] R1[192/300], Temp: 0.2871, Energy: -46.780947+0.003687j
[2025-08-27 02:41:08] [Iter 344/450] R1[193/300], Temp: 0.2824, Energy: -46.783316-0.001113j
[2025-08-27 02:41:20] [Iter 345/450] R1[194/300], Temp: 0.2777, Energy: -46.773275+0.001414j
[2025-08-27 02:41:32] [Iter 346/450] R1[195/300], Temp: 0.2730, Energy: -46.771231+0.003119j
[2025-08-27 02:41:45] [Iter 347/450] R1[196/300], Temp: 0.2684, Energy: -46.785666+0.000677j
[2025-08-27 02:41:57] [Iter 348/450] R1[197/300], Temp: 0.2637, Energy: -46.790464+0.003008j
[2025-08-27 02:42:09] [Iter 349/450] R1[198/300], Temp: 0.2591, Energy: -46.794966-0.001223j
[2025-08-27 02:42:22] [Iter 350/450] R1[199/300], Temp: 0.2545, Energy: -46.791640+0.001308j
[2025-08-27 02:42:22] ✓ Checkpoint saved: checkpoint_iter_000350.pkl
[2025-08-27 02:42:34] [Iter 351/450] R1[200/300], Temp: 0.2500, Energy: -46.799569+0.002519j
[2025-08-27 02:42:46] [Iter 352/450] R1[201/300], Temp: 0.2455, Energy: -46.783453+0.000194j
[2025-08-27 02:42:59] [Iter 353/450] R1[202/300], Temp: 0.2410, Energy: -46.771667+0.003627j
[2025-08-27 02:43:11] [Iter 354/450] R1[203/300], Temp: 0.2365, Energy: -46.794330+0.001008j
[2025-08-27 02:43:24] [Iter 355/450] R1[204/300], Temp: 0.2321, Energy: -46.792001+0.003134j
[2025-08-27 02:43:36] [Iter 356/450] R1[205/300], Temp: 0.2277, Energy: -46.784150-0.000288j
[2025-08-27 02:43:48] [Iter 357/450] R1[206/300], Temp: 0.2233, Energy: -46.801829+0.001543j
[2025-08-27 02:44:01] [Iter 358/450] R1[207/300], Temp: 0.2190, Energy: -46.785408+0.002165j
[2025-08-27 02:44:13] [Iter 359/450] R1[208/300], Temp: 0.2146, Energy: -46.789715-0.001905j
[2025-08-27 02:44:25] [Iter 360/450] R1[209/300], Temp: 0.2104, Energy: -46.794931-0.000067j
[2025-08-27 02:44:38] [Iter 361/450] R1[210/300], Temp: 0.2061, Energy: -46.796458+0.002194j
[2025-08-27 02:44:50] [Iter 362/450] R1[211/300], Temp: 0.2019, Energy: -46.789340+0.003771j
[2025-08-27 02:45:02] [Iter 363/450] R1[212/300], Temp: 0.1977, Energy: -46.782952+0.002027j
[2025-08-27 02:45:15] [Iter 364/450] R1[213/300], Temp: 0.1935, Energy: -46.794869-0.001282j
[2025-08-27 02:45:27] [Iter 365/450] R1[214/300], Temp: 0.1894, Energy: -46.782038-0.004034j
[2025-08-27 02:45:40] [Iter 366/450] R1[215/300], Temp: 0.1853, Energy: -46.795757+0.003083j
[2025-08-27 02:45:52] [Iter 367/450] R1[216/300], Temp: 0.1813, Energy: -46.787233+0.003054j
[2025-08-27 02:46:04] [Iter 368/450] R1[217/300], Temp: 0.1773, Energy: -46.794004+0.004457j
[2025-08-27 02:46:17] [Iter 369/450] R1[218/300], Temp: 0.1733, Energy: -46.785292+0.002115j
[2025-08-27 02:46:29] [Iter 370/450] R1[219/300], Temp: 0.1693, Energy: -46.778821+0.000643j
[2025-08-27 02:46:41] [Iter 371/450] R1[220/300], Temp: 0.1654, Energy: -46.772747+0.003502j
[2025-08-27 02:46:54] [Iter 372/450] R1[221/300], Temp: 0.1616, Energy: -46.798620+0.001579j
[2025-08-27 02:47:06] [Iter 373/450] R1[222/300], Temp: 0.1577, Energy: -46.778940+0.001048j
[2025-08-27 02:47:18] [Iter 374/450] R1[223/300], Temp: 0.1539, Energy: -46.797170-0.001744j
[2025-08-27 02:47:31] [Iter 375/450] R1[224/300], Temp: 0.1502, Energy: -46.780206+0.001148j
[2025-08-27 02:47:43] [Iter 376/450] R1[225/300], Temp: 0.1464, Energy: -46.786180+0.001442j
[2025-08-27 02:47:56] [Iter 377/450] R1[226/300], Temp: 0.1428, Energy: -46.778056+0.001192j
[2025-08-27 02:48:08] [Iter 378/450] R1[227/300], Temp: 0.1391, Energy: -46.781827-0.002628j
[2025-08-27 02:48:20] [Iter 379/450] R1[228/300], Temp: 0.1355, Energy: -46.784803+0.000572j
[2025-08-27 02:48:33] [Iter 380/450] R1[229/300], Temp: 0.1320, Energy: -46.784264-0.000389j
[2025-08-27 02:48:45] [Iter 381/450] R1[230/300], Temp: 0.1284, Energy: -46.790495-0.000582j
[2025-08-27 02:48:57] [Iter 382/450] R1[231/300], Temp: 0.1249, Energy: -46.787767+0.000488j
[2025-08-27 02:49:10] [Iter 383/450] R1[232/300], Temp: 0.1215, Energy: -46.791863-0.002519j
[2025-08-27 02:49:22] [Iter 384/450] R1[233/300], Temp: 0.1181, Energy: -46.781312+0.000341j
[2025-08-27 02:49:34] [Iter 385/450] R1[234/300], Temp: 0.1147, Energy: -46.795447-0.001621j
[2025-08-27 02:49:47] [Iter 386/450] R1[235/300], Temp: 0.1114, Energy: -46.791377-0.000383j
[2025-08-27 02:49:59] [Iter 387/450] R1[236/300], Temp: 0.1082, Energy: -46.795645-0.003602j
[2025-08-27 02:50:12] [Iter 388/450] R1[237/300], Temp: 0.1049, Energy: -46.794494-0.003517j
[2025-08-27 02:50:24] [Iter 389/450] R1[238/300], Temp: 0.1017, Energy: -46.790121+0.000724j
[2025-08-27 02:50:36] [Iter 390/450] R1[239/300], Temp: 0.0986, Energy: -46.781281+0.000149j
[2025-08-27 02:50:49] [Iter 391/450] R1[240/300], Temp: 0.0955, Energy: -46.778297+0.000615j
[2025-08-27 02:51:01] [Iter 392/450] R1[241/300], Temp: 0.0924, Energy: -46.780119+0.001126j
[2025-08-27 02:51:13] [Iter 393/450] R1[242/300], Temp: 0.0894, Energy: -46.784271+0.003679j
[2025-08-27 02:51:26] [Iter 394/450] R1[243/300], Temp: 0.0865, Energy: -46.789809+0.000644j
[2025-08-27 02:51:38] [Iter 395/450] R1[244/300], Temp: 0.0835, Energy: -46.802437+0.000315j
[2025-08-27 02:51:50] [Iter 396/450] R1[245/300], Temp: 0.0807, Energy: -46.792804-0.001349j
[2025-08-27 02:52:03] [Iter 397/450] R1[246/300], Temp: 0.0778, Energy: -46.785652+0.001764j
[2025-08-27 02:52:15] [Iter 398/450] R1[247/300], Temp: 0.0751, Energy: -46.781022+0.001396j
[2025-08-27 02:52:27] [Iter 399/450] R1[248/300], Temp: 0.0723, Energy: -46.782700+0.000698j
[2025-08-27 02:52:40] [Iter 400/450] R1[249/300], Temp: 0.0696, Energy: -46.780882+0.001140j
[2025-08-27 02:52:40] ✓ Checkpoint saved: checkpoint_iter_000400.pkl
[2025-08-27 02:52:52] [Iter 401/450] R1[250/300], Temp: 0.0670, Energy: -46.788302+0.002489j
[2025-08-27 02:53:05] [Iter 402/450] R1[251/300], Temp: 0.0644, Energy: -46.798446+0.003569j
[2025-08-27 02:53:17] [Iter 403/450] R1[252/300], Temp: 0.0618, Energy: -46.772437+0.003134j
[2025-08-27 02:53:29] [Iter 404/450] R1[253/300], Temp: 0.0593, Energy: -46.790145-0.000532j
[2025-08-27 02:53:42] [Iter 405/450] R1[254/300], Temp: 0.0569, Energy: -46.801167-0.000344j
[2025-08-27 02:53:54] [Iter 406/450] R1[255/300], Temp: 0.0545, Energy: -46.789246+0.002796j
[2025-08-27 02:54:06] [Iter 407/450] R1[256/300], Temp: 0.0521, Energy: -46.785520+0.002108j
[2025-08-27 02:54:19] [Iter 408/450] R1[257/300], Temp: 0.0498, Energy: -46.785145-0.000530j
[2025-08-27 02:54:31] [Iter 409/450] R1[258/300], Temp: 0.0476, Energy: -46.781264-0.003413j
[2025-08-27 02:54:43] [Iter 410/450] R1[259/300], Temp: 0.0454, Energy: -46.788236+0.001622j
[2025-08-27 02:54:56] [Iter 411/450] R1[260/300], Temp: 0.0432, Energy: -46.783801-0.002528j
[2025-08-27 02:55:08] [Iter 412/450] R1[261/300], Temp: 0.0411, Energy: -46.780780+0.001703j
[2025-08-27 02:55:21] [Iter 413/450] R1[262/300], Temp: 0.0391, Energy: -46.797284+0.003839j
[2025-08-27 02:55:33] [Iter 414/450] R1[263/300], Temp: 0.0371, Energy: -46.769307-0.000711j
[2025-08-27 02:55:45] [Iter 415/450] R1[264/300], Temp: 0.0351, Energy: -46.790716-0.000039j
[2025-08-27 02:55:58] [Iter 416/450] R1[265/300], Temp: 0.0332, Energy: -46.781458-0.003115j
[2025-08-27 02:56:10] [Iter 417/450] R1[266/300], Temp: 0.0314, Energy: -46.797662-0.000476j
[2025-08-27 02:56:22] [Iter 418/450] R1[267/300], Temp: 0.0296, Energy: -46.776939-0.001831j
[2025-08-27 02:56:35] [Iter 419/450] R1[268/300], Temp: 0.0278, Energy: -46.790841-0.002993j
[2025-08-27 02:56:47] [Iter 420/450] R1[269/300], Temp: 0.0261, Energy: -46.790554-0.000720j
[2025-08-27 02:56:59] [Iter 421/450] R1[270/300], Temp: 0.0245, Energy: -46.785471+0.000451j
[2025-08-27 02:57:12] [Iter 422/450] R1[271/300], Temp: 0.0229, Energy: -46.795785-0.003454j
[2025-08-27 02:57:24] [Iter 423/450] R1[272/300], Temp: 0.0213, Energy: -46.793775-0.003064j
[2025-08-27 02:57:37] [Iter 424/450] R1[273/300], Temp: 0.0199, Energy: -46.790681-0.000079j
[2025-08-27 02:57:49] [Iter 425/450] R1[274/300], Temp: 0.0184, Energy: -46.787483+0.003145j
[2025-08-27 02:58:01] [Iter 426/450] R1[275/300], Temp: 0.0170, Energy: -46.789665-0.000411j
[2025-08-27 02:58:14] [Iter 427/450] R1[276/300], Temp: 0.0157, Energy: -46.791582-0.000042j
[2025-08-27 02:58:26] [Iter 428/450] R1[277/300], Temp: 0.0144, Energy: -46.776196+0.001414j
[2025-08-27 02:58:38] [Iter 429/450] R1[278/300], Temp: 0.0132, Energy: -46.802601+0.001523j
[2025-08-27 02:58:51] [Iter 430/450] R1[279/300], Temp: 0.0120, Energy: -46.797057+0.002659j
[2025-08-27 02:59:03] [Iter 431/450] R1[280/300], Temp: 0.0109, Energy: -46.781507+0.000628j
[2025-08-27 02:59:15] [Iter 432/450] R1[281/300], Temp: 0.0099, Energy: -46.792819-0.001241j
[2025-08-27 02:59:28] [Iter 433/450] R1[282/300], Temp: 0.0089, Energy: -46.792552-0.002294j
[2025-08-27 02:59:40] [Iter 434/450] R1[283/300], Temp: 0.0079, Energy: -46.781229+0.000957j
[2025-08-27 02:59:52] [Iter 435/450] R1[284/300], Temp: 0.0070, Energy: -46.790025+0.003098j
[2025-08-27 03:00:05] [Iter 436/450] R1[285/300], Temp: 0.0062, Energy: -46.789376-0.002306j
[2025-08-27 03:00:17] [Iter 437/450] R1[286/300], Temp: 0.0054, Energy: -46.787343-0.001480j
[2025-08-27 03:00:30] [Iter 438/450] R1[287/300], Temp: 0.0046, Energy: -46.788028+0.001385j
[2025-08-27 03:00:42] [Iter 439/450] R1[288/300], Temp: 0.0039, Energy: -46.784999+0.000968j
[2025-08-27 03:00:54] [Iter 440/450] R1[289/300], Temp: 0.0033, Energy: -46.782147-0.001908j
[2025-08-27 03:01:07] [Iter 441/450] R1[290/300], Temp: 0.0027, Energy: -46.795779+0.001041j
[2025-08-27 03:01:19] [Iter 442/450] R1[291/300], Temp: 0.0022, Energy: -46.779491-0.000203j
[2025-08-27 03:01:31] [Iter 443/450] R1[292/300], Temp: 0.0018, Energy: -46.783803-0.004007j
[2025-08-27 03:01:44] [Iter 444/450] R1[293/300], Temp: 0.0013, Energy: -46.792491+0.000302j
[2025-08-27 03:01:56] [Iter 445/450] R1[294/300], Temp: 0.0010, Energy: -46.778016-0.000677j
[2025-08-27 03:02:08] [Iter 446/450] R1[295/300], Temp: 0.0007, Energy: -46.782646-0.003061j
[2025-08-27 03:02:21] [Iter 447/450] R1[296/300], Temp: 0.0004, Energy: -46.778142-0.002833j
[2025-08-27 03:02:33] [Iter 448/450] R1[297/300], Temp: 0.0002, Energy: -46.794468-0.002433j
[2025-08-27 03:02:46] [Iter 449/450] R1[298/300], Temp: 0.0001, Energy: -46.789487-0.006040j
[2025-08-27 03:02:58] [Iter 450/450] R1[299/300], Temp: 0.0000, Energy: -46.783297-0.001752j
[2025-08-27 03:02:58] ✓ Checkpoint saved: checkpoint_iter_000450.pkl
[2025-08-27 03:02:58] ✅ Training completed | Restarts: 1
[2025-08-27 03:02:58] ============================================================
[2025-08-27 03:02:58] Training completed | Runtime: 5609.8s
[2025-08-27 03:03:02] ✓ Final state saved: checkpoints/final_GCNN.pkl
[2025-08-27 03:03:02] ============================================================
