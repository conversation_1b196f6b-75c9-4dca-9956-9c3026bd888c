[2025-08-25 18:15:27] ==================================================
[2025-08-25 18:15:27] GCNN for Shastry-Sutherland Model
[2025-08-25 18:15:27] ==================================================
[2025-08-25 18:15:27] System parameters:
[2025-08-25 18:15:27]   - System size: L=5, N=100
[2025-08-25 18:15:27]   - System parameters: J1=0.8, J2=1.0, Q=0.0
[2025-08-25 18:15:27] --------------------------------------------------
[2025-08-25 18:15:27] Model parameters:
[2025-08-25 18:15:27]   - Number of layers = 4
[2025-08-25 18:15:27]   - Number of features = 4
[2025-08-25 18:15:27]   - Total parameters = 19628
[2025-08-25 18:15:27] --------------------------------------------------
[2025-08-25 18:15:27] Training parameters:
[2025-08-25 18:15:27]   - Learning rate: 0.015
[2025-08-25 18:15:27]   - Total iterations: 2250
[2025-08-25 18:15:27]   - Annealing cycles: 4
[2025-08-25 18:15:27]   - Initial period: 150
[2025-08-25 18:15:27]   - Period multiplier: 2.0
[2025-08-25 18:15:27]   - Temperature range: 0.0-1.0
[2025-08-25 18:15:27]   - Samples: 16384
[2025-08-25 18:15:27]   - Discarded samples: 0
[2025-08-25 18:15:27]   - Chunk size: 2048
[2025-08-25 18:15:27]   - Diagonal shift: 0.2
[2025-08-25 18:15:27]   - Gradient clipping: 1.0
[2025-08-25 18:15:27]   - Checkpoint enabled: interval=250
[2025-08-25 18:15:27]   - Checkpoint directory: results/L=5/J2=1.00/J1=0.80/training/checkpoints
[2025-08-25 18:15:27] --------------------------------------------------
[2025-08-25 18:15:27] Device status:
[2025-08-25 18:15:27]   - Devices model: NVIDIA H200 NVL
[2025-08-25 18:15:27]   - Number of devices: 1
[2025-08-25 18:15:27]   - Sharding: True
[2025-08-25 18:15:27] ============================================================
[2025-08-25 18:16:21] [Iter 1/2250] R0[0/150], Temp: 1.0000, Energy: 52.495455-0.000119j
[2025-08-25 18:16:47] [Iter 2/2250] R0[1/150], Temp: 0.9999, Energy: 52.495146-0.001753j
[2025-08-25 18:17:13] [Iter 3/2250] R0[2/150], Temp: 0.9996, Energy: 52.494428+0.001941j
[2025-08-25 18:17:39] [Iter 4/2250] R0[3/150], Temp: 0.9990, Energy: 52.492682-0.000645j
[2025-08-25 18:18:05] [Iter 5/2250] R0[4/150], Temp: 0.9982, Energy: 52.491616+0.000557j
[2025-08-25 18:18:31] [Iter 6/2250] R0[5/150], Temp: 0.9973, Energy: 52.490349-0.001119j
[2025-08-25 18:18:57] [Iter 7/2250] R0[6/150], Temp: 0.9961, Energy: 52.489742+0.001332j
[2025-08-25 18:19:22] [Iter 8/2250] R0[7/150], Temp: 0.9946, Energy: 52.487628+0.000515j
[2025-08-25 18:19:48] [Iter 9/2250] R0[8/150], Temp: 0.9930, Energy: 52.479457-0.001361j
[2025-08-25 18:20:14] [Iter 10/2250] R0[9/150], Temp: 0.9911, Energy: 52.473423-0.002268j
[2025-08-25 18:20:40] [Iter 11/2250] R0[10/150], Temp: 0.9891, Energy: 52.470308-0.002064j
[2025-08-25 18:21:06] [Iter 12/2250] R0[11/150], Temp: 0.9868, Energy: 52.457612-0.004743j
[2025-08-25 18:21:32] [Iter 13/2250] R0[12/150], Temp: 0.9843, Energy: 52.451644-0.004495j
[2025-08-25 18:21:58] [Iter 14/2250] R0[13/150], Temp: 0.9816, Energy: 52.430852+0.002853j
[2025-08-25 18:22:24] [Iter 15/2250] R0[14/150], Temp: 0.9787, Energy: 52.398448+0.004689j
[2025-08-25 18:22:50] [Iter 16/2250] R0[15/150], Temp: 0.9755, Energy: 52.366804+0.000222j
[2025-08-25 18:23:16] [Iter 17/2250] R0[16/150], Temp: 0.9722, Energy: 52.319323-0.001267j
[2025-08-25 18:23:42] [Iter 18/2250] R0[17/150], Temp: 0.9686, Energy: 52.243655+0.003086j
[2025-08-25 18:24:08] [Iter 19/2250] R0[18/150], Temp: 0.9649, Energy: 52.134319+0.001110j
[2025-08-25 18:24:34] [Iter 20/2250] R0[19/150], Temp: 0.9609, Energy: 51.966356-0.035767j
[2025-08-25 18:25:00] [Iter 21/2250] R0[20/150], Temp: 0.9568, Energy: 51.702874-0.013646j
[2025-08-25 18:25:26] [Iter 22/2250] R0[21/150], Temp: 0.9524, Energy: 51.325257+0.029351j
[2025-08-25 18:25:52] [Iter 23/2250] R0[22/150], Temp: 0.9479, Energy: 50.796343-0.015376j
[2025-08-25 18:26:18] [Iter 24/2250] R0[23/150], Temp: 0.9431, Energy: 49.967186-0.019784j
[2025-08-25 18:26:44] [Iter 25/2250] R0[24/150], Temp: 0.9382, Energy: 48.573350+0.006333j
[2025-08-25 18:27:10] [Iter 26/2250] R0[25/150], Temp: 0.9330, Energy: 46.498422+0.000599j
[2025-08-25 18:27:36] [Iter 27/2250] R0[26/150], Temp: 0.9277, Energy: 43.440231+0.007857j
[2025-08-25 18:28:02] [Iter 28/2250] R0[27/150], Temp: 0.9222, Energy: 39.178089-0.029470j
[2025-08-25 18:28:28] [Iter 29/2250] R0[28/150], Temp: 0.9165, Energy: 33.904627-0.002125j
[2025-08-25 18:28:53] [Iter 30/2250] R0[29/150], Temp: 0.9106, Energy: 28.085392+0.044597j
[2025-08-25 18:29:19] [Iter 31/2250] R0[30/150], Temp: 0.9045, Energy: 22.163019-0.135535j
[2025-08-25 18:29:45] [Iter 32/2250] R0[31/150], Temp: 0.8983, Energy: 16.263963+0.035318j
[2025-08-25 18:30:11] [Iter 33/2250] R0[32/150], Temp: 0.8918, Energy: 10.764447-0.162200j
[2025-08-25 18:30:37] [Iter 34/2250] R0[33/150], Temp: 0.8853, Energy: 5.621077-0.047111j
[2025-08-25 18:31:03] [Iter 35/2250] R0[34/150], Temp: 0.8785, Energy: 0.982922+0.055964j
[2025-08-25 18:31:29] [Iter 36/2250] R0[35/150], Temp: 0.8716, Energy: -3.217496-0.006568j
[2025-08-25 18:31:55] [Iter 37/2250] R0[36/150], Temp: 0.8645, Energy: -6.627710+0.045247j
[2025-08-25 18:32:21] [Iter 38/2250] R0[37/150], Temp: 0.8572, Energy: -9.578032-0.004335j
[2025-08-25 18:32:47] [Iter 39/2250] R0[38/150], Temp: 0.8498, Energy: -12.235926-0.019669j
[2025-08-25 18:33:13] [Iter 40/2250] R0[39/150], Temp: 0.8423, Energy: -14.383384+0.022256j
[2025-08-25 18:33:39] [Iter 41/2250] R0[40/150], Temp: 0.8346, Energy: -16.267831-0.035333j
[2025-08-25 18:34:05] [Iter 42/2250] R0[41/150], Temp: 0.8267, Energy: -17.979951+0.002562j
[2025-08-25 18:34:31] [Iter 43/2250] R0[42/150], Temp: 0.8187, Energy: -19.524554-0.053252j
[2025-08-25 18:34:56] [Iter 44/2250] R0[43/150], Temp: 0.8106, Energy: -20.836252-0.015416j
[2025-08-25 18:35:22] [Iter 45/2250] R0[44/150], Temp: 0.8023, Energy: -22.032066-0.034180j
[2025-08-25 18:35:48] [Iter 46/2250] R0[45/150], Temp: 0.7939, Energy: -23.138734-0.013522j
[2025-08-25 18:36:14] [Iter 47/2250] R0[46/150], Temp: 0.7854, Energy: -24.207488-0.028941j
[2025-08-25 18:36:40] [Iter 48/2250] R0[47/150], Temp: 0.7767, Energy: -25.070497-0.030836j
[2025-08-25 18:37:06] [Iter 49/2250] R0[48/150], Temp: 0.7679, Energy: -25.905732+0.001162j
[2025-08-25 18:37:32] [Iter 50/2250] R0[49/150], Temp: 0.7590, Energy: -26.686562+0.006405j
[2025-08-25 18:37:58] [Iter 51/2250] R0[50/150], Temp: 0.7500, Energy: -27.384830-0.028799j
[2025-08-25 18:38:24] [Iter 52/2250] R0[51/150], Temp: 0.7409, Energy: -28.011559+0.042423j
[2025-08-25 18:38:50] [Iter 53/2250] R0[52/150], Temp: 0.7316, Energy: -28.603089+0.035876j
[2025-08-25 18:39:16] [Iter 54/2250] R0[53/150], Temp: 0.7223, Energy: -29.161538+0.018928j
[2025-08-25 18:39:42] [Iter 55/2250] R0[54/150], Temp: 0.7129, Energy: -29.708708-0.010697j
[2025-08-25 18:40:07] [Iter 56/2250] R0[55/150], Temp: 0.7034, Energy: -30.137475-0.008570j
[2025-08-25 18:40:33] [Iter 57/2250] R0[56/150], Temp: 0.6938, Energy: -30.565258-0.031766j
[2025-08-25 18:40:59] [Iter 58/2250] R0[57/150], Temp: 0.6841, Energy: -31.002688-0.006326j
[2025-08-25 18:41:25] [Iter 59/2250] R0[58/150], Temp: 0.6743, Energy: -31.287312-0.009501j
[2025-08-25 18:41:51] [Iter 60/2250] R0[59/150], Temp: 0.6644, Energy: -31.672543+0.008269j
[2025-08-25 18:42:17] [Iter 61/2250] R0[60/150], Temp: 0.6545, Energy: -32.041236+0.003930j
[2025-08-25 18:42:43] [Iter 62/2250] R0[61/150], Temp: 0.6445, Energy: -32.355984+0.021740j
[2025-08-25 18:43:09] [Iter 63/2250] R0[62/150], Temp: 0.6345, Energy: -32.654512+0.010371j
[2025-08-25 18:43:35] [Iter 64/2250] R0[63/150], Temp: 0.6243, Energy: -32.877603+0.000211j
[2025-08-25 18:44:01] [Iter 65/2250] R0[64/150], Temp: 0.6142, Energy: -33.134284-0.012462j
[2025-08-25 18:44:27] [Iter 66/2250] R0[65/150], Temp: 0.6040, Energy: -33.423922+0.029396j
[2025-08-25 18:44:53] [Iter 67/2250] R0[66/150], Temp: 0.5937, Energy: -33.606483-0.014836j
[2025-08-25 18:45:19] [Iter 68/2250] R0[67/150], Temp: 0.5834, Energy: -33.817248-0.005526j
[2025-08-25 18:45:45] [Iter 69/2250] R0[68/150], Temp: 0.5730, Energy: -34.024240-0.020698j
[2025-08-25 18:46:11] [Iter 70/2250] R0[69/150], Temp: 0.5627, Energy: -34.241171-0.045433j
[2025-08-25 18:46:37] [Iter 71/2250] R0[70/150], Temp: 0.5523, Energy: -34.419850+0.000769j
[2025-08-25 18:47:03] [Iter 72/2250] R0[71/150], Temp: 0.5418, Energy: -34.591702+0.010432j
[2025-08-25 18:47:29] [Iter 73/2250] R0[72/150], Temp: 0.5314, Energy: -34.737881-0.006568j
[2025-08-25 18:47:54] [Iter 74/2250] R0[73/150], Temp: 0.5209, Energy: -34.900254-0.008859j
[2025-08-25 18:48:20] [Iter 75/2250] R0[74/150], Temp: 0.5105, Energy: -35.039994+0.029508j
[2025-08-25 18:48:46] [Iter 76/2250] R0[75/150], Temp: 0.5000, Energy: -35.198406-0.019754j
[2025-08-25 18:49:12] [Iter 77/2250] R0[76/150], Temp: 0.4895, Energy: -35.316738-0.023190j
[2025-08-25 18:49:38] [Iter 78/2250] R0[77/150], Temp: 0.4791, Energy: -35.427306+0.013453j
[2025-08-25 18:50:04] [Iter 79/2250] R0[78/150], Temp: 0.4686, Energy: -35.619679+0.027781j
[2025-08-25 18:50:30] [Iter 80/2250] R0[79/150], Temp: 0.4582, Energy: -35.717649-0.026812j
[2025-08-25 18:50:56] [Iter 81/2250] R0[80/150], Temp: 0.4477, Energy: -35.863332-0.033813j
[2025-08-25 18:51:22] [Iter 82/2250] R0[81/150], Temp: 0.4373, Energy: -35.945850-0.003737j
[2025-08-25 18:51:48] [Iter 83/2250] R0[82/150], Temp: 0.4270, Energy: -36.045855+0.012122j
[2025-08-25 18:52:14] [Iter 84/2250] R0[83/150], Temp: 0.4166, Energy: -36.191229+0.016212j
[2025-08-25 18:52:40] [Iter 85/2250] R0[84/150], Temp: 0.4063, Energy: -36.269612-0.016769j
[2025-08-25 18:53:05] [Iter 86/2250] R0[85/150], Temp: 0.3960, Energy: -36.385147-0.026963j
[2025-08-25 18:53:31] [Iter 87/2250] R0[86/150], Temp: 0.3858, Energy: -36.453241-0.001752j
[2025-08-25 18:53:57] [Iter 88/2250] R0[87/150], Temp: 0.3757, Energy: -36.570087-0.023987j
[2025-08-25 18:54:23] [Iter 89/2250] R0[88/150], Temp: 0.3655, Energy: -36.652406-0.018649j
[2025-08-25 18:54:49] [Iter 90/2250] R0[89/150], Temp: 0.3555, Energy: -36.736850+0.004430j
[2025-08-25 18:55:15] [Iter 91/2250] R0[90/150], Temp: 0.3455, Energy: -36.844233-0.023312j
[2025-08-25 18:55:41] [Iter 92/2250] R0[91/150], Temp: 0.3356, Energy: -36.929717+0.007656j
[2025-08-25 18:56:07] [Iter 93/2250] R0[92/150], Temp: 0.3257, Energy: -36.983521+0.034659j
[2025-08-25 18:56:33] [Iter 94/2250] R0[93/150], Temp: 0.3159, Energy: -37.066941-0.000969j
[2025-08-25 18:56:59] [Iter 95/2250] R0[94/150], Temp: 0.3062, Energy: -37.129086+0.004576j
[2025-08-25 18:57:25] [Iter 96/2250] R0[95/150], Temp: 0.2966, Energy: -37.235667-0.010391j
[2025-08-25 18:57:51] [Iter 97/2250] R0[96/150], Temp: 0.2871, Energy: -37.315607+0.018935j
[2025-08-25 18:58:17] [Iter 98/2250] R0[97/150], Temp: 0.2777, Energy: -37.398060+0.036312j
[2025-08-25 18:58:42] [Iter 99/2250] R0[98/150], Temp: 0.2684, Energy: -37.445448+0.024995j
[2025-08-25 18:59:08] [Iter 100/2250] R0[99/150], Temp: 0.2591, Energy: -37.533215+0.013628j
[2025-08-25 18:59:34] [Iter 101/2250] R0[100/150], Temp: 0.2500, Energy: -37.596488-0.020410j
[2025-08-25 19:00:00] [Iter 102/2250] R0[101/150], Temp: 0.2410, Energy: -37.660697-0.017585j
[2025-08-25 19:00:26] [Iter 103/2250] R0[102/150], Temp: 0.2321, Energy: -37.752224-0.004051j
[2025-08-25 19:00:52] [Iter 104/2250] R0[103/150], Temp: 0.2233, Energy: -37.828921+0.025164j
[2025-08-25 19:01:18] [Iter 105/2250] R0[104/150], Temp: 0.2146, Energy: -37.915622-0.007619j
[2025-08-25 19:01:44] [Iter 106/2250] R0[105/150], Temp: 0.2061, Energy: -37.965140-0.010939j
[2025-08-25 19:02:10] [Iter 107/2250] R0[106/150], Temp: 0.1977, Energy: -38.019189-0.000607j
[2025-08-25 19:02:36] [Iter 108/2250] R0[107/150], Temp: 0.1894, Energy: -38.049769-0.011464j
[2025-08-25 19:03:01] [Iter 109/2250] R0[108/150], Temp: 0.1813, Energy: -38.194456+0.030820j
[2025-08-25 19:03:27] [Iter 110/2250] R0[109/150], Temp: 0.1733, Energy: -38.214250-0.003960j
[2025-08-25 19:03:53] [Iter 111/2250] R0[110/150], Temp: 0.1654, Energy: -38.298288-0.007637j
[2025-08-25 19:04:19] [Iter 112/2250] R0[111/150], Temp: 0.1577, Energy: -38.341373-0.018949j
[2025-08-25 19:04:45] [Iter 113/2250] R0[112/150], Temp: 0.1502, Energy: -38.396458-0.001657j
[2025-08-25 19:05:11] [Iter 114/2250] R0[113/150], Temp: 0.1428, Energy: -38.479489-0.015540j
[2025-08-25 19:05:37] [Iter 115/2250] R0[114/150], Temp: 0.1355, Energy: -38.526691-0.004143j
[2025-08-25 19:06:03] [Iter 116/2250] R0[115/150], Temp: 0.1284, Energy: -38.590138-0.003539j
[2025-08-25 19:06:29] [Iter 117/2250] R0[116/150], Temp: 0.1215, Energy: -38.701741-0.011441j
[2025-08-25 19:06:55] [Iter 118/2250] R0[117/150], Temp: 0.1147, Energy: -38.732802+0.032749j
[2025-08-25 19:07:21] [Iter 119/2250] R0[118/150], Temp: 0.1082, Energy: -38.762760+0.006690j
[2025-08-25 19:07:47] [Iter 120/2250] R0[119/150], Temp: 0.1017, Energy: -38.862163-0.004026j
[2025-08-25 19:08:12] [Iter 121/2250] R0[120/150], Temp: 0.0955, Energy: -38.905727+0.003304j
[2025-08-25 19:08:38] [Iter 122/2250] R0[121/150], Temp: 0.0894, Energy: -38.957875-0.002448j
[2025-08-25 19:09:04] [Iter 123/2250] R0[122/150], Temp: 0.0835, Energy: -39.029180+0.003385j
[2025-08-25 19:09:30] [Iter 124/2250] R0[123/150], Temp: 0.0778, Energy: -39.076136+0.014750j
[2025-08-25 19:09:56] [Iter 125/2250] R0[124/150], Temp: 0.0723, Energy: -39.134185+0.009498j
[2025-08-25 19:10:22] [Iter 126/2250] R0[125/150], Temp: 0.0670, Energy: -39.202754-0.007907j
[2025-08-25 19:10:48] [Iter 127/2250] R0[126/150], Temp: 0.0618, Energy: -39.261131-0.000734j
[2025-08-25 19:11:14] [Iter 128/2250] R0[127/150], Temp: 0.0569, Energy: -39.328540-0.012516j
[2025-08-25 19:11:40] [Iter 129/2250] R0[128/150], Temp: 0.0521, Energy: -39.356275-0.014353j
[2025-08-25 19:12:06] [Iter 130/2250] R0[129/150], Temp: 0.0476, Energy: -39.451355-0.036758j
[2025-08-25 19:12:32] [Iter 131/2250] R0[130/150], Temp: 0.0432, Energy: -39.503496+0.004280j
[2025-08-25 19:12:57] [Iter 132/2250] R0[131/150], Temp: 0.0391, Energy: -39.562319-0.018614j
[2025-08-25 19:13:23] [Iter 133/2250] R0[132/150], Temp: 0.0351, Energy: -39.658401+0.003909j
[2025-08-25 19:13:49] [Iter 134/2250] R0[133/150], Temp: 0.0314, Energy: -39.679395+0.015343j
[2025-08-25 19:14:15] [Iter 135/2250] R0[134/150], Temp: 0.0278, Energy: -39.769951+0.010877j
[2025-08-25 19:14:41] [Iter 136/2250] R0[135/150], Temp: 0.0245, Energy: -39.834981-0.026282j
[2025-08-25 19:15:07] [Iter 137/2250] R0[136/150], Temp: 0.0213, Energy: -39.898303+0.003658j
[2025-08-25 19:15:33] [Iter 138/2250] R0[137/150], Temp: 0.0184, Energy: -39.945202+0.007787j
[2025-08-25 19:15:59] [Iter 139/2250] R0[138/150], Temp: 0.0157, Energy: -40.039207+0.002914j
[2025-08-25 19:16:25] [Iter 140/2250] R0[139/150], Temp: 0.0132, Energy: -40.120470-0.016884j
[2025-08-25 19:16:51] [Iter 141/2250] R0[140/150], Temp: 0.0109, Energy: -40.178097-0.036457j
[2025-08-25 19:17:17] [Iter 142/2250] R0[141/150], Temp: 0.0089, Energy: -40.268778-0.002545j
[2025-08-25 19:17:42] [Iter 143/2250] R0[142/150], Temp: 0.0070, Energy: -40.348688+0.016575j
[2025-08-25 19:18:08] [Iter 144/2250] R0[143/150], Temp: 0.0054, Energy: -40.409617+0.008478j
[2025-08-25 19:18:34] [Iter 145/2250] R0[144/150], Temp: 0.0039, Energy: -40.513849+0.007558j
[2025-08-25 19:19:00] [Iter 146/2250] R0[145/150], Temp: 0.0027, Energy: -40.593472+0.011090j
[2025-08-25 19:19:26] [Iter 147/2250] R0[146/150], Temp: 0.0018, Energy: -40.705628-0.000773j
[2025-08-25 19:19:52] [Iter 148/2250] R0[147/150], Temp: 0.0010, Energy: -40.771524+0.012819j
[2025-08-25 19:20:18] [Iter 149/2250] R0[148/150], Temp: 0.0004, Energy: -40.866630-0.010348j
[2025-08-25 19:20:44] [Iter 150/2250] R0[149/150], Temp: 0.0001, Energy: -40.957417+0.006582j
[2025-08-25 19:20:44] RESTART #1 | Period: 300
[2025-08-25 19:21:10] [Iter 151/2250] R1[0/300], Temp: 1.0000, Energy: -41.040682+0.011047j
[2025-08-25 19:21:36] [Iter 152/2250] R1[1/300], Temp: 1.0000, Energy: -41.131679+0.006515j
[2025-08-25 19:22:02] [Iter 153/2250] R1[2/300], Temp: 0.9999, Energy: -41.214455-0.001397j
[2025-08-25 19:22:28] [Iter 154/2250] R1[3/300], Temp: 0.9998, Energy: -41.319651-0.024947j
[2025-08-25 19:22:53] [Iter 155/2250] R1[4/300], Temp: 0.9996, Energy: -41.413651+0.010581j
[2025-08-25 19:23:19] [Iter 156/2250] R1[5/300], Temp: 0.9993, Energy: -41.474047-0.002976j
[2025-08-25 19:23:45] [Iter 157/2250] R1[6/300], Temp: 0.9990, Energy: -41.587432+0.007847j
[2025-08-25 19:24:11] [Iter 158/2250] R1[7/300], Temp: 0.9987, Energy: -41.674927-0.018406j
[2025-08-25 19:24:37] [Iter 159/2250] R1[8/300], Temp: 0.9982, Energy: -41.755119-0.029966j
[2025-08-25 19:25:03] [Iter 160/2250] R1[9/300], Temp: 0.9978, Energy: -41.854723+0.007586j
[2025-08-25 19:25:29] [Iter 161/2250] R1[10/300], Temp: 0.9973, Energy: -41.922449-0.000737j
[2025-08-25 19:25:55] [Iter 162/2250] R1[11/300], Temp: 0.9967, Energy: -42.017684-0.009963j
[2025-08-25 19:26:21] [Iter 163/2250] R1[12/300], Temp: 0.9961, Energy: -42.092551+0.023532j
[2025-08-25 19:26:47] [Iter 164/2250] R1[13/300], Temp: 0.9954, Energy: -42.174753+0.008743j
[2025-08-25 19:27:13] [Iter 165/2250] R1[14/300], Temp: 0.9946, Energy: -42.220375-0.010135j
[2025-08-25 19:27:39] [Iter 166/2250] R1[15/300], Temp: 0.9938, Energy: -42.299413+0.009949j
[2025-08-25 19:28:05] [Iter 167/2250] R1[16/300], Temp: 0.9930, Energy: -42.389249-0.023563j
[2025-08-25 19:28:31] [Iter 168/2250] R1[17/300], Temp: 0.9921, Energy: -42.452477-0.010921j
[2025-08-25 19:28:57] [Iter 169/2250] R1[18/300], Temp: 0.9911, Energy: -42.511088+0.005183j
[2025-08-25 19:29:23] [Iter 170/2250] R1[19/300], Temp: 0.9901, Energy: -42.558813+0.003080j
[2025-08-25 19:29:49] [Iter 171/2250] R1[20/300], Temp: 0.9891, Energy: -42.572613-0.020147j
[2025-08-25 19:30:15] [Iter 172/2250] R1[21/300], Temp: 0.9880, Energy: -42.670497-0.000487j
[2025-08-25 19:30:41] [Iter 173/2250] R1[22/300], Temp: 0.9868, Energy: -42.686562+0.010590j
[2025-08-25 19:31:06] [Iter 174/2250] R1[23/300], Temp: 0.9856, Energy: -42.765824+0.015214j
[2025-08-25 19:31:32] [Iter 175/2250] R1[24/300], Temp: 0.9843, Energy: -42.792857-0.005756j
[2025-08-25 19:31:58] [Iter 176/2250] R1[25/300], Temp: 0.9830, Energy: -42.837779-0.006913j
[2025-08-25 19:32:24] [Iter 177/2250] R1[26/300], Temp: 0.9816, Energy: -42.862902-0.006916j
[2025-08-25 19:32:50] [Iter 178/2250] R1[27/300], Temp: 0.9801, Energy: -42.935456+0.010933j
[2025-08-25 19:33:16] [Iter 179/2250] R1[28/300], Temp: 0.9787, Energy: -42.962269-0.015232j
[2025-08-25 19:33:42] [Iter 180/2250] R1[29/300], Temp: 0.9771, Energy: -42.996748-0.002658j
[2025-08-25 19:34:08] [Iter 181/2250] R1[30/300], Temp: 0.9755, Energy: -43.017689-0.005401j
[2025-08-25 19:34:34] [Iter 182/2250] R1[31/300], Temp: 0.9739, Energy: -43.036802-0.009015j
[2025-08-25 19:35:00] [Iter 183/2250] R1[32/300], Temp: 0.9722, Energy: -43.099868+0.008363j
[2025-08-25 19:35:26] [Iter 184/2250] R1[33/300], Temp: 0.9704, Energy: -43.120561+0.002632j
[2025-08-25 19:35:51] [Iter 185/2250] R1[34/300], Temp: 0.9686, Energy: -43.123220+0.000947j
[2025-08-25 19:36:17] [Iter 186/2250] R1[35/300], Temp: 0.9668, Energy: -43.160380-0.006561j
[2025-08-25 19:36:43] [Iter 187/2250] R1[36/300], Temp: 0.9649, Energy: -43.191440+0.000712j
[2025-08-25 19:37:09] [Iter 188/2250] R1[37/300], Temp: 0.9629, Energy: -43.212030-0.004176j
[2025-08-25 19:37:35] [Iter 189/2250] R1[38/300], Temp: 0.9609, Energy: -43.218625-0.010763j
[2025-08-25 19:38:01] [Iter 190/2250] R1[39/300], Temp: 0.9589, Energy: -43.244639-0.002386j
[2025-08-25 19:38:27] [Iter 191/2250] R1[40/300], Temp: 0.9568, Energy: -43.273870+0.002854j
[2025-08-25 19:38:53] [Iter 192/2250] R1[41/300], Temp: 0.9546, Energy: -43.279872-0.002518j
[2025-08-25 19:39:19] [Iter 193/2250] R1[42/300], Temp: 0.9524, Energy: -43.309575-0.006069j
[2025-08-25 19:39:45] [Iter 194/2250] R1[43/300], Temp: 0.9502, Energy: -43.338266-0.002859j
[2025-08-25 19:40:11] [Iter 195/2250] R1[44/300], Temp: 0.9479, Energy: -43.356089-0.001344j
[2025-08-25 19:40:37] [Iter 196/2250] R1[45/300], Temp: 0.9455, Energy: -43.370866+0.004274j
[2025-08-25 19:41:03] [Iter 197/2250] R1[46/300], Temp: 0.9431, Energy: -43.373485+0.003511j
[2025-08-25 19:41:29] [Iter 198/2250] R1[47/300], Temp: 0.9407, Energy: -43.409868-0.002704j
[2025-08-25 19:41:54] [Iter 199/2250] R1[48/300], Temp: 0.9382, Energy: -43.418522+0.008916j
[2025-08-25 19:42:20] [Iter 200/2250] R1[49/300], Temp: 0.9356, Energy: -43.436170+0.001233j
[2025-08-25 19:42:46] [Iter 201/2250] R1[50/300], Temp: 0.9330, Energy: -43.454167+0.003318j
[2025-08-25 19:43:12] [Iter 202/2250] R1[51/300], Temp: 0.9304, Energy: -43.464639+0.005313j
[2025-08-25 19:43:38] [Iter 203/2250] R1[52/300], Temp: 0.9277, Energy: -43.467270+0.008390j
[2025-08-25 19:44:04] [Iter 204/2250] R1[53/300], Temp: 0.9249, Energy: -43.482105-0.000704j
[2025-08-25 19:44:30] [Iter 205/2250] R1[54/300], Temp: 0.9222, Energy: -43.486048+0.001263j
[2025-08-25 19:44:56] [Iter 206/2250] R1[55/300], Temp: 0.9193, Energy: -43.487124+0.002124j
[2025-08-25 19:45:22] [Iter 207/2250] R1[56/300], Temp: 0.9165, Energy: -43.532895-0.005935j
[2025-08-25 19:45:48] [Iter 208/2250] R1[57/300], Temp: 0.9135, Energy: -43.531254-0.003111j
[2025-08-25 19:46:14] [Iter 209/2250] R1[58/300], Temp: 0.9106, Energy: -43.544868+0.002753j
[2025-08-25 19:46:40] [Iter 210/2250] R1[59/300], Temp: 0.9076, Energy: -43.546598-0.003358j
[2025-08-25 19:47:06] [Iter 211/2250] R1[60/300], Temp: 0.9045, Energy: -43.577175-0.004152j
[2025-08-25 19:47:32] [Iter 212/2250] R1[61/300], Temp: 0.9014, Energy: -43.568834-0.011072j
[2025-08-25 19:47:58] [Iter 213/2250] R1[62/300], Temp: 0.8983, Energy: -43.604138-0.002292j
[2025-08-25 19:48:24] [Iter 214/2250] R1[63/300], Temp: 0.8951, Energy: -43.617890+0.006431j
[2025-08-25 19:48:50] [Iter 215/2250] R1[64/300], Temp: 0.8918, Energy: -43.606714+0.005414j
[2025-08-25 19:49:16] [Iter 216/2250] R1[65/300], Temp: 0.8886, Energy: -43.613626+0.007768j
[2025-08-25 19:49:42] [Iter 217/2250] R1[66/300], Temp: 0.8853, Energy: -43.612020+0.009573j
[2025-08-25 19:50:08] [Iter 218/2250] R1[67/300], Temp: 0.8819, Energy: -43.633875-0.006621j
[2025-08-25 19:50:33] [Iter 219/2250] R1[68/300], Temp: 0.8785, Energy: -43.636184-0.001758j
[2025-08-25 19:50:59] [Iter 220/2250] R1[69/300], Temp: 0.8751, Energy: -43.640865+0.001565j
[2025-08-25 19:51:25] [Iter 221/2250] R1[70/300], Temp: 0.8716, Energy: -43.670799-0.001019j
[2025-08-25 19:51:51] [Iter 222/2250] R1[71/300], Temp: 0.8680, Energy: -43.663369+0.004351j
[2025-08-25 19:52:17] [Iter 223/2250] R1[72/300], Temp: 0.8645, Energy: -43.668580+0.003631j
[2025-08-25 19:52:43] [Iter 224/2250] R1[73/300], Temp: 0.8609, Energy: -43.688015+0.008162j
[2025-08-25 19:53:09] [Iter 225/2250] R1[74/300], Temp: 0.8572, Energy: -43.700524+0.003774j
[2025-08-25 19:53:35] [Iter 226/2250] R1[75/300], Temp: 0.8536, Energy: -43.705700+0.004427j
[2025-08-25 19:54:01] [Iter 227/2250] R1[76/300], Temp: 0.8498, Energy: -43.695272+0.000297j
[2025-08-25 19:54:27] [Iter 228/2250] R1[77/300], Temp: 0.8461, Energy: -43.708340+0.004567j
[2025-08-25 19:54:53] [Iter 229/2250] R1[78/300], Temp: 0.8423, Energy: -43.692792-0.000548j
[2025-08-25 19:55:19] [Iter 230/2250] R1[79/300], Temp: 0.8384, Energy: -43.722126+0.001487j
[2025-08-25 19:55:44] [Iter 231/2250] R1[80/300], Temp: 0.8346, Energy: -43.720801+0.002139j
[2025-08-25 19:56:10] [Iter 232/2250] R1[81/300], Temp: 0.8307, Energy: -43.716529+0.005127j
[2025-08-25 19:56:36] [Iter 233/2250] R1[82/300], Temp: 0.8267, Energy: -43.715351+0.005529j
[2025-08-25 19:57:02] [Iter 234/2250] R1[83/300], Temp: 0.8227, Energy: -43.740918-0.001397j
[2025-08-25 19:57:28] [Iter 235/2250] R1[84/300], Temp: 0.8187, Energy: -43.748273+0.010580j
[2025-08-25 19:57:54] [Iter 236/2250] R1[85/300], Temp: 0.8147, Energy: -43.765187-0.006938j
[2025-08-25 19:58:20] [Iter 237/2250] R1[86/300], Temp: 0.8106, Energy: -43.759348-0.005036j
[2025-08-25 19:58:46] [Iter 238/2250] R1[87/300], Temp: 0.8065, Energy: -43.772741+0.005089j
[2025-08-25 19:59:12] [Iter 239/2250] R1[88/300], Temp: 0.8023, Energy: -43.780853-0.007957j
[2025-08-25 19:59:38] [Iter 240/2250] R1[89/300], Temp: 0.7981, Energy: -43.792999+0.006210j
[2025-08-25 20:00:04] [Iter 241/2250] R1[90/300], Temp: 0.7939, Energy: -43.785154-0.003658j
[2025-08-25 20:00:29] [Iter 242/2250] R1[91/300], Temp: 0.7896, Energy: -43.776536-0.002554j
[2025-08-25 20:00:55] [Iter 243/2250] R1[92/300], Temp: 0.7854, Energy: -43.784125-0.008610j
[2025-08-25 20:01:21] [Iter 244/2250] R1[93/300], Temp: 0.7810, Energy: -43.790893-0.009230j
[2025-08-25 20:01:47] [Iter 245/2250] R1[94/300], Temp: 0.7767, Energy: -43.806247-0.000580j
[2025-08-25 20:02:13] [Iter 246/2250] R1[95/300], Temp: 0.7723, Energy: -43.801824+0.012445j
[2025-08-25 20:02:39] [Iter 247/2250] R1[96/300], Temp: 0.7679, Energy: -43.816232-0.004168j
[2025-08-25 20:03:05] [Iter 248/2250] R1[97/300], Temp: 0.7635, Energy: -43.821821-0.006880j
[2025-08-25 20:03:31] [Iter 249/2250] R1[98/300], Temp: 0.7590, Energy: -43.814211+0.000951j
[2025-08-25 20:03:57] [Iter 250/2250] R1[99/300], Temp: 0.7545, Energy: -43.818827-0.004162j
[2025-08-25 20:03:57] ✓ Checkpoint saved: checkpoint_iter_000250.pkl
[2025-08-25 20:04:23] [Iter 251/2250] R1[100/300], Temp: 0.7500, Energy: -43.823169+0.005372j
[2025-08-25 20:04:49] [Iter 252/2250] R1[101/300], Temp: 0.7455, Energy: -43.822253-0.005900j
[2025-08-25 20:05:15] [Iter 253/2250] R1[102/300], Temp: 0.7409, Energy: -43.839107-0.000492j
[2025-08-25 20:05:41] [Iter 254/2250] R1[103/300], Temp: 0.7363, Energy: -43.841492+0.001396j
[2025-08-25 20:06:07] [Iter 255/2250] R1[104/300], Temp: 0.7316, Energy: -43.851863+0.007664j
[2025-08-25 20:06:32] [Iter 256/2250] R1[105/300], Temp: 0.7270, Energy: -43.858205-0.004800j
[2025-08-25 20:06:58] [Iter 257/2250] R1[106/300], Temp: 0.7223, Energy: -43.858495-0.003078j
[2025-08-25 20:07:24] [Iter 258/2250] R1[107/300], Temp: 0.7176, Energy: -43.861233-0.001954j
[2025-08-25 20:07:50] [Iter 259/2250] R1[108/300], Temp: 0.7129, Energy: -43.885758+0.009210j
[2025-08-25 20:08:16] [Iter 260/2250] R1[109/300], Temp: 0.7081, Energy: -43.877466-0.001747j
[2025-08-25 20:08:42] [Iter 261/2250] R1[110/300], Temp: 0.7034, Energy: -43.867695+0.003157j
[2025-08-25 20:09:08] [Iter 262/2250] R1[111/300], Temp: 0.6986, Energy: -43.885530+0.000168j
[2025-08-25 20:09:34] [Iter 263/2250] R1[112/300], Temp: 0.6938, Energy: -43.892009-0.001963j
[2025-08-25 20:10:00] [Iter 264/2250] R1[113/300], Temp: 0.6889, Energy: -43.896158+0.000836j
[2025-08-25 20:10:26] [Iter 265/2250] R1[114/300], Temp: 0.6841, Energy: -43.895495-0.002447j
[2025-08-25 20:10:52] [Iter 266/2250] R1[115/300], Temp: 0.6792, Energy: -43.882910-0.002277j
[2025-08-25 20:11:18] [Iter 267/2250] R1[116/300], Temp: 0.6743, Energy: -43.895248+0.001332j
[2025-08-25 20:11:44] [Iter 268/2250] R1[117/300], Temp: 0.6694, Energy: -43.905508+0.000553j
[2025-08-25 20:12:10] [Iter 269/2250] R1[118/300], Temp: 0.6644, Energy: -43.899779-0.009300j
[2025-08-25 20:12:35] [Iter 270/2250] R1[119/300], Temp: 0.6595, Energy: -43.903717-0.000535j
[2025-08-25 20:13:01] [Iter 271/2250] R1[120/300], Temp: 0.6545, Energy: -43.913514+0.005200j
[2025-08-25 20:13:27] [Iter 272/2250] R1[121/300], Temp: 0.6495, Energy: -43.923424-0.000055j
[2025-08-25 20:13:53] [Iter 273/2250] R1[122/300], Temp: 0.6445, Energy: -43.935465-0.002555j
[2025-08-25 20:14:19] [Iter 274/2250] R1[123/300], Temp: 0.6395, Energy: -43.925114+0.006206j
[2025-08-25 20:14:45] [Iter 275/2250] R1[124/300], Temp: 0.6345, Energy: -43.943411+0.004989j
[2025-08-25 20:15:11] [Iter 276/2250] R1[125/300], Temp: 0.6294, Energy: -43.933849+0.005680j
[2025-08-25 20:15:37] [Iter 277/2250] R1[126/300], Temp: 0.6243, Energy: -43.940907+0.005865j
[2025-08-25 20:16:03] [Iter 278/2250] R1[127/300], Temp: 0.6193, Energy: -43.957314+0.002319j
[2025-08-25 20:16:29] [Iter 279/2250] R1[128/300], Temp: 0.6142, Energy: -43.933177-0.006723j
[2025-08-25 20:16:55] [Iter 280/2250] R1[129/300], Temp: 0.6091, Energy: -43.941822+0.001276j
[2025-08-25 20:17:20] [Iter 281/2250] R1[130/300], Temp: 0.6040, Energy: -43.955976-0.001044j
[2025-08-25 20:17:46] [Iter 282/2250] R1[131/300], Temp: 0.5988, Energy: -43.949390+0.005744j
[2025-08-25 20:18:12] [Iter 283/2250] R1[132/300], Temp: 0.5937, Energy: -43.933615+0.002659j
[2025-08-25 20:18:38] [Iter 284/2250] R1[133/300], Temp: 0.5885, Energy: -43.962511+0.008713j
[2025-08-25 20:19:04] [Iter 285/2250] R1[134/300], Temp: 0.5834, Energy: -43.956426-0.004009j
[2025-08-25 20:19:30] [Iter 286/2250] R1[135/300], Temp: 0.5782, Energy: -43.977478+0.006765j
[2025-08-25 20:19:56] [Iter 287/2250] R1[136/300], Temp: 0.5730, Energy: -43.985069+0.009168j
[2025-08-25 20:20:22] [Iter 288/2250] R1[137/300], Temp: 0.5679, Energy: -43.974172-0.005926j
[2025-08-25 20:20:48] [Iter 289/2250] R1[138/300], Temp: 0.5627, Energy: -43.976911+0.009090j
[2025-08-25 20:21:14] [Iter 290/2250] R1[139/300], Temp: 0.5575, Energy: -43.962316+0.008804j
[2025-08-25 20:21:40] [Iter 291/2250] R1[140/300], Temp: 0.5523, Energy: -43.980051+0.007824j
[2025-08-25 20:22:06] [Iter 292/2250] R1[141/300], Temp: 0.5471, Energy: -43.983465-0.005117j
[2025-08-25 20:22:31] [Iter 293/2250] R1[142/300], Temp: 0.5418, Energy: -43.983123+0.003060j
[2025-08-25 20:22:57] [Iter 294/2250] R1[143/300], Temp: 0.5366, Energy: -44.000305-0.005355j
[2025-08-25 20:23:23] [Iter 295/2250] R1[144/300], Temp: 0.5314, Energy: -43.983524-0.000226j
[2025-08-25 20:23:49] [Iter 296/2250] R1[145/300], Temp: 0.5262, Energy: -43.995606+0.000760j
[2025-08-25 20:24:15] [Iter 297/2250] R1[146/300], Temp: 0.5209, Energy: -43.994157+0.004686j
[2025-08-25 20:24:41] [Iter 298/2250] R1[147/300], Temp: 0.5157, Energy: -44.017840+0.000330j
[2025-08-25 20:25:07] [Iter 299/2250] R1[148/300], Temp: 0.5105, Energy: -44.001468-0.003489j
[2025-08-25 20:25:33] [Iter 300/2250] R1[149/300], Temp: 0.5052, Energy: -44.011141+0.002689j
[2025-08-25 20:25:59] [Iter 301/2250] R1[150/300], Temp: 0.5000, Energy: -44.003606+0.004539j
[2025-08-25 20:26:25] [Iter 302/2250] R1[151/300], Temp: 0.4948, Energy: -44.016123+0.002207j
[2025-08-25 20:26:51] [Iter 303/2250] R1[152/300], Temp: 0.4895, Energy: -44.028675-0.007622j
[2025-08-25 20:27:16] [Iter 304/2250] R1[153/300], Temp: 0.4843, Energy: -44.014835-0.001121j
[2025-08-25 20:27:42] [Iter 305/2250] R1[154/300], Temp: 0.4791, Energy: -44.005159+0.000192j
[2025-08-25 20:28:08] [Iter 306/2250] R1[155/300], Temp: 0.4738, Energy: -44.048548+0.010179j
[2025-08-25 20:28:34] [Iter 307/2250] R1[156/300], Temp: 0.4686, Energy: -44.010884-0.004103j
[2025-08-25 20:29:00] [Iter 308/2250] R1[157/300], Temp: 0.4634, Energy: -44.014404-0.002859j
[2025-08-25 20:29:26] [Iter 309/2250] R1[158/300], Temp: 0.4582, Energy: -44.047564-0.006634j
[2025-08-25 20:29:52] [Iter 310/2250] R1[159/300], Temp: 0.4529, Energy: -44.051281-0.001661j
[2025-08-25 20:30:18] [Iter 311/2250] R1[160/300], Temp: 0.4477, Energy: -44.038012-0.003341j
[2025-08-25 20:30:44] [Iter 312/2250] R1[161/300], Temp: 0.4425, Energy: -44.056556-0.004177j
[2025-08-25 20:31:10] [Iter 313/2250] R1[162/300], Temp: 0.4373, Energy: -44.051669-0.002345j
[2025-08-25 20:31:36] [Iter 314/2250] R1[163/300], Temp: 0.4321, Energy: -44.050734+0.005625j
[2025-08-25 20:32:01] [Iter 315/2250] R1[164/300], Temp: 0.4270, Energy: -44.049423-0.004195j
[2025-08-25 20:32:27] [Iter 316/2250] R1[165/300], Temp: 0.4218, Energy: -44.052674+0.001881j
[2025-08-25 20:32:53] [Iter 317/2250] R1[166/300], Temp: 0.4166, Energy: -44.055387+0.008218j
[2025-08-25 20:33:19] [Iter 318/2250] R1[167/300], Temp: 0.4115, Energy: -44.075996-0.014030j
[2025-08-25 20:33:45] [Iter 319/2250] R1[168/300], Temp: 0.4063, Energy: -44.071703+0.000928j
[2025-08-25 20:34:11] [Iter 320/2250] R1[169/300], Temp: 0.4012, Energy: -44.058981+0.007210j
[2025-08-25 20:34:37] [Iter 321/2250] R1[170/300], Temp: 0.3960, Energy: -44.083971+0.008494j
[2025-08-25 20:35:03] [Iter 322/2250] R1[171/300], Temp: 0.3909, Energy: -44.071322+0.005367j
[2025-08-25 20:35:29] [Iter 323/2250] R1[172/300], Temp: 0.3858, Energy: -44.068266-0.001385j
[2025-08-25 20:35:55] [Iter 324/2250] R1[173/300], Temp: 0.3807, Energy: -44.090048-0.006797j
[2025-08-25 20:36:21] [Iter 325/2250] R1[174/300], Temp: 0.3757, Energy: -44.075057+0.004295j
[2025-08-25 20:36:47] [Iter 326/2250] R1[175/300], Temp: 0.3706, Energy: -44.094648+0.002633j
[2025-08-25 20:37:13] [Iter 327/2250] R1[176/300], Temp: 0.3655, Energy: -44.082202-0.000969j
[2025-08-25 20:37:39] [Iter 328/2250] R1[177/300], Temp: 0.3605, Energy: -44.094151+0.001302j
[2025-08-25 20:38:04] [Iter 329/2250] R1[178/300], Temp: 0.3555, Energy: -44.107930-0.000670j
[2025-08-25 20:38:30] [Iter 330/2250] R1[179/300], Temp: 0.3505, Energy: -44.103530+0.009144j
[2025-08-25 20:38:56] [Iter 331/2250] R1[180/300], Temp: 0.3455, Energy: -44.104573-0.002283j
[2025-08-25 20:39:22] [Iter 332/2250] R1[181/300], Temp: 0.3405, Energy: -44.086905-0.004832j
[2025-08-25 20:39:48] [Iter 333/2250] R1[182/300], Temp: 0.3356, Energy: -44.108899-0.002430j
[2025-08-25 20:40:14] [Iter 334/2250] R1[183/300], Temp: 0.3306, Energy: -44.118869+0.002799j
[2025-08-25 20:40:40] [Iter 335/2250] R1[184/300], Temp: 0.3257, Energy: -44.095565-0.010277j
[2025-08-25 20:41:06] [Iter 336/2250] R1[185/300], Temp: 0.3208, Energy: -44.113584-0.000117j
[2025-08-25 20:41:32] [Iter 337/2250] R1[186/300], Temp: 0.3159, Energy: -44.121542+0.001399j
[2025-08-25 20:41:58] [Iter 338/2250] R1[187/300], Temp: 0.3111, Energy: -44.106430+0.009293j
[2025-08-25 20:42:24] [Iter 339/2250] R1[188/300], Temp: 0.3062, Energy: -44.103086+0.001365j
[2025-08-25 20:42:50] [Iter 340/2250] R1[189/300], Temp: 0.3014, Energy: -44.106221-0.001431j
[2025-08-25 20:43:16] [Iter 341/2250] R1[190/300], Temp: 0.2966, Energy: -44.124840+0.005161j
[2025-08-25 20:43:42] [Iter 342/2250] R1[191/300], Temp: 0.2919, Energy: -44.113177+0.000731j
[2025-08-25 20:44:08] [Iter 343/2250] R1[192/300], Temp: 0.2871, Energy: -44.139449+0.002784j
[2025-08-25 20:44:33] [Iter 344/2250] R1[193/300], Temp: 0.2824, Energy: -44.126807+0.003239j
[2025-08-25 20:44:59] [Iter 345/2250] R1[194/300], Temp: 0.2777, Energy: -44.134502-0.000961j
[2025-08-25 20:45:25] [Iter 346/2250] R1[195/300], Temp: 0.2730, Energy: -44.141247-0.006574j
[2025-08-25 20:45:51] [Iter 347/2250] R1[196/300], Temp: 0.2684, Energy: -44.156433-0.002478j
[2025-08-25 20:46:17] [Iter 348/2250] R1[197/300], Temp: 0.2637, Energy: -44.133775-0.006525j
[2025-08-25 20:46:43] [Iter 349/2250] R1[198/300], Temp: 0.2591, Energy: -44.142763+0.004927j
[2025-08-25 20:47:09] [Iter 350/2250] R1[199/300], Temp: 0.2545, Energy: -44.150794+0.003718j
[2025-08-25 20:47:35] [Iter 351/2250] R1[200/300], Temp: 0.2500, Energy: -44.154827-0.004859j
[2025-08-25 20:48:01] [Iter 352/2250] R1[201/300], Temp: 0.2455, Energy: -44.150599-0.004929j
[2025-08-25 20:48:27] [Iter 353/2250] R1[202/300], Temp: 0.2410, Energy: -44.152560-0.003967j
[2025-08-25 20:48:53] [Iter 354/2250] R1[203/300], Temp: 0.2365, Energy: -44.162937+0.000205j
[2025-08-25 20:49:19] [Iter 355/2250] R1[204/300], Temp: 0.2321, Energy: -44.167735-0.002591j
[2025-08-25 20:49:44] [Iter 356/2250] R1[205/300], Temp: 0.2277, Energy: -44.171129+0.008834j
[2025-08-25 20:50:10] [Iter 357/2250] R1[206/300], Temp: 0.2233, Energy: -44.175663+0.002085j
[2025-08-25 20:50:36] [Iter 358/2250] R1[207/300], Temp: 0.2190, Energy: -44.171064+0.001523j
[2025-08-25 20:51:02] [Iter 359/2250] R1[208/300], Temp: 0.2146, Energy: -44.167427+0.005017j
[2025-08-25 20:51:28] [Iter 360/2250] R1[209/300], Temp: 0.2104, Energy: -44.172224-0.002165j
[2025-08-25 20:51:54] [Iter 361/2250] R1[210/300], Temp: 0.2061, Energy: -44.188432-0.001310j
[2025-08-25 20:52:20] [Iter 362/2250] R1[211/300], Temp: 0.2019, Energy: -44.178635-0.001611j
[2025-08-25 20:52:46] [Iter 363/2250] R1[212/300], Temp: 0.1977, Energy: -44.182856-0.000485j
[2025-08-25 20:53:12] [Iter 364/2250] R1[213/300], Temp: 0.1935, Energy: -44.198276+0.001876j
[2025-08-25 20:53:38] [Iter 365/2250] R1[214/300], Temp: 0.1894, Energy: -44.182234-0.006222j
[2025-08-25 20:54:04] [Iter 366/2250] R1[215/300], Temp: 0.1853, Energy: -44.178025-0.002154j
[2025-08-25 20:54:30] [Iter 367/2250] R1[216/300], Temp: 0.1813, Energy: -44.196945+0.005531j
[2025-08-25 20:54:56] [Iter 368/2250] R1[217/300], Temp: 0.1773, Energy: -44.190414+0.002951j
[2025-08-25 20:55:21] [Iter 369/2250] R1[218/300], Temp: 0.1733, Energy: -44.193248+0.000844j
[2025-08-25 20:55:47] [Iter 370/2250] R1[219/300], Temp: 0.1693, Energy: -44.211332-0.006424j
[2025-08-25 20:56:13] [Iter 371/2250] R1[220/300], Temp: 0.1654, Energy: -44.205598-0.003822j
[2025-08-25 20:56:39] [Iter 372/2250] R1[221/300], Temp: 0.1616, Energy: -44.214895+0.001011j
[2025-08-25 20:57:05] [Iter 373/2250] R1[222/300], Temp: 0.1577, Energy: -44.207409-0.002214j
[2025-08-25 20:57:31] [Iter 374/2250] R1[223/300], Temp: 0.1539, Energy: -44.223059+0.002381j
[2025-08-25 20:57:57] [Iter 375/2250] R1[224/300], Temp: 0.1502, Energy: -44.209520-0.002872j
[2025-08-25 20:58:23] [Iter 376/2250] R1[225/300], Temp: 0.1464, Energy: -44.202730+0.000794j
[2025-08-25 20:58:49] [Iter 377/2250] R1[226/300], Temp: 0.1428, Energy: -44.215398+0.000461j
[2025-08-25 20:59:15] [Iter 378/2250] R1[227/300], Temp: 0.1391, Energy: -44.224124-0.005423j
[2025-08-25 20:59:40] [Iter 379/2250] R1[228/300], Temp: 0.1355, Energy: -44.223613-0.002466j
[2025-08-25 21:00:06] [Iter 380/2250] R1[229/300], Temp: 0.1320, Energy: -44.219591-0.005418j
[2025-08-25 21:00:32] [Iter 381/2250] R1[230/300], Temp: 0.1284, Energy: -44.220816-0.002763j
[2025-08-25 21:00:58] [Iter 382/2250] R1[231/300], Temp: 0.1249, Energy: -44.220370-0.007733j
[2025-08-25 21:01:24] [Iter 383/2250] R1[232/300], Temp: 0.1215, Energy: -44.241663+0.001759j
[2025-08-25 21:01:50] [Iter 384/2250] R1[233/300], Temp: 0.1181, Energy: -44.242610-0.001545j
[2025-08-25 21:02:16] [Iter 385/2250] R1[234/300], Temp: 0.1147, Energy: -44.255947-0.004587j
[2025-08-25 21:02:42] [Iter 386/2250] R1[235/300], Temp: 0.1114, Energy: -44.242443+0.000585j
[2025-08-25 21:03:08] [Iter 387/2250] R1[236/300], Temp: 0.1082, Energy: -44.256443+0.002637j
[2025-08-25 21:03:34] [Iter 388/2250] R1[237/300], Temp: 0.1049, Energy: -44.246797-0.002611j
[2025-08-25 21:04:00] [Iter 389/2250] R1[238/300], Temp: 0.1017, Energy: -44.243861-0.000392j
[2025-08-25 21:04:26] [Iter 390/2250] R1[239/300], Temp: 0.0986, Energy: -44.260507-0.000012j
[2025-08-25 21:04:51] [Iter 391/2250] R1[240/300], Temp: 0.0955, Energy: -44.264474+0.004071j
[2025-08-25 21:05:17] [Iter 392/2250] R1[241/300], Temp: 0.0924, Energy: -44.272699+0.005216j
[2025-08-25 21:05:43] [Iter 393/2250] R1[242/300], Temp: 0.0894, Energy: -44.259361+0.000722j
[2025-08-25 21:06:09] [Iter 394/2250] R1[243/300], Temp: 0.0865, Energy: -44.282130+0.005943j
[2025-08-25 21:06:35] [Iter 395/2250] R1[244/300], Temp: 0.0835, Energy: -44.280665-0.002018j
[2025-08-25 21:07:01] [Iter 396/2250] R1[245/300], Temp: 0.0807, Energy: -44.272448+0.001285j
[2025-08-25 21:07:27] [Iter 397/2250] R1[246/300], Temp: 0.0778, Energy: -44.271117+0.005653j
[2025-08-25 21:07:53] [Iter 398/2250] R1[247/300], Temp: 0.0751, Energy: -44.280489+0.003382j
[2025-08-25 21:08:19] [Iter 399/2250] R1[248/300], Temp: 0.0723, Energy: -44.279815-0.001342j
[2025-08-25 21:08:45] [Iter 400/2250] R1[249/300], Temp: 0.0696, Energy: -44.290811-0.001780j
[2025-08-25 21:09:10] [Iter 401/2250] R1[250/300], Temp: 0.0670, Energy: -44.283756+0.000248j
[2025-08-25 21:09:36] [Iter 402/2250] R1[251/300], Temp: 0.0644, Energy: -44.268766-0.006070j
[2025-08-25 21:10:02] [Iter 403/2250] R1[252/300], Temp: 0.0618, Energy: -44.296755+0.000161j
[2025-08-25 21:10:28] [Iter 404/2250] R1[253/300], Temp: 0.0593, Energy: -44.299358-0.003331j
[2025-08-25 21:10:54] [Iter 405/2250] R1[254/300], Temp: 0.0569, Energy: -44.291555+0.010805j
[2025-08-25 21:11:20] [Iter 406/2250] R1[255/300], Temp: 0.0545, Energy: -44.315383+0.005589j
[2025-08-25 21:11:46] [Iter 407/2250] R1[256/300], Temp: 0.0521, Energy: -44.305361-0.001822j
[2025-08-25 21:12:12] [Iter 408/2250] R1[257/300], Temp: 0.0498, Energy: -44.295750-0.001181j
[2025-08-25 21:12:38] [Iter 409/2250] R1[258/300], Temp: 0.0476, Energy: -44.308126+0.003383j
[2025-08-25 21:13:04] [Iter 410/2250] R1[259/300], Temp: 0.0454, Energy: -44.321168-0.005576j
[2025-08-25 21:13:30] [Iter 411/2250] R1[260/300], Temp: 0.0432, Energy: -44.317445+0.006160j
[2025-08-25 21:13:56] [Iter 412/2250] R1[261/300], Temp: 0.0411, Energy: -44.326539+0.000134j
[2025-08-25 21:14:21] [Iter 413/2250] R1[262/300], Temp: 0.0391, Energy: -44.326150-0.001052j
[2025-08-25 21:14:47] [Iter 414/2250] R1[263/300], Temp: 0.0371, Energy: -44.338412-0.001904j
[2025-08-25 21:15:13] [Iter 415/2250] R1[264/300], Temp: 0.0351, Energy: -44.311434-0.005985j
[2025-08-25 21:15:39] [Iter 416/2250] R1[265/300], Temp: 0.0332, Energy: -44.326857-0.001930j
[2025-08-25 21:16:05] [Iter 417/2250] R1[266/300], Temp: 0.0314, Energy: -44.338781+0.001829j
[2025-08-25 21:16:31] [Iter 418/2250] R1[267/300], Temp: 0.0296, Energy: -44.331904+0.001022j
[2025-08-25 21:16:57] [Iter 419/2250] R1[268/300], Temp: 0.0278, Energy: -44.324137+0.001378j
[2025-08-25 21:17:23] [Iter 420/2250] R1[269/300], Temp: 0.0261, Energy: -44.319896+0.004782j
[2025-08-25 21:17:49] [Iter 421/2250] R1[270/300], Temp: 0.0245, Energy: -44.346361+0.001590j
[2025-08-25 21:18:15] [Iter 422/2250] R1[271/300], Temp: 0.0229, Energy: -44.362551+0.007758j
[2025-08-25 21:18:41] [Iter 423/2250] R1[272/300], Temp: 0.0213, Energy: -44.347210-0.005075j
[2025-08-25 21:19:06] [Iter 424/2250] R1[273/300], Temp: 0.0199, Energy: -44.334160-0.001954j
[2025-08-25 21:19:32] [Iter 425/2250] R1[274/300], Temp: 0.0184, Energy: -44.367083-0.006389j
[2025-08-25 21:19:58] [Iter 426/2250] R1[275/300], Temp: 0.0170, Energy: -44.339281+0.003003j
[2025-08-25 21:20:24] [Iter 427/2250] R1[276/300], Temp: 0.0157, Energy: -44.361083+0.003438j
[2025-08-25 21:20:50] [Iter 428/2250] R1[277/300], Temp: 0.0144, Energy: -44.361877-0.003055j
[2025-08-25 21:21:16] [Iter 429/2250] R1[278/300], Temp: 0.0132, Energy: -44.359878+0.001503j
[2025-08-25 21:21:42] [Iter 430/2250] R1[279/300], Temp: 0.0120, Energy: -44.381978+0.003402j
[2025-08-25 21:22:08] [Iter 431/2250] R1[280/300], Temp: 0.0109, Energy: -44.372248+0.002591j
[2025-08-25 21:22:34] [Iter 432/2250] R1[281/300], Temp: 0.0099, Energy: -44.368836+0.002370j
[2025-08-25 21:23:00] [Iter 433/2250] R1[282/300], Temp: 0.0089, Energy: -44.379473-0.000914j
[2025-08-25 21:23:26] [Iter 434/2250] R1[283/300], Temp: 0.0079, Energy: -44.378517-0.006866j
[2025-08-25 21:23:52] [Iter 435/2250] R1[284/300], Temp: 0.0070, Energy: -44.382096+0.002310j
[2025-08-25 21:24:18] [Iter 436/2250] R1[285/300], Temp: 0.0062, Energy: -44.404492+0.001750j
[2025-08-25 21:24:43] [Iter 437/2250] R1[286/300], Temp: 0.0054, Energy: -44.393157-0.002470j
[2025-08-25 21:25:09] [Iter 438/2250] R1[287/300], Temp: 0.0046, Energy: -44.402477-0.006186j
[2025-08-25 21:25:35] [Iter 439/2250] R1[288/300], Temp: 0.0039, Energy: -44.394033+0.000512j
[2025-08-25 21:26:01] [Iter 440/2250] R1[289/300], Temp: 0.0033, Energy: -44.379063-0.003694j
[2025-08-25 21:26:27] [Iter 441/2250] R1[290/300], Temp: 0.0027, Energy: -44.390631-0.005430j
[2025-08-25 21:26:53] [Iter 442/2250] R1[291/300], Temp: 0.0022, Energy: -44.404765+0.003555j
[2025-08-25 21:27:19] [Iter 443/2250] R1[292/300], Temp: 0.0018, Energy: -44.414186-0.001161j
[2025-08-25 21:27:45] [Iter 444/2250] R1[293/300], Temp: 0.0013, Energy: -44.394055+0.002214j
[2025-08-25 21:28:11] [Iter 445/2250] R1[294/300], Temp: 0.0010, Energy: -44.395015-0.005536j
[2025-08-25 21:28:37] [Iter 446/2250] R1[295/300], Temp: 0.0007, Energy: -44.417250+0.005387j
[2025-08-25 21:29:03] [Iter 447/2250] R1[296/300], Temp: 0.0004, Energy: -44.410847+0.001659j
[2025-08-25 21:29:29] [Iter 448/2250] R1[297/300], Temp: 0.0002, Energy: -44.406361+0.001069j
[2025-08-25 21:29:54] [Iter 449/2250] R1[298/300], Temp: 0.0001, Energy: -44.418034-0.004992j
[2025-08-25 21:30:20] [Iter 450/2250] R1[299/300], Temp: 0.0000, Energy: -44.431199+0.002818j
[2025-08-25 21:30:20] RESTART #2 | Period: 600
[2025-08-25 21:30:46] [Iter 451/2250] R2[0/600], Temp: 1.0000, Energy: -44.430760+0.004932j
[2025-08-25 21:31:12] [Iter 452/2250] R2[1/600], Temp: 1.0000, Energy: -44.406561-0.003685j
[2025-08-25 21:31:38] [Iter 453/2250] R2[2/600], Temp: 1.0000, Energy: -44.438483+0.008565j
[2025-08-25 21:32:04] [Iter 454/2250] R2[3/600], Temp: 0.9999, Energy: -44.424598+0.009718j
[2025-08-25 21:32:30] [Iter 455/2250] R2[4/600], Temp: 0.9999, Energy: -44.419504-0.002005j
[2025-08-25 21:32:56] [Iter 456/2250] R2[5/600], Temp: 0.9998, Energy: -44.422902+0.000958j
[2025-08-25 21:33:22] [Iter 457/2250] R2[6/600], Temp: 0.9998, Energy: -44.430083-0.004439j
[2025-08-25 21:33:48] [Iter 458/2250] R2[7/600], Temp: 0.9997, Energy: -44.434321-0.000347j
[2025-08-25 21:34:14] [Iter 459/2250] R2[8/600], Temp: 0.9996, Energy: -44.427780+0.000111j
[2025-08-25 21:34:39] [Iter 460/2250] R2[9/600], Temp: 0.9994, Energy: -44.427370+0.002402j
[2025-08-25 21:35:05] [Iter 461/2250] R2[10/600], Temp: 0.9993, Energy: -44.446054+0.002477j
[2025-08-25 21:35:31] [Iter 462/2250] R2[11/600], Temp: 0.9992, Energy: -44.446493+0.000970j
[2025-08-25 21:35:57] [Iter 463/2250] R2[12/600], Temp: 0.9990, Energy: -44.443036-0.009930j
[2025-08-25 21:36:23] [Iter 464/2250] R2[13/600], Temp: 0.9988, Energy: -44.445677-0.002799j
[2025-08-25 21:36:49] [Iter 465/2250] R2[14/600], Temp: 0.9987, Energy: -44.445285+0.008694j
[2025-08-25 21:37:15] [Iter 466/2250] R2[15/600], Temp: 0.9985, Energy: -44.458790-0.005355j
[2025-08-25 21:37:41] [Iter 467/2250] R2[16/600], Temp: 0.9982, Energy: -44.445580+0.001059j
[2025-08-25 21:38:07] [Iter 468/2250] R2[17/600], Temp: 0.9980, Energy: -44.458950-0.000165j
[2025-08-25 21:38:32] [Iter 469/2250] R2[18/600], Temp: 0.9978, Energy: -44.458977-0.008617j
[2025-08-25 21:38:58] [Iter 470/2250] R2[19/600], Temp: 0.9975, Energy: -44.457788-0.005171j
[2025-08-25 21:39:24] [Iter 471/2250] R2[20/600], Temp: 0.9973, Energy: -44.453329-0.004434j
[2025-08-25 21:39:50] [Iter 472/2250] R2[21/600], Temp: 0.9970, Energy: -44.461096+0.005283j
[2025-08-25 21:40:16] [Iter 473/2250] R2[22/600], Temp: 0.9967, Energy: -44.484341+0.001060j
[2025-08-25 21:40:42] [Iter 474/2250] R2[23/600], Temp: 0.9964, Energy: -44.478049-0.003693j
[2025-08-25 21:41:08] [Iter 475/2250] R2[24/600], Temp: 0.9961, Energy: -44.463764-0.006551j
[2025-08-25 21:41:34] [Iter 476/2250] R2[25/600], Temp: 0.9957, Energy: -44.486150+0.000859j
[2025-08-25 21:42:00] [Iter 477/2250] R2[26/600], Temp: 0.9954, Energy: -44.472551-0.000812j
[2025-08-25 21:42:26] [Iter 478/2250] R2[27/600], Temp: 0.9950, Energy: -44.467921+0.003623j
[2025-08-25 21:42:52] [Iter 479/2250] R2[28/600], Temp: 0.9946, Energy: -44.477030-0.004371j
[2025-08-25 21:43:18] [Iter 480/2250] R2[29/600], Temp: 0.9942, Energy: -44.472032+0.003713j
[2025-08-25 21:43:43] [Iter 481/2250] R2[30/600], Temp: 0.9938, Energy: -44.477562+0.000786j
[2025-08-25 21:44:09] [Iter 482/2250] R2[31/600], Temp: 0.9934, Energy: -44.492792+0.005154j
[2025-08-25 21:44:35] [Iter 483/2250] R2[32/600], Temp: 0.9930, Energy: -44.478373+0.002938j
[2025-08-25 21:45:01] [Iter 484/2250] R2[33/600], Temp: 0.9926, Energy: -44.469671+0.000195j
[2025-08-25 21:45:27] [Iter 485/2250] R2[34/600], Temp: 0.9921, Energy: -44.494172-0.001852j
[2025-08-25 21:45:53] [Iter 486/2250] R2[35/600], Temp: 0.9916, Energy: -44.508424+0.002478j
[2025-08-25 21:46:19] [Iter 487/2250] R2[36/600], Temp: 0.9911, Energy: -44.491875+0.002587j
[2025-08-25 21:46:45] [Iter 488/2250] R2[37/600], Temp: 0.9906, Energy: -44.482550+0.001200j
[2025-08-25 21:47:11] [Iter 489/2250] R2[38/600], Temp: 0.9901, Energy: -44.484711+0.001179j
[2025-08-25 21:47:37] [Iter 490/2250] R2[39/600], Temp: 0.9896, Energy: -44.511799-0.000297j
[2025-08-25 21:48:03] [Iter 491/2250] R2[40/600], Temp: 0.9891, Energy: -44.494101+0.000602j
[2025-08-25 21:48:28] [Iter 492/2250] R2[41/600], Temp: 0.9885, Energy: -44.491374-0.002375j
[2025-08-25 21:48:54] [Iter 493/2250] R2[42/600], Temp: 0.9880, Energy: -44.495864-0.000823j
[2025-08-25 21:49:20] [Iter 494/2250] R2[43/600], Temp: 0.9874, Energy: -44.499271+0.001283j
[2025-08-25 21:49:46] [Iter 495/2250] R2[44/600], Temp: 0.9868, Energy: -44.509972+0.001482j
[2025-08-25 21:50:12] [Iter 496/2250] R2[45/600], Temp: 0.9862, Energy: -44.518388-0.000953j
[2025-08-25 21:50:38] [Iter 497/2250] R2[46/600], Temp: 0.9856, Energy: -44.498400-0.007450j
[2025-08-25 21:51:04] [Iter 498/2250] R2[47/600], Temp: 0.9849, Energy: -44.500015-0.000752j
[2025-08-25 21:51:30] [Iter 499/2250] R2[48/600], Temp: 0.9843, Energy: -44.518877-0.001714j
[2025-08-25 21:51:56] [Iter 500/2250] R2[49/600], Temp: 0.9836, Energy: -44.488595-0.004480j
[2025-08-25 21:51:56] ✓ Checkpoint saved: checkpoint_iter_000500.pkl
[2025-08-25 21:52:22] [Iter 501/2250] R2[50/600], Temp: 0.9830, Energy: -44.508076-0.002212j
[2025-08-25 21:52:48] [Iter 502/2250] R2[51/600], Temp: 0.9823, Energy: -44.505641-0.002259j
[2025-08-25 21:53:14] [Iter 503/2250] R2[52/600], Temp: 0.9816, Energy: -44.511823+0.003548j
[2025-08-25 21:53:39] [Iter 504/2250] R2[53/600], Temp: 0.9809, Energy: -44.524630-0.000201j
[2025-08-25 21:54:05] [Iter 505/2250] R2[54/600], Temp: 0.9801, Energy: -44.516353+0.004221j
[2025-08-25 21:54:31] [Iter 506/2250] R2[55/600], Temp: 0.9794, Energy: -44.510235+0.000808j
[2025-08-25 21:54:57] [Iter 507/2250] R2[56/600], Temp: 0.9787, Energy: -44.535729+0.002770j
[2025-08-25 21:55:23] [Iter 508/2250] R2[57/600], Temp: 0.9779, Energy: -44.508897+0.001297j
[2025-08-25 21:55:49] [Iter 509/2250] R2[58/600], Temp: 0.9771, Energy: -44.530346-0.003895j
[2025-08-25 21:56:15] [Iter 510/2250] R2[59/600], Temp: 0.9763, Energy: -44.528993-0.000671j
[2025-08-25 21:56:41] [Iter 511/2250] R2[60/600], Temp: 0.9755, Energy: -44.523233-0.006869j
[2025-08-25 21:57:07] [Iter 512/2250] R2[61/600], Temp: 0.9747, Energy: -44.516395-0.002594j
[2025-08-25 21:57:33] [Iter 513/2250] R2[62/600], Temp: 0.9739, Energy: -44.523563+0.002844j
[2025-08-25 21:57:59] [Iter 514/2250] R2[63/600], Temp: 0.9730, Energy: -44.534202+0.004228j
[2025-08-25 21:58:25] [Iter 515/2250] R2[64/600], Temp: 0.9722, Energy: -44.536704-0.001393j
[2025-08-25 21:58:51] [Iter 516/2250] R2[65/600], Temp: 0.9713, Energy: -44.535769-0.011197j
[2025-08-25 21:59:16] [Iter 517/2250] R2[66/600], Temp: 0.9704, Energy: -44.534672+0.002877j
[2025-08-25 21:59:42] [Iter 518/2250] R2[67/600], Temp: 0.9695, Energy: -44.535997-0.006154j
[2025-08-25 22:00:08] [Iter 519/2250] R2[68/600], Temp: 0.9686, Energy: -44.536380+0.004436j
[2025-08-25 22:00:34] [Iter 520/2250] R2[69/600], Temp: 0.9677, Energy: -44.529821-0.003262j
[2025-08-25 22:01:00] [Iter 521/2250] R2[70/600], Temp: 0.9668, Energy: -44.541309+0.004070j
[2025-08-25 22:01:26] [Iter 522/2250] R2[71/600], Temp: 0.9658, Energy: -44.534435+0.000869j
[2025-08-25 22:01:52] [Iter 523/2250] R2[72/600], Temp: 0.9649, Energy: -44.527292-0.007208j
[2025-08-25 22:02:18] [Iter 524/2250] R2[73/600], Temp: 0.9639, Energy: -44.549910+0.001775j
[2025-08-25 22:02:44] [Iter 525/2250] R2[74/600], Temp: 0.9629, Energy: -44.546540+0.003097j
[2025-08-25 22:03:10] [Iter 526/2250] R2[75/600], Temp: 0.9619, Energy: -44.523663+0.005435j
[2025-08-25 22:03:36] [Iter 527/2250] R2[76/600], Temp: 0.9609, Energy: -44.534118+0.000551j
[2025-08-25 22:04:02] [Iter 528/2250] R2[77/600], Temp: 0.9599, Energy: -44.538414+0.004683j
[2025-08-25 22:04:28] [Iter 529/2250] R2[78/600], Temp: 0.9589, Energy: -44.550946+0.003199j
[2025-08-25 22:04:54] [Iter 530/2250] R2[79/600], Temp: 0.9578, Energy: -44.556795+0.006626j
[2025-08-25 22:05:19] [Iter 531/2250] R2[80/600], Temp: 0.9568, Energy: -44.543253+0.005644j
[2025-08-25 22:05:45] [Iter 532/2250] R2[81/600], Temp: 0.9557, Energy: -44.547913+0.004115j
[2025-08-25 22:06:11] [Iter 533/2250] R2[82/600], Temp: 0.9546, Energy: -44.552216-0.002723j
[2025-08-25 22:06:37] [Iter 534/2250] R2[83/600], Temp: 0.9535, Energy: -44.559982-0.001270j
[2025-08-25 22:07:03] [Iter 535/2250] R2[84/600], Temp: 0.9524, Energy: -44.565228+0.004213j
[2025-08-25 22:07:29] [Iter 536/2250] R2[85/600], Temp: 0.9513, Energy: -44.556824-0.003844j
[2025-08-25 22:07:55] [Iter 537/2250] R2[86/600], Temp: 0.9502, Energy: -44.543093-0.000522j
[2025-08-25 22:08:21] [Iter 538/2250] R2[87/600], Temp: 0.9490, Energy: -44.571351-0.000196j
[2025-08-25 22:08:47] [Iter 539/2250] R2[88/600], Temp: 0.9479, Energy: -44.563157+0.006404j
[2025-08-25 22:09:13] [Iter 540/2250] R2[89/600], Temp: 0.9467, Energy: -44.571117+0.003621j
[2025-08-25 22:09:39] [Iter 541/2250] R2[90/600], Temp: 0.9455, Energy: -44.564700-0.000784j
[2025-08-25 22:10:05] [Iter 542/2250] R2[91/600], Temp: 0.9443, Energy: -44.561280+0.004122j
[2025-08-25 22:10:31] [Iter 543/2250] R2[92/600], Temp: 0.9431, Energy: -44.570781-0.001639j
[2025-08-25 22:10:57] [Iter 544/2250] R2[93/600], Temp: 0.9419, Energy: -44.553674-0.002946j
[2025-08-25 22:11:22] [Iter 545/2250] R2[94/600], Temp: 0.9407, Energy: -44.567389-0.004141j
[2025-08-25 22:11:48] [Iter 546/2250] R2[95/600], Temp: 0.9394, Energy: -44.576646-0.001664j
[2025-08-25 22:12:14] [Iter 547/2250] R2[96/600], Temp: 0.9382, Energy: -44.569572+0.004399j
[2025-08-25 22:12:40] [Iter 548/2250] R2[97/600], Temp: 0.9369, Energy: -44.571742-0.000313j
[2025-08-25 22:13:06] [Iter 549/2250] R2[98/600], Temp: 0.9356, Energy: -44.570365-0.006355j
[2025-08-25 22:13:32] [Iter 550/2250] R2[99/600], Temp: 0.9343, Energy: -44.569986+0.010328j
[2025-08-25 22:13:58] [Iter 551/2250] R2[100/600], Temp: 0.9330, Energy: -44.578384-0.004354j
[2025-08-25 22:14:24] [Iter 552/2250] R2[101/600], Temp: 0.9317, Energy: -44.569719-0.001951j
[2025-08-25 22:14:50] [Iter 553/2250] R2[102/600], Temp: 0.9304, Energy: -44.568236-0.007530j
[2025-08-25 22:15:16] [Iter 554/2250] R2[103/600], Temp: 0.9290, Energy: -44.567808-0.001979j
[2025-08-25 22:15:42] [Iter 555/2250] R2[104/600], Temp: 0.9277, Energy: -44.573821+0.004529j
[2025-08-25 22:16:08] [Iter 556/2250] R2[105/600], Temp: 0.9263, Energy: -44.580950-0.002744j
[2025-08-25 22:16:33] [Iter 557/2250] R2[106/600], Temp: 0.9249, Energy: -44.566871+0.000132j
[2025-08-25 22:16:59] [Iter 558/2250] R2[107/600], Temp: 0.9236, Energy: -44.577203+0.002632j
[2025-08-25 22:17:25] [Iter 559/2250] R2[108/600], Temp: 0.9222, Energy: -44.571656+0.003769j
[2025-08-25 22:17:51] [Iter 560/2250] R2[109/600], Temp: 0.9208, Energy: -44.569435-0.002295j
[2025-08-25 22:18:17] [Iter 561/2250] R2[110/600], Temp: 0.9193, Energy: -44.585374-0.005875j
[2025-08-25 22:18:43] [Iter 562/2250] R2[111/600], Temp: 0.9179, Energy: -44.591120-0.005494j
[2025-08-25 22:19:09] [Iter 563/2250] R2[112/600], Temp: 0.9165, Energy: -44.593521+0.007920j
[2025-08-25 22:19:35] [Iter 564/2250] R2[113/600], Temp: 0.9150, Energy: -44.569548-0.000308j
[2025-08-25 22:20:01] [Iter 565/2250] R2[114/600], Temp: 0.9135, Energy: -44.586836-0.003649j
[2025-08-25 22:20:27] [Iter 566/2250] R2[115/600], Temp: 0.9121, Energy: -44.573349+0.001676j
[2025-08-25 22:20:53] [Iter 567/2250] R2[116/600], Temp: 0.9106, Energy: -44.576834+0.004327j
[2025-08-25 22:21:19] [Iter 568/2250] R2[117/600], Temp: 0.9091, Energy: -44.576750+0.004592j
[2025-08-25 22:21:45] [Iter 569/2250] R2[118/600], Temp: 0.9076, Energy: -44.576845-0.000330j
[2025-08-25 22:22:10] [Iter 570/2250] R2[119/600], Temp: 0.9060, Energy: -44.599111-0.007683j
[2025-08-25 22:22:36] [Iter 571/2250] R2[120/600], Temp: 0.9045, Energy: -44.598535+0.003643j
[2025-08-25 22:23:02] [Iter 572/2250] R2[121/600], Temp: 0.9030, Energy: -44.595527-0.003812j
[2025-08-25 22:23:28] [Iter 573/2250] R2[122/600], Temp: 0.9014, Energy: -44.595638-0.001192j
[2025-08-25 22:23:54] [Iter 574/2250] R2[123/600], Temp: 0.8998, Energy: -44.592043-0.002130j
[2025-08-25 22:24:20] [Iter 575/2250] R2[124/600], Temp: 0.8983, Energy: -44.592563-0.000178j
[2025-08-25 22:24:46] [Iter 576/2250] R2[125/600], Temp: 0.8967, Energy: -44.598742-0.000379j
[2025-08-25 22:25:12] [Iter 577/2250] R2[126/600], Temp: 0.8951, Energy: -44.591490-0.003548j
[2025-08-25 22:25:38] [Iter 578/2250] R2[127/600], Temp: 0.8935, Energy: -44.595970-0.001936j
[2025-08-25 22:26:04] [Iter 579/2250] R2[128/600], Temp: 0.8918, Energy: -44.592937+0.005324j
[2025-08-25 22:26:30] [Iter 580/2250] R2[129/600], Temp: 0.8902, Energy: -44.607579+0.001952j
[2025-08-25 22:26:56] [Iter 581/2250] R2[130/600], Temp: 0.8886, Energy: -44.593410+0.005290j
[2025-08-25 22:27:21] [Iter 582/2250] R2[131/600], Temp: 0.8869, Energy: -44.591993+0.001724j
[2025-08-25 22:27:47] [Iter 583/2250] R2[132/600], Temp: 0.8853, Energy: -44.595778+0.001459j
[2025-08-25 22:28:13] [Iter 584/2250] R2[133/600], Temp: 0.8836, Energy: -44.613799+0.000964j
[2025-08-25 22:28:39] [Iter 585/2250] R2[134/600], Temp: 0.8819, Energy: -44.603075+0.004117j
[2025-08-25 22:29:05] [Iter 586/2250] R2[135/600], Temp: 0.8802, Energy: -44.601448+0.002952j
[2025-08-25 22:29:31] [Iter 587/2250] R2[136/600], Temp: 0.8785, Energy: -44.615538+0.010820j
[2025-08-25 22:29:57] [Iter 588/2250] R2[137/600], Temp: 0.8768, Energy: -44.610926+0.001326j
[2025-08-25 22:30:23] [Iter 589/2250] R2[138/600], Temp: 0.8751, Energy: -44.613013-0.002015j
[2025-08-25 22:30:49] [Iter 590/2250] R2[139/600], Temp: 0.8733, Energy: -44.604183+0.000735j
[2025-08-25 22:31:15] [Iter 591/2250] R2[140/600], Temp: 0.8716, Energy: -44.615583+0.006800j
[2025-08-25 22:31:41] [Iter 592/2250] R2[141/600], Temp: 0.8698, Energy: -44.614046-0.000217j
[2025-08-25 22:32:07] [Iter 593/2250] R2[142/600], Temp: 0.8680, Energy: -44.621421+0.001361j
[2025-08-25 22:32:32] [Iter 594/2250] R2[143/600], Temp: 0.8663, Energy: -44.602073+0.000744j
[2025-08-25 22:32:58] [Iter 595/2250] R2[144/600], Temp: 0.8645, Energy: -44.619119+0.001266j
[2025-08-25 22:33:24] [Iter 596/2250] R2[145/600], Temp: 0.8627, Energy: -44.617843-0.002794j
[2025-08-25 22:33:50] [Iter 597/2250] R2[146/600], Temp: 0.8609, Energy: -44.624504-0.001392j
[2025-08-25 22:34:16] [Iter 598/2250] R2[147/600], Temp: 0.8591, Energy: -44.599237-0.003078j
[2025-08-25 22:34:42] [Iter 599/2250] R2[148/600], Temp: 0.8572, Energy: -44.593558+0.001066j
[2025-08-25 22:35:08] [Iter 600/2250] R2[149/600], Temp: 0.8554, Energy: -44.614873-0.000305j
[2025-08-25 22:35:34] [Iter 601/2250] R2[150/600], Temp: 0.8536, Energy: -44.609127-0.002102j
[2025-08-25 22:36:00] [Iter 602/2250] R2[151/600], Temp: 0.8517, Energy: -44.614306+0.001027j
[2025-08-25 22:36:26] [Iter 603/2250] R2[152/600], Temp: 0.8498, Energy: -44.620384-0.003381j
[2025-08-25 22:36:52] [Iter 604/2250] R2[153/600], Temp: 0.8480, Energy: -44.598415+0.000727j
[2025-08-25 22:37:18] [Iter 605/2250] R2[154/600], Temp: 0.8461, Energy: -44.612891+0.001388j
[2025-08-25 22:37:44] [Iter 606/2250] R2[155/600], Temp: 0.8442, Energy: -44.611945-0.005953j
[2025-08-25 22:38:10] [Iter 607/2250] R2[156/600], Temp: 0.8423, Energy: -44.629152+0.000963j
[2025-08-25 22:38:35] [Iter 608/2250] R2[157/600], Temp: 0.8404, Energy: -44.610206-0.004900j
[2025-08-25 22:39:01] [Iter 609/2250] R2[158/600], Temp: 0.8384, Energy: -44.619533+0.003022j
[2025-08-25 22:39:27] [Iter 610/2250] R2[159/600], Temp: 0.8365, Energy: -44.622558-0.004660j
[2025-08-25 22:39:53] [Iter 611/2250] R2[160/600], Temp: 0.8346, Energy: -44.607130+0.001681j
[2025-08-25 22:40:19] [Iter 612/2250] R2[161/600], Temp: 0.8326, Energy: -44.640552-0.004672j
[2025-08-25 22:40:45] [Iter 613/2250] R2[162/600], Temp: 0.8307, Energy: -44.627892-0.003530j
[2025-08-25 22:41:11] [Iter 614/2250] R2[163/600], Temp: 0.8287, Energy: -44.633873-0.001308j
[2025-08-25 22:41:37] [Iter 615/2250] R2[164/600], Temp: 0.8267, Energy: -44.625811-0.004747j
[2025-08-25 22:42:03] [Iter 616/2250] R2[165/600], Temp: 0.8247, Energy: -44.634231-0.000400j
[2025-08-25 22:42:29] [Iter 617/2250] R2[166/600], Temp: 0.8227, Energy: -44.623595+0.002974j
[2025-08-25 22:42:55] [Iter 618/2250] R2[167/600], Temp: 0.8207, Energy: -44.637965+0.002888j
[2025-08-25 22:43:21] [Iter 619/2250] R2[168/600], Temp: 0.8187, Energy: -44.623903+0.004410j
[2025-08-25 22:43:47] [Iter 620/2250] R2[169/600], Temp: 0.8167, Energy: -44.644195+0.001867j
[2025-08-25 22:44:12] [Iter 621/2250] R2[170/600], Temp: 0.8147, Energy: -44.640865+0.005363j
[2025-08-25 22:44:38] [Iter 622/2250] R2[171/600], Temp: 0.8126, Energy: -44.638812-0.003449j
[2025-08-25 22:45:04] [Iter 623/2250] R2[172/600], Temp: 0.8106, Energy: -44.629094-0.008613j
[2025-08-25 22:45:30] [Iter 624/2250] R2[173/600], Temp: 0.8085, Energy: -44.642026+0.001488j
[2025-08-25 22:45:56] [Iter 625/2250] R2[174/600], Temp: 0.8065, Energy: -44.647027+0.003179j
[2025-08-25 22:46:22] [Iter 626/2250] R2[175/600], Temp: 0.8044, Energy: -44.636277-0.003154j
[2025-08-25 22:46:48] [Iter 627/2250] R2[176/600], Temp: 0.8023, Energy: -44.627072-0.000005j
[2025-08-25 22:47:14] [Iter 628/2250] R2[177/600], Temp: 0.8002, Energy: -44.634587-0.005459j
[2025-08-25 22:47:40] [Iter 629/2250] R2[178/600], Temp: 0.7981, Energy: -44.637366+0.001377j
[2025-08-25 22:48:06] [Iter 630/2250] R2[179/600], Temp: 0.7960, Energy: -44.635604-0.001554j
[2025-08-25 22:48:32] [Iter 631/2250] R2[180/600], Temp: 0.7939, Energy: -44.649853-0.000530j
[2025-08-25 22:48:58] [Iter 632/2250] R2[181/600], Temp: 0.7918, Energy: -44.638564-0.001294j
[2025-08-25 22:49:24] [Iter 633/2250] R2[182/600], Temp: 0.7896, Energy: -44.650805-0.000320j
[2025-08-25 22:49:50] [Iter 634/2250] R2[183/600], Temp: 0.7875, Energy: -44.647518+0.003784j
[2025-08-25 22:50:16] [Iter 635/2250] R2[184/600], Temp: 0.7854, Energy: -44.645122+0.004282j
[2025-08-25 22:50:41] [Iter 636/2250] R2[185/600], Temp: 0.7832, Energy: -44.646984-0.002326j
[2025-08-25 22:51:07] [Iter 637/2250] R2[186/600], Temp: 0.7810, Energy: -44.639617-0.003388j
[2025-08-25 22:51:33] [Iter 638/2250] R2[187/600], Temp: 0.7789, Energy: -44.646051+0.003181j
[2025-08-25 22:51:59] [Iter 639/2250] R2[188/600], Temp: 0.7767, Energy: -44.651066+0.002437j
[2025-08-25 22:52:25] [Iter 640/2250] R2[189/600], Temp: 0.7745, Energy: -44.644516-0.000684j
[2025-08-25 22:52:51] [Iter 641/2250] R2[190/600], Temp: 0.7723, Energy: -44.647904+0.002608j
[2025-08-25 22:53:17] [Iter 642/2250] R2[191/600], Temp: 0.7701, Energy: -44.665263-0.005633j
[2025-08-25 22:53:43] [Iter 643/2250] R2[192/600], Temp: 0.7679, Energy: -44.653791-0.000838j
[2025-08-25 22:54:09] [Iter 644/2250] R2[193/600], Temp: 0.7657, Energy: -44.648178+0.004447j
[2025-08-25 22:54:35] [Iter 645/2250] R2[194/600], Temp: 0.7635, Energy: -44.656974+0.005223j
[2025-08-25 22:55:01] [Iter 646/2250] R2[195/600], Temp: 0.7612, Energy: -44.665206+0.002004j
[2025-08-25 22:55:26] [Iter 647/2250] R2[196/600], Temp: 0.7590, Energy: -44.643049-0.002044j
[2025-08-25 22:55:52] [Iter 648/2250] R2[197/600], Temp: 0.7568, Energy: -44.648335-0.000584j
[2025-08-25 22:56:18] [Iter 649/2250] R2[198/600], Temp: 0.7545, Energy: -44.652902+0.003294j
[2025-08-25 22:56:44] [Iter 650/2250] R2[199/600], Temp: 0.7523, Energy: -44.641394+0.000228j
[2025-08-25 22:57:10] [Iter 651/2250] R2[200/600], Temp: 0.7500, Energy: -44.647870+0.002097j
[2025-08-25 22:57:36] [Iter 652/2250] R2[201/600], Temp: 0.7477, Energy: -44.654752-0.000272j
[2025-08-25 22:58:02] [Iter 653/2250] R2[202/600], Temp: 0.7455, Energy: -44.639867+0.001496j
[2025-08-25 22:58:28] [Iter 654/2250] R2[203/600], Temp: 0.7432, Energy: -44.665853-0.002349j
[2025-08-25 22:58:54] [Iter 655/2250] R2[204/600], Temp: 0.7409, Energy: -44.645205-0.000304j
[2025-08-25 22:59:20] [Iter 656/2250] R2[205/600], Temp: 0.7386, Energy: -44.667386+0.007698j
[2025-08-25 22:59:46] [Iter 657/2250] R2[206/600], Temp: 0.7363, Energy: -44.659254-0.000029j
[2025-08-25 23:00:12] [Iter 658/2250] R2[207/600], Temp: 0.7340, Energy: -44.647415-0.000224j
[2025-08-25 23:00:38] [Iter 659/2250] R2[208/600], Temp: 0.7316, Energy: -44.654823-0.007003j
[2025-08-25 23:01:04] [Iter 660/2250] R2[209/600], Temp: 0.7293, Energy: -44.671348-0.003083j
[2025-08-25 23:01:29] [Iter 661/2250] R2[210/600], Temp: 0.7270, Energy: -44.655183+0.001319j
[2025-08-25 23:01:55] [Iter 662/2250] R2[211/600], Temp: 0.7247, Energy: -44.649408-0.001147j
[2025-08-25 23:02:21] [Iter 663/2250] R2[212/600], Temp: 0.7223, Energy: -44.654860+0.002348j
[2025-08-25 23:02:47] [Iter 664/2250] R2[213/600], Temp: 0.7200, Energy: -44.651763+0.005585j
[2025-08-25 23:03:13] [Iter 665/2250] R2[214/600], Temp: 0.7176, Energy: -44.665460+0.000680j
[2025-08-25 23:03:39] [Iter 666/2250] R2[215/600], Temp: 0.7153, Energy: -44.650240-0.003294j
[2025-08-25 23:04:05] [Iter 667/2250] R2[216/600], Temp: 0.7129, Energy: -44.661869-0.002200j
[2025-08-25 23:04:31] [Iter 668/2250] R2[217/600], Temp: 0.7105, Energy: -44.670053+0.000429j
[2025-08-25 23:04:57] [Iter 669/2250] R2[218/600], Temp: 0.7081, Energy: -44.667548-0.004234j
[2025-08-25 23:05:23] [Iter 670/2250] R2[219/600], Temp: 0.7058, Energy: -44.660511+0.000196j
[2025-08-25 23:05:49] [Iter 671/2250] R2[220/600], Temp: 0.7034, Energy: -44.666987+0.002237j
[2025-08-25 23:06:15] [Iter 672/2250] R2[221/600], Temp: 0.7010, Energy: -44.670902-0.005102j
[2025-08-25 23:06:40] [Iter 673/2250] R2[222/600], Temp: 0.6986, Energy: -44.671082-0.001413j
[2025-08-25 23:07:06] [Iter 674/2250] R2[223/600], Temp: 0.6962, Energy: -44.667153-0.001380j
[2025-08-25 23:07:32] [Iter 675/2250] R2[224/600], Temp: 0.6938, Energy: -44.668774+0.001868j
[2025-08-25 23:07:58] [Iter 676/2250] R2[225/600], Temp: 0.6913, Energy: -44.649908+0.005375j
[2025-08-25 23:08:24] [Iter 677/2250] R2[226/600], Temp: 0.6889, Energy: -44.671500-0.002165j
[2025-08-25 23:08:50] [Iter 678/2250] R2[227/600], Temp: 0.6865, Energy: -44.663845+0.001395j
[2025-08-25 23:09:16] [Iter 679/2250] R2[228/600], Temp: 0.6841, Energy: -44.691544-0.001964j
[2025-08-25 23:09:42] [Iter 680/2250] R2[229/600], Temp: 0.6816, Energy: -44.684611-0.005587j
[2025-08-25 23:10:08] [Iter 681/2250] R2[230/600], Temp: 0.6792, Energy: -44.666740+0.002238j
[2025-08-25 23:10:34] [Iter 682/2250] R2[231/600], Temp: 0.6767, Energy: -44.665356+0.001214j
[2025-08-25 23:11:00] [Iter 683/2250] R2[232/600], Temp: 0.6743, Energy: -44.679734-0.002028j
[2025-08-25 23:11:26] [Iter 684/2250] R2[233/600], Temp: 0.6718, Energy: -44.681596+0.004245j
[2025-08-25 23:11:52] [Iter 685/2250] R2[234/600], Temp: 0.6694, Energy: -44.667308-0.004565j
[2025-08-25 23:12:17] [Iter 686/2250] R2[235/600], Temp: 0.6669, Energy: -44.672785-0.001670j
[2025-08-25 23:12:43] [Iter 687/2250] R2[236/600], Temp: 0.6644, Energy: -44.668958+0.004199j
[2025-08-25 23:13:09] [Iter 688/2250] R2[237/600], Temp: 0.6620, Energy: -44.673824+0.001386j
[2025-08-25 23:13:35] [Iter 689/2250] R2[238/600], Temp: 0.6595, Energy: -44.668765+0.005449j
[2025-08-25 23:14:01] [Iter 690/2250] R2[239/600], Temp: 0.6570, Energy: -44.673852+0.000829j
[2025-08-25 23:14:27] [Iter 691/2250] R2[240/600], Temp: 0.6545, Energy: -44.677533+0.000072j
[2025-08-25 23:14:53] [Iter 692/2250] R2[241/600], Temp: 0.6520, Energy: -44.676911-0.005603j
[2025-08-25 23:15:19] [Iter 693/2250] R2[242/600], Temp: 0.6495, Energy: -44.661201+0.002347j
[2025-08-25 23:15:45] [Iter 694/2250] R2[243/600], Temp: 0.6470, Energy: -44.683426-0.002412j
[2025-08-25 23:16:11] [Iter 695/2250] R2[244/600], Temp: 0.6445, Energy: -44.678087+0.002007j
[2025-08-25 23:16:37] [Iter 696/2250] R2[245/600], Temp: 0.6420, Energy: -44.700947+0.004795j
[2025-08-25 23:17:02] [Iter 697/2250] R2[246/600], Temp: 0.6395, Energy: -44.673698+0.002138j
[2025-08-25 23:17:28] [Iter 698/2250] R2[247/600], Temp: 0.6370, Energy: -44.671743+0.002952j
[2025-08-25 23:17:54] [Iter 699/2250] R2[248/600], Temp: 0.6345, Energy: -44.679375-0.000142j
[2025-08-25 23:18:20] [Iter 700/2250] R2[249/600], Temp: 0.6319, Energy: -44.680976+0.003413j
[2025-08-25 23:18:46] [Iter 701/2250] R2[250/600], Temp: 0.6294, Energy: -44.676947-0.003131j
[2025-08-25 23:19:12] [Iter 702/2250] R2[251/600], Temp: 0.6269, Energy: -44.693112-0.001662j
[2025-08-25 23:19:38] [Iter 703/2250] R2[252/600], Temp: 0.6243, Energy: -44.668744-0.001051j
[2025-08-25 23:20:04] [Iter 704/2250] R2[253/600], Temp: 0.6218, Energy: -44.680959+0.001695j
[2025-08-25 23:20:30] [Iter 705/2250] R2[254/600], Temp: 0.6193, Energy: -44.681467+0.002501j
[2025-08-25 23:20:56] [Iter 706/2250] R2[255/600], Temp: 0.6167, Energy: -44.680473-0.003951j
[2025-08-25 23:21:21] [Iter 707/2250] R2[256/600], Temp: 0.6142, Energy: -44.692350+0.002371j
[2025-08-25 23:21:47] [Iter 708/2250] R2[257/600], Temp: 0.6116, Energy: -44.673129-0.000739j
[2025-08-25 23:22:13] [Iter 709/2250] R2[258/600], Temp: 0.6091, Energy: -44.691762+0.002085j
[2025-08-25 23:22:39] [Iter 710/2250] R2[259/600], Temp: 0.6065, Energy: -44.677032-0.004037j
[2025-08-25 23:23:05] [Iter 711/2250] R2[260/600], Temp: 0.6040, Energy: -44.675308+0.000209j
[2025-08-25 23:23:31] [Iter 712/2250] R2[261/600], Temp: 0.6014, Energy: -44.688354-0.004517j
[2025-08-25 23:23:57] [Iter 713/2250] R2[262/600], Temp: 0.5988, Energy: -44.680189+0.000988j
[2025-08-25 23:24:23] [Iter 714/2250] R2[263/600], Temp: 0.5963, Energy: -44.689174+0.002757j
[2025-08-25 23:24:49] [Iter 715/2250] R2[264/600], Temp: 0.5937, Energy: -44.683376+0.002255j
[2025-08-25 23:25:15] [Iter 716/2250] R2[265/600], Temp: 0.5911, Energy: -44.679152-0.001835j
[2025-08-25 23:25:41] [Iter 717/2250] R2[266/600], Temp: 0.5885, Energy: -44.693861-0.003975j
[2025-08-25 23:26:07] [Iter 718/2250] R2[267/600], Temp: 0.5860, Energy: -44.680175+0.001000j
[2025-08-25 23:26:33] [Iter 719/2250] R2[268/600], Temp: 0.5834, Energy: -44.687492+0.001550j
[2025-08-25 23:26:59] [Iter 720/2250] R2[269/600], Temp: 0.5808, Energy: -44.691864+0.001724j
[2025-08-25 23:27:25] [Iter 721/2250] R2[270/600], Temp: 0.5782, Energy: -44.689233+0.001961j
[2025-08-25 23:27:51] [Iter 722/2250] R2[271/600], Temp: 0.5756, Energy: -44.685522-0.001053j
[2025-08-25 23:28:17] [Iter 723/2250] R2[272/600], Temp: 0.5730, Energy: -44.694022+0.001269j
[2025-08-25 23:28:42] [Iter 724/2250] R2[273/600], Temp: 0.5705, Energy: -44.694243-0.003848j
[2025-08-25 23:29:08] [Iter 725/2250] R2[274/600], Temp: 0.5679, Energy: -44.693860+0.000313j
[2025-08-25 23:29:34] [Iter 726/2250] R2[275/600], Temp: 0.5653, Energy: -44.689849-0.002009j
[2025-08-25 23:30:00] [Iter 727/2250] R2[276/600], Temp: 0.5627, Energy: -44.686091-0.001042j
[2025-08-25 23:30:26] [Iter 728/2250] R2[277/600], Temp: 0.5601, Energy: -44.711202+0.001090j
[2025-08-25 23:30:52] [Iter 729/2250] R2[278/600], Temp: 0.5575, Energy: -44.686825+0.003273j
[2025-08-25 23:31:18] [Iter 730/2250] R2[279/600], Temp: 0.5549, Energy: -44.694646-0.000121j
[2025-08-25 23:31:44] [Iter 731/2250] R2[280/600], Temp: 0.5523, Energy: -44.704988+0.005000j
[2025-08-25 23:32:10] [Iter 732/2250] R2[281/600], Temp: 0.5497, Energy: -44.693052+0.008328j
[2025-08-25 23:32:36] [Iter 733/2250] R2[282/600], Temp: 0.5471, Energy: -44.699718+0.002154j
[2025-08-25 23:33:02] [Iter 734/2250] R2[283/600], Temp: 0.5444, Energy: -44.695394+0.000403j
[2025-08-25 23:33:28] [Iter 735/2250] R2[284/600], Temp: 0.5418, Energy: -44.695048-0.004345j
[2025-08-25 23:33:54] [Iter 736/2250] R2[285/600], Temp: 0.5392, Energy: -44.703269-0.000584j
[2025-08-25 23:34:19] [Iter 737/2250] R2[286/600], Temp: 0.5366, Energy: -44.692905-0.001267j
[2025-08-25 23:34:45] [Iter 738/2250] R2[287/600], Temp: 0.5340, Energy: -44.693527-0.000390j
[2025-08-25 23:35:11] [Iter 739/2250] R2[288/600], Temp: 0.5314, Energy: -44.706992+0.000627j
[2025-08-25 23:35:37] [Iter 740/2250] R2[289/600], Temp: 0.5288, Energy: -44.703871-0.004710j
[2025-08-25 23:36:03] [Iter 741/2250] R2[290/600], Temp: 0.5262, Energy: -44.693256-0.008707j
[2025-08-25 23:36:29] [Iter 742/2250] R2[291/600], Temp: 0.5236, Energy: -44.697639+0.001878j
[2025-08-25 23:36:55] [Iter 743/2250] R2[292/600], Temp: 0.5209, Energy: -44.711764-0.002806j
[2025-08-25 23:37:21] [Iter 744/2250] R2[293/600], Temp: 0.5183, Energy: -44.701764+0.001417j
[2025-08-25 23:37:47] [Iter 745/2250] R2[294/600], Temp: 0.5157, Energy: -44.705301-0.001794j
[2025-08-25 23:38:13] [Iter 746/2250] R2[295/600], Temp: 0.5131, Energy: -44.703078-0.006953j
[2025-08-25 23:38:39] [Iter 747/2250] R2[296/600], Temp: 0.5105, Energy: -44.703559-0.000079j
[2025-08-25 23:39:05] [Iter 748/2250] R2[297/600], Temp: 0.5079, Energy: -44.695901-0.000458j
[2025-08-25 23:39:31] [Iter 749/2250] R2[298/600], Temp: 0.5052, Energy: -44.691704+0.000232j
[2025-08-25 23:39:57] [Iter 750/2250] R2[299/600], Temp: 0.5026, Energy: -44.710372+0.001851j
[2025-08-25 23:39:57] ✓ Checkpoint saved: checkpoint_iter_000750.pkl
[2025-08-25 23:40:23] [Iter 751/2250] R2[300/600], Temp: 0.5000, Energy: -44.711910-0.001576j
[2025-08-25 23:40:49] [Iter 752/2250] R2[301/600], Temp: 0.4974, Energy: -44.705763+0.000913j
[2025-08-25 23:41:15] [Iter 753/2250] R2[302/600], Temp: 0.4948, Energy: -44.720038+0.005325j
[2025-08-25 23:41:41] [Iter 754/2250] R2[303/600], Temp: 0.4921, Energy: -44.689308-0.006697j
[2025-08-25 23:42:07] [Iter 755/2250] R2[304/600], Temp: 0.4895, Energy: -44.696929+0.000373j
[2025-08-25 23:42:33] [Iter 756/2250] R2[305/600], Temp: 0.4869, Energy: -44.709922+0.004234j
[2025-08-25 23:42:59] [Iter 757/2250] R2[306/600], Temp: 0.4843, Energy: -44.710059-0.000162j
[2025-08-25 23:43:25] [Iter 758/2250] R2[307/600], Temp: 0.4817, Energy: -44.707216-0.001173j
[2025-08-25 23:43:50] [Iter 759/2250] R2[308/600], Temp: 0.4791, Energy: -44.711061-0.002551j
[2025-08-25 23:44:16] [Iter 760/2250] R2[309/600], Temp: 0.4764, Energy: -44.710174-0.001364j
[2025-08-25 23:44:42] [Iter 761/2250] R2[310/600], Temp: 0.4738, Energy: -44.703457-0.000900j
[2025-08-25 23:45:08] [Iter 762/2250] R2[311/600], Temp: 0.4712, Energy: -44.707015+0.004682j
[2025-08-25 23:45:34] [Iter 763/2250] R2[312/600], Temp: 0.4686, Energy: -44.699915-0.004866j
[2025-08-25 23:46:00] [Iter 764/2250] R2[313/600], Temp: 0.4660, Energy: -44.708938-0.001062j
[2025-08-25 23:46:26] [Iter 765/2250] R2[314/600], Temp: 0.4634, Energy: -44.708272-0.000371j
[2025-08-25 23:46:52] [Iter 766/2250] R2[315/600], Temp: 0.4608, Energy: -44.726995-0.000392j
[2025-08-25 23:47:18] [Iter 767/2250] R2[316/600], Temp: 0.4582, Energy: -44.701971-0.000817j
[2025-08-25 23:47:44] [Iter 768/2250] R2[317/600], Temp: 0.4556, Energy: -44.718207-0.003698j
[2025-08-25 23:48:10] [Iter 769/2250] R2[318/600], Temp: 0.4529, Energy: -44.725035+0.003742j
[2025-08-25 23:48:36] [Iter 770/2250] R2[319/600], Temp: 0.4503, Energy: -44.705610-0.000384j
[2025-08-25 23:49:01] [Iter 771/2250] R2[320/600], Temp: 0.4477, Energy: -44.723519-0.003566j
[2025-08-25 23:49:27] [Iter 772/2250] R2[321/600], Temp: 0.4451, Energy: -44.713530+0.000660j
[2025-08-25 23:49:53] [Iter 773/2250] R2[322/600], Temp: 0.4425, Energy: -44.719271+0.001118j
[2025-08-25 23:50:19] [Iter 774/2250] R2[323/600], Temp: 0.4399, Energy: -44.723418+0.001598j
[2025-08-25 23:50:45] [Iter 775/2250] R2[324/600], Temp: 0.4373, Energy: -44.710286+0.003778j
[2025-08-25 23:51:11] [Iter 776/2250] R2[325/600], Temp: 0.4347, Energy: -44.703721+0.002470j
[2025-08-25 23:51:37] [Iter 777/2250] R2[326/600], Temp: 0.4321, Energy: -44.714679-0.001702j
[2025-08-25 23:52:03] [Iter 778/2250] R2[327/600], Temp: 0.4295, Energy: -44.716804+0.000754j
[2025-08-25 23:52:29] [Iter 779/2250] R2[328/600], Temp: 0.4270, Energy: -44.707073+0.000675j
[2025-08-25 23:52:55] [Iter 780/2250] R2[329/600], Temp: 0.4244, Energy: -44.714341-0.000478j
[2025-08-25 23:53:21] [Iter 781/2250] R2[330/600], Temp: 0.4218, Energy: -44.723523-0.000368j
[2025-08-25 23:53:46] [Iter 782/2250] R2[331/600], Temp: 0.4192, Energy: -44.715490+0.001421j
[2025-08-25 23:54:12] [Iter 783/2250] R2[332/600], Temp: 0.4166, Energy: -44.719159-0.000342j
[2025-08-25 23:54:38] [Iter 784/2250] R2[333/600], Temp: 0.4140, Energy: -44.713120-0.000450j
[2025-08-25 23:55:04] [Iter 785/2250] R2[334/600], Temp: 0.4115, Energy: -44.715608+0.001118j
[2025-08-25 23:55:30] [Iter 786/2250] R2[335/600], Temp: 0.4089, Energy: -44.723259-0.004909j
[2025-08-25 23:55:56] [Iter 787/2250] R2[336/600], Temp: 0.4063, Energy: -44.713735-0.002030j
[2025-08-25 23:56:22] [Iter 788/2250] R2[337/600], Temp: 0.4037, Energy: -44.721704-0.000802j
[2025-08-25 23:56:48] [Iter 789/2250] R2[338/600], Temp: 0.4012, Energy: -44.715331+0.000092j
[2025-08-25 23:57:14] [Iter 790/2250] R2[339/600], Temp: 0.3986, Energy: -44.722489+0.000994j
[2025-08-25 23:57:40] [Iter 791/2250] R2[340/600], Temp: 0.3960, Energy: -44.712211+0.000129j
[2025-08-25 23:58:06] [Iter 792/2250] R2[341/600], Temp: 0.3935, Energy: -44.718213+0.003412j
[2025-08-25 23:58:32] [Iter 793/2250] R2[342/600], Temp: 0.3909, Energy: -44.727555+0.001100j
[2025-08-25 23:58:57] [Iter 794/2250] R2[343/600], Temp: 0.3884, Energy: -44.727633+0.009553j
[2025-08-25 23:59:23] [Iter 795/2250] R2[344/600], Temp: 0.3858, Energy: -44.714547-0.006784j
[2025-08-25 23:59:49] [Iter 796/2250] R2[345/600], Temp: 0.3833, Energy: -44.716514+0.001429j
[2025-08-26 00:00:15] [Iter 797/2250] R2[346/600], Temp: 0.3807, Energy: -44.721051-0.003284j
[2025-08-26 00:00:41] [Iter 798/2250] R2[347/600], Temp: 0.3782, Energy: -44.725046+0.000825j
[2025-08-26 00:01:07] [Iter 799/2250] R2[348/600], Temp: 0.3757, Energy: -44.713742+0.002465j
[2025-08-26 00:01:33] [Iter 800/2250] R2[349/600], Temp: 0.3731, Energy: -44.714572-0.000295j
[2025-08-26 00:01:59] [Iter 801/2250] R2[350/600], Temp: 0.3706, Energy: -44.728532+0.001096j
[2025-08-26 00:02:25] [Iter 802/2250] R2[351/600], Temp: 0.3681, Energy: -44.712559-0.002777j
[2025-08-26 00:02:51] [Iter 803/2250] R2[352/600], Temp: 0.3655, Energy: -44.728927+0.004144j
[2025-08-26 00:03:17] [Iter 804/2250] R2[353/600], Temp: 0.3630, Energy: -44.707246-0.001765j
[2025-08-26 00:03:43] [Iter 805/2250] R2[354/600], Temp: 0.3605, Energy: -44.717323+0.001451j
[2025-08-26 00:04:09] [Iter 806/2250] R2[355/600], Temp: 0.3580, Energy: -44.718054+0.003606j
[2025-08-26 00:04:34] [Iter 807/2250] R2[356/600], Temp: 0.3555, Energy: -44.721893-0.003526j
[2025-08-26 00:05:00] [Iter 808/2250] R2[357/600], Temp: 0.3530, Energy: -44.731545-0.001170j
[2025-08-26 00:05:26] [Iter 809/2250] R2[358/600], Temp: 0.3505, Energy: -44.731050+0.000694j
[2025-08-26 00:05:52] [Iter 810/2250] R2[359/600], Temp: 0.3480, Energy: -44.727996-0.007075j
[2025-08-26 00:06:18] [Iter 811/2250] R2[360/600], Temp: 0.3455, Energy: -44.727685+0.002924j
[2025-08-26 00:06:44] [Iter 812/2250] R2[361/600], Temp: 0.3430, Energy: -44.725869+0.003662j
[2025-08-26 00:07:10] [Iter 813/2250] R2[362/600], Temp: 0.3405, Energy: -44.741365+0.000396j
[2025-08-26 00:07:36] [Iter 814/2250] R2[363/600], Temp: 0.3380, Energy: -44.729508-0.001428j
[2025-08-26 00:08:02] [Iter 815/2250] R2[364/600], Temp: 0.3356, Energy: -44.735309+0.003381j
[2025-08-26 00:08:28] [Iter 816/2250] R2[365/600], Temp: 0.3331, Energy: -44.737592-0.004341j
[2025-08-26 00:08:54] [Iter 817/2250] R2[366/600], Temp: 0.3306, Energy: -44.716791-0.001997j
[2025-08-26 00:09:19] [Iter 818/2250] R2[367/600], Temp: 0.3282, Energy: -44.733072-0.000126j
[2025-08-26 00:09:45] [Iter 819/2250] R2[368/600], Temp: 0.3257, Energy: -44.732886-0.002650j
[2025-08-26 00:10:11] [Iter 820/2250] R2[369/600], Temp: 0.3233, Energy: -44.730207+0.000112j
[2025-08-26 00:10:37] [Iter 821/2250] R2[370/600], Temp: 0.3208, Energy: -44.723166-0.003513j
[2025-08-26 00:11:03] [Iter 822/2250] R2[371/600], Temp: 0.3184, Energy: -44.724851-0.002210j
[2025-08-26 00:11:29] [Iter 823/2250] R2[372/600], Temp: 0.3159, Energy: -44.736050-0.002688j
[2025-08-26 00:11:55] [Iter 824/2250] R2[373/600], Temp: 0.3135, Energy: -44.734027-0.000744j
[2025-08-26 00:12:21] [Iter 825/2250] R2[374/600], Temp: 0.3111, Energy: -44.728049-0.004223j
[2025-08-26 00:12:47] [Iter 826/2250] R2[375/600], Temp: 0.3087, Energy: -44.733402+0.004061j
[2025-08-26 00:13:13] [Iter 827/2250] R2[376/600], Temp: 0.3062, Energy: -44.730387-0.001702j
[2025-08-26 00:13:39] [Iter 828/2250] R2[377/600], Temp: 0.3038, Energy: -44.730282-0.000324j
[2025-08-26 00:14:05] [Iter 829/2250] R2[378/600], Temp: 0.3014, Energy: -44.729236-0.000056j
[2025-08-26 00:14:30] [Iter 830/2250] R2[379/600], Temp: 0.2990, Energy: -44.721432+0.002281j
[2025-08-26 00:14:56] [Iter 831/2250] R2[380/600], Temp: 0.2966, Energy: -44.721795-0.002863j
[2025-08-26 00:15:22] [Iter 832/2250] R2[381/600], Temp: 0.2942, Energy: -44.722757+0.000684j
[2025-08-26 00:15:48] [Iter 833/2250] R2[382/600], Temp: 0.2919, Energy: -44.730153+0.002483j
[2025-08-26 00:16:14] [Iter 834/2250] R2[383/600], Temp: 0.2895, Energy: -44.737894-0.001417j
[2025-08-26 00:16:40] [Iter 835/2250] R2[384/600], Temp: 0.2871, Energy: -44.730007+0.002001j
[2025-08-26 00:17:06] [Iter 836/2250] R2[385/600], Temp: 0.2847, Energy: -44.742011-0.002542j
[2025-08-26 00:17:32] [Iter 837/2250] R2[386/600], Temp: 0.2824, Energy: -44.737247-0.001352j
[2025-08-26 00:17:58] [Iter 838/2250] R2[387/600], Temp: 0.2800, Energy: -44.736952-0.002126j
[2025-08-26 00:18:24] [Iter 839/2250] R2[388/600], Temp: 0.2777, Energy: -44.741519+0.001033j
[2025-08-26 00:18:49] [Iter 840/2250] R2[389/600], Temp: 0.2753, Energy: -44.738142+0.006100j
[2025-08-26 00:19:15] [Iter 841/2250] R2[390/600], Temp: 0.2730, Energy: -44.732137-0.004262j
[2025-08-26 00:19:41] [Iter 842/2250] R2[391/600], Temp: 0.2707, Energy: -44.737611-0.003024j
[2025-08-26 00:20:07] [Iter 843/2250] R2[392/600], Temp: 0.2684, Energy: -44.727403-0.002238j
[2025-08-26 00:20:33] [Iter 844/2250] R2[393/600], Temp: 0.2660, Energy: -44.725087+0.000651j
[2025-08-26 00:20:59] [Iter 845/2250] R2[394/600], Temp: 0.2637, Energy: -44.726255+0.001518j
[2025-08-26 00:21:25] [Iter 846/2250] R2[395/600], Temp: 0.2614, Energy: -44.729971-0.008367j
[2025-08-26 00:21:51] [Iter 847/2250] R2[396/600], Temp: 0.2591, Energy: -44.744964+0.002007j
[2025-08-26 00:22:17] [Iter 848/2250] R2[397/600], Temp: 0.2568, Energy: -44.738284-0.000438j
[2025-08-26 00:22:43] [Iter 849/2250] R2[398/600], Temp: 0.2545, Energy: -44.736646-0.003383j
[2025-08-26 00:23:08] [Iter 850/2250] R2[399/600], Temp: 0.2523, Energy: -44.744030+0.002805j
[2025-08-26 00:23:34] [Iter 851/2250] R2[400/600], Temp: 0.2500, Energy: -44.737653-0.002069j
[2025-08-26 00:24:00] [Iter 852/2250] R2[401/600], Temp: 0.2477, Energy: -44.730998+0.001289j
[2025-08-26 00:24:26] [Iter 853/2250] R2[402/600], Temp: 0.2455, Energy: -44.737932-0.000164j
[2025-08-26 00:24:52] [Iter 854/2250] R2[403/600], Temp: 0.2432, Energy: -44.744545-0.002933j
[2025-08-26 00:25:18] [Iter 855/2250] R2[404/600], Temp: 0.2410, Energy: -44.726092+0.007173j
[2025-08-26 00:25:44] [Iter 856/2250] R2[405/600], Temp: 0.2388, Energy: -44.743586-0.003175j
[2025-08-26 00:26:10] [Iter 857/2250] R2[406/600], Temp: 0.2365, Energy: -44.740087-0.005296j
[2025-08-26 00:26:36] [Iter 858/2250] R2[407/600], Temp: 0.2343, Energy: -44.733595-0.002376j
[2025-08-26 00:27:01] [Iter 859/2250] R2[408/600], Temp: 0.2321, Energy: -44.745085+0.000419j
[2025-08-26 00:27:27] [Iter 860/2250] R2[409/600], Temp: 0.2299, Energy: -44.739517-0.000652j
[2025-08-26 00:27:53] [Iter 861/2250] R2[410/600], Temp: 0.2277, Energy: -44.723905+0.002877j
[2025-08-26 00:28:19] [Iter 862/2250] R2[411/600], Temp: 0.2255, Energy: -44.733987+0.000613j
[2025-08-26 00:28:45] [Iter 863/2250] R2[412/600], Temp: 0.2233, Energy: -44.745007+0.004251j
[2025-08-26 00:29:11] [Iter 864/2250] R2[413/600], Temp: 0.2211, Energy: -44.734046-0.003127j
[2025-08-26 00:29:37] [Iter 865/2250] R2[414/600], Temp: 0.2190, Energy: -44.751027+0.000592j
[2025-08-26 00:30:03] [Iter 866/2250] R2[415/600], Temp: 0.2168, Energy: -44.742991+0.001838j
[2025-08-26 00:30:29] [Iter 867/2250] R2[416/600], Temp: 0.2146, Energy: -44.734338-0.004070j
[2025-08-26 00:30:55] [Iter 868/2250] R2[417/600], Temp: 0.2125, Energy: -44.728409-0.000290j
[2025-08-26 00:31:21] [Iter 869/2250] R2[418/600], Temp: 0.2104, Energy: -44.739207+0.000267j
[2025-08-26 00:31:47] [Iter 870/2250] R2[419/600], Temp: 0.2082, Energy: -44.736890+0.003450j
[2025-08-26 00:32:12] [Iter 871/2250] R2[420/600], Temp: 0.2061, Energy: -44.745290+0.004321j
[2025-08-26 00:32:38] [Iter 872/2250] R2[421/600], Temp: 0.2040, Energy: -44.743212-0.001767j
[2025-08-26 00:33:04] [Iter 873/2250] R2[422/600], Temp: 0.2019, Energy: -44.737955-0.000429j
[2025-08-26 00:33:30] [Iter 874/2250] R2[423/600], Temp: 0.1998, Energy: -44.739988+0.000305j
[2025-08-26 00:33:56] [Iter 875/2250] R2[424/600], Temp: 0.1977, Energy: -44.758209+0.003821j
[2025-08-26 00:34:22] [Iter 876/2250] R2[425/600], Temp: 0.1956, Energy: -44.735497+0.003703j
[2025-08-26 00:34:48] [Iter 877/2250] R2[426/600], Temp: 0.1935, Energy: -44.755389+0.002206j
[2025-08-26 00:35:14] [Iter 878/2250] R2[427/600], Temp: 0.1915, Energy: -44.741367-0.002009j
[2025-08-26 00:35:40] [Iter 879/2250] R2[428/600], Temp: 0.1894, Energy: -44.740716-0.000202j
[2025-08-26 00:36:06] [Iter 880/2250] R2[429/600], Temp: 0.1874, Energy: -44.757309+0.002068j
[2025-08-26 00:36:31] [Iter 881/2250] R2[430/600], Temp: 0.1853, Energy: -44.750094-0.001302j
[2025-08-26 00:36:57] [Iter 882/2250] R2[431/600], Temp: 0.1833, Energy: -44.749250+0.000603j
[2025-08-26 00:37:23] [Iter 883/2250] R2[432/600], Temp: 0.1813, Energy: -44.744459-0.001933j
[2025-08-26 00:37:49] [Iter 884/2250] R2[433/600], Temp: 0.1793, Energy: -44.743326+0.005381j
[2025-08-26 00:38:15] [Iter 885/2250] R2[434/600], Temp: 0.1773, Energy: -44.751124+0.002456j
[2025-08-26 00:38:41] [Iter 886/2250] R2[435/600], Temp: 0.1753, Energy: -44.749161-0.002645j
[2025-08-26 00:39:07] [Iter 887/2250] R2[436/600], Temp: 0.1733, Energy: -44.738022-0.000183j
[2025-08-26 00:39:33] [Iter 888/2250] R2[437/600], Temp: 0.1713, Energy: -44.749069-0.000906j
[2025-08-26 00:39:58] [Iter 889/2250] R2[438/600], Temp: 0.1693, Energy: -44.753183+0.001378j
[2025-08-26 00:40:24] [Iter 890/2250] R2[439/600], Temp: 0.1674, Energy: -44.751868-0.001444j
[2025-08-26 00:40:50] [Iter 891/2250] R2[440/600], Temp: 0.1654, Energy: -44.749713+0.000928j
[2025-08-26 00:41:16] [Iter 892/2250] R2[441/600], Temp: 0.1635, Energy: -44.749719+0.000384j
[2025-08-26 00:41:42] [Iter 893/2250] R2[442/600], Temp: 0.1616, Energy: -44.754147-0.002189j
[2025-08-26 00:42:08] [Iter 894/2250] R2[443/600], Temp: 0.1596, Energy: -44.750668-0.000547j
[2025-08-26 00:42:34] [Iter 895/2250] R2[444/600], Temp: 0.1577, Energy: -44.753859-0.003989j
[2025-08-26 00:43:00] [Iter 896/2250] R2[445/600], Temp: 0.1558, Energy: -44.759799-0.001970j
[2025-08-26 00:43:26] [Iter 897/2250] R2[446/600], Temp: 0.1539, Energy: -44.766983-0.002108j
[2025-08-26 00:43:52] [Iter 898/2250] R2[447/600], Temp: 0.1520, Energy: -44.756286+0.003445j
[2025-08-26 00:44:18] [Iter 899/2250] R2[448/600], Temp: 0.1502, Energy: -44.752334+0.002976j
[2025-08-26 00:44:43] [Iter 900/2250] R2[449/600], Temp: 0.1483, Energy: -44.743818+0.004796j
[2025-08-26 00:45:09] [Iter 901/2250] R2[450/600], Temp: 0.1464, Energy: -44.747447+0.000344j
[2025-08-26 00:45:35] [Iter 902/2250] R2[451/600], Temp: 0.1446, Energy: -44.747707-0.001632j
[2025-08-26 00:46:01] [Iter 903/2250] R2[452/600], Temp: 0.1428, Energy: -44.748036-0.000378j
[2025-08-26 00:46:27] [Iter 904/2250] R2[453/600], Temp: 0.1409, Energy: -44.758267-0.001561j
[2025-08-26 00:46:53] [Iter 905/2250] R2[454/600], Temp: 0.1391, Energy: -44.739013+0.005235j
[2025-08-26 00:47:19] [Iter 906/2250] R2[455/600], Temp: 0.1373, Energy: -44.751284+0.000607j
[2025-08-26 00:47:45] [Iter 907/2250] R2[456/600], Temp: 0.1355, Energy: -44.755167-0.001563j
[2025-08-26 00:48:11] [Iter 908/2250] R2[457/600], Temp: 0.1337, Energy: -44.754910-0.002277j
[2025-08-26 00:48:37] [Iter 909/2250] R2[458/600], Temp: 0.1320, Energy: -44.750867-0.003955j
[2025-08-26 00:49:02] [Iter 910/2250] R2[459/600], Temp: 0.1302, Energy: -44.748038+0.002543j
[2025-08-26 00:49:28] [Iter 911/2250] R2[460/600], Temp: 0.1284, Energy: -44.748695+0.000322j
[2025-08-26 00:49:54] [Iter 912/2250] R2[461/600], Temp: 0.1267, Energy: -44.749179-0.000897j
[2025-08-26 00:50:20] [Iter 913/2250] R2[462/600], Temp: 0.1249, Energy: -44.757569-0.003347j
[2025-08-26 00:50:46] [Iter 914/2250] R2[463/600], Temp: 0.1232, Energy: -44.759435-0.000580j
[2025-08-26 00:51:12] [Iter 915/2250] R2[464/600], Temp: 0.1215, Energy: -44.750311+0.004967j
[2025-08-26 00:51:38] [Iter 916/2250] R2[465/600], Temp: 0.1198, Energy: -44.759152-0.002409j
[2025-08-26 00:52:04] [Iter 917/2250] R2[466/600], Temp: 0.1181, Energy: -44.758724-0.002744j
[2025-08-26 00:52:30] [Iter 918/2250] R2[467/600], Temp: 0.1164, Energy: -44.756813-0.000114j
[2025-08-26 00:52:56] [Iter 919/2250] R2[468/600], Temp: 0.1147, Energy: -44.750811+0.001881j
[2025-08-26 00:53:21] [Iter 920/2250] R2[469/600], Temp: 0.1131, Energy: -44.751536+0.001167j
[2025-08-26 00:53:47] [Iter 921/2250] R2[470/600], Temp: 0.1114, Energy: -44.761999+0.000788j
[2025-08-26 00:54:13] [Iter 922/2250] R2[471/600], Temp: 0.1098, Energy: -44.764423+0.002145j
[2025-08-26 00:54:39] [Iter 923/2250] R2[472/600], Temp: 0.1082, Energy: -44.762359+0.003823j
[2025-08-26 00:55:05] [Iter 924/2250] R2[473/600], Temp: 0.1065, Energy: -44.752442+0.002046j
[2025-08-26 00:55:31] [Iter 925/2250] R2[474/600], Temp: 0.1049, Energy: -44.745178-0.001387j
[2025-08-26 00:55:57] [Iter 926/2250] R2[475/600], Temp: 0.1033, Energy: -44.759639+0.001551j
[2025-08-26 00:56:23] [Iter 927/2250] R2[476/600], Temp: 0.1017, Energy: -44.755442+0.000252j
[2025-08-26 00:56:49] [Iter 928/2250] R2[477/600], Temp: 0.1002, Energy: -44.762183+0.001620j
[2025-08-26 00:57:15] [Iter 929/2250] R2[478/600], Temp: 0.0986, Energy: -44.759891-0.004441j
[2025-08-26 00:57:40] [Iter 930/2250] R2[479/600], Temp: 0.0970, Energy: -44.765932+0.001698j
[2025-08-26 00:58:06] [Iter 931/2250] R2[480/600], Temp: 0.0955, Energy: -44.750976+0.003129j
[2025-08-26 00:58:32] [Iter 932/2250] R2[481/600], Temp: 0.0940, Energy: -44.753001-0.000126j
[2025-08-26 00:58:58] [Iter 933/2250] R2[482/600], Temp: 0.0924, Energy: -44.757797+0.000975j
[2025-08-26 00:59:24] [Iter 934/2250] R2[483/600], Temp: 0.0909, Energy: -44.754731-0.000234j
[2025-08-26 00:59:50] [Iter 935/2250] R2[484/600], Temp: 0.0894, Energy: -44.751940+0.000950j
[2025-08-26 01:00:16] [Iter 936/2250] R2[485/600], Temp: 0.0879, Energy: -44.758569+0.001903j
[2025-08-26 01:00:42] [Iter 937/2250] R2[486/600], Temp: 0.0865, Energy: -44.752701-0.001193j
[2025-08-26 01:01:08] [Iter 938/2250] R2[487/600], Temp: 0.0850, Energy: -44.767093-0.003726j
[2025-08-26 01:01:34] [Iter 939/2250] R2[488/600], Temp: 0.0835, Energy: -44.763503-0.000121j
[2025-08-26 01:01:59] [Iter 940/2250] R2[489/600], Temp: 0.0821, Energy: -44.757971-0.002348j
[2025-08-26 01:02:25] [Iter 941/2250] R2[490/600], Temp: 0.0807, Energy: -44.765506-0.005290j
[2025-08-26 01:02:51] [Iter 942/2250] R2[491/600], Temp: 0.0792, Energy: -44.763574-0.003034j
[2025-08-26 01:03:17] [Iter 943/2250] R2[492/600], Temp: 0.0778, Energy: -44.761741+0.002986j
[2025-08-26 01:03:43] [Iter 944/2250] R2[493/600], Temp: 0.0764, Energy: -44.760162+0.005016j
[2025-08-26 01:04:09] [Iter 945/2250] R2[494/600], Temp: 0.0751, Energy: -44.762706+0.002695j
[2025-08-26 01:04:35] [Iter 946/2250] R2[495/600], Temp: 0.0737, Energy: -44.757737+0.002129j
[2025-08-26 01:05:01] [Iter 947/2250] R2[496/600], Temp: 0.0723, Energy: -44.763322-0.003258j
[2025-08-26 01:05:27] [Iter 948/2250] R2[497/600], Temp: 0.0710, Energy: -44.747756-0.004722j
[2025-08-26 01:05:52] [Iter 949/2250] R2[498/600], Temp: 0.0696, Energy: -44.760481-0.001864j
[2025-08-26 01:06:18] [Iter 950/2250] R2[499/600], Temp: 0.0683, Energy: -44.752957-0.001230j
[2025-08-26 01:06:44] [Iter 951/2250] R2[500/600], Temp: 0.0670, Energy: -44.749075-0.001348j
[2025-08-26 01:07:10] [Iter 952/2250] R2[501/600], Temp: 0.0657, Energy: -44.755899-0.000727j
[2025-08-26 01:07:36] [Iter 953/2250] R2[502/600], Temp: 0.0644, Energy: -44.775205+0.002536j
[2025-08-26 01:08:02] [Iter 954/2250] R2[503/600], Temp: 0.0631, Energy: -44.759590+0.002640j
[2025-08-26 01:08:28] [Iter 955/2250] R2[504/600], Temp: 0.0618, Energy: -44.756816+0.002231j
[2025-08-26 01:08:54] [Iter 956/2250] R2[505/600], Temp: 0.0606, Energy: -44.759091+0.000398j
[2025-08-26 01:09:20] [Iter 957/2250] R2[506/600], Temp: 0.0593, Energy: -44.765705+0.002236j
[2025-08-26 01:09:46] [Iter 958/2250] R2[507/600], Temp: 0.0581, Energy: -44.764624-0.000542j
[2025-08-26 01:10:11] [Iter 959/2250] R2[508/600], Temp: 0.0569, Energy: -44.758403-0.000501j
[2025-08-26 01:10:37] [Iter 960/2250] R2[509/600], Temp: 0.0557, Energy: -44.764135-0.001342j
[2025-08-26 01:11:03] [Iter 961/2250] R2[510/600], Temp: 0.0545, Energy: -44.767305-0.004301j
[2025-08-26 01:11:29] [Iter 962/2250] R2[511/600], Temp: 0.0533, Energy: -44.763788+0.002903j
[2025-08-26 01:11:55] [Iter 963/2250] R2[512/600], Temp: 0.0521, Energy: -44.764759+0.000136j
[2025-08-26 01:12:21] [Iter 964/2250] R2[513/600], Temp: 0.0510, Energy: -44.761055-0.000030j
[2025-08-26 01:12:47] [Iter 965/2250] R2[514/600], Temp: 0.0498, Energy: -44.770411-0.000906j
[2025-08-26 01:13:13] [Iter 966/2250] R2[515/600], Temp: 0.0487, Energy: -44.762255-0.003448j
[2025-08-26 01:13:39] [Iter 967/2250] R2[516/600], Temp: 0.0476, Energy: -44.769002+0.000088j
[2025-08-26 01:14:05] [Iter 968/2250] R2[517/600], Temp: 0.0465, Energy: -44.763605-0.003099j
[2025-08-26 01:14:31] [Iter 969/2250] R2[518/600], Temp: 0.0454, Energy: -44.747853+0.000391j
[2025-08-26 01:14:56] [Iter 970/2250] R2[519/600], Temp: 0.0443, Energy: -44.753979-0.001514j
[2025-08-26 01:15:22] [Iter 971/2250] R2[520/600], Temp: 0.0432, Energy: -44.771020+0.001842j
[2025-08-26 01:15:48] [Iter 972/2250] R2[521/600], Temp: 0.0422, Energy: -44.755878+0.000620j
[2025-08-26 01:16:14] [Iter 973/2250] R2[522/600], Temp: 0.0411, Energy: -44.754645-0.000235j
[2025-08-26 01:16:40] [Iter 974/2250] R2[523/600], Temp: 0.0401, Energy: -44.761069+0.001247j
[2025-08-26 01:17:06] [Iter 975/2250] R2[524/600], Temp: 0.0391, Energy: -44.764667+0.002052j
[2025-08-26 01:17:32] [Iter 976/2250] R2[525/600], Temp: 0.0381, Energy: -44.768254+0.002054j
[2025-08-26 01:17:58] [Iter 977/2250] R2[526/600], Temp: 0.0371, Energy: -44.761088-0.001164j
[2025-08-26 01:18:24] [Iter 978/2250] R2[527/600], Temp: 0.0361, Energy: -44.771445-0.005672j
[2025-08-26 01:18:50] [Iter 979/2250] R2[528/600], Temp: 0.0351, Energy: -44.761844-0.001886j
[2025-08-26 01:19:17] [Iter 980/2250] R2[529/600], Temp: 0.0342, Energy: -44.758089-0.007156j
[2025-08-26 01:19:42] [Iter 981/2250] R2[530/600], Temp: 0.0332, Energy: -44.763279+0.002141j
[2025-08-26 01:20:08] [Iter 982/2250] R2[531/600], Temp: 0.0323, Energy: -44.763004-0.000179j
[2025-08-26 01:20:34] [Iter 983/2250] R2[532/600], Temp: 0.0314, Energy: -44.766677-0.001652j
[2025-08-26 01:21:00] [Iter 984/2250] R2[533/600], Temp: 0.0305, Energy: -44.759668-0.000082j
[2025-08-26 01:21:26] [Iter 985/2250] R2[534/600], Temp: 0.0296, Energy: -44.770031-0.001358j
[2025-08-26 01:21:52] [Iter 986/2250] R2[535/600], Temp: 0.0287, Energy: -44.772915-0.004106j
[2025-08-26 01:22:19] [Iter 987/2250] R2[536/600], Temp: 0.0278, Energy: -44.765327+0.003526j
[2025-08-26 01:22:45] [Iter 988/2250] R2[537/600], Temp: 0.0270, Energy: -44.773272-0.001364j
[2025-08-26 01:23:11] [Iter 989/2250] R2[538/600], Temp: 0.0261, Energy: -44.772804-0.004241j
[2025-08-26 01:23:37] [Iter 990/2250] R2[539/600], Temp: 0.0253, Energy: -44.758865-0.003311j
[2025-08-26 01:24:02] [Iter 991/2250] R2[540/600], Temp: 0.0245, Energy: -44.776517-0.000608j
[2025-08-26 01:24:28] [Iter 992/2250] R2[541/600], Temp: 0.0237, Energy: -44.765610+0.001978j
[2025-08-26 01:24:54] [Iter 993/2250] R2[542/600], Temp: 0.0229, Energy: -44.768406-0.001994j
[2025-08-26 01:25:20] [Iter 994/2250] R2[543/600], Temp: 0.0221, Energy: -44.765160-0.001216j
[2025-08-26 01:25:46] [Iter 995/2250] R2[544/600], Temp: 0.0213, Energy: -44.765987-0.001951j
[2025-08-26 01:26:12] [Iter 996/2250] R2[545/600], Temp: 0.0206, Energy: -44.769552+0.002410j
[2025-08-26 01:26:38] [Iter 997/2250] R2[546/600], Temp: 0.0199, Energy: -44.767541-0.003260j
[2025-08-26 01:27:04] [Iter 998/2250] R2[547/600], Temp: 0.0191, Energy: -44.768621-0.001865j
[2025-08-26 01:27:30] [Iter 999/2250] R2[548/600], Temp: 0.0184, Energy: -44.776639-0.001628j
[2025-08-26 01:27:55] [Iter 1000/2250] R2[549/600], Temp: 0.0177, Energy: -44.771352-0.001778j
[2025-08-26 01:27:56] ✓ Checkpoint saved: checkpoint_iter_001000.pkl
[2025-08-26 01:28:21] [Iter 1001/2250] R2[550/600], Temp: 0.0170, Energy: -44.764026-0.000505j
[2025-08-26 01:28:47] [Iter 1002/2250] R2[551/600], Temp: 0.0164, Energy: -44.769258-0.003508j
[2025-08-26 01:29:13] [Iter 1003/2250] R2[552/600], Temp: 0.0157, Energy: -44.776671+0.001140j
[2025-08-26 01:29:39] [Iter 1004/2250] R2[553/600], Temp: 0.0151, Energy: -44.768203+0.001436j
[2025-08-26 01:30:05] [Iter 1005/2250] R2[554/600], Temp: 0.0144, Energy: -44.773928-0.001018j
[2025-08-26 01:30:31] [Iter 1006/2250] R2[555/600], Temp: 0.0138, Energy: -44.778260-0.001969j
[2025-08-26 01:30:57] [Iter 1007/2250] R2[556/600], Temp: 0.0132, Energy: -44.768388-0.001872j
[2025-08-26 01:31:23] [Iter 1008/2250] R2[557/600], Temp: 0.0126, Energy: -44.774306+0.004722j
[2025-08-26 01:31:49] [Iter 1009/2250] R2[558/600], Temp: 0.0120, Energy: -44.768592+0.000591j
[2025-08-26 01:32:15] [Iter 1010/2250] R2[559/600], Temp: 0.0115, Energy: -44.773163-0.000036j
[2025-08-26 01:32:40] [Iter 1011/2250] R2[560/600], Temp: 0.0109, Energy: -44.780695-0.000577j
[2025-08-26 01:33:06] [Iter 1012/2250] R2[561/600], Temp: 0.0104, Energy: -44.770163-0.002101j
[2025-08-26 01:33:32] [Iter 1013/2250] R2[562/600], Temp: 0.0099, Energy: -44.775012-0.001912j
[2025-08-26 01:33:58] [Iter 1014/2250] R2[563/600], Temp: 0.0094, Energy: -44.767732-0.000798j
[2025-08-26 01:34:24] [Iter 1015/2250] R2[564/600], Temp: 0.0089, Energy: -44.768404-0.002916j
[2025-08-26 01:34:50] [Iter 1016/2250] R2[565/600], Temp: 0.0084, Energy: -44.767830-0.005621j
[2025-08-26 01:35:16] [Iter 1017/2250] R2[566/600], Temp: 0.0079, Energy: -44.781599+0.002451j
[2025-08-26 01:35:42] [Iter 1018/2250] R2[567/600], Temp: 0.0074, Energy: -44.766481-0.001512j
[2025-08-26 01:36:08] [Iter 1019/2250] R2[568/600], Temp: 0.0070, Energy: -44.777115+0.005284j
[2025-08-26 01:36:33] [Iter 1020/2250] R2[569/600], Temp: 0.0066, Energy: -44.782871+0.003335j
[2025-08-26 01:36:59] [Iter 1021/2250] R2[570/600], Temp: 0.0062, Energy: -44.758686-0.000451j
[2025-08-26 01:37:25] [Iter 1022/2250] R2[571/600], Temp: 0.0058, Energy: -44.778667+0.002422j
[2025-08-26 01:37:51] [Iter 1023/2250] R2[572/600], Temp: 0.0054, Energy: -44.773702+0.003887j
[2025-08-26 01:38:17] [Iter 1024/2250] R2[573/600], Temp: 0.0050, Energy: -44.778571-0.004680j
[2025-08-26 01:38:43] [Iter 1025/2250] R2[574/600], Temp: 0.0046, Energy: -44.765887-0.000448j
[2025-08-26 01:39:09] [Iter 1026/2250] R2[575/600], Temp: 0.0043, Energy: -44.778608+0.000856j
[2025-08-26 01:39:35] [Iter 1027/2250] R2[576/600], Temp: 0.0039, Energy: -44.770735-0.001673j
[2025-08-26 01:40:01] [Iter 1028/2250] R2[577/600], Temp: 0.0036, Energy: -44.773454-0.000794j
[2025-08-26 01:40:27] [Iter 1029/2250] R2[578/600], Temp: 0.0033, Energy: -44.762479+0.000751j
[2025-08-26 01:40:53] [Iter 1030/2250] R2[579/600], Temp: 0.0030, Energy: -44.774943+0.001881j
[2025-08-26 01:41:18] [Iter 1031/2250] R2[580/600], Temp: 0.0027, Energy: -44.764107+0.001013j
[2025-08-26 01:41:44] [Iter 1032/2250] R2[581/600], Temp: 0.0025, Energy: -44.771776-0.005979j
[2025-08-26 01:42:10] [Iter 1033/2250] R2[582/600], Temp: 0.0022, Energy: -44.780429-0.002047j
[2025-08-26 01:42:36] [Iter 1034/2250] R2[583/600], Temp: 0.0020, Energy: -44.783701-0.000931j
[2025-08-26 01:43:02] [Iter 1035/2250] R2[584/600], Temp: 0.0018, Energy: -44.775312-0.000940j
[2025-08-26 01:43:28] [Iter 1036/2250] R2[585/600], Temp: 0.0015, Energy: -44.778095+0.003653j
[2025-08-26 01:43:54] [Iter 1037/2250] R2[586/600], Temp: 0.0013, Energy: -44.769240+0.001060j
[2025-08-26 01:44:20] [Iter 1038/2250] R2[587/600], Temp: 0.0012, Energy: -44.774131+0.002904j
[2025-08-26 01:44:46] [Iter 1039/2250] R2[588/600], Temp: 0.0010, Energy: -44.766390+0.002309j
[2025-08-26 01:45:12] [Iter 1040/2250] R2[589/600], Temp: 0.0008, Energy: -44.774556+0.003112j
[2025-08-26 01:45:38] [Iter 1041/2250] R2[590/600], Temp: 0.0007, Energy: -44.785258-0.001753j
[2025-08-26 01:46:04] [Iter 1042/2250] R2[591/600], Temp: 0.0006, Energy: -44.775814+0.001269j
[2025-08-26 01:46:29] [Iter 1043/2250] R2[592/600], Temp: 0.0004, Energy: -44.777974-0.003046j
[2025-08-26 01:46:55] [Iter 1044/2250] R2[593/600], Temp: 0.0003, Energy: -44.782271+0.001423j
[2025-08-26 01:47:21] [Iter 1045/2250] R2[594/600], Temp: 0.0002, Energy: -44.774510+0.002687j
[2025-08-26 01:47:47] [Iter 1046/2250] R2[595/600], Temp: 0.0002, Energy: -44.780432-0.000082j
[2025-08-26 01:48:13] [Iter 1047/2250] R2[596/600], Temp: 0.0001, Energy: -44.779572+0.002513j
[2025-08-26 01:48:39] [Iter 1048/2250] R2[597/600], Temp: 0.0001, Energy: -44.772622+0.002279j
[2025-08-26 01:49:05] [Iter 1049/2250] R2[598/600], Temp: 0.0000, Energy: -44.767131+0.001697j
[2025-08-26 01:49:31] [Iter 1050/2250] R2[599/600], Temp: 0.0000, Energy: -44.780016+0.000016j
[2025-08-26 01:49:31] RESTART #3 | Period: 1200
[2025-08-26 01:49:57] [Iter 1051/2250] R3[0/1200], Temp: 1.0000, Energy: -44.774580+0.002690j
[2025-08-26 01:50:23] [Iter 1052/2250] R3[1/1200], Temp: 1.0000, Energy: -44.775418-0.000696j
[2025-08-26 01:50:49] [Iter 1053/2250] R3[2/1200], Temp: 1.0000, Energy: -44.775248+0.001988j
[2025-08-26 01:51:14] [Iter 1054/2250] R3[3/1200], Temp: 1.0000, Energy: -44.784830+0.003868j
[2025-08-26 01:51:40] [Iter 1055/2250] R3[4/1200], Temp: 1.0000, Energy: -44.779218+0.002148j
[2025-08-26 01:52:06] [Iter 1056/2250] R3[5/1200], Temp: 1.0000, Energy: -44.786351-0.000968j
[2025-08-26 01:52:32] [Iter 1057/2250] R3[6/1200], Temp: 0.9999, Energy: -44.781373-0.000029j
[2025-08-26 01:52:58] [Iter 1058/2250] R3[7/1200], Temp: 0.9999, Energy: -44.766375+0.000239j
[2025-08-26 01:53:24] [Iter 1059/2250] R3[8/1200], Temp: 0.9999, Energy: -44.775955+0.004929j
[2025-08-26 01:53:50] [Iter 1060/2250] R3[9/1200], Temp: 0.9999, Energy: -44.786546-0.002079j
[2025-08-26 01:54:16] [Iter 1061/2250] R3[10/1200], Temp: 0.9998, Energy: -44.770757-0.000830j
[2025-08-26 01:54:42] [Iter 1062/2250] R3[11/1200], Temp: 0.9998, Energy: -44.774966-0.001705j
[2025-08-26 01:55:08] [Iter 1063/2250] R3[12/1200], Temp: 0.9998, Energy: -44.784626+0.004723j
[2025-08-26 01:55:34] [Iter 1064/2250] R3[13/1200], Temp: 0.9997, Energy: -44.787281-0.000314j
[2025-08-26 01:55:59] [Iter 1065/2250] R3[14/1200], Temp: 0.9997, Energy: -44.781040-0.001726j
[2025-08-26 01:56:25] [Iter 1066/2250] R3[15/1200], Temp: 0.9996, Energy: -44.783825+0.000323j
[2025-08-26 01:56:51] [Iter 1067/2250] R3[16/1200], Temp: 0.9996, Energy: -44.772818+0.001848j
[2025-08-26 01:57:17] [Iter 1068/2250] R3[17/1200], Temp: 0.9995, Energy: -44.774184-0.001919j
[2025-08-26 01:57:43] [Iter 1069/2250] R3[18/1200], Temp: 0.9994, Energy: -44.776522-0.001118j
[2025-08-26 01:58:09] [Iter 1070/2250] R3[19/1200], Temp: 0.9994, Energy: -44.765692-0.001305j
[2025-08-26 01:58:35] [Iter 1071/2250] R3[20/1200], Temp: 0.9993, Energy: -44.780447+0.003327j
[2025-08-26 01:59:01] [Iter 1072/2250] R3[21/1200], Temp: 0.9992, Energy: -44.775721+0.001495j
[2025-08-26 01:59:27] [Iter 1073/2250] R3[22/1200], Temp: 0.9992, Energy: -44.772718+0.000423j
[2025-08-26 01:59:53] [Iter 1074/2250] R3[23/1200], Temp: 0.9991, Energy: -44.767209-0.001041j
[2025-08-26 02:00:19] [Iter 1075/2250] R3[24/1200], Temp: 0.9990, Energy: -44.780067+0.001054j
[2025-08-26 02:00:45] [Iter 1076/2250] R3[25/1200], Temp: 0.9989, Energy: -44.780630+0.001669j
[2025-08-26 02:01:10] [Iter 1077/2250] R3[26/1200], Temp: 0.9988, Energy: -44.785718+0.000466j
[2025-08-26 02:01:36] [Iter 1078/2250] R3[27/1200], Temp: 0.9988, Energy: -44.776364+0.001809j
[2025-08-26 02:02:02] [Iter 1079/2250] R3[28/1200], Temp: 0.9987, Energy: -44.784295+0.000134j
[2025-08-26 02:02:28] [Iter 1080/2250] R3[29/1200], Temp: 0.9986, Energy: -44.781755+0.002437j
[2025-08-26 02:02:54] [Iter 1081/2250] R3[30/1200], Temp: 0.9985, Energy: -44.775022-0.000628j
[2025-08-26 02:03:20] [Iter 1082/2250] R3[31/1200], Temp: 0.9984, Energy: -44.779294+0.005302j
[2025-08-26 02:03:46] [Iter 1083/2250] R3[32/1200], Temp: 0.9982, Energy: -44.782006+0.003036j
[2025-08-26 02:04:12] [Iter 1084/2250] R3[33/1200], Temp: 0.9981, Energy: -44.789634+0.005081j
[2025-08-26 02:04:38] [Iter 1085/2250] R3[34/1200], Temp: 0.9980, Energy: -44.774036-0.000575j
[2025-08-26 02:05:04] [Iter 1086/2250] R3[35/1200], Temp: 0.9979, Energy: -44.789699+0.001697j
[2025-08-26 02:05:30] [Iter 1087/2250] R3[36/1200], Temp: 0.9978, Energy: -44.781808-0.002251j
[2025-08-26 02:05:56] [Iter 1088/2250] R3[37/1200], Temp: 0.9977, Energy: -44.771462+0.004081j
[2025-08-26 02:06:21] [Iter 1089/2250] R3[38/1200], Temp: 0.9975, Energy: -44.794528-0.000376j
[2025-08-26 02:06:47] [Iter 1090/2250] R3[39/1200], Temp: 0.9974, Energy: -44.773556-0.001164j
[2025-08-26 02:07:13] [Iter 1091/2250] R3[40/1200], Temp: 0.9973, Energy: -44.783211+0.002503j
[2025-08-26 02:07:39] [Iter 1092/2250] R3[41/1200], Temp: 0.9971, Energy: -44.784930-0.000893j
[2025-08-26 02:08:05] [Iter 1093/2250] R3[42/1200], Temp: 0.9970, Energy: -44.789081+0.001034j
[2025-08-26 02:08:31] [Iter 1094/2250] R3[43/1200], Temp: 0.9968, Energy: -44.788048-0.000462j
[2025-08-26 02:08:57] [Iter 1095/2250] R3[44/1200], Temp: 0.9967, Energy: -44.787080+0.000883j
[2025-08-26 02:09:23] [Iter 1096/2250] R3[45/1200], Temp: 0.9965, Energy: -44.780881+0.001564j
[2025-08-26 02:09:49] [Iter 1097/2250] R3[46/1200], Temp: 0.9964, Energy: -44.787254-0.005278j
[2025-08-26 02:10:15] [Iter 1098/2250] R3[47/1200], Temp: 0.9962, Energy: -44.786368+0.001527j
[2025-08-26 02:10:41] [Iter 1099/2250] R3[48/1200], Temp: 0.9961, Energy: -44.780018-0.003138j
[2025-08-26 02:11:06] [Iter 1100/2250] R3[49/1200], Temp: 0.9959, Energy: -44.786830-0.004052j
[2025-08-26 02:11:32] [Iter 1101/2250] R3[50/1200], Temp: 0.9957, Energy: -44.785908+0.001431j
[2025-08-26 02:11:58] [Iter 1102/2250] R3[51/1200], Temp: 0.9955, Energy: -44.767841-0.000226j
[2025-08-26 02:12:24] [Iter 1103/2250] R3[52/1200], Temp: 0.9954, Energy: -44.774939+0.000235j
[2025-08-26 02:12:50] [Iter 1104/2250] R3[53/1200], Temp: 0.9952, Energy: -44.784241+0.001858j
[2025-08-26 02:13:16] [Iter 1105/2250] R3[54/1200], Temp: 0.9950, Energy: -44.789479+0.004868j
[2025-08-26 02:13:42] [Iter 1106/2250] R3[55/1200], Temp: 0.9948, Energy: -44.782292+0.000990j
[2025-08-26 02:14:08] [Iter 1107/2250] R3[56/1200], Temp: 0.9946, Energy: -44.776242+0.001454j
[2025-08-26 02:14:34] [Iter 1108/2250] R3[57/1200], Temp: 0.9944, Energy: -44.781412-0.001756j
[2025-08-26 02:15:00] [Iter 1109/2250] R3[58/1200], Temp: 0.9942, Energy: -44.781111+0.004670j
[2025-08-26 02:15:26] [Iter 1110/2250] R3[59/1200], Temp: 0.9940, Energy: -44.787005-0.002740j
[2025-08-26 02:15:51] [Iter 1111/2250] R3[60/1200], Temp: 0.9938, Energy: -44.786855+0.000079j
[2025-08-26 02:16:17] [Iter 1112/2250] R3[61/1200], Temp: 0.9936, Energy: -44.781463-0.001224j
[2025-08-26 02:16:43] [Iter 1113/2250] R3[62/1200], Temp: 0.9934, Energy: -44.789096-0.000434j
[2025-08-26 02:17:09] [Iter 1114/2250] R3[63/1200], Temp: 0.9932, Energy: -44.781566+0.003258j
[2025-08-26 02:17:35] [Iter 1115/2250] R3[64/1200], Temp: 0.9930, Energy: -44.777820-0.003191j
[2025-08-26 02:18:01] [Iter 1116/2250] R3[65/1200], Temp: 0.9928, Energy: -44.787376-0.000489j
[2025-08-26 02:18:27] [Iter 1117/2250] R3[66/1200], Temp: 0.9926, Energy: -44.789682+0.001853j
[2025-08-26 02:18:53] [Iter 1118/2250] R3[67/1200], Temp: 0.9923, Energy: -44.789276-0.000941j
[2025-08-26 02:19:19] [Iter 1119/2250] R3[68/1200], Temp: 0.9921, Energy: -44.781148-0.000787j
[2025-08-26 02:19:45] [Iter 1120/2250] R3[69/1200], Temp: 0.9919, Energy: -44.792084+0.002680j
[2025-08-26 02:20:11] [Iter 1121/2250] R3[70/1200], Temp: 0.9916, Energy: -44.788746-0.002591j
[2025-08-26 02:20:37] [Iter 1122/2250] R3[71/1200], Temp: 0.9914, Energy: -44.790629-0.001306j
[2025-08-26 02:21:03] [Iter 1123/2250] R3[72/1200], Temp: 0.9911, Energy: -44.791720-0.001504j
[2025-08-26 02:21:28] [Iter 1124/2250] R3[73/1200], Temp: 0.9909, Energy: -44.787447-0.000009j
[2025-08-26 02:21:54] [Iter 1125/2250] R3[74/1200], Temp: 0.9906, Energy: -44.785712-0.002681j
[2025-08-26 02:22:20] [Iter 1126/2250] R3[75/1200], Temp: 0.9904, Energy: -44.779043+0.003833j
[2025-08-26 02:22:46] [Iter 1127/2250] R3[76/1200], Temp: 0.9901, Energy: -44.785773-0.004491j
[2025-08-26 02:23:12] [Iter 1128/2250] R3[77/1200], Temp: 0.9899, Energy: -44.783792-0.003300j
[2025-08-26 02:23:38] [Iter 1129/2250] R3[78/1200], Temp: 0.9896, Energy: -44.781008-0.002860j
[2025-08-26 02:24:04] [Iter 1130/2250] R3[79/1200], Temp: 0.9893, Energy: -44.793714-0.000692j
[2025-08-26 02:24:30] [Iter 1131/2250] R3[80/1200], Temp: 0.9891, Energy: -44.787964+0.000704j
[2025-08-26 02:24:56] [Iter 1132/2250] R3[81/1200], Temp: 0.9888, Energy: -44.785948+0.000208j
[2025-08-26 02:25:22] [Iter 1133/2250] R3[82/1200], Temp: 0.9885, Energy: -44.787390-0.001710j
[2025-08-26 02:25:48] [Iter 1134/2250] R3[83/1200], Temp: 0.9882, Energy: -44.782000-0.002209j
[2025-08-26 02:26:14] [Iter 1135/2250] R3[84/1200], Temp: 0.9880, Energy: -44.791098+0.000355j
[2025-08-26 02:26:39] [Iter 1136/2250] R3[85/1200], Temp: 0.9877, Energy: -44.793345-0.000524j
[2025-08-26 02:27:05] [Iter 1137/2250] R3[86/1200], Temp: 0.9874, Energy: -44.790453-0.000345j
[2025-08-26 02:27:31] [Iter 1138/2250] R3[87/1200], Temp: 0.9871, Energy: -44.782349+0.003086j
[2025-08-26 02:27:57] [Iter 1139/2250] R3[88/1200], Temp: 0.9868, Energy: -44.785840+0.000708j
[2025-08-26 02:28:23] [Iter 1140/2250] R3[89/1200], Temp: 0.9865, Energy: -44.787123+0.000475j
[2025-08-26 02:28:49] [Iter 1141/2250] R3[90/1200], Temp: 0.9862, Energy: -44.783696+0.005539j
[2025-08-26 02:29:15] [Iter 1142/2250] R3[91/1200], Temp: 0.9859, Energy: -44.795705-0.000072j
[2025-08-26 02:29:41] [Iter 1143/2250] R3[92/1200], Temp: 0.9856, Energy: -44.786793+0.002343j
[2025-08-26 02:30:07] [Iter 1144/2250] R3[93/1200], Temp: 0.9853, Energy: -44.793045-0.000603j
[2025-08-26 02:30:33] [Iter 1145/2250] R3[94/1200], Temp: 0.9849, Energy: -44.787049-0.000753j
[2025-08-26 02:30:59] [Iter 1146/2250] R3[95/1200], Temp: 0.9846, Energy: -44.794165-0.002628j
[2025-08-26 02:31:25] [Iter 1147/2250] R3[96/1200], Temp: 0.9843, Energy: -44.786320-0.000583j
[2025-08-26 02:31:51] [Iter 1148/2250] R3[97/1200], Temp: 0.9840, Energy: -44.791683+0.003226j
[2025-08-26 02:32:16] [Iter 1149/2250] R3[98/1200], Temp: 0.9836, Energy: -44.796399-0.002723j
[2025-08-26 02:32:42] [Iter 1150/2250] R3[99/1200], Temp: 0.9833, Energy: -44.790444-0.001114j
[2025-08-26 02:33:08] [Iter 1151/2250] R3[100/1200], Temp: 0.9830, Energy: -44.795650+0.003036j
[2025-08-26 02:33:34] [Iter 1152/2250] R3[101/1200], Temp: 0.9826, Energy: -44.784535+0.001299j
[2025-08-26 02:34:00] [Iter 1153/2250] R3[102/1200], Temp: 0.9823, Energy: -44.791021+0.001999j
[2025-08-26 02:34:26] [Iter 1154/2250] R3[103/1200], Temp: 0.9819, Energy: -44.789782+0.002816j
[2025-08-26 02:34:52] [Iter 1155/2250] R3[104/1200], Temp: 0.9816, Energy: -44.782319-0.003545j
[2025-08-26 02:35:18] [Iter 1156/2250] R3[105/1200], Temp: 0.9812, Energy: -44.788231-0.004638j
[2025-08-26 02:35:44] [Iter 1157/2250] R3[106/1200], Temp: 0.9809, Energy: -44.782238+0.004293j
[2025-08-26 02:36:09] [Iter 1158/2250] R3[107/1200], Temp: 0.9805, Energy: -44.791854-0.002328j
[2025-08-26 02:36:35] [Iter 1159/2250] R3[108/1200], Temp: 0.9801, Energy: -44.802613+0.001694j
[2025-08-26 02:37:01] [Iter 1160/2250] R3[109/1200], Temp: 0.9798, Energy: -44.795119-0.000287j
[2025-08-26 02:37:27] [Iter 1161/2250] R3[110/1200], Temp: 0.9794, Energy: -44.789613+0.000729j
[2025-08-26 02:37:53] [Iter 1162/2250] R3[111/1200], Temp: 0.9790, Energy: -44.792786-0.000783j
[2025-08-26 02:38:19] [Iter 1163/2250] R3[112/1200], Temp: 0.9787, Energy: -44.782094-0.002071j
[2025-08-26 02:38:45] [Iter 1164/2250] R3[113/1200], Temp: 0.9783, Energy: -44.792099-0.002082j
[2025-08-26 02:39:11] [Iter 1165/2250] R3[114/1200], Temp: 0.9779, Energy: -44.787839+0.001173j
[2025-08-26 02:39:37] [Iter 1166/2250] R3[115/1200], Temp: 0.9775, Energy: -44.800206+0.001329j
[2025-08-26 02:40:03] [Iter 1167/2250] R3[116/1200], Temp: 0.9771, Energy: -44.793394-0.001569j
[2025-08-26 02:40:29] [Iter 1168/2250] R3[117/1200], Temp: 0.9767, Energy: -44.790507+0.001650j
[2025-08-26 02:40:54] [Iter 1169/2250] R3[118/1200], Temp: 0.9763, Energy: -44.788895-0.002405j
[2025-08-26 02:41:20] [Iter 1170/2250] R3[119/1200], Temp: 0.9759, Energy: -44.787930-0.001674j
[2025-08-26 02:41:46] [Iter 1171/2250] R3[120/1200], Temp: 0.9755, Energy: -44.789307+0.003264j
[2025-08-26 02:42:12] [Iter 1172/2250] R3[121/1200], Temp: 0.9751, Energy: -44.797207+0.001809j
[2025-08-26 02:42:38] [Iter 1173/2250] R3[122/1200], Temp: 0.9747, Energy: -44.785534+0.001208j
[2025-08-26 02:43:04] [Iter 1174/2250] R3[123/1200], Temp: 0.9743, Energy: -44.791820-0.003670j
[2025-08-26 02:43:30] [Iter 1175/2250] R3[124/1200], Temp: 0.9739, Energy: -44.793790+0.000350j
[2025-08-26 02:43:56] [Iter 1176/2250] R3[125/1200], Temp: 0.9735, Energy: -44.788011-0.001315j
[2025-08-26 02:44:22] [Iter 1177/2250] R3[126/1200], Temp: 0.9730, Energy: -44.797282+0.001228j
[2025-08-26 02:44:48] [Iter 1178/2250] R3[127/1200], Temp: 0.9726, Energy: -44.793332+0.000183j
[2025-08-26 02:45:14] [Iter 1179/2250] R3[128/1200], Temp: 0.9722, Energy: -44.792065-0.000263j
[2025-08-26 02:45:39] [Iter 1180/2250] R3[129/1200], Temp: 0.9718, Energy: -44.794092+0.000634j
[2025-08-26 02:46:05] [Iter 1181/2250] R3[130/1200], Temp: 0.9713, Energy: -44.794806+0.002388j
[2025-08-26 02:46:31] [Iter 1182/2250] R3[131/1200], Temp: 0.9709, Energy: -44.804028+0.003919j
[2025-08-26 02:46:57] [Iter 1183/2250] R3[132/1200], Temp: 0.9704, Energy: -44.787302+0.001222j
[2025-08-26 02:47:23] [Iter 1184/2250] R3[133/1200], Temp: 0.9700, Energy: -44.787416-0.002002j
[2025-08-26 02:47:49] [Iter 1185/2250] R3[134/1200], Temp: 0.9695, Energy: -44.795008+0.000657j
[2025-08-26 02:48:15] [Iter 1186/2250] R3[135/1200], Temp: 0.9691, Energy: -44.799596+0.002781j
[2025-08-26 02:48:41] [Iter 1187/2250] R3[136/1200], Temp: 0.9686, Energy: -44.790998+0.002139j
[2025-08-26 02:49:07] [Iter 1188/2250] R3[137/1200], Temp: 0.9682, Energy: -44.793220-0.000097j
[2025-08-26 02:49:33] [Iter 1189/2250] R3[138/1200], Temp: 0.9677, Energy: -44.788017+0.001161j
[2025-08-26 02:49:59] [Iter 1190/2250] R3[139/1200], Temp: 0.9673, Energy: -44.799409-0.002775j
[2025-08-26 02:50:25] [Iter 1191/2250] R3[140/1200], Temp: 0.9668, Energy: -44.801864+0.001419j
[2025-08-26 02:50:50] [Iter 1192/2250] R3[141/1200], Temp: 0.9663, Energy: -44.787368-0.000051j
[2025-08-26 02:51:16] [Iter 1193/2250] R3[142/1200], Temp: 0.9658, Energy: -44.797323+0.003570j
[2025-08-26 02:51:42] [Iter 1194/2250] R3[143/1200], Temp: 0.9654, Energy: -44.794598-0.002083j
[2025-08-26 02:52:08] [Iter 1195/2250] R3[144/1200], Temp: 0.9649, Energy: -44.788564+0.000633j
[2025-08-26 02:52:34] [Iter 1196/2250] R3[145/1200], Temp: 0.9644, Energy: -44.803142-0.001611j
[2025-08-26 02:53:00] [Iter 1197/2250] R3[146/1200], Temp: 0.9639, Energy: -44.795743+0.000005j
[2025-08-26 02:53:26] [Iter 1198/2250] R3[147/1200], Temp: 0.9634, Energy: -44.797486+0.001693j
[2025-08-26 02:53:52] [Iter 1199/2250] R3[148/1200], Temp: 0.9629, Energy: -44.795057+0.002174j
[2025-08-26 02:54:18] [Iter 1200/2250] R3[149/1200], Temp: 0.9624, Energy: -44.793995+0.000439j
[2025-08-26 02:54:44] [Iter 1201/2250] R3[150/1200], Temp: 0.9619, Energy: -44.791568-0.001426j
[2025-08-26 02:55:10] [Iter 1202/2250] R3[151/1200], Temp: 0.9614, Energy: -44.794372+0.001348j
[2025-08-26 02:55:35] [Iter 1203/2250] R3[152/1200], Temp: 0.9609, Energy: -44.793378+0.001881j
[2025-08-26 02:56:01] [Iter 1204/2250] R3[153/1200], Temp: 0.9604, Energy: -44.808780-0.004499j
[2025-08-26 02:56:27] [Iter 1205/2250] R3[154/1200], Temp: 0.9599, Energy: -44.792091-0.003492j
[2025-08-26 02:56:53] [Iter 1206/2250] R3[155/1200], Temp: 0.9594, Energy: -44.802687-0.000458j
[2025-08-26 02:57:19] [Iter 1207/2250] R3[156/1200], Temp: 0.9589, Energy: -44.796642-0.000223j
[2025-08-26 02:57:45] [Iter 1208/2250] R3[157/1200], Temp: 0.9584, Energy: -44.788985-0.001135j
[2025-08-26 02:58:11] [Iter 1209/2250] R3[158/1200], Temp: 0.9578, Energy: -44.794648-0.002213j
[2025-08-26 02:58:37] [Iter 1210/2250] R3[159/1200], Temp: 0.9573, Energy: -44.797712-0.001702j
[2025-08-26 02:59:03] [Iter 1211/2250] R3[160/1200], Temp: 0.9568, Energy: -44.783319-0.001411j
[2025-08-26 02:59:29] [Iter 1212/2250] R3[161/1200], Temp: 0.9562, Energy: -44.792444+0.001889j
[2025-08-26 02:59:55] [Iter 1213/2250] R3[162/1200], Temp: 0.9557, Energy: -44.792594-0.002030j
[2025-08-26 03:00:20] [Iter 1214/2250] R3[163/1200], Temp: 0.9552, Energy: -44.790181+0.002227j
[2025-08-26 03:00:46] [Iter 1215/2250] R3[164/1200], Temp: 0.9546, Energy: -44.799883+0.000217j
[2025-08-26 03:01:12] [Iter 1216/2250] R3[165/1200], Temp: 0.9541, Energy: -44.803134-0.000116j
[2025-08-26 03:01:38] [Iter 1217/2250] R3[166/1200], Temp: 0.9535, Energy: -44.792415-0.001478j
[2025-08-26 03:02:04] [Iter 1218/2250] R3[167/1200], Temp: 0.9530, Energy: -44.799291-0.000758j
[2025-08-26 03:02:30] [Iter 1219/2250] R3[168/1200], Temp: 0.9524, Energy: -44.794240+0.002537j
[2025-08-26 03:02:56] [Iter 1220/2250] R3[169/1200], Temp: 0.9519, Energy: -44.799591+0.001273j
[2025-08-26 03:03:22] [Iter 1221/2250] R3[170/1200], Temp: 0.9513, Energy: -44.791625-0.000472j
[2025-08-26 03:03:48] [Iter 1222/2250] R3[171/1200], Temp: 0.9507, Energy: -44.799134-0.001202j
[2025-08-26 03:04:14] [Iter 1223/2250] R3[172/1200], Temp: 0.9502, Energy: -44.805294+0.000089j
[2025-08-26 03:04:40] [Iter 1224/2250] R3[173/1200], Temp: 0.9496, Energy: -44.791585+0.002721j
[2025-08-26 03:05:06] [Iter 1225/2250] R3[174/1200], Temp: 0.9490, Energy: -44.787962-0.002597j
[2025-08-26 03:05:32] [Iter 1226/2250] R3[175/1200], Temp: 0.9484, Energy: -44.792160+0.001433j
[2025-08-26 03:05:57] [Iter 1227/2250] R3[176/1200], Temp: 0.9479, Energy: -44.803102+0.003034j
[2025-08-26 03:06:23] [Iter 1228/2250] R3[177/1200], Temp: 0.9473, Energy: -44.800806-0.001507j
[2025-08-26 03:06:49] [Iter 1229/2250] R3[178/1200], Temp: 0.9467, Energy: -44.796774+0.002898j
[2025-08-26 03:07:15] [Iter 1230/2250] R3[179/1200], Temp: 0.9461, Energy: -44.792326-0.000250j
[2025-08-26 03:07:41] [Iter 1231/2250] R3[180/1200], Temp: 0.9455, Energy: -44.795565+0.001966j
[2025-08-26 03:08:07] [Iter 1232/2250] R3[181/1200], Temp: 0.9449, Energy: -44.801182+0.000092j
[2025-08-26 03:08:33] [Iter 1233/2250] R3[182/1200], Temp: 0.9443, Energy: -44.797230+0.003661j
[2025-08-26 03:08:59] [Iter 1234/2250] R3[183/1200], Temp: 0.9437, Energy: -44.800672+0.001479j
[2025-08-26 03:09:25] [Iter 1235/2250] R3[184/1200], Temp: 0.9431, Energy: -44.800156+0.000553j
[2025-08-26 03:09:51] [Iter 1236/2250] R3[185/1200], Temp: 0.9425, Energy: -44.800911+0.001459j
[2025-08-26 03:10:16] [Iter 1237/2250] R3[186/1200], Temp: 0.9419, Energy: -44.818300+0.001550j
[2025-08-26 03:10:42] [Iter 1238/2250] R3[187/1200], Temp: 0.9413, Energy: -44.794270+0.002711j
[2025-08-26 03:11:08] [Iter 1239/2250] R3[188/1200], Temp: 0.9407, Energy: -44.806780+0.001873j
[2025-08-26 03:11:34] [Iter 1240/2250] R3[189/1200], Temp: 0.9400, Energy: -44.808212+0.001380j
[2025-08-26 03:12:00] [Iter 1241/2250] R3[190/1200], Temp: 0.9394, Energy: -44.800157+0.000470j
[2025-08-26 03:12:26] [Iter 1242/2250] R3[191/1200], Temp: 0.9388, Energy: -44.803277+0.002272j
[2025-08-26 03:12:52] [Iter 1243/2250] R3[192/1200], Temp: 0.9382, Energy: -44.806189+0.003068j
[2025-08-26 03:13:18] [Iter 1244/2250] R3[193/1200], Temp: 0.9375, Energy: -44.802292-0.002739j
[2025-08-26 03:13:44] [Iter 1245/2250] R3[194/1200], Temp: 0.9369, Energy: -44.796675-0.000970j
[2025-08-26 03:14:10] [Iter 1246/2250] R3[195/1200], Temp: 0.9362, Energy: -44.796805+0.001407j
[2025-08-26 03:14:36] [Iter 1247/2250] R3[196/1200], Temp: 0.9356, Energy: -44.799788-0.001389j
[2025-08-26 03:15:02] [Iter 1248/2250] R3[197/1200], Temp: 0.9350, Energy: -44.797804-0.003768j
[2025-08-26 03:15:28] [Iter 1249/2250] R3[198/1200], Temp: 0.9343, Energy: -44.804173+0.001717j
[2025-08-26 03:15:53] [Iter 1250/2250] R3[199/1200], Temp: 0.9337, Energy: -44.795708+0.000168j
[2025-08-26 03:15:54] ✓ Checkpoint saved: checkpoint_iter_001250.pkl
[2025-08-26 03:16:19] [Iter 1251/2250] R3[200/1200], Temp: 0.9330, Energy: -44.798482-0.000781j
[2025-08-26 03:16:45] [Iter 1252/2250] R3[201/1200], Temp: 0.9324, Energy: -44.798209-0.003555j
[2025-08-26 03:17:11] [Iter 1253/2250] R3[202/1200], Temp: 0.9317, Energy: -44.797756-0.003770j
[2025-08-26 03:17:37] [Iter 1254/2250] R3[203/1200], Temp: 0.9310, Energy: -44.803153-0.002616j
[2025-08-26 03:18:03] [Iter 1255/2250] R3[204/1200], Temp: 0.9304, Energy: -44.803001-0.000158j
[2025-08-26 03:18:29] [Iter 1256/2250] R3[205/1200], Temp: 0.9297, Energy: -44.801649-0.000160j
[2025-08-26 03:18:55] [Iter 1257/2250] R3[206/1200], Temp: 0.9290, Energy: -44.800710-0.000587j
[2025-08-26 03:19:21] [Iter 1258/2250] R3[207/1200], Temp: 0.9284, Energy: -44.790598+0.001226j
[2025-08-26 03:19:47] [Iter 1259/2250] R3[208/1200], Temp: 0.9277, Energy: -44.807738+0.005814j
[2025-08-26 03:20:13] [Iter 1260/2250] R3[209/1200], Temp: 0.9270, Energy: -44.785937+0.000191j
[2025-08-26 03:20:39] [Iter 1261/2250] R3[210/1200], Temp: 0.9263, Energy: -44.796663+0.000188j
[2025-08-26 03:21:05] [Iter 1262/2250] R3[211/1200], Temp: 0.9256, Energy: -44.792695+0.000546j
[2025-08-26 03:21:30] [Iter 1263/2250] R3[212/1200], Temp: 0.9249, Energy: -44.790105+0.001350j
[2025-08-26 03:21:56] [Iter 1264/2250] R3[213/1200], Temp: 0.9243, Energy: -44.810509+0.000533j
[2025-08-26 03:22:22] [Iter 1265/2250] R3[214/1200], Temp: 0.9236, Energy: -44.799582+0.001431j
[2025-08-26 03:22:48] [Iter 1266/2250] R3[215/1200], Temp: 0.9229, Energy: -44.796288-0.000807j
[2025-08-26 03:23:14] [Iter 1267/2250] R3[216/1200], Temp: 0.9222, Energy: -44.793257-0.001188j
[2025-08-26 03:23:40] [Iter 1268/2250] R3[217/1200], Temp: 0.9215, Energy: -44.801032+0.001791j
[2025-08-26 03:24:06] [Iter 1269/2250] R3[218/1200], Temp: 0.9208, Energy: -44.801327-0.002131j
[2025-08-26 03:24:32] [Iter 1270/2250] R3[219/1200], Temp: 0.9200, Energy: -44.799892+0.000702j
[2025-08-26 03:24:58] [Iter 1271/2250] R3[220/1200], Temp: 0.9193, Energy: -44.802424-0.001022j
[2025-08-26 03:25:24] [Iter 1272/2250] R3[221/1200], Temp: 0.9186, Energy: -44.803213-0.000066j
[2025-08-26 03:25:50] [Iter 1273/2250] R3[222/1200], Temp: 0.9179, Energy: -44.806176-0.002680j
[2025-08-26 03:26:16] [Iter 1274/2250] R3[223/1200], Temp: 0.9172, Energy: -44.809154-0.001490j
[2025-08-26 03:26:42] [Iter 1275/2250] R3[224/1200], Temp: 0.9165, Energy: -44.803654+0.001820j
[2025-08-26 03:27:07] [Iter 1276/2250] R3[225/1200], Temp: 0.9157, Energy: -44.809939+0.001680j
[2025-08-26 03:27:33] [Iter 1277/2250] R3[226/1200], Temp: 0.9150, Energy: -44.797910+0.003583j
[2025-08-26 03:27:59] [Iter 1278/2250] R3[227/1200], Temp: 0.9143, Energy: -44.795571+0.002481j
[2025-08-26 03:28:25] [Iter 1279/2250] R3[228/1200], Temp: 0.9135, Energy: -44.797159+0.005087j
[2025-08-26 03:28:51] [Iter 1280/2250] R3[229/1200], Temp: 0.9128, Energy: -44.805818-0.000332j
[2025-08-26 03:29:17] [Iter 1281/2250] R3[230/1200], Temp: 0.9121, Energy: -44.810458+0.002702j
[2025-08-26 03:29:43] [Iter 1282/2250] R3[231/1200], Temp: 0.9113, Energy: -44.807503-0.000827j
[2025-08-26 03:30:09] [Iter 1283/2250] R3[232/1200], Temp: 0.9106, Energy: -44.802056+0.005004j
[2025-08-26 03:30:35] [Iter 1284/2250] R3[233/1200], Temp: 0.9098, Energy: -44.794756+0.000479j
[2025-08-26 03:31:01] [Iter 1285/2250] R3[234/1200], Temp: 0.9091, Energy: -44.803740-0.004798j
[2025-08-26 03:31:27] [Iter 1286/2250] R3[235/1200], Temp: 0.9083, Energy: -44.794466+0.002250j
[2025-08-26 03:31:53] [Iter 1287/2250] R3[236/1200], Temp: 0.9076, Energy: -44.806286+0.001045j
[2025-08-26 03:32:18] [Iter 1288/2250] R3[237/1200], Temp: 0.9068, Energy: -44.807588-0.000576j
[2025-08-26 03:32:44] [Iter 1289/2250] R3[238/1200], Temp: 0.9060, Energy: -44.803065+0.001728j
[2025-08-26 03:33:10] [Iter 1290/2250] R3[239/1200], Temp: 0.9053, Energy: -44.788546-0.000898j
[2025-08-26 03:33:36] [Iter 1291/2250] R3[240/1200], Temp: 0.9045, Energy: -44.797422-0.001195j
[2025-08-26 03:34:02] [Iter 1292/2250] R3[241/1200], Temp: 0.9037, Energy: -44.805600+0.000689j
[2025-08-26 03:34:28] [Iter 1293/2250] R3[242/1200], Temp: 0.9030, Energy: -44.808065-0.003819j
[2025-08-26 03:34:54] [Iter 1294/2250] R3[243/1200], Temp: 0.9022, Energy: -44.799767-0.000113j
[2025-08-26 03:35:20] [Iter 1295/2250] R3[244/1200], Temp: 0.9014, Energy: -44.807415-0.005094j
[2025-08-26 03:35:46] [Iter 1296/2250] R3[245/1200], Temp: 0.9006, Energy: -44.795003+0.000567j
[2025-08-26 03:36:12] [Iter 1297/2250] R3[246/1200], Temp: 0.8998, Energy: -44.798697+0.002569j
[2025-08-26 03:36:38] [Iter 1298/2250] R3[247/1200], Temp: 0.8991, Energy: -44.805517-0.001312j
[2025-08-26 03:37:04] [Iter 1299/2250] R3[248/1200], Temp: 0.8983, Energy: -44.793016-0.000402j
[2025-08-26 03:37:29] [Iter 1300/2250] R3[249/1200], Temp: 0.8975, Energy: -44.795036-0.002289j
[2025-08-26 03:37:55] [Iter 1301/2250] R3[250/1200], Temp: 0.8967, Energy: -44.795944+0.001211j
[2025-08-26 03:38:21] [Iter 1302/2250] R3[251/1200], Temp: 0.8959, Energy: -44.807944+0.002450j
[2025-08-26 03:38:47] [Iter 1303/2250] R3[252/1200], Temp: 0.8951, Energy: -44.805329-0.001118j
[2025-08-26 03:39:13] [Iter 1304/2250] R3[253/1200], Temp: 0.8943, Energy: -44.807551+0.001299j
[2025-08-26 03:39:39] [Iter 1305/2250] R3[254/1200], Temp: 0.8935, Energy: -44.806568+0.003816j
[2025-08-26 03:40:05] [Iter 1306/2250] R3[255/1200], Temp: 0.8927, Energy: -44.810910-0.000326j
[2025-08-26 03:40:31] [Iter 1307/2250] R3[256/1200], Temp: 0.8918, Energy: -44.797822-0.003548j
[2025-08-26 03:40:57] [Iter 1308/2250] R3[257/1200], Temp: 0.8910, Energy: -44.804328-0.002034j
[2025-08-26 03:41:23] [Iter 1309/2250] R3[258/1200], Temp: 0.8902, Energy: -44.803126+0.000057j
[2025-08-26 03:41:49] [Iter 1310/2250] R3[259/1200], Temp: 0.8894, Energy: -44.800220+0.002459j
[2025-08-26 03:42:15] [Iter 1311/2250] R3[260/1200], Temp: 0.8886, Energy: -44.804109-0.000492j
[2025-08-26 03:42:40] [Iter 1312/2250] R3[261/1200], Temp: 0.8877, Energy: -44.806869-0.003639j
[2025-08-26 03:43:06] [Iter 1313/2250] R3[262/1200], Temp: 0.8869, Energy: -44.792267+0.000068j
[2025-08-26 03:43:32] [Iter 1314/2250] R3[263/1200], Temp: 0.8861, Energy: -44.811641+0.001391j
[2025-08-26 03:43:58] [Iter 1315/2250] R3[264/1200], Temp: 0.8853, Energy: -44.798124+0.001377j
[2025-08-26 03:44:24] [Iter 1316/2250] R3[265/1200], Temp: 0.8844, Energy: -44.808237+0.000387j
[2025-08-26 03:44:50] [Iter 1317/2250] R3[266/1200], Temp: 0.8836, Energy: -44.805982+0.001456j
[2025-08-26 03:45:16] [Iter 1318/2250] R3[267/1200], Temp: 0.8827, Energy: -44.809433-0.001672j
[2025-08-26 03:45:42] [Iter 1319/2250] R3[268/1200], Temp: 0.8819, Energy: -44.809220+0.001956j
[2025-08-26 03:46:08] [Iter 1320/2250] R3[269/1200], Temp: 0.8811, Energy: -44.798340-0.002134j
[2025-08-26 03:46:34] [Iter 1321/2250] R3[270/1200], Temp: 0.8802, Energy: -44.806924+0.000418j
[2025-08-26 03:47:00] [Iter 1322/2250] R3[271/1200], Temp: 0.8794, Energy: -44.813451-0.001234j
[2025-08-26 03:47:25] [Iter 1323/2250] R3[272/1200], Temp: 0.8785, Energy: -44.799128+0.001804j
[2025-08-26 03:47:51] [Iter 1324/2250] R3[273/1200], Temp: 0.8776, Energy: -44.792763-0.001357j
[2025-08-26 03:48:17] [Iter 1325/2250] R3[274/1200], Temp: 0.8768, Energy: -44.807316-0.000596j
[2025-08-26 03:48:43] [Iter 1326/2250] R3[275/1200], Temp: 0.8759, Energy: -44.793786+0.003286j
[2025-08-26 03:49:09] [Iter 1327/2250] R3[276/1200], Temp: 0.8751, Energy: -44.799927+0.001104j
[2025-08-26 03:49:35] [Iter 1328/2250] R3[277/1200], Temp: 0.8742, Energy: -44.793793+0.000938j
[2025-08-26 03:50:01] [Iter 1329/2250] R3[278/1200], Temp: 0.8733, Energy: -44.809608-0.001176j
[2025-08-26 03:50:27] [Iter 1330/2250] R3[279/1200], Temp: 0.8724, Energy: -44.801981+0.000365j
[2025-08-26 03:50:53] [Iter 1331/2250] R3[280/1200], Temp: 0.8716, Energy: -44.809125-0.003004j
[2025-08-26 03:51:19] [Iter 1332/2250] R3[281/1200], Temp: 0.8707, Energy: -44.807558+0.000002j
[2025-08-26 03:51:45] [Iter 1333/2250] R3[282/1200], Temp: 0.8698, Energy: -44.804952-0.001956j
[2025-08-26 03:52:11] [Iter 1334/2250] R3[283/1200], Temp: 0.8689, Energy: -44.804138+0.001271j
[2025-08-26 03:52:36] [Iter 1335/2250] R3[284/1200], Temp: 0.8680, Energy: -44.802221+0.000001j
[2025-08-26 03:53:02] [Iter 1336/2250] R3[285/1200], Temp: 0.8672, Energy: -44.798989+0.000848j
[2025-08-26 03:53:28] [Iter 1337/2250] R3[286/1200], Temp: 0.8663, Energy: -44.808629+0.003723j
[2025-08-26 03:53:54] [Iter 1338/2250] R3[287/1200], Temp: 0.8654, Energy: -44.805260+0.002771j
[2025-08-26 03:54:20] [Iter 1339/2250] R3[288/1200], Temp: 0.8645, Energy: -44.806524-0.004073j
[2025-08-26 03:54:46] [Iter 1340/2250] R3[289/1200], Temp: 0.8636, Energy: -44.813881-0.000325j
[2025-08-26 03:55:12] [Iter 1341/2250] R3[290/1200], Temp: 0.8627, Energy: -44.814390-0.000851j
[2025-08-26 03:55:38] [Iter 1342/2250] R3[291/1200], Temp: 0.8618, Energy: -44.808868+0.002521j
[2025-08-26 03:56:04] [Iter 1343/2250] R3[292/1200], Temp: 0.8609, Energy: -44.802664-0.000490j
[2025-08-26 03:56:30] [Iter 1344/2250] R3[293/1200], Temp: 0.8600, Energy: -44.809789-0.002853j
[2025-08-26 03:56:56] [Iter 1345/2250] R3[294/1200], Temp: 0.8591, Energy: -44.802395-0.000959j
[2025-08-26 03:57:22] [Iter 1346/2250] R3[295/1200], Temp: 0.8582, Energy: -44.812005-0.001204j
[2025-08-26 03:57:48] [Iter 1347/2250] R3[296/1200], Temp: 0.8572, Energy: -44.803734+0.002760j
[2025-08-26 03:58:13] [Iter 1348/2250] R3[297/1200], Temp: 0.8563, Energy: -44.805631-0.002092j
[2025-08-26 03:58:39] [Iter 1349/2250] R3[298/1200], Temp: 0.8554, Energy: -44.803694-0.001433j
[2025-08-26 03:59:05] [Iter 1350/2250] R3[299/1200], Temp: 0.8545, Energy: -44.808971-0.001128j
[2025-08-26 03:59:31] [Iter 1351/2250] R3[300/1200], Temp: 0.8536, Energy: -44.806982-0.001905j
[2025-08-26 03:59:57] [Iter 1352/2250] R3[301/1200], Temp: 0.8526, Energy: -44.804656+0.001213j
[2025-08-26 04:00:23] [Iter 1353/2250] R3[302/1200], Temp: 0.8517, Energy: -44.801958-0.001143j
[2025-08-26 04:00:49] [Iter 1354/2250] R3[303/1200], Temp: 0.8508, Energy: -44.808613-0.004045j
[2025-08-26 04:01:15] [Iter 1355/2250] R3[304/1200], Temp: 0.8498, Energy: -44.805557-0.002551j
[2025-08-26 04:01:41] [Iter 1356/2250] R3[305/1200], Temp: 0.8489, Energy: -44.806692-0.001902j
[2025-08-26 04:02:07] [Iter 1357/2250] R3[306/1200], Temp: 0.8480, Energy: -44.818027-0.001071j
[2025-08-26 04:02:33] [Iter 1358/2250] R3[307/1200], Temp: 0.8470, Energy: -44.805380-0.003260j
[2025-08-26 04:02:59] [Iter 1359/2250] R3[308/1200], Temp: 0.8461, Energy: -44.816860+0.002383j
[2025-08-26 04:03:25] [Iter 1360/2250] R3[309/1200], Temp: 0.8451, Energy: -44.807460-0.001231j
[2025-08-26 04:03:50] [Iter 1361/2250] R3[310/1200], Temp: 0.8442, Energy: -44.822983+0.001333j
[2025-08-26 04:04:16] [Iter 1362/2250] R3[311/1200], Temp: 0.8432, Energy: -44.815502-0.002265j
[2025-08-26 04:04:42] [Iter 1363/2250] R3[312/1200], Temp: 0.8423, Energy: -44.808709-0.000605j
[2025-08-26 04:05:08] [Iter 1364/2250] R3[313/1200], Temp: 0.8413, Energy: -44.800788-0.001164j
[2025-08-26 04:05:34] [Iter 1365/2250] R3[314/1200], Temp: 0.8404, Energy: -44.813948-0.000645j
[2025-08-26 04:06:00] [Iter 1366/2250] R3[315/1200], Temp: 0.8394, Energy: -44.798483+0.002292j
[2025-08-26 04:06:26] [Iter 1367/2250] R3[316/1200], Temp: 0.8384, Energy: -44.805336-0.001580j
[2025-08-26 04:06:52] [Iter 1368/2250] R3[317/1200], Temp: 0.8375, Energy: -44.796917-0.002426j
[2025-08-26 04:07:18] [Iter 1369/2250] R3[318/1200], Temp: 0.8365, Energy: -44.800501-0.000234j
[2025-08-26 04:07:44] [Iter 1370/2250] R3[319/1200], Temp: 0.8355, Energy: -44.814415-0.003029j
[2025-08-26 04:08:09] [Iter 1371/2250] R3[320/1200], Temp: 0.8346, Energy: -44.810163+0.002141j
[2025-08-26 04:08:35] [Iter 1372/2250] R3[321/1200], Temp: 0.8336, Energy: -44.805861-0.002192j
[2025-08-26 04:09:01] [Iter 1373/2250] R3[322/1200], Temp: 0.8326, Energy: -44.811236-0.000066j
[2025-08-26 04:09:27] [Iter 1374/2250] R3[323/1200], Temp: 0.8316, Energy: -44.818178+0.000433j
[2025-08-26 04:09:53] [Iter 1375/2250] R3[324/1200], Temp: 0.8307, Energy: -44.814483+0.001193j
[2025-08-26 04:10:19] [Iter 1376/2250] R3[325/1200], Temp: 0.8297, Energy: -44.811551-0.003115j
[2025-08-26 04:10:45] [Iter 1377/2250] R3[326/1200], Temp: 0.8287, Energy: -44.809218+0.001327j
[2025-08-26 04:11:11] [Iter 1378/2250] R3[327/1200], Temp: 0.8277, Energy: -44.810344-0.000270j
[2025-08-26 04:11:37] [Iter 1379/2250] R3[328/1200], Temp: 0.8267, Energy: -44.811942+0.002446j
[2025-08-26 04:12:03] [Iter 1380/2250] R3[329/1200], Temp: 0.8257, Energy: -44.802881+0.001244j
[2025-08-26 04:12:29] [Iter 1381/2250] R3[330/1200], Temp: 0.8247, Energy: -44.803556+0.003548j
[2025-08-26 04:12:54] [Iter 1382/2250] R3[331/1200], Temp: 0.8237, Energy: -44.820561+0.002340j
[2025-08-26 04:13:20] [Iter 1383/2250] R3[332/1200], Temp: 0.8227, Energy: -44.815587+0.000090j
[2025-08-26 04:13:46] [Iter 1384/2250] R3[333/1200], Temp: 0.8217, Energy: -44.807805+0.001379j
[2025-08-26 04:14:12] [Iter 1385/2250] R3[334/1200], Temp: 0.8207, Energy: -44.804207+0.000338j
[2025-08-26 04:14:38] [Iter 1386/2250] R3[335/1200], Temp: 0.8197, Energy: -44.806468-0.000629j
[2025-08-26 04:15:04] [Iter 1387/2250] R3[336/1200], Temp: 0.8187, Energy: -44.813548-0.000374j
[2025-08-26 04:15:30] [Iter 1388/2250] R3[337/1200], Temp: 0.8177, Energy: -44.808408-0.000113j
[2025-08-26 04:15:56] [Iter 1389/2250] R3[338/1200], Temp: 0.8167, Energy: -44.805118-0.002355j
[2025-08-26 04:16:22] [Iter 1390/2250] R3[339/1200], Temp: 0.8157, Energy: -44.809766+0.006109j
[2025-08-26 04:16:48] [Iter 1391/2250] R3[340/1200], Temp: 0.8147, Energy: -44.812897+0.001784j
[2025-08-26 04:17:14] [Iter 1392/2250] R3[341/1200], Temp: 0.8136, Energy: -44.805567+0.000243j
[2025-08-26 04:17:39] [Iter 1393/2250] R3[342/1200], Temp: 0.8126, Energy: -44.803118-0.003290j
[2025-08-26 04:18:05] [Iter 1394/2250] R3[343/1200], Temp: 0.8116, Energy: -44.806512-0.001585j
[2025-08-26 04:18:31] [Iter 1395/2250] R3[344/1200], Temp: 0.8106, Energy: -44.800446-0.000004j
[2025-08-26 04:18:57] [Iter 1396/2250] R3[345/1200], Temp: 0.8095, Energy: -44.803682-0.006359j
[2025-08-26 04:19:23] [Iter 1397/2250] R3[346/1200], Temp: 0.8085, Energy: -44.815330-0.000605j
[2025-08-26 04:19:49] [Iter 1398/2250] R3[347/1200], Temp: 0.8075, Energy: -44.803434+0.002285j
[2025-08-26 04:20:15] [Iter 1399/2250] R3[348/1200], Temp: 0.8065, Energy: -44.798212+0.000670j
[2025-08-26 04:20:41] [Iter 1400/2250] R3[349/1200], Temp: 0.8054, Energy: -44.811494-0.000169j
[2025-08-26 04:21:07] [Iter 1401/2250] R3[350/1200], Temp: 0.8044, Energy: -44.805750-0.003476j
[2025-08-26 04:21:33] [Iter 1402/2250] R3[351/1200], Temp: 0.8033, Energy: -44.811420-0.000117j
[2025-08-26 04:21:58] [Iter 1403/2250] R3[352/1200], Temp: 0.8023, Energy: -44.802987-0.002896j
[2025-08-26 04:22:24] [Iter 1404/2250] R3[353/1200], Temp: 0.8013, Energy: -44.806296+0.000382j
[2025-08-26 04:22:50] [Iter 1405/2250] R3[354/1200], Temp: 0.8002, Energy: -44.818295-0.001261j
[2025-08-26 04:23:16] [Iter 1406/2250] R3[355/1200], Temp: 0.7992, Energy: -44.817928+0.001848j
[2025-08-26 04:23:42] [Iter 1407/2250] R3[356/1200], Temp: 0.7981, Energy: -44.818737-0.000112j
[2025-08-26 04:24:08] [Iter 1408/2250] R3[357/1200], Temp: 0.7971, Energy: -44.816298+0.000812j
[2025-08-26 04:24:34] [Iter 1409/2250] R3[358/1200], Temp: 0.7960, Energy: -44.811080+0.000647j
[2025-08-26 04:25:00] [Iter 1410/2250] R3[359/1200], Temp: 0.7950, Energy: -44.808838+0.000254j
[2025-08-26 04:25:26] [Iter 1411/2250] R3[360/1200], Temp: 0.7939, Energy: -44.806424-0.002553j
[2025-08-26 04:25:52] [Iter 1412/2250] R3[361/1200], Temp: 0.7928, Energy: -44.818252+0.001654j
[2025-08-26 04:26:18] [Iter 1413/2250] R3[362/1200], Temp: 0.7918, Energy: -44.811872+0.001493j
[2025-08-26 04:26:43] [Iter 1414/2250] R3[363/1200], Temp: 0.7907, Energy: -44.807575-0.001094j
[2025-08-26 04:27:09] [Iter 1415/2250] R3[364/1200], Temp: 0.7896, Energy: -44.813790-0.003602j
[2025-08-26 04:27:35] [Iter 1416/2250] R3[365/1200], Temp: 0.7886, Energy: -44.821981+0.000595j
[2025-08-26 04:28:01] [Iter 1417/2250] R3[366/1200], Temp: 0.7875, Energy: -44.814298+0.001991j
[2025-08-26 04:28:27] [Iter 1418/2250] R3[367/1200], Temp: 0.7864, Energy: -44.815094-0.002180j
[2025-08-26 04:28:53] [Iter 1419/2250] R3[368/1200], Temp: 0.7854, Energy: -44.808720-0.002276j
[2025-08-26 04:29:19] [Iter 1420/2250] R3[369/1200], Temp: 0.7843, Energy: -44.818509-0.001283j
[2025-08-26 04:29:45] [Iter 1421/2250] R3[370/1200], Temp: 0.7832, Energy: -44.807390-0.001851j
[2025-08-26 04:30:11] [Iter 1422/2250] R3[371/1200], Temp: 0.7821, Energy: -44.823896+0.000435j
[2025-08-26 04:30:37] [Iter 1423/2250] R3[372/1200], Temp: 0.7810, Energy: -44.818530+0.000062j
[2025-08-26 04:31:03] [Iter 1424/2250] R3[373/1200], Temp: 0.7800, Energy: -44.815982+0.003215j
[2025-08-26 04:31:29] [Iter 1425/2250] R3[374/1200], Temp: 0.7789, Energy: -44.810791+0.003364j
[2025-08-26 04:31:55] [Iter 1426/2250] R3[375/1200], Temp: 0.7778, Energy: -44.808050-0.000390j
[2025-08-26 04:32:20] [Iter 1427/2250] R3[376/1200], Temp: 0.7767, Energy: -44.812842+0.002474j
[2025-08-26 04:32:46] [Iter 1428/2250] R3[377/1200], Temp: 0.7756, Energy: -44.810859+0.001110j
[2025-08-26 04:33:12] [Iter 1429/2250] R3[378/1200], Temp: 0.7745, Energy: -44.818914-0.000092j
[2025-08-26 04:33:38] [Iter 1430/2250] R3[379/1200], Temp: 0.7734, Energy: -44.815611+0.001122j
[2025-08-26 04:34:04] [Iter 1431/2250] R3[380/1200], Temp: 0.7723, Energy: -44.814231-0.000618j
[2025-08-26 04:34:30] [Iter 1432/2250] R3[381/1200], Temp: 0.7712, Energy: -44.802178+0.002674j
[2025-08-26 04:34:56] [Iter 1433/2250] R3[382/1200], Temp: 0.7701, Energy: -44.809394-0.000256j
[2025-08-26 04:35:22] [Iter 1434/2250] R3[383/1200], Temp: 0.7690, Energy: -44.808525+0.001492j
[2025-08-26 04:35:48] [Iter 1435/2250] R3[384/1200], Temp: 0.7679, Energy: -44.810072-0.000525j
[2025-08-26 04:36:14] [Iter 1436/2250] R3[385/1200], Temp: 0.7668, Energy: -44.819230+0.001442j
[2025-08-26 04:36:40] [Iter 1437/2250] R3[386/1200], Temp: 0.7657, Energy: -44.812018+0.000358j
[2025-08-26 04:37:06] [Iter 1438/2250] R3[387/1200], Temp: 0.7646, Energy: -44.804935-0.000571j
[2025-08-26 04:37:31] [Iter 1439/2250] R3[388/1200], Temp: 0.7635, Energy: -44.817581-0.003407j
[2025-08-26 04:37:57] [Iter 1440/2250] R3[389/1200], Temp: 0.7624, Energy: -44.812191+0.000553j
[2025-08-26 04:38:23] [Iter 1441/2250] R3[390/1200], Temp: 0.7612, Energy: -44.817015+0.001038j
[2025-08-26 04:38:49] [Iter 1442/2250] R3[391/1200], Temp: 0.7601, Energy: -44.815190+0.000358j
[2025-08-26 04:39:15] [Iter 1443/2250] R3[392/1200], Temp: 0.7590, Energy: -44.810536-0.002639j
[2025-08-26 04:39:41] [Iter 1444/2250] R3[393/1200], Temp: 0.7579, Energy: -44.809613+0.001962j
[2025-08-26 04:40:07] [Iter 1445/2250] R3[394/1200], Temp: 0.7568, Energy: -44.812828-0.000265j
[2025-08-26 04:40:33] [Iter 1446/2250] R3[395/1200], Temp: 0.7556, Energy: -44.811526+0.001292j
[2025-08-26 04:40:59] [Iter 1447/2250] R3[396/1200], Temp: 0.7545, Energy: -44.822061+0.002765j
[2025-08-26 04:41:25] [Iter 1448/2250] R3[397/1200], Temp: 0.7534, Energy: -44.799487-0.000769j
[2025-08-26 04:41:51] [Iter 1449/2250] R3[398/1200], Temp: 0.7523, Energy: -44.812848-0.001853j
[2025-08-26 04:42:17] [Iter 1450/2250] R3[399/1200], Temp: 0.7511, Energy: -44.814491+0.000560j
[2025-08-26 04:42:42] [Iter 1451/2250] R3[400/1200], Temp: 0.7500, Energy: -44.823162+0.002016j
[2025-08-26 04:43:08] [Iter 1452/2250] R3[401/1200], Temp: 0.7489, Energy: -44.819781+0.001384j
[2025-08-26 04:43:34] [Iter 1453/2250] R3[402/1200], Temp: 0.7477, Energy: -44.811624+0.001199j
[2025-08-26 04:44:00] [Iter 1454/2250] R3[403/1200], Temp: 0.7466, Energy: -44.822324-0.000255j
[2025-08-26 04:44:26] [Iter 1455/2250] R3[404/1200], Temp: 0.7455, Energy: -44.821836-0.001594j
[2025-08-26 04:44:52] [Iter 1456/2250] R3[405/1200], Temp: 0.7443, Energy: -44.812198-0.003506j
[2025-08-26 04:45:18] [Iter 1457/2250] R3[406/1200], Temp: 0.7432, Energy: -44.817769+0.000443j
[2025-08-26 04:45:44] [Iter 1458/2250] R3[407/1200], Temp: 0.7420, Energy: -44.811767-0.000150j
[2025-08-26 04:46:10] [Iter 1459/2250] R3[408/1200], Temp: 0.7409, Energy: -44.812867-0.002613j
[2025-08-26 04:46:36] [Iter 1460/2250] R3[409/1200], Temp: 0.7397, Energy: -44.815217-0.002254j
[2025-08-26 04:47:02] [Iter 1461/2250] R3[410/1200], Temp: 0.7386, Energy: -44.813493+0.002475j
[2025-08-26 04:47:28] [Iter 1462/2250] R3[411/1200], Temp: 0.7374, Energy: -44.807604-0.000129j
[2025-08-26 04:47:54] [Iter 1463/2250] R3[412/1200], Temp: 0.7363, Energy: -44.811578+0.004531j
[2025-08-26 04:48:20] [Iter 1464/2250] R3[413/1200], Temp: 0.7351, Energy: -44.811924+0.002598j
[2025-08-26 04:48:45] [Iter 1465/2250] R3[414/1200], Temp: 0.7340, Energy: -44.825541-0.000846j
[2025-08-26 04:49:11] [Iter 1466/2250] R3[415/1200], Temp: 0.7328, Energy: -44.819882-0.001015j
[2025-08-26 04:49:37] [Iter 1467/2250] R3[416/1200], Temp: 0.7316, Energy: -44.817226-0.001810j
[2025-08-26 04:50:03] [Iter 1468/2250] R3[417/1200], Temp: 0.7305, Energy: -44.806527-0.000334j
[2025-08-26 04:50:29] [Iter 1469/2250] R3[418/1200], Temp: 0.7293, Energy: -44.806381-0.000783j
[2025-08-26 04:50:55] [Iter 1470/2250] R3[419/1200], Temp: 0.7282, Energy: -44.822897+0.003271j
[2025-08-26 04:51:21] [Iter 1471/2250] R3[420/1200], Temp: 0.7270, Energy: -44.816729-0.000700j
[2025-08-26 04:51:47] [Iter 1472/2250] R3[421/1200], Temp: 0.7258, Energy: -44.815673-0.001492j
[2025-08-26 04:52:13] [Iter 1473/2250] R3[422/1200], Temp: 0.7247, Energy: -44.805740-0.000175j
[2025-08-26 04:52:39] [Iter 1474/2250] R3[423/1200], Temp: 0.7235, Energy: -44.813274+0.002598j
[2025-08-26 04:53:05] [Iter 1475/2250] R3[424/1200], Temp: 0.7223, Energy: -44.822594+0.002084j
[2025-08-26 04:53:31] [Iter 1476/2250] R3[425/1200], Temp: 0.7211, Energy: -44.813902-0.000232j
[2025-08-26 04:53:57] [Iter 1477/2250] R3[426/1200], Temp: 0.7200, Energy: -44.815464-0.001566j
[2025-08-26 04:54:23] [Iter 1478/2250] R3[427/1200], Temp: 0.7188, Energy: -44.814814+0.002467j
[2025-08-26 04:54:49] [Iter 1479/2250] R3[428/1200], Temp: 0.7176, Energy: -44.810797+0.000410j
[2025-08-26 04:55:15] [Iter 1480/2250] R3[429/1200], Temp: 0.7164, Energy: -44.829284+0.001871j
[2025-08-26 04:55:41] [Iter 1481/2250] R3[430/1200], Temp: 0.7153, Energy: -44.814409+0.000863j
[2025-08-26 04:56:07] [Iter 1482/2250] R3[431/1200], Temp: 0.7141, Energy: -44.807972-0.000628j
[2025-08-26 04:56:33] [Iter 1483/2250] R3[432/1200], Temp: 0.7129, Energy: -44.810207+0.002438j
[2025-08-26 04:56:58] [Iter 1484/2250] R3[433/1200], Temp: 0.7117, Energy: -44.815455+0.000479j
[2025-08-26 04:57:24] [Iter 1485/2250] R3[434/1200], Temp: 0.7105, Energy: -44.816426-0.001595j
[2025-08-26 04:57:50] [Iter 1486/2250] R3[435/1200], Temp: 0.7093, Energy: -44.821317+0.002277j
[2025-08-26 04:58:16] [Iter 1487/2250] R3[436/1200], Temp: 0.7081, Energy: -44.819185-0.002236j
[2025-08-26 04:58:42] [Iter 1488/2250] R3[437/1200], Temp: 0.7069, Energy: -44.818260-0.002811j
[2025-08-26 04:59:08] [Iter 1489/2250] R3[438/1200], Temp: 0.7058, Energy: -44.819854+0.001597j
[2025-08-26 04:59:34] [Iter 1490/2250] R3[439/1200], Temp: 0.7046, Energy: -44.811661+0.001974j
[2025-08-26 05:00:00] [Iter 1491/2250] R3[440/1200], Temp: 0.7034, Energy: -44.820161+0.001918j
[2025-08-26 05:00:26] [Iter 1492/2250] R3[441/1200], Temp: 0.7022, Energy: -44.810566+0.000475j
[2025-08-26 05:00:52] [Iter 1493/2250] R3[442/1200], Temp: 0.7010, Energy: -44.814483-0.002177j
[2025-08-26 05:01:18] [Iter 1494/2250] R3[443/1200], Temp: 0.6998, Energy: -44.823331+0.001556j
[2025-08-26 05:01:44] [Iter 1495/2250] R3[444/1200], Temp: 0.6986, Energy: -44.805484+0.000619j
[2025-08-26 05:02:09] [Iter 1496/2250] R3[445/1200], Temp: 0.6974, Energy: -44.822500+0.001230j
[2025-08-26 05:02:35] [Iter 1497/2250] R3[446/1200], Temp: 0.6962, Energy: -44.821898+0.003335j
[2025-08-26 05:03:01] [Iter 1498/2250] R3[447/1200], Temp: 0.6950, Energy: -44.815362+0.000310j
[2025-08-26 05:03:27] [Iter 1499/2250] R3[448/1200], Temp: 0.6938, Energy: -44.815498+0.000364j
[2025-08-26 05:03:53] [Iter 1500/2250] R3[449/1200], Temp: 0.6926, Energy: -44.811426+0.000023j
[2025-08-26 05:03:53] ✓ Checkpoint saved: checkpoint_iter_001500.pkl
[2025-08-26 05:04:19] [Iter 1501/2250] R3[450/1200], Temp: 0.6913, Energy: -44.813723+0.005714j
[2025-08-26 05:04:45] [Iter 1502/2250] R3[451/1200], Temp: 0.6901, Energy: -44.820149-0.001154j
[2025-08-26 05:05:11] [Iter 1503/2250] R3[452/1200], Temp: 0.6889, Energy: -44.821778+0.001574j
[2025-08-26 05:05:37] [Iter 1504/2250] R3[453/1200], Temp: 0.6877, Energy: -44.822627-0.000279j
[2025-08-26 05:06:03] [Iter 1505/2250] R3[454/1200], Temp: 0.6865, Energy: -44.818029+0.001294j
[2025-08-26 05:06:29] [Iter 1506/2250] R3[455/1200], Temp: 0.6853, Energy: -44.824918+0.002266j
[2025-08-26 05:06:55] [Iter 1507/2250] R3[456/1200], Temp: 0.6841, Energy: -44.819338+0.001060j
[2025-08-26 05:07:21] [Iter 1508/2250] R3[457/1200], Temp: 0.6828, Energy: -44.820934+0.003702j
[2025-08-26 05:07:46] [Iter 1509/2250] R3[458/1200], Temp: 0.6816, Energy: -44.816628+0.000605j
[2025-08-26 05:08:12] [Iter 1510/2250] R3[459/1200], Temp: 0.6804, Energy: -44.817367-0.000577j
[2025-08-26 05:08:38] [Iter 1511/2250] R3[460/1200], Temp: 0.6792, Energy: -44.812675+0.000096j
[2025-08-26 05:09:04] [Iter 1512/2250] R3[461/1200], Temp: 0.6780, Energy: -44.822138+0.002496j
[2025-08-26 05:09:30] [Iter 1513/2250] R3[462/1200], Temp: 0.6767, Energy: -44.819009+0.000694j
[2025-08-26 05:09:56] [Iter 1514/2250] R3[463/1200], Temp: 0.6755, Energy: -44.820992-0.003285j
[2025-08-26 05:10:22] [Iter 1515/2250] R3[464/1200], Temp: 0.6743, Energy: -44.816475-0.003017j
[2025-08-26 05:10:48] [Iter 1516/2250] R3[465/1200], Temp: 0.6731, Energy: -44.817181-0.002492j
[2025-08-26 05:11:14] [Iter 1517/2250] R3[466/1200], Temp: 0.6718, Energy: -44.823524-0.000533j
[2025-08-26 05:11:40] [Iter 1518/2250] R3[467/1200], Temp: 0.6706, Energy: -44.819327+0.000618j
[2025-08-26 05:12:06] [Iter 1519/2250] R3[468/1200], Temp: 0.6694, Energy: -44.818196+0.001253j
[2025-08-26 05:12:32] [Iter 1520/2250] R3[469/1200], Temp: 0.6681, Energy: -44.815371-0.002069j
[2025-08-26 05:12:57] [Iter 1521/2250] R3[470/1200], Temp: 0.6669, Energy: -44.814515+0.002061j
[2025-08-26 05:13:23] [Iter 1522/2250] R3[471/1200], Temp: 0.6657, Energy: -44.812760-0.000568j
[2025-08-26 05:13:49] [Iter 1523/2250] R3[472/1200], Temp: 0.6644, Energy: -44.817663-0.001150j
[2025-08-26 05:14:15] [Iter 1524/2250] R3[473/1200], Temp: 0.6632, Energy: -44.816202+0.005476j
[2025-08-26 05:14:41] [Iter 1525/2250] R3[474/1200], Temp: 0.6620, Energy: -44.824200+0.000104j
[2025-08-26 05:15:07] [Iter 1526/2250] R3[475/1200], Temp: 0.6607, Energy: -44.812682+0.000748j
[2025-08-26 05:15:33] [Iter 1527/2250] R3[476/1200], Temp: 0.6595, Energy: -44.822174-0.002354j
[2025-08-26 05:15:59] [Iter 1528/2250] R3[477/1200], Temp: 0.6582, Energy: -44.827404-0.001119j
[2025-08-26 05:16:25] [Iter 1529/2250] R3[478/1200], Temp: 0.6570, Energy: -44.826291-0.000547j
[2025-08-26 05:16:51] [Iter 1530/2250] R3[479/1200], Temp: 0.6558, Energy: -44.813881-0.002558j
[2025-08-26 05:17:16] [Iter 1531/2250] R3[480/1200], Temp: 0.6545, Energy: -44.820328-0.001240j
[2025-08-26 05:17:42] [Iter 1532/2250] R3[481/1200], Temp: 0.6533, Energy: -44.815724+0.000366j
[2025-08-26 05:18:08] [Iter 1533/2250] R3[482/1200], Temp: 0.6520, Energy: -44.812629-0.000052j
[2025-08-26 05:18:34] [Iter 1534/2250] R3[483/1200], Temp: 0.6508, Energy: -44.822002-0.003756j
[2025-08-26 05:19:00] [Iter 1535/2250] R3[484/1200], Temp: 0.6495, Energy: -44.813982-0.001896j
[2025-08-26 05:19:26] [Iter 1536/2250] R3[485/1200], Temp: 0.6483, Energy: -44.820177+0.001461j
[2025-08-26 05:19:52] [Iter 1537/2250] R3[486/1200], Temp: 0.6470, Energy: -44.822596+0.000640j
[2025-08-26 05:20:18] [Iter 1538/2250] R3[487/1200], Temp: 0.6458, Energy: -44.819598+0.002676j
[2025-08-26 05:20:44] [Iter 1539/2250] R3[488/1200], Temp: 0.6445, Energy: -44.825682-0.000656j
[2025-08-26 05:21:10] [Iter 1540/2250] R3[489/1200], Temp: 0.6433, Energy: -44.809100+0.000673j
[2025-08-26 05:21:36] [Iter 1541/2250] R3[490/1200], Temp: 0.6420, Energy: -44.806486-0.001922j
[2025-08-26 05:22:01] [Iter 1542/2250] R3[491/1200], Temp: 0.6408, Energy: -44.822095+0.001992j
[2025-08-26 05:22:27] [Iter 1543/2250] R3[492/1200], Temp: 0.6395, Energy: -44.821841+0.000038j
[2025-08-26 05:22:53] [Iter 1544/2250] R3[493/1200], Temp: 0.6382, Energy: -44.831056-0.000279j
[2025-08-26 05:23:19] [Iter 1545/2250] R3[494/1200], Temp: 0.6370, Energy: -44.814790-0.001559j
[2025-08-26 05:23:45] [Iter 1546/2250] R3[495/1200], Temp: 0.6357, Energy: -44.814857-0.002565j
[2025-08-26 05:24:11] [Iter 1547/2250] R3[496/1200], Temp: 0.6345, Energy: -44.822317+0.000362j
[2025-08-26 05:24:37] [Iter 1548/2250] R3[497/1200], Temp: 0.6332, Energy: -44.821505+0.004333j
[2025-08-26 05:25:03] [Iter 1549/2250] R3[498/1200], Temp: 0.6319, Energy: -44.824653+0.003739j
[2025-08-26 05:25:29] [Iter 1550/2250] R3[499/1200], Temp: 0.6307, Energy: -44.816848+0.001352j
[2025-08-26 05:25:55] [Iter 1551/2250] R3[500/1200], Temp: 0.6294, Energy: -44.827698-0.001051j
[2025-08-26 05:26:21] [Iter 1552/2250] R3[501/1200], Temp: 0.6281, Energy: -44.819527+0.000961j
[2025-08-26 05:26:46] [Iter 1553/2250] R3[502/1200], Temp: 0.6269, Energy: -44.823033-0.002403j
[2025-08-26 05:27:12] [Iter 1554/2250] R3[503/1200], Temp: 0.6256, Energy: -44.818447+0.000006j
[2025-08-26 05:27:38] [Iter 1555/2250] R3[504/1200], Temp: 0.6243, Energy: -44.815830+0.000740j
[2025-08-26 05:28:04] [Iter 1556/2250] R3[505/1200], Temp: 0.6231, Energy: -44.820143-0.001040j
[2025-08-26 05:28:30] [Iter 1557/2250] R3[506/1200], Temp: 0.6218, Energy: -44.826468-0.001686j
[2025-08-26 05:28:56] [Iter 1558/2250] R3[507/1200], Temp: 0.6205, Energy: -44.820004+0.000149j
[2025-08-26 05:29:22] [Iter 1559/2250] R3[508/1200], Temp: 0.6193, Energy: -44.826042-0.000101j
[2025-08-26 05:29:48] [Iter 1560/2250] R3[509/1200], Temp: 0.6180, Energy: -44.830951-0.006306j
[2025-08-26 05:30:14] [Iter 1561/2250] R3[510/1200], Temp: 0.6167, Energy: -44.817924-0.001875j
[2025-08-26 05:30:40] [Iter 1562/2250] R3[511/1200], Temp: 0.6154, Energy: -44.818657-0.002837j
[2025-08-26 05:31:06] [Iter 1563/2250] R3[512/1200], Temp: 0.6142, Energy: -44.820587-0.000701j
[2025-08-26 05:31:31] [Iter 1564/2250] R3[513/1200], Temp: 0.6129, Energy: -44.831668-0.002507j
[2025-08-26 05:31:57] [Iter 1565/2250] R3[514/1200], Temp: 0.6116, Energy: -44.824536-0.000039j
[2025-08-26 05:32:23] [Iter 1566/2250] R3[515/1200], Temp: 0.6103, Energy: -44.820233-0.001998j
[2025-08-26 05:32:49] [Iter 1567/2250] R3[516/1200], Temp: 0.6091, Energy: -44.816464+0.000355j
[2025-08-26 05:33:15] [Iter 1568/2250] R3[517/1200], Temp: 0.6078, Energy: -44.809980+0.000748j
[2025-08-26 05:33:41] [Iter 1569/2250] R3[518/1200], Temp: 0.6065, Energy: -44.816480+0.000860j
[2025-08-26 05:34:07] [Iter 1570/2250] R3[519/1200], Temp: 0.6052, Energy: -44.820262+0.000282j
[2025-08-26 05:34:33] [Iter 1571/2250] R3[520/1200], Temp: 0.6040, Energy: -44.823382+0.002562j
[2025-08-26 05:34:59] [Iter 1572/2250] R3[521/1200], Temp: 0.6027, Energy: -44.823393+0.001716j
[2025-08-26 05:35:25] [Iter 1573/2250] R3[522/1200], Temp: 0.6014, Energy: -44.817908+0.002574j
[2025-08-26 05:35:50] [Iter 1574/2250] R3[523/1200], Temp: 0.6001, Energy: -44.818379+0.002422j
[2025-08-26 05:36:16] [Iter 1575/2250] R3[524/1200], Temp: 0.5988, Energy: -44.816278+0.001020j
[2025-08-26 05:36:42] [Iter 1576/2250] R3[525/1200], Temp: 0.5975, Energy: -44.826572-0.004662j
[2025-08-26 05:37:08] [Iter 1577/2250] R3[526/1200], Temp: 0.5963, Energy: -44.822894-0.000048j
[2025-08-26 05:37:34] [Iter 1578/2250] R3[527/1200], Temp: 0.5950, Energy: -44.819320-0.000259j
[2025-08-26 05:38:00] [Iter 1579/2250] R3[528/1200], Temp: 0.5937, Energy: -44.828551+0.000866j
[2025-08-26 05:38:26] [Iter 1580/2250] R3[529/1200], Temp: 0.5924, Energy: -44.820258+0.000283j
[2025-08-26 05:38:52] [Iter 1581/2250] R3[530/1200], Temp: 0.5911, Energy: -44.807828-0.001693j
[2025-08-26 05:39:18] [Iter 1582/2250] R3[531/1200], Temp: 0.5898, Energy: -44.818786+0.004654j
[2025-08-26 05:39:44] [Iter 1583/2250] R3[532/1200], Temp: 0.5885, Energy: -44.815126+0.000459j
[2025-08-26 05:40:10] [Iter 1584/2250] R3[533/1200], Temp: 0.5873, Energy: -44.812041-0.000552j
[2025-08-26 05:40:36] [Iter 1585/2250] R3[534/1200], Temp: 0.5860, Energy: -44.824537-0.001077j
[2025-08-26 05:41:01] [Iter 1586/2250] R3[535/1200], Temp: 0.5847, Energy: -44.820960-0.000672j
[2025-08-26 05:41:27] [Iter 1587/2250] R3[536/1200], Temp: 0.5834, Energy: -44.825110+0.000298j
[2025-08-26 05:41:53] [Iter 1588/2250] R3[537/1200], Temp: 0.5821, Energy: -44.809010+0.000914j
[2025-08-26 05:42:19] [Iter 1589/2250] R3[538/1200], Temp: 0.5808, Energy: -44.821182+0.000836j
[2025-08-26 05:42:45] [Iter 1590/2250] R3[539/1200], Temp: 0.5795, Energy: -44.822158+0.000441j
[2025-08-26 05:43:11] [Iter 1591/2250] R3[540/1200], Temp: 0.5782, Energy: -44.816344-0.000962j
[2025-08-26 05:43:37] [Iter 1592/2250] R3[541/1200], Temp: 0.5769, Energy: -44.825575-0.000575j
[2025-08-26 05:44:03] [Iter 1593/2250] R3[542/1200], Temp: 0.5756, Energy: -44.822226-0.000468j
[2025-08-26 05:44:29] [Iter 1594/2250] R3[543/1200], Temp: 0.5743, Energy: -44.826205-0.003037j
[2025-08-26 05:44:55] [Iter 1595/2250] R3[544/1200], Temp: 0.5730, Energy: -44.820191-0.000392j
[2025-08-26 05:45:21] [Iter 1596/2250] R3[545/1200], Temp: 0.5717, Energy: -44.819541-0.000378j
[2025-08-26 05:45:46] [Iter 1597/2250] R3[546/1200], Temp: 0.5705, Energy: -44.828752+0.002596j
[2025-08-26 05:46:12] [Iter 1598/2250] R3[547/1200], Temp: 0.5692, Energy: -44.818600+0.000642j
[2025-08-26 05:46:38] [Iter 1599/2250] R3[548/1200], Temp: 0.5679, Energy: -44.814242+0.000474j
[2025-08-26 05:47:04] [Iter 1600/2250] R3[549/1200], Temp: 0.5666, Energy: -44.811603+0.001324j
[2025-08-26 05:47:30] [Iter 1601/2250] R3[550/1200], Temp: 0.5653, Energy: -44.827396-0.001260j
[2025-08-26 05:47:56] [Iter 1602/2250] R3[551/1200], Temp: 0.5640, Energy: -44.830159+0.000099j
[2025-08-26 05:48:22] [Iter 1603/2250] R3[552/1200], Temp: 0.5627, Energy: -44.825676+0.000329j
[2025-08-26 05:48:48] [Iter 1604/2250] R3[553/1200], Temp: 0.5614, Energy: -44.812448-0.000578j
[2025-08-26 05:49:14] [Iter 1605/2250] R3[554/1200], Temp: 0.5601, Energy: -44.811721+0.002386j
[2025-08-26 05:49:40] [Iter 1606/2250] R3[555/1200], Temp: 0.5588, Energy: -44.820498-0.002399j
[2025-08-26 05:50:06] [Iter 1607/2250] R3[556/1200], Temp: 0.5575, Energy: -44.819561-0.001463j
[2025-08-26 05:50:32] [Iter 1608/2250] R3[557/1200], Temp: 0.5562, Energy: -44.820898+0.001429j
[2025-08-26 05:50:57] [Iter 1609/2250] R3[558/1200], Temp: 0.5549, Energy: -44.822427+0.001044j
[2025-08-26 05:51:23] [Iter 1610/2250] R3[559/1200], Temp: 0.5536, Energy: -44.829491+0.000082j
[2025-08-26 05:51:49] [Iter 1611/2250] R3[560/1200], Temp: 0.5523, Energy: -44.816371-0.001337j
[2025-08-26 05:52:15] [Iter 1612/2250] R3[561/1200], Temp: 0.5510, Energy: -44.826849-0.001377j
[2025-08-26 05:52:41] [Iter 1613/2250] R3[562/1200], Temp: 0.5497, Energy: -44.813442-0.002418j
[2025-08-26 05:53:07] [Iter 1614/2250] R3[563/1200], Temp: 0.5484, Energy: -44.820507-0.002937j
[2025-08-26 05:53:33] [Iter 1615/2250] R3[564/1200], Temp: 0.5471, Energy: -44.815919+0.002469j
[2025-08-26 05:53:59] [Iter 1616/2250] R3[565/1200], Temp: 0.5458, Energy: -44.821614+0.000866j
[2025-08-26 05:54:25] [Iter 1617/2250] R3[566/1200], Temp: 0.5444, Energy: -44.831815+0.001721j
[2025-08-26 05:54:51] [Iter 1618/2250] R3[567/1200], Temp: 0.5431, Energy: -44.811811+0.000454j
[2025-08-26 05:55:17] [Iter 1619/2250] R3[568/1200], Temp: 0.5418, Energy: -44.821272+0.000858j
[2025-08-26 05:55:42] [Iter 1620/2250] R3[569/1200], Temp: 0.5405, Energy: -44.825376-0.000143j
[2025-08-26 05:56:08] [Iter 1621/2250] R3[570/1200], Temp: 0.5392, Energy: -44.819225-0.001453j
[2025-08-26 05:56:34] [Iter 1622/2250] R3[571/1200], Temp: 0.5379, Energy: -44.819798+0.001321j
[2025-08-26 05:57:00] [Iter 1623/2250] R3[572/1200], Temp: 0.5366, Energy: -44.829119+0.000426j
[2025-08-26 05:57:26] [Iter 1624/2250] R3[573/1200], Temp: 0.5353, Energy: -44.812954-0.003790j
[2025-08-26 05:57:52] [Iter 1625/2250] R3[574/1200], Temp: 0.5340, Energy: -44.817490+0.003911j
[2025-08-26 05:58:18] [Iter 1626/2250] R3[575/1200], Temp: 0.5327, Energy: -44.819570-0.000012j
[2025-08-26 05:58:44] [Iter 1627/2250] R3[576/1200], Temp: 0.5314, Energy: -44.815619-0.002555j
[2025-08-26 05:59:10] [Iter 1628/2250] R3[577/1200], Temp: 0.5301, Energy: -44.819411-0.001111j
[2025-08-26 05:59:36] [Iter 1629/2250] R3[578/1200], Temp: 0.5288, Energy: -44.823481+0.000337j
[2025-08-26 06:00:01] [Iter 1630/2250] R3[579/1200], Temp: 0.5275, Energy: -44.830984+0.001450j
[2025-08-26 06:00:27] [Iter 1631/2250] R3[580/1200], Temp: 0.5262, Energy: -44.829545+0.000921j
[2025-08-26 06:00:53] [Iter 1632/2250] R3[581/1200], Temp: 0.5249, Energy: -44.820652+0.004888j
[2025-08-26 06:01:19] [Iter 1633/2250] R3[582/1200], Temp: 0.5236, Energy: -44.831301+0.000867j
[2025-08-26 06:01:45] [Iter 1634/2250] R3[583/1200], Temp: 0.5222, Energy: -44.817249-0.001976j
[2025-08-26 06:02:11] [Iter 1635/2250] R3[584/1200], Temp: 0.5209, Energy: -44.822593-0.000894j
[2025-08-26 06:02:37] [Iter 1636/2250] R3[585/1200], Temp: 0.5196, Energy: -44.824213+0.001257j
[2025-08-26 06:03:03] [Iter 1637/2250] R3[586/1200], Temp: 0.5183, Energy: -44.823826+0.000595j
[2025-08-26 06:03:29] [Iter 1638/2250] R3[587/1200], Temp: 0.5170, Energy: -44.821737+0.001132j
[2025-08-26 06:03:55] [Iter 1639/2250] R3[588/1200], Temp: 0.5157, Energy: -44.824566-0.000382j
[2025-08-26 06:04:21] [Iter 1640/2250] R3[589/1200], Temp: 0.5144, Energy: -44.818285+0.001292j
[2025-08-26 06:04:46] [Iter 1641/2250] R3[590/1200], Temp: 0.5131, Energy: -44.827386+0.000336j
[2025-08-26 06:05:12] [Iter 1642/2250] R3[591/1200], Temp: 0.5118, Energy: -44.818559-0.000486j
[2025-08-26 06:05:38] [Iter 1643/2250] R3[592/1200], Temp: 0.5105, Energy: -44.833172+0.000784j
[2025-08-26 06:06:04] [Iter 1644/2250] R3[593/1200], Temp: 0.5092, Energy: -44.814263-0.002244j
[2025-08-26 06:06:30] [Iter 1645/2250] R3[594/1200], Temp: 0.5079, Energy: -44.819205+0.001604j
[2025-08-26 06:06:56] [Iter 1646/2250] R3[595/1200], Temp: 0.5065, Energy: -44.833693-0.001310j
[2025-08-26 06:07:22] [Iter 1647/2250] R3[596/1200], Temp: 0.5052, Energy: -44.831560-0.002304j
[2025-08-26 06:07:48] [Iter 1648/2250] R3[597/1200], Temp: 0.5039, Energy: -44.827143-0.000138j
[2025-08-26 06:08:14] [Iter 1649/2250] R3[598/1200], Temp: 0.5026, Energy: -44.829774-0.002207j
[2025-08-26 06:08:40] [Iter 1650/2250] R3[599/1200], Temp: 0.5013, Energy: -44.823564-0.000962j
[2025-08-26 06:09:06] [Iter 1651/2250] R3[600/1200], Temp: 0.5000, Energy: -44.821070+0.004161j
[2025-08-26 06:09:31] [Iter 1652/2250] R3[601/1200], Temp: 0.4987, Energy: -44.817587-0.000661j
[2025-08-26 06:09:57] [Iter 1653/2250] R3[602/1200], Temp: 0.4974, Energy: -44.820760+0.001237j
[2025-08-26 06:10:23] [Iter 1654/2250] R3[603/1200], Temp: 0.4961, Energy: -44.813344-0.000183j
[2025-08-26 06:10:49] [Iter 1655/2250] R3[604/1200], Temp: 0.4948, Energy: -44.825406-0.002805j
[2025-08-26 06:11:15] [Iter 1656/2250] R3[605/1200], Temp: 0.4935, Energy: -44.824220-0.001359j
[2025-08-26 06:11:41] [Iter 1657/2250] R3[606/1200], Temp: 0.4921, Energy: -44.806924-0.001612j
[2025-08-26 06:12:07] [Iter 1658/2250] R3[607/1200], Temp: 0.4908, Energy: -44.829414+0.001557j
[2025-08-26 06:12:33] [Iter 1659/2250] R3[608/1200], Temp: 0.4895, Energy: -44.824683-0.001288j
[2025-08-26 06:12:59] [Iter 1660/2250] R3[609/1200], Temp: 0.4882, Energy: -44.822551+0.001187j
[2025-08-26 06:13:25] [Iter 1661/2250] R3[610/1200], Temp: 0.4869, Energy: -44.823334+0.000464j
[2025-08-26 06:13:51] [Iter 1662/2250] R3[611/1200], Temp: 0.4856, Energy: -44.819827-0.000855j
[2025-08-26 06:14:16] [Iter 1663/2250] R3[612/1200], Temp: 0.4843, Energy: -44.831176+0.001684j
[2025-08-26 06:14:42] [Iter 1664/2250] R3[613/1200], Temp: 0.4830, Energy: -44.822007+0.002398j
[2025-08-26 06:15:08] [Iter 1665/2250] R3[614/1200], Temp: 0.4817, Energy: -44.829497+0.001544j
[2025-08-26 06:15:34] [Iter 1666/2250] R3[615/1200], Temp: 0.4804, Energy: -44.815340-0.000795j
[2025-08-26 06:16:00] [Iter 1667/2250] R3[616/1200], Temp: 0.4791, Energy: -44.825542-0.000293j
[2025-08-26 06:16:26] [Iter 1668/2250] R3[617/1200], Temp: 0.4778, Energy: -44.835187+0.000365j
[2025-08-26 06:16:52] [Iter 1669/2250] R3[618/1200], Temp: 0.4764, Energy: -44.823106-0.000434j
[2025-08-26 06:17:18] [Iter 1670/2250] R3[619/1200], Temp: 0.4751, Energy: -44.823703+0.000945j
[2025-08-26 06:17:44] [Iter 1671/2250] R3[620/1200], Temp: 0.4738, Energy: -44.830518+0.002262j
[2025-08-26 06:18:09] [Iter 1672/2250] R3[621/1200], Temp: 0.4725, Energy: -44.832598+0.003759j
[2025-08-26 06:18:36] [Iter 1673/2250] R3[622/1200], Temp: 0.4712, Energy: -44.829072-0.001319j
[2025-08-26 06:19:02] [Iter 1674/2250] R3[623/1200], Temp: 0.4699, Energy: -44.820913+0.000963j
[2025-08-26 06:19:28] [Iter 1675/2250] R3[624/1200], Temp: 0.4686, Energy: -44.828300-0.001037j
[2025-08-26 06:19:54] [Iter 1676/2250] R3[625/1200], Temp: 0.4673, Energy: -44.824072-0.001214j
[2025-08-26 06:20:19] [Iter 1677/2250] R3[626/1200], Temp: 0.4660, Energy: -44.824110-0.002305j
[2025-08-26 06:20:45] [Iter 1678/2250] R3[627/1200], Temp: 0.4647, Energy: -44.827628-0.002066j
[2025-08-26 06:21:11] [Iter 1679/2250] R3[628/1200], Temp: 0.4634, Energy: -44.830209-0.000889j
[2025-08-26 06:21:37] [Iter 1680/2250] R3[629/1200], Temp: 0.4621, Energy: -44.823945+0.000476j
[2025-08-26 06:22:03] [Iter 1681/2250] R3[630/1200], Temp: 0.4608, Energy: -44.834166+0.000325j
[2025-08-26 06:22:29] [Iter 1682/2250] R3[631/1200], Temp: 0.4595, Energy: -44.831220+0.000396j
[2025-08-26 06:22:55] [Iter 1683/2250] R3[632/1200], Temp: 0.4582, Energy: -44.828884+0.000722j
[2025-08-26 06:23:21] [Iter 1684/2250] R3[633/1200], Temp: 0.4569, Energy: -44.817977-0.000168j
[2025-08-26 06:23:47] [Iter 1685/2250] R3[634/1200], Temp: 0.4556, Energy: -44.821825-0.000969j
[2025-08-26 06:24:12] [Iter 1686/2250] R3[635/1200], Temp: 0.4542, Energy: -44.836587+0.001074j
[2025-08-26 06:24:38] [Iter 1687/2250] R3[636/1200], Temp: 0.4529, Energy: -44.818915-0.000790j
[2025-08-26 06:25:04] [Iter 1688/2250] R3[637/1200], Temp: 0.4516, Energy: -44.832778+0.000466j
[2025-08-26 06:25:30] [Iter 1689/2250] R3[638/1200], Temp: 0.4503, Energy: -44.827795+0.001788j
[2025-08-26 06:25:56] [Iter 1690/2250] R3[639/1200], Temp: 0.4490, Energy: -44.827447+0.003663j
[2025-08-26 06:26:22] [Iter 1691/2250] R3[640/1200], Temp: 0.4477, Energy: -44.826988-0.002499j
[2025-08-26 06:26:48] [Iter 1692/2250] R3[641/1200], Temp: 0.4464, Energy: -44.829426+0.000748j
[2025-08-26 06:27:14] [Iter 1693/2250] R3[642/1200], Temp: 0.4451, Energy: -44.825863+0.000203j
[2025-08-26 06:27:40] [Iter 1694/2250] R3[643/1200], Temp: 0.4438, Energy: -44.825911-0.005582j
[2025-08-26 06:28:06] [Iter 1695/2250] R3[644/1200], Temp: 0.4425, Energy: -44.825969+0.001469j
[2025-08-26 06:28:31] [Iter 1696/2250] R3[645/1200], Temp: 0.4412, Energy: -44.832222-0.000566j
[2025-08-26 06:28:57] [Iter 1697/2250] R3[646/1200], Temp: 0.4399, Energy: -44.829399+0.001538j
[2025-08-26 06:29:23] [Iter 1698/2250] R3[647/1200], Temp: 0.4386, Energy: -44.830951+0.001795j
[2025-08-26 06:29:49] [Iter 1699/2250] R3[648/1200], Temp: 0.4373, Energy: -44.816185-0.000179j
[2025-08-26 06:30:15] [Iter 1700/2250] R3[649/1200], Temp: 0.4360, Energy: -44.828646-0.002556j
[2025-08-26 06:30:41] [Iter 1701/2250] R3[650/1200], Temp: 0.4347, Energy: -44.828746+0.001451j
[2025-08-26 06:31:07] [Iter 1702/2250] R3[651/1200], Temp: 0.4334, Energy: -44.830448-0.002848j
[2025-08-26 06:31:33] [Iter 1703/2250] R3[652/1200], Temp: 0.4321, Energy: -44.824133+0.000546j
[2025-08-26 06:31:59] [Iter 1704/2250] R3[653/1200], Temp: 0.4308, Energy: -44.830523+0.002790j
[2025-08-26 06:32:25] [Iter 1705/2250] R3[654/1200], Temp: 0.4295, Energy: -44.825101+0.000625j
[2025-08-26 06:32:50] [Iter 1706/2250] R3[655/1200], Temp: 0.4283, Energy: -44.815894+0.001054j
[2025-08-26 06:33:16] [Iter 1707/2250] R3[656/1200], Temp: 0.4270, Energy: -44.837907-0.000556j
[2025-08-26 06:33:42] [Iter 1708/2250] R3[657/1200], Temp: 0.4257, Energy: -44.827547-0.000133j
[2025-08-26 06:34:08] [Iter 1709/2250] R3[658/1200], Temp: 0.4244, Energy: -44.828958+0.004601j
[2025-08-26 06:34:34] [Iter 1710/2250] R3[659/1200], Temp: 0.4231, Energy: -44.822068+0.001612j
[2025-08-26 06:35:00] [Iter 1711/2250] R3[660/1200], Temp: 0.4218, Energy: -44.828640+0.000719j
[2025-08-26 06:35:26] [Iter 1712/2250] R3[661/1200], Temp: 0.4205, Energy: -44.822831+0.000146j
[2025-08-26 06:35:52] [Iter 1713/2250] R3[662/1200], Temp: 0.4192, Energy: -44.820513-0.000399j
[2025-08-26 06:36:18] [Iter 1714/2250] R3[663/1200], Temp: 0.4179, Energy: -44.823884+0.002846j
[2025-08-26 06:36:44] [Iter 1715/2250] R3[664/1200], Temp: 0.4166, Energy: -44.835558-0.001388j
[2025-08-26 06:37:09] [Iter 1716/2250] R3[665/1200], Temp: 0.4153, Energy: -44.825747-0.002444j
[2025-08-26 06:37:35] [Iter 1717/2250] R3[666/1200], Temp: 0.4140, Energy: -44.824063-0.003183j
[2025-08-26 06:38:01] [Iter 1718/2250] R3[667/1200], Temp: 0.4127, Energy: -44.822218+0.000011j
[2025-08-26 06:38:27] [Iter 1719/2250] R3[668/1200], Temp: 0.4115, Energy: -44.823531+0.002365j
[2025-08-26 06:38:53] [Iter 1720/2250] R3[669/1200], Temp: 0.4102, Energy: -44.830692-0.000022j
[2025-08-26 06:39:19] [Iter 1721/2250] R3[670/1200], Temp: 0.4089, Energy: -44.836123-0.000504j
[2025-08-26 06:39:45] [Iter 1722/2250] R3[671/1200], Temp: 0.4076, Energy: -44.823695-0.003385j
[2025-08-26 06:40:11] [Iter 1723/2250] R3[672/1200], Temp: 0.4063, Energy: -44.816102-0.000391j
[2025-08-26 06:40:37] [Iter 1724/2250] R3[673/1200], Temp: 0.4050, Energy: -44.831529-0.000733j
[2025-08-26 06:41:03] [Iter 1725/2250] R3[674/1200], Temp: 0.4037, Energy: -44.832030+0.000469j
[2025-08-26 06:41:28] [Iter 1726/2250] R3[675/1200], Temp: 0.4025, Energy: -44.828466+0.000968j
[2025-08-26 06:41:54] [Iter 1727/2250] R3[676/1200], Temp: 0.4012, Energy: -44.818038+0.003336j
[2025-08-26 06:42:20] [Iter 1728/2250] R3[677/1200], Temp: 0.3999, Energy: -44.829003-0.003095j
[2025-08-26 06:42:46] [Iter 1729/2250] R3[678/1200], Temp: 0.3986, Energy: -44.835527+0.000145j
[2025-08-26 06:43:12] [Iter 1730/2250] R3[679/1200], Temp: 0.3973, Energy: -44.832493+0.000163j
[2025-08-26 06:43:38] [Iter 1731/2250] R3[680/1200], Temp: 0.3960, Energy: -44.837270+0.000907j
[2025-08-26 06:44:04] [Iter 1732/2250] R3[681/1200], Temp: 0.3948, Energy: -44.840567+0.000632j
[2025-08-26 06:44:30] [Iter 1733/2250] R3[682/1200], Temp: 0.3935, Energy: -44.827252+0.002194j
[2025-08-26 06:44:56] [Iter 1734/2250] R3[683/1200], Temp: 0.3922, Energy: -44.838945-0.000014j
[2025-08-26 06:45:22] [Iter 1735/2250] R3[684/1200], Temp: 0.3909, Energy: -44.832647-0.001478j
[2025-08-26 06:45:48] [Iter 1736/2250] R3[685/1200], Temp: 0.3897, Energy: -44.832941-0.001143j
[2025-08-26 06:46:13] [Iter 1737/2250] R3[686/1200], Temp: 0.3884, Energy: -44.829925+0.001322j
[2025-08-26 06:46:39] [Iter 1738/2250] R3[687/1200], Temp: 0.3871, Energy: -44.822084+0.001507j
[2025-08-26 06:47:05] [Iter 1739/2250] R3[688/1200], Temp: 0.3858, Energy: -44.830459-0.002694j
[2025-08-26 06:47:31] [Iter 1740/2250] R3[689/1200], Temp: 0.3846, Energy: -44.832782+0.003265j
[2025-08-26 06:47:57] [Iter 1741/2250] R3[690/1200], Temp: 0.3833, Energy: -44.833454-0.000186j
[2025-08-26 06:48:23] [Iter 1742/2250] R3[691/1200], Temp: 0.3820, Energy: -44.834753-0.000408j
[2025-08-26 06:48:49] [Iter 1743/2250] R3[692/1200], Temp: 0.3807, Energy: -44.832611-0.002460j
[2025-08-26 06:49:15] [Iter 1744/2250] R3[693/1200], Temp: 0.3795, Energy: -44.824225+0.000913j
[2025-08-26 06:49:41] [Iter 1745/2250] R3[694/1200], Temp: 0.3782, Energy: -44.830121-0.001662j
[2025-08-26 06:50:07] [Iter 1746/2250] R3[695/1200], Temp: 0.3769, Energy: -44.824005+0.001555j
[2025-08-26 06:50:32] [Iter 1747/2250] R3[696/1200], Temp: 0.3757, Energy: -44.820398-0.000893j
[2025-08-26 06:50:58] [Iter 1748/2250] R3[697/1200], Temp: 0.3744, Energy: -44.836828-0.000715j
[2025-08-26 06:51:24] [Iter 1749/2250] R3[698/1200], Temp: 0.3731, Energy: -44.833159+0.000665j
[2025-08-26 06:51:50] [Iter 1750/2250] R3[699/1200], Temp: 0.3719, Energy: -44.831841-0.000293j
[2025-08-26 06:51:50] ✓ Checkpoint saved: checkpoint_iter_001750.pkl
[2025-08-26 06:52:16] [Iter 1751/2250] R3[700/1200], Temp: 0.3706, Energy: -44.829632+0.001921j
[2025-08-26 06:52:42] [Iter 1752/2250] R3[701/1200], Temp: 0.3693, Energy: -44.829981-0.002087j
[2025-08-26 06:53:08] [Iter 1753/2250] R3[702/1200], Temp: 0.3681, Energy: -44.817717-0.001032j
[2025-08-26 06:53:34] [Iter 1754/2250] R3[703/1200], Temp: 0.3668, Energy: -44.825597-0.001880j
[2025-08-26 06:54:00] [Iter 1755/2250] R3[704/1200], Temp: 0.3655, Energy: -44.825527-0.002500j
[2025-08-26 06:54:26] [Iter 1756/2250] R3[705/1200], Temp: 0.3643, Energy: -44.820970-0.000946j
[2025-08-26 06:54:52] [Iter 1757/2250] R3[706/1200], Temp: 0.3630, Energy: -44.825643-0.000287j
[2025-08-26 06:55:17] [Iter 1758/2250] R3[707/1200], Temp: 0.3618, Energy: -44.834519-0.000782j
[2025-08-26 06:55:43] [Iter 1759/2250] R3[708/1200], Temp: 0.3605, Energy: -44.836311+0.000438j
[2025-08-26 06:56:09] [Iter 1760/2250] R3[709/1200], Temp: 0.3592, Energy: -44.825206+0.000562j
[2025-08-26 06:56:35] [Iter 1761/2250] R3[710/1200], Temp: 0.3580, Energy: -44.835441+0.004717j
[2025-08-26 06:57:01] [Iter 1762/2250] R3[711/1200], Temp: 0.3567, Energy: -44.827593-0.000944j
[2025-08-26 06:57:27] [Iter 1763/2250] R3[712/1200], Temp: 0.3555, Energy: -44.831358-0.003287j
[2025-08-26 06:57:53] [Iter 1764/2250] R3[713/1200], Temp: 0.3542, Energy: -44.819675-0.003334j
[2025-08-26 06:58:19] [Iter 1765/2250] R3[714/1200], Temp: 0.3530, Energy: -44.826328-0.003670j
[2025-08-26 06:58:45] [Iter 1766/2250] R3[715/1200], Temp: 0.3517, Energy: -44.849410+0.002115j
[2025-08-26 06:59:11] [Iter 1767/2250] R3[716/1200], Temp: 0.3505, Energy: -44.831485+0.001706j
[2025-08-26 06:59:37] [Iter 1768/2250] R3[717/1200], Temp: 0.3492, Energy: -44.831279+0.001121j
[2025-08-26 07:00:02] [Iter 1769/2250] R3[718/1200], Temp: 0.3480, Energy: -44.834119-0.001081j
[2025-08-26 07:00:28] [Iter 1770/2250] R3[719/1200], Temp: 0.3467, Energy: -44.834558-0.000777j
[2025-08-26 07:00:54] [Iter 1771/2250] R3[720/1200], Temp: 0.3455, Energy: -44.826281+0.001105j
[2025-08-26 07:01:20] [Iter 1772/2250] R3[721/1200], Temp: 0.3442, Energy: -44.832484-0.000842j
[2025-08-26 07:01:46] [Iter 1773/2250] R3[722/1200], Temp: 0.3430, Energy: -44.836780+0.000168j
[2025-08-26 07:02:12] [Iter 1774/2250] R3[723/1200], Temp: 0.3418, Energy: -44.820835+0.000553j
[2025-08-26 07:02:38] [Iter 1775/2250] R3[724/1200], Temp: 0.3405, Energy: -44.826292-0.000666j
[2025-08-26 07:03:04] [Iter 1776/2250] R3[725/1200], Temp: 0.3393, Energy: -44.834808+0.001550j
[2025-08-26 07:03:30] [Iter 1777/2250] R3[726/1200], Temp: 0.3380, Energy: -44.836367+0.002806j
[2025-08-26 07:03:56] [Iter 1778/2250] R3[727/1200], Temp: 0.3368, Energy: -44.827730-0.001543j
[2025-08-26 07:04:22] [Iter 1779/2250] R3[728/1200], Temp: 0.3356, Energy: -44.830282-0.001584j
[2025-08-26 07:04:47] [Iter 1780/2250] R3[729/1200], Temp: 0.3343, Energy: -44.834568-0.001313j
[2025-08-26 07:05:13] [Iter 1781/2250] R3[730/1200], Temp: 0.3331, Energy: -44.829729+0.000844j
[2025-08-26 07:05:39] [Iter 1782/2250] R3[731/1200], Temp: 0.3319, Energy: -44.826042-0.001127j
[2025-08-26 07:06:05] [Iter 1783/2250] R3[732/1200], Temp: 0.3306, Energy: -44.832063+0.001288j
[2025-08-26 07:06:31] [Iter 1784/2250] R3[733/1200], Temp: 0.3294, Energy: -44.828976-0.000005j
[2025-08-26 07:06:57] [Iter 1785/2250] R3[734/1200], Temp: 0.3282, Energy: -44.836269-0.000619j
[2025-08-26 07:07:23] [Iter 1786/2250] R3[735/1200], Temp: 0.3269, Energy: -44.828443-0.003791j
[2025-08-26 07:07:49] [Iter 1787/2250] R3[736/1200], Temp: 0.3257, Energy: -44.832368-0.001871j
[2025-08-26 07:08:15] [Iter 1788/2250] R3[737/1200], Temp: 0.3245, Energy: -44.840241+0.003182j
[2025-08-26 07:08:41] [Iter 1789/2250] R3[738/1200], Temp: 0.3233, Energy: -44.828522+0.001035j
[2025-08-26 07:09:07] [Iter 1790/2250] R3[739/1200], Temp: 0.3220, Energy: -44.831427-0.000542j
[2025-08-26 07:09:33] [Iter 1791/2250] R3[740/1200], Temp: 0.3208, Energy: -44.829518+0.001346j
[2025-08-26 07:09:58] [Iter 1792/2250] R3[741/1200], Temp: 0.3196, Energy: -44.834689+0.004113j
[2025-08-26 07:10:24] [Iter 1793/2250] R3[742/1200], Temp: 0.3184, Energy: -44.832523-0.002518j
[2025-08-26 07:10:50] [Iter 1794/2250] R3[743/1200], Temp: 0.3172, Energy: -44.833650-0.003376j
[2025-08-26 07:11:16] [Iter 1795/2250] R3[744/1200], Temp: 0.3159, Energy: -44.835580-0.000260j
[2025-08-26 07:11:42] [Iter 1796/2250] R3[745/1200], Temp: 0.3147, Energy: -44.825006+0.000048j
[2025-08-26 07:12:08] [Iter 1797/2250] R3[746/1200], Temp: 0.3135, Energy: -44.836492+0.000131j
[2025-08-26 07:12:34] [Iter 1798/2250] R3[747/1200], Temp: 0.3123, Energy: -44.818482-0.001950j
[2025-08-26 07:13:00] [Iter 1799/2250] R3[748/1200], Temp: 0.3111, Energy: -44.832118+0.000294j
[2025-08-26 07:13:26] [Iter 1800/2250] R3[749/1200], Temp: 0.3099, Energy: -44.827614-0.002860j
[2025-08-26 07:13:52] [Iter 1801/2250] R3[750/1200], Temp: 0.3087, Energy: -44.841230-0.003180j
[2025-08-26 07:14:18] [Iter 1802/2250] R3[751/1200], Temp: 0.3074, Energy: -44.824128+0.000585j
[2025-08-26 07:14:44] [Iter 1803/2250] R3[752/1200], Temp: 0.3062, Energy: -44.836180-0.002520j
[2025-08-26 07:15:09] [Iter 1804/2250] R3[753/1200], Temp: 0.3050, Energy: -44.833573-0.000330j
[2025-08-26 07:15:35] [Iter 1805/2250] R3[754/1200], Temp: 0.3038, Energy: -44.824796+0.000078j
[2025-08-26 07:16:01] [Iter 1806/2250] R3[755/1200], Temp: 0.3026, Energy: -44.829795+0.001883j
[2025-08-26 07:16:27] [Iter 1807/2250] R3[756/1200], Temp: 0.3014, Energy: -44.825560+0.002438j
[2025-08-26 07:16:53] [Iter 1808/2250] R3[757/1200], Temp: 0.3002, Energy: -44.828992+0.002107j
[2025-08-26 07:17:19] [Iter 1809/2250] R3[758/1200], Temp: 0.2990, Energy: -44.818550-0.000733j
[2025-08-26 07:17:45] [Iter 1810/2250] R3[759/1200], Temp: 0.2978, Energy: -44.837619-0.002583j
[2025-08-26 07:18:11] [Iter 1811/2250] R3[760/1200], Temp: 0.2966, Energy: -44.836976-0.003174j
[2025-08-26 07:18:37] [Iter 1812/2250] R3[761/1200], Temp: 0.2954, Energy: -44.840386-0.001670j
[2025-08-26 07:19:03] [Iter 1813/2250] R3[762/1200], Temp: 0.2942, Energy: -44.828183-0.003003j
[2025-08-26 07:19:28] [Iter 1814/2250] R3[763/1200], Temp: 0.2931, Energy: -44.838029-0.003827j
[2025-08-26 07:19:54] [Iter 1815/2250] R3[764/1200], Temp: 0.2919, Energy: -44.831677-0.000651j
[2025-08-26 07:20:20] [Iter 1816/2250] R3[765/1200], Temp: 0.2907, Energy: -44.833837-0.001726j
[2025-08-26 07:20:46] [Iter 1817/2250] R3[766/1200], Temp: 0.2895, Energy: -44.835573+0.001210j
[2025-08-26 07:21:12] [Iter 1818/2250] R3[767/1200], Temp: 0.2883, Energy: -44.823872-0.000451j
[2025-08-26 07:21:38] [Iter 1819/2250] R3[768/1200], Temp: 0.2871, Energy: -44.835769-0.000015j
[2025-08-26 07:22:04] [Iter 1820/2250] R3[769/1200], Temp: 0.2859, Energy: -44.833662-0.000493j
[2025-08-26 07:22:30] [Iter 1821/2250] R3[770/1200], Temp: 0.2847, Energy: -44.836104-0.002064j
[2025-08-26 07:22:56] [Iter 1822/2250] R3[771/1200], Temp: 0.2836, Energy: -44.836985-0.000932j
[2025-08-26 07:23:22] [Iter 1823/2250] R3[772/1200], Temp: 0.2824, Energy: -44.830726-0.000980j
[2025-08-26 07:23:47] [Iter 1824/2250] R3[773/1200], Temp: 0.2812, Energy: -44.833721-0.002140j
[2025-08-26 07:24:13] [Iter 1825/2250] R3[774/1200], Temp: 0.2800, Energy: -44.831479-0.001742j
[2025-08-26 07:24:39] [Iter 1826/2250] R3[775/1200], Temp: 0.2789, Energy: -44.835198-0.000184j
[2025-08-26 07:25:05] [Iter 1827/2250] R3[776/1200], Temp: 0.2777, Energy: -44.831767+0.000787j
[2025-08-26 07:25:31] [Iter 1828/2250] R3[777/1200], Temp: 0.2765, Energy: -44.833243-0.000083j
[2025-08-26 07:25:57] [Iter 1829/2250] R3[778/1200], Temp: 0.2753, Energy: -44.836035+0.002194j
[2025-08-26 07:26:23] [Iter 1830/2250] R3[779/1200], Temp: 0.2742, Energy: -44.835074-0.001732j
[2025-08-26 07:26:49] [Iter 1831/2250] R3[780/1200], Temp: 0.2730, Energy: -44.833238+0.001424j
[2025-08-26 07:27:15] [Iter 1832/2250] R3[781/1200], Temp: 0.2718, Energy: -44.833423+0.000061j
[2025-08-26 07:27:41] [Iter 1833/2250] R3[782/1200], Temp: 0.2707, Energy: -44.839725-0.000599j
[2025-08-26 07:28:06] [Iter 1834/2250] R3[783/1200], Temp: 0.2695, Energy: -44.829510+0.000045j
[2025-08-26 07:28:32] [Iter 1835/2250] R3[784/1200], Temp: 0.2684, Energy: -44.839147-0.002885j
[2025-08-26 07:28:58] [Iter 1836/2250] R3[785/1200], Temp: 0.2672, Energy: -44.829600+0.000232j
[2025-08-26 07:29:24] [Iter 1837/2250] R3[786/1200], Temp: 0.2660, Energy: -44.830199+0.001169j
[2025-08-26 07:29:50] [Iter 1838/2250] R3[787/1200], Temp: 0.2649, Energy: -44.834085+0.001234j
[2025-08-26 07:30:16] [Iter 1839/2250] R3[788/1200], Temp: 0.2637, Energy: -44.830854-0.000171j
[2025-08-26 07:30:42] [Iter 1840/2250] R3[789/1200], Temp: 0.2626, Energy: -44.834075+0.001041j
[2025-08-26 07:31:08] [Iter 1841/2250] R3[790/1200], Temp: 0.2614, Energy: -44.834579+0.000151j
[2025-08-26 07:31:34] [Iter 1842/2250] R3[791/1200], Temp: 0.2603, Energy: -44.839274+0.001152j
[2025-08-26 07:32:00] [Iter 1843/2250] R3[792/1200], Temp: 0.2591, Energy: -44.833809+0.000959j
[2025-08-26 07:32:26] [Iter 1844/2250] R3[793/1200], Temp: 0.2580, Energy: -44.832185-0.002227j
[2025-08-26 07:32:52] [Iter 1845/2250] R3[794/1200], Temp: 0.2568, Energy: -44.829805-0.003291j
[2025-08-26 07:33:17] [Iter 1846/2250] R3[795/1200], Temp: 0.2557, Energy: -44.828631+0.001423j
[2025-08-26 07:33:43] [Iter 1847/2250] R3[796/1200], Temp: 0.2545, Energy: -44.829961+0.001353j
[2025-08-26 07:34:09] [Iter 1848/2250] R3[797/1200], Temp: 0.2534, Energy: -44.830361-0.001694j
[2025-08-26 07:34:35] [Iter 1849/2250] R3[798/1200], Temp: 0.2523, Energy: -44.836565-0.001658j
[2025-08-26 07:35:01] [Iter 1850/2250] R3[799/1200], Temp: 0.2511, Energy: -44.831062-0.001455j
[2025-08-26 07:35:27] [Iter 1851/2250] R3[800/1200], Temp: 0.2500, Energy: -44.825718-0.000153j
[2025-08-26 07:35:53] [Iter 1852/2250] R3[801/1200], Temp: 0.2489, Energy: -44.837824-0.003618j
[2025-08-26 07:36:19] [Iter 1853/2250] R3[802/1200], Temp: 0.2477, Energy: -44.833295-0.002738j
[2025-08-26 07:36:45] [Iter 1854/2250] R3[803/1200], Temp: 0.2466, Energy: -44.831039+0.000165j
[2025-08-26 07:37:11] [Iter 1855/2250] R3[804/1200], Temp: 0.2455, Energy: -44.829169-0.000115j
[2025-08-26 07:37:36] [Iter 1856/2250] R3[805/1200], Temp: 0.2444, Energy: -44.827986+0.000636j
[2025-08-26 07:38:02] [Iter 1857/2250] R3[806/1200], Temp: 0.2432, Energy: -44.836906-0.001945j
[2025-08-26 07:38:28] [Iter 1858/2250] R3[807/1200], Temp: 0.2421, Energy: -44.833739+0.002932j
[2025-08-26 07:38:54] [Iter 1859/2250] R3[808/1200], Temp: 0.2410, Energy: -44.840739-0.002050j
[2025-08-26 07:39:20] [Iter 1860/2250] R3[809/1200], Temp: 0.2399, Energy: -44.836991+0.002942j
[2025-08-26 07:39:46] [Iter 1861/2250] R3[810/1200], Temp: 0.2388, Energy: -44.837836+0.001994j
[2025-08-26 07:40:12] [Iter 1862/2250] R3[811/1200], Temp: 0.2376, Energy: -44.832659+0.000115j
[2025-08-26 07:40:38] [Iter 1863/2250] R3[812/1200], Temp: 0.2365, Energy: -44.832396-0.000854j
[2025-08-26 07:41:04] [Iter 1864/2250] R3[813/1200], Temp: 0.2354, Energy: -44.820333-0.000422j
[2025-08-26 07:41:30] [Iter 1865/2250] R3[814/1200], Temp: 0.2343, Energy: -44.840245+0.002074j
[2025-08-26 07:41:56] [Iter 1866/2250] R3[815/1200], Temp: 0.2332, Energy: -44.830015+0.000340j
[2025-08-26 07:42:22] [Iter 1867/2250] R3[816/1200], Temp: 0.2321, Energy: -44.820124-0.000242j
[2025-08-26 07:42:47] [Iter 1868/2250] R3[817/1200], Temp: 0.2310, Energy: -44.835256+0.004496j
[2025-08-26 07:43:13] [Iter 1869/2250] R3[818/1200], Temp: 0.2299, Energy: -44.833189-0.002548j
[2025-08-26 07:43:39] [Iter 1870/2250] R3[819/1200], Temp: 0.2288, Energy: -44.833535-0.002742j
[2025-08-26 07:44:05] [Iter 1871/2250] R3[820/1200], Temp: 0.2277, Energy: -44.837068+0.000047j
[2025-08-26 07:44:31] [Iter 1872/2250] R3[821/1200], Temp: 0.2266, Energy: -44.838235-0.001028j
[2025-08-26 07:44:57] [Iter 1873/2250] R3[822/1200], Temp: 0.2255, Energy: -44.836446-0.003144j
[2025-08-26 07:45:23] [Iter 1874/2250] R3[823/1200], Temp: 0.2244, Energy: -44.836688-0.002250j
[2025-08-26 07:45:49] [Iter 1875/2250] R3[824/1200], Temp: 0.2233, Energy: -44.836754-0.000460j
[2025-08-26 07:46:15] [Iter 1876/2250] R3[825/1200], Temp: 0.2222, Energy: -44.829070-0.002049j
[2025-08-26 07:46:41] [Iter 1877/2250] R3[826/1200], Temp: 0.2211, Energy: -44.822876-0.005918j
[2025-08-26 07:47:07] [Iter 1878/2250] R3[827/1200], Temp: 0.2200, Energy: -44.835696-0.000788j
[2025-08-26 07:47:33] [Iter 1879/2250] R3[828/1200], Temp: 0.2190, Energy: -44.833075-0.001796j
[2025-08-26 07:47:58] [Iter 1880/2250] R3[829/1200], Temp: 0.2179, Energy: -44.838244-0.000069j
[2025-08-26 07:48:24] [Iter 1881/2250] R3[830/1200], Temp: 0.2168, Energy: -44.831245+0.001409j
[2025-08-26 07:48:50] [Iter 1882/2250] R3[831/1200], Temp: 0.2157, Energy: -44.832542-0.001023j
[2025-08-26 07:49:16] [Iter 1883/2250] R3[832/1200], Temp: 0.2146, Energy: -44.826408+0.001902j
[2025-08-26 07:49:42] [Iter 1884/2250] R3[833/1200], Temp: 0.2136, Energy: -44.837736+0.002344j
[2025-08-26 07:50:08] [Iter 1885/2250] R3[834/1200], Temp: 0.2125, Energy: -44.831219+0.001685j
[2025-08-26 07:50:34] [Iter 1886/2250] R3[835/1200], Temp: 0.2114, Energy: -44.833027+0.002483j
[2025-08-26 07:51:00] [Iter 1887/2250] R3[836/1200], Temp: 0.2104, Energy: -44.833311-0.003207j
[2025-08-26 07:51:26] [Iter 1888/2250] R3[837/1200], Temp: 0.2093, Energy: -44.830564+0.000260j
[2025-08-26 07:51:52] [Iter 1889/2250] R3[838/1200], Temp: 0.2082, Energy: -44.835775-0.001143j
[2025-08-26 07:52:17] [Iter 1890/2250] R3[839/1200], Temp: 0.2072, Energy: -44.833886+0.001240j
[2025-08-26 07:52:43] [Iter 1891/2250] R3[840/1200], Temp: 0.2061, Energy: -44.833818-0.000073j
[2025-08-26 07:53:09] [Iter 1892/2250] R3[841/1200], Temp: 0.2050, Energy: -44.837720-0.000402j
[2025-08-26 07:53:35] [Iter 1893/2250] R3[842/1200], Temp: 0.2040, Energy: -44.832129-0.001034j
[2025-08-26 07:54:01] [Iter 1894/2250] R3[843/1200], Temp: 0.2029, Energy: -44.828974-0.003012j
[2025-08-26 07:54:27] [Iter 1895/2250] R3[844/1200], Temp: 0.2019, Energy: -44.829782+0.002346j
[2025-08-26 07:54:53] [Iter 1896/2250] R3[845/1200], Temp: 0.2008, Energy: -44.836558+0.000347j
[2025-08-26 07:55:19] [Iter 1897/2250] R3[846/1200], Temp: 0.1998, Energy: -44.834059+0.000733j
[2025-08-26 07:55:45] [Iter 1898/2250] R3[847/1200], Temp: 0.1987, Energy: -44.840054-0.001223j
[2025-08-26 07:56:11] [Iter 1899/2250] R3[848/1200], Temp: 0.1977, Energy: -44.842606-0.000360j
[2025-08-26 07:56:36] [Iter 1900/2250] R3[849/1200], Temp: 0.1967, Energy: -44.835425-0.000102j
[2025-08-26 07:57:02] [Iter 1901/2250] R3[850/1200], Temp: 0.1956, Energy: -44.833541+0.000668j
[2025-08-26 07:57:28] [Iter 1902/2250] R3[851/1200], Temp: 0.1946, Energy: -44.828839+0.000944j
[2025-08-26 07:57:54] [Iter 1903/2250] R3[852/1200], Temp: 0.1935, Energy: -44.839779+0.001404j
[2025-08-26 07:58:20] [Iter 1904/2250] R3[853/1200], Temp: 0.1925, Energy: -44.832084-0.002105j
[2025-08-26 07:58:46] [Iter 1905/2250] R3[854/1200], Temp: 0.1915, Energy: -44.835286-0.000411j
[2025-08-26 07:59:12] [Iter 1906/2250] R3[855/1200], Temp: 0.1905, Energy: -44.830306-0.000061j
[2025-08-26 07:59:38] [Iter 1907/2250] R3[856/1200], Temp: 0.1894, Energy: -44.828572+0.003483j
[2025-08-26 08:00:04] [Iter 1908/2250] R3[857/1200], Temp: 0.1884, Energy: -44.831388+0.001335j
[2025-08-26 08:00:30] [Iter 1909/2250] R3[858/1200], Temp: 0.1874, Energy: -44.833376+0.000730j
[2025-08-26 08:00:56] [Iter 1910/2250] R3[859/1200], Temp: 0.1864, Energy: -44.830951+0.000743j
[2025-08-26 08:01:21] [Iter 1911/2250] R3[860/1200], Temp: 0.1853, Energy: -44.846762+0.001319j
[2025-08-26 08:01:47] [Iter 1912/2250] R3[861/1200], Temp: 0.1843, Energy: -44.834302+0.000145j
[2025-08-26 08:02:13] [Iter 1913/2250] R3[862/1200], Temp: 0.1833, Energy: -44.836180-0.001063j
[2025-08-26 08:02:39] [Iter 1914/2250] R3[863/1200], Temp: 0.1823, Energy: -44.837427-0.000047j
[2025-08-26 08:03:05] [Iter 1915/2250] R3[864/1200], Temp: 0.1813, Energy: -44.836126-0.002603j
[2025-08-26 08:03:31] [Iter 1916/2250] R3[865/1200], Temp: 0.1803, Energy: -44.833264+0.003407j
[2025-08-26 08:03:57] [Iter 1917/2250] R3[866/1200], Temp: 0.1793, Energy: -44.834270-0.000496j
[2025-08-26 08:04:23] [Iter 1918/2250] R3[867/1200], Temp: 0.1783, Energy: -44.840042-0.002757j
[2025-08-26 08:04:49] [Iter 1919/2250] R3[868/1200], Temp: 0.1773, Energy: -44.825448+0.000930j
[2025-08-26 08:05:15] [Iter 1920/2250] R3[869/1200], Temp: 0.1763, Energy: -44.840953+0.002580j
[2025-08-26 08:05:41] [Iter 1921/2250] R3[870/1200], Temp: 0.1753, Energy: -44.835601+0.000069j
[2025-08-26 08:06:06] [Iter 1922/2250] R3[871/1200], Temp: 0.1743, Energy: -44.837117+0.000992j
[2025-08-26 08:06:32] [Iter 1923/2250] R3[872/1200], Temp: 0.1733, Energy: -44.830956-0.001111j
[2025-08-26 08:06:58] [Iter 1924/2250] R3[873/1200], Temp: 0.1723, Energy: -44.834992+0.001142j
[2025-08-26 08:07:24] [Iter 1925/2250] R3[874/1200], Temp: 0.1713, Energy: -44.840816-0.000889j
[2025-08-26 08:07:50] [Iter 1926/2250] R3[875/1200], Temp: 0.1703, Energy: -44.836920-0.002269j
[2025-08-26 08:08:16] [Iter 1927/2250] R3[876/1200], Temp: 0.1693, Energy: -44.831892-0.001271j
[2025-08-26 08:08:42] [Iter 1928/2250] R3[877/1200], Temp: 0.1684, Energy: -44.835235+0.000465j
[2025-08-26 08:09:08] [Iter 1929/2250] R3[878/1200], Temp: 0.1674, Energy: -44.840251+0.000659j
[2025-08-26 08:09:34] [Iter 1930/2250] R3[879/1200], Temp: 0.1664, Energy: -44.836583+0.002846j
[2025-08-26 08:10:00] [Iter 1931/2250] R3[880/1200], Temp: 0.1654, Energy: -44.823704+0.002615j
[2025-08-26 08:10:26] [Iter 1932/2250] R3[881/1200], Temp: 0.1645, Energy: -44.837039-0.001096j
[2025-08-26 08:10:52] [Iter 1933/2250] R3[882/1200], Temp: 0.1635, Energy: -44.830750+0.000921j
[2025-08-26 08:11:17] [Iter 1934/2250] R3[883/1200], Temp: 0.1625, Energy: -44.835139+0.000270j
[2025-08-26 08:11:43] [Iter 1935/2250] R3[884/1200], Temp: 0.1616, Energy: -44.845223-0.000027j
[2025-08-26 08:12:09] [Iter 1936/2250] R3[885/1200], Temp: 0.1606, Energy: -44.833067-0.001663j
[2025-08-26 08:12:35] [Iter 1937/2250] R3[886/1200], Temp: 0.1596, Energy: -44.843469-0.001349j
[2025-08-26 08:13:01] [Iter 1938/2250] R3[887/1200], Temp: 0.1587, Energy: -44.841302-0.001270j
[2025-08-26 08:13:27] [Iter 1939/2250] R3[888/1200], Temp: 0.1577, Energy: -44.841851+0.001723j
[2025-08-26 08:13:53] [Iter 1940/2250] R3[889/1200], Temp: 0.1568, Energy: -44.828542-0.000861j
[2025-08-26 08:14:19] [Iter 1941/2250] R3[890/1200], Temp: 0.1558, Energy: -44.841864+0.001511j
[2025-08-26 08:14:45] [Iter 1942/2250] R3[891/1200], Temp: 0.1549, Energy: -44.838977-0.000410j
[2025-08-26 08:15:11] [Iter 1943/2250] R3[892/1200], Temp: 0.1539, Energy: -44.833501-0.002596j
[2025-08-26 08:15:36] [Iter 1944/2250] R3[893/1200], Temp: 0.1530, Energy: -44.836267+0.002032j
[2025-08-26 08:16:02] [Iter 1945/2250] R3[894/1200], Temp: 0.1520, Energy: -44.836626-0.002294j
[2025-08-26 08:16:28] [Iter 1946/2250] R3[895/1200], Temp: 0.1511, Energy: -44.844819-0.002933j
[2025-08-26 08:16:54] [Iter 1947/2250] R3[896/1200], Temp: 0.1502, Energy: -44.841992+0.003929j
[2025-08-26 08:17:20] [Iter 1948/2250] R3[897/1200], Temp: 0.1492, Energy: -44.832476+0.006081j
[2025-08-26 08:17:46] [Iter 1949/2250] R3[898/1200], Temp: 0.1483, Energy: -44.837219-0.002572j
[2025-08-26 08:18:12] [Iter 1950/2250] R3[899/1200], Temp: 0.1474, Energy: -44.840425+0.001430j
[2025-08-26 08:18:38] [Iter 1951/2250] R3[900/1200], Temp: 0.1464, Energy: -44.833219+0.000565j
[2025-08-26 08:19:04] [Iter 1952/2250] R3[901/1200], Temp: 0.1455, Energy: -44.828771+0.001973j
[2025-08-26 08:19:30] [Iter 1953/2250] R3[902/1200], Temp: 0.1446, Energy: -44.833498+0.001037j
[2025-08-26 08:19:56] [Iter 1954/2250] R3[903/1200], Temp: 0.1437, Energy: -44.837129+0.001441j
[2025-08-26 08:20:21] [Iter 1955/2250] R3[904/1200], Temp: 0.1428, Energy: -44.836246-0.000337j
[2025-08-26 08:20:47] [Iter 1956/2250] R3[905/1200], Temp: 0.1418, Energy: -44.837120-0.001684j
[2025-08-26 08:21:13] [Iter 1957/2250] R3[906/1200], Temp: 0.1409, Energy: -44.827504+0.002269j
[2025-08-26 08:21:39] [Iter 1958/2250] R3[907/1200], Temp: 0.1400, Energy: -44.843416-0.001484j
[2025-08-26 08:22:05] [Iter 1959/2250] R3[908/1200], Temp: 0.1391, Energy: -44.846656+0.000540j
[2025-08-26 08:22:31] [Iter 1960/2250] R3[909/1200], Temp: 0.1382, Energy: -44.836512-0.001683j
[2025-08-26 08:22:57] [Iter 1961/2250] R3[910/1200], Temp: 0.1373, Energy: -44.826330+0.000228j
[2025-08-26 08:23:23] [Iter 1962/2250] R3[911/1200], Temp: 0.1364, Energy: -44.838932+0.002023j
[2025-08-26 08:23:49] [Iter 1963/2250] R3[912/1200], Temp: 0.1355, Energy: -44.839433-0.000792j
[2025-08-26 08:24:15] [Iter 1964/2250] R3[913/1200], Temp: 0.1346, Energy: -44.842286+0.001956j
[2025-08-26 08:24:41] [Iter 1965/2250] R3[914/1200], Temp: 0.1337, Energy: -44.832347-0.000512j
[2025-08-26 08:25:07] [Iter 1966/2250] R3[915/1200], Temp: 0.1328, Energy: -44.840570-0.001902j
[2025-08-26 08:25:33] [Iter 1967/2250] R3[916/1200], Temp: 0.1320, Energy: -44.822485-0.000211j
[2025-08-26 08:25:59] [Iter 1968/2250] R3[917/1200], Temp: 0.1311, Energy: -44.833657+0.002141j
[2025-08-26 08:26:24] [Iter 1969/2250] R3[918/1200], Temp: 0.1302, Energy: -44.840258-0.001163j
[2025-08-26 08:26:50] [Iter 1970/2250] R3[919/1200], Temp: 0.1293, Energy: -44.836893-0.000330j
[2025-08-26 08:27:16] [Iter 1971/2250] R3[920/1200], Temp: 0.1284, Energy: -44.840727+0.000779j
[2025-08-26 08:27:42] [Iter 1972/2250] R3[921/1200], Temp: 0.1276, Energy: -44.830702+0.002492j
[2025-08-26 08:28:08] [Iter 1973/2250] R3[922/1200], Temp: 0.1267, Energy: -44.834746-0.001119j
[2025-08-26 08:28:34] [Iter 1974/2250] R3[923/1200], Temp: 0.1258, Energy: -44.836784-0.000597j
[2025-08-26 08:29:00] [Iter 1975/2250] R3[924/1200], Temp: 0.1249, Energy: -44.831874+0.001767j
[2025-08-26 08:29:26] [Iter 1976/2250] R3[925/1200], Temp: 0.1241, Energy: -44.838947+0.000153j
[2025-08-26 08:29:52] [Iter 1977/2250] R3[926/1200], Temp: 0.1232, Energy: -44.840175-0.001880j
[2025-08-26 08:30:18] [Iter 1978/2250] R3[927/1200], Temp: 0.1224, Energy: -44.842683+0.000121j
[2025-08-26 08:30:43] [Iter 1979/2250] R3[928/1200], Temp: 0.1215, Energy: -44.845176-0.000933j
[2025-08-26 08:31:09] [Iter 1980/2250] R3[929/1200], Temp: 0.1206, Energy: -44.833020+0.002338j
[2025-08-26 08:31:35] [Iter 1981/2250] R3[930/1200], Temp: 0.1198, Energy: -44.833360-0.003406j
[2025-08-26 08:32:01] [Iter 1982/2250] R3[931/1200], Temp: 0.1189, Energy: -44.835181+0.001789j
[2025-08-26 08:32:27] [Iter 1983/2250] R3[932/1200], Temp: 0.1181, Energy: -44.834198-0.000103j
[2025-08-26 08:32:53] [Iter 1984/2250] R3[933/1200], Temp: 0.1173, Energy: -44.835849+0.001332j
[2025-08-26 08:33:19] [Iter 1985/2250] R3[934/1200], Temp: 0.1164, Energy: -44.835863+0.001588j
[2025-08-26 08:33:45] [Iter 1986/2250] R3[935/1200], Temp: 0.1156, Energy: -44.835543-0.000728j
[2025-08-26 08:34:11] [Iter 1987/2250] R3[936/1200], Temp: 0.1147, Energy: -44.841852-0.000069j
[2025-08-26 08:34:37] [Iter 1988/2250] R3[937/1200], Temp: 0.1139, Energy: -44.833846+0.000456j
[2025-08-26 08:35:03] [Iter 1989/2250] R3[938/1200], Temp: 0.1131, Energy: -44.839800-0.000349j
[2025-08-26 08:35:28] [Iter 1990/2250] R3[939/1200], Temp: 0.1123, Energy: -44.847376-0.000870j
[2025-08-26 08:35:54] [Iter 1991/2250] R3[940/1200], Temp: 0.1114, Energy: -44.837475+0.002778j
[2025-08-26 08:36:20] [Iter 1992/2250] R3[941/1200], Temp: 0.1106, Energy: -44.837150-0.000718j
[2025-08-26 08:36:46] [Iter 1993/2250] R3[942/1200], Temp: 0.1098, Energy: -44.843765+0.001546j
[2025-08-26 08:37:12] [Iter 1994/2250] R3[943/1200], Temp: 0.1090, Energy: -44.838343-0.001009j
[2025-08-26 08:37:38] [Iter 1995/2250] R3[944/1200], Temp: 0.1082, Energy: -44.840739-0.001991j
[2025-08-26 08:38:04] [Iter 1996/2250] R3[945/1200], Temp: 0.1073, Energy: -44.838685+0.000126j
[2025-08-26 08:38:30] [Iter 1997/2250] R3[946/1200], Temp: 0.1065, Energy: -44.839933-0.001483j
[2025-08-26 08:38:56] [Iter 1998/2250] R3[947/1200], Temp: 0.1057, Energy: -44.827054+0.000369j
[2025-08-26 08:39:22] [Iter 1999/2250] R3[948/1200], Temp: 0.1049, Energy: -44.832788+0.000812j
[2025-08-26 08:39:48] [Iter 2000/2250] R3[949/1200], Temp: 0.1041, Energy: -44.839464-0.003225j
[2025-08-26 08:39:48] ✓ Checkpoint saved: checkpoint_iter_002000.pkl
[2025-08-26 08:40:14] [Iter 2001/2250] R3[950/1200], Temp: 0.1033, Energy: -44.838436-0.004155j
[2025-08-26 08:40:39] [Iter 2002/2250] R3[951/1200], Temp: 0.1025, Energy: -44.840153+0.001106j
[2025-08-26 08:41:05] [Iter 2003/2250] R3[952/1200], Temp: 0.1017, Energy: -44.839933+0.001342j
[2025-08-26 08:41:31] [Iter 2004/2250] R3[953/1200], Temp: 0.1009, Energy: -44.832695+0.002835j
[2025-08-26 08:41:57] [Iter 2005/2250] R3[954/1200], Temp: 0.1002, Energy: -44.840691-0.002202j
[2025-08-26 08:42:23] [Iter 2006/2250] R3[955/1200], Temp: 0.0994, Energy: -44.837129-0.002821j
[2025-08-26 08:42:49] [Iter 2007/2250] R3[956/1200], Temp: 0.0986, Energy: -44.836272-0.000435j
[2025-08-26 08:43:15] [Iter 2008/2250] R3[957/1200], Temp: 0.0978, Energy: -44.831270+0.000527j
[2025-08-26 08:43:41] [Iter 2009/2250] R3[958/1200], Temp: 0.0970, Energy: -44.839300+0.001850j
[2025-08-26 08:44:07] [Iter 2010/2250] R3[959/1200], Temp: 0.0963, Energy: -44.837826+0.000376j
[2025-08-26 08:44:33] [Iter 2011/2250] R3[960/1200], Temp: 0.0955, Energy: -44.838262-0.002684j
[2025-08-26 08:44:59] [Iter 2012/2250] R3[961/1200], Temp: 0.0947, Energy: -44.843943-0.001431j
[2025-08-26 08:45:24] [Iter 2013/2250] R3[962/1200], Temp: 0.0940, Energy: -44.840458+0.001145j
[2025-08-26 08:45:50] [Iter 2014/2250] R3[963/1200], Temp: 0.0932, Energy: -44.834696-0.000054j
[2025-08-26 08:46:16] [Iter 2015/2250] R3[964/1200], Temp: 0.0924, Energy: -44.838644-0.002575j
[2025-08-26 08:46:42] [Iter 2016/2250] R3[965/1200], Temp: 0.0917, Energy: -44.835621-0.000092j
[2025-08-26 08:47:08] [Iter 2017/2250] R3[966/1200], Temp: 0.0909, Energy: -44.832192+0.000875j
[2025-08-26 08:47:34] [Iter 2018/2250] R3[967/1200], Temp: 0.0902, Energy: -44.835336+0.000302j
[2025-08-26 08:48:00] [Iter 2019/2250] R3[968/1200], Temp: 0.0894, Energy: -44.840698+0.000169j
[2025-08-26 08:48:26] [Iter 2020/2250] R3[969/1200], Temp: 0.0887, Energy: -44.835282-0.002689j
[2025-08-26 08:48:52] [Iter 2021/2250] R3[970/1200], Temp: 0.0879, Energy: -44.840633-0.000721j
[2025-08-26 08:49:18] [Iter 2022/2250] R3[971/1200], Temp: 0.0872, Energy: -44.828082+0.002854j
[2025-08-26 08:49:44] [Iter 2023/2250] R3[972/1200], Temp: 0.0865, Energy: -44.837768-0.001515j
[2025-08-26 08:50:09] [Iter 2024/2250] R3[973/1200], Temp: 0.0857, Energy: -44.843236+0.000546j
[2025-08-26 08:50:35] [Iter 2025/2250] R3[974/1200], Temp: 0.0850, Energy: -44.835172+0.001738j
[2025-08-26 08:51:01] [Iter 2026/2250] R3[975/1200], Temp: 0.0843, Energy: -44.838205-0.001339j
[2025-08-26 08:51:27] [Iter 2027/2250] R3[976/1200], Temp: 0.0835, Energy: -44.833360+0.000093j
[2025-08-26 08:51:53] [Iter 2028/2250] R3[977/1200], Temp: 0.0828, Energy: -44.837633-0.001570j
[2025-08-26 08:52:19] [Iter 2029/2250] R3[978/1200], Temp: 0.0821, Energy: -44.839519+0.003222j
[2025-08-26 08:52:45] [Iter 2030/2250] R3[979/1200], Temp: 0.0814, Energy: -44.839304-0.000480j
[2025-08-26 08:53:11] [Iter 2031/2250] R3[980/1200], Temp: 0.0807, Energy: -44.839189+0.002539j
[2025-08-26 08:53:37] [Iter 2032/2250] R3[981/1200], Temp: 0.0800, Energy: -44.843082+0.000771j
[2025-08-26 08:54:03] [Iter 2033/2250] R3[982/1200], Temp: 0.0792, Energy: -44.819798+0.000340j
[2025-08-26 08:54:29] [Iter 2034/2250] R3[983/1200], Temp: 0.0785, Energy: -44.845236+0.000537j
[2025-08-26 08:54:54] [Iter 2035/2250] R3[984/1200], Temp: 0.0778, Energy: -44.848382-0.000480j
[2025-08-26 08:55:20] [Iter 2036/2250] R3[985/1200], Temp: 0.0771, Energy: -44.837533+0.000248j
[2025-08-26 08:55:46] [Iter 2037/2250] R3[986/1200], Temp: 0.0764, Energy: -44.843504-0.000098j
[2025-08-26 08:56:12] [Iter 2038/2250] R3[987/1200], Temp: 0.0757, Energy: -44.842773-0.000367j
[2025-08-26 08:56:38] [Iter 2039/2250] R3[988/1200], Temp: 0.0751, Energy: -44.832861-0.000720j
[2025-08-26 08:57:04] [Iter 2040/2250] R3[989/1200], Temp: 0.0744, Energy: -44.837170-0.002960j
[2025-08-26 08:57:30] [Iter 2041/2250] R3[990/1200], Temp: 0.0737, Energy: -44.838604+0.000875j
[2025-08-26 08:57:56] [Iter 2042/2250] R3[991/1200], Temp: 0.0730, Energy: -44.841818-0.000712j
[2025-08-26 08:58:22] [Iter 2043/2250] R3[992/1200], Temp: 0.0723, Energy: -44.840676+0.002820j
[2025-08-26 08:58:48] [Iter 2044/2250] R3[993/1200], Temp: 0.0716, Energy: -44.840223+0.001211j
[2025-08-26 08:59:14] [Iter 2045/2250] R3[994/1200], Temp: 0.0710, Energy: -44.835151+0.000442j
[2025-08-26 08:59:39] [Iter 2046/2250] R3[995/1200], Temp: 0.0703, Energy: -44.844303+0.000477j
[2025-08-26 09:00:05] [Iter 2047/2250] R3[996/1200], Temp: 0.0696, Energy: -44.838472+0.002649j
[2025-08-26 09:00:31] [Iter 2048/2250] R3[997/1200], Temp: 0.0690, Energy: -44.841118-0.001360j
[2025-08-26 09:00:57] [Iter 2049/2250] R3[998/1200], Temp: 0.0683, Energy: -44.839133-0.001075j
[2025-08-26 09:01:23] [Iter 2050/2250] R3[999/1200], Temp: 0.0676, Energy: -44.838842+0.000214j
[2025-08-26 09:01:49] [Iter 2051/2250] R3[1000/1200], Temp: 0.0670, Energy: -44.837793+0.000500j
[2025-08-26 09:02:15] [Iter 2052/2250] R3[1001/1200], Temp: 0.0663, Energy: -44.840616+0.000765j
[2025-08-26 09:02:41] [Iter 2053/2250] R3[1002/1200], Temp: 0.0657, Energy: -44.840815+0.001148j
[2025-08-26 09:03:07] [Iter 2054/2250] R3[1003/1200], Temp: 0.0650, Energy: -44.838208-0.001538j
[2025-08-26 09:03:33] [Iter 2055/2250] R3[1004/1200], Temp: 0.0644, Energy: -44.849962-0.000180j
[2025-08-26 09:03:59] [Iter 2056/2250] R3[1005/1200], Temp: 0.0638, Energy: -44.845620-0.004159j
[2025-08-26 09:04:24] [Iter 2057/2250] R3[1006/1200], Temp: 0.0631, Energy: -44.835982-0.001631j
[2025-08-26 09:04:50] [Iter 2058/2250] R3[1007/1200], Temp: 0.0625, Energy: -44.837302-0.000719j
[2025-08-26 09:05:16] [Iter 2059/2250] R3[1008/1200], Temp: 0.0618, Energy: -44.841259+0.003198j
[2025-08-26 09:05:42] [Iter 2060/2250] R3[1009/1200], Temp: 0.0612, Energy: -44.849140+0.000375j
[2025-08-26 09:06:08] [Iter 2061/2250] R3[1010/1200], Temp: 0.0606, Energy: -44.841222-0.000471j
[2025-08-26 09:06:34] [Iter 2062/2250] R3[1011/1200], Temp: 0.0600, Energy: -44.844774-0.000828j
[2025-08-26 09:07:00] [Iter 2063/2250] R3[1012/1200], Temp: 0.0593, Energy: -44.844665+0.002811j
[2025-08-26 09:07:26] [Iter 2064/2250] R3[1013/1200], Temp: 0.0587, Energy: -44.837331+0.000962j
[2025-08-26 09:07:52] [Iter 2065/2250] R3[1014/1200], Temp: 0.0581, Energy: -44.843003-0.000327j
[2025-08-26 09:08:18] [Iter 2066/2250] R3[1015/1200], Temp: 0.0575, Energy: -44.843228-0.000108j
[2025-08-26 09:08:44] [Iter 2067/2250] R3[1016/1200], Temp: 0.0569, Energy: -44.848521-0.001750j
[2025-08-26 09:09:09] [Iter 2068/2250] R3[1017/1200], Temp: 0.0563, Energy: -44.829620-0.001572j
[2025-08-26 09:09:35] [Iter 2069/2250] R3[1018/1200], Temp: 0.0557, Energy: -44.842452+0.002532j
[2025-08-26 09:10:01] [Iter 2070/2250] R3[1019/1200], Temp: 0.0551, Energy: -44.848081+0.001132j
[2025-08-26 09:10:27] [Iter 2071/2250] R3[1020/1200], Temp: 0.0545, Energy: -44.838198-0.001105j
[2025-08-26 09:10:53] [Iter 2072/2250] R3[1021/1200], Temp: 0.0539, Energy: -44.835521+0.003589j
[2025-08-26 09:11:19] [Iter 2073/2250] R3[1022/1200], Temp: 0.0533, Energy: -44.825834+0.001471j
[2025-08-26 09:11:45] [Iter 2074/2250] R3[1023/1200], Temp: 0.0527, Energy: -44.834108-0.001923j
[2025-08-26 09:12:11] [Iter 2075/2250] R3[1024/1200], Temp: 0.0521, Energy: -44.844574-0.002041j
[2025-08-26 09:12:37] [Iter 2076/2250] R3[1025/1200], Temp: 0.0516, Energy: -44.839692-0.000062j
[2025-08-26 09:13:03] [Iter 2077/2250] R3[1026/1200], Temp: 0.0510, Energy: -44.842397-0.001560j
[2025-08-26 09:13:29] [Iter 2078/2250] R3[1027/1200], Temp: 0.0504, Energy: -44.841172-0.001026j
[2025-08-26 09:13:54] [Iter 2079/2250] R3[1028/1200], Temp: 0.0498, Energy: -44.837089+0.001835j
[2025-08-26 09:14:20] [Iter 2080/2250] R3[1029/1200], Temp: 0.0493, Energy: -44.840787-0.001502j
[2025-08-26 09:14:46] [Iter 2081/2250] R3[1030/1200], Temp: 0.0487, Energy: -44.845538+0.001776j
[2025-08-26 09:15:12] [Iter 2082/2250] R3[1031/1200], Temp: 0.0481, Energy: -44.843174+0.003043j
[2025-08-26 09:15:38] [Iter 2083/2250] R3[1032/1200], Temp: 0.0476, Energy: -44.837197-0.001019j
[2025-08-26 09:16:04] [Iter 2084/2250] R3[1033/1200], Temp: 0.0470, Energy: -44.842339+0.000860j
[2025-08-26 09:16:30] [Iter 2085/2250] R3[1034/1200], Temp: 0.0465, Energy: -44.832916-0.001405j
[2025-08-26 09:16:56] [Iter 2086/2250] R3[1035/1200], Temp: 0.0459, Energy: -44.845162+0.002521j
[2025-08-26 09:17:22] [Iter 2087/2250] R3[1036/1200], Temp: 0.0454, Energy: -44.838451-0.000220j
[2025-08-26 09:17:48] [Iter 2088/2250] R3[1037/1200], Temp: 0.0448, Energy: -44.842097+0.000446j
[2025-08-26 09:18:14] [Iter 2089/2250] R3[1038/1200], Temp: 0.0443, Energy: -44.837180-0.000673j
[2025-08-26 09:18:39] [Iter 2090/2250] R3[1039/1200], Temp: 0.0438, Energy: -44.844234+0.001194j
[2025-08-26 09:19:05] [Iter 2091/2250] R3[1040/1200], Temp: 0.0432, Energy: -44.834113+0.000030j
[2025-08-26 09:19:31] [Iter 2092/2250] R3[1041/1200], Temp: 0.0427, Energy: -44.835109-0.001037j
[2025-08-26 09:19:57] [Iter 2093/2250] R3[1042/1200], Temp: 0.0422, Energy: -44.845497-0.000129j
[2025-08-26 09:20:23] [Iter 2094/2250] R3[1043/1200], Temp: 0.0416, Energy: -44.838778+0.000346j
[2025-08-26 09:20:49] [Iter 2095/2250] R3[1044/1200], Temp: 0.0411, Energy: -44.835537-0.002354j
[2025-08-26 09:21:15] [Iter 2096/2250] R3[1045/1200], Temp: 0.0406, Energy: -44.837897+0.002662j
[2025-08-26 09:21:41] [Iter 2097/2250] R3[1046/1200], Temp: 0.0401, Energy: -44.833587+0.000547j
[2025-08-26 09:22:07] [Iter 2098/2250] R3[1047/1200], Temp: 0.0396, Energy: -44.831590-0.000752j
[2025-08-26 09:22:33] [Iter 2099/2250] R3[1048/1200], Temp: 0.0391, Energy: -44.837929+0.000644j
[2025-08-26 09:22:58] [Iter 2100/2250] R3[1049/1200], Temp: 0.0386, Energy: -44.838757+0.001116j
[2025-08-26 09:23:24] [Iter 2101/2250] R3[1050/1200], Temp: 0.0381, Energy: -44.842533-0.000419j
[2025-08-26 09:23:50] [Iter 2102/2250] R3[1051/1200], Temp: 0.0376, Energy: -44.840535+0.000210j
[2025-08-26 09:24:16] [Iter 2103/2250] R3[1052/1200], Temp: 0.0371, Energy: -44.830444+0.000279j
[2025-08-26 09:24:42] [Iter 2104/2250] R3[1053/1200], Temp: 0.0366, Energy: -44.834052-0.001150j
[2025-08-26 09:25:08] [Iter 2105/2250] R3[1054/1200], Temp: 0.0361, Energy: -44.844758+0.000243j
[2025-08-26 09:25:34] [Iter 2106/2250] R3[1055/1200], Temp: 0.0356, Energy: -44.837741-0.000651j
[2025-08-26 09:26:00] [Iter 2107/2250] R3[1056/1200], Temp: 0.0351, Energy: -44.844807-0.002440j
[2025-08-26 09:26:26] [Iter 2108/2250] R3[1057/1200], Temp: 0.0346, Energy: -44.848650+0.002254j
[2025-08-26 09:26:52] [Iter 2109/2250] R3[1058/1200], Temp: 0.0342, Energy: -44.841880+0.002724j
[2025-08-26 09:27:18] [Iter 2110/2250] R3[1059/1200], Temp: 0.0337, Energy: -44.840867+0.000979j
[2025-08-26 09:27:44] [Iter 2111/2250] R3[1060/1200], Temp: 0.0332, Energy: -44.839076+0.000393j
[2025-08-26 09:28:09] [Iter 2112/2250] R3[1061/1200], Temp: 0.0327, Energy: -44.846542+0.000570j
[2025-08-26 09:28:35] [Iter 2113/2250] R3[1062/1200], Temp: 0.0323, Energy: -44.835368-0.001846j
[2025-08-26 09:29:01] [Iter 2114/2250] R3[1063/1200], Temp: 0.0318, Energy: -44.833275-0.001079j
[2025-08-26 09:29:27] [Iter 2115/2250] R3[1064/1200], Temp: 0.0314, Energy: -44.848747-0.002642j
[2025-08-26 09:29:53] [Iter 2116/2250] R3[1065/1200], Temp: 0.0309, Energy: -44.836006+0.002599j
[2025-08-26 09:30:19] [Iter 2117/2250] R3[1066/1200], Temp: 0.0305, Energy: -44.842457-0.000274j
[2025-08-26 09:30:45] [Iter 2118/2250] R3[1067/1200], Temp: 0.0300, Energy: -44.848536-0.000433j
[2025-08-26 09:31:11] [Iter 2119/2250] R3[1068/1200], Temp: 0.0296, Energy: -44.835302-0.001875j
[2025-08-26 09:31:37] [Iter 2120/2250] R3[1069/1200], Temp: 0.0291, Energy: -44.841558-0.000545j
[2025-08-26 09:32:03] [Iter 2121/2250] R3[1070/1200], Temp: 0.0287, Energy: -44.839118+0.000693j
[2025-08-26 09:32:28] [Iter 2122/2250] R3[1071/1200], Temp: 0.0282, Energy: -44.837637+0.001864j
[2025-08-26 09:32:54] [Iter 2123/2250] R3[1072/1200], Temp: 0.0278, Energy: -44.841816+0.002741j
[2025-08-26 09:33:20] [Iter 2124/2250] R3[1073/1200], Temp: 0.0274, Energy: -44.844616+0.000342j
[2025-08-26 09:33:46] [Iter 2125/2250] R3[1074/1200], Temp: 0.0270, Energy: -44.837620+0.000145j
[2025-08-26 09:34:12] [Iter 2126/2250] R3[1075/1200], Temp: 0.0265, Energy: -44.833215+0.002396j
[2025-08-26 09:34:38] [Iter 2127/2250] R3[1076/1200], Temp: 0.0261, Energy: -44.838037-0.000315j
[2025-08-26 09:35:04] [Iter 2128/2250] R3[1077/1200], Temp: 0.0257, Energy: -44.845226+0.001158j
[2025-08-26 09:35:30] [Iter 2129/2250] R3[1078/1200], Temp: 0.0253, Energy: -44.834144+0.002058j
[2025-08-26 09:35:56] [Iter 2130/2250] R3[1079/1200], Temp: 0.0249, Energy: -44.842790-0.001063j
[2025-08-26 09:36:22] [Iter 2131/2250] R3[1080/1200], Temp: 0.0245, Energy: -44.841378-0.000810j
[2025-08-26 09:36:48] [Iter 2132/2250] R3[1081/1200], Temp: 0.0241, Energy: -44.841140-0.000210j
[2025-08-26 09:37:13] [Iter 2133/2250] R3[1082/1200], Temp: 0.0237, Energy: -44.843552+0.000894j
[2025-08-26 09:37:39] [Iter 2134/2250] R3[1083/1200], Temp: 0.0233, Energy: -44.834896-0.001945j
[2025-08-26 09:38:05] [Iter 2135/2250] R3[1084/1200], Temp: 0.0229, Energy: -44.838593+0.001941j
[2025-08-26 09:38:31] [Iter 2136/2250] R3[1085/1200], Temp: 0.0225, Energy: -44.839232+0.002487j
[2025-08-26 09:38:57] [Iter 2137/2250] R3[1086/1200], Temp: 0.0221, Energy: -44.850717+0.001223j
[2025-08-26 09:39:23] [Iter 2138/2250] R3[1087/1200], Temp: 0.0217, Energy: -44.847076-0.000505j
[2025-08-26 09:39:49] [Iter 2139/2250] R3[1088/1200], Temp: 0.0213, Energy: -44.839874-0.001585j
[2025-08-26 09:40:15] [Iter 2140/2250] R3[1089/1200], Temp: 0.0210, Energy: -44.846625+0.000082j
[2025-08-26 09:40:41] [Iter 2141/2250] R3[1090/1200], Temp: 0.0206, Energy: -44.844192+0.000278j
[2025-08-26 09:41:07] [Iter 2142/2250] R3[1091/1200], Temp: 0.0202, Energy: -44.842158+0.001294j
[2025-08-26 09:41:33] [Iter 2143/2250] R3[1092/1200], Temp: 0.0199, Energy: -44.846883-0.001103j
[2025-08-26 09:41:59] [Iter 2144/2250] R3[1093/1200], Temp: 0.0195, Energy: -44.839458-0.000797j
[2025-08-26 09:42:24] [Iter 2145/2250] R3[1094/1200], Temp: 0.0191, Energy: -44.838837-0.000594j
[2025-08-26 09:42:50] [Iter 2146/2250] R3[1095/1200], Temp: 0.0188, Energy: -44.844350-0.001363j
[2025-08-26 09:43:16] [Iter 2147/2250] R3[1096/1200], Temp: 0.0184, Energy: -44.846888-0.002788j
[2025-08-26 09:43:42] [Iter 2148/2250] R3[1097/1200], Temp: 0.0181, Energy: -44.845736+0.000533j
[2025-08-26 09:44:08] [Iter 2149/2250] R3[1098/1200], Temp: 0.0177, Energy: -44.851157+0.000939j
[2025-08-26 09:44:34] [Iter 2150/2250] R3[1099/1200], Temp: 0.0174, Energy: -44.836458+0.002243j
[2025-08-26 09:45:00] [Iter 2151/2250] R3[1100/1200], Temp: 0.0170, Energy: -44.837830+0.005496j
[2025-08-26 09:45:26] [Iter 2152/2250] R3[1101/1200], Temp: 0.0167, Energy: -44.840127+0.000406j
[2025-08-26 09:45:52] [Iter 2153/2250] R3[1102/1200], Temp: 0.0164, Energy: -44.843548+0.003298j
[2025-08-26 09:46:18] [Iter 2154/2250] R3[1103/1200], Temp: 0.0160, Energy: -44.839553+0.000243j
[2025-08-26 09:46:44] [Iter 2155/2250] R3[1104/1200], Temp: 0.0157, Energy: -44.844849-0.000216j
[2025-08-26 09:47:09] [Iter 2156/2250] R3[1105/1200], Temp: 0.0154, Energy: -44.840122-0.001631j
[2025-08-26 09:47:35] [Iter 2157/2250] R3[1106/1200], Temp: 0.0151, Energy: -44.837159-0.003950j
[2025-08-26 09:48:01] [Iter 2158/2250] R3[1107/1200], Temp: 0.0147, Energy: -44.838445+0.000839j
[2025-08-26 09:48:27] [Iter 2159/2250] R3[1108/1200], Temp: 0.0144, Energy: -44.843063+0.000488j
[2025-08-26 09:48:53] [Iter 2160/2250] R3[1109/1200], Temp: 0.0141, Energy: -44.844302-0.001180j
[2025-08-26 09:49:19] [Iter 2161/2250] R3[1110/1200], Temp: 0.0138, Energy: -44.837587-0.000056j
[2025-08-26 09:49:45] [Iter 2162/2250] R3[1111/1200], Temp: 0.0135, Energy: -44.842521-0.001531j
[2025-08-26 09:50:11] [Iter 2163/2250] R3[1112/1200], Temp: 0.0132, Energy: -44.844607-0.001110j
[2025-08-26 09:50:37] [Iter 2164/2250] R3[1113/1200], Temp: 0.0129, Energy: -44.840949-0.000768j
[2025-08-26 09:51:03] [Iter 2165/2250] R3[1114/1200], Temp: 0.0126, Energy: -44.835509+0.002439j
[2025-08-26 09:51:29] [Iter 2166/2250] R3[1115/1200], Temp: 0.0123, Energy: -44.845320+0.000553j
[2025-08-26 09:51:55] [Iter 2167/2250] R3[1116/1200], Temp: 0.0120, Energy: -44.840476+0.002184j
[2025-08-26 09:52:20] [Iter 2168/2250] R3[1117/1200], Temp: 0.0118, Energy: -44.841227-0.002875j
[2025-08-26 09:52:46] [Iter 2169/2250] R3[1118/1200], Temp: 0.0115, Energy: -44.845081+0.000973j
[2025-08-26 09:53:12] [Iter 2170/2250] R3[1119/1200], Temp: 0.0112, Energy: -44.841976+0.000999j
[2025-08-26 09:53:38] [Iter 2171/2250] R3[1120/1200], Temp: 0.0109, Energy: -44.835082-0.001749j
[2025-08-26 09:54:04] [Iter 2172/2250] R3[1121/1200], Temp: 0.0107, Energy: -44.838172+0.000766j
[2025-08-26 09:54:30] [Iter 2173/2250] R3[1122/1200], Temp: 0.0104, Energy: -44.846484-0.000622j
[2025-08-26 09:54:56] [Iter 2174/2250] R3[1123/1200], Temp: 0.0101, Energy: -44.840810+0.000067j
[2025-08-26 09:55:22] [Iter 2175/2250] R3[1124/1200], Temp: 0.0099, Energy: -44.840907+0.000622j
[2025-08-26 09:55:48] [Iter 2176/2250] R3[1125/1200], Temp: 0.0096, Energy: -44.850198-0.000345j
[2025-08-26 09:56:14] [Iter 2177/2250] R3[1126/1200], Temp: 0.0094, Energy: -44.844070+0.000693j
[2025-08-26 09:56:40] [Iter 2178/2250] R3[1127/1200], Temp: 0.0091, Energy: -44.844348+0.001852j
[2025-08-26 09:57:05] [Iter 2179/2250] R3[1128/1200], Temp: 0.0089, Energy: -44.844708-0.001865j
[2025-08-26 09:57:31] [Iter 2180/2250] R3[1129/1200], Temp: 0.0086, Energy: -44.838119+0.002091j
[2025-08-26 09:57:57] [Iter 2181/2250] R3[1130/1200], Temp: 0.0084, Energy: -44.846183+0.000368j
[2025-08-26 09:58:23] [Iter 2182/2250] R3[1131/1200], Temp: 0.0081, Energy: -44.839686+0.000428j
[2025-08-26 09:58:49] [Iter 2183/2250] R3[1132/1200], Temp: 0.0079, Energy: -44.846641-0.000258j
[2025-08-26 09:59:15] [Iter 2184/2250] R3[1133/1200], Temp: 0.0077, Energy: -44.843669+0.002330j
[2025-08-26 09:59:41] [Iter 2185/2250] R3[1134/1200], Temp: 0.0074, Energy: -44.844552+0.000527j
[2025-08-26 10:00:07] [Iter 2186/2250] R3[1135/1200], Temp: 0.0072, Energy: -44.841303-0.000190j
[2025-08-26 10:00:33] [Iter 2187/2250] R3[1136/1200], Temp: 0.0070, Energy: -44.841814-0.001445j
[2025-08-26 10:00:59] [Iter 2188/2250] R3[1137/1200], Temp: 0.0068, Energy: -44.845325+0.000143j
[2025-08-26 10:01:25] [Iter 2189/2250] R3[1138/1200], Temp: 0.0066, Energy: -44.849823-0.001756j
[2025-08-26 10:01:51] [Iter 2190/2250] R3[1139/1200], Temp: 0.0064, Energy: -44.855962-0.002192j
[2025-08-26 10:02:16] [Iter 2191/2250] R3[1140/1200], Temp: 0.0062, Energy: -44.836658+0.002782j
[2025-08-26 10:02:42] [Iter 2192/2250] R3[1141/1200], Temp: 0.0060, Energy: -44.841973-0.000215j
[2025-08-26 10:03:08] [Iter 2193/2250] R3[1142/1200], Temp: 0.0058, Energy: -44.843485+0.003582j
[2025-08-26 10:03:34] [Iter 2194/2250] R3[1143/1200], Temp: 0.0056, Energy: -44.848112-0.000793j
[2025-08-26 10:04:00] [Iter 2195/2250] R3[1144/1200], Temp: 0.0054, Energy: -44.844841-0.001947j
[2025-08-26 10:04:26] [Iter 2196/2250] R3[1145/1200], Temp: 0.0052, Energy: -44.842581+0.003013j
[2025-08-26 10:04:52] [Iter 2197/2250] R3[1146/1200], Temp: 0.0050, Energy: -44.844717+0.000960j
[2025-08-26 10:05:18] [Iter 2198/2250] R3[1147/1200], Temp: 0.0048, Energy: -44.848619+0.000618j
[2025-08-26 10:05:44] [Iter 2199/2250] R3[1148/1200], Temp: 0.0046, Energy: -44.844016-0.000158j
[2025-08-26 10:06:10] [Iter 2200/2250] R3[1149/1200], Temp: 0.0045, Energy: -44.842897+0.000437j
[2025-08-26 10:06:35] [Iter 2201/2250] R3[1150/1200], Temp: 0.0043, Energy: -44.848525-0.000115j
[2025-08-26 10:07:01] [Iter 2202/2250] R3[1151/1200], Temp: 0.0041, Energy: -44.851658-0.001650j
[2025-08-26 10:07:27] [Iter 2203/2250] R3[1152/1200], Temp: 0.0039, Energy: -44.842049+0.000961j
[2025-08-26 10:07:53] [Iter 2204/2250] R3[1153/1200], Temp: 0.0038, Energy: -44.850467+0.000394j
[2025-08-26 10:08:19] [Iter 2205/2250] R3[1154/1200], Temp: 0.0036, Energy: -44.845483+0.001175j
[2025-08-26 10:08:45] [Iter 2206/2250] R3[1155/1200], Temp: 0.0035, Energy: -44.844654+0.000372j
[2025-08-26 10:09:11] [Iter 2207/2250] R3[1156/1200], Temp: 0.0033, Energy: -44.842873-0.001688j
[2025-08-26 10:09:37] [Iter 2208/2250] R3[1157/1200], Temp: 0.0032, Energy: -44.847696-0.001076j
[2025-08-26 10:10:03] [Iter 2209/2250] R3[1158/1200], Temp: 0.0030, Energy: -44.849724+0.000427j
[2025-08-26 10:10:29] [Iter 2210/2250] R3[1159/1200], Temp: 0.0029, Energy: -44.845329+0.004442j
[2025-08-26 10:10:55] [Iter 2211/2250] R3[1160/1200], Temp: 0.0027, Energy: -44.846714+0.000067j
[2025-08-26 10:11:21] [Iter 2212/2250] R3[1161/1200], Temp: 0.0026, Energy: -44.849659+0.000822j
[2025-08-26 10:11:46] [Iter 2213/2250] R3[1162/1200], Temp: 0.0025, Energy: -44.839985+0.001739j
[2025-08-26 10:12:12] [Iter 2214/2250] R3[1163/1200], Temp: 0.0023, Energy: -44.842992-0.001227j
[2025-08-26 10:12:38] [Iter 2215/2250] R3[1164/1200], Temp: 0.0022, Energy: -44.841459-0.000603j
[2025-08-26 10:13:04] [Iter 2216/2250] R3[1165/1200], Temp: 0.0021, Energy: -44.839768+0.001327j
[2025-08-26 10:13:30] [Iter 2217/2250] R3[1166/1200], Temp: 0.0020, Energy: -44.844694-0.000732j
[2025-08-26 10:13:56] [Iter 2218/2250] R3[1167/1200], Temp: 0.0019, Energy: -44.841216-0.000571j
[2025-08-26 10:14:22] [Iter 2219/2250] R3[1168/1200], Temp: 0.0018, Energy: -44.840553+0.001211j
[2025-08-26 10:14:48] [Iter 2220/2250] R3[1169/1200], Temp: 0.0016, Energy: -44.846381+0.001113j
[2025-08-26 10:15:14] [Iter 2221/2250] R3[1170/1200], Temp: 0.0015, Energy: -44.847046+0.000178j
[2025-08-26 10:15:40] [Iter 2222/2250] R3[1171/1200], Temp: 0.0014, Energy: -44.843188-0.000323j
[2025-08-26 10:16:06] [Iter 2223/2250] R3[1172/1200], Temp: 0.0013, Energy: -44.844736-0.000183j
[2025-08-26 10:16:32] [Iter 2224/2250] R3[1173/1200], Temp: 0.0012, Energy: -44.835961-0.001394j
[2025-08-26 10:16:57] [Iter 2225/2250] R3[1174/1200], Temp: 0.0012, Energy: -44.844645-0.001058j
[2025-08-26 10:17:23] [Iter 2226/2250] R3[1175/1200], Temp: 0.0011, Energy: -44.842684-0.001711j
[2025-08-26 10:17:49] [Iter 2227/2250] R3[1176/1200], Temp: 0.0010, Energy: -44.845199+0.000151j
[2025-08-26 10:18:15] [Iter 2228/2250] R3[1177/1200], Temp: 0.0009, Energy: -44.849691-0.000238j
[2025-08-26 10:18:41] [Iter 2229/2250] R3[1178/1200], Temp: 0.0008, Energy: -44.845972+0.001298j
[2025-08-26 10:19:07] [Iter 2230/2250] R3[1179/1200], Temp: 0.0008, Energy: -44.852662-0.003740j
[2025-08-26 10:19:33] [Iter 2231/2250] R3[1180/1200], Temp: 0.0007, Energy: -44.850354-0.001025j
[2025-08-26 10:19:59] [Iter 2232/2250] R3[1181/1200], Temp: 0.0006, Energy: -44.845749+0.001492j
[2025-08-26 10:20:25] [Iter 2233/2250] R3[1182/1200], Temp: 0.0006, Energy: -44.844961+0.002719j
[2025-08-26 10:20:51] [Iter 2234/2250] R3[1183/1200], Temp: 0.0005, Energy: -44.846647-0.000143j
[2025-08-26 10:21:16] [Iter 2235/2250] R3[1184/1200], Temp: 0.0004, Energy: -44.841234-0.001123j
[2025-08-26 10:21:42] [Iter 2236/2250] R3[1185/1200], Temp: 0.0004, Energy: -44.846701-0.000144j
[2025-08-26 10:22:08] [Iter 2237/2250] R3[1186/1200], Temp: 0.0003, Energy: -44.854031+0.000950j
[2025-08-26 10:22:34] [Iter 2238/2250] R3[1187/1200], Temp: 0.0003, Energy: -44.846681+0.001878j
[2025-08-26 10:23:00] [Iter 2239/2250] R3[1188/1200], Temp: 0.0002, Energy: -44.854760+0.000540j
[2025-08-26 10:23:26] [Iter 2240/2250] R3[1189/1200], Temp: 0.0002, Energy: -44.847276+0.000583j
[2025-08-26 10:23:52] [Iter 2241/2250] R3[1190/1200], Temp: 0.0002, Energy: -44.842886-0.002251j
[2025-08-26 10:24:18] [Iter 2242/2250] R3[1191/1200], Temp: 0.0001, Energy: -44.848387+0.002365j
[2025-08-26 10:24:44] [Iter 2243/2250] R3[1192/1200], Temp: 0.0001, Energy: -44.845614-0.000431j
[2025-08-26 10:25:10] [Iter 2244/2250] R3[1193/1200], Temp: 0.0001, Energy: -44.833042-0.000235j
[2025-08-26 10:25:36] [Iter 2245/2250] R3[1194/1200], Temp: 0.0001, Energy: -44.838337+0.000409j
[2025-08-26 10:26:01] [Iter 2246/2250] R3[1195/1200], Temp: 0.0000, Energy: -44.849557-0.004815j
[2025-08-26 10:26:27] [Iter 2247/2250] R3[1196/1200], Temp: 0.0000, Energy: -44.842016-0.001392j
[2025-08-26 10:26:54] [Iter 2248/2250] R3[1197/1200], Temp: 0.0000, Energy: -44.844470+0.000809j
[2025-08-26 10:27:20] [Iter 2249/2250] R3[1198/1200], Temp: 0.0000, Energy: -44.840095+0.002387j
[2025-08-26 10:27:46] [Iter 2250/2250] R3[1199/1200], Temp: 0.0000, Energy: -44.841680+0.001760j
[2025-08-26 10:27:46] ✓ Checkpoint saved: checkpoint_iter_002250.pkl
[2025-08-26 10:27:46] ✅ Training completed | Restarts: 3
[2025-08-26 10:27:46] ============================================================
[2025-08-26 10:27:46] Training completed | Runtime: 58338.4s
[2025-08-26 10:27:54] ✓ Final state saved: checkpoints/final_GCNN.pkl
[2025-08-26 10:27:54] ============================================================
