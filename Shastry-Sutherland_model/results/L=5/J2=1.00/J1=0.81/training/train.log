[2025-08-26 22:19:59] ✓ 从checkpoint恢复: results/L=5/J2=1.00/J1=0.80/training/checkpoints/final_GCNN.pkl
[2025-08-26 22:19:59]   - 迭代次数: final
[2025-08-26 22:19:59]   - 能量: -44.841680+0.001760j ± 0.005054
[2025-08-26 22:19:59]   - 时间戳: 2025-08-26T10:27:53.974121+08:00
[2025-08-26 22:20:10] ✓ 变分状态参数已从checkpoint恢复
[2025-08-26 22:20:10] ✓ 从final状态恢复, 重置迭代计数为0
[2025-08-26 22:20:10] ==================================================
[2025-08-26 22:20:10] GCNN for Shastry-Sutherland Model
[2025-08-26 22:20:10] ==================================================
[2025-08-26 22:20:10] System parameters:
[2025-08-26 22:20:10]   - System size: L=5, N=100
[2025-08-26 22:20:10]   - System parameters: J1=0.81, J2=1.0, Q=0.0
[2025-08-26 22:20:10] --------------------------------------------------
[2025-08-26 22:20:10] Model parameters:
[2025-08-26 22:20:10]   - Number of layers = 4
[2025-08-26 22:20:10]   - Number of features = 4
[2025-08-26 22:20:10]   - Total parameters = 19628
[2025-08-26 22:20:10] --------------------------------------------------
[2025-08-26 22:20:10] Training parameters:
[2025-08-26 22:20:10]   - Learning rate: 0.015
[2025-08-26 22:20:10]   - Total iterations: 450
[2025-08-26 22:20:10]   - Annealing cycles: 2
[2025-08-26 22:20:10]   - Initial period: 150
[2025-08-26 22:20:10]   - Period multiplier: 2.0
[2025-08-26 22:20:10]   - Temperature range: 0.0-1.0
[2025-08-26 22:20:10]   - Samples: 4096
[2025-08-26 22:20:10]   - Discarded samples: 0
[2025-08-26 22:20:10]   - Chunk size: 2048
[2025-08-26 22:20:10]   - Diagonal shift: 0.2
[2025-08-26 22:20:10]   - Gradient clipping: 1.0
[2025-08-26 22:20:10]   - Checkpoint enabled: interval=50
[2025-08-26 22:20:10]   - Checkpoint directory: results/L=5/J2=1.00/J1=0.81/training/checkpoints
[2025-08-26 22:20:10] --------------------------------------------------
[2025-08-26 22:20:10] Device status:
[2025-08-26 22:20:10]   - Devices model: NVIDIA H200 NVL
[2025-08-26 22:20:10]   - Number of devices: 1
[2025-08-26 22:20:10]   - Sharding: True
[2025-08-26 22:20:10] ============================================================
[2025-08-26 22:20:52] [Iter 1/450] R0[0/150], Temp: 1.0000, Energy: -45.389824-0.010992j
[2025-08-26 22:21:20] [Iter 2/450] R0[1/150], Temp: 0.9999, Energy: -45.477817-0.008334j
[2025-08-26 22:21:33] [Iter 3/450] R0[2/150], Temp: 0.9996, Energy: -45.482687-0.001317j
[2025-08-26 22:21:45] [Iter 4/450] R0[3/150], Temp: 0.9990, Energy: -45.481368+0.000403j
[2025-08-26 22:21:57] [Iter 5/450] R0[4/150], Temp: 0.9982, Energy: -45.488869+0.001214j
[2025-08-26 22:22:10] [Iter 6/450] R0[5/150], Temp: 0.9973, Energy: -45.476572-0.002382j
[2025-08-26 22:22:22] [Iter 7/450] R0[6/150], Temp: 0.9961, Energy: -45.474922+0.003184j
[2025-08-26 22:22:35] [Iter 8/450] R0[7/150], Temp: 0.9946, Energy: -45.483929-0.001824j
[2025-08-26 22:22:47] [Iter 9/450] R0[8/150], Temp: 0.9930, Energy: -45.494785+0.001053j
[2025-08-26 22:22:59] [Iter 10/450] R0[9/150], Temp: 0.9911, Energy: -45.491739+0.003876j
[2025-08-26 22:23:12] [Iter 11/450] R0[10/150], Temp: 0.9891, Energy: -45.481734+0.000713j
[2025-08-26 22:23:24] [Iter 12/450] R0[11/150], Temp: 0.9868, Energy: -45.496403+0.000524j
[2025-08-26 22:23:36] [Iter 13/450] R0[12/150], Temp: 0.9843, Energy: -45.481652+0.004061j
[2025-08-26 22:23:49] [Iter 14/450] R0[13/150], Temp: 0.9816, Energy: -45.489998+0.000476j
[2025-08-26 22:24:01] [Iter 15/450] R0[14/150], Temp: 0.9787, Energy: -45.491399-0.003087j
[2025-08-26 22:24:14] [Iter 16/450] R0[15/150], Temp: 0.9755, Energy: -45.491242-0.007198j
[2025-08-26 22:24:26] [Iter 17/450] R0[16/150], Temp: 0.9722, Energy: -45.482041-0.000344j
[2025-08-26 22:24:38] [Iter 18/450] R0[17/150], Temp: 0.9686, Energy: -45.483810-0.003117j
[2025-08-26 22:24:51] [Iter 19/450] R0[18/150], Temp: 0.9649, Energy: -45.476218+0.000683j
[2025-08-26 22:25:03] [Iter 20/450] R0[19/150], Temp: 0.9609, Energy: -45.482943-0.003796j
[2025-08-26 22:25:16] [Iter 21/450] R0[20/150], Temp: 0.9568, Energy: -45.472701-0.003937j
[2025-08-26 22:25:28] [Iter 22/450] R0[21/150], Temp: 0.9524, Energy: -45.488926-0.002889j
[2025-08-26 22:25:40] [Iter 23/450] R0[22/150], Temp: 0.9479, Energy: -45.499796-0.003871j
[2025-08-26 22:25:53] [Iter 24/450] R0[23/150], Temp: 0.9431, Energy: -45.481249+0.000066j
[2025-08-26 22:26:05] [Iter 25/450] R0[24/150], Temp: 0.9382, Energy: -45.472987-0.003863j
[2025-08-26 22:26:18] [Iter 26/450] R0[25/150], Temp: 0.9330, Energy: -45.460010+0.002285j
[2025-08-26 22:26:30] [Iter 27/450] R0[26/150], Temp: 0.9277, Energy: -45.499530-0.001407j
[2025-08-26 22:26:42] [Iter 28/450] R0[27/150], Temp: 0.9222, Energy: -45.474688-0.000322j
[2025-08-26 22:26:55] [Iter 29/450] R0[28/150], Temp: 0.9165, Energy: -45.479387-0.005677j
[2025-08-26 22:27:07] [Iter 30/450] R0[29/150], Temp: 0.9106, Energy: -45.485721-0.005930j
[2025-08-26 22:27:20] [Iter 31/450] R0[30/150], Temp: 0.9045, Energy: -45.483631+0.001916j
[2025-08-26 22:27:32] [Iter 32/450] R0[31/150], Temp: 0.8983, Energy: -45.476221-0.002678j
[2025-08-26 22:27:44] [Iter 33/450] R0[32/150], Temp: 0.8918, Energy: -45.496109-0.001657j
[2025-08-26 22:27:57] [Iter 34/450] R0[33/150], Temp: 0.8853, Energy: -45.488337+0.001070j
[2025-08-26 22:28:09] [Iter 35/450] R0[34/150], Temp: 0.8785, Energy: -45.482777+0.006310j
[2025-08-26 22:28:21] [Iter 36/450] R0[35/150], Temp: 0.8716, Energy: -45.471997+0.002103j
[2025-08-26 22:28:34] [Iter 37/450] R0[36/150], Temp: 0.8645, Energy: -45.476168+0.001412j
[2025-08-26 22:28:46] [Iter 38/450] R0[37/150], Temp: 0.8572, Energy: -45.479055+0.001249j
[2025-08-26 22:28:59] [Iter 39/450] R0[38/150], Temp: 0.8498, Energy: -45.487199+0.003907j
[2025-08-26 22:29:11] [Iter 40/450] R0[39/150], Temp: 0.8423, Energy: -45.487502-0.000281j
[2025-08-26 22:29:23] [Iter 41/450] R0[40/150], Temp: 0.8346, Energy: -45.470520-0.000181j
[2025-08-26 22:29:36] [Iter 42/450] R0[41/150], Temp: 0.8267, Energy: -45.488810-0.003319j
[2025-08-26 22:29:48] [Iter 43/450] R0[42/150], Temp: 0.8187, Energy: -45.483887+0.001185j
[2025-08-26 22:30:01] [Iter 44/450] R0[43/150], Temp: 0.8106, Energy: -45.469465+0.004264j
[2025-08-26 22:30:13] [Iter 45/450] R0[44/150], Temp: 0.8023, Energy: -45.473797-0.000581j
[2025-08-26 22:30:25] [Iter 46/450] R0[45/150], Temp: 0.7939, Energy: -45.488292+0.006219j
[2025-08-26 22:30:38] [Iter 47/450] R0[46/150], Temp: 0.7854, Energy: -45.486685+0.002383j
[2025-08-26 22:30:50] [Iter 48/450] R0[47/150], Temp: 0.7767, Energy: -45.484728+0.000690j
[2025-08-26 22:31:03] [Iter 49/450] R0[48/150], Temp: 0.7679, Energy: -45.503116+0.001838j
[2025-08-26 22:31:15] [Iter 50/450] R0[49/150], Temp: 0.7590, Energy: -45.480867-0.004755j
[2025-08-26 22:31:15] ✓ Checkpoint saved: checkpoint_iter_000050.pkl
[2025-08-26 22:31:27] [Iter 51/450] R0[50/150], Temp: 0.7500, Energy: -45.494730-0.001016j
[2025-08-26 22:31:40] [Iter 52/450] R0[51/150], Temp: 0.7409, Energy: -45.481280+0.003673j
[2025-08-26 22:31:52] [Iter 53/450] R0[52/150], Temp: 0.7316, Energy: -45.481391+0.005431j
[2025-08-26 22:32:05] [Iter 54/450] R0[53/150], Temp: 0.7223, Energy: -45.484664+0.002026j
[2025-08-26 22:32:17] [Iter 55/450] R0[54/150], Temp: 0.7129, Energy: -45.497653-0.001316j
[2025-08-26 22:32:29] [Iter 56/450] R0[55/150], Temp: 0.7034, Energy: -45.494435+0.003381j
[2025-08-26 22:32:42] [Iter 57/450] R0[56/150], Temp: 0.6938, Energy: -45.490435+0.000452j
[2025-08-26 22:32:54] [Iter 58/450] R0[57/150], Temp: 0.6841, Energy: -45.504489+0.004787j
[2025-08-26 22:33:07] [Iter 59/450] R0[58/150], Temp: 0.6743, Energy: -45.481326-0.002259j
[2025-08-26 22:33:19] [Iter 60/450] R0[59/150], Temp: 0.6644, Energy: -45.485397-0.000868j
[2025-08-26 22:33:32] [Iter 61/450] R0[60/150], Temp: 0.6545, Energy: -45.493084-0.000130j
[2025-08-26 22:33:44] [Iter 62/450] R0[61/150], Temp: 0.6445, Energy: -45.479375-0.000334j
[2025-08-26 22:33:56] [Iter 63/450] R0[62/150], Temp: 0.6345, Energy: -45.483153+0.001164j
[2025-08-26 22:34:09] [Iter 64/450] R0[63/150], Temp: 0.6243, Energy: -45.485100-0.000134j
[2025-08-26 22:34:21] [Iter 65/450] R0[64/150], Temp: 0.6142, Energy: -45.501824+0.001144j
[2025-08-26 22:34:34] [Iter 66/450] R0[65/150], Temp: 0.6040, Energy: -45.488356+0.001984j
[2025-08-26 22:34:46] [Iter 67/450] R0[66/150], Temp: 0.5937, Energy: -45.493848-0.000770j
[2025-08-26 22:34:58] [Iter 68/450] R0[67/150], Temp: 0.5834, Energy: -45.494107-0.007312j
[2025-08-26 22:35:11] [Iter 69/450] R0[68/150], Temp: 0.5730, Energy: -45.493984-0.001674j
[2025-08-26 22:35:23] [Iter 70/450] R0[69/150], Temp: 0.5627, Energy: -45.494395-0.001791j
[2025-08-26 22:35:36] [Iter 71/450] R0[70/150], Temp: 0.5523, Energy: -45.486814+0.001838j
[2025-08-26 22:35:48] [Iter 72/450] R0[71/150], Temp: 0.5418, Energy: -45.495355+0.004233j
[2025-08-26 22:36:00] [Iter 73/450] R0[72/150], Temp: 0.5314, Energy: -45.489205+0.000681j
[2025-08-26 22:36:13] [Iter 74/450] R0[73/150], Temp: 0.5209, Energy: -45.488618-0.002366j
[2025-08-26 22:36:25] [Iter 75/450] R0[74/150], Temp: 0.5105, Energy: -45.493798+0.001862j
[2025-08-26 22:36:38] [Iter 76/450] R0[75/150], Temp: 0.5000, Energy: -45.488561+0.000149j
[2025-08-26 22:36:50] [Iter 77/450] R0[76/150], Temp: 0.4895, Energy: -45.488244+0.006536j
[2025-08-26 22:37:02] [Iter 78/450] R0[77/150], Temp: 0.4791, Energy: -45.486168+0.002238j
[2025-08-26 22:37:15] [Iter 79/450] R0[78/150], Temp: 0.4686, Energy: -45.476723+0.006914j
[2025-08-26 22:37:27] [Iter 80/450] R0[79/150], Temp: 0.4582, Energy: -45.491470-0.001556j
[2025-08-26 22:37:40] [Iter 81/450] R0[80/150], Temp: 0.4477, Energy: -45.482320+0.001169j
[2025-08-26 22:37:52] [Iter 82/450] R0[81/150], Temp: 0.4373, Energy: -45.484936+0.002819j
[2025-08-26 22:38:04] [Iter 83/450] R0[82/150], Temp: 0.4270, Energy: -45.477006+0.004246j
[2025-08-26 22:38:17] [Iter 84/450] R0[83/150], Temp: 0.4166, Energy: -45.500026+0.002944j
[2025-08-26 22:38:29] [Iter 85/450] R0[84/150], Temp: 0.4063, Energy: -45.473565-0.004348j
[2025-08-26 22:38:42] [Iter 86/450] R0[85/150], Temp: 0.3960, Energy: -45.469800+0.002121j
[2025-08-26 22:38:54] [Iter 87/450] R0[86/150], Temp: 0.3858, Energy: -45.483574-0.000592j
[2025-08-26 22:39:06] [Iter 88/450] R0[87/150], Temp: 0.3757, Energy: -45.479477+0.003478j
[2025-08-26 22:39:19] [Iter 89/450] R0[88/150], Temp: 0.3655, Energy: -45.493184+0.003944j
[2025-08-26 22:39:31] [Iter 90/450] R0[89/150], Temp: 0.3555, Energy: -45.489284-0.000207j
[2025-08-26 22:39:44] [Iter 91/450] R0[90/150], Temp: 0.3455, Energy: -45.488722+0.007891j
[2025-08-26 22:39:56] [Iter 92/450] R0[91/150], Temp: 0.3356, Energy: -45.483167+0.001061j
[2025-08-26 22:40:08] [Iter 93/450] R0[92/150], Temp: 0.3257, Energy: -45.480481+0.003094j
[2025-08-26 22:40:21] [Iter 94/450] R0[93/150], Temp: 0.3159, Energy: -45.509149-0.002678j
[2025-08-26 22:40:33] [Iter 95/450] R0[94/150], Temp: 0.3062, Energy: -45.492850+0.001891j
[2025-08-26 22:40:46] [Iter 96/450] R0[95/150], Temp: 0.2966, Energy: -45.481877-0.000288j
[2025-08-26 22:40:58] [Iter 97/450] R0[96/150], Temp: 0.2871, Energy: -45.502447-0.001198j
[2025-08-26 22:41:10] [Iter 98/450] R0[97/150], Temp: 0.2777, Energy: -45.474627-0.002562j
[2025-08-26 22:41:23] [Iter 99/450] R0[98/150], Temp: 0.2684, Energy: -45.490413-0.002953j
[2025-08-26 22:41:35] [Iter 100/450] R0[99/150], Temp: 0.2591, Energy: -45.485135-0.001841j
[2025-08-26 22:41:35] ✓ Checkpoint saved: checkpoint_iter_000100.pkl
[2025-08-26 22:41:48] [Iter 101/450] R0[100/150], Temp: 0.2500, Energy: -45.494743-0.000711j
[2025-08-26 22:42:00] [Iter 102/450] R0[101/150], Temp: 0.2410, Energy: -45.470409-0.000764j
[2025-08-26 22:42:12] [Iter 103/450] R0[102/150], Temp: 0.2321, Energy: -45.481812+0.000936j
[2025-08-26 22:42:25] [Iter 104/450] R0[103/150], Temp: 0.2233, Energy: -45.473032+0.001532j
[2025-08-26 22:42:37] [Iter 105/450] R0[104/150], Temp: 0.2146, Energy: -45.499380+0.001495j
[2025-08-26 22:42:50] [Iter 106/450] R0[105/150], Temp: 0.2061, Energy: -45.501727-0.000020j
[2025-08-26 22:43:02] [Iter 107/450] R0[106/150], Temp: 0.1977, Energy: -45.508339-0.004675j
[2025-08-26 22:43:14] [Iter 108/450] R0[107/150], Temp: 0.1894, Energy: -45.501017+0.002245j
[2025-08-26 22:43:27] [Iter 109/450] R0[108/150], Temp: 0.1813, Energy: -45.490577-0.001131j
[2025-08-26 22:43:39] [Iter 110/450] R0[109/150], Temp: 0.1733, Energy: -45.473085-0.002253j
[2025-08-26 22:43:52] [Iter 111/450] R0[110/150], Temp: 0.1654, Energy: -45.501486-0.000393j
[2025-08-26 22:44:04] [Iter 112/450] R0[111/150], Temp: 0.1577, Energy: -45.467576+0.004510j
[2025-08-26 22:44:16] [Iter 113/450] R0[112/150], Temp: 0.1502, Energy: -45.466906+0.007814j
[2025-08-26 22:44:28] [Iter 114/450] R0[113/150], Temp: 0.1428, Energy: -45.484776+0.002257j
[2025-08-26 22:44:41] [Iter 115/450] R0[114/150], Temp: 0.1355, Energy: -45.473821+0.000449j
[2025-08-26 22:44:53] [Iter 116/450] R0[115/150], Temp: 0.1284, Energy: -45.469712+0.000199j
[2025-08-26 22:45:05] [Iter 117/450] R0[116/150], Temp: 0.1215, Energy: -45.504803-0.001419j
[2025-08-26 22:45:18] [Iter 118/450] R0[117/150], Temp: 0.1147, Energy: -45.476543+0.000245j
[2025-08-26 22:45:30] [Iter 119/450] R0[118/150], Temp: 0.1082, Energy: -45.493408+0.002842j
[2025-08-26 22:45:43] [Iter 120/450] R0[119/150], Temp: 0.1017, Energy: -45.463670+0.006286j
[2025-08-26 22:45:55] [Iter 121/450] R0[120/150], Temp: 0.0955, Energy: -45.478413-0.000777j
[2025-08-26 22:46:07] [Iter 122/450] R0[121/150], Temp: 0.0894, Energy: -45.483774-0.004807j
[2025-08-26 22:46:20] [Iter 123/450] R0[122/150], Temp: 0.0835, Energy: -45.467107+0.002483j
[2025-08-26 22:46:32] [Iter 124/450] R0[123/150], Temp: 0.0778, Energy: -45.481443-0.001074j
[2025-08-26 22:46:45] [Iter 125/450] R0[124/150], Temp: 0.0723, Energy: -45.497360+0.002671j
[2025-08-26 22:46:57] [Iter 126/450] R0[125/150], Temp: 0.0670, Energy: -45.502617+0.000623j
[2025-08-26 22:47:09] [Iter 127/450] R0[126/150], Temp: 0.0618, Energy: -45.488137+0.001654j
[2025-08-26 22:47:22] [Iter 128/450] R0[127/150], Temp: 0.0569, Energy: -45.483549-0.003194j
[2025-08-26 22:47:34] [Iter 129/450] R0[128/150], Temp: 0.0521, Energy: -45.506303+0.000701j
[2025-08-26 22:47:47] [Iter 130/450] R0[129/150], Temp: 0.0476, Energy: -45.489681-0.001830j
[2025-08-26 22:47:59] [Iter 131/450] R0[130/150], Temp: 0.0432, Energy: -45.483788+0.007290j
[2025-08-26 22:48:11] [Iter 132/450] R0[131/150], Temp: 0.0391, Energy: -45.476153+0.001194j
[2025-08-26 22:48:24] [Iter 133/450] R0[132/150], Temp: 0.0351, Energy: -45.485203+0.004800j
[2025-08-26 22:48:36] [Iter 134/450] R0[133/150], Temp: 0.0314, Energy: -45.478709+0.001082j
[2025-08-26 22:48:49] [Iter 135/450] R0[134/150], Temp: 0.0278, Energy: -45.476785+0.001285j
[2025-08-26 22:49:01] [Iter 136/450] R0[135/150], Temp: 0.0245, Energy: -45.487674-0.002457j
[2025-08-26 22:49:14] [Iter 137/450] R0[136/150], Temp: 0.0213, Energy: -45.483708+0.005250j
[2025-08-26 22:49:26] [Iter 138/450] R0[137/150], Temp: 0.0184, Energy: -45.488565+0.005531j
[2025-08-26 22:49:38] [Iter 139/450] R0[138/150], Temp: 0.0157, Energy: -45.482226+0.002284j
[2025-08-26 22:49:51] [Iter 140/450] R0[139/150], Temp: 0.0132, Energy: -45.498426+0.002804j
[2025-08-26 22:50:03] [Iter 141/450] R0[140/150], Temp: 0.0109, Energy: -45.493512-0.002254j
[2025-08-26 22:50:16] [Iter 142/450] R0[141/150], Temp: 0.0089, Energy: -45.496906+0.004404j
[2025-08-26 22:50:28] [Iter 143/450] R0[142/150], Temp: 0.0070, Energy: -45.473905-0.000438j
[2025-08-26 22:50:40] [Iter 144/450] R0[143/150], Temp: 0.0054, Energy: -45.503542-0.001715j
[2025-08-26 22:50:53] [Iter 145/450] R0[144/150], Temp: 0.0039, Energy: -45.481660+0.004729j
[2025-08-26 22:51:05] [Iter 146/450] R0[145/150], Temp: 0.0027, Energy: -45.483786+0.003616j
[2025-08-26 22:51:18] [Iter 147/450] R0[146/150], Temp: 0.0018, Energy: -45.490955+0.001886j
[2025-08-26 22:51:30] [Iter 148/450] R0[147/150], Temp: 0.0010, Energy: -45.488902-0.002516j
[2025-08-26 22:51:42] [Iter 149/450] R0[148/150], Temp: 0.0004, Energy: -45.480817-0.000017j
[2025-08-26 22:51:55] [Iter 150/450] R0[149/150], Temp: 0.0001, Energy: -45.493662-0.001608j
[2025-08-26 22:51:55] ✓ Checkpoint saved: checkpoint_iter_000150.pkl
[2025-08-26 22:51:55] RESTART #1 | Period: 300
[2025-08-26 22:52:07] [Iter 151/450] R1[0/300], Temp: 1.0000, Energy: -45.479132+0.003481j
[2025-08-26 22:52:20] [Iter 152/450] R1[1/300], Temp: 1.0000, Energy: -45.488849-0.004220j
[2025-08-26 22:52:32] [Iter 153/450] R1[2/300], Temp: 0.9999, Energy: -45.478027-0.005261j
[2025-08-26 22:52:45] [Iter 154/450] R1[3/300], Temp: 0.9998, Energy: -45.482208+0.002626j
[2025-08-26 22:52:57] [Iter 155/450] R1[4/300], Temp: 0.9996, Energy: -45.490787+0.004041j
[2025-08-26 22:53:10] [Iter 156/450] R1[5/300], Temp: 0.9993, Energy: -45.496857+0.000375j
[2025-08-26 22:53:23] [Iter 157/450] R1[6/300], Temp: 0.9990, Energy: -45.487552-0.002196j
[2025-08-26 22:53:35] [Iter 158/450] R1[7/300], Temp: 0.9987, Energy: -45.468976-0.003069j
[2025-08-26 22:53:48] [Iter 159/450] R1[8/300], Temp: 0.9982, Energy: -45.487568-0.001079j
[2025-08-26 22:54:00] [Iter 160/450] R1[9/300], Temp: 0.9978, Energy: -45.493557-0.002639j
[2025-08-26 22:54:13] [Iter 161/450] R1[10/300], Temp: 0.9973, Energy: -45.482367+0.003326j
[2025-08-26 22:54:25] [Iter 162/450] R1[11/300], Temp: 0.9967, Energy: -45.491763+0.001160j
[2025-08-26 22:54:38] [Iter 163/450] R1[12/300], Temp: 0.9961, Energy: -45.484854-0.002920j
[2025-08-26 22:54:50] [Iter 164/450] R1[13/300], Temp: 0.9954, Energy: -45.489030+0.000340j
[2025-08-26 22:55:03] [Iter 165/450] R1[14/300], Temp: 0.9946, Energy: -45.472387-0.006559j
[2025-08-26 22:55:15] [Iter 166/450] R1[15/300], Temp: 0.9938, Energy: -45.494088-0.004286j
[2025-08-26 22:55:28] [Iter 167/450] R1[16/300], Temp: 0.9930, Energy: -45.480898-0.000917j
[2025-08-26 22:55:40] [Iter 168/450] R1[17/300], Temp: 0.9921, Energy: -45.487077-0.003517j
[2025-08-26 22:55:53] [Iter 169/450] R1[18/300], Temp: 0.9911, Energy: -45.488541-0.003949j
[2025-08-26 22:56:05] [Iter 170/450] R1[19/300], Temp: 0.9901, Energy: -45.485108+0.001804j
[2025-08-26 22:56:18] [Iter 171/450] R1[20/300], Temp: 0.9891, Energy: -45.482961+0.003038j
[2025-08-26 22:56:30] [Iter 172/450] R1[21/300], Temp: 0.9880, Energy: -45.489591+0.000738j
[2025-08-26 22:56:43] [Iter 173/450] R1[22/300], Temp: 0.9868, Energy: -45.491319-0.000639j
[2025-08-26 22:56:55] [Iter 174/450] R1[23/300], Temp: 0.9856, Energy: -45.463424+0.001861j
[2025-08-26 22:57:08] [Iter 175/450] R1[24/300], Temp: 0.9843, Energy: -45.483516+0.001306j
[2025-08-26 22:57:20] [Iter 176/450] R1[25/300], Temp: 0.9830, Energy: -45.488327-0.001441j
[2025-08-26 22:57:33] [Iter 177/450] R1[26/300], Temp: 0.9816, Energy: -45.491277-0.002026j
[2025-08-26 22:57:45] [Iter 178/450] R1[27/300], Temp: 0.9801, Energy: -45.490047-0.002496j
[2025-08-26 22:57:58] [Iter 179/450] R1[28/300], Temp: 0.9787, Energy: -45.486096+0.001168j
[2025-08-26 22:58:10] [Iter 180/450] R1[29/300], Temp: 0.9771, Energy: -45.493991-0.006130j
[2025-08-26 22:58:23] [Iter 181/450] R1[30/300], Temp: 0.9755, Energy: -45.482237-0.000225j
[2025-08-26 22:58:35] [Iter 182/450] R1[31/300], Temp: 0.9739, Energy: -45.484111+0.001916j
[2025-08-26 22:58:48] [Iter 183/450] R1[32/300], Temp: 0.9722, Energy: -45.475089-0.000891j
[2025-08-26 22:59:00] [Iter 184/450] R1[33/300], Temp: 0.9704, Energy: -45.494534-0.001744j
[2025-08-26 22:59:13] [Iter 185/450] R1[34/300], Temp: 0.9686, Energy: -45.494049-0.001148j
[2025-08-26 22:59:25] [Iter 186/450] R1[35/300], Temp: 0.9668, Energy: -45.491398+0.000678j
[2025-08-26 22:59:38] [Iter 187/450] R1[36/300], Temp: 0.9649, Energy: -45.498792+0.000741j
[2025-08-26 22:59:50] [Iter 188/450] R1[37/300], Temp: 0.9629, Energy: -45.499122-0.002036j
[2025-08-26 23:00:03] [Iter 189/450] R1[38/300], Temp: 0.9609, Energy: -45.493831+0.004429j
[2025-08-26 23:00:15] [Iter 190/450] R1[39/300], Temp: 0.9589, Energy: -45.485564-0.000462j
[2025-08-26 23:00:28] [Iter 191/450] R1[40/300], Temp: 0.9568, Energy: -45.484805-0.000404j
[2025-08-26 23:00:40] [Iter 192/450] R1[41/300], Temp: 0.9546, Energy: -45.489242+0.000721j
[2025-08-26 23:00:53] [Iter 193/450] R1[42/300], Temp: 0.9524, Energy: -45.502569+0.003022j
[2025-08-26 23:01:05] [Iter 194/450] R1[43/300], Temp: 0.9502, Energy: -45.464586-0.001060j
[2025-08-26 23:01:18] [Iter 195/450] R1[44/300], Temp: 0.9479, Energy: -45.492939+0.001984j
[2025-08-26 23:01:30] [Iter 196/450] R1[45/300], Temp: 0.9455, Energy: -45.484334-0.003752j
[2025-08-26 23:01:43] [Iter 197/450] R1[46/300], Temp: 0.9431, Energy: -45.507960-0.002827j
[2025-08-26 23:01:55] [Iter 198/450] R1[47/300], Temp: 0.9407, Energy: -45.487953-0.005820j
[2025-08-26 23:02:08] [Iter 199/450] R1[48/300], Temp: 0.9382, Energy: -45.500652+0.005019j
[2025-08-26 23:02:20] [Iter 200/450] R1[49/300], Temp: 0.9356, Energy: -45.485769+0.001477j
[2025-08-26 23:02:20] ✓ Checkpoint saved: checkpoint_iter_000200.pkl
[2025-08-26 23:02:33] [Iter 201/450] R1[50/300], Temp: 0.9330, Energy: -45.488475-0.000302j
[2025-08-26 23:02:45] [Iter 202/450] R1[51/300], Temp: 0.9304, Energy: -45.486116-0.000177j
[2025-08-26 23:02:58] [Iter 203/450] R1[52/300], Temp: 0.9277, Energy: -45.475319-0.001757j
[2025-08-26 23:03:10] [Iter 204/450] R1[53/300], Temp: 0.9249, Energy: -45.495934+0.002228j
[2025-08-26 23:03:23] [Iter 205/450] R1[54/300], Temp: 0.9222, Energy: -45.492195-0.002801j
[2025-08-26 23:03:35] [Iter 206/450] R1[55/300], Temp: 0.9193, Energy: -45.484333-0.002702j
[2025-08-26 23:03:47] [Iter 207/450] R1[56/300], Temp: 0.9165, Energy: -45.488377+0.002924j
[2025-08-26 23:04:00] [Iter 208/450] R1[57/300], Temp: 0.9135, Energy: -45.480102+0.000071j
[2025-08-26 23:04:12] [Iter 209/450] R1[58/300], Temp: 0.9106, Energy: -45.494882+0.006083j
[2025-08-26 23:04:25] [Iter 210/450] R1[59/300], Temp: 0.9076, Energy: -45.479573+0.003347j
[2025-08-26 23:04:37] [Iter 211/450] R1[60/300], Temp: 0.9045, Energy: -45.489406-0.001798j
[2025-08-26 23:04:50] [Iter 212/450] R1[61/300], Temp: 0.9014, Energy: -45.495556-0.000530j
[2025-08-26 23:05:02] [Iter 213/450] R1[62/300], Temp: 0.8983, Energy: -45.492636+0.001248j
[2025-08-26 23:05:15] [Iter 214/450] R1[63/300], Temp: 0.8951, Energy: -45.494764-0.000498j
[2025-08-26 23:05:27] [Iter 215/450] R1[64/300], Temp: 0.8918, Energy: -45.471027-0.000304j
[2025-08-26 23:05:40] [Iter 216/450] R1[65/300], Temp: 0.8886, Energy: -45.481965-0.000894j
[2025-08-26 23:05:52] [Iter 217/450] R1[66/300], Temp: 0.8853, Energy: -45.496926-0.005190j
[2025-08-26 23:06:05] [Iter 218/450] R1[67/300], Temp: 0.8819, Energy: -45.476611-0.002237j
[2025-08-26 23:06:17] [Iter 219/450] R1[68/300], Temp: 0.8785, Energy: -45.486747+0.000169j
[2025-08-26 23:06:30] [Iter 220/450] R1[69/300], Temp: 0.8751, Energy: -45.504353-0.000982j
[2025-08-26 23:06:42] [Iter 221/450] R1[70/300], Temp: 0.8716, Energy: -45.486388+0.002744j
[2025-08-26 23:06:55] [Iter 222/450] R1[71/300], Temp: 0.8680, Energy: -45.481132-0.000941j
[2025-08-26 23:07:07] [Iter 223/450] R1[72/300], Temp: 0.8645, Energy: -45.493693+0.008120j
[2025-08-26 23:07:20] [Iter 224/450] R1[73/300], Temp: 0.8609, Energy: -45.492971+0.001572j
[2025-08-26 23:07:32] [Iter 225/450] R1[74/300], Temp: 0.8572, Energy: -45.500278-0.001119j
[2025-08-26 23:07:45] [Iter 226/450] R1[75/300], Temp: 0.8536, Energy: -45.487206+0.002734j
[2025-08-26 23:07:57] [Iter 227/450] R1[76/300], Temp: 0.8498, Energy: -45.479097-0.001660j
[2025-08-26 23:08:10] [Iter 228/450] R1[77/300], Temp: 0.8461, Energy: -45.484233-0.003155j
[2025-08-26 23:08:22] [Iter 229/450] R1[78/300], Temp: 0.8423, Energy: -45.485305-0.000562j
[2025-08-26 23:08:35] [Iter 230/450] R1[79/300], Temp: 0.8384, Energy: -45.488562+0.000658j
[2025-08-26 23:08:47] [Iter 231/450] R1[80/300], Temp: 0.8346, Energy: -45.491968-0.002767j
[2025-08-26 23:09:00] [Iter 232/450] R1[81/300], Temp: 0.8307, Energy: -45.498494+0.001449j
[2025-08-26 23:09:12] [Iter 233/450] R1[82/300], Temp: 0.8267, Energy: -45.501265+0.001541j
[2025-08-26 23:09:25] [Iter 234/450] R1[83/300], Temp: 0.8227, Energy: -45.482528-0.005273j
[2025-08-26 23:09:37] [Iter 235/450] R1[84/300], Temp: 0.8187, Energy: -45.472450+0.004860j
[2025-08-26 23:09:49] [Iter 236/450] R1[85/300], Temp: 0.8147, Energy: -45.466135+0.002692j
[2025-08-26 23:10:02] [Iter 237/450] R1[86/300], Temp: 0.8106, Energy: -45.487583-0.002913j
[2025-08-26 23:10:14] [Iter 238/450] R1[87/300], Temp: 0.8065, Energy: -45.495202-0.002404j
[2025-08-26 23:10:27] [Iter 239/450] R1[88/300], Temp: 0.8023, Energy: -45.482433-0.001478j
[2025-08-26 23:10:39] [Iter 240/450] R1[89/300], Temp: 0.7981, Energy: -45.480918+0.003195j
[2025-08-26 23:10:52] [Iter 241/450] R1[90/300], Temp: 0.7939, Energy: -45.491124+0.001722j
[2025-08-26 23:11:04] [Iter 242/450] R1[91/300], Temp: 0.7896, Energy: -45.488371+0.002414j
[2025-08-26 23:11:17] [Iter 243/450] R1[92/300], Temp: 0.7854, Energy: -45.487220+0.000922j
[2025-08-26 23:11:29] [Iter 244/450] R1[93/300], Temp: 0.7810, Energy: -45.487737-0.003153j
[2025-08-26 23:11:42] [Iter 245/450] R1[94/300], Temp: 0.7767, Energy: -45.489048+0.002287j
[2025-08-26 23:11:54] [Iter 246/450] R1[95/300], Temp: 0.7723, Energy: -45.494862-0.001676j
[2025-08-26 23:12:07] [Iter 247/450] R1[96/300], Temp: 0.7679, Energy: -45.487888-0.001912j
[2025-08-26 23:12:19] [Iter 248/450] R1[97/300], Temp: 0.7635, Energy: -45.494500-0.001827j
[2025-08-26 23:12:32] [Iter 249/450] R1[98/300], Temp: 0.7590, Energy: -45.488612+0.002933j
[2025-08-26 23:12:44] [Iter 250/450] R1[99/300], Temp: 0.7545, Energy: -45.490362-0.002384j
[2025-08-26 23:12:44] ✓ Checkpoint saved: checkpoint_iter_000250.pkl
[2025-08-26 23:12:57] [Iter 251/450] R1[100/300], Temp: 0.7500, Energy: -45.492891-0.001849j
[2025-08-26 23:13:09] [Iter 252/450] R1[101/300], Temp: 0.7455, Energy: -45.494678-0.001579j
[2025-08-26 23:13:22] [Iter 253/450] R1[102/300], Temp: 0.7409, Energy: -45.489913+0.000769j
[2025-08-26 23:13:34] [Iter 254/450] R1[103/300], Temp: 0.7363, Energy: -45.470183+0.002977j
[2025-08-26 23:13:47] [Iter 255/450] R1[104/300], Temp: 0.7316, Energy: -45.497541-0.004753j
[2025-08-26 23:13:59] [Iter 256/450] R1[105/300], Temp: 0.7270, Energy: -45.480076-0.000409j
[2025-08-26 23:14:12] [Iter 257/450] R1[106/300], Temp: 0.7223, Energy: -45.492316-0.001691j
[2025-08-26 23:14:24] [Iter 258/450] R1[107/300], Temp: 0.7176, Energy: -45.475835+0.003002j
[2025-08-26 23:14:37] [Iter 259/450] R1[108/300], Temp: 0.7129, Energy: -45.474386-0.010566j
[2025-08-26 23:14:49] [Iter 260/450] R1[109/300], Temp: 0.7081, Energy: -45.478273-0.003611j
[2025-08-26 23:15:02] [Iter 261/450] R1[110/300], Temp: 0.7034, Energy: -45.488298+0.001337j
[2025-08-26 23:15:14] [Iter 262/450] R1[111/300], Temp: 0.6986, Energy: -45.509113+0.003662j
[2025-08-26 23:15:26] [Iter 263/450] R1[112/300], Temp: 0.6938, Energy: -45.509833-0.000734j
[2025-08-26 23:15:39] [Iter 264/450] R1[113/300], Temp: 0.6889, Energy: -45.481461-0.001936j
[2025-08-26 23:15:51] [Iter 265/450] R1[114/300], Temp: 0.6841, Energy: -45.488236+0.003523j
[2025-08-26 23:16:04] [Iter 266/450] R1[115/300], Temp: 0.6792, Energy: -45.507711-0.000483j
[2025-08-26 23:16:16] [Iter 267/450] R1[116/300], Temp: 0.6743, Energy: -45.475467-0.004606j
[2025-08-26 23:16:29] [Iter 268/450] R1[117/300], Temp: 0.6694, Energy: -45.494989-0.003799j
[2025-08-26 23:16:41] [Iter 269/450] R1[118/300], Temp: 0.6644, Energy: -45.493901-0.002910j
[2025-08-26 23:16:54] [Iter 270/450] R1[119/300], Temp: 0.6595, Energy: -45.493095+0.002934j
[2025-08-26 23:17:06] [Iter 271/450] R1[120/300], Temp: 0.6545, Energy: -45.473673+0.006411j
[2025-08-26 23:17:19] [Iter 272/450] R1[121/300], Temp: 0.6495, Energy: -45.495356-0.003034j
[2025-08-26 23:17:31] [Iter 273/450] R1[122/300], Temp: 0.6445, Energy: -45.481912+0.000720j
[2025-08-26 23:17:44] [Iter 274/450] R1[123/300], Temp: 0.6395, Energy: -45.492122-0.000082j
[2025-08-26 23:17:56] [Iter 275/450] R1[124/300], Temp: 0.6345, Energy: -45.471995+0.000578j
[2025-08-26 23:18:09] [Iter 276/450] R1[125/300], Temp: 0.6294, Energy: -45.497218-0.000432j
[2025-08-26 23:18:21] [Iter 277/450] R1[126/300], Temp: 0.6243, Energy: -45.483853-0.003995j
[2025-08-26 23:18:34] [Iter 278/450] R1[127/300], Temp: 0.6193, Energy: -45.480995-0.001474j
[2025-08-26 23:18:46] [Iter 279/450] R1[128/300], Temp: 0.6142, Energy: -45.490615+0.001451j
[2025-08-26 23:18:59] [Iter 280/450] R1[129/300], Temp: 0.6091, Energy: -45.491690-0.003821j
[2025-08-26 23:19:11] [Iter 281/450] R1[130/300], Temp: 0.6040, Energy: -45.491548-0.000528j
[2025-08-26 23:19:24] [Iter 282/450] R1[131/300], Temp: 0.5988, Energy: -45.493480+0.002116j
[2025-08-26 23:19:36] [Iter 283/450] R1[132/300], Temp: 0.5937, Energy: -45.487740-0.000685j
[2025-08-26 23:19:49] [Iter 284/450] R1[133/300], Temp: 0.5885, Energy: -45.490758-0.001627j
[2025-08-26 23:20:01] [Iter 285/450] R1[134/300], Temp: 0.5834, Energy: -45.486155-0.006344j
[2025-08-26 23:20:14] [Iter 286/450] R1[135/300], Temp: 0.5782, Energy: -45.478094-0.005725j
[2025-08-26 23:20:26] [Iter 287/450] R1[136/300], Temp: 0.5730, Energy: -45.471617+0.000599j
[2025-08-26 23:20:39] [Iter 288/450] R1[137/300], Temp: 0.5679, Energy: -45.495184-0.001335j
[2025-08-26 23:20:51] [Iter 289/450] R1[138/300], Temp: 0.5627, Energy: -45.491053-0.002593j
[2025-08-26 23:21:04] [Iter 290/450] R1[139/300], Temp: 0.5575, Energy: -45.497271+0.005732j
[2025-08-26 23:21:16] [Iter 291/450] R1[140/300], Temp: 0.5523, Energy: -45.483432-0.002435j
[2025-08-26 23:21:28] [Iter 292/450] R1[141/300], Temp: 0.5471, Energy: -45.483377-0.001994j
[2025-08-26 23:21:41] [Iter 293/450] R1[142/300], Temp: 0.5418, Energy: -45.501754-0.004463j
[2025-08-26 23:21:53] [Iter 294/450] R1[143/300], Temp: 0.5366, Energy: -45.478098+0.002859j
[2025-08-26 23:22:06] [Iter 295/450] R1[144/300], Temp: 0.5314, Energy: -45.490080+0.000552j
[2025-08-26 23:22:18] [Iter 296/450] R1[145/300], Temp: 0.5262, Energy: -45.475389+0.004400j
[2025-08-26 23:22:31] [Iter 297/450] R1[146/300], Temp: 0.5209, Energy: -45.502812+0.000178j
[2025-08-26 23:22:43] [Iter 298/450] R1[147/300], Temp: 0.5157, Energy: -45.490649+0.002184j
[2025-08-26 23:22:56] [Iter 299/450] R1[148/300], Temp: 0.5105, Energy: -45.488979-0.002616j
[2025-08-26 23:23:08] [Iter 300/450] R1[149/300], Temp: 0.5052, Energy: -45.502237+0.000071j
[2025-08-26 23:23:08] ✓ Checkpoint saved: checkpoint_iter_000300.pkl
[2025-08-26 23:23:21] [Iter 301/450] R1[150/300], Temp: 0.5000, Energy: -45.493325-0.001833j
[2025-08-26 23:23:33] [Iter 302/450] R1[151/300], Temp: 0.4948, Energy: -45.485258-0.000014j
[2025-08-26 23:23:46] [Iter 303/450] R1[152/300], Temp: 0.4895, Energy: -45.483331+0.000187j
[2025-08-26 23:23:58] [Iter 304/450] R1[153/300], Temp: 0.4843, Energy: -45.471729-0.000779j
[2025-08-26 23:24:11] [Iter 305/450] R1[154/300], Temp: 0.4791, Energy: -45.498397-0.003371j
[2025-08-26 23:24:23] [Iter 306/450] R1[155/300], Temp: 0.4738, Energy: -45.486166-0.001029j
[2025-08-26 23:24:36] [Iter 307/450] R1[156/300], Temp: 0.4686, Energy: -45.477564+0.000565j
[2025-08-26 23:24:48] [Iter 308/450] R1[157/300], Temp: 0.4634, Energy: -45.486125+0.000866j
[2025-08-26 23:25:01] [Iter 309/450] R1[158/300], Temp: 0.4582, Energy: -45.487191-0.001690j
[2025-08-26 23:25:13] [Iter 310/450] R1[159/300], Temp: 0.4529, Energy: -45.512438-0.000633j
[2025-08-26 23:25:26] [Iter 311/450] R1[160/300], Temp: 0.4477, Energy: -45.489616-0.003318j
[2025-08-26 23:25:38] [Iter 312/450] R1[161/300], Temp: 0.4425, Energy: -45.492659+0.001491j
[2025-08-26 23:25:51] [Iter 313/450] R1[162/300], Temp: 0.4373, Energy: -45.491886-0.000674j
[2025-08-26 23:26:03] [Iter 314/450] R1[163/300], Temp: 0.4321, Energy: -45.479880-0.000919j
[2025-08-26 23:26:16] [Iter 315/450] R1[164/300], Temp: 0.4270, Energy: -45.478019+0.006824j
[2025-08-26 23:26:28] [Iter 316/450] R1[165/300], Temp: 0.4218, Energy: -45.486524+0.004227j
[2025-08-26 23:26:41] [Iter 317/450] R1[166/300], Temp: 0.4166, Energy: -45.497149+0.000919j
[2025-08-26 23:26:53] [Iter 318/450] R1[167/300], Temp: 0.4115, Energy: -45.483175+0.004198j
[2025-08-26 23:27:06] [Iter 319/450] R1[168/300], Temp: 0.4063, Energy: -45.478411+0.003086j
[2025-08-26 23:27:18] [Iter 320/450] R1[169/300], Temp: 0.4012, Energy: -45.474523+0.001197j
[2025-08-26 23:27:31] [Iter 321/450] R1[170/300], Temp: 0.3960, Energy: -45.493325-0.000639j
[2025-08-26 23:27:43] [Iter 322/450] R1[171/300], Temp: 0.3909, Energy: -45.493231+0.000904j
[2025-08-26 23:27:56] [Iter 323/450] R1[172/300], Temp: 0.3858, Energy: -45.500718-0.000630j
[2025-08-26 23:28:08] [Iter 324/450] R1[173/300], Temp: 0.3807, Energy: -45.483125+0.002051j
[2025-08-26 23:28:21] [Iter 325/450] R1[174/300], Temp: 0.3757, Energy: -45.484754-0.001068j
[2025-08-26 23:28:33] [Iter 326/450] R1[175/300], Temp: 0.3706, Energy: -45.482616-0.001470j
[2025-08-26 23:28:46] [Iter 327/450] R1[176/300], Temp: 0.3655, Energy: -45.489799-0.002886j
[2025-08-26 23:28:58] [Iter 328/450] R1[177/300], Temp: 0.3605, Energy: -45.497032-0.002750j
[2025-08-26 23:29:11] [Iter 329/450] R1[178/300], Temp: 0.3555, Energy: -45.483884+0.001081j
[2025-08-26 23:29:23] [Iter 330/450] R1[179/300], Temp: 0.3505, Energy: -45.500718+0.000873j
[2025-08-26 23:29:36] [Iter 331/450] R1[180/300], Temp: 0.3455, Energy: -45.489493-0.010743j
[2025-08-26 23:29:48] [Iter 332/450] R1[181/300], Temp: 0.3405, Energy: -45.494758-0.001800j
[2025-08-26 23:30:01] [Iter 333/450] R1[182/300], Temp: 0.3356, Energy: -45.490918-0.001838j
[2025-08-26 23:30:13] [Iter 334/450] R1[183/300], Temp: 0.3306, Energy: -45.492288-0.000687j
[2025-08-26 23:30:26] [Iter 335/450] R1[184/300], Temp: 0.3257, Energy: -45.489704+0.001344j
[2025-08-26 23:30:38] [Iter 336/450] R1[185/300], Temp: 0.3208, Energy: -45.493390+0.003211j
[2025-08-26 23:30:51] [Iter 337/450] R1[186/300], Temp: 0.3159, Energy: -45.501049+0.000566j
[2025-08-26 23:31:03] [Iter 338/450] R1[187/300], Temp: 0.3111, Energy: -45.490764-0.000935j
[2025-08-26 23:31:16] [Iter 339/450] R1[188/300], Temp: 0.3062, Energy: -45.502296-0.001978j
[2025-08-26 23:31:28] [Iter 340/450] R1[189/300], Temp: 0.3014, Energy: -45.489810+0.004203j
[2025-08-26 23:31:41] [Iter 341/450] R1[190/300], Temp: 0.2966, Energy: -45.483772-0.002191j
[2025-08-26 23:31:53] [Iter 342/450] R1[191/300], Temp: 0.2919, Energy: -45.492344-0.004943j
[2025-08-26 23:32:06] [Iter 343/450] R1[192/300], Temp: 0.2871, Energy: -45.500267+0.000573j
[2025-08-26 23:32:18] [Iter 344/450] R1[193/300], Temp: 0.2824, Energy: -45.487248+0.000532j
[2025-08-26 23:32:31] [Iter 345/450] R1[194/300], Temp: 0.2777, Energy: -45.488084+0.000371j
[2025-08-26 23:32:43] [Iter 346/450] R1[195/300], Temp: 0.2730, Energy: -45.490608-0.001559j
[2025-08-26 23:32:56] [Iter 347/450] R1[196/300], Temp: 0.2684, Energy: -45.487114+0.001718j
[2025-08-26 23:33:08] [Iter 348/450] R1[197/300], Temp: 0.2637, Energy: -45.473867+0.003348j
[2025-08-26 23:33:21] [Iter 349/450] R1[198/300], Temp: 0.2591, Energy: -45.493210-0.001395j
[2025-08-26 23:33:33] [Iter 350/450] R1[199/300], Temp: 0.2545, Energy: -45.497340+0.002916j
[2025-08-26 23:33:33] ✓ Checkpoint saved: checkpoint_iter_000350.pkl
[2025-08-26 23:33:46] [Iter 351/450] R1[200/300], Temp: 0.2500, Energy: -45.469995+0.010581j
[2025-08-26 23:33:58] [Iter 352/450] R1[201/300], Temp: 0.2455, Energy: -45.489029-0.004848j
[2025-08-26 23:34:11] [Iter 353/450] R1[202/300], Temp: 0.2410, Energy: -45.493628+0.002054j
[2025-08-26 23:34:23] [Iter 354/450] R1[203/300], Temp: 0.2365, Energy: -45.490549-0.004926j
[2025-08-26 23:34:36] [Iter 355/450] R1[204/300], Temp: 0.2321, Energy: -45.485301-0.000265j
[2025-08-26 23:34:48] [Iter 356/450] R1[205/300], Temp: 0.2277, Energy: -45.491412-0.003693j
[2025-08-26 23:35:01] [Iter 357/450] R1[206/300], Temp: 0.2233, Energy: -45.493365+0.000146j
[2025-08-26 23:35:13] [Iter 358/450] R1[207/300], Temp: 0.2190, Energy: -45.497685+0.004353j
[2025-08-26 23:35:26] [Iter 359/450] R1[208/300], Temp: 0.2146, Energy: -45.489699-0.006727j
[2025-08-26 23:35:38] [Iter 360/450] R1[209/300], Temp: 0.2104, Energy: -45.483899-0.000215j
[2025-08-26 23:35:51] [Iter 361/450] R1[210/300], Temp: 0.2061, Energy: -45.505689+0.000575j
[2025-08-26 23:36:03] [Iter 362/450] R1[211/300], Temp: 0.2019, Energy: -45.488135+0.003446j
[2025-08-26 23:36:16] [Iter 363/450] R1[212/300], Temp: 0.1977, Energy: -45.498546-0.004027j
[2025-08-26 23:36:28] [Iter 364/450] R1[213/300], Temp: 0.1935, Energy: -45.486614-0.001373j
[2025-08-26 23:36:41] [Iter 365/450] R1[214/300], Temp: 0.1894, Energy: -45.484964+0.000705j
[2025-08-26 23:36:53] [Iter 366/450] R1[215/300], Temp: 0.1853, Energy: -45.497228+0.002382j
[2025-08-26 23:37:06] [Iter 367/450] R1[216/300], Temp: 0.1813, Energy: -45.486760-0.005388j
[2025-08-26 23:37:18] [Iter 368/450] R1[217/300], Temp: 0.1773, Energy: -45.490797+0.000072j
[2025-08-26 23:37:31] [Iter 369/450] R1[218/300], Temp: 0.1733, Energy: -45.494261-0.003070j
[2025-08-26 23:37:43] [Iter 370/450] R1[219/300], Temp: 0.1693, Energy: -45.482045-0.000879j
[2025-08-26 23:37:56] [Iter 371/450] R1[220/300], Temp: 0.1654, Energy: -45.499912-0.000613j
[2025-08-26 23:38:08] [Iter 372/450] R1[221/300], Temp: 0.1616, Energy: -45.478829+0.000714j
[2025-08-26 23:38:21] [Iter 373/450] R1[222/300], Temp: 0.1577, Energy: -45.491859+0.000669j
[2025-08-26 23:38:33] [Iter 374/450] R1[223/300], Temp: 0.1539, Energy: -45.479935+0.000211j
[2025-08-26 23:38:46] [Iter 375/450] R1[224/300], Temp: 0.1502, Energy: -45.489099-0.000501j
[2025-08-26 23:38:58] [Iter 376/450] R1[225/300], Temp: 0.1464, Energy: -45.486101+0.000131j
[2025-08-26 23:39:11] [Iter 377/450] R1[226/300], Temp: 0.1428, Energy: -45.495170-0.004312j
[2025-08-26 23:39:23] [Iter 378/450] R1[227/300], Temp: 0.1391, Energy: -45.502693+0.000936j
[2025-08-26 23:39:36] [Iter 379/450] R1[228/300], Temp: 0.1355, Energy: -45.487961+0.002550j
[2025-08-26 23:39:48] [Iter 380/450] R1[229/300], Temp: 0.1320, Energy: -45.492669+0.001131j
[2025-08-26 23:40:01] [Iter 381/450] R1[230/300], Temp: 0.1284, Energy: -45.495499-0.005247j
[2025-08-26 23:40:13] [Iter 382/450] R1[231/300], Temp: 0.1249, Energy: -45.503276+0.001098j
[2025-08-26 23:40:26] [Iter 383/450] R1[232/300], Temp: 0.1215, Energy: -45.475980-0.000861j
[2025-08-26 23:40:38] [Iter 384/450] R1[233/300], Temp: 0.1181, Energy: -45.480831+0.002551j
[2025-08-26 23:40:51] [Iter 385/450] R1[234/300], Temp: 0.1147, Energy: -45.490148+0.003820j
[2025-08-26 23:41:03] [Iter 386/450] R1[235/300], Temp: 0.1114, Energy: -45.488843-0.010816j
[2025-08-26 23:41:16] [Iter 387/450] R1[236/300], Temp: 0.1082, Energy: -45.477729+0.004118j
[2025-08-26 23:41:28] [Iter 388/450] R1[237/300], Temp: 0.1049, Energy: -45.490863+0.005645j
[2025-08-26 23:41:40] [Iter 389/450] R1[238/300], Temp: 0.1017, Energy: -45.487722+0.003185j
[2025-08-26 23:41:53] [Iter 390/450] R1[239/300], Temp: 0.0986, Energy: -45.490407+0.003297j
[2025-08-26 23:42:05] [Iter 391/450] R1[240/300], Temp: 0.0955, Energy: -45.488838-0.002620j
[2025-08-26 23:42:18] [Iter 392/450] R1[241/300], Temp: 0.0924, Energy: -45.473942-0.001168j
[2025-08-26 23:42:30] [Iter 393/450] R1[242/300], Temp: 0.0894, Energy: -45.505021+0.002108j
[2025-08-26 23:42:43] [Iter 394/450] R1[243/300], Temp: 0.0865, Energy: -45.483119+0.000006j
[2025-08-26 23:42:55] [Iter 395/450] R1[244/300], Temp: 0.0835, Energy: -45.509859-0.002138j
[2025-08-26 23:43:08] [Iter 396/450] R1[245/300], Temp: 0.0807, Energy: -45.491028+0.005194j
[2025-08-26 23:43:20] [Iter 397/450] R1[246/300], Temp: 0.0778, Energy: -45.510670-0.001651j
[2025-08-26 23:43:33] [Iter 398/450] R1[247/300], Temp: 0.0751, Energy: -45.484658+0.003883j
[2025-08-26 23:43:46] [Iter 399/450] R1[248/300], Temp: 0.0723, Energy: -45.499155-0.004082j
[2025-08-26 23:43:58] [Iter 400/450] R1[249/300], Temp: 0.0696, Energy: -45.493553-0.000318j
[2025-08-26 23:43:59] ✓ Checkpoint saved: checkpoint_iter_000400.pkl
[2025-08-26 23:44:12] [Iter 401/450] R1[250/300], Temp: 0.0670, Energy: -45.483590+0.004630j
[2025-08-26 23:44:24] [Iter 402/450] R1[251/300], Temp: 0.0644, Energy: -45.499470+0.002672j
[2025-08-26 23:44:37] [Iter 403/450] R1[252/300], Temp: 0.0618, Energy: -45.483281+0.000389j
[2025-08-26 23:44:49] [Iter 404/450] R1[253/300], Temp: 0.0593, Energy: -45.512373+0.003163j
[2025-08-26 23:45:02] [Iter 405/450] R1[254/300], Temp: 0.0569, Energy: -45.491452+0.001004j
[2025-08-26 23:45:14] [Iter 406/450] R1[255/300], Temp: 0.0545, Energy: -45.481120+0.002371j
[2025-08-26 23:45:27] [Iter 407/450] R1[256/300], Temp: 0.0521, Energy: -45.487820+0.000528j
[2025-08-26 23:45:39] [Iter 408/450] R1[257/300], Temp: 0.0498, Energy: -45.499846+0.000275j
[2025-08-26 23:45:52] [Iter 409/450] R1[258/300], Temp: 0.0476, Energy: -45.486050+0.001171j
[2025-08-26 23:46:04] [Iter 410/450] R1[259/300], Temp: 0.0454, Energy: -45.495911+0.002901j
[2025-08-26 23:46:17] [Iter 411/450] R1[260/300], Temp: 0.0432, Energy: -45.474302-0.002990j
[2025-08-26 23:46:29] [Iter 412/450] R1[261/300], Temp: 0.0411, Energy: -45.495551-0.005554j
[2025-08-26 23:46:42] [Iter 413/450] R1[262/300], Temp: 0.0391, Energy: -45.497709-0.001212j
[2025-08-26 23:46:54] [Iter 414/450] R1[263/300], Temp: 0.0371, Energy: -45.499147+0.000399j
[2025-08-26 23:47:07] [Iter 415/450] R1[264/300], Temp: 0.0351, Energy: -45.487974+0.007161j
[2025-08-26 23:47:19] [Iter 416/450] R1[265/300], Temp: 0.0332, Energy: -45.482220-0.005664j
[2025-08-26 23:47:32] [Iter 417/450] R1[266/300], Temp: 0.0314, Energy: -45.489903-0.000695j
[2025-08-26 23:47:44] [Iter 418/450] R1[267/300], Temp: 0.0296, Energy: -45.495870-0.001235j
[2025-08-26 23:47:57] [Iter 419/450] R1[268/300], Temp: 0.0278, Energy: -45.475774-0.001181j
[2025-08-26 23:48:09] [Iter 420/450] R1[269/300], Temp: 0.0261, Energy: -45.480238-0.000491j
[2025-08-26 23:48:21] [Iter 421/450] R1[270/300], Temp: 0.0245, Energy: -45.493205-0.003462j
[2025-08-26 23:48:34] [Iter 422/450] R1[271/300], Temp: 0.0229, Energy: -45.480030+0.004200j
[2025-08-26 23:48:46] [Iter 423/450] R1[272/300], Temp: 0.0213, Energy: -45.487835+0.004890j
[2025-08-26 23:48:59] [Iter 424/450] R1[273/300], Temp: 0.0199, Energy: -45.485438+0.000142j
[2025-08-26 23:49:11] [Iter 425/450] R1[274/300], Temp: 0.0184, Energy: -45.494155-0.001307j
[2025-08-26 23:49:24] [Iter 426/450] R1[275/300], Temp: 0.0170, Energy: -45.479215+0.001566j
[2025-08-26 23:49:36] [Iter 427/450] R1[276/300], Temp: 0.0157, Energy: -45.490928+0.002152j
[2025-08-26 23:49:49] [Iter 428/450] R1[277/300], Temp: 0.0144, Energy: -45.508401-0.005367j
[2025-08-26 23:50:01] [Iter 429/450] R1[278/300], Temp: 0.0132, Energy: -45.490468+0.000676j
[2025-08-26 23:50:14] [Iter 430/450] R1[279/300], Temp: 0.0120, Energy: -45.482097+0.001003j
[2025-08-26 23:50:26] [Iter 431/450] R1[280/300], Temp: 0.0109, Energy: -45.487075+0.001558j
[2025-08-26 23:50:39] [Iter 432/450] R1[281/300], Temp: 0.0099, Energy: -45.484505+0.001134j
[2025-08-26 23:50:51] [Iter 433/450] R1[282/300], Temp: 0.0089, Energy: -45.490732-0.003920j
[2025-08-26 23:51:04] [Iter 434/450] R1[283/300], Temp: 0.0079, Energy: -45.494012-0.000930j
[2025-08-26 23:51:16] [Iter 435/450] R1[284/300], Temp: 0.0070, Energy: -45.499612-0.001902j
[2025-08-26 23:51:29] [Iter 436/450] R1[285/300], Temp: 0.0062, Energy: -45.507874-0.001071j
[2025-08-26 23:51:41] [Iter 437/450] R1[286/300], Temp: 0.0054, Energy: -45.499014-0.000009j
[2025-08-26 23:51:54] [Iter 438/450] R1[287/300], Temp: 0.0046, Energy: -45.481009-0.003647j
[2025-08-26 23:52:06] [Iter 439/450] R1[288/300], Temp: 0.0039, Energy: -45.474154-0.001040j
[2025-08-26 23:52:19] [Iter 440/450] R1[289/300], Temp: 0.0033, Energy: -45.481516-0.004086j
[2025-08-26 23:52:31] [Iter 441/450] R1[290/300], Temp: 0.0027, Energy: -45.485897+0.001675j
[2025-08-26 23:52:44] [Iter 442/450] R1[291/300], Temp: 0.0022, Energy: -45.505573-0.001948j
[2025-08-26 23:52:56] [Iter 443/450] R1[292/300], Temp: 0.0018, Energy: -45.502336-0.000891j
[2025-08-26 23:53:09] [Iter 444/450] R1[293/300], Temp: 0.0013, Energy: -45.489010+0.003849j
[2025-08-26 23:53:21] [Iter 445/450] R1[294/300], Temp: 0.0010, Energy: -45.490433-0.000217j
[2025-08-26 23:53:34] [Iter 446/450] R1[295/300], Temp: 0.0007, Energy: -45.483489+0.004970j
[2025-08-26 23:53:46] [Iter 447/450] R1[296/300], Temp: 0.0004, Energy: -45.484790-0.002425j
[2025-08-26 23:53:58] [Iter 448/450] R1[297/300], Temp: 0.0002, Energy: -45.493864-0.000183j
[2025-08-26 23:54:11] [Iter 449/450] R1[298/300], Temp: 0.0001, Energy: -45.476635-0.002060j
[2025-08-26 23:54:23] [Iter 450/450] R1[299/300], Temp: 0.0000, Energy: -45.483602+0.001030j
[2025-08-26 23:54:24] ✓ Checkpoint saved: checkpoint_iter_000450.pkl
[2025-08-26 23:54:24] ✅ Training completed | Restarts: 1
[2025-08-26 23:54:24] ============================================================
[2025-08-26 23:54:24] Training completed | Runtime: 5653.5s
[2025-08-26 23:54:28] ✓ Final state saved: checkpoints/final_GCNN.pkl
[2025-08-26 23:54:28] ============================================================
