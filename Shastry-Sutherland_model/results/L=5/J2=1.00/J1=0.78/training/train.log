[2025-08-26 23:54:46] ✓ 从checkpoint恢复: results/L=5/J2=1.00/J1=0.79/training/checkpoints/final_GCNN.pkl
[2025-08-26 23:54:46]   - 迭代次数: final
[2025-08-26 23:54:46]   - 能量: -44.212738+0.000870j ± 0.009942
[2025-08-26 23:54:46]   - 时间戳: 2025-08-26T23:54:35.352482+08:00
[2025-08-26 23:54:56] ✓ 变分状态参数已从checkpoint恢复
[2025-08-26 23:54:56] ✓ 从final状态恢复, 重置迭代计数为0
[2025-08-26 23:54:56] ==================================================
[2025-08-26 23:54:56] GCNN for Shastry-Sutherland Model
[2025-08-26 23:54:56] ==================================================
[2025-08-26 23:54:56] System parameters:
[2025-08-26 23:54:56]   - System size: L=5, N=100
[2025-08-26 23:54:56]   - System parameters: J1=0.78, J2=1.0, Q=0.0
[2025-08-26 23:54:56] --------------------------------------------------
[2025-08-26 23:54:56] Model parameters:
[2025-08-26 23:54:56]   - Number of layers = 4
[2025-08-26 23:54:56]   - Number of features = 4
[2025-08-26 23:54:56]   - Total parameters = 19628
[2025-08-26 23:54:56] --------------------------------------------------
[2025-08-26 23:54:56] Training parameters:
[2025-08-26 23:54:56]   - Learning rate: 0.015
[2025-08-26 23:54:56]   - Total iterations: 450
[2025-08-26 23:54:56]   - Annealing cycles: 2
[2025-08-26 23:54:56]   - Initial period: 150
[2025-08-26 23:54:56]   - Period multiplier: 2.0
[2025-08-26 23:54:56]   - Temperature range: 0.0-1.0
[2025-08-26 23:54:56]   - Samples: 4096
[2025-08-26 23:54:56]   - Discarded samples: 0
[2025-08-26 23:54:56]   - Chunk size: 2048
[2025-08-26 23:54:56]   - Diagonal shift: 0.2
[2025-08-26 23:54:56]   - Gradient clipping: 1.0
[2025-08-26 23:54:56]   - Checkpoint enabled: interval=50
[2025-08-26 23:54:56]   - Checkpoint directory: results/L=5/J2=1.00/J1=0.78/training/checkpoints
[2025-08-26 23:54:56] --------------------------------------------------
[2025-08-26 23:54:56] Device status:
[2025-08-26 23:54:56]   - Devices model: NVIDIA H200 NVL
[2025-08-26 23:54:56]   - Number of devices: 1
[2025-08-26 23:54:56]   - Sharding: True
[2025-08-26 23:54:56] ============================================================
[2025-08-26 23:55:37] [Iter 1/450] R0[0/150], Temp: 1.0000, Energy: -43.501555+0.009303j
[2025-08-26 23:56:06] [Iter 2/450] R0[1/150], Temp: 0.9999, Energy: -43.554593+0.000374j
[2025-08-26 23:56:18] [Iter 3/450] R0[2/150], Temp: 0.9996, Energy: -43.577617-0.000418j
[2025-08-26 23:56:31] [Iter 4/450] R0[3/150], Temp: 0.9990, Energy: -43.559072+0.003064j
[2025-08-26 23:56:43] [Iter 5/450] R0[4/150], Temp: 0.9982, Energy: -43.574477-0.001250j
[2025-08-26 23:56:55] [Iter 6/450] R0[5/150], Temp: 0.9973, Energy: -43.559830-0.001324j
[2025-08-26 23:57:08] [Iter 7/450] R0[6/150], Temp: 0.9961, Energy: -43.563327+0.000890j
[2025-08-26 23:57:20] [Iter 8/450] R0[7/150], Temp: 0.9946, Energy: -43.586282-0.004458j
[2025-08-26 23:57:33] [Iter 9/450] R0[8/150], Temp: 0.9930, Energy: -43.582293+0.002684j
[2025-08-26 23:57:45] [Iter 10/450] R0[9/150], Temp: 0.9911, Energy: -43.574361-0.002288j
[2025-08-26 23:57:57] [Iter 11/450] R0[10/150], Temp: 0.9891, Energy: -43.568462+0.001095j
[2025-08-26 23:58:10] [Iter 12/450] R0[11/150], Temp: 0.9868, Energy: -43.553331+0.001728j
[2025-08-26 23:58:22] [Iter 13/450] R0[12/150], Temp: 0.9843, Energy: -43.573267+0.004041j
[2025-08-26 23:58:35] [Iter 14/450] R0[13/150], Temp: 0.9816, Energy: -43.574822-0.001047j
[2025-08-26 23:58:47] [Iter 15/450] R0[14/150], Temp: 0.9787, Energy: -43.560762+0.004078j
[2025-08-26 23:58:59] [Iter 16/450] R0[15/150], Temp: 0.9755, Energy: -43.590335-0.004237j
[2025-08-26 23:59:12] [Iter 17/450] R0[16/150], Temp: 0.9722, Energy: -43.572653+0.000334j
[2025-08-26 23:59:24] [Iter 18/450] R0[17/150], Temp: 0.9686, Energy: -43.573863-0.000485j
[2025-08-26 23:59:37] [Iter 19/450] R0[18/150], Temp: 0.9649, Energy: -43.575928-0.003337j
[2025-08-26 23:59:49] [Iter 20/450] R0[19/150], Temp: 0.9609, Energy: -43.564403-0.000446j
[2025-08-27 00:00:01] [Iter 21/450] R0[20/150], Temp: 0.9568, Energy: -43.567312-0.002910j
[2025-08-27 00:00:14] [Iter 22/450] R0[21/150], Temp: 0.9524, Energy: -43.537237+0.000969j
[2025-08-27 00:00:26] [Iter 23/450] R0[22/150], Temp: 0.9479, Energy: -43.571247+0.000002j
[2025-08-27 00:00:39] [Iter 24/450] R0[23/150], Temp: 0.9431, Energy: -43.574294+0.002421j
[2025-08-27 00:00:51] [Iter 25/450] R0[24/150], Temp: 0.9382, Energy: -43.564071-0.006956j
[2025-08-27 00:01:03] [Iter 26/450] R0[25/150], Temp: 0.9330, Energy: -43.567261+0.003816j
[2025-08-27 00:01:16] [Iter 27/450] R0[26/150], Temp: 0.9277, Energy: -43.580148-0.000139j
[2025-08-27 00:01:28] [Iter 28/450] R0[27/150], Temp: 0.9222, Energy: -43.568904-0.000578j
[2025-08-27 00:01:40] [Iter 29/450] R0[28/150], Temp: 0.9165, Energy: -43.569311+0.001066j
[2025-08-27 00:01:53] [Iter 30/450] R0[29/150], Temp: 0.9106, Energy: -43.568339+0.000865j
[2025-08-27 00:02:05] [Iter 31/450] R0[30/150], Temp: 0.9045, Energy: -43.571581-0.000866j
[2025-08-27 00:02:18] [Iter 32/450] R0[31/150], Temp: 0.8983, Energy: -43.561644-0.004632j
[2025-08-27 00:02:30] [Iter 33/450] R0[32/150], Temp: 0.8918, Energy: -43.550537-0.001369j
[2025-08-27 00:02:42] [Iter 34/450] R0[33/150], Temp: 0.8853, Energy: -43.568051-0.000519j
[2025-08-27 00:02:55] [Iter 35/450] R0[34/150], Temp: 0.8785, Energy: -43.573874-0.000316j
[2025-08-27 00:03:07] [Iter 36/450] R0[35/150], Temp: 0.8716, Energy: -43.573537+0.000073j
[2025-08-27 00:03:20] [Iter 37/450] R0[36/150], Temp: 0.8645, Energy: -43.568975+0.002462j
[2025-08-27 00:03:32] [Iter 38/450] R0[37/150], Temp: 0.8572, Energy: -43.583265+0.003829j
[2025-08-27 00:03:44] [Iter 39/450] R0[38/150], Temp: 0.8498, Energy: -43.582911-0.002698j
[2025-08-27 00:03:57] [Iter 40/450] R0[39/150], Temp: 0.8423, Energy: -43.577786-0.002724j
[2025-08-27 00:04:09] [Iter 41/450] R0[40/150], Temp: 0.8346, Energy: -43.577807-0.001874j
[2025-08-27 00:04:22] [Iter 42/450] R0[41/150], Temp: 0.8267, Energy: -43.554912+0.000013j
[2025-08-27 00:04:34] [Iter 43/450] R0[42/150], Temp: 0.8187, Energy: -43.576601+0.000210j
[2025-08-27 00:04:46] [Iter 44/450] R0[43/150], Temp: 0.8106, Energy: -43.568311+0.001900j
[2025-08-27 00:04:59] [Iter 45/450] R0[44/150], Temp: 0.8023, Energy: -43.577950-0.001729j
[2025-08-27 00:05:11] [Iter 46/450] R0[45/150], Temp: 0.7939, Energy: -43.566412-0.003855j
[2025-08-27 00:05:24] [Iter 47/450] R0[46/150], Temp: 0.7854, Energy: -43.565407-0.000135j
[2025-08-27 00:05:36] [Iter 48/450] R0[47/150], Temp: 0.7767, Energy: -43.555729-0.002036j
[2025-08-27 00:05:48] [Iter 49/450] R0[48/150], Temp: 0.7679, Energy: -43.568318-0.003049j
[2025-08-27 00:06:01] [Iter 50/450] R0[49/150], Temp: 0.7590, Energy: -43.590885-0.002715j
[2025-08-27 00:06:01] ✓ Checkpoint saved: checkpoint_iter_000050.pkl
[2025-08-27 00:06:13] [Iter 51/450] R0[50/150], Temp: 0.7500, Energy: -43.574302+0.000387j
[2025-08-27 00:06:26] [Iter 52/450] R0[51/150], Temp: 0.7409, Energy: -43.552744-0.002543j
[2025-08-27 00:06:38] [Iter 53/450] R0[52/150], Temp: 0.7316, Energy: -43.579368-0.002808j
[2025-08-27 00:06:50] [Iter 54/450] R0[53/150], Temp: 0.7223, Energy: -43.595943+0.001792j
[2025-08-27 00:07:03] [Iter 55/450] R0[54/150], Temp: 0.7129, Energy: -43.556366-0.000620j
[2025-08-27 00:07:15] [Iter 56/450] R0[55/150], Temp: 0.7034, Energy: -43.572460-0.002609j
[2025-08-27 00:07:27] [Iter 57/450] R0[56/150], Temp: 0.6938, Energy: -43.565948+0.000886j
[2025-08-27 00:07:40] [Iter 58/450] R0[57/150], Temp: 0.6841, Energy: -43.571557-0.002505j
[2025-08-27 00:07:52] [Iter 59/450] R0[58/150], Temp: 0.6743, Energy: -43.586588+0.002853j
[2025-08-27 00:08:05] [Iter 60/450] R0[59/150], Temp: 0.6644, Energy: -43.570614-0.001971j
[2025-08-27 00:08:17] [Iter 61/450] R0[60/150], Temp: 0.6545, Energy: -43.576891+0.005384j
[2025-08-27 00:08:29] [Iter 62/450] R0[61/150], Temp: 0.6445, Energy: -43.562735-0.000089j
[2025-08-27 00:08:42] [Iter 63/450] R0[62/150], Temp: 0.6345, Energy: -43.572283-0.004758j
[2025-08-27 00:08:54] [Iter 64/450] R0[63/150], Temp: 0.6243, Energy: -43.582526+0.002190j
[2025-08-27 00:09:07] [Iter 65/450] R0[64/150], Temp: 0.6142, Energy: -43.601253-0.000727j
[2025-08-27 00:09:19] [Iter 66/450] R0[65/150], Temp: 0.6040, Energy: -43.571482+0.001352j
[2025-08-27 00:09:31] [Iter 67/450] R0[66/150], Temp: 0.5937, Energy: -43.584151-0.000558j
[2025-08-27 00:09:44] [Iter 68/450] R0[67/150], Temp: 0.5834, Energy: -43.586634-0.001241j
[2025-08-27 00:09:56] [Iter 69/450] R0[68/150], Temp: 0.5730, Energy: -43.575032+0.002468j
[2025-08-27 00:10:09] [Iter 70/450] R0[69/150], Temp: 0.5627, Energy: -43.574133+0.001949j
[2025-08-27 00:10:21] [Iter 71/450] R0[70/150], Temp: 0.5523, Energy: -43.566311+0.003119j
[2025-08-27 00:10:33] [Iter 72/450] R0[71/150], Temp: 0.5418, Energy: -43.572672+0.002398j
[2025-08-27 00:10:46] [Iter 73/450] R0[72/150], Temp: 0.5314, Energy: -43.568238-0.000968j
[2025-08-27 00:10:58] [Iter 74/450] R0[73/150], Temp: 0.5209, Energy: -43.584111+0.002682j
[2025-08-27 00:11:11] [Iter 75/450] R0[74/150], Temp: 0.5105, Energy: -43.571492-0.007660j
[2025-08-27 00:11:23] [Iter 76/450] R0[75/150], Temp: 0.5000, Energy: -43.554438+0.003182j
[2025-08-27 00:11:35] [Iter 77/450] R0[76/150], Temp: 0.4895, Energy: -43.571822-0.000187j
[2025-08-27 00:11:48] [Iter 78/450] R0[77/150], Temp: 0.4791, Energy: -43.553513-0.002521j
[2025-08-27 00:12:00] [Iter 79/450] R0[78/150], Temp: 0.4686, Energy: -43.569791+0.001745j
[2025-08-27 00:12:13] [Iter 80/450] R0[79/150], Temp: 0.4582, Energy: -43.564116+0.000501j
[2025-08-27 00:12:25] [Iter 81/450] R0[80/150], Temp: 0.4477, Energy: -43.573680-0.000329j
[2025-08-27 00:12:37] [Iter 82/450] R0[81/150], Temp: 0.4373, Energy: -43.561518+0.005267j
[2025-08-27 00:12:50] [Iter 83/450] R0[82/150], Temp: 0.4270, Energy: -43.578901+0.000480j
[2025-08-27 00:13:02] [Iter 84/450] R0[83/150], Temp: 0.4166, Energy: -43.581757+0.003172j
[2025-08-27 00:13:15] [Iter 85/450] R0[84/150], Temp: 0.4063, Energy: -43.575514+0.001799j
[2025-08-27 00:13:27] [Iter 86/450] R0[85/150], Temp: 0.3960, Energy: -43.576686+0.000311j
[2025-08-27 00:13:39] [Iter 87/450] R0[86/150], Temp: 0.3858, Energy: -43.556819-0.000326j
[2025-08-27 00:13:52] [Iter 88/450] R0[87/150], Temp: 0.3757, Energy: -43.564405+0.005061j
[2025-08-27 00:14:04] [Iter 89/450] R0[88/150], Temp: 0.3655, Energy: -43.573970+0.003833j
[2025-08-27 00:14:16] [Iter 90/450] R0[89/150], Temp: 0.3555, Energy: -43.578750-0.005480j
[2025-08-27 00:14:29] [Iter 91/450] R0[90/150], Temp: 0.3455, Energy: -43.559611-0.001929j
[2025-08-27 00:14:41] [Iter 92/450] R0[91/150], Temp: 0.3356, Energy: -43.558411+0.002712j
[2025-08-27 00:14:54] [Iter 93/450] R0[92/150], Temp: 0.3257, Energy: -43.574655-0.001297j
[2025-08-27 00:15:06] [Iter 94/450] R0[93/150], Temp: 0.3159, Energy: -43.565998+0.001887j
[2025-08-27 00:15:18] [Iter 95/450] R0[94/150], Temp: 0.3062, Energy: -43.572378+0.002035j
[2025-08-27 00:15:31] [Iter 96/450] R0[95/150], Temp: 0.2966, Energy: -43.573111+0.002361j
[2025-08-27 00:15:43] [Iter 97/450] R0[96/150], Temp: 0.2871, Energy: -43.549783+0.000264j
[2025-08-27 00:15:56] [Iter 98/450] R0[97/150], Temp: 0.2777, Energy: -43.557715-0.000763j
[2025-08-27 00:16:08] [Iter 99/450] R0[98/150], Temp: 0.2684, Energy: -43.574935+0.004044j
[2025-08-27 00:16:20] [Iter 100/450] R0[99/150], Temp: 0.2591, Energy: -43.581320+0.001819j
[2025-08-27 00:16:21] ✓ Checkpoint saved: checkpoint_iter_000100.pkl
[2025-08-27 00:16:33] [Iter 101/450] R0[100/150], Temp: 0.2500, Energy: -43.578753+0.001948j
[2025-08-27 00:16:45] [Iter 102/450] R0[101/150], Temp: 0.2410, Energy: -43.573291+0.000607j
[2025-08-27 00:16:58] [Iter 103/450] R0[102/150], Temp: 0.2321, Energy: -43.589186-0.004244j
[2025-08-27 00:17:10] [Iter 104/450] R0[103/150], Temp: 0.2233, Energy: -43.568122+0.001733j
[2025-08-27 00:17:22] [Iter 105/450] R0[104/150], Temp: 0.2146, Energy: -43.571111+0.000826j
[2025-08-27 00:17:35] [Iter 106/450] R0[105/150], Temp: 0.2061, Energy: -43.555732+0.001168j
[2025-08-27 00:17:47] [Iter 107/450] R0[106/150], Temp: 0.1977, Energy: -43.580360-0.011119j
[2025-08-27 00:17:59] [Iter 108/450] R0[107/150], Temp: 0.1894, Energy: -43.554678+0.003446j
[2025-08-27 00:18:12] [Iter 109/450] R0[108/150], Temp: 0.1813, Energy: -43.582459-0.001015j
[2025-08-27 00:18:24] [Iter 110/450] R0[109/150], Temp: 0.1733, Energy: -43.562318-0.000128j
[2025-08-27 00:18:37] [Iter 111/450] R0[110/150], Temp: 0.1654, Energy: -43.564880-0.004303j
[2025-08-27 00:18:49] [Iter 112/450] R0[111/150], Temp: 0.1577, Energy: -43.569595+0.005940j
[2025-08-27 00:19:01] [Iter 113/450] R0[112/150], Temp: 0.1502, Energy: -43.574865-0.004383j
[2025-08-27 00:19:14] [Iter 114/450] R0[113/150], Temp: 0.1428, Energy: -43.568800+0.003694j
[2025-08-27 00:19:26] [Iter 115/450] R0[114/150], Temp: 0.1355, Energy: -43.584613-0.004114j
[2025-08-27 00:19:38] [Iter 116/450] R0[115/150], Temp: 0.1284, Energy: -43.575801-0.000749j
[2025-08-27 00:19:51] [Iter 117/450] R0[116/150], Temp: 0.1215, Energy: -43.587577-0.003187j
[2025-08-27 00:20:03] [Iter 118/450] R0[117/150], Temp: 0.1147, Energy: -43.558824-0.000261j
[2025-08-27 00:20:16] [Iter 119/450] R0[118/150], Temp: 0.1082, Energy: -43.573857-0.000473j
[2025-08-27 00:20:28] [Iter 120/450] R0[119/150], Temp: 0.1017, Energy: -43.570493-0.000942j
[2025-08-27 00:20:40] [Iter 121/450] R0[120/150], Temp: 0.0955, Energy: -43.575416-0.001663j
[2025-08-27 00:20:53] [Iter 122/450] R0[121/150], Temp: 0.0894, Energy: -43.568404+0.005139j
[2025-08-27 00:21:05] [Iter 123/450] R0[122/150], Temp: 0.0835, Energy: -43.593616+0.001572j
[2025-08-27 00:21:18] [Iter 124/450] R0[123/150], Temp: 0.0778, Energy: -43.562678-0.004059j
[2025-08-27 00:21:30] [Iter 125/450] R0[124/150], Temp: 0.0723, Energy: -43.581840-0.000240j
[2025-08-27 00:21:42] [Iter 126/450] R0[125/150], Temp: 0.0670, Energy: -43.571290+0.000086j
[2025-08-27 00:21:55] [Iter 127/450] R0[126/150], Temp: 0.0618, Energy: -43.574305-0.002295j
[2025-08-27 00:22:07] [Iter 128/450] R0[127/150], Temp: 0.0569, Energy: -43.574686-0.000161j
[2025-08-27 00:22:19] [Iter 129/450] R0[128/150], Temp: 0.0521, Energy: -43.551794-0.002953j
[2025-08-27 00:22:32] [Iter 130/450] R0[129/150], Temp: 0.0476, Energy: -43.558854-0.005735j
[2025-08-27 00:22:44] [Iter 131/450] R0[130/150], Temp: 0.0432, Energy: -43.568000-0.006570j
[2025-08-27 00:22:57] [Iter 132/450] R0[131/150], Temp: 0.0391, Energy: -43.583473+0.001521j
[2025-08-27 00:23:09] [Iter 133/450] R0[132/150], Temp: 0.0351, Energy: -43.579074-0.001093j
[2025-08-27 00:23:21] [Iter 134/450] R0[133/150], Temp: 0.0314, Energy: -43.572629-0.000506j
[2025-08-27 00:23:34] [Iter 135/450] R0[134/150], Temp: 0.0278, Energy: -43.573937+0.003454j
[2025-08-27 00:23:46] [Iter 136/450] R0[135/150], Temp: 0.0245, Energy: -43.578704+0.005312j
[2025-08-27 00:23:59] [Iter 137/450] R0[136/150], Temp: 0.0213, Energy: -43.580358-0.002572j
[2025-08-27 00:24:11] [Iter 138/450] R0[137/150], Temp: 0.0184, Energy: -43.566963-0.004916j
[2025-08-27 00:24:23] [Iter 139/450] R0[138/150], Temp: 0.0157, Energy: -43.585136+0.000951j
[2025-08-27 00:24:36] [Iter 140/450] R0[139/150], Temp: 0.0132, Energy: -43.578477+0.007564j
[2025-08-27 00:24:48] [Iter 141/450] R0[140/150], Temp: 0.0109, Energy: -43.561361-0.004414j
[2025-08-27 00:25:01] [Iter 142/450] R0[141/150], Temp: 0.0089, Energy: -43.566805-0.000504j
[2025-08-27 00:25:13] [Iter 143/450] R0[142/150], Temp: 0.0070, Energy: -43.569158+0.004374j
[2025-08-27 00:25:25] [Iter 144/450] R0[143/150], Temp: 0.0054, Energy: -43.573700-0.000953j
[2025-08-27 00:25:38] [Iter 145/450] R0[144/150], Temp: 0.0039, Energy: -43.566791-0.003802j
[2025-08-27 00:25:50] [Iter 146/450] R0[145/150], Temp: 0.0027, Energy: -43.572664+0.000447j
[2025-08-27 00:26:02] [Iter 147/450] R0[146/150], Temp: 0.0018, Energy: -43.546642-0.001742j
[2025-08-27 00:26:15] [Iter 148/450] R0[147/150], Temp: 0.0010, Energy: -43.585201-0.000229j
[2025-08-27 00:26:27] [Iter 149/450] R0[148/150], Temp: 0.0004, Energy: -43.558796-0.002069j
[2025-08-27 00:26:40] [Iter 150/450] R0[149/150], Temp: 0.0001, Energy: -43.573172-0.001934j
[2025-08-27 00:26:40] ✓ Checkpoint saved: checkpoint_iter_000150.pkl
[2025-08-27 00:26:40] RESTART #1 | Period: 300
[2025-08-27 00:26:52] [Iter 151/450] R1[0/300], Temp: 1.0000, Energy: -43.570867-0.002830j
[2025-08-27 00:27:04] [Iter 152/450] R1[1/300], Temp: 1.0000, Energy: -43.583507-0.004307j
[2025-08-27 00:27:17] [Iter 153/450] R1[2/300], Temp: 0.9999, Energy: -43.565093-0.004310j
[2025-08-27 00:27:29] [Iter 154/450] R1[3/300], Temp: 0.9998, Energy: -43.560523-0.000566j
[2025-08-27 00:27:42] [Iter 155/450] R1[4/300], Temp: 0.9996, Energy: -43.577637+0.001116j
[2025-08-27 00:27:54] [Iter 156/450] R1[5/300], Temp: 0.9993, Energy: -43.569271+0.000809j
[2025-08-27 00:28:06] [Iter 157/450] R1[6/300], Temp: 0.9990, Energy: -43.569686+0.005424j
[2025-08-27 00:28:19] [Iter 158/450] R1[7/300], Temp: 0.9987, Energy: -43.556464-0.004806j
[2025-08-27 00:28:31] [Iter 159/450] R1[8/300], Temp: 0.9982, Energy: -43.573950+0.001871j
[2025-08-27 00:28:44] [Iter 160/450] R1[9/300], Temp: 0.9978, Energy: -43.570581-0.001227j
[2025-08-27 00:28:56] [Iter 161/450] R1[10/300], Temp: 0.9973, Energy: -43.588389-0.005164j
[2025-08-27 00:29:08] [Iter 162/450] R1[11/300], Temp: 0.9967, Energy: -43.584363-0.005634j
[2025-08-27 00:29:21] [Iter 163/450] R1[12/300], Temp: 0.9961, Energy: -43.570645-0.002360j
[2025-08-27 00:29:33] [Iter 164/450] R1[13/300], Temp: 0.9954, Energy: -43.573168+0.003125j
[2025-08-27 00:29:46] [Iter 165/450] R1[14/300], Temp: 0.9946, Energy: -43.564864+0.000617j
[2025-08-27 00:29:58] [Iter 166/450] R1[15/300], Temp: 0.9938, Energy: -43.567646-0.001251j
[2025-08-27 00:30:10] [Iter 167/450] R1[16/300], Temp: 0.9930, Energy: -43.577525-0.001418j
[2025-08-27 00:30:23] [Iter 168/450] R1[17/300], Temp: 0.9921, Energy: -43.567810-0.003175j
[2025-08-27 00:30:35] [Iter 169/450] R1[18/300], Temp: 0.9911, Energy: -43.581366+0.001091j
[2025-08-27 00:30:48] [Iter 170/450] R1[19/300], Temp: 0.9901, Energy: -43.570656+0.000647j
[2025-08-27 00:31:02] [Iter 171/450] R1[20/300], Temp: 0.9891, Energy: -43.588226-0.001299j
[2025-08-27 00:31:14] [Iter 172/450] R1[21/300], Temp: 0.9880, Energy: -43.574219+0.004360j
[2025-08-27 00:31:26] [Iter 173/450] R1[22/300], Temp: 0.9868, Energy: -43.562215+0.004711j
[2025-08-27 00:31:39] [Iter 174/450] R1[23/300], Temp: 0.9856, Energy: -43.571517+0.008325j
[2025-08-27 00:31:51] [Iter 175/450] R1[24/300], Temp: 0.9843, Energy: -43.566461-0.001612j
[2025-08-27 00:32:03] [Iter 176/450] R1[25/300], Temp: 0.9830, Energy: -43.577102+0.000068j
[2025-08-27 00:32:16] [Iter 177/450] R1[26/300], Temp: 0.9816, Energy: -43.572562+0.002818j
[2025-08-27 00:32:28] [Iter 178/450] R1[27/300], Temp: 0.9801, Energy: -43.589269+0.000967j
[2025-08-27 00:32:41] [Iter 179/450] R1[28/300], Temp: 0.9787, Energy: -43.583102-0.000850j
[2025-08-27 00:32:53] [Iter 180/450] R1[29/300], Temp: 0.9771, Energy: -43.592283+0.008406j
[2025-08-27 00:33:05] [Iter 181/450] R1[30/300], Temp: 0.9755, Energy: -43.587007-0.000648j
[2025-08-27 00:33:18] [Iter 182/450] R1[31/300], Temp: 0.9739, Energy: -43.570561+0.002128j
[2025-08-27 00:33:30] [Iter 183/450] R1[32/300], Temp: 0.9722, Energy: -43.562952-0.002693j
[2025-08-27 00:33:43] [Iter 184/450] R1[33/300], Temp: 0.9704, Energy: -43.598029+0.005926j
[2025-08-27 00:33:55] [Iter 185/450] R1[34/300], Temp: 0.9686, Energy: -43.574537-0.001227j
[2025-08-27 00:34:07] [Iter 186/450] R1[35/300], Temp: 0.9668, Energy: -43.576664+0.000812j
[2025-08-27 00:34:20] [Iter 187/450] R1[36/300], Temp: 0.9649, Energy: -43.568567-0.004993j
[2025-08-27 00:34:32] [Iter 188/450] R1[37/300], Temp: 0.9629, Energy: -43.583764+0.003715j
[2025-08-27 00:34:44] [Iter 189/450] R1[38/300], Temp: 0.9609, Energy: -43.581667-0.000051j
[2025-08-27 00:34:57] [Iter 190/450] R1[39/300], Temp: 0.9589, Energy: -43.570037-0.000055j
[2025-08-27 00:35:09] [Iter 191/450] R1[40/300], Temp: 0.9568, Energy: -43.583438-0.006094j
[2025-08-27 00:35:22] [Iter 192/450] R1[41/300], Temp: 0.9546, Energy: -43.568660-0.003175j
[2025-08-27 00:35:34] [Iter 193/450] R1[42/300], Temp: 0.9524, Energy: -43.585634-0.001788j
[2025-08-27 00:35:46] [Iter 194/450] R1[43/300], Temp: 0.9502, Energy: -43.569602+0.001315j
[2025-08-27 00:35:59] [Iter 195/450] R1[44/300], Temp: 0.9479, Energy: -43.560083+0.001822j
[2025-08-27 00:36:11] [Iter 196/450] R1[45/300], Temp: 0.9455, Energy: -43.570231-0.001916j
[2025-08-27 00:36:24] [Iter 197/450] R1[46/300], Temp: 0.9431, Energy: -43.569407+0.003494j
[2025-08-27 00:36:36] [Iter 198/450] R1[47/300], Temp: 0.9407, Energy: -43.571949+0.000143j
[2025-08-27 00:36:48] [Iter 199/450] R1[48/300], Temp: 0.9382, Energy: -43.559571+0.000652j
[2025-08-27 00:37:01] [Iter 200/450] R1[49/300], Temp: 0.9356, Energy: -43.591825+0.003799j
[2025-08-27 00:37:01] ✓ Checkpoint saved: checkpoint_iter_000200.pkl
[2025-08-27 00:37:13] [Iter 201/450] R1[50/300], Temp: 0.9330, Energy: -43.585995-0.007247j
[2025-08-27 00:37:25] [Iter 202/450] R1[51/300], Temp: 0.9304, Energy: -43.584280+0.006801j
[2025-08-27 00:37:38] [Iter 203/450] R1[52/300], Temp: 0.9277, Energy: -43.573232-0.001518j
[2025-08-27 00:37:50] [Iter 204/450] R1[53/300], Temp: 0.9249, Energy: -43.569901+0.000984j
[2025-08-27 00:38:03] [Iter 205/450] R1[54/300], Temp: 0.9222, Energy: -43.581465-0.001483j
[2025-08-27 00:38:15] [Iter 206/450] R1[55/300], Temp: 0.9193, Energy: -43.573963+0.002266j
[2025-08-27 00:38:27] [Iter 207/450] R1[56/300], Temp: 0.9165, Energy: -43.570813+0.000736j
[2025-08-27 00:38:40] [Iter 208/450] R1[57/300], Temp: 0.9135, Energy: -43.580738-0.001668j
[2025-08-27 00:38:52] [Iter 209/450] R1[58/300], Temp: 0.9106, Energy: -43.567120+0.004112j
[2025-08-27 00:39:05] [Iter 210/450] R1[59/300], Temp: 0.9076, Energy: -43.563986+0.003467j
[2025-08-27 00:39:17] [Iter 211/450] R1[60/300], Temp: 0.9045, Energy: -43.570921+0.003450j
[2025-08-27 00:39:29] [Iter 212/450] R1[61/300], Temp: 0.9014, Energy: -43.574443+0.003395j
[2025-08-27 00:39:42] [Iter 213/450] R1[62/300], Temp: 0.8983, Energy: -43.571024+0.000440j
[2025-08-27 00:39:54] [Iter 214/450] R1[63/300], Temp: 0.8951, Energy: -43.562623-0.000454j
[2025-08-27 00:40:07] [Iter 215/450] R1[64/300], Temp: 0.8918, Energy: -43.582485-0.000355j
[2025-08-27 00:40:19] [Iter 216/450] R1[65/300], Temp: 0.8886, Energy: -43.575456-0.002371j
[2025-08-27 00:40:31] [Iter 217/450] R1[66/300], Temp: 0.8853, Energy: -43.582679-0.004317j
[2025-08-27 00:40:44] [Iter 218/450] R1[67/300], Temp: 0.8819, Energy: -43.588061+0.003285j
[2025-08-27 00:40:56] [Iter 219/450] R1[68/300], Temp: 0.8785, Energy: -43.573491+0.002718j
[2025-08-27 00:41:09] [Iter 220/450] R1[69/300], Temp: 0.8751, Energy: -43.564211+0.000037j
[2025-08-27 00:41:21] [Iter 221/450] R1[70/300], Temp: 0.8716, Energy: -43.577908+0.001545j
[2025-08-27 00:41:33] [Iter 222/450] R1[71/300], Temp: 0.8680, Energy: -43.575992+0.001394j
[2025-08-27 00:41:46] [Iter 223/450] R1[72/300], Temp: 0.8645, Energy: -43.576116+0.002637j
[2025-08-27 00:41:58] [Iter 224/450] R1[73/300], Temp: 0.8609, Energy: -43.572752-0.001077j
[2025-08-27 00:42:10] [Iter 225/450] R1[74/300], Temp: 0.8572, Energy: -43.579833+0.002230j
[2025-08-27 00:42:23] [Iter 226/450] R1[75/300], Temp: 0.8536, Energy: -43.591570+0.000035j
[2025-08-27 00:42:35] [Iter 227/450] R1[76/300], Temp: 0.8498, Energy: -43.556688-0.004746j
[2025-08-27 00:42:48] [Iter 228/450] R1[77/300], Temp: 0.8461, Energy: -43.577034-0.003649j
[2025-08-27 00:43:00] [Iter 229/450] R1[78/300], Temp: 0.8423, Energy: -43.577525+0.004187j
[2025-08-27 00:43:12] [Iter 230/450] R1[79/300], Temp: 0.8384, Energy: -43.567217+0.001954j
[2025-08-27 00:43:25] [Iter 231/450] R1[80/300], Temp: 0.8346, Energy: -43.583372+0.001862j
[2025-08-27 00:43:37] [Iter 232/450] R1[81/300], Temp: 0.8307, Energy: -43.577811-0.000363j
[2025-08-27 00:43:50] [Iter 233/450] R1[82/300], Temp: 0.8267, Energy: -43.573235-0.003728j
[2025-08-27 00:44:02] [Iter 234/450] R1[83/300], Temp: 0.8227, Energy: -43.574139+0.002208j
[2025-08-27 00:44:14] [Iter 235/450] R1[84/300], Temp: 0.8187, Energy: -43.578986+0.002273j
[2025-08-27 00:44:27] [Iter 236/450] R1[85/300], Temp: 0.8147, Energy: -43.563898+0.002760j
[2025-08-27 00:44:39] [Iter 237/450] R1[86/300], Temp: 0.8106, Energy: -43.560647-0.001364j
[2025-08-27 00:44:52] [Iter 238/450] R1[87/300], Temp: 0.8065, Energy: -43.576381+0.000727j
[2025-08-27 00:45:04] [Iter 239/450] R1[88/300], Temp: 0.8023, Energy: -43.572443+0.002231j
[2025-08-27 00:45:16] [Iter 240/450] R1[89/300], Temp: 0.7981, Energy: -43.588443+0.003507j
[2025-08-27 00:45:29] [Iter 241/450] R1[90/300], Temp: 0.7939, Energy: -43.585143+0.011851j
[2025-08-27 00:45:41] [Iter 242/450] R1[91/300], Temp: 0.7896, Energy: -43.579333+0.001041j
[2025-08-27 00:45:54] [Iter 243/450] R1[92/300], Temp: 0.7854, Energy: -43.577383-0.003463j
[2025-08-27 00:46:06] [Iter 244/450] R1[93/300], Temp: 0.7810, Energy: -43.586720+0.001771j
[2025-08-27 00:46:18] [Iter 245/450] R1[94/300], Temp: 0.7767, Energy: -43.553230-0.000419j
[2025-08-27 00:46:31] [Iter 246/450] R1[95/300], Temp: 0.7723, Energy: -43.563960-0.003248j
[2025-08-27 00:46:43] [Iter 247/450] R1[96/300], Temp: 0.7679, Energy: -43.561293+0.001112j
[2025-08-27 00:46:56] [Iter 248/450] R1[97/300], Temp: 0.7635, Energy: -43.580782-0.002809j
[2025-08-27 00:47:08] [Iter 249/450] R1[98/300], Temp: 0.7590, Energy: -43.567564-0.004643j
[2025-08-27 00:47:20] [Iter 250/450] R1[99/300], Temp: 0.7545, Energy: -43.568779-0.001593j
[2025-08-27 00:47:20] ✓ Checkpoint saved: checkpoint_iter_000250.pkl
[2025-08-27 00:47:33] [Iter 251/450] R1[100/300], Temp: 0.7500, Energy: -43.576511-0.001649j
[2025-08-27 00:47:45] [Iter 252/450] R1[101/300], Temp: 0.7455, Energy: -43.591560+0.001365j
[2025-08-27 00:47:58] [Iter 253/450] R1[102/300], Temp: 0.7409, Energy: -43.577379-0.006547j
[2025-08-27 00:48:10] [Iter 254/450] R1[103/300], Temp: 0.7363, Energy: -43.561158+0.002202j
[2025-08-27 00:48:22] [Iter 255/450] R1[104/300], Temp: 0.7316, Energy: -43.560577+0.000284j
[2025-08-27 00:48:35] [Iter 256/450] R1[105/300], Temp: 0.7270, Energy: -43.585660+0.001858j
[2025-08-27 00:48:47] [Iter 257/450] R1[106/300], Temp: 0.7223, Energy: -43.574674-0.000893j
[2025-08-27 00:49:00] [Iter 258/450] R1[107/300], Temp: 0.7176, Energy: -43.566937+0.002578j
[2025-08-27 00:49:12] [Iter 259/450] R1[108/300], Temp: 0.7129, Energy: -43.576306-0.004409j
[2025-08-27 00:49:24] [Iter 260/450] R1[109/300], Temp: 0.7081, Energy: -43.587177-0.002134j
[2025-08-27 00:49:37] [Iter 261/450] R1[110/300], Temp: 0.7034, Energy: -43.569471-0.001709j
[2025-08-27 00:49:49] [Iter 262/450] R1[111/300], Temp: 0.6986, Energy: -43.582005+0.002636j
[2025-08-27 00:50:01] [Iter 263/450] R1[112/300], Temp: 0.6938, Energy: -43.575578-0.001929j
[2025-08-27 00:50:14] [Iter 264/450] R1[113/300], Temp: 0.6889, Energy: -43.584679-0.000282j
[2025-08-27 00:50:26] [Iter 265/450] R1[114/300], Temp: 0.6841, Energy: -43.585485+0.003213j
[2025-08-27 00:50:39] [Iter 266/450] R1[115/300], Temp: 0.6792, Energy: -43.580760+0.004625j
[2025-08-27 00:50:51] [Iter 267/450] R1[116/300], Temp: 0.6743, Energy: -43.566533-0.001557j
[2025-08-27 00:51:03] [Iter 268/450] R1[117/300], Temp: 0.6694, Energy: -43.575817-0.002075j
[2025-08-27 00:51:16] [Iter 269/450] R1[118/300], Temp: 0.6644, Energy: -43.569469+0.001599j
[2025-08-27 00:51:28] [Iter 270/450] R1[119/300], Temp: 0.6595, Energy: -43.571649+0.001103j
[2025-08-27 00:51:41] [Iter 271/450] R1[120/300], Temp: 0.6545, Energy: -43.585143-0.000712j
[2025-08-27 00:51:53] [Iter 272/450] R1[121/300], Temp: 0.6495, Energy: -43.574381+0.005470j
[2025-08-27 00:52:05] [Iter 273/450] R1[122/300], Temp: 0.6445, Energy: -43.585461+0.003838j
[2025-08-27 00:52:18] [Iter 274/450] R1[123/300], Temp: 0.6395, Energy: -43.576400+0.007453j
[2025-08-27 00:52:30] [Iter 275/450] R1[124/300], Temp: 0.6345, Energy: -43.568877+0.003308j
[2025-08-27 00:52:43] [Iter 276/450] R1[125/300], Temp: 0.6294, Energy: -43.578591+0.005107j
[2025-08-27 00:52:55] [Iter 277/450] R1[126/300], Temp: 0.6243, Energy: -43.578667+0.006115j
[2025-08-27 00:53:07] [Iter 278/450] R1[127/300], Temp: 0.6193, Energy: -43.567083-0.000325j
[2025-08-27 00:53:20] [Iter 279/450] R1[128/300], Temp: 0.6142, Energy: -43.593381+0.001101j
[2025-08-27 00:53:32] [Iter 280/450] R1[129/300], Temp: 0.6091, Energy: -43.575132+0.001295j
[2025-08-27 00:53:44] [Iter 281/450] R1[130/300], Temp: 0.6040, Energy: -43.572372+0.003755j
[2025-08-27 00:53:57] [Iter 282/450] R1[131/300], Temp: 0.5988, Energy: -43.590384-0.001787j
[2025-08-27 00:54:09] [Iter 283/450] R1[132/300], Temp: 0.5937, Energy: -43.554111+0.000284j
[2025-08-27 00:54:22] [Iter 284/450] R1[133/300], Temp: 0.5885, Energy: -43.584115+0.004069j
[2025-08-27 00:54:34] [Iter 285/450] R1[134/300], Temp: 0.5834, Energy: -43.562354+0.000143j
[2025-08-27 00:54:46] [Iter 286/450] R1[135/300], Temp: 0.5782, Energy: -43.568619-0.004444j
[2025-08-27 00:54:59] [Iter 287/450] R1[136/300], Temp: 0.5730, Energy: -43.570572+0.003782j
[2025-08-27 00:55:11] [Iter 288/450] R1[137/300], Temp: 0.5679, Energy: -43.573534-0.001685j
[2025-08-27 00:55:24] [Iter 289/450] R1[138/300], Temp: 0.5627, Energy: -43.589067-0.004151j
[2025-08-27 00:55:36] [Iter 290/450] R1[139/300], Temp: 0.5575, Energy: -43.583398-0.003078j
[2025-08-27 00:55:48] [Iter 291/450] R1[140/300], Temp: 0.5523, Energy: -43.576384-0.002858j
[2025-08-27 00:56:01] [Iter 292/450] R1[141/300], Temp: 0.5471, Energy: -43.568631-0.003132j
[2025-08-27 00:56:13] [Iter 293/450] R1[142/300], Temp: 0.5418, Energy: -43.574391+0.000214j
[2025-08-27 00:56:26] [Iter 294/450] R1[143/300], Temp: 0.5366, Energy: -43.597716+0.002971j
[2025-08-27 00:56:38] [Iter 295/450] R1[144/300], Temp: 0.5314, Energy: -43.585064-0.001987j
[2025-08-27 00:56:50] [Iter 296/450] R1[145/300], Temp: 0.5262, Energy: -43.586440-0.000292j
[2025-08-27 00:57:03] [Iter 297/450] R1[146/300], Temp: 0.5209, Energy: -43.568672-0.003256j
[2025-08-27 00:57:15] [Iter 298/450] R1[147/300], Temp: 0.5157, Energy: -43.592434+0.000285j
[2025-08-27 00:57:27] [Iter 299/450] R1[148/300], Temp: 0.5105, Energy: -43.570097-0.001976j
[2025-08-27 00:57:40] [Iter 300/450] R1[149/300], Temp: 0.5052, Energy: -43.573761+0.001448j
[2025-08-27 00:57:40] ✓ Checkpoint saved: checkpoint_iter_000300.pkl
[2025-08-27 00:57:52] [Iter 301/450] R1[150/300], Temp: 0.5000, Energy: -43.559849-0.007051j
[2025-08-27 00:58:05] [Iter 302/450] R1[151/300], Temp: 0.4948, Energy: -43.587567+0.002771j
[2025-08-27 00:58:17] [Iter 303/450] R1[152/300], Temp: 0.4895, Energy: -43.576100+0.008129j
[2025-08-27 00:58:29] [Iter 304/450] R1[153/300], Temp: 0.4843, Energy: -43.567541-0.004396j
[2025-08-27 00:58:42] [Iter 305/450] R1[154/300], Temp: 0.4791, Energy: -43.574497-0.000212j
[2025-08-27 00:58:54] [Iter 306/450] R1[155/300], Temp: 0.4738, Energy: -43.582767+0.003231j
[2025-08-27 00:59:07] [Iter 307/450] R1[156/300], Temp: 0.4686, Energy: -43.590230-0.001614j
[2025-08-27 00:59:19] [Iter 308/450] R1[157/300], Temp: 0.4634, Energy: -43.574248-0.001614j
[2025-08-27 00:59:31] [Iter 309/450] R1[158/300], Temp: 0.4582, Energy: -43.585166+0.005382j
[2025-08-27 00:59:44] [Iter 310/450] R1[159/300], Temp: 0.4529, Energy: -43.571348+0.002554j
[2025-08-27 00:59:56] [Iter 311/450] R1[160/300], Temp: 0.4477, Energy: -43.576513-0.000583j
[2025-08-27 01:00:09] [Iter 312/450] R1[161/300], Temp: 0.4425, Energy: -43.565837-0.000510j
[2025-08-27 01:00:21] [Iter 313/450] R1[162/300], Temp: 0.4373, Energy: -43.572418+0.002502j
[2025-08-27 01:00:33] [Iter 314/450] R1[163/300], Temp: 0.4321, Energy: -43.580718+0.000311j
[2025-08-27 01:00:46] [Iter 315/450] R1[164/300], Temp: 0.4270, Energy: -43.554589-0.002466j
[2025-08-27 01:00:58] [Iter 316/450] R1[165/300], Temp: 0.4218, Energy: -43.565373+0.003824j
[2025-08-27 01:01:11] [Iter 317/450] R1[166/300], Temp: 0.4166, Energy: -43.551459-0.001114j
[2025-08-27 01:01:23] [Iter 318/450] R1[167/300], Temp: 0.4115, Energy: -43.563537+0.001365j
[2025-08-27 01:01:35] [Iter 319/450] R1[168/300], Temp: 0.4063, Energy: -43.553266-0.002112j
[2025-08-27 01:01:48] [Iter 320/450] R1[169/300], Temp: 0.4012, Energy: -43.570271+0.002127j
[2025-08-27 01:02:00] [Iter 321/450] R1[170/300], Temp: 0.3960, Energy: -43.574882+0.003855j
[2025-08-27 01:02:13] [Iter 322/450] R1[171/300], Temp: 0.3909, Energy: -43.576487-0.001199j
[2025-08-27 01:02:25] [Iter 323/450] R1[172/300], Temp: 0.3858, Energy: -43.566683-0.001249j
[2025-08-27 01:02:37] [Iter 324/450] R1[173/300], Temp: 0.3807, Energy: -43.576991+0.001500j
[2025-08-27 01:02:50] [Iter 325/450] R1[174/300], Temp: 0.3757, Energy: -43.560326+0.001292j
[2025-08-27 01:03:02] [Iter 326/450] R1[175/300], Temp: 0.3706, Energy: -43.576942+0.004155j
[2025-08-27 01:03:14] [Iter 327/450] R1[176/300], Temp: 0.3655, Energy: -43.592313-0.001396j
[2025-08-27 01:03:27] [Iter 328/450] R1[177/300], Temp: 0.3605, Energy: -43.572870+0.002019j
[2025-08-27 01:03:39] [Iter 329/450] R1[178/300], Temp: 0.3555, Energy: -43.575140-0.000079j
[2025-08-27 01:03:52] [Iter 330/450] R1[179/300], Temp: 0.3505, Energy: -43.562915-0.002350j
[2025-08-27 01:04:04] [Iter 331/450] R1[180/300], Temp: 0.3455, Energy: -43.565944+0.003972j
[2025-08-27 01:04:16] [Iter 332/450] R1[181/300], Temp: 0.3405, Energy: -43.576383+0.002053j
[2025-08-27 01:04:29] [Iter 333/450] R1[182/300], Temp: 0.3356, Energy: -43.573948+0.001965j
[2025-08-27 01:04:41] [Iter 334/450] R1[183/300], Temp: 0.3306, Energy: -43.577787-0.004285j
[2025-08-27 01:04:54] [Iter 335/450] R1[184/300], Temp: 0.3257, Energy: -43.573275+0.000304j
[2025-08-27 01:05:06] [Iter 336/450] R1[185/300], Temp: 0.3208, Energy: -43.585932+0.000573j
[2025-08-27 01:05:18] [Iter 337/450] R1[186/300], Temp: 0.3159, Energy: -43.588272-0.005186j
[2025-08-27 01:05:31] [Iter 338/450] R1[187/300], Temp: 0.3111, Energy: -43.569608-0.000659j
[2025-08-27 01:05:43] [Iter 339/450] R1[188/300], Temp: 0.3062, Energy: -43.581898+0.000646j
[2025-08-27 01:05:55] [Iter 340/450] R1[189/300], Temp: 0.3014, Energy: -43.585711+0.005086j
[2025-08-27 01:06:08] [Iter 341/450] R1[190/300], Temp: 0.2966, Energy: -43.579833+0.003093j
[2025-08-27 01:06:20] [Iter 342/450] R1[191/300], Temp: 0.2919, Energy: -43.581398-0.000913j
[2025-08-27 01:06:33] [Iter 343/450] R1[192/300], Temp: 0.2871, Energy: -43.578332-0.000555j
[2025-08-27 01:06:45] [Iter 344/450] R1[193/300], Temp: 0.2824, Energy: -43.576901-0.000554j
[2025-08-27 01:06:57] [Iter 345/450] R1[194/300], Temp: 0.2777, Energy: -43.570073+0.001958j
[2025-08-27 01:07:10] [Iter 346/450] R1[195/300], Temp: 0.2730, Energy: -43.588672-0.000529j
[2025-08-27 01:07:22] [Iter 347/450] R1[196/300], Temp: 0.2684, Energy: -43.576375-0.001751j
[2025-08-27 01:07:35] [Iter 348/450] R1[197/300], Temp: 0.2637, Energy: -43.584297+0.000976j
[2025-08-27 01:07:47] [Iter 349/450] R1[198/300], Temp: 0.2591, Energy: -43.588906+0.004263j
[2025-08-27 01:07:59] [Iter 350/450] R1[199/300], Temp: 0.2545, Energy: -43.578485-0.003516j
[2025-08-27 01:07:59] ✓ Checkpoint saved: checkpoint_iter_000350.pkl
[2025-08-27 01:08:12] [Iter 351/450] R1[200/300], Temp: 0.2500, Energy: -43.575122-0.002259j
[2025-08-27 01:08:24] [Iter 352/450] R1[201/300], Temp: 0.2455, Energy: -43.583357+0.004850j
[2025-08-27 01:08:36] [Iter 353/450] R1[202/300], Temp: 0.2410, Energy: -43.573778-0.000061j
[2025-08-27 01:08:49] [Iter 354/450] R1[203/300], Temp: 0.2365, Energy: -43.573421-0.001875j
[2025-08-27 01:09:01] [Iter 355/450] R1[204/300], Temp: 0.2321, Energy: -43.587245-0.000744j
[2025-08-27 01:09:14] [Iter 356/450] R1[205/300], Temp: 0.2277, Energy: -43.577163-0.000520j
[2025-08-27 01:09:26] [Iter 357/450] R1[206/300], Temp: 0.2233, Energy: -43.584100-0.004529j
[2025-08-27 01:09:38] [Iter 358/450] R1[207/300], Temp: 0.2190, Energy: -43.580073+0.004882j
[2025-08-27 01:09:51] [Iter 359/450] R1[208/300], Temp: 0.2146, Energy: -43.587701-0.001954j
[2025-08-27 01:10:03] [Iter 360/450] R1[209/300], Temp: 0.2104, Energy: -43.576536+0.002947j
[2025-08-27 01:10:16] [Iter 361/450] R1[210/300], Temp: 0.2061, Energy: -43.580222+0.000453j
[2025-08-27 01:10:28] [Iter 362/450] R1[211/300], Temp: 0.2019, Energy: -43.585921+0.000639j
[2025-08-27 01:10:40] [Iter 363/450] R1[212/300], Temp: 0.1977, Energy: -43.580409+0.003854j
[2025-08-27 01:10:53] [Iter 364/450] R1[213/300], Temp: 0.1935, Energy: -43.571939+0.002081j
[2025-08-27 01:11:05] [Iter 365/450] R1[214/300], Temp: 0.1894, Energy: -43.583776+0.001901j
[2025-08-27 01:11:18] [Iter 366/450] R1[215/300], Temp: 0.1853, Energy: -43.583150-0.000309j
[2025-08-27 01:11:30] [Iter 367/450] R1[216/300], Temp: 0.1813, Energy: -43.594305-0.000552j
[2025-08-27 01:11:42] [Iter 368/450] R1[217/300], Temp: 0.1773, Energy: -43.575855+0.003646j
[2025-08-27 01:11:55] [Iter 369/450] R1[218/300], Temp: 0.1733, Energy: -43.562629-0.000858j
[2025-08-27 01:12:07] [Iter 370/450] R1[219/300], Temp: 0.1693, Energy: -43.575498+0.000870j
[2025-08-27 01:12:20] [Iter 371/450] R1[220/300], Temp: 0.1654, Energy: -43.559360+0.000797j
[2025-08-27 01:12:32] [Iter 372/450] R1[221/300], Temp: 0.1616, Energy: -43.585526-0.003952j
[2025-08-27 01:12:44] [Iter 373/450] R1[222/300], Temp: 0.1577, Energy: -43.571375-0.001425j
[2025-08-27 01:12:57] [Iter 374/450] R1[223/300], Temp: 0.1539, Energy: -43.574965-0.000273j
[2025-08-27 01:13:09] [Iter 375/450] R1[224/300], Temp: 0.1502, Energy: -43.577856+0.003069j
[2025-08-27 01:13:22] [Iter 376/450] R1[225/300], Temp: 0.1464, Energy: -43.563877-0.000558j
[2025-08-27 01:13:34] [Iter 377/450] R1[226/300], Temp: 0.1428, Energy: -43.566670+0.001964j
[2025-08-27 01:13:46] [Iter 378/450] R1[227/300], Temp: 0.1391, Energy: -43.585153+0.004344j
[2025-08-27 01:13:59] [Iter 379/450] R1[228/300], Temp: 0.1355, Energy: -43.572161+0.000572j
[2025-08-27 01:14:11] [Iter 380/450] R1[229/300], Temp: 0.1320, Energy: -43.579110-0.001337j
[2025-08-27 01:14:24] [Iter 381/450] R1[230/300], Temp: 0.1284, Energy: -43.564050-0.001714j
[2025-08-27 01:14:36] [Iter 382/450] R1[231/300], Temp: 0.1249, Energy: -43.582309+0.002445j
[2025-08-27 01:14:48] [Iter 383/450] R1[232/300], Temp: 0.1215, Energy: -43.569403+0.001558j
[2025-08-27 01:15:01] [Iter 384/450] R1[233/300], Temp: 0.1181, Energy: -43.576289-0.000435j
[2025-08-27 01:15:13] [Iter 385/450] R1[234/300], Temp: 0.1147, Energy: -43.584835-0.000785j
[2025-08-27 01:15:26] [Iter 386/450] R1[235/300], Temp: 0.1114, Energy: -43.583209+0.006823j
[2025-08-27 01:15:38] [Iter 387/450] R1[236/300], Temp: 0.1082, Energy: -43.582824-0.000738j
[2025-08-27 01:15:50] [Iter 388/450] R1[237/300], Temp: 0.1049, Energy: -43.580859+0.001709j
[2025-08-27 01:16:03] [Iter 389/450] R1[238/300], Temp: 0.1017, Energy: -43.580109+0.001303j
[2025-08-27 01:16:15] [Iter 390/450] R1[239/300], Temp: 0.0986, Energy: -43.576195-0.005205j
[2025-08-27 01:16:27] [Iter 391/450] R1[240/300], Temp: 0.0955, Energy: -43.586699+0.001784j
[2025-08-27 01:16:40] [Iter 392/450] R1[241/300], Temp: 0.0924, Energy: -43.571390+0.000061j
[2025-08-27 01:16:52] [Iter 393/450] R1[242/300], Temp: 0.0894, Energy: -43.572787+0.002049j
[2025-08-27 01:17:05] [Iter 394/450] R1[243/300], Temp: 0.0865, Energy: -43.571994+0.001585j
[2025-08-27 01:17:17] [Iter 395/450] R1[244/300], Temp: 0.0835, Energy: -43.560900-0.000130j
[2025-08-27 01:17:29] [Iter 396/450] R1[245/300], Temp: 0.0807, Energy: -43.570160-0.002209j
[2025-08-27 01:17:42] [Iter 397/450] R1[246/300], Temp: 0.0778, Energy: -43.583809+0.000878j
[2025-08-27 01:17:54] [Iter 398/450] R1[247/300], Temp: 0.0751, Energy: -43.574860-0.003972j
[2025-08-27 01:18:07] [Iter 399/450] R1[248/300], Temp: 0.0723, Energy: -43.578083+0.006576j
[2025-08-27 01:18:19] [Iter 400/450] R1[249/300], Temp: 0.0696, Energy: -43.571361-0.007232j
[2025-08-27 01:18:19] ✓ Checkpoint saved: checkpoint_iter_000400.pkl
[2025-08-27 01:18:31] [Iter 401/450] R1[250/300], Temp: 0.0670, Energy: -43.557408-0.000063j
[2025-08-27 01:18:44] [Iter 402/450] R1[251/300], Temp: 0.0644, Energy: -43.582020-0.000582j
[2025-08-27 01:18:56] [Iter 403/450] R1[252/300], Temp: 0.0618, Energy: -43.561071+0.000456j
[2025-08-27 01:19:08] [Iter 404/450] R1[253/300], Temp: 0.0593, Energy: -43.578649-0.001965j
[2025-08-27 01:19:21] [Iter 405/450] R1[254/300], Temp: 0.0569, Energy: -43.581147+0.002276j
[2025-08-27 01:19:33] [Iter 406/450] R1[255/300], Temp: 0.0545, Energy: -43.571490-0.000081j
[2025-08-27 01:19:45] [Iter 407/450] R1[256/300], Temp: 0.0521, Energy: -43.572091+0.007254j
[2025-08-27 01:19:58] [Iter 408/450] R1[257/300], Temp: 0.0498, Energy: -43.572459+0.002571j
[2025-08-27 01:20:10] [Iter 409/450] R1[258/300], Temp: 0.0476, Energy: -43.577183+0.003760j
[2025-08-27 01:20:23] [Iter 410/450] R1[259/300], Temp: 0.0454, Energy: -43.561110+0.000910j
[2025-08-27 01:20:35] [Iter 411/450] R1[260/300], Temp: 0.0432, Energy: -43.589334-0.000874j
[2025-08-27 01:20:47] [Iter 412/450] R1[261/300], Temp: 0.0411, Energy: -43.578871-0.003230j
[2025-08-27 01:21:00] [Iter 413/450] R1[262/300], Temp: 0.0391, Energy: -43.582630+0.006240j
[2025-08-27 01:21:12] [Iter 414/450] R1[263/300], Temp: 0.0371, Energy: -43.573295+0.000263j
[2025-08-27 01:21:25] [Iter 415/450] R1[264/300], Temp: 0.0351, Energy: -43.572584+0.001888j
[2025-08-27 01:21:37] [Iter 416/450] R1[265/300], Temp: 0.0332, Energy: -43.557478-0.000583j
[2025-08-27 01:21:49] [Iter 417/450] R1[266/300], Temp: 0.0314, Energy: -43.570218+0.001770j
[2025-08-27 01:22:02] [Iter 418/450] R1[267/300], Temp: 0.0296, Energy: -43.581731+0.000381j
[2025-08-27 01:22:14] [Iter 419/450] R1[268/300], Temp: 0.0278, Energy: -43.575011-0.003249j
[2025-08-27 01:22:26] [Iter 420/450] R1[269/300], Temp: 0.0261, Energy: -43.588003+0.002158j
[2025-08-27 01:22:39] [Iter 421/450] R1[270/300], Temp: 0.0245, Energy: -43.575444-0.002717j
[2025-08-27 01:22:51] [Iter 422/450] R1[271/300], Temp: 0.0229, Energy: -43.575153+0.000639j
[2025-08-27 01:23:04] [Iter 423/450] R1[272/300], Temp: 0.0213, Energy: -43.567353+0.001489j
[2025-08-27 01:23:16] [Iter 424/450] R1[273/300], Temp: 0.0199, Energy: -43.580414+0.001296j
[2025-08-27 01:23:28] [Iter 425/450] R1[274/300], Temp: 0.0184, Energy: -43.589651+0.000483j
[2025-08-27 01:23:41] [Iter 426/450] R1[275/300], Temp: 0.0170, Energy: -43.581046+0.001932j
[2025-08-27 01:23:53] [Iter 427/450] R1[276/300], Temp: 0.0157, Energy: -43.579017+0.004533j
[2025-08-27 01:24:06] [Iter 428/450] R1[277/300], Temp: 0.0144, Energy: -43.584821+0.000335j
[2025-08-27 01:24:18] [Iter 429/450] R1[278/300], Temp: 0.0132, Energy: -43.579768+0.002944j
[2025-08-27 01:24:30] [Iter 430/450] R1[279/300], Temp: 0.0120, Energy: -43.573971+0.000054j
[2025-08-27 01:24:43] [Iter 431/450] R1[280/300], Temp: 0.0109, Energy: -43.588369+0.003152j
[2025-08-27 01:24:55] [Iter 432/450] R1[281/300], Temp: 0.0099, Energy: -43.553773+0.000436j
[2025-08-27 01:25:08] [Iter 433/450] R1[282/300], Temp: 0.0089, Energy: -43.588456-0.000433j
[2025-08-27 01:25:20] [Iter 434/450] R1[283/300], Temp: 0.0079, Energy: -43.572381-0.003698j
[2025-08-27 01:25:32] [Iter 435/450] R1[284/300], Temp: 0.0070, Energy: -43.576425+0.001479j
[2025-08-27 01:25:45] [Iter 436/450] R1[285/300], Temp: 0.0062, Energy: -43.585902+0.000820j
[2025-08-27 01:25:57] [Iter 437/450] R1[286/300], Temp: 0.0054, Energy: -43.582704-0.011695j
[2025-08-27 01:26:10] [Iter 438/450] R1[287/300], Temp: 0.0046, Energy: -43.569869+0.002161j
[2025-08-27 01:26:22] [Iter 439/450] R1[288/300], Temp: 0.0039, Energy: -43.585788-0.001900j
[2025-08-27 01:26:34] [Iter 440/450] R1[289/300], Temp: 0.0033, Energy: -43.586596-0.003484j
[2025-08-27 01:26:47] [Iter 441/450] R1[290/300], Temp: 0.0027, Energy: -43.575909+0.001931j
[2025-08-27 01:26:59] [Iter 442/450] R1[291/300], Temp: 0.0022, Energy: -43.591232-0.011111j
[2025-08-27 01:27:11] [Iter 443/450] R1[292/300], Temp: 0.0018, Energy: -43.577349+0.007559j
[2025-08-27 01:27:24] [Iter 444/450] R1[293/300], Temp: 0.0013, Energy: -43.588775-0.001496j
[2025-08-27 01:27:36] [Iter 445/450] R1[294/300], Temp: 0.0010, Energy: -43.581073+0.005869j
[2025-08-27 01:27:49] [Iter 446/450] R1[295/300], Temp: 0.0007, Energy: -43.581108-0.000129j
[2025-08-27 01:28:01] [Iter 447/450] R1[296/300], Temp: 0.0004, Energy: -43.598247-0.000007j
[2025-08-27 01:28:13] [Iter 448/450] R1[297/300], Temp: 0.0002, Energy: -43.592075+0.000739j
[2025-08-27 01:28:26] [Iter 449/450] R1[298/300], Temp: 0.0001, Energy: -43.587724-0.004693j
[2025-08-27 01:28:38] [Iter 450/450] R1[299/300], Temp: 0.0000, Energy: -43.579441+0.002087j
[2025-08-27 01:28:38] ✓ Checkpoint saved: checkpoint_iter_000450.pkl
[2025-08-27 01:28:38] ✅ Training completed | Restarts: 1
[2025-08-27 01:28:38] ============================================================
[2025-08-27 01:28:38] Training completed | Runtime: 5621.9s
[2025-08-27 01:28:43] ✓ Final state saved: checkpoints/final_GCNN.pkl
[2025-08-27 01:28:43] ============================================================
