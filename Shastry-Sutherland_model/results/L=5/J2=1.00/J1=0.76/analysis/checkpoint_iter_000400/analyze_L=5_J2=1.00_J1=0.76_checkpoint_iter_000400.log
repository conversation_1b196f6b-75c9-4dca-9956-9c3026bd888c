[2025-08-28 18:23:07] 使用checkpoint文件: results/L=5/J2=1.00/J1=0.76/training/checkpoints/checkpoint_iter_000400.pkl
[2025-08-28 18:23:19] ✓ 从checkpoint加载参数: 400
[2025-08-28 18:23:19]   - 能量: -42.310241-0.001896j ± 0.010372
[2025-08-28 18:23:19] ================================================================================
[2025-08-28 18:23:19] 加载量子态: L=5, J2=1.00, J1=0.76, checkpoint=checkpoint_iter_000400
[2025-08-28 18:23:19] 设置样本数为: 1048576
[2025-08-28 18:23:19] 开始生成共享样本集...
[2025-08-28 18:26:16] 样本生成完成,耗时: 177.587 秒
[2025-08-28 18:26:17] ================================================================================
[2025-08-28 18:26:17] 开始计算自旋结构因子...
[2025-08-28 18:26:17] 初始化操作符缓存...
[2025-08-28 18:26:17] 预构建所有自旋相关操作符...
[2025-08-28 18:26:17] 开始计算自旋相关函数...
[2025-08-28 18:26:26] 自旋算符进度: 1/100, 当前算符: S_0 · S_0, 耗时: 9.186s
[2025-08-28 18:26:37] 自旋算符进度: 2/100, 当前算符: S_0 · S_1, 耗时: 11.057s
[2025-08-28 18:26:43] 自旋算符进度: 3/100, 当前算符: S_0 · S_2, 耗时: 6.192s
[2025-08-28 18:26:49] 自旋算符进度: 4/100, 当前算符: S_0 · S_3, 耗时: 6.193s
[2025-08-28 18:26:55] 自旋算符进度: 5/100, 当前算符: S_0 · S_4, 耗时: 6.197s
[2025-08-28 18:27:02] 自旋算符进度: 6/100, 当前算符: S_0 · S_5, 耗时: 6.194s
[2025-08-28 18:27:08] 自旋算符进度: 7/100, 当前算符: S_0 · S_6, 耗时: 6.195s
[2025-08-28 18:27:14] 自旋算符进度: 8/100, 当前算符: S_0 · S_7, 耗时: 6.218s
[2025-08-28 18:27:20] 自旋算符进度: 9/100, 当前算符: S_0 · S_8, 耗时: 6.217s
[2025-08-28 18:27:27] 自旋算符进度: 10/100, 当前算符: S_0 · S_9, 耗时: 6.195s
[2025-08-28 18:27:33] 自旋算符进度: 11/100, 当前算符: S_0 · S_10, 耗时: 6.195s
[2025-08-28 18:27:39] 自旋算符进度: 12/100, 当前算符: S_0 · S_11, 耗时: 6.197s
[2025-08-28 18:27:45] 自旋算符进度: 13/100, 当前算符: S_0 · S_12, 耗时: 6.196s
[2025-08-28 18:27:51] 自旋算符进度: 14/100, 当前算符: S_0 · S_13, 耗时: 6.193s
[2025-08-28 18:27:57] 自旋算符进度: 15/100, 当前算符: S_0 · S_14, 耗时: 6.193s
[2025-08-28 18:28:04] 自旋算符进度: 16/100, 当前算符: S_0 · S_15, 耗时: 6.195s
[2025-08-28 18:28:10] 自旋算符进度: 17/100, 当前算符: S_0 · S_16, 耗时: 6.218s
[2025-08-28 18:28:16] 自旋算符进度: 18/100, 当前算符: S_0 · S_17, 耗时: 6.193s
[2025-08-28 18:28:22] 自旋算符进度: 19/100, 当前算符: S_0 · S_18, 耗时: 6.195s
[2025-08-28 18:28:29] 自旋算符进度: 20/100, 当前算符: S_0 · S_19, 耗时: 6.219s
[2025-08-28 18:28:35] 自旋算符进度: 21/100, 当前算符: S_0 · S_20, 耗时: 6.195s
[2025-08-28 18:28:41] 自旋算符进度: 22/100, 当前算符: S_0 · S_21, 耗时: 6.195s
[2025-08-28 18:28:47] 自旋算符进度: 23/100, 当前算符: S_0 · S_22, 耗时: 6.216s
[2025-08-28 18:28:53] 自旋算符进度: 24/100, 当前算符: S_0 · S_23, 耗时: 6.218s
[2025-08-28 18:29:00] 自旋算符进度: 25/100, 当前算符: S_0 · S_24, 耗时: 6.194s
[2025-08-28 18:29:06] 自旋算符进度: 26/100, 当前算符: S_0 · S_25, 耗时: 6.218s
[2025-08-28 18:29:12] 自旋算符进度: 27/100, 当前算符: S_0 · S_26, 耗时: 6.195s
[2025-08-28 18:29:18] 自旋算符进度: 28/100, 当前算符: S_0 · S_27, 耗时: 6.218s
[2025-08-28 18:29:24] 自旋算符进度: 29/100, 当前算符: S_0 · S_28, 耗时: 6.194s
[2025-08-28 18:29:31] 自旋算符进度: 30/100, 当前算符: S_0 · S_29, 耗时: 6.219s
[2025-08-28 18:29:37] 自旋算符进度: 31/100, 当前算符: S_0 · S_30, 耗时: 6.194s
[2025-08-28 18:29:43] 自旋算符进度: 32/100, 当前算符: S_0 · S_31, 耗时: 6.193s
[2025-08-28 18:29:49] 自旋算符进度: 33/100, 当前算符: S_0 · S_32, 耗时: 6.192s
[2025-08-28 18:29:55] 自旋算符进度: 34/100, 当前算符: S_0 · S_33, 耗时: 6.217s
[2025-08-28 18:30:02] 自旋算符进度: 35/100, 当前算符: S_0 · S_34, 耗时: 6.194s
[2025-08-28 18:30:08] 自旋算符进度: 36/100, 当前算符: S_0 · S_35, 耗时: 6.191s
[2025-08-28 18:30:14] 自旋算符进度: 37/100, 当前算符: S_0 · S_36, 耗时: 6.192s
[2025-08-28 18:30:20] 自旋算符进度: 38/100, 当前算符: S_0 · S_37, 耗时: 6.192s
[2025-08-28 18:30:26] 自旋算符进度: 39/100, 当前算符: S_0 · S_38, 耗时: 6.193s
[2025-08-28 18:30:33] 自旋算符进度: 40/100, 当前算符: S_0 · S_39, 耗时: 6.193s
[2025-08-28 18:30:39] 自旋算符进度: 41/100, 当前算符: S_0 · S_40, 耗时: 6.194s
[2025-08-28 18:30:45] 自旋算符进度: 42/100, 当前算符: S_0 · S_41, 耗时: 6.194s
[2025-08-28 18:30:51] 自旋算符进度: 43/100, 当前算符: S_0 · S_42, 耗时: 6.191s
[2025-08-28 18:30:57] 自旋算符进度: 44/100, 当前算符: S_0 · S_43, 耗时: 6.216s
[2025-08-28 18:31:04] 自旋算符进度: 45/100, 当前算符: S_0 · S_44, 耗时: 6.194s
[2025-08-28 18:31:10] 自旋算符进度: 46/100, 当前算符: S_0 · S_45, 耗时: 6.193s
[2025-08-28 18:31:16] 自旋算符进度: 47/100, 当前算符: S_0 · S_46, 耗时: 6.192s
[2025-08-28 18:31:22] 自旋算符进度: 48/100, 当前算符: S_0 · S_47, 耗时: 6.191s
[2025-08-28 18:31:28] 自旋算符进度: 49/100, 当前算符: S_0 · S_48, 耗时: 6.194s
[2025-08-28 18:31:35] 自旋算符进度: 50/100, 当前算符: S_0 · S_49, 耗时: 6.196s
[2025-08-28 18:31:41] 自旋算符进度: 51/100, 当前算符: S_0 · S_50, 耗时: 6.192s
[2025-08-28 18:31:47] 自旋算符进度: 52/100, 当前算符: S_0 · S_51, 耗时: 6.194s
[2025-08-28 18:31:53] 自旋算符进度: 53/100, 当前算符: S_0 · S_52, 耗时: 6.192s
[2025-08-28 18:31:59] 自旋算符进度: 54/100, 当前算符: S_0 · S_53, 耗时: 6.193s
[2025-08-28 18:32:06] 自旋算符进度: 55/100, 当前算符: S_0 · S_54, 耗时: 6.193s
[2025-08-28 18:32:12] 自旋算符进度: 56/100, 当前算符: S_0 · S_55, 耗时: 6.191s
[2025-08-28 18:32:18] 自旋算符进度: 57/100, 当前算符: S_0 · S_56, 耗时: 6.194s
[2025-08-28 18:32:24] 自旋算符进度: 58/100, 当前算符: S_0 · S_57, 耗时: 6.193s
[2025-08-28 18:32:30] 自旋算符进度: 59/100, 当前算符: S_0 · S_58, 耗时: 6.217s
[2025-08-28 18:32:37] 自旋算符进度: 60/100, 当前算符: S_0 · S_59, 耗时: 6.192s
[2025-08-28 18:32:43] 自旋算符进度: 61/100, 当前算符: S_0 · S_60, 耗时: 6.197s
[2025-08-28 18:32:49] 自旋算符进度: 62/100, 当前算符: S_0 · S_61, 耗时: 6.193s
[2025-08-28 18:32:55] 自旋算符进度: 63/100, 当前算符: S_0 · S_62, 耗时: 6.217s
[2025-08-28 18:33:01] 自旋算符进度: 64/100, 当前算符: S_0 · S_63, 耗时: 6.192s
[2025-08-28 18:33:08] 自旋算符进度: 65/100, 当前算符: S_0 · S_64, 耗时: 6.195s
[2025-08-28 18:33:14] 自旋算符进度: 66/100, 当前算符: S_0 · S_65, 耗时: 6.190s
[2025-08-28 18:33:20] 自旋算符进度: 67/100, 当前算符: S_0 · S_66, 耗时: 6.196s
[2025-08-28 18:33:26] 自旋算符进度: 68/100, 当前算符: S_0 · S_67, 耗时: 6.191s
[2025-08-28 18:33:32] 自旋算符进度: 69/100, 当前算符: S_0 · S_68, 耗时: 6.193s
[2025-08-28 18:33:39] 自旋算符进度: 70/100, 当前算符: S_0 · S_69, 耗时: 6.219s
[2025-08-28 18:33:45] 自旋算符进度: 71/100, 当前算符: S_0 · S_70, 耗时: 6.195s
[2025-08-28 18:33:51] 自旋算符进度: 72/100, 当前算符: S_0 · S_71, 耗时: 6.219s
[2025-08-28 18:33:57] 自旋算符进度: 73/100, 当前算符: S_0 · S_72, 耗时: 6.219s
[2025-08-28 18:34:03] 自旋算符进度: 74/100, 当前算符: S_0 · S_73, 耗时: 6.193s
[2025-08-28 18:34:10] 自旋算符进度: 75/100, 当前算符: S_0 · S_74, 耗时: 6.218s
[2025-08-28 18:34:16] 自旋算符进度: 76/100, 当前算符: S_0 · S_75, 耗时: 6.193s
[2025-08-28 18:34:22] 自旋算符进度: 77/100, 当前算符: S_0 · S_76, 耗时: 6.195s
[2025-08-28 18:34:28] 自旋算符进度: 78/100, 当前算符: S_0 · S_77, 耗时: 6.192s
[2025-08-28 18:34:34] 自旋算符进度: 79/100, 当前算符: S_0 · S_78, 耗时: 6.218s
[2025-08-28 18:34:41] 自旋算符进度: 80/100, 当前算符: S_0 · S_79, 耗时: 6.193s
[2025-08-28 18:34:47] 自旋算符进度: 81/100, 当前算符: S_0 · S_80, 耗时: 6.193s
[2025-08-28 18:34:53] 自旋算符进度: 82/100, 当前算符: S_0 · S_81, 耗时: 6.193s
[2025-08-28 18:34:59] 自旋算符进度: 83/100, 当前算符: S_0 · S_82, 耗时: 6.218s
[2025-08-28 18:35:06] 自旋算符进度: 84/100, 当前算符: S_0 · S_83, 耗时: 6.195s
[2025-08-28 18:35:12] 自旋算符进度: 85/100, 当前算符: S_0 · S_84, 耗时: 6.192s
[2025-08-28 18:35:18] 自旋算符进度: 86/100, 当前算符: S_0 · S_85, 耗时: 6.218s
[2025-08-28 18:35:24] 自旋算符进度: 87/100, 当前算符: S_0 · S_86, 耗时: 6.194s
[2025-08-28 18:35:30] 自旋算符进度: 88/100, 当前算符: S_0 · S_87, 耗时: 6.192s
[2025-08-28 18:35:37] 自旋算符进度: 89/100, 当前算符: S_0 · S_88, 耗时: 6.193s
[2025-08-28 18:35:43] 自旋算符进度: 90/100, 当前算符: S_0 · S_89, 耗时: 6.218s
[2025-08-28 18:35:49] 自旋算符进度: 91/100, 当前算符: S_0 · S_90, 耗时: 6.196s
[2025-08-28 18:35:55] 自旋算符进度: 92/100, 当前算符: S_0 · S_91, 耗时: 6.196s
[2025-08-28 18:36:01] 自旋算符进度: 93/100, 当前算符: S_0 · S_92, 耗时: 6.217s
[2025-08-28 18:36:08] 自旋算符进度: 94/100, 当前算符: S_0 · S_93, 耗时: 6.194s
[2025-08-28 18:36:14] 自旋算符进度: 95/100, 当前算符: S_0 · S_94, 耗时: 6.194s
[2025-08-28 18:36:20] 自旋算符进度: 96/100, 当前算符: S_0 · S_95, 耗时: 6.195s
[2025-08-28 18:36:26] 自旋算符进度: 97/100, 当前算符: S_0 · S_96, 耗时: 6.194s
[2025-08-28 18:36:32] 自旋算符进度: 98/100, 当前算符: S_0 · S_97, 耗时: 6.193s
[2025-08-28 18:36:39] 自旋算符进度: 99/100, 当前算符: S_0 · S_98, 耗时: 6.218s
[2025-08-28 18:36:45] 自旋算符进度: 100/100, 当前算符: S_0 · S_99, 耗时: 6.193s
[2025-08-28 18:36:45] 自旋相关函数计算完成,总耗时 628.13 秒
[2025-08-28 18:36:45] 计算傅里叶变换...
[2025-08-28 18:36:46] 自旋结构因子计算完成
[2025-08-28 18:36:47] 自旋相关函数平均误差: 0.000612
