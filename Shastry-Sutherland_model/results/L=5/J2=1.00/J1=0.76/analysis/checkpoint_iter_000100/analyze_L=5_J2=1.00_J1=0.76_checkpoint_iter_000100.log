[2025-08-28 17:00:49] 使用checkpoint文件: results/L=5/J2=1.00/J1=0.76/training/checkpoints/checkpoint_iter_000100.pkl
[2025-08-28 17:01:00] ✓ 从checkpoint加载参数: 100
[2025-08-28 17:01:00]   - 能量: -42.315073-0.001300j ± 0.010431
[2025-08-28 17:01:00] ================================================================================
[2025-08-28 17:01:00] 加载量子态: L=5, J2=1.00, J1=0.76, checkpoint=checkpoint_iter_000100
[2025-08-28 17:01:00] 设置样本数为: 1048576
[2025-08-28 17:01:00] 开始生成共享样本集...
[2025-08-28 17:03:58] 样本生成完成,耗时: 177.598 秒
[2025-08-28 17:03:58] ================================================================================
[2025-08-28 17:03:58] 开始计算自旋结构因子...
[2025-08-28 17:03:58] 初始化操作符缓存...
[2025-08-28 17:03:58] 预构建所有自旋相关操作符...
[2025-08-28 17:03:58] 开始计算自旋相关函数...
[2025-08-28 17:04:07] 自旋算符进度: 1/100, 当前算符: S_0 · S_0, 耗时: 9.276s
[2025-08-28 17:04:18] 自旋算符进度: 2/100, 当前算符: S_0 · S_1, 耗时: 11.017s
[2025-08-28 17:04:25] 自旋算符进度: 3/100, 当前算符: S_0 · S_2, 耗时: 6.191s
[2025-08-28 17:04:31] 自旋算符进度: 4/100, 当前算符: S_0 · S_3, 耗时: 6.193s
[2025-08-28 17:04:37] 自旋算符进度: 5/100, 当前算符: S_0 · S_4, 耗时: 6.196s
[2025-08-28 17:04:43] 自旋算符进度: 6/100, 当前算符: S_0 · S_5, 耗时: 6.193s
[2025-08-28 17:04:49] 自旋算符进度: 7/100, 当前算符: S_0 · S_6, 耗时: 6.196s
[2025-08-28 17:04:56] 自旋算符进度: 8/100, 当前算符: S_0 · S_7, 耗时: 6.721s
[2025-08-28 17:05:02] 自旋算符进度: 9/100, 当前算符: S_0 · S_8, 耗时: 6.251s
[2025-08-28 17:05:09] 自旋算符进度: 10/100, 当前算符: S_0 · S_9, 耗时: 6.194s
[2025-08-28 17:05:15] 自旋算符进度: 11/100, 当前算符: S_0 · S_10, 耗时: 6.197s
[2025-08-28 17:05:21] 自旋算符进度: 12/100, 当前算符: S_0 · S_11, 耗时: 6.197s
[2025-08-28 17:05:27] 自旋算符进度: 13/100, 当前算符: S_0 · S_12, 耗时: 6.196s
[2025-08-28 17:05:33] 自旋算符进度: 14/100, 当前算符: S_0 · S_13, 耗时: 6.195s
[2025-08-28 17:05:40] 自旋算符进度: 15/100, 当前算符: S_0 · S_14, 耗时: 6.195s
[2025-08-28 17:05:46] 自旋算符进度: 16/100, 当前算符: S_0 · S_15, 耗时: 6.196s
[2025-08-28 17:05:52] 自旋算符进度: 17/100, 当前算符: S_0 · S_16, 耗时: 6.220s
[2025-08-28 17:05:58] 自旋算符进度: 18/100, 当前算符: S_0 · S_17, 耗时: 6.193s
[2025-08-28 17:06:04] 自旋算符进度: 19/100, 当前算符: S_0 · S_18, 耗时: 6.195s
[2025-08-28 17:06:11] 自旋算符进度: 20/100, 当前算符: S_0 · S_19, 耗时: 6.219s
[2025-08-28 17:06:17] 自旋算符进度: 21/100, 当前算符: S_0 · S_20, 耗时: 6.196s
[2025-08-28 17:06:23] 自旋算符进度: 22/100, 当前算符: S_0 · S_21, 耗时: 6.197s
[2025-08-28 17:06:29] 自旋算符进度: 23/100, 当前算符: S_0 · S_22, 耗时: 6.221s
[2025-08-28 17:06:35] 自旋算符进度: 24/100, 当前算符: S_0 · S_23, 耗时: 6.219s
[2025-08-28 17:06:42] 自旋算符进度: 25/100, 当前算符: S_0 · S_24, 耗时: 6.196s
[2025-08-28 17:06:48] 自旋算符进度: 26/100, 当前算符: S_0 · S_25, 耗时: 6.219s
[2025-08-28 17:06:54] 自旋算符进度: 27/100, 当前算符: S_0 · S_26, 耗时: 6.195s
[2025-08-28 17:07:00] 自旋算符进度: 28/100, 当前算符: S_0 · S_27, 耗时: 6.219s
[2025-08-28 17:07:07] 自旋算符进度: 29/100, 当前算符: S_0 · S_28, 耗时: 6.195s
[2025-08-28 17:07:13] 自旋算符进度: 30/100, 当前算符: S_0 · S_29, 耗时: 6.219s
[2025-08-28 17:07:19] 自旋算符进度: 31/100, 当前算符: S_0 · S_30, 耗时: 6.195s
[2025-08-28 17:07:25] 自旋算符进度: 32/100, 当前算符: S_0 · S_31, 耗时: 6.195s
[2025-08-28 17:07:31] 自旋算符进度: 33/100, 当前算符: S_0 · S_32, 耗时: 6.194s
[2025-08-28 17:07:38] 自旋算符进度: 34/100, 当前算符: S_0 · S_33, 耗时: 6.219s
[2025-08-28 17:07:44] 自旋算符进度: 35/100, 当前算符: S_0 · S_34, 耗时: 6.194s
[2025-08-28 17:07:50] 自旋算符进度: 36/100, 当前算符: S_0 · S_35, 耗时: 6.191s
[2025-08-28 17:07:56] 自旋算符进度: 37/100, 当前算符: S_0 · S_36, 耗时: 6.192s
[2025-08-28 17:08:02] 自旋算符进度: 38/100, 当前算符: S_0 · S_37, 耗时: 6.194s
[2025-08-28 17:08:09] 自旋算符进度: 39/100, 当前算符: S_0 · S_38, 耗时: 6.192s
[2025-08-28 17:08:15] 自旋算符进度: 40/100, 当前算符: S_0 · S_39, 耗时: 6.192s
[2025-08-28 17:08:21] 自旋算符进度: 41/100, 当前算符: S_0 · S_40, 耗时: 6.193s
[2025-08-28 17:08:27] 自旋算符进度: 42/100, 当前算符: S_0 · S_41, 耗时: 6.193s
[2025-08-28 17:08:33] 自旋算符进度: 43/100, 当前算符: S_0 · S_42, 耗时: 6.191s
[2025-08-28 17:08:40] 自旋算符进度: 44/100, 当前算符: S_0 · S_43, 耗时: 6.218s
[2025-08-28 17:08:46] 自旋算符进度: 45/100, 当前算符: S_0 · S_44, 耗时: 6.194s
[2025-08-28 17:08:52] 自旋算符进度: 46/100, 当前算符: S_0 · S_45, 耗时: 6.194s
[2025-08-28 17:08:58] 自旋算符进度: 47/100, 当前算符: S_0 · S_46, 耗时: 6.192s
[2025-08-28 17:09:04] 自旋算符进度: 48/100, 当前算符: S_0 · S_47, 耗时: 6.192s
[2025-08-28 17:09:11] 自旋算符进度: 49/100, 当前算符: S_0 · S_48, 耗时: 6.194s
[2025-08-28 17:09:17] 自旋算符进度: 50/100, 当前算符: S_0 · S_49, 耗时: 6.194s
[2025-08-28 17:09:23] 自旋算符进度: 51/100, 当前算符: S_0 · S_50, 耗时: 6.194s
[2025-08-28 17:09:29] 自旋算符进度: 52/100, 当前算符: S_0 · S_51, 耗时: 6.194s
[2025-08-28 17:09:35] 自旋算符进度: 53/100, 当前算符: S_0 · S_52, 耗时: 6.193s
[2025-08-28 17:09:42] 自旋算符进度: 54/100, 当前算符: S_0 · S_53, 耗时: 6.192s
[2025-08-28 17:09:48] 自旋算符进度: 55/100, 当前算符: S_0 · S_54, 耗时: 6.195s
[2025-08-28 17:09:54] 自旋算符进度: 56/100, 当前算符: S_0 · S_55, 耗时: 6.194s
[2025-08-28 17:10:00] 自旋算符进度: 57/100, 当前算符: S_0 · S_56, 耗时: 6.195s
[2025-08-28 17:10:06] 自旋算符进度: 58/100, 当前算符: S_0 · S_57, 耗时: 6.195s
[2025-08-28 17:10:13] 自旋算符进度: 59/100, 当前算符: S_0 · S_58, 耗时: 6.218s
[2025-08-28 17:10:19] 自旋算符进度: 60/100, 当前算符: S_0 · S_59, 耗时: 6.193s
[2025-08-28 17:10:25] 自旋算符进度: 61/100, 当前算符: S_0 · S_60, 耗时: 6.194s
[2025-08-28 17:10:31] 自旋算符进度: 62/100, 当前算符: S_0 · S_61, 耗时: 6.193s
[2025-08-28 17:10:37] 自旋算符进度: 63/100, 当前算符: S_0 · S_62, 耗时: 6.219s
[2025-08-28 17:10:44] 自旋算符进度: 64/100, 当前算符: S_0 · S_63, 耗时: 6.193s
[2025-08-28 17:10:50] 自旋算符进度: 65/100, 当前算符: S_0 · S_64, 耗时: 6.194s
[2025-08-28 17:10:56] 自旋算符进度: 66/100, 当前算符: S_0 · S_65, 耗时: 6.193s
[2025-08-28 17:11:02] 自旋算符进度: 67/100, 当前算符: S_0 · S_66, 耗时: 6.196s
[2025-08-28 17:11:08] 自旋算符进度: 68/100, 当前算符: S_0 · S_67, 耗时: 6.194s
[2025-08-28 17:11:15] 自旋算符进度: 69/100, 当前算符: S_0 · S_68, 耗时: 6.196s
[2025-08-28 17:11:21] 自旋算符进度: 70/100, 当前算符: S_0 · S_69, 耗时: 6.218s
[2025-08-28 17:11:27] 自旋算符进度: 71/100, 当前算符: S_0 · S_70, 耗时: 6.195s
[2025-08-28 17:11:33] 自旋算符进度: 72/100, 当前算符: S_0 · S_71, 耗时: 6.218s
[2025-08-28 17:11:39] 自旋算符进度: 73/100, 当前算符: S_0 · S_72, 耗时: 6.219s
[2025-08-28 17:11:46] 自旋算符进度: 74/100, 当前算符: S_0 · S_73, 耗时: 6.193s
[2025-08-28 17:11:52] 自旋算符进度: 75/100, 当前算符: S_0 · S_74, 耗时: 6.218s
[2025-08-28 17:11:58] 自旋算符进度: 76/100, 当前算符: S_0 · S_75, 耗时: 6.193s
[2025-08-28 17:12:04] 自旋算符进度: 77/100, 当前算符: S_0 · S_76, 耗时: 6.194s
[2025-08-28 17:12:10] 自旋算符进度: 78/100, 当前算符: S_0 · S_77, 耗时: 6.190s
[2025-08-28 17:12:17] 自旋算符进度: 79/100, 当前算符: S_0 · S_78, 耗时: 6.218s
[2025-08-28 17:12:23] 自旋算符进度: 80/100, 当前算符: S_0 · S_79, 耗时: 6.192s
[2025-08-28 17:12:29] 自旋算符进度: 81/100, 当前算符: S_0 · S_80, 耗时: 6.193s
[2025-08-28 17:12:35] 自旋算符进度: 82/100, 当前算符: S_0 · S_81, 耗时: 6.190s
[2025-08-28 17:12:41] 自旋算符进度: 83/100, 当前算符: S_0 · S_82, 耗时: 6.218s
[2025-08-28 17:12:48] 自旋算符进度: 84/100, 当前算符: S_0 · S_83, 耗时: 6.192s
[2025-08-28 17:12:54] 自旋算符进度: 85/100, 当前算符: S_0 · S_84, 耗时: 6.192s
[2025-08-28 17:13:00] 自旋算符进度: 86/100, 当前算符: S_0 · S_85, 耗时: 6.218s
[2025-08-28 17:13:06] 自旋算符进度: 87/100, 当前算符: S_0 · S_86, 耗时: 6.194s
[2025-08-28 17:13:12] 自旋算符进度: 88/100, 当前算符: S_0 · S_87, 耗时: 6.189s
[2025-08-28 17:13:19] 自旋算符进度: 89/100, 当前算符: S_0 · S_88, 耗时: 6.193s
[2025-08-28 17:13:25] 自旋算符进度: 90/100, 当前算符: S_0 · S_89, 耗时: 6.217s
[2025-08-28 17:13:31] 自旋算符进度: 91/100, 当前算符: S_0 · S_90, 耗时: 6.194s
[2025-08-28 17:13:37] 自旋算符进度: 92/100, 当前算符: S_0 · S_91, 耗时: 6.194s
[2025-08-28 17:13:44] 自旋算符进度: 93/100, 当前算符: S_0 · S_92, 耗时: 6.217s
[2025-08-28 17:13:50] 自旋算符进度: 94/100, 当前算符: S_0 · S_93, 耗时: 6.192s
[2025-08-28 17:13:56] 自旋算符进度: 95/100, 当前算符: S_0 · S_94, 耗时: 6.193s
[2025-08-28 17:14:02] 自旋算符进度: 96/100, 当前算符: S_0 · S_95, 耗时: 6.193s
[2025-08-28 17:14:08] 自旋算符进度: 97/100, 当前算符: S_0 · S_96, 耗时: 6.194s
[2025-08-28 17:14:15] 自旋算符进度: 98/100, 当前算符: S_0 · S_97, 耗时: 6.192s
[2025-08-28 17:14:21] 自旋算符进度: 99/100, 当前算符: S_0 · S_98, 耗时: 6.217s
[2025-08-28 17:14:27] 自旋算符进度: 100/100, 当前算符: S_0 · S_99, 耗时: 6.191s
[2025-08-28 17:14:27] 自旋相关函数计算完成,总耗时 628.87 秒
[2025-08-28 17:14:27] 计算傅里叶变换...
[2025-08-28 17:14:28] 自旋结构因子计算完成
[2025-08-28 17:14:29] 自旋相关函数平均误差: 0.000606
