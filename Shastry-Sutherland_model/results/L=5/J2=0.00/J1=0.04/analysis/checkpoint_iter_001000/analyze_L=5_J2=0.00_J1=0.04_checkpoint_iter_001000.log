[2025-09-02 23:49:37] 使用checkpoint文件: results/L=5/J2=0.00/J1=0.04/training/checkpoints/checkpoint_iter_001000.pkl
[2025-09-02 23:49:52] ✓ 从checkpoint加载参数: 1000
[2025-09-02 23:49:52]   - 能量: -85.048613+0.003217j ± 0.055899
[2025-09-02 23:49:52] ================================================================================
[2025-09-02 23:49:52] 加载量子态: L=5, J2=0.00, J1=0.04, checkpoint=checkpoint_iter_001000
[2025-09-02 23:49:52] 使用采样数目: 1048576
[2025-09-02 23:49:52] 设置样本数为: 1048576
[2025-09-02 23:49:52] 开始生成共享样本集...
[2025-09-02 23:56:20] 样本生成完成,耗时: 388.770 秒
[2025-09-02 23:56:20] ================================================================================
[2025-09-02 23:56:20] 开始计算自旋结构因子...
[2025-09-02 23:56:20] 初始化操作符缓存...
[2025-09-02 23:56:20] 预构建所有自旋相关操作符...
[2025-09-02 23:56:21] 开始计算自旋相关函数...
[2025-09-02 23:56:34] 自旋算符进度: 1/100, 当前算符: S_0 · S_0, 耗时: 13.868s
[2025-09-02 23:56:53] 自旋算符进度: 2/100, 当前算符: S_0 · S_1, 耗时: 18.391s
[2025-09-02 23:57:06] 自旋算符进度: 3/100, 当前算符: S_0 · S_2, 耗时: 13.565s
[2025-09-02 23:57:20] 自旋算符进度: 4/100, 当前算符: S_0 · S_3, 耗时: 13.558s
[2025-09-02 23:57:34] 自旋算符进度: 5/100, 当前算符: S_0 · S_4, 耗时: 13.616s
[2025-09-02 23:57:47] 自旋算符进度: 6/100, 当前算符: S_0 · S_5, 耗时: 13.577s
[2025-09-02 23:58:01] 自旋算符进度: 7/100, 当前算符: S_0 · S_6, 耗时: 13.579s
[2025-09-02 23:58:14] 自旋算符进度: 8/100, 当前算符: S_0 · S_7, 耗时: 13.594s
[2025-09-02 23:58:28] 自旋算符进度: 9/100, 当前算符: S_0 · S_8, 耗时: 13.613s
[2025-09-02 23:58:42] 自旋算符进度: 10/100, 当前算符: S_0 · S_9, 耗时: 13.579s
[2025-09-02 23:58:55] 自旋算符进度: 11/100, 当前算符: S_0 · S_10, 耗时: 13.602s
[2025-09-02 23:59:09] 自旋算符进度: 12/100, 当前算符: S_0 · S_11, 耗时: 13.602s
[2025-09-02 23:59:22] 自旋算符进度: 13/100, 当前算符: S_0 · S_12, 耗时: 13.588s
[2025-09-02 23:59:36] 自旋算符进度: 14/100, 当前算符: S_0 · S_13, 耗时: 13.562s
[2025-09-02 23:59:50] 自旋算符进度: 15/100, 当前算符: S_0 · S_14, 耗时: 13.606s
[2025-09-03 00:00:03] 自旋算符进度: 16/100, 当前算符: S_0 · S_15, 耗时: 13.609s
[2025-09-03 00:00:17] 自旋算符进度: 17/100, 当前算符: S_0 · S_16, 耗时: 13.545s
[2025-09-03 00:00:30] 自旋算符进度: 18/100, 当前算符: S_0 · S_17, 耗时: 13.604s
[2025-09-03 00:00:44] 自旋算符进度: 19/100, 当前算符: S_0 · S_18, 耗时: 13.566s
[2025-09-03 00:00:57] 自旋算符进度: 20/100, 当前算符: S_0 · S_19, 耗时: 13.563s
[2025-09-03 00:01:11] 自旋算符进度: 21/100, 当前算符: S_0 · S_20, 耗时: 13.607s
[2025-09-03 00:01:25] 自旋算符进度: 22/100, 当前算符: S_0 · S_21, 耗时: 13.573s
[2025-09-03 00:01:38] 自旋算符进度: 23/100, 当前算符: S_0 · S_22, 耗时: 13.541s
[2025-09-03 00:01:52] 自旋算符进度: 24/100, 当前算符: S_0 · S_23, 耗时: 13.567s
[2025-09-03 00:02:05] 自旋算符进度: 25/100, 当前算符: S_0 · S_24, 耗时: 13.613s
[2025-09-03 00:02:19] 自旋算符进度: 26/100, 当前算符: S_0 · S_25, 耗时: 13.560s
[2025-09-03 00:02:33] 自旋算符进度: 27/100, 当前算符: S_0 · S_26, 耗时: 13.606s
[2025-09-03 00:02:46] 自旋算符进度: 28/100, 当前算符: S_0 · S_27, 耗时: 13.571s
[2025-09-03 00:03:00] 自旋算符进度: 29/100, 当前算符: S_0 · S_28, 耗时: 13.602s
[2025-09-03 00:03:13] 自旋算符进度: 30/100, 当前算符: S_0 · S_29, 耗时: 13.579s
[2025-09-03 00:03:27] 自旋算符进度: 31/100, 当前算符: S_0 · S_30, 耗时: 13.601s
[2025-09-03 00:03:40] 自旋算符进度: 32/100, 当前算符: S_0 · S_31, 耗时: 13.591s
[2025-09-03 00:03:54] 自旋算符进度: 33/100, 当前算符: S_0 · S_32, 耗时: 13.606s
[2025-09-03 00:04:08] 自旋算符进度: 34/100, 当前算符: S_0 · S_33, 耗时: 13.577s
[2025-09-03 00:04:21] 自旋算符进度: 35/100, 当前算符: S_0 · S_34, 耗时: 13.593s
[2025-09-03 00:04:35] 自旋算符进度: 36/100, 当前算符: S_0 · S_35, 耗时: 13.610s
[2025-09-03 00:04:48] 自旋算符进度: 37/100, 当前算符: S_0 · S_36, 耗时: 13.581s
[2025-09-03 00:05:02] 自旋算符进度: 38/100, 当前算符: S_0 · S_37, 耗时: 13.592s
[2025-09-03 00:05:16] 自旋算符进度: 39/100, 当前算符: S_0 · S_38, 耗时: 13.581s
[2025-09-03 00:05:29] 自旋算符进度: 40/100, 当前算符: S_0 · S_39, 耗时: 13.561s
[2025-09-03 00:05:43] 自旋算符进度: 41/100, 当前算符: S_0 · S_40, 耗时: 13.593s
[2025-09-03 00:05:56] 自旋算符进度: 42/100, 当前算符: S_0 · S_41, 耗时: 13.568s
[2025-09-03 00:06:10] 自旋算符进度: 43/100, 当前算符: S_0 · S_42, 耗时: 13.614s
[2025-09-03 00:06:24] 自旋算符进度: 44/100, 当前算符: S_0 · S_43, 耗时: 13.538s
[2025-09-03 00:06:37] 自旋算符进度: 45/100, 当前算符: S_0 · S_44, 耗时: 13.601s
[2025-09-03 00:06:51] 自旋算符进度: 46/100, 当前算符: S_0 · S_45, 耗时: 13.581s
[2025-09-03 00:07:04] 自旋算符进度: 47/100, 当前算符: S_0 · S_46, 耗时: 13.595s
[2025-09-03 00:07:18] 自旋算符进度: 48/100, 当前算符: S_0 · S_47, 耗时: 13.602s
[2025-09-03 00:07:32] 自旋算符进度: 49/100, 当前算符: S_0 · S_48, 耗时: 13.605s
[2025-09-03 00:07:45] 自旋算符进度: 50/100, 当前算符: S_0 · S_49, 耗时: 13.567s
[2025-09-03 00:07:59] 自旋算符进度: 51/100, 当前算符: S_0 · S_50, 耗时: 13.609s
[2025-09-03 00:08:13] 自旋算符进度: 52/100, 当前算符: S_0 · S_51, 耗时: 13.599s
[2025-09-03 00:08:26] 自旋算符进度: 53/100, 当前算符: S_0 · S_52, 耗时: 13.569s
[2025-09-03 00:08:40] 自旋算符进度: 54/100, 当前算符: S_0 · S_53, 耗时: 13.606s
[2025-09-03 00:08:53] 自旋算符进度: 55/100, 当前算符: S_0 · S_54, 耗时: 13.598s
[2025-09-03 00:09:07] 自旋算符进度: 56/100, 当前算符: S_0 · S_55, 耗时: 13.608s
[2025-09-03 00:09:21] 自旋算符进度: 57/100, 当前算符: S_0 · S_56, 耗时: 13.583s
[2025-09-03 00:09:34] 自旋算符进度: 58/100, 当前算符: S_0 · S_57, 耗时: 13.600s
[2025-09-03 00:09:48] 自旋算符进度: 59/100, 当前算符: S_0 · S_58, 耗时: 13.572s
[2025-09-03 00:10:01] 自旋算符进度: 60/100, 当前算符: S_0 · S_59, 耗时: 13.583s
[2025-09-03 00:10:15] 自旋算符进度: 61/100, 当前算符: S_0 · S_60, 耗时: 13.611s
[2025-09-03 00:10:29] 自旋算符进度: 62/100, 当前算符: S_0 · S_61, 耗时: 13.557s
[2025-09-03 00:10:42] 自旋算符进度: 63/100, 当前算符: S_0 · S_62, 耗时: 13.604s
[2025-09-03 00:10:56] 自旋算符进度: 64/100, 当前算符: S_0 · S_63, 耗时: 13.583s
[2025-09-03 00:11:09] 自旋算符进度: 65/100, 当前算符: S_0 · S_64, 耗时: 13.555s
[2025-09-03 00:11:23] 自旋算符进度: 66/100, 当前算符: S_0 · S_65, 耗时: 13.577s
[2025-09-03 00:11:36] 自旋算符进度: 67/100, 当前算符: S_0 · S_66, 耗时: 13.605s
[2025-09-03 00:11:50] 自旋算符进度: 68/100, 当前算符: S_0 · S_67, 耗时: 13.564s
[2025-09-03 00:12:04] 自旋算符进度: 69/100, 当前算符: S_0 · S_68, 耗时: 13.574s
[2025-09-03 00:12:17] 自旋算符进度: 70/100, 当前算符: S_0 · S_69, 耗时: 13.607s
[2025-09-03 00:12:31] 自旋算符进度: 71/100, 当前算符: S_0 · S_70, 耗时: 13.559s
[2025-09-03 00:12:44] 自旋算符进度: 72/100, 当前算符: S_0 · S_71, 耗时: 13.605s
[2025-09-03 00:12:58] 自旋算符进度: 73/100, 当前算符: S_0 · S_72, 耗时: 13.610s
[2025-09-03 00:13:12] 自旋算符进度: 74/100, 当前算符: S_0 · S_73, 耗时: 13.572s
[2025-09-03 00:13:25] 自旋算符进度: 75/100, 当前算符: S_0 · S_74, 耗时: 13.602s
[2025-09-03 00:13:39] 自旋算符进度: 76/100, 当前算符: S_0 · S_75, 耗时: 13.564s
[2025-09-03 00:13:52] 自旋算符进度: 77/100, 当前算符: S_0 · S_76, 耗时: 13.607s
[2025-09-03 00:14:06] 自旋算符进度: 78/100, 当前算符: S_0 · S_77, 耗时: 13.573s
[2025-09-03 00:14:20] 自旋算符进度: 79/100, 当前算符: S_0 · S_78, 耗时: 13.609s
[2025-09-03 00:14:33] 自旋算符进度: 80/100, 当前算符: S_0 · S_79, 耗时: 13.555s
[2025-09-03 00:14:47] 自旋算符进度: 81/100, 当前算符: S_0 · S_80, 耗时: 13.570s
[2025-09-03 00:15:00] 自旋算符进度: 82/100, 当前算符: S_0 · S_81, 耗时: 13.569s
[2025-09-03 00:15:14] 自旋算符进度: 83/100, 当前算符: S_0 · S_82, 耗时: 13.604s
[2025-09-03 00:15:27] 自旋算符进度: 84/100, 当前算符: S_0 · S_83, 耗时: 13.582s
[2025-09-03 00:15:41] 自旋算符进度: 85/100, 当前算符: S_0 · S_84, 耗时: 13.567s
[2025-09-03 00:15:55] 自旋算符进度: 86/100, 当前算符: S_0 · S_85, 耗时: 13.590s
[2025-09-03 00:16:08] 自旋算符进度: 87/100, 当前算符: S_0 · S_86, 耗时: 13.584s
[2025-09-03 00:16:22] 自旋算符进度: 88/100, 当前算符: S_0 · S_87, 耗时: 13.568s
[2025-09-03 00:16:35] 自旋算符进度: 89/100, 当前算符: S_0 · S_88, 耗时: 13.558s
[2025-09-03 00:16:49] 自旋算符进度: 90/100, 当前算符: S_0 · S_89, 耗时: 13.599s
[2025-09-03 00:17:03] 自旋算符进度: 91/100, 当前算符: S_0 · S_90, 耗时: 13.586s
[2025-09-03 00:17:16] 自旋算符进度: 92/100, 当前算符: S_0 · S_91, 耗时: 13.604s
[2025-09-03 00:17:30] 自旋算符进度: 93/100, 当前算符: S_0 · S_92, 耗时: 13.608s
[2025-09-03 00:17:43] 自旋算符进度: 94/100, 当前算符: S_0 · S_93, 耗时: 13.552s
[2025-09-03 00:17:57] 自旋算符进度: 95/100, 当前算符: S_0 · S_94, 耗时: 13.600s
[2025-09-03 00:18:10] 自旋算符进度: 96/100, 当前算符: S_0 · S_95, 耗时: 13.567s
[2025-09-03 00:18:24] 自旋算符进度: 97/100, 当前算符: S_0 · S_96, 耗时: 13.582s
[2025-09-03 00:18:38] 自旋算符进度: 98/100, 当前算符: S_0 · S_97, 耗时: 13.490s
[2025-09-03 00:18:51] 自旋算符进度: 99/100, 当前算符: S_0 · S_98, 耗时: 13.604s
[2025-09-03 00:19:05] 自旋算符进度: 100/100, 当前算符: S_0 · S_99, 耗时: 13.571s
[2025-09-03 00:19:05] 自旋相关函数计算完成,总耗时 1364.17 秒
[2025-09-03 00:19:06] 计算傅里叶变换...
[2025-09-03 00:19:09] 自旋结构因子计算完成
[2025-09-03 00:19:10] 自旋相关函数平均误差: 0.000755
