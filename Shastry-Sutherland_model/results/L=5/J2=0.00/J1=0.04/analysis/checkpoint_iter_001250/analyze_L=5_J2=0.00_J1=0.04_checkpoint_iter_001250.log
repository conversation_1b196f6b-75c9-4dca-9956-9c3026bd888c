[2025-09-03 00:19:17] 使用checkpoint文件: results/L=5/J2=0.00/J1=0.04/training/checkpoints/checkpoint_iter_001250.pkl
[2025-09-03 00:19:32] ✓ 从checkpoint加载参数: 1250
[2025-09-03 00:19:32]   - 能量: -85.028770-0.002382j ± 0.056571
[2025-09-03 00:19:32] ================================================================================
[2025-09-03 00:19:32] 加载量子态: L=5, J2=0.00, J1=0.04, checkpoint=checkpoint_iter_001250
[2025-09-03 00:19:32] 使用采样数目: 1048576
[2025-09-03 00:19:32] 设置样本数为: 1048576
[2025-09-03 00:19:32] 开始生成共享样本集...
[2025-09-03 00:26:01] 样本生成完成,耗时: 388.797 秒
[2025-09-03 00:26:01] ================================================================================
[2025-09-03 00:26:01] 开始计算自旋结构因子...
[2025-09-03 00:26:01] 初始化操作符缓存...
[2025-09-03 00:26:01] 预构建所有自旋相关操作符...
[2025-09-03 00:26:01] 开始计算自旋相关函数...
[2025-09-03 00:26:15] 自旋算符进度: 1/100, 当前算符: S_0 · S_0, 耗时: 14.754s
[2025-09-03 00:26:34] 自旋算符进度: 2/100, 当前算符: S_0 · S_1, 耗时: 18.353s
[2025-09-03 00:26:47] 自旋算符进度: 3/100, 当前算符: S_0 · S_2, 耗时: 13.586s
[2025-09-03 00:27:01] 自旋算符进度: 4/100, 当前算符: S_0 · S_3, 耗时: 13.601s
[2025-09-03 00:27:15] 自旋算符进度: 5/100, 当前算符: S_0 · S_4, 耗时: 13.715s
[2025-09-03 00:27:28] 自旋算符进度: 6/100, 当前算符: S_0 · S_5, 耗时: 13.662s
[2025-09-03 00:27:42] 自旋算符进度: 7/100, 当前算符: S_0 · S_6, 耗时: 13.655s
[2025-09-03 00:27:56] 自旋算符进度: 8/100, 当前算符: S_0 · S_7, 耗时: 13.726s
[2025-09-03 00:28:09] 自旋算符进度: 9/100, 当前算符: S_0 · S_8, 耗时: 13.730s
[2025-09-03 00:28:23] 自旋算符进度: 10/100, 当前算符: S_0 · S_9, 耗时: 13.658s
[2025-09-03 00:28:37] 自旋算符进度: 11/100, 当前算符: S_0 · S_10, 耗时: 13.732s
[2025-09-03 00:28:51] 自旋算符进度: 12/100, 当前算符: S_0 · S_11, 耗时: 13.704s
[2025-09-03 00:29:04] 自旋算符进度: 13/100, 当前算符: S_0 · S_12, 耗时: 13.705s
[2025-09-03 00:29:18] 自旋算符进度: 14/100, 当前算符: S_0 · S_13, 耗时: 13.665s
[2025-09-03 00:29:32] 自旋算符进度: 15/100, 当前算符: S_0 · S_14, 耗时: 13.738s
[2025-09-03 00:29:45] 自旋算符进度: 16/100, 当前算符: S_0 · S_15, 耗时: 13.719s
[2025-09-03 00:29:59] 自旋算符进度: 17/100, 当前算符: S_0 · S_16, 耗时: 13.647s
[2025-09-03 00:30:13] 自旋算符进度: 18/100, 当前算符: S_0 · S_17, 耗时: 13.710s
[2025-09-03 00:30:26] 自旋算符进度: 19/100, 当前算符: S_0 · S_18, 耗时: 13.647s
[2025-09-03 00:30:40] 自旋算符进度: 20/100, 当前算符: S_0 · S_19, 耗时: 13.665s
[2025-09-03 00:30:54] 自旋算符进度: 21/100, 当前算符: S_0 · S_20, 耗时: 13.724s
[2025-09-03 00:31:07] 自旋算符进度: 22/100, 当前算符: S_0 · S_21, 耗时: 13.666s
[2025-09-03 00:31:21] 自旋算符进度: 23/100, 当前算符: S_0 · S_22, 耗时: 13.672s
[2025-09-03 00:31:35] 自旋算符进度: 24/100, 当前算符: S_0 · S_23, 耗时: 13.640s
[2025-09-03 00:31:49] 自旋算符进度: 25/100, 当前算符: S_0 · S_24, 耗时: 13.707s
[2025-09-03 00:32:02] 自旋算符进度: 26/100, 当前算符: S_0 · S_25, 耗时: 13.690s
[2025-09-03 00:32:16] 自旋算符进度: 27/100, 当前算符: S_0 · S_26, 耗时: 13.712s
[2025-09-03 00:32:30] 自旋算符进度: 28/100, 当前算符: S_0 · S_27, 耗时: 13.648s
[2025-09-03 00:32:43] 自旋算符进度: 29/100, 当前算符: S_0 · S_28, 耗时: 13.714s
[2025-09-03 00:32:57] 自旋算符进度: 30/100, 当前算符: S_0 · S_29, 耗时: 13.679s
[2025-09-03 00:33:11] 自旋算符进度: 31/100, 当前算符: S_0 · S_30, 耗时: 13.713s
[2025-09-03 00:33:24] 自旋算符进度: 32/100, 当前算符: S_0 · S_31, 耗时: 13.711s
[2025-09-03 00:33:38] 自旋算符进度: 33/100, 当前算符: S_0 · S_32, 耗时: 13.709s
[2025-09-03 00:33:52] 自旋算符进度: 34/100, 当前算符: S_0 · S_33, 耗时: 13.649s
[2025-09-03 00:34:06] 自旋算符进度: 35/100, 当前算符: S_0 · S_34, 耗时: 13.707s
[2025-09-03 00:34:19] 自旋算符进度: 36/100, 当前算符: S_0 · S_35, 耗时: 13.700s
[2025-09-03 00:34:33] 自旋算符进度: 37/100, 当前算符: S_0 · S_36, 耗时: 13.639s
[2025-09-03 00:34:47] 自旋算符进度: 38/100, 当前算符: S_0 · S_37, 耗时: 13.716s
[2025-09-03 00:35:00] 自旋算符进度: 39/100, 当前算符: S_0 · S_38, 耗时: 13.402s
[2025-09-03 00:35:14] 自旋算符进度: 40/100, 当前算符: S_0 · S_39, 耗时: 13.648s
[2025-09-03 00:35:27] 自旋算符进度: 41/100, 当前算符: S_0 · S_40, 耗时: 13.709s
[2025-09-03 00:35:41] 自旋算符进度: 42/100, 当前算符: S_0 · S_41, 耗时: 13.653s
[2025-09-03 00:35:55] 自旋算符进度: 43/100, 当前算符: S_0 · S_42, 耗时: 13.716s
[2025-09-03 00:36:08] 自旋算符进度: 44/100, 当前算符: S_0 · S_43, 耗时: 13.668s
[2025-09-03 00:36:22] 自旋算符进度: 45/100, 当前算符: S_0 · S_44, 耗时: 13.709s
[2025-09-03 00:36:36] 自旋算符进度: 46/100, 当前算符: S_0 · S_45, 耗时: 13.676s
[2025-09-03 00:36:50] 自旋算符进度: 47/100, 当前算符: S_0 · S_46, 耗时: 13.723s
[2025-09-03 00:37:03] 自旋算符进度: 48/100, 当前算符: S_0 · S_47, 耗时: 13.711s
[2025-09-03 00:37:17] 自旋算符进度: 49/100, 当前算符: S_0 · S_48, 耗时: 13.706s
[2025-09-03 00:37:31] 自旋算符进度: 50/100, 当前算符: S_0 · S_49, 耗时: 13.664s
[2025-09-03 00:37:44] 自旋算符进度: 51/100, 当前算符: S_0 · S_50, 耗时: 13.723s
[2025-09-03 00:37:58] 自旋算符进度: 52/100, 当前算符: S_0 · S_51, 耗时: 13.717s
[2025-09-03 00:38:12] 自旋算符进度: 53/100, 当前算符: S_0 · S_52, 耗时: 13.670s
[2025-09-03 00:38:25] 自旋算符进度: 54/100, 当前算符: S_0 · S_53, 耗时: 13.696s
[2025-09-03 00:38:39] 自旋算符进度: 55/100, 当前算符: S_0 · S_54, 耗时: 13.693s
[2025-09-03 00:38:53] 自旋算符进度: 56/100, 当前算符: S_0 · S_55, 耗时: 13.738s
[2025-09-03 00:39:07] 自旋算符进度: 57/100, 当前算符: S_0 · S_56, 耗时: 13.676s
[2025-09-03 00:39:20] 自旋算符进度: 58/100, 当前算符: S_0 · S_57, 耗时: 13.685s
[2025-09-03 00:39:34] 自旋算符进度: 59/100, 当前算符: S_0 · S_58, 耗时: 13.662s
[2025-09-03 00:39:48] 自旋算符进度: 60/100, 当前算符: S_0 · S_59, 耗时: 13.651s
[2025-09-03 00:40:01] 自旋算符进度: 61/100, 当前算符: S_0 · S_60, 耗时: 13.712s
[2025-09-03 00:40:15] 自旋算符进度: 62/100, 当前算符: S_0 · S_61, 耗时: 13.658s
[2025-09-03 00:40:29] 自旋算符进度: 63/100, 当前算符: S_0 · S_62, 耗时: 13.723s
[2025-09-03 00:40:42] 自旋算符进度: 64/100, 当前算符: S_0 · S_63, 耗时: 13.664s
[2025-09-03 00:40:56] 自旋算符进度: 65/100, 当前算符: S_0 · S_64, 耗时: 13.663s
[2025-09-03 00:41:10] 自旋算符进度: 66/100, 当前算符: S_0 · S_65, 耗时: 13.654s
[2025-09-03 00:41:23] 自旋算符进度: 67/100, 当前算符: S_0 · S_66, 耗时: 13.698s
[2025-09-03 00:41:37] 自旋算符进度: 68/100, 当前算符: S_0 · S_67, 耗时: 13.660s
[2025-09-03 00:41:51] 自旋算符进度: 69/100, 当前算符: S_0 · S_68, 耗时: 13.657s
[2025-09-03 00:42:04] 自旋算符进度: 70/100, 当前算符: S_0 · S_69, 耗时: 13.698s
[2025-09-03 00:42:18] 自旋算符进度: 71/100, 当前算符: S_0 · S_70, 耗时: 13.663s
[2025-09-03 00:42:32] 自旋算符进度: 72/100, 当前算符: S_0 · S_71, 耗时: 13.712s
[2025-09-03 00:42:45] 自旋算符进度: 73/100, 当前算符: S_0 · S_72, 耗时: 13.720s
[2025-09-03 00:42:59] 自旋算符进度: 74/100, 当前算符: S_0 · S_73, 耗时: 13.672s
[2025-09-03 00:43:13] 自旋算符进度: 75/100, 当前算符: S_0 · S_74, 耗时: 13.721s
[2025-09-03 00:43:27] 自旋算符进度: 76/100, 当前算符: S_0 · S_75, 耗时: 13.647s
[2025-09-03 00:43:40] 自旋算符进度: 77/100, 当前算符: S_0 · S_76, 耗时: 13.724s
[2025-09-03 00:43:54] 自旋算符进度: 78/100, 当前算符: S_0 · S_77, 耗时: 13.664s
[2025-09-03 00:44:08] 自旋算符进度: 79/100, 当前算符: S_0 · S_78, 耗时: 13.705s
[2025-09-03 00:44:21] 自旋算符进度: 80/100, 当前算符: S_0 · S_79, 耗时: 13.661s
[2025-09-03 00:44:35] 自旋算符进度: 81/100, 当前算符: S_0 · S_80, 耗时: 13.664s
[2025-09-03 00:44:49] 自旋算符进度: 82/100, 当前算符: S_0 · S_81, 耗时: 13.647s
[2025-09-03 00:45:02] 自旋算符进度: 83/100, 当前算符: S_0 · S_82, 耗时: 13.738s
[2025-09-03 00:45:16] 自旋算符进度: 84/100, 当前算符: S_0 · S_83, 耗时: 13.666s
[2025-09-03 00:45:30] 自旋算符进度: 85/100, 当前算符: S_0 · S_84, 耗时: 13.619s
[2025-09-03 00:45:43] 自旋算符进度: 86/100, 当前算符: S_0 · S_85, 耗时: 13.715s
[2025-09-03 00:45:57] 自旋算符进度: 87/100, 当前算符: S_0 · S_86, 耗时: 13.654s
[2025-09-03 00:46:11] 自旋算符进度: 88/100, 当前算符: S_0 · S_87, 耗时: 13.663s
[2025-09-03 00:46:24] 自旋算符进度: 89/100, 当前算符: S_0 · S_88, 耗时: 13.650s
[2025-09-03 00:46:38] 自旋算符进度: 90/100, 当前算符: S_0 · S_89, 耗时: 13.725s
[2025-09-03 00:46:52] 自旋算符进度: 91/100, 当前算符: S_0 · S_90, 耗时: 13.665s
[2025-09-03 00:47:05] 自旋算符进度: 92/100, 当前算符: S_0 · S_91, 耗时: 13.710s
[2025-09-03 00:47:19] 自旋算符进度: 93/100, 当前算符: S_0 · S_92, 耗时: 13.726s
[2025-09-03 00:47:33] 自旋算符进度: 94/100, 当前算符: S_0 · S_93, 耗时: 13.619s
[2025-09-03 00:47:47] 自旋算符进度: 95/100, 当前算符: S_0 · S_94, 耗时: 13.716s
[2025-09-03 00:48:00] 自旋算符进度: 96/100, 当前算符: S_0 · S_95, 耗时: 13.635s
[2025-09-03 00:48:14] 自旋算符进度: 97/100, 当前算符: S_0 · S_96, 耗时: 13.648s
[2025-09-03 00:48:28] 自旋算符进度: 98/100, 当前算符: S_0 · S_97, 耗时: 13.669s
[2025-09-03 00:48:41] 自旋算符进度: 99/100, 当前算符: S_0 · S_98, 耗时: 13.696s
[2025-09-03 00:48:55] 自旋算符进度: 100/100, 当前算符: S_0 · S_99, 耗时: 13.667s
[2025-09-03 00:48:55] 自旋相关函数计算完成,总耗时 1374.22 秒
[2025-09-03 00:48:56] 计算傅里叶变换...
[2025-09-03 00:48:59] 自旋结构因子计算完成
[2025-09-03 00:49:00] 自旋相关函数平均误差: 0.000759
