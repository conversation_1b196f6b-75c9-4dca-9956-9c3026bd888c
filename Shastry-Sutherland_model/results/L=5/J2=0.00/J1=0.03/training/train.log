[2025-09-03 14:33:17] ✓ 从checkpoint恢复: results/L=5/J2=0.00/J1=0.04/training/checkpoints/final_GCNN.pkl
[2025-09-03 14:33:17]   - 迭代次数: final
[2025-09-03 14:33:17]   - 能量: -85.089073+0.000976j ± 0.056522
[2025-09-03 14:33:17]   - 时间戳: 2025-09-02T21:33:23.744915+08:00
[2025-09-03 14:33:28] ✓ 变分状态参数已从checkpoint恢复
[2025-09-03 14:33:28] ✓ 从final状态恢复, 重置迭代计数为0
[2025-09-03 14:33:28] ==================================================
[2025-09-03 14:33:28] GCNN for Shastry-Sutherland Model
[2025-09-03 14:33:28] ==================================================
[2025-09-03 14:33:28] System parameters:
[2025-09-03 14:33:28]   - System size: L=5, N=100
[2025-09-03 14:33:28]   - System parameters: J1=0.03, J2=0.0, Q=1.0
[2025-09-03 14:33:28] --------------------------------------------------
[2025-09-03 14:33:28] Model parameters:
[2025-09-03 14:33:28]   - Number of layers = 4
[2025-09-03 14:33:28]   - Number of features = 4
[2025-09-03 14:33:28]   - Total parameters = 19628
[2025-09-03 14:33:28] --------------------------------------------------
[2025-09-03 14:33:28] Training parameters:
[2025-09-03 14:33:28]   - Learning rate: 0.015
[2025-09-03 14:33:28]   - Total iterations: 450
[2025-09-03 14:33:28]   - Annealing cycles: 2
[2025-09-03 14:33:28]   - Initial period: 150
[2025-09-03 14:33:28]   - Period multiplier: 2.0
[2025-09-03 14:33:28]   - Temperature range: 0.0-1.0
[2025-09-03 14:33:28]   - Samples: 4096
[2025-09-03 14:33:28]   - Discarded samples: 0
[2025-09-03 14:33:28]   - Chunk size: 2048
[2025-09-03 14:33:28]   - Diagonal shift: 0.2
[2025-09-03 14:33:28]   - Gradient clipping: 1.0
[2025-09-03 14:33:28]   - Checkpoint enabled: interval=50
[2025-09-03 14:33:28]   - Checkpoint directory: results/L=5/J2=0.00/J1=0.03/training/checkpoints
[2025-09-03 14:33:28] --------------------------------------------------
[2025-09-03 14:33:28] Device status:
[2025-09-03 14:33:28]   - Devices model: NVIDIA H200 NVL
[2025-09-03 14:33:28]   - Number of devices: 1
[2025-09-03 14:33:28]   - Sharding: True
[2025-09-03 14:33:28] ============================================================
[2025-09-03 14:34:23] [Iter 1/450] R0[0/150], Temp: 1.0000, Energy: -84.180924+0.011298j
[2025-09-03 14:35:09] [Iter 2/450] R0[1/150], Temp: 0.9999, Energy: -84.365478+0.014149j
[2025-09-03 14:35:40] [Iter 3/450] R0[2/150], Temp: 0.9996, Energy: -84.484856+0.002996j
[2025-09-03 14:36:11] [Iter 4/450] R0[3/150], Temp: 0.9990, Energy: -84.673892+0.006171j
[2025-09-03 14:36:42] [Iter 5/450] R0[4/150], Temp: 0.9982, Energy: -84.715909+0.004174j
[2025-09-03 14:37:13] [Iter 6/450] R0[5/150], Temp: 0.9973, Energy: -84.590454+0.012204j
[2025-09-03 14:37:44] [Iter 7/450] R0[6/150], Temp: 0.9961, Energy: -84.424476+0.006257j
[2025-09-03 14:38:15] [Iter 8/450] R0[7/150], Temp: 0.9946, Energy: -84.297893+0.005962j
[2025-09-03 14:38:46] [Iter 9/450] R0[8/150], Temp: 0.9930, Energy: -84.474607+0.004658j
[2025-09-03 14:39:17] [Iter 10/450] R0[9/150], Temp: 0.9911, Energy: -84.507205+0.007142j
[2025-09-03 14:39:48] [Iter 11/450] R0[10/150], Temp: 0.9891, Energy: -84.463586-0.003309j
[2025-09-03 14:40:19] [Iter 12/450] R0[11/150], Temp: 0.9868, Energy: -84.354383+0.003407j
[2025-09-03 14:40:50] [Iter 13/450] R0[12/150], Temp: 0.9843, Energy: -84.394276+0.009556j
[2025-09-03 14:41:21] [Iter 14/450] R0[13/150], Temp: 0.9816, Energy: -84.407052+0.000090j
[2025-09-03 14:41:52] [Iter 15/450] R0[14/150], Temp: 0.9787, Energy: -84.409634-0.006288j
[2025-09-03 14:42:23] [Iter 16/450] R0[15/150], Temp: 0.9755, Energy: -84.353811-0.004333j
[2025-09-03 14:42:54] [Iter 17/450] R0[16/150], Temp: 0.9722, Energy: -84.378439+0.001241j
[2025-09-03 14:43:25] [Iter 18/450] R0[17/150], Temp: 0.9686, Energy: -84.578517+0.004719j
[2025-09-03 14:43:56] [Iter 19/450] R0[18/150], Temp: 0.9649, Energy: -84.597263+0.002006j
[2025-09-03 14:44:27] [Iter 20/450] R0[19/150], Temp: 0.9609, Energy: -84.524260+0.004378j
[2025-09-03 14:44:58] [Iter 21/450] R0[20/150], Temp: 0.9568, Energy: -84.364301+0.002358j
[2025-09-03 14:45:29] [Iter 22/450] R0[21/150], Temp: 0.9524, Energy: -84.351076+0.005388j
[2025-09-03 14:46:00] [Iter 23/450] R0[22/150], Temp: 0.9479, Energy: -84.412986+0.000189j
[2025-09-03 14:46:31] [Iter 24/450] R0[23/150], Temp: 0.9431, Energy: -84.334139-0.000003j
[2025-09-03 14:47:02] [Iter 25/450] R0[24/150], Temp: 0.9382, Energy: -84.318561+0.007309j
[2025-09-03 14:47:33] [Iter 26/450] R0[25/150], Temp: 0.9330, Energy: -84.198517-0.010981j
[2025-09-03 14:48:04] [Iter 27/450] R0[26/150], Temp: 0.9277, Energy: -84.272749+0.006730j
[2025-09-03 14:48:35] [Iter 28/450] R0[27/150], Temp: 0.9222, Energy: -84.294691+0.007266j
[2025-09-03 14:49:06] [Iter 29/450] R0[28/150], Temp: 0.9165, Energy: -84.296923+0.001060j
[2025-09-03 14:49:37] [Iter 30/450] R0[29/150], Temp: 0.9106, Energy: -84.282976-0.000248j
[2025-09-03 14:50:08] [Iter 31/450] R0[30/150], Temp: 0.9045, Energy: -84.174972-0.004273j
[2025-09-03 14:50:39] [Iter 32/450] R0[31/150], Temp: 0.8983, Energy: -84.221675-0.004962j
[2025-09-03 14:51:10] [Iter 33/450] R0[32/150], Temp: 0.8918, Energy: -84.257327-0.005016j
[2025-09-03 14:51:41] [Iter 34/450] R0[33/150], Temp: 0.8853, Energy: -84.491969-0.004977j
[2025-09-03 14:52:12] [Iter 35/450] R0[34/150], Temp: 0.8785, Energy: -84.542482+0.003993j
[2025-09-03 14:52:43] [Iter 36/450] R0[35/150], Temp: 0.8716, Energy: -84.277789+0.001456j
[2025-09-03 14:53:14] [Iter 37/450] R0[36/150], Temp: 0.8645, Energy: -84.529623+0.004747j
[2025-09-03 14:53:45] [Iter 38/450] R0[37/150], Temp: 0.8572, Energy: -84.528004-0.005410j
[2025-09-03 14:54:16] [Iter 39/450] R0[38/150], Temp: 0.8498, Energy: -84.491964-0.006784j
[2025-09-03 14:54:47] [Iter 40/450] R0[39/150], Temp: 0.8423, Energy: -84.461347+0.000990j
[2025-09-03 14:55:18] [Iter 41/450] R0[40/150], Temp: 0.8346, Energy: -84.437335+0.003687j
[2025-09-03 14:55:49] [Iter 42/450] R0[41/150], Temp: 0.8267, Energy: -84.510236+0.000751j
[2025-09-03 14:56:20] [Iter 43/450] R0[42/150], Temp: 0.8187, Energy: -84.485859+0.003664j
[2025-09-03 14:56:51] [Iter 44/450] R0[43/150], Temp: 0.8106, Energy: -84.531374-0.002097j
[2025-09-03 14:57:22] [Iter 45/450] R0[44/150], Temp: 0.8023, Energy: -84.479442+0.005415j
[2025-09-03 14:57:53] [Iter 46/450] R0[45/150], Temp: 0.7939, Energy: -84.513618-0.002663j
[2025-09-03 14:58:24] [Iter 47/450] R0[46/150], Temp: 0.7854, Energy: -84.433265-0.000391j
[2025-09-03 14:58:55] [Iter 48/450] R0[47/150], Temp: 0.7767, Energy: -84.384756-0.001440j
[2025-09-03 14:59:26] [Iter 49/450] R0[48/150], Temp: 0.7679, Energy: -84.225285-0.003773j
[2025-09-03 14:59:57] [Iter 50/450] R0[49/150], Temp: 0.7590, Energy: -84.162825-0.001844j
[2025-09-03 14:59:57] ✓ Checkpoint saved: checkpoint_iter_000050.pkl
[2025-09-03 15:00:28] [Iter 51/450] R0[50/150], Temp: 0.7500, Energy: -84.321433-0.001695j
[2025-09-03 15:00:59] [Iter 52/450] R0[51/150], Temp: 0.7409, Energy: -84.396775-0.004280j
[2025-09-03 15:01:30] [Iter 53/450] R0[52/150], Temp: 0.7316, Energy: -84.244284+0.002154j
[2025-09-03 15:02:01] [Iter 54/450] R0[53/150], Temp: 0.7223, Energy: -84.400485-0.001012j
[2025-09-03 15:02:32] [Iter 55/450] R0[54/150], Temp: 0.7129, Energy: -84.200587+0.001852j
[2025-09-03 15:03:03] [Iter 56/450] R0[55/150], Temp: 0.7034, Energy: -84.304634-0.005095j
[2025-09-03 15:03:34] [Iter 57/450] R0[56/150], Temp: 0.6938, Energy: -84.411542-0.005725j
[2025-09-03 15:04:05] [Iter 58/450] R0[57/150], Temp: 0.6841, Energy: -84.387809-0.001799j
[2025-09-03 15:04:36] [Iter 59/450] R0[58/150], Temp: 0.6743, Energy: -84.433985-0.004711j
[2025-09-03 15:05:06] [Iter 60/450] R0[59/150], Temp: 0.6644, Energy: -84.429281-0.006047j
[2025-09-03 15:05:37] [Iter 61/450] R0[60/150], Temp: 0.6545, Energy: -84.427307-0.002290j
[2025-09-03 15:06:08] [Iter 62/450] R0[61/150], Temp: 0.6445, Energy: -84.340754+0.001760j
[2025-09-03 15:06:39] [Iter 63/450] R0[62/150], Temp: 0.6345, Energy: -84.355693+0.001794j
[2025-09-03 15:07:10] [Iter 64/450] R0[63/150], Temp: 0.6243, Energy: -84.206639+0.001108j
[2025-09-03 15:07:41] [Iter 65/450] R0[64/150], Temp: 0.6142, Energy: -84.122557+0.001565j
[2025-09-03 15:08:12] [Iter 66/450] R0[65/150], Temp: 0.6040, Energy: -84.199170-0.002588j
[2025-09-03 15:08:43] [Iter 67/450] R0[66/150], Temp: 0.5937, Energy: -84.400400-0.006791j
[2025-09-03 15:09:14] [Iter 68/450] R0[67/150], Temp: 0.5834, Energy: -84.309505+0.000899j
[2025-09-03 15:09:45] [Iter 69/450] R0[68/150], Temp: 0.5730, Energy: -84.525533-0.005295j
[2025-09-03 15:10:16] [Iter 70/450] R0[69/150], Temp: 0.5627, Energy: -84.470756+0.004225j
[2025-09-03 15:10:47] [Iter 71/450] R0[70/150], Temp: 0.5523, Energy: -84.434322-0.002165j
[2025-09-03 15:11:18] [Iter 72/450] R0[71/150], Temp: 0.5418, Energy: -84.537285-0.008386j
[2025-09-03 15:11:49] [Iter 73/450] R0[72/150], Temp: 0.5314, Energy: -84.481857+0.003052j
[2025-09-03 15:12:20] [Iter 74/450] R0[73/150], Temp: 0.5209, Energy: -84.548791-0.000871j
[2025-09-03 15:12:51] [Iter 75/450] R0[74/150], Temp: 0.5105, Energy: -84.424659+0.002572j
[2025-09-03 15:13:22] [Iter 76/450] R0[75/150], Temp: 0.5000, Energy: -84.470693+0.001799j
[2025-09-03 15:13:53] [Iter 77/450] R0[76/150], Temp: 0.4895, Energy: -84.281615-0.009191j
[2025-09-03 15:14:24] [Iter 78/450] R0[77/150], Temp: 0.4791, Energy: -84.323563+0.002619j
[2025-09-03 15:14:55] [Iter 79/450] R0[78/150], Temp: 0.4686, Energy: -84.451755-0.007012j
[2025-09-03 15:15:26] [Iter 80/450] R0[79/150], Temp: 0.4582, Energy: -84.321522+0.006490j
[2025-09-03 15:15:57] [Iter 81/450] R0[80/150], Temp: 0.4477, Energy: -84.364484-0.001639j
[2025-09-03 15:16:27] [Iter 82/450] R0[81/150], Temp: 0.4373, Energy: -84.390359-0.003108j
[2025-09-03 15:16:58] [Iter 83/450] R0[82/150], Temp: 0.4270, Energy: -84.349142+0.001199j
[2025-09-03 15:17:29] [Iter 84/450] R0[83/150], Temp: 0.4166, Energy: -84.264461-0.002599j
[2025-09-03 15:18:00] [Iter 85/450] R0[84/150], Temp: 0.4063, Energy: -84.290765-0.005255j
[2025-09-03 15:18:31] [Iter 86/450] R0[85/150], Temp: 0.3960, Energy: -84.442542+0.007234j
[2025-09-03 15:19:02] [Iter 87/450] R0[86/150], Temp: 0.3858, Energy: -84.459407-0.000748j
[2025-09-03 15:19:33] [Iter 88/450] R0[87/150], Temp: 0.3757, Energy: -84.327058+0.003918j
[2025-09-03 15:20:04] [Iter 89/450] R0[88/150], Temp: 0.3655, Energy: -84.261284-0.000566j
[2025-09-03 15:20:35] [Iter 90/450] R0[89/150], Temp: 0.3555, Energy: -84.362960-0.000357j
[2025-09-03 15:21:06] [Iter 91/450] R0[90/150], Temp: 0.3455, Energy: -84.436620-0.003423j
[2025-09-03 15:21:37] [Iter 92/450] R0[91/150], Temp: 0.3356, Energy: -84.538629+0.007270j
[2025-09-03 15:22:08] [Iter 93/450] R0[92/150], Temp: 0.3257, Energy: -84.479922+0.000160j
[2025-09-03 15:22:39] [Iter 94/450] R0[93/150], Temp: 0.3159, Energy: -84.540550-0.006820j
[2025-09-03 15:23:10] [Iter 95/450] R0[94/150], Temp: 0.3062, Energy: -84.408431-0.001760j
[2025-09-03 15:23:41] [Iter 96/450] R0[95/150], Temp: 0.2966, Energy: -84.222574+0.001898j
[2025-09-03 15:24:12] [Iter 97/450] R0[96/150], Temp: 0.2871, Energy: -84.358453-0.003493j
[2025-09-03 15:24:43] [Iter 98/450] R0[97/150], Temp: 0.2777, Energy: -84.359067-0.002746j
[2025-09-03 15:25:14] [Iter 99/450] R0[98/150], Temp: 0.2684, Energy: -84.588683-0.001891j
[2025-09-03 15:25:45] [Iter 100/450] R0[99/150], Temp: 0.2591, Energy: -84.617021+0.006717j
[2025-09-03 15:25:45] ✓ Checkpoint saved: checkpoint_iter_000100.pkl
[2025-09-03 15:26:16] [Iter 101/450] R0[100/150], Temp: 0.2500, Energy: -84.481299-0.002850j
[2025-09-03 15:26:46] [Iter 102/450] R0[101/150], Temp: 0.2410, Energy: -84.357853-0.004017j
[2025-09-03 15:27:17] [Iter 103/450] R0[102/150], Temp: 0.2321, Energy: -84.343839-0.002139j
[2025-09-03 15:27:48] [Iter 104/450] R0[103/150], Temp: 0.2233, Energy: -84.295172+0.001199j
[2025-09-03 15:28:19] [Iter 105/450] R0[104/150], Temp: 0.2146, Energy: -84.402208-0.000347j
[2025-09-03 15:28:50] [Iter 106/450] R0[105/150], Temp: 0.2061, Energy: -84.434492-0.003548j
[2025-09-03 15:29:21] [Iter 107/450] R0[106/150], Temp: 0.1977, Energy: -84.504737-0.003879j
[2025-09-03 15:29:52] [Iter 108/450] R0[107/150], Temp: 0.1894, Energy: -84.329024+0.008814j
[2025-09-03 15:30:23] [Iter 109/450] R0[108/150], Temp: 0.1813, Energy: -84.373542+0.004471j
[2025-09-03 15:30:54] [Iter 110/450] R0[109/150], Temp: 0.1733, Energy: -84.399331-0.004729j
[2025-09-03 15:31:25] [Iter 111/450] R0[110/150], Temp: 0.1654, Energy: -84.414432-0.005911j
[2025-09-03 15:31:56] [Iter 112/450] R0[111/150], Temp: 0.1577, Energy: -84.483868-0.013913j
[2025-09-03 15:32:27] [Iter 113/450] R0[112/150], Temp: 0.1502, Energy: -84.312499-0.002423j
[2025-09-03 15:32:58] [Iter 114/450] R0[113/150], Temp: 0.1428, Energy: -84.385557+0.001416j
[2025-09-03 15:33:29] [Iter 115/450] R0[114/150], Temp: 0.1355, Energy: -84.375831+0.001224j
[2025-09-03 15:34:00] [Iter 116/450] R0[115/150], Temp: 0.1284, Energy: -84.437380+0.003814j
[2025-09-03 15:34:30] [Iter 117/450] R0[116/150], Temp: 0.1215, Energy: -84.361326-0.000768j
[2025-09-03 15:35:01] [Iter 118/450] R0[117/150], Temp: 0.1147, Energy: -84.415621-0.001830j
[2025-09-03 15:35:32] [Iter 119/450] R0[118/150], Temp: 0.1082, Energy: -84.350534+0.003542j
[2025-09-03 15:36:03] [Iter 120/450] R0[119/150], Temp: 0.1017, Energy: -84.380846+0.000405j
[2025-09-03 15:36:34] [Iter 121/450] R0[120/150], Temp: 0.0955, Energy: -84.119857-0.000165j
[2025-09-03 15:37:05] [Iter 122/450] R0[121/150], Temp: 0.0894, Energy: -84.224099+0.004263j
[2025-09-03 15:37:36] [Iter 123/450] R0[122/150], Temp: 0.0835, Energy: -84.444977-0.001095j
[2025-09-03 15:38:07] [Iter 124/450] R0[123/150], Temp: 0.0778, Energy: -84.375550+0.000646j
[2025-09-03 15:38:38] [Iter 125/450] R0[124/150], Temp: 0.0723, Energy: -84.454046-0.000280j
[2025-09-03 15:39:09] [Iter 126/450] R0[125/150], Temp: 0.0670, Energy: -84.390059-0.005140j
[2025-09-03 15:39:40] [Iter 127/450] R0[126/150], Temp: 0.0618, Energy: -84.375368+0.000003j
[2025-09-03 15:40:11] [Iter 128/450] R0[127/150], Temp: 0.0569, Energy: -84.340067-0.003527j
[2025-09-03 15:40:42] [Iter 129/450] R0[128/150], Temp: 0.0521, Energy: -84.302204+0.000220j
[2025-09-03 15:41:12] [Iter 130/450] R0[129/150], Temp: 0.0476, Energy: -84.265223-0.003902j
[2025-09-03 15:41:43] [Iter 131/450] R0[130/150], Temp: 0.0432, Energy: -84.237366+0.003581j
[2025-09-03 15:42:14] [Iter 132/450] R0[131/150], Temp: 0.0391, Energy: -84.350827+0.008070j
[2025-09-03 15:42:45] [Iter 133/450] R0[132/150], Temp: 0.0351, Energy: -84.505551+0.002986j
[2025-09-03 15:43:16] [Iter 134/450] R0[133/150], Temp: 0.0314, Energy: -84.308574-0.002200j
[2025-09-03 15:43:47] [Iter 135/450] R0[134/150], Temp: 0.0278, Energy: -84.404573+0.007277j
[2025-09-03 15:44:18] [Iter 136/450] R0[135/150], Temp: 0.0245, Energy: -84.466415-0.004910j
[2025-09-03 15:44:49] [Iter 137/450] R0[136/150], Temp: 0.0213, Energy: -84.461959+0.002823j
[2025-09-03 15:45:20] [Iter 138/450] R0[137/150], Temp: 0.0184, Energy: -84.515459+0.001076j
[2025-09-03 15:45:51] [Iter 139/450] R0[138/150], Temp: 0.0157, Energy: -84.431104+0.004997j
[2025-09-03 15:46:22] [Iter 140/450] R0[139/150], Temp: 0.0132, Energy: -84.349241+0.003229j
[2025-09-03 15:46:53] [Iter 141/450] R0[140/150], Temp: 0.0109, Energy: -84.291792+0.001116j
[2025-09-03 15:47:24] [Iter 142/450] R0[141/150], Temp: 0.0089, Energy: -84.362084+0.001433j
[2025-09-03 15:47:54] [Iter 143/450] R0[142/150], Temp: 0.0070, Energy: -84.403293+0.000587j
[2025-09-03 15:48:25] [Iter 144/450] R0[143/150], Temp: 0.0054, Energy: -84.498421-0.002163j
[2025-09-03 15:48:56] [Iter 145/450] R0[144/150], Temp: 0.0039, Energy: -84.456124-0.000903j
[2025-09-03 15:49:27] [Iter 146/450] R0[145/150], Temp: 0.0027, Energy: -84.351319+0.008794j
[2025-09-03 15:49:58] [Iter 147/450] R0[146/150], Temp: 0.0018, Energy: -84.243037+0.003015j
[2025-09-03 15:50:29] [Iter 148/450] R0[147/150], Temp: 0.0010, Energy: -84.402096-0.001221j
[2025-09-03 15:51:00] [Iter 149/450] R0[148/150], Temp: 0.0004, Energy: -84.418031+0.004511j
[2025-09-03 15:51:31] [Iter 150/450] R0[149/150], Temp: 0.0001, Energy: -84.302992-0.001193j
[2025-09-03 15:51:31] ✓ Checkpoint saved: checkpoint_iter_000150.pkl
[2025-09-03 15:51:31] RESTART #1 | Period: 300
[2025-09-03 15:52:02] [Iter 151/450] R1[0/300], Temp: 1.0000, Energy: -84.296756-0.009137j
[2025-09-03 15:52:33] [Iter 152/450] R1[1/300], Temp: 1.0000, Energy: -84.297266-0.000763j
[2025-09-03 15:53:04] [Iter 153/450] R1[2/300], Temp: 0.9999, Energy: -84.165081-0.001399j
[2025-09-03 15:53:35] [Iter 154/450] R1[3/300], Temp: 0.9998, Energy: -84.189943-0.000989j
[2025-09-03 15:54:06] [Iter 155/450] R1[4/300], Temp: 0.9996, Energy: -84.265139-0.003320j
[2025-09-03 15:54:37] [Iter 156/450] R1[5/300], Temp: 0.9993, Energy: -84.520012-0.003523j
[2025-09-03 15:55:08] [Iter 157/450] R1[6/300], Temp: 0.9990, Energy: -84.496760+0.001606j
[2025-09-03 15:55:38] [Iter 158/450] R1[7/300], Temp: 0.9987, Energy: -84.151408-0.001634j
[2025-09-03 15:56:09] [Iter 159/450] R1[8/300], Temp: 0.9982, Energy: -84.167208+0.001610j
[2025-09-03 15:56:40] [Iter 160/450] R1[9/300], Temp: 0.9978, Energy: -84.262777-0.005605j
[2025-09-03 15:57:11] [Iter 161/450] R1[10/300], Temp: 0.9973, Energy: -84.257775-0.001174j
[2025-09-03 15:57:42] [Iter 162/450] R1[11/300], Temp: 0.9967, Energy: -84.385304+0.005765j
[2025-09-03 15:58:13] [Iter 163/450] R1[12/300], Temp: 0.9961, Energy: -84.425523+0.004545j
[2025-09-03 15:58:44] [Iter 164/450] R1[13/300], Temp: 0.9954, Energy: -84.552176+0.005350j
[2025-09-03 15:59:15] [Iter 165/450] R1[14/300], Temp: 0.9946, Energy: -84.677599+0.003346j
[2025-09-03 15:59:46] [Iter 166/450] R1[15/300], Temp: 0.9938, Energy: -84.727401+0.008168j
[2025-09-03 16:00:17] [Iter 167/450] R1[16/300], Temp: 0.9930, Energy: -84.597371-0.001512j
[2025-09-03 16:00:48] [Iter 168/450] R1[17/300], Temp: 0.9921, Energy: -84.521823-0.004834j
[2025-09-03 16:01:19] [Iter 169/450] R1[18/300], Temp: 0.9911, Energy: -84.493156+0.002042j
[2025-09-03 16:01:49] [Iter 170/450] R1[19/300], Temp: 0.9901, Energy: -84.494912+0.006974j
[2025-09-03 16:02:20] [Iter 171/450] R1[20/300], Temp: 0.9891, Energy: -84.528804+0.000191j
[2025-09-03 16:02:51] [Iter 172/450] R1[21/300], Temp: 0.9880, Energy: -84.393583+0.002266j
[2025-09-03 16:03:22] [Iter 173/450] R1[22/300], Temp: 0.9868, Energy: -84.397166-0.003544j
[2025-09-03 16:03:53] [Iter 174/450] R1[23/300], Temp: 0.9856, Energy: -84.474531-0.000725j
[2025-09-03 16:04:24] [Iter 175/450] R1[24/300], Temp: 0.9843, Energy: -84.490217-0.002265j
[2025-09-03 16:04:55] [Iter 176/450] R1[25/300], Temp: 0.9830, Energy: -84.397336+0.008331j
[2025-09-03 16:05:26] [Iter 177/450] R1[26/300], Temp: 0.9816, Energy: -84.316691-0.002519j
[2025-09-03 16:05:57] [Iter 178/450] R1[27/300], Temp: 0.9801, Energy: -84.364545-0.001497j
[2025-09-03 16:06:28] [Iter 179/450] R1[28/300], Temp: 0.9787, Energy: -84.417954+0.006620j
[2025-09-03 16:06:59] [Iter 180/450] R1[29/300], Temp: 0.9771, Energy: -84.514966-0.005614j
[2025-09-03 16:07:30] [Iter 181/450] R1[30/300], Temp: 0.9755, Energy: -84.574203+0.005437j
[2025-09-03 16:08:01] [Iter 182/450] R1[31/300], Temp: 0.9739, Energy: -84.486419+0.000223j
[2025-09-03 16:08:32] [Iter 183/450] R1[32/300], Temp: 0.9722, Energy: -84.354702-0.001683j
[2025-09-03 16:09:02] [Iter 184/450] R1[33/300], Temp: 0.9704, Energy: -84.451411-0.006729j
[2025-09-03 16:09:33] [Iter 185/450] R1[34/300], Temp: 0.9686, Energy: -84.365086-0.000809j
[2025-09-03 16:10:04] [Iter 186/450] R1[35/300], Temp: 0.9668, Energy: -84.391255-0.000228j
[2025-09-03 16:10:35] [Iter 187/450] R1[36/300], Temp: 0.9649, Energy: -84.291611-0.001109j
[2025-09-03 16:11:06] [Iter 188/450] R1[37/300], Temp: 0.9629, Energy: -84.341064-0.007717j
[2025-09-03 16:11:37] [Iter 189/450] R1[38/300], Temp: 0.9609, Energy: -84.336606+0.001715j
[2025-09-03 16:12:08] [Iter 190/450] R1[39/300], Temp: 0.9589, Energy: -84.251629-0.003258j
[2025-09-03 16:12:39] [Iter 191/450] R1[40/300], Temp: 0.9568, Energy: -84.248839+0.002669j
[2025-09-03 16:13:10] [Iter 192/450] R1[41/300], Temp: 0.9546, Energy: -84.271722-0.000740j
[2025-09-03 16:13:41] [Iter 193/450] R1[42/300], Temp: 0.9524, Energy: -84.256195-0.002574j
[2025-09-03 16:14:12] [Iter 194/450] R1[43/300], Temp: 0.9502, Energy: -84.342630+0.003712j
[2025-09-03 16:14:43] [Iter 195/450] R1[44/300], Temp: 0.9479, Energy: -84.339089-0.008369j
[2025-09-03 16:15:14] [Iter 196/450] R1[45/300], Temp: 0.9455, Energy: -84.332937-0.003234j
[2025-09-03 16:15:45] [Iter 197/450] R1[46/300], Temp: 0.9431, Energy: -84.298282+0.001908j
[2025-09-03 16:16:16] [Iter 198/450] R1[47/300], Temp: 0.9407, Energy: -84.192797-0.009281j
[2025-09-03 16:16:47] [Iter 199/450] R1[48/300], Temp: 0.9382, Energy: -84.297304-0.001165j
[2025-09-03 16:17:18] [Iter 200/450] R1[49/300], Temp: 0.9356, Energy: -84.316744-0.002155j
[2025-09-03 16:17:18] ✓ Checkpoint saved: checkpoint_iter_000200.pkl
[2025-09-03 16:17:49] [Iter 201/450] R1[50/300], Temp: 0.9330, Energy: -84.267972+0.004151j
[2025-09-03 16:18:20] [Iter 202/450] R1[51/300], Temp: 0.9304, Energy: -84.466349+0.002371j
[2025-09-03 16:18:51] [Iter 203/450] R1[52/300], Temp: 0.9277, Energy: -84.349019+0.007232j
[2025-09-03 16:19:22] [Iter 204/450] R1[53/300], Temp: 0.9249, Energy: -84.325414-0.002809j
[2025-09-03 16:19:53] [Iter 205/450] R1[54/300], Temp: 0.9222, Energy: -84.278568+0.000709j
[2025-09-03 16:20:24] [Iter 206/450] R1[55/300], Temp: 0.9193, Energy: -84.342673-0.005111j
[2025-09-03 16:20:55] [Iter 207/450] R1[56/300], Temp: 0.9165, Energy: -84.426454-0.002575j
[2025-09-03 16:21:26] [Iter 208/450] R1[57/300], Temp: 0.9135, Energy: -84.521667+0.000673j
[2025-09-03 16:21:57] [Iter 209/450] R1[58/300], Temp: 0.9106, Energy: -84.388186+0.001715j
[2025-09-03 16:22:28] [Iter 210/450] R1[59/300], Temp: 0.9076, Energy: -84.380446+0.004112j
[2025-09-03 16:22:59] [Iter 211/450] R1[60/300], Temp: 0.9045, Energy: -84.323345-0.006121j
[2025-09-03 16:23:30] [Iter 212/450] R1[61/300], Temp: 0.9014, Energy: -84.327840-0.003489j
[2025-09-03 16:24:01] [Iter 213/450] R1[62/300], Temp: 0.8983, Energy: -84.407047+0.003963j
[2025-09-03 16:24:32] [Iter 214/450] R1[63/300], Temp: 0.8951, Energy: -84.430539+0.004137j
[2025-09-03 16:25:03] [Iter 215/450] R1[64/300], Temp: 0.8918, Energy: -84.343949+0.002444j
[2025-09-03 16:25:33] [Iter 216/450] R1[65/300], Temp: 0.8886, Energy: -84.322314-0.000740j
[2025-09-03 16:26:04] [Iter 217/450] R1[66/300], Temp: 0.8853, Energy: -84.467178+0.000334j
[2025-09-03 16:26:35] [Iter 218/450] R1[67/300], Temp: 0.8819, Energy: -84.429059-0.003603j
[2025-09-03 16:27:06] [Iter 219/450] R1[68/300], Temp: 0.8785, Energy: -84.505822+0.000616j
[2025-09-03 16:27:37] [Iter 220/450] R1[69/300], Temp: 0.8751, Energy: -84.490593-0.005471j
[2025-09-03 16:28:09] [Iter 221/450] R1[70/300], Temp: 0.8716, Energy: -84.408426+0.008440j
[2025-09-03 16:28:39] [Iter 222/450] R1[71/300], Temp: 0.8680, Energy: -84.328279+0.002308j
[2025-09-03 16:29:10] [Iter 223/450] R1[72/300], Temp: 0.8645, Energy: -84.313281+0.001656j
[2025-09-03 16:29:41] [Iter 224/450] R1[73/300], Temp: 0.8609, Energy: -84.476483+0.004273j
[2025-09-03 16:30:13] [Iter 225/450] R1[74/300], Temp: 0.8572, Energy: -84.427339+0.001206j
[2025-09-03 16:30:44] [Iter 226/450] R1[75/300], Temp: 0.8536, Energy: -84.462218+0.001876j
[2025-09-03 16:31:14] [Iter 227/450] R1[76/300], Temp: 0.8498, Energy: -84.319152-0.007998j
[2025-09-03 16:31:45] [Iter 228/450] R1[77/300], Temp: 0.8461, Energy: -84.463140-0.003479j
[2025-09-03 16:32:16] [Iter 229/450] R1[78/300], Temp: 0.8423, Energy: -84.320809-0.001503j
[2025-09-03 16:32:47] [Iter 230/450] R1[79/300], Temp: 0.8384, Energy: -84.317023-0.000904j
[2025-09-03 16:33:18] [Iter 231/450] R1[80/300], Temp: 0.8346, Energy: -84.355783+0.001821j
[2025-09-03 16:33:49] [Iter 232/450] R1[81/300], Temp: 0.8307, Energy: -84.369367-0.000698j
[2025-09-03 16:34:20] [Iter 233/450] R1[82/300], Temp: 0.8267, Energy: -84.288574-0.000388j
[2025-09-03 16:34:51] [Iter 234/450] R1[83/300], Temp: 0.8227, Energy: -84.303711-0.002516j
[2025-09-03 16:35:22] [Iter 235/450] R1[84/300], Temp: 0.8187, Energy: -84.398581+0.004272j
[2025-09-03 16:35:53] [Iter 236/450] R1[85/300], Temp: 0.8147, Energy: -84.488689-0.000372j
[2025-09-03 16:36:24] [Iter 237/450] R1[86/300], Temp: 0.8106, Energy: -84.339703-0.001749j
[2025-09-03 16:36:55] [Iter 238/450] R1[87/300], Temp: 0.8065, Energy: -84.424524-0.006287j
[2025-09-03 16:37:26] [Iter 239/450] R1[88/300], Temp: 0.8023, Energy: -84.475279+0.002921j
[2025-09-03 16:37:57] [Iter 240/450] R1[89/300], Temp: 0.7981, Energy: -84.348093-0.004570j
[2025-09-03 16:38:28] [Iter 241/450] R1[90/300], Temp: 0.7939, Energy: -84.396523+0.002197j
[2025-09-03 16:38:59] [Iter 242/450] R1[91/300], Temp: 0.7896, Energy: -84.180899-0.001804j
[2025-09-03 16:39:30] [Iter 243/450] R1[92/300], Temp: 0.7854, Energy: -84.340914-0.003156j
[2025-09-03 16:40:01] [Iter 244/450] R1[93/300], Temp: 0.7810, Energy: -84.402678+0.000300j
[2025-09-03 16:40:32] [Iter 245/450] R1[94/300], Temp: 0.7767, Energy: -84.281109+0.001179j
[2025-09-03 16:41:03] [Iter 246/450] R1[95/300], Temp: 0.7723, Energy: -84.362698-0.004313j
[2025-09-03 16:41:34] [Iter 247/450] R1[96/300], Temp: 0.7679, Energy: -84.232885-0.004693j
[2025-09-03 16:42:05] [Iter 248/450] R1[97/300], Temp: 0.7635, Energy: -84.282412+0.002442j
[2025-09-03 16:42:36] [Iter 249/450] R1[98/300], Temp: 0.7590, Energy: -84.277290-0.012100j
[2025-09-03 16:43:07] [Iter 250/450] R1[99/300], Temp: 0.7545, Energy: -84.385877+0.002879j
[2025-09-03 16:43:07] ✓ Checkpoint saved: checkpoint_iter_000250.pkl
[2025-09-03 16:43:38] [Iter 251/450] R1[100/300], Temp: 0.7500, Energy: -84.526507+0.000639j
[2025-09-03 16:44:09] [Iter 252/450] R1[101/300], Temp: 0.7455, Energy: -84.471201-0.002452j
[2025-09-03 16:44:40] [Iter 253/450] R1[102/300], Temp: 0.7409, Energy: -84.425260-0.000655j
[2025-09-03 16:45:11] [Iter 254/450] R1[103/300], Temp: 0.7363, Energy: -84.564258+0.001293j
[2025-09-03 16:45:42] [Iter 255/450] R1[104/300], Temp: 0.7316, Energy: -84.532728-0.000131j
[2025-09-03 16:46:13] [Iter 256/450] R1[105/300], Temp: 0.7270, Energy: -84.425542-0.002845j
[2025-09-03 16:46:44] [Iter 257/450] R1[106/300], Temp: 0.7223, Energy: -84.525476-0.005962j
[2025-09-03 16:47:15] [Iter 258/450] R1[107/300], Temp: 0.7176, Energy: -84.498964+0.002049j
[2025-09-03 16:47:46] [Iter 259/450] R1[108/300], Temp: 0.7129, Energy: -84.414112-0.002190j
[2025-09-03 16:48:17] [Iter 260/450] R1[109/300], Temp: 0.7081, Energy: -84.267241-0.000076j
[2025-09-03 16:48:48] [Iter 261/450] R1[110/300], Temp: 0.7034, Energy: -84.329667-0.000980j
[2025-09-03 16:49:19] [Iter 262/450] R1[111/300], Temp: 0.6986, Energy: -84.389740-0.000027j
