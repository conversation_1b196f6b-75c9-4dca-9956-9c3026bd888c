[2025-08-23 23:52:37] ==================================================
[2025-08-23 23:52:37] GCNN for Shastry-Sutherland Model
[2025-08-23 23:52:37] ==================================================
[2025-08-23 23:52:37] System parameters:
[2025-08-23 23:52:37]   - System size: L=4, N=64
[2025-08-23 23:52:37]   - System parameters: J1=0.8, J2=1.0, Q=0.0
[2025-08-23 23:52:37] --------------------------------------------------
[2025-08-23 23:52:37] Model parameters:
[2025-08-23 23:52:37]   - Number of layers = 4
[2025-08-23 23:52:37]   - Number of features = 4
[2025-08-23 23:52:37]   - Total parameters = 12572
[2025-08-23 23:52:37] --------------------------------------------------
[2025-08-23 23:52:37] Training parameters:
[2025-08-23 23:52:37]   - Learning rate: 0.015
[2025-08-23 23:52:37]   - Total iterations: 4650
[2025-08-23 23:52:37]   - Annealing cycles: 5
[2025-08-23 23:52:37]   - Initial period: 150
[2025-08-23 23:52:37]   - Period multiplier: 2.0
[2025-08-23 23:52:37]   - Temperature range: 0.0-1.0
[2025-08-23 23:52:37]   - Samples: 16384
[2025-08-23 23:52:37]   - Discarded samples: 0
[2025-08-23 23:52:37]   - Chunk size: 2048
[2025-08-23 23:52:37]   - Diagonal shift: 0.2
[2025-08-23 23:52:37]   - Gradient clipping: 1.0
[2025-08-23 23:52:37]   - Checkpoint enabled: interval=500
[2025-08-23 23:52:37]   - Checkpoint directory: results/L=4/J2=1.00/J1=0.80/training/checkpoints
[2025-08-23 23:52:37] --------------------------------------------------
[2025-08-23 23:52:37] Device status:
[2025-08-23 23:52:37]   - Devices model: NVIDIA H200 NVL
[2025-08-23 23:52:37]   - Number of devices: 1
[2025-08-23 23:52:37]   - Sharding: True
[2025-08-23 23:52:37] ============================================================
[2025-08-23 23:53:15] [Iter 1/4650] R0[0/150], Temp: 1.0000, Energy: 33.588915-0.000147j
[2025-08-23 23:53:25] [Iter 2/4650] R0[1/150], Temp: 0.9999, Energy: 33.588063+0.000481j
[2025-08-23 23:53:35] [Iter 3/4650] R0[2/150], Temp: 0.9996, Energy: 33.588795+0.001748j
[2025-08-23 23:53:45] [Iter 4/4650] R0[3/150], Temp: 0.9990, Energy: 33.582970+0.000470j
[2025-08-23 23:53:55] [Iter 5/4650] R0[4/150], Temp: 0.9982, Energy: 33.578712+0.000238j
[2025-08-23 23:54:05] [Iter 6/4650] R0[5/150], Temp: 0.9973, Energy: 33.570185-0.001346j
[2025-08-23 23:54:15] [Iter 7/4650] R0[6/150], Temp: 0.9961, Energy: 33.565133-0.001359j
[2025-08-23 23:54:25] [Iter 8/4650] R0[7/150], Temp: 0.9946, Energy: 33.546264+0.000755j
[2025-08-23 23:54:36] [Iter 9/4650] R0[8/150], Temp: 0.9930, Energy: 33.529736+0.000355j
[2025-08-23 23:54:46] [Iter 10/4650] R0[9/150], Temp: 0.9911, Energy: 33.518410-0.001342j
[2025-08-23 23:54:56] [Iter 11/4650] R0[10/150], Temp: 0.9891, Energy: 33.479180+0.000828j
[2025-08-23 23:55:06] [Iter 12/4650] R0[11/150], Temp: 0.9868, Energy: 33.436772-0.005063j
[2025-08-23 23:55:16] [Iter 13/4650] R0[12/150], Temp: 0.9843, Energy: 33.381528-0.003837j
[2025-08-23 23:55:26] [Iter 14/4650] R0[13/150], Temp: 0.9816, Energy: 33.270887+0.004581j
[2025-08-23 23:55:37] [Iter 15/4650] R0[14/150], Temp: 0.9787, Energy: 33.136466-0.003636j
[2025-08-23 23:55:47] [Iter 16/4650] R0[15/150], Temp: 0.9755, Energy: 32.917403+0.012968j
[2025-08-23 23:55:57] [Iter 17/4650] R0[16/150], Temp: 0.9722, Energy: 32.581708-0.014660j
[2025-08-23 23:56:07] [Iter 18/4650] R0[17/150], Temp: 0.9686, Energy: 32.089225-0.008469j
[2025-08-23 23:56:17] [Iter 19/4650] R0[18/150], Temp: 0.9649, Energy: 31.301213-0.041001j
[2025-08-23 23:56:27] [Iter 20/4650] R0[19/150], Temp: 0.9609, Energy: 30.072818+0.007719j
[2025-08-23 23:56:37] [Iter 21/4650] R0[20/150], Temp: 0.9568, Energy: 28.216670+0.042770j
[2025-08-23 23:56:48] [Iter 22/4650] R0[21/150], Temp: 0.9524, Energy: 25.414520-0.052555j
[2025-08-23 23:56:58] [Iter 23/4650] R0[22/150], Temp: 0.9479, Energy: 21.983688-0.003834j
[2025-08-23 23:57:08] [Iter 24/4650] R0[23/150], Temp: 0.9431, Energy: 18.129914+0.008249j
[2025-08-23 23:57:18] [Iter 25/4650] R0[24/150], Temp: 0.9382, Energy: 14.706094+0.009462j
[2025-08-23 23:57:28] [Iter 26/4650] R0[25/150], Temp: 0.9330, Energy: 11.722322-0.008275j
[2025-08-23 23:57:38] [Iter 27/4650] R0[26/150], Temp: 0.9277, Energy: 9.088222-0.005468j
[2025-08-23 23:57:49] [Iter 28/4650] R0[27/150], Temp: 0.9222, Energy: 6.719761+0.042828j
[2025-08-23 23:57:59] [Iter 29/4650] R0[28/150], Temp: 0.9165, Energy: 4.681608-0.048745j
[2025-08-23 23:58:09] [Iter 30/4650] R0[29/150], Temp: 0.9106, Energy: 2.665924-0.000943j
[2025-08-23 23:58:19] [Iter 31/4650] R0[30/150], Temp: 0.9045, Energy: 0.951772+0.054325j
[2025-08-23 23:58:29] [Iter 32/4650] R0[31/150], Temp: 0.8983, Energy: -0.694850-0.028742j
[2025-08-23 23:58:39] [Iter 33/4650] R0[32/150], Temp: 0.8918, Energy: -2.119828+0.045634j
[2025-08-23 23:58:50] [Iter 34/4650] R0[33/150], Temp: 0.8853, Energy: -3.516554-0.006226j
[2025-08-23 23:59:00] [Iter 35/4650] R0[34/150], Temp: 0.8785, Energy: -4.773893+0.000862j
[2025-08-23 23:59:10] [Iter 36/4650] R0[35/150], Temp: 0.8716, Energy: -5.897033+0.048492j
[2025-08-23 23:59:20] [Iter 37/4650] R0[36/150], Temp: 0.8645, Energy: -7.017515+0.009997j
[2025-08-23 23:59:30] [Iter 38/4650] R0[37/150], Temp: 0.8572, Energy: -7.935343+0.017382j
[2025-08-23 23:59:40] [Iter 39/4650] R0[38/150], Temp: 0.8498, Energy: -8.872955+0.000967j
[2025-08-23 23:59:50] [Iter 40/4650] R0[39/150], Temp: 0.8423, Energy: -9.735491+0.021038j
[2025-08-24 00:00:01] [Iter 41/4650] R0[40/150], Temp: 0.8346, Energy: -10.570586-0.008512j
[2025-08-24 00:00:11] [Iter 42/4650] R0[41/150], Temp: 0.8267, Energy: -11.245884+0.016269j
[2025-08-24 00:00:21] [Iter 43/4650] R0[42/150], Temp: 0.8187, Energy: -11.972187+0.012305j
[2025-08-24 00:00:31] [Iter 44/4650] R0[43/150], Temp: 0.8106, Energy: -12.608639+0.001396j
[2025-08-24 00:00:41] [Iter 45/4650] R0[44/150], Temp: 0.8023, Energy: -13.238092+0.043023j
[2025-08-24 00:00:51] [Iter 46/4650] R0[45/150], Temp: 0.7939, Energy: -13.856416+0.000304j
[2025-08-24 00:01:02] [Iter 47/4650] R0[46/150], Temp: 0.7854, Energy: -14.322906+0.008697j
[2025-08-24 00:01:12] [Iter 48/4650] R0[47/150], Temp: 0.7767, Energy: -14.840042+0.010574j
[2025-08-24 00:01:22] [Iter 49/4650] R0[48/150], Temp: 0.7679, Energy: -15.275335-0.017486j
[2025-08-24 00:01:32] [Iter 50/4650] R0[49/150], Temp: 0.7590, Energy: -15.745091-0.040427j
[2025-08-24 00:01:42] [Iter 51/4650] R0[50/150], Temp: 0.7500, Energy: -16.106996+0.032075j
[2025-08-24 00:01:52] [Iter 52/4650] R0[51/150], Temp: 0.7409, Energy: -16.472940-0.009838j
[2025-08-24 00:02:02] [Iter 53/4650] R0[52/150], Temp: 0.7316, Energy: -16.814103+0.035690j
[2025-08-24 00:02:13] [Iter 54/4650] R0[53/150], Temp: 0.7223, Energy: -17.169423-0.025594j
[2025-08-24 00:02:23] [Iter 55/4650] R0[54/150], Temp: 0.7129, Energy: -17.451620+0.020408j
[2025-08-24 00:02:33] [Iter 56/4650] R0[55/150], Temp: 0.7034, Energy: -17.729196-0.009898j
[2025-08-24 00:02:43] [Iter 57/4650] R0[56/150], Temp: 0.6938, Energy: -17.992950+0.008797j
[2025-08-24 00:02:53] [Iter 58/4650] R0[57/150], Temp: 0.6841, Energy: -18.246032-0.010601j
[2025-08-24 00:03:03] [Iter 59/4650] R0[58/150], Temp: 0.6743, Energy: -18.493698+0.032513j
[2025-08-24 00:03:14] [Iter 60/4650] R0[59/150], Temp: 0.6644, Energy: -18.657571-0.030491j
[2025-08-24 00:03:24] [Iter 61/4650] R0[60/150], Temp: 0.6545, Energy: -18.883544+0.008934j
[2025-08-24 00:03:34] [Iter 62/4650] R0[61/150], Temp: 0.6445, Energy: -19.068954+0.030678j
[2025-08-24 00:03:44] [Iter 63/4650] R0[62/150], Temp: 0.6345, Energy: -19.221256-0.003064j
[2025-08-24 00:03:54] [Iter 64/4650] R0[63/150], Temp: 0.6243, Energy: -19.404834-0.021400j
[2025-08-24 00:04:04] [Iter 65/4650] R0[64/150], Temp: 0.6142, Energy: -19.601818-0.004437j
[2025-08-24 00:04:15] [Iter 66/4650] R0[65/150], Temp: 0.6040, Energy: -19.721234+0.012146j
[2025-08-24 00:04:25] [Iter 67/4650] R0[66/150], Temp: 0.5937, Energy: -19.860485-0.002130j
[2025-08-24 00:04:35] [Iter 68/4650] R0[67/150], Temp: 0.5834, Energy: -20.000761+0.025618j
[2025-08-24 00:04:45] [Iter 69/4650] R0[68/150], Temp: 0.5730, Energy: -20.128347-0.013494j
[2025-08-24 00:04:55] [Iter 70/4650] R0[69/150], Temp: 0.5627, Energy: -20.300138+0.026114j
[2025-08-24 00:05:05] [Iter 71/4650] R0[70/150], Temp: 0.5523, Energy: -20.386943-0.009698j
[2025-08-24 00:05:15] [Iter 72/4650] R0[71/150], Temp: 0.5418, Energy: -20.509218+0.007123j
[2025-08-24 00:05:26] [Iter 73/4650] R0[72/150], Temp: 0.5314, Energy: -20.626704+0.003927j
[2025-08-24 00:05:36] [Iter 74/4650] R0[73/150], Temp: 0.5209, Energy: -20.726293-0.025217j
[2025-08-24 00:05:46] [Iter 75/4650] R0[74/150], Temp: 0.5105, Energy: -20.846148+0.012938j
[2025-08-24 00:05:56] [Iter 76/4650] R0[75/150], Temp: 0.5000, Energy: -20.922135+0.012917j
[2025-08-24 00:06:06] [Iter 77/4650] R0[76/150], Temp: 0.4895, Energy: -21.008662+0.001054j
[2025-08-24 00:06:16] [Iter 78/4650] R0[77/150], Temp: 0.4791, Energy: -21.125418+0.000118j
[2025-08-24 00:06:27] [Iter 79/4650] R0[78/150], Temp: 0.4686, Energy: -21.220590-0.014309j
[2025-08-24 00:06:37] [Iter 80/4650] R0[79/150], Temp: 0.4582, Energy: -21.286361+0.004379j
[2025-08-24 00:06:47] [Iter 81/4650] R0[80/150], Temp: 0.4477, Energy: -21.400624-0.009470j
[2025-08-24 00:06:57] [Iter 82/4650] R0[81/150], Temp: 0.4373, Energy: -21.458899-0.013752j
[2025-08-24 00:07:07] [Iter 83/4650] R0[82/150], Temp: 0.4270, Energy: -21.549547-0.000371j
[2025-08-24 00:07:17] [Iter 84/4650] R0[83/150], Temp: 0.4166, Energy: -21.616611-0.000984j
[2025-08-24 00:07:28] [Iter 85/4650] R0[84/150], Temp: 0.4063, Energy: -21.696769-0.020966j
[2025-08-24 00:07:38] [Iter 86/4650] R0[85/150], Temp: 0.3960, Energy: -21.762806-0.021434j
[2025-08-24 00:07:48] [Iter 87/4650] R0[86/150], Temp: 0.3858, Energy: -21.802709-0.016515j
[2025-08-24 00:07:58] [Iter 88/4650] R0[87/150], Temp: 0.3757, Energy: -21.905421+0.020636j
[2025-08-24 00:08:08] [Iter 89/4650] R0[88/150], Temp: 0.3655, Energy: -21.966271+0.002808j
[2025-08-24 00:08:18] [Iter 90/4650] R0[89/150], Temp: 0.3555, Energy: -22.058068+0.002930j
[2025-08-24 00:08:28] [Iter 91/4650] R0[90/150], Temp: 0.3455, Energy: -22.113454-0.012797j
[2025-08-24 00:08:39] [Iter 92/4650] R0[91/150], Temp: 0.3356, Energy: -22.183335-0.009078j
[2025-08-24 00:08:49] [Iter 93/4650] R0[92/150], Temp: 0.3257, Energy: -22.224406+0.010719j
[2025-08-24 00:08:59] [Iter 94/4650] R0[93/150], Temp: 0.3159, Energy: -22.286176+0.026070j
[2025-08-24 00:09:09] [Iter 95/4650] R0[94/150], Temp: 0.3062, Energy: -22.368259+0.024394j
[2025-08-24 00:09:19] [Iter 96/4650] R0[95/150], Temp: 0.2966, Energy: -22.419969-0.013998j
[2025-08-24 00:09:29] [Iter 97/4650] R0[96/150], Temp: 0.2871, Energy: -22.483419-0.009825j
[2025-08-24 00:09:40] [Iter 98/4650] R0[97/150], Temp: 0.2777, Energy: -22.541958-0.004058j
[2025-08-24 00:09:50] [Iter 99/4650] R0[98/150], Temp: 0.2684, Energy: -22.579263+0.003529j
[2025-08-24 00:10:00] [Iter 100/4650] R0[99/150], Temp: 0.2591, Energy: -22.697137-0.006543j
[2025-08-24 00:10:10] [Iter 101/4650] R0[100/150], Temp: 0.2500, Energy: -22.730641-0.012365j
[2025-08-24 00:10:20] [Iter 102/4650] R0[101/150], Temp: 0.2410, Energy: -22.768785-0.009469j
[2025-08-24 00:10:30] [Iter 103/4650] R0[102/150], Temp: 0.2321, Energy: -22.853672+0.001292j
[2025-08-24 00:10:40] [Iter 104/4650] R0[103/150], Temp: 0.2233, Energy: -22.926058+0.008790j
[2025-08-24 00:10:51] [Iter 105/4650] R0[104/150], Temp: 0.2146, Energy: -22.949633+0.004681j
[2025-08-24 00:11:01] [Iter 106/4650] R0[105/150], Temp: 0.2061, Energy: -22.991860-0.004404j
[2025-08-24 00:11:11] [Iter 107/4650] R0[106/150], Temp: 0.1977, Energy: -23.075258-0.016295j
[2025-08-24 00:11:21] [Iter 108/4650] R0[107/150], Temp: 0.1894, Energy: -23.115891+0.004595j
[2025-08-24 00:11:31] [Iter 109/4650] R0[108/150], Temp: 0.1813, Energy: -23.167772-0.006575j
[2025-08-24 00:11:41] [Iter 110/4650] R0[109/150], Temp: 0.1733, Energy: -23.225323+0.032825j
[2025-08-24 00:11:52] [Iter 111/4650] R0[110/150], Temp: 0.1654, Energy: -23.274722-0.013633j
[2025-08-24 00:12:02] [Iter 112/4650] R0[111/150], Temp: 0.1577, Energy: -23.332498+0.005977j
[2025-08-24 00:12:12] [Iter 113/4650] R0[112/150], Temp: 0.1502, Energy: -23.386970+0.018869j
[2025-08-24 00:12:22] [Iter 114/4650] R0[113/150], Temp: 0.1428, Energy: -23.427676-0.004716j
[2025-08-24 00:12:32] [Iter 115/4650] R0[114/150], Temp: 0.1355, Energy: -23.458163-0.005360j
[2025-08-24 00:12:42] [Iter 116/4650] R0[115/150], Temp: 0.1284, Energy: -23.551217+0.011602j
[2025-08-24 00:12:52] [Iter 117/4650] R0[116/150], Temp: 0.1215, Energy: -23.584461+0.004692j
[2025-08-24 00:13:03] [Iter 118/4650] R0[117/150], Temp: 0.1147, Energy: -23.665190+0.011296j
[2025-08-24 00:13:13] [Iter 119/4650] R0[118/150], Temp: 0.1082, Energy: -23.701863-0.018191j
[2025-08-24 00:13:23] [Iter 120/4650] R0[119/150], Temp: 0.1017, Energy: -23.721268+0.009581j
[2025-08-24 00:13:33] [Iter 121/4650] R0[120/150], Temp: 0.0955, Energy: -23.816179-0.015213j
[2025-08-24 00:13:43] [Iter 122/4650] R0[121/150], Temp: 0.0894, Energy: -23.839943-0.018854j
[2025-08-24 00:13:53] [Iter 123/4650] R0[122/150], Temp: 0.0835, Energy: -23.870436+0.005747j
[2025-08-24 00:14:03] [Iter 124/4650] R0[123/150], Temp: 0.0778, Energy: -23.956318-0.005520j
[2025-08-24 00:14:14] [Iter 125/4650] R0[124/150], Temp: 0.0723, Energy: -23.999846-0.014325j
[2025-08-24 00:14:24] [Iter 126/4650] R0[125/150], Temp: 0.0670, Energy: -24.046946-0.005577j
[2025-08-24 00:14:34] [Iter 127/4650] R0[126/150], Temp: 0.0618, Energy: -24.106619+0.013052j
[2025-08-24 00:14:44] [Iter 128/4650] R0[127/150], Temp: 0.0569, Energy: -24.155131+0.007834j
[2025-08-24 00:14:54] [Iter 129/4650] R0[128/150], Temp: 0.0521, Energy: -24.191126-0.012872j
[2025-08-24 00:15:04] [Iter 130/4650] R0[129/150], Temp: 0.0476, Energy: -24.238206+0.028989j
[2025-08-24 00:15:15] [Iter 131/4650] R0[130/150], Temp: 0.0432, Energy: -24.275324-0.011593j
[2025-08-24 00:15:25] [Iter 132/4650] R0[131/150], Temp: 0.0391, Energy: -24.339809-0.010450j
[2025-08-24 00:15:35] [Iter 133/4650] R0[132/150], Temp: 0.0351, Energy: -24.408734-0.010528j
[2025-08-24 00:15:45] [Iter 134/4650] R0[133/150], Temp: 0.0314, Energy: -24.451923+0.011741j
[2025-08-24 00:15:55] [Iter 135/4650] R0[134/150], Temp: 0.0278, Energy: -24.509895+0.008048j
[2025-08-24 00:16:05] [Iter 136/4650] R0[135/150], Temp: 0.0245, Energy: -24.558639+0.007938j
[2025-08-24 00:16:15] [Iter 137/4650] R0[136/150], Temp: 0.0213, Energy: -24.595991-0.022312j
[2025-08-24 00:16:26] [Iter 138/4650] R0[137/150], Temp: 0.0184, Energy: -24.664949-0.020582j
[2025-08-24 00:16:36] [Iter 139/4650] R0[138/150], Temp: 0.0157, Energy: -24.743095+0.008974j
[2025-08-24 00:16:46] [Iter 140/4650] R0[139/150], Temp: 0.0132, Energy: -24.766095-0.018000j
[2025-08-24 00:16:56] [Iter 141/4650] R0[140/150], Temp: 0.0109, Energy: -24.831383-0.002193j
[2025-08-24 00:17:06] [Iter 142/4650] R0[141/150], Temp: 0.0089, Energy: -24.862832-0.012387j
[2025-08-24 00:17:16] [Iter 143/4650] R0[142/150], Temp: 0.0070, Energy: -24.938614-0.024799j
[2025-08-24 00:17:27] [Iter 144/4650] R0[143/150], Temp: 0.0054, Energy: -24.955460+0.021231j
[2025-08-24 00:17:37] [Iter 145/4650] R0[144/150], Temp: 0.0039, Energy: -25.024187-0.038069j
[2025-08-24 00:17:47] [Iter 146/4650] R0[145/150], Temp: 0.0027, Energy: -25.081228+0.001004j
[2025-08-24 00:17:57] [Iter 147/4650] R0[146/150], Temp: 0.0018, Energy: -25.125216-0.014774j
[2025-08-24 00:18:07] [Iter 148/4650] R0[147/150], Temp: 0.0010, Energy: -25.192137+0.007926j
[2025-08-24 00:18:17] [Iter 149/4650] R0[148/150], Temp: 0.0004, Energy: -25.226719-0.023545j
[2025-08-24 00:18:27] [Iter 150/4650] R0[149/150], Temp: 0.0001, Energy: -25.305911-0.003837j
[2025-08-24 00:18:27] RESTART #1 | Period: 300
[2025-08-24 00:18:38] [Iter 151/4650] R1[0/300], Temp: 1.0000, Energy: -25.342823+0.000429j
[2025-08-24 00:18:48] [Iter 152/4650] R1[1/300], Temp: 1.0000, Energy: -25.386517+0.017536j
[2025-08-24 00:18:58] [Iter 153/4650] R1[2/300], Temp: 0.9999, Energy: -25.426202-0.020322j
[2025-08-24 00:19:08] [Iter 154/4650] R1[3/300], Temp: 0.9998, Energy: -25.468190+0.004924j
[2025-08-24 00:19:18] [Iter 155/4650] R1[4/300], Temp: 0.9996, Energy: -25.539991-0.023067j
[2025-08-24 00:19:28] [Iter 156/4650] R1[5/300], Temp: 0.9993, Energy: -25.571602-0.015781j
[2025-08-24 00:19:39] [Iter 157/4650] R1[6/300], Temp: 0.9990, Energy: -25.622691+0.006723j
[2025-08-24 00:19:49] [Iter 158/4650] R1[7/300], Temp: 0.9987, Energy: -25.689574-0.006763j
[2025-08-24 00:19:59] [Iter 159/4650] R1[8/300], Temp: 0.9982, Energy: -25.734154+0.006155j
[2025-08-24 00:20:09] [Iter 160/4650] R1[9/300], Temp: 0.9978, Energy: -25.757767+0.007983j
[2025-08-24 00:20:19] [Iter 161/4650] R1[10/300], Temp: 0.9973, Energy: -25.815877+0.003555j
[2025-08-24 00:20:29] [Iter 162/4650] R1[11/300], Temp: 0.9967, Energy: -25.872023-0.001832j
[2025-08-24 00:20:39] [Iter 163/4650] R1[12/300], Temp: 0.9961, Energy: -25.905173+0.016159j
[2025-08-24 00:20:50] [Iter 164/4650] R1[13/300], Temp: 0.9954, Energy: -25.923298-0.013269j
[2025-08-24 00:21:00] [Iter 165/4650] R1[14/300], Temp: 0.9946, Energy: -25.999505+0.001842j
[2025-08-24 00:21:10] [Iter 166/4650] R1[15/300], Temp: 0.9938, Energy: -26.035453+0.001076j
[2025-08-24 00:21:20] [Iter 167/4650] R1[16/300], Temp: 0.9930, Energy: -26.081645+0.001298j
[2025-08-24 00:21:30] [Iter 168/4650] R1[17/300], Temp: 0.9921, Energy: -26.126250-0.000076j
[2025-08-24 00:21:40] [Iter 169/4650] R1[18/300], Temp: 0.9911, Energy: -26.135683-0.008852j
[2025-08-24 00:21:51] [Iter 170/4650] R1[19/300], Temp: 0.9901, Energy: -26.213381-0.012641j
[2025-08-24 00:22:01] [Iter 171/4650] R1[20/300], Temp: 0.9891, Energy: -26.247639+0.011492j
[2025-08-24 00:22:11] [Iter 172/4650] R1[21/300], Temp: 0.9880, Energy: -26.256316+0.011085j
[2025-08-24 00:22:21] [Iter 173/4650] R1[22/300], Temp: 0.9868, Energy: -26.305078+0.011559j
[2025-08-24 00:22:31] [Iter 174/4650] R1[23/300], Temp: 0.9856, Energy: -26.353525-0.006627j
[2025-08-24 00:22:41] [Iter 175/4650] R1[24/300], Temp: 0.9843, Energy: -26.371982-0.009310j
[2025-08-24 00:22:51] [Iter 176/4650] R1[25/300], Temp: 0.9830, Energy: -26.419705-0.004365j
[2025-08-24 00:23:02] [Iter 177/4650] R1[26/300], Temp: 0.9816, Energy: -26.453796-0.000342j
[2025-08-24 00:23:12] [Iter 178/4650] R1[27/300], Temp: 0.9801, Energy: -26.505685-0.008799j
[2025-08-24 00:23:22] [Iter 179/4650] R1[28/300], Temp: 0.9787, Energy: -26.519653+0.006252j
[2025-08-24 00:23:32] [Iter 180/4650] R1[29/300], Temp: 0.9771, Energy: -26.573515-0.006110j
[2025-08-24 00:23:42] [Iter 181/4650] R1[30/300], Temp: 0.9755, Energy: -26.592548-0.000418j
[2025-08-24 00:23:52] [Iter 182/4650] R1[31/300], Temp: 0.9739, Energy: -26.618207+0.007837j
[2025-08-24 00:24:02] [Iter 183/4650] R1[32/300], Temp: 0.9722, Energy: -26.668026+0.007517j
[2025-08-24 00:24:13] [Iter 184/4650] R1[33/300], Temp: 0.9704, Energy: -26.670368+0.004167j
[2025-08-24 00:24:23] [Iter 185/4650] R1[34/300], Temp: 0.9686, Energy: -26.728782-0.013185j
[2025-08-24 00:24:33] [Iter 186/4650] R1[35/300], Temp: 0.9668, Energy: -26.739215-0.016498j
[2025-08-24 00:24:43] [Iter 187/4650] R1[36/300], Temp: 0.9649, Energy: -26.807067+0.007569j
[2025-08-24 00:24:53] [Iter 188/4650] R1[37/300], Temp: 0.9629, Energy: -26.794038+0.003793j
[2025-08-24 00:25:03] [Iter 189/4650] R1[38/300], Temp: 0.9609, Energy: -26.808431-0.005116j
[2025-08-24 00:25:13] [Iter 190/4650] R1[39/300], Temp: 0.9589, Energy: -26.854436-0.004137j
[2025-08-24 00:25:24] [Iter 191/4650] R1[40/300], Temp: 0.9568, Energy: -26.877956-0.009934j
[2025-08-24 00:25:34] [Iter 192/4650] R1[41/300], Temp: 0.9546, Energy: -26.893195-0.005813j
[2025-08-24 00:25:44] [Iter 193/4650] R1[42/300], Temp: 0.9524, Energy: -26.947794-0.013560j
[2025-08-24 00:25:54] [Iter 194/4650] R1[43/300], Temp: 0.9502, Energy: -26.973694+0.008157j
[2025-08-24 00:26:04] [Iter 195/4650] R1[44/300], Temp: 0.9479, Energy: -27.025613+0.019945j
[2025-08-24 00:26:14] [Iter 196/4650] R1[45/300], Temp: 0.9455, Energy: -27.039709-0.008107j
[2025-08-24 00:26:25] [Iter 197/4650] R1[46/300], Temp: 0.9431, Energy: -27.078783-0.012152j
[2025-08-24 00:26:35] [Iter 198/4650] R1[47/300], Temp: 0.9407, Energy: -27.077571-0.003605j
[2025-08-24 00:26:45] [Iter 199/4650] R1[48/300], Temp: 0.9382, Energy: -27.109535-0.006795j
[2025-08-24 00:26:55] [Iter 200/4650] R1[49/300], Temp: 0.9356, Energy: -27.165191-0.003968j
[2025-08-24 00:27:05] [Iter 201/4650] R1[50/300], Temp: 0.9330, Energy: -27.141972-0.005431j
[2025-08-24 00:27:15] [Iter 202/4650] R1[51/300], Temp: 0.9304, Energy: -27.176639-0.001781j
[2025-08-24 00:27:25] [Iter 203/4650] R1[52/300], Temp: 0.9277, Energy: -27.211235+0.006574j
[2025-08-24 00:27:36] [Iter 204/4650] R1[53/300], Temp: 0.9249, Energy: -27.241058-0.007680j
[2025-08-24 00:27:46] [Iter 205/4650] R1[54/300], Temp: 0.9222, Energy: -27.292607-0.009298j
[2025-08-24 00:27:56] [Iter 206/4650] R1[55/300], Temp: 0.9193, Energy: -27.281125+0.009795j
[2025-08-24 00:28:06] [Iter 207/4650] R1[56/300], Temp: 0.9165, Energy: -27.300411+0.007913j
[2025-08-24 00:28:16] [Iter 208/4650] R1[57/300], Temp: 0.9135, Energy: -27.350731-0.002705j
[2025-08-24 00:28:26] [Iter 209/4650] R1[58/300], Temp: 0.9106, Energy: -27.346406-0.020921j
[2025-08-24 00:28:36] [Iter 210/4650] R1[59/300], Temp: 0.9076, Energy: -27.390684-0.006787j
[2025-08-24 00:28:47] [Iter 211/4650] R1[60/300], Temp: 0.9045, Energy: -27.415951+0.007323j
[2025-08-24 00:28:57] [Iter 212/4650] R1[61/300], Temp: 0.9014, Energy: -27.458146-0.012718j
[2025-08-24 00:29:07] [Iter 213/4650] R1[62/300], Temp: 0.8983, Energy: -27.478790+0.005998j
[2025-08-24 00:29:17] [Iter 214/4650] R1[63/300], Temp: 0.8951, Energy: -27.482628-0.001968j
[2025-08-24 00:29:27] [Iter 215/4650] R1[64/300], Temp: 0.8918, Energy: -27.526607-0.005028j
[2025-08-24 00:29:37] [Iter 216/4650] R1[65/300], Temp: 0.8886, Energy: -27.549472+0.002163j
[2025-08-24 00:29:47] [Iter 217/4650] R1[66/300], Temp: 0.8853, Energy: -27.548150-0.000056j
[2025-08-24 00:29:58] [Iter 218/4650] R1[67/300], Temp: 0.8819, Energy: -27.581982+0.012184j
[2025-08-24 00:30:08] [Iter 219/4650] R1[68/300], Temp: 0.8785, Energy: -27.612720-0.016101j
[2025-08-24 00:30:18] [Iter 220/4650] R1[69/300], Temp: 0.8751, Energy: -27.641754-0.005724j
[2025-08-24 00:30:28] [Iter 221/4650] R1[70/300], Temp: 0.8716, Energy: -27.679470-0.002894j
[2025-08-24 00:30:38] [Iter 222/4650] R1[71/300], Temp: 0.8680, Energy: -27.694927+0.010103j
[2025-08-24 00:30:48] [Iter 223/4650] R1[72/300], Temp: 0.8645, Energy: -27.721312-0.008788j
[2025-08-24 00:30:59] [Iter 224/4650] R1[73/300], Temp: 0.8609, Energy: -27.719582-0.007375j
[2025-08-24 00:31:09] [Iter 225/4650] R1[74/300], Temp: 0.8572, Energy: -27.767834-0.008345j
[2025-08-24 00:31:19] [Iter 226/4650] R1[75/300], Temp: 0.8536, Energy: -27.778932+0.005778j
[2025-08-24 00:31:29] [Iter 227/4650] R1[76/300], Temp: 0.8498, Energy: -27.803573+0.011346j
[2025-08-24 00:31:39] [Iter 228/4650] R1[77/300], Temp: 0.8461, Energy: -27.821492-0.009555j
[2025-08-24 00:31:49] [Iter 229/4650] R1[78/300], Temp: 0.8423, Energy: -27.856654-0.003844j
[2025-08-24 00:31:59] [Iter 230/4650] R1[79/300], Temp: 0.8384, Energy: -27.881901-0.002644j
[2025-08-24 00:32:10] [Iter 231/4650] R1[80/300], Temp: 0.8346, Energy: -27.898297-0.006799j
[2025-08-24 00:32:20] [Iter 232/4650] R1[81/300], Temp: 0.8307, Energy: -27.920534-0.005916j
[2025-08-24 00:32:30] [Iter 233/4650] R1[82/300], Temp: 0.8267, Energy: -27.921986-0.002257j
[2025-08-24 00:32:40] [Iter 234/4650] R1[83/300], Temp: 0.8227, Energy: -27.934652-0.024796j
[2025-08-24 00:32:50] [Iter 235/4650] R1[84/300], Temp: 0.8187, Energy: -27.946902-0.009015j
[2025-08-24 00:33:00] [Iter 236/4650] R1[85/300], Temp: 0.8147, Energy: -27.983355+0.002269j
[2025-08-24 00:33:10] [Iter 237/4650] R1[86/300], Temp: 0.8106, Energy: -28.002098-0.001326j
[2025-08-24 00:33:21] [Iter 238/4650] R1[87/300], Temp: 0.8065, Energy: -28.026077-0.010982j
[2025-08-24 00:33:31] [Iter 239/4650] R1[88/300], Temp: 0.8023, Energy: -28.029820+0.005333j
[2025-08-24 00:33:41] [Iter 240/4650] R1[89/300], Temp: 0.7981, Energy: -28.053372-0.002780j
[2025-08-24 00:33:51] [Iter 241/4650] R1[90/300], Temp: 0.7939, Energy: -28.079781-0.016241j
[2025-08-24 00:34:01] [Iter 242/4650] R1[91/300], Temp: 0.7896, Energy: -28.105263-0.007256j
[2025-08-24 00:34:11] [Iter 243/4650] R1[92/300], Temp: 0.7854, Energy: -28.122694-0.007256j
[2025-08-24 00:34:21] [Iter 244/4650] R1[93/300], Temp: 0.7810, Energy: -28.130606-0.000979j
[2025-08-24 00:34:32] [Iter 245/4650] R1[94/300], Temp: 0.7767, Energy: -28.134477+0.009798j
[2025-08-24 00:34:42] [Iter 246/4650] R1[95/300], Temp: 0.7723, Energy: -28.151491-0.011502j
[2025-08-24 00:34:52] [Iter 247/4650] R1[96/300], Temp: 0.7679, Energy: -28.145676-0.010119j
[2025-08-24 00:35:02] [Iter 248/4650] R1[97/300], Temp: 0.7635, Energy: -28.164130+0.001896j
[2025-08-24 00:35:12] [Iter 249/4650] R1[98/300], Temp: 0.7590, Energy: -28.185462-0.006763j
[2025-08-24 00:35:22] [Iter 250/4650] R1[99/300], Temp: 0.7545, Energy: -28.192681-0.000541j
[2025-08-24 00:35:32] [Iter 251/4650] R1[100/300], Temp: 0.7500, Energy: -28.212900+0.007495j
[2025-08-24 00:35:43] [Iter 252/4650] R1[101/300], Temp: 0.7455, Energy: -28.221977-0.006167j
[2025-08-24 00:35:53] [Iter 253/4650] R1[102/300], Temp: 0.7409, Energy: -28.219128+0.003423j
[2025-08-24 00:36:03] [Iter 254/4650] R1[103/300], Temp: 0.7363, Energy: -28.261249-0.005495j
[2025-08-24 00:36:13] [Iter 255/4650] R1[104/300], Temp: 0.7316, Energy: -28.258517-0.004507j
[2025-08-24 00:36:23] [Iter 256/4650] R1[105/300], Temp: 0.7270, Energy: -28.269314+0.004858j
[2025-08-24 00:36:33] [Iter 257/4650] R1[106/300], Temp: 0.7223, Energy: -28.265583-0.007938j
[2025-08-24 00:36:43] [Iter 258/4650] R1[107/300], Temp: 0.7176, Energy: -28.275665-0.004506j
[2025-08-24 00:36:54] [Iter 259/4650] R1[108/300], Temp: 0.7129, Energy: -28.287163-0.007037j
[2025-08-24 00:37:04] [Iter 260/4650] R1[109/300], Temp: 0.7081, Energy: -28.308722-0.002164j
[2025-08-24 00:37:14] [Iter 261/4650] R1[110/300], Temp: 0.7034, Energy: -28.324971-0.007200j
[2025-08-24 00:37:24] [Iter 262/4650] R1[111/300], Temp: 0.6986, Energy: -28.328417-0.000208j
[2025-08-24 00:37:34] [Iter 263/4650] R1[112/300], Temp: 0.6938, Energy: -28.335683+0.010038j
[2025-08-24 00:37:44] [Iter 264/4650] R1[113/300], Temp: 0.6889, Energy: -28.335067+0.000544j
[2025-08-24 00:37:54] [Iter 265/4650] R1[114/300], Temp: 0.6841, Energy: -28.345957-0.004192j
[2025-08-24 00:38:05] [Iter 266/4650] R1[115/300], Temp: 0.6792, Energy: -28.359823+0.009137j
[2025-08-24 00:38:15] [Iter 267/4650] R1[116/300], Temp: 0.6743, Energy: -28.354102+0.003509j
[2025-08-24 00:38:25] [Iter 268/4650] R1[117/300], Temp: 0.6694, Energy: -28.373634-0.006073j
[2025-08-24 00:38:35] [Iter 269/4650] R1[118/300], Temp: 0.6644, Energy: -28.367914-0.009071j
[2025-08-24 00:38:45] [Iter 270/4650] R1[119/300], Temp: 0.6595, Energy: -28.387720-0.011155j
[2025-08-24 00:38:55] [Iter 271/4650] R1[120/300], Temp: 0.6545, Energy: -28.399763-0.006174j
[2025-08-24 00:39:05] [Iter 272/4650] R1[121/300], Temp: 0.6495, Energy: -28.396668-0.003967j
[2025-08-24 00:39:16] [Iter 273/4650] R1[122/300], Temp: 0.6445, Energy: -28.417489-0.003502j
[2025-08-24 00:39:26] [Iter 274/4650] R1[123/300], Temp: 0.6395, Energy: -28.398531-0.002576j
[2025-08-24 00:39:36] [Iter 275/4650] R1[124/300], Temp: 0.6345, Energy: -28.420812-0.005138j
[2025-08-24 00:39:46] [Iter 276/4650] R1[125/300], Temp: 0.6294, Energy: -28.418884+0.006243j
[2025-08-24 00:39:56] [Iter 277/4650] R1[126/300], Temp: 0.6243, Energy: -28.418424-0.001276j
[2025-08-24 00:40:06] [Iter 278/4650] R1[127/300], Temp: 0.6193, Energy: -28.430114-0.003477j
[2025-08-24 00:40:16] [Iter 279/4650] R1[128/300], Temp: 0.6142, Energy: -28.435884-0.007858j
[2025-08-24 00:40:27] [Iter 280/4650] R1[129/300], Temp: 0.6091, Energy: -28.427903+0.002126j
[2025-08-24 00:40:37] [Iter 281/4650] R1[130/300], Temp: 0.6040, Energy: -28.444996-0.003436j
[2025-08-24 00:40:47] [Iter 282/4650] R1[131/300], Temp: 0.5988, Energy: -28.445235+0.001568j
[2025-08-24 00:40:57] [Iter 283/4650] R1[132/300], Temp: 0.5937, Energy: -28.449566-0.000372j
[2025-08-24 00:41:07] [Iter 284/4650] R1[133/300], Temp: 0.5885, Energy: -28.464519+0.005451j
[2025-08-24 00:41:17] [Iter 285/4650] R1[134/300], Temp: 0.5834, Energy: -28.459350+0.000335j
[2025-08-24 00:41:28] [Iter 286/4650] R1[135/300], Temp: 0.5782, Energy: -28.467636+0.009502j
[2025-08-24 00:41:38] [Iter 287/4650] R1[136/300], Temp: 0.5730, Energy: -28.472973-0.003720j
[2025-08-24 00:41:48] [Iter 288/4650] R1[137/300], Temp: 0.5679, Energy: -28.468169-0.006890j
[2025-08-24 00:41:58] [Iter 289/4650] R1[138/300], Temp: 0.5627, Energy: -28.472043+0.003762j
[2025-08-24 00:42:08] [Iter 290/4650] R1[139/300], Temp: 0.5575, Energy: -28.492465-0.002423j
[2025-08-24 00:42:18] [Iter 291/4650] R1[140/300], Temp: 0.5523, Energy: -28.481011+0.007262j
[2025-08-24 00:42:28] [Iter 292/4650] R1[141/300], Temp: 0.5471, Energy: -28.477252-0.002440j
[2025-08-24 00:42:38] [Iter 293/4650] R1[142/300], Temp: 0.5418, Energy: -28.482191-0.001176j
[2025-08-24 00:42:49] [Iter 294/4650] R1[143/300], Temp: 0.5366, Energy: -28.499902-0.000802j
[2025-08-24 00:42:59] [Iter 295/4650] R1[144/300], Temp: 0.5314, Energy: -28.481639-0.003866j
[2025-08-24 00:43:09] [Iter 296/4650] R1[145/300], Temp: 0.5262, Energy: -28.507102-0.004162j
[2025-08-24 00:43:19] [Iter 297/4650] R1[146/300], Temp: 0.5209, Energy: -28.519822-0.002392j
[2025-08-24 00:43:29] [Iter 298/4650] R1[147/300], Temp: 0.5157, Energy: -28.511978+0.007659j
[2025-08-24 00:43:39] [Iter 299/4650] R1[148/300], Temp: 0.5105, Energy: -28.514438+0.003617j
[2025-08-24 00:43:49] [Iter 300/4650] R1[149/300], Temp: 0.5052, Energy: -28.505638-0.000875j
[2025-08-24 00:44:00] [Iter 301/4650] R1[150/300], Temp: 0.5000, Energy: -28.499285+0.007151j
[2025-08-24 00:44:10] [Iter 302/4650] R1[151/300], Temp: 0.4948, Energy: -28.510670-0.001785j
[2025-08-24 00:44:20] [Iter 303/4650] R1[152/300], Temp: 0.4895, Energy: -28.511215+0.000645j
[2025-08-24 00:44:30] [Iter 304/4650] R1[153/300], Temp: 0.4843, Energy: -28.526914-0.004525j
[2025-08-24 00:44:40] [Iter 305/4650] R1[154/300], Temp: 0.4791, Energy: -28.525379+0.001531j
[2025-08-24 00:44:50] [Iter 306/4650] R1[155/300], Temp: 0.4738, Energy: -28.508869-0.005938j
[2025-08-24 00:45:00] [Iter 307/4650] R1[156/300], Temp: 0.4686, Energy: -28.520487+0.005959j
[2025-08-24 00:45:11] [Iter 308/4650] R1[157/300], Temp: 0.4634, Energy: -28.525315+0.002660j
[2025-08-24 00:45:21] [Iter 309/4650] R1[158/300], Temp: 0.4582, Energy: -28.530902-0.002179j
[2025-08-24 00:45:31] [Iter 310/4650] R1[159/300], Temp: 0.4529, Energy: -28.535196+0.001380j
[2025-08-24 00:45:41] [Iter 311/4650] R1[160/300], Temp: 0.4477, Energy: -28.517161-0.001000j
[2025-08-24 00:45:51] [Iter 312/4650] R1[161/300], Temp: 0.4425, Energy: -28.540105-0.004912j
[2025-08-24 00:46:01] [Iter 313/4650] R1[162/300], Temp: 0.4373, Energy: -28.547199+0.002564j
[2025-08-24 00:46:11] [Iter 314/4650] R1[163/300], Temp: 0.4321, Energy: -28.549674-0.002943j
[2025-08-24 00:46:22] [Iter 315/4650] R1[164/300], Temp: 0.4270, Energy: -28.536196-0.000692j
[2025-08-24 00:46:32] [Iter 316/4650] R1[165/300], Temp: 0.4218, Energy: -28.549556-0.003952j
[2025-08-24 00:46:42] [Iter 317/4650] R1[166/300], Temp: 0.4166, Energy: -28.549132+0.011632j
[2025-08-24 00:46:52] [Iter 318/4650] R1[167/300], Temp: 0.4115, Energy: -28.536281+0.001394j
[2025-08-24 00:47:02] [Iter 319/4650] R1[168/300], Temp: 0.4063, Energy: -28.539592+0.002356j
[2025-08-24 00:47:12] [Iter 320/4650] R1[169/300], Temp: 0.4012, Energy: -28.537643+0.008942j
[2025-08-24 00:47:23] [Iter 321/4650] R1[170/300], Temp: 0.3960, Energy: -28.561319-0.002015j
[2025-08-24 00:47:33] [Iter 322/4650] R1[171/300], Temp: 0.3909, Energy: -28.557898-0.002620j
[2025-08-24 00:47:43] [Iter 323/4650] R1[172/300], Temp: 0.3858, Energy: -28.548660-0.001463j
[2025-08-24 00:47:53] [Iter 324/4650] R1[173/300], Temp: 0.3807, Energy: -28.557616+0.002824j
[2025-08-24 00:48:03] [Iter 325/4650] R1[174/300], Temp: 0.3757, Energy: -28.552362+0.001551j
[2025-08-24 00:48:13] [Iter 326/4650] R1[175/300], Temp: 0.3706, Energy: -28.556740+0.003570j
[2025-08-24 00:48:23] [Iter 327/4650] R1[176/300], Temp: 0.3655, Energy: -28.564927-0.004585j
[2025-08-24 00:48:34] [Iter 328/4650] R1[177/300], Temp: 0.3605, Energy: -28.560071-0.002714j
[2025-08-24 00:48:44] [Iter 329/4650] R1[178/300], Temp: 0.3555, Energy: -28.566185+0.001620j
[2025-08-24 00:48:54] [Iter 330/4650] R1[179/300], Temp: 0.3505, Energy: -28.572154-0.002594j
[2025-08-24 00:49:04] [Iter 331/4650] R1[180/300], Temp: 0.3455, Energy: -28.569168-0.002750j
[2025-08-24 00:49:14] [Iter 332/4650] R1[181/300], Temp: 0.3405, Energy: -28.551160+0.001351j
[2025-08-24 00:49:24] [Iter 333/4650] R1[182/300], Temp: 0.3356, Energy: -28.558268+0.000117j
[2025-08-24 00:49:34] [Iter 334/4650] R1[183/300], Temp: 0.3306, Energy: -28.578574+0.000221j
[2025-08-24 00:49:44] [Iter 335/4650] R1[184/300], Temp: 0.3257, Energy: -28.572495-0.001157j
[2025-08-24 00:49:55] [Iter 336/4650] R1[185/300], Temp: 0.3208, Energy: -28.582393-0.005516j
[2025-08-24 00:50:05] [Iter 337/4650] R1[186/300], Temp: 0.3159, Energy: -28.574703+0.008357j
[2025-08-24 00:50:15] [Iter 338/4650] R1[187/300], Temp: 0.3111, Energy: -28.582942-0.004590j
[2025-08-24 00:50:25] [Iter 339/4650] R1[188/300], Temp: 0.3062, Energy: -28.577151-0.004617j
[2025-08-24 00:50:35] [Iter 340/4650] R1[189/300], Temp: 0.3014, Energy: -28.588207-0.002299j
[2025-08-24 00:50:45] [Iter 341/4650] R1[190/300], Temp: 0.2966, Energy: -28.577470+0.002094j
[2025-08-24 00:50:56] [Iter 342/4650] R1[191/300], Temp: 0.2919, Energy: -28.572673-0.000192j
[2025-08-24 00:51:06] [Iter 343/4650] R1[192/300], Temp: 0.2871, Energy: -28.574228-0.000152j
[2025-08-24 00:51:16] [Iter 344/4650] R1[193/300], Temp: 0.2824, Energy: -28.572822+0.003099j
[2025-08-24 00:51:26] [Iter 345/4650] R1[194/300], Temp: 0.2777, Energy: -28.580873-0.000923j
[2025-08-24 00:51:36] [Iter 346/4650] R1[195/300], Temp: 0.2730, Energy: -28.576410+0.005144j
[2025-08-24 00:51:46] [Iter 347/4650] R1[196/300], Temp: 0.2684, Energy: -28.589331+0.002628j
[2025-08-24 00:51:56] [Iter 348/4650] R1[197/300], Temp: 0.2637, Energy: -28.589431+0.003348j
[2025-08-24 00:52:07] [Iter 349/4650] R1[198/300], Temp: 0.2591, Energy: -28.581219+0.001761j
[2025-08-24 00:52:17] [Iter 350/4650] R1[199/300], Temp: 0.2545, Energy: -28.591976-0.006568j
[2025-08-24 00:52:27] [Iter 351/4650] R1[200/300], Temp: 0.2500, Energy: -28.595339-0.006589j
[2025-08-24 00:52:37] [Iter 352/4650] R1[201/300], Temp: 0.2455, Energy: -28.593122-0.001869j
[2025-08-24 00:52:47] [Iter 353/4650] R1[202/300], Temp: 0.2410, Energy: -28.588307-0.002562j
[2025-08-24 00:52:57] [Iter 354/4650] R1[203/300], Temp: 0.2365, Energy: -28.596582+0.002831j
[2025-08-24 00:53:07] [Iter 355/4650] R1[204/300], Temp: 0.2321, Energy: -28.589918+0.012020j
[2025-08-24 00:53:18] [Iter 356/4650] R1[205/300], Temp: 0.2277, Energy: -28.596225+0.003370j
[2025-08-24 00:53:28] [Iter 357/4650] R1[206/300], Temp: 0.2233, Energy: -28.599268-0.004917j
[2025-08-24 00:53:38] [Iter 358/4650] R1[207/300], Temp: 0.2190, Energy: -28.601223-0.000539j
[2025-08-24 00:53:48] [Iter 359/4650] R1[208/300], Temp: 0.2146, Energy: -28.605861-0.002111j
[2025-08-24 00:53:58] [Iter 360/4650] R1[209/300], Temp: 0.2104, Energy: -28.601937-0.000179j
[2025-08-24 00:54:08] [Iter 361/4650] R1[210/300], Temp: 0.2061, Energy: -28.601153+0.002908j
[2025-08-24 00:54:18] [Iter 362/4650] R1[211/300], Temp: 0.2019, Energy: -28.594407+0.002781j
[2025-08-24 00:54:29] [Iter 363/4650] R1[212/300], Temp: 0.1977, Energy: -28.600077+0.005467j
[2025-08-24 00:54:39] [Iter 364/4650] R1[213/300], Temp: 0.1935, Energy: -28.597188+0.002966j
[2025-08-24 00:54:49] [Iter 365/4650] R1[214/300], Temp: 0.1894, Energy: -28.598523+0.006480j
[2025-08-24 00:54:59] [Iter 366/4650] R1[215/300], Temp: 0.1853, Energy: -28.614905+0.001299j
[2025-08-24 00:55:09] [Iter 367/4650] R1[216/300], Temp: 0.1813, Energy: -28.604032+0.001789j
[2025-08-24 00:55:19] [Iter 368/4650] R1[217/300], Temp: 0.1773, Energy: -28.612162+0.008442j
[2025-08-24 00:55:29] [Iter 369/4650] R1[218/300], Temp: 0.1733, Energy: -28.601426+0.001624j
[2025-08-24 00:55:40] [Iter 370/4650] R1[219/300], Temp: 0.1693, Energy: -28.604845+0.000864j
[2025-08-24 00:55:50] [Iter 371/4650] R1[220/300], Temp: 0.1654, Energy: -28.601296-0.009427j
[2025-08-24 00:56:00] [Iter 372/4650] R1[221/300], Temp: 0.1616, Energy: -28.613333-0.005221j
[2025-08-24 00:56:10] [Iter 373/4650] R1[222/300], Temp: 0.1577, Energy: -28.615070-0.002841j
[2025-08-24 00:56:20] [Iter 374/4650] R1[223/300], Temp: 0.1539, Energy: -28.610538-0.005384j
[2025-08-24 00:56:30] [Iter 375/4650] R1[224/300], Temp: 0.1502, Energy: -28.622615-0.001338j
[2025-08-24 00:56:40] [Iter 376/4650] R1[225/300], Temp: 0.1464, Energy: -28.608233+0.002452j
[2025-08-24 00:56:51] [Iter 377/4650] R1[226/300], Temp: 0.1428, Energy: -28.612288-0.003021j
[2025-08-24 00:57:01] [Iter 378/4650] R1[227/300], Temp: 0.1391, Energy: -28.604105+0.001640j
[2025-08-24 00:57:11] [Iter 379/4650] R1[228/300], Temp: 0.1355, Energy: -28.609923-0.000788j
[2025-08-24 00:57:21] [Iter 380/4650] R1[229/300], Temp: 0.1320, Energy: -28.613433-0.004219j
[2025-08-24 00:57:31] [Iter 381/4650] R1[230/300], Temp: 0.1284, Energy: -28.622973-0.007295j
[2025-08-24 00:57:41] [Iter 382/4650] R1[231/300], Temp: 0.1249, Energy: -28.609524-0.002902j
[2025-08-24 00:57:51] [Iter 383/4650] R1[232/300], Temp: 0.1215, Energy: -28.612637+0.009407j
[2025-08-24 00:58:02] [Iter 384/4650] R1[233/300], Temp: 0.1181, Energy: -28.617218-0.002490j
[2025-08-24 00:58:12] [Iter 385/4650] R1[234/300], Temp: 0.1147, Energy: -28.612642+0.001158j
[2025-08-24 00:58:22] [Iter 386/4650] R1[235/300], Temp: 0.1114, Energy: -28.617378-0.000352j
[2025-08-24 00:58:32] [Iter 387/4650] R1[236/300], Temp: 0.1082, Energy: -28.620215-0.002312j
[2025-08-24 00:58:42] [Iter 388/4650] R1[237/300], Temp: 0.1049, Energy: -28.615355+0.000266j
[2025-08-24 00:58:52] [Iter 389/4650] R1[238/300], Temp: 0.1017, Energy: -28.611137+0.004478j
[2025-08-24 00:59:02] [Iter 390/4650] R1[239/300], Temp: 0.0986, Energy: -28.624682+0.001392j
[2025-08-24 00:59:13] [Iter 391/4650] R1[240/300], Temp: 0.0955, Energy: -28.625582-0.004265j
[2025-08-24 00:59:23] [Iter 392/4650] R1[241/300], Temp: 0.0924, Energy: -28.622854-0.001497j
[2025-08-24 00:59:33] [Iter 393/4650] R1[242/300], Temp: 0.0894, Energy: -28.623875+0.000449j
[2025-08-24 00:59:43] [Iter 394/4650] R1[243/300], Temp: 0.0865, Energy: -28.616703+0.001570j
[2025-08-24 00:59:53] [Iter 395/4650] R1[244/300], Temp: 0.0835, Energy: -28.620825+0.004159j
[2025-08-24 01:00:03] [Iter 396/4650] R1[245/300], Temp: 0.0807, Energy: -28.619241+0.003991j
[2025-08-24 01:00:13] [Iter 397/4650] R1[246/300], Temp: 0.0778, Energy: -28.616152+0.001591j
[2025-08-24 01:00:24] [Iter 398/4650] R1[247/300], Temp: 0.0751, Energy: -28.619975-0.005137j
[2025-08-24 01:00:34] [Iter 399/4650] R1[248/300], Temp: 0.0723, Energy: -28.625418+0.000416j
[2025-08-24 01:00:44] [Iter 400/4650] R1[249/300], Temp: 0.0696, Energy: -28.621801-0.005124j
[2025-08-24 01:00:54] [Iter 401/4650] R1[250/300], Temp: 0.0670, Energy: -28.636447-0.002257j
[2025-08-24 01:01:04] [Iter 402/4650] R1[251/300], Temp: 0.0644, Energy: -28.631624-0.004878j
[2025-08-24 01:01:14] [Iter 403/4650] R1[252/300], Temp: 0.0618, Energy: -28.624924+0.000197j
[2025-08-24 01:01:24] [Iter 404/4650] R1[253/300], Temp: 0.0593, Energy: -28.625655-0.007347j
[2025-08-24 01:01:35] [Iter 405/4650] R1[254/300], Temp: 0.0569, Energy: -28.623317+0.005957j
[2025-08-24 01:01:45] [Iter 406/4650] R1[255/300], Temp: 0.0545, Energy: -28.628337-0.003021j
[2025-08-24 01:01:55] [Iter 407/4650] R1[256/300], Temp: 0.0521, Energy: -28.632008-0.002663j
[2025-08-24 01:02:05] [Iter 408/4650] R1[257/300], Temp: 0.0498, Energy: -28.623118+0.001151j
[2025-08-24 01:02:15] [Iter 409/4650] R1[258/300], Temp: 0.0476, Energy: -28.625062-0.000396j
[2025-08-24 01:02:25] [Iter 410/4650] R1[259/300], Temp: 0.0454, Energy: -28.631137-0.002819j
[2025-08-24 01:02:35] [Iter 411/4650] R1[260/300], Temp: 0.0432, Energy: -28.625029+0.001478j
[2025-08-24 01:02:46] [Iter 412/4650] R1[261/300], Temp: 0.0411, Energy: -28.632287-0.004004j
[2025-08-24 01:02:56] [Iter 413/4650] R1[262/300], Temp: 0.0391, Energy: -28.635179-0.000778j
[2025-08-24 01:03:06] [Iter 414/4650] R1[263/300], Temp: 0.0371, Energy: -28.620977+0.002467j
[2025-08-24 01:03:16] [Iter 415/4650] R1[264/300], Temp: 0.0351, Energy: -28.638422+0.000483j
[2025-08-24 01:03:26] [Iter 416/4650] R1[265/300], Temp: 0.0332, Energy: -28.631077+0.002616j
[2025-08-24 01:03:36] [Iter 417/4650] R1[266/300], Temp: 0.0314, Energy: -28.633848-0.004635j
[2025-08-24 01:03:46] [Iter 418/4650] R1[267/300], Temp: 0.0296, Energy: -28.633351+0.003264j
[2025-08-24 01:03:57] [Iter 419/4650] R1[268/300], Temp: 0.0278, Energy: -28.624253+0.001652j
[2025-08-24 01:04:07] [Iter 420/4650] R1[269/300], Temp: 0.0261, Energy: -28.634126-0.000248j
[2025-08-24 01:04:17] [Iter 421/4650] R1[270/300], Temp: 0.0245, Energy: -28.628768-0.002419j
[2025-08-24 01:04:27] [Iter 422/4650] R1[271/300], Temp: 0.0229, Energy: -28.635002+0.000748j
[2025-08-24 01:04:37] [Iter 423/4650] R1[272/300], Temp: 0.0213, Energy: -28.636082+0.002878j
[2025-08-24 01:04:47] [Iter 424/4650] R1[273/300], Temp: 0.0199, Energy: -28.635335-0.000491j
[2025-08-24 01:04:57] [Iter 425/4650] R1[274/300], Temp: 0.0184, Energy: -28.631799+0.002038j
[2025-08-24 01:05:08] [Iter 426/4650] R1[275/300], Temp: 0.0170, Energy: -28.634779+0.000053j
[2025-08-24 01:05:18] [Iter 427/4650] R1[276/300], Temp: 0.0157, Energy: -28.637925+0.002885j
[2025-08-24 01:05:28] [Iter 428/4650] R1[277/300], Temp: 0.0144, Energy: -28.634394+0.001433j
[2025-08-24 01:05:38] [Iter 429/4650] R1[278/300], Temp: 0.0132, Energy: -28.642826-0.000218j
[2025-08-24 01:05:48] [Iter 430/4650] R1[279/300], Temp: 0.0120, Energy: -28.638709-0.005577j
[2025-08-24 01:05:58] [Iter 431/4650] R1[280/300], Temp: 0.0109, Energy: -28.639287-0.008267j
[2025-08-24 01:06:08] [Iter 432/4650] R1[281/300], Temp: 0.0099, Energy: -28.646607-0.010402j
[2025-08-24 01:06:19] [Iter 433/4650] R1[282/300], Temp: 0.0089, Energy: -28.636032+0.002893j
[2025-08-24 01:06:29] [Iter 434/4650] R1[283/300], Temp: 0.0079, Energy: -28.637358+0.004192j
[2025-08-24 01:06:39] [Iter 435/4650] R1[284/300], Temp: 0.0070, Energy: -28.633838-0.002899j
[2025-08-24 01:06:49] [Iter 436/4650] R1[285/300], Temp: 0.0062, Energy: -28.638120+0.001761j
[2025-08-24 01:06:59] [Iter 437/4650] R1[286/300], Temp: 0.0054, Energy: -28.645574+0.004892j
[2025-08-24 01:07:09] [Iter 438/4650] R1[287/300], Temp: 0.0046, Energy: -28.633953-0.002373j
[2025-08-24 01:07:19] [Iter 439/4650] R1[288/300], Temp: 0.0039, Energy: -28.644979-0.003430j
[2025-08-24 01:07:30] [Iter 440/4650] R1[289/300], Temp: 0.0033, Energy: -28.646563-0.007518j
[2025-08-24 01:07:40] [Iter 441/4650] R1[290/300], Temp: 0.0027, Energy: -28.640127+0.000626j
[2025-08-24 01:07:50] [Iter 442/4650] R1[291/300], Temp: 0.0022, Energy: -28.646827-0.012729j
[2025-08-24 01:08:00] [Iter 443/4650] R1[292/300], Temp: 0.0018, Energy: -28.651704+0.001268j
[2025-08-24 01:08:10] [Iter 444/4650] R1[293/300], Temp: 0.0013, Energy: -28.638478+0.002638j
[2025-08-24 01:08:20] [Iter 445/4650] R1[294/300], Temp: 0.0010, Energy: -28.640136-0.005050j
[2025-08-24 01:08:30] [Iter 446/4650] R1[295/300], Temp: 0.0007, Energy: -28.633745+0.000857j
[2025-08-24 01:08:41] [Iter 447/4650] R1[296/300], Temp: 0.0004, Energy: -28.636494-0.002281j
[2025-08-24 01:08:51] [Iter 448/4650] R1[297/300], Temp: 0.0002, Energy: -28.638267+0.006597j
[2025-08-24 01:09:01] [Iter 449/4650] R1[298/300], Temp: 0.0001, Energy: -28.644103+0.007225j
[2025-08-24 01:09:11] [Iter 450/4650] R1[299/300], Temp: 0.0000, Energy: -28.647150-0.002000j
[2025-08-24 01:09:11] RESTART #2 | Period: 600
[2025-08-24 01:09:21] [Iter 451/4650] R2[0/600], Temp: 1.0000, Energy: -28.654625-0.002409j
[2025-08-24 01:09:31] [Iter 452/4650] R2[1/600], Temp: 1.0000, Energy: -28.639954-0.003973j
[2025-08-24 01:09:41] [Iter 453/4650] R2[2/600], Temp: 1.0000, Energy: -28.643315-0.000008j
[2025-08-24 01:09:52] [Iter 454/4650] R2[3/600], Temp: 0.9999, Energy: -28.648435-0.001859j
[2025-08-24 01:10:02] [Iter 455/4650] R2[4/600], Temp: 0.9999, Energy: -28.638750-0.008327j
[2025-08-24 01:10:12] [Iter 456/4650] R2[5/600], Temp: 0.9998, Energy: -28.644740-0.000484j
[2025-08-24 01:10:22] [Iter 457/4650] R2[6/600], Temp: 0.9998, Energy: -28.650587+0.002986j
[2025-08-24 01:10:32] [Iter 458/4650] R2[7/600], Temp: 0.9997, Energy: -28.648333+0.002766j
[2025-08-24 01:10:42] [Iter 459/4650] R2[8/600], Temp: 0.9996, Energy: -28.643662-0.002824j
[2025-08-24 01:10:52] [Iter 460/4650] R2[9/600], Temp: 0.9994, Energy: -28.648483+0.001437j
[2025-08-24 01:11:03] [Iter 461/4650] R2[10/600], Temp: 0.9993, Energy: -28.650555-0.001352j
[2025-08-24 01:11:13] [Iter 462/4650] R2[11/600], Temp: 0.9992, Energy: -28.642559-0.000752j
[2025-08-24 01:11:23] [Iter 463/4650] R2[12/600], Temp: 0.9990, Energy: -28.652264-0.006351j
[2025-08-24 01:11:33] [Iter 464/4650] R2[13/600], Temp: 0.9988, Energy: -28.653468+0.002306j
[2025-08-24 01:11:43] [Iter 465/4650] R2[14/600], Temp: 0.9987, Energy: -28.648487-0.001424j
[2025-08-24 01:11:53] [Iter 466/4650] R2[15/600], Temp: 0.9985, Energy: -28.643201+0.001108j
[2025-08-24 01:12:03] [Iter 467/4650] R2[16/600], Temp: 0.9982, Energy: -28.649883-0.005966j
[2025-08-24 01:12:14] [Iter 468/4650] R2[17/600], Temp: 0.9980, Energy: -28.648022-0.001543j
[2025-08-24 01:12:24] [Iter 469/4650] R2[18/600], Temp: 0.9978, Energy: -28.651381+0.001949j
[2025-08-24 01:12:34] [Iter 470/4650] R2[19/600], Temp: 0.9975, Energy: -28.642297+0.002555j
[2025-08-24 01:12:44] [Iter 471/4650] R2[20/600], Temp: 0.9973, Energy: -28.656468-0.002171j
[2025-08-24 01:12:54] [Iter 472/4650] R2[21/600], Temp: 0.9970, Energy: -28.652766-0.003618j
[2025-08-24 01:13:04] [Iter 473/4650] R2[22/600], Temp: 0.9967, Energy: -28.656238+0.005385j
[2025-08-24 01:13:14] [Iter 474/4650] R2[23/600], Temp: 0.9964, Energy: -28.655104-0.000750j
[2025-08-24 01:13:24] [Iter 475/4650] R2[24/600], Temp: 0.9961, Energy: -28.658370+0.000982j
[2025-08-24 01:13:35] [Iter 476/4650] R2[25/600], Temp: 0.9957, Energy: -28.647611+0.003019j
[2025-08-24 01:13:45] [Iter 477/4650] R2[26/600], Temp: 0.9954, Energy: -28.650549-0.000698j
[2025-08-24 01:13:55] [Iter 478/4650] R2[27/600], Temp: 0.9950, Energy: -28.651435-0.002162j
[2025-08-24 01:14:05] [Iter 479/4650] R2[28/600], Temp: 0.9946, Energy: -28.660831+0.004597j
[2025-08-24 01:14:15] [Iter 480/4650] R2[29/600], Temp: 0.9942, Energy: -28.654886-0.004120j
[2025-08-24 01:14:25] [Iter 481/4650] R2[30/600], Temp: 0.9938, Energy: -28.656404+0.003939j
[2025-08-24 01:14:35] [Iter 482/4650] R2[31/600], Temp: 0.9934, Energy: -28.654097-0.000530j
[2025-08-24 01:14:46] [Iter 483/4650] R2[32/600], Temp: 0.9930, Energy: -28.657124-0.003369j
[2025-08-24 01:14:56] [Iter 484/4650] R2[33/600], Temp: 0.9926, Energy: -28.657330-0.002259j
[2025-08-24 01:15:06] [Iter 485/4650] R2[34/600], Temp: 0.9921, Energy: -28.645985+0.004642j
[2025-08-24 01:15:16] [Iter 486/4650] R2[35/600], Temp: 0.9916, Energy: -28.656066-0.006346j
[2025-08-24 01:15:26] [Iter 487/4650] R2[36/600], Temp: 0.9911, Energy: -28.655383+0.003094j
[2025-08-24 01:15:36] [Iter 488/4650] R2[37/600], Temp: 0.9906, Energy: -28.645872+0.000250j
[2025-08-24 01:15:46] [Iter 489/4650] R2[38/600], Temp: 0.9901, Energy: -28.653180+0.001959j
[2025-08-24 01:15:57] [Iter 490/4650] R2[39/600], Temp: 0.9896, Energy: -28.652783-0.004555j
[2025-08-24 01:16:07] [Iter 491/4650] R2[40/600], Temp: 0.9891, Energy: -28.659741-0.002333j
[2025-08-24 01:16:17] [Iter 492/4650] R2[41/600], Temp: 0.9885, Energy: -28.658466+0.003669j
[2025-08-24 01:16:27] [Iter 493/4650] R2[42/600], Temp: 0.9880, Energy: -28.660613-0.002145j
[2025-08-24 01:16:37] [Iter 494/4650] R2[43/600], Temp: 0.9874, Energy: -28.649145+0.000582j
[2025-08-24 01:16:47] [Iter 495/4650] R2[44/600], Temp: 0.9868, Energy: -28.650018-0.000354j
[2025-08-24 01:16:57] [Iter 496/4650] R2[45/600], Temp: 0.9862, Energy: -28.654315-0.003445j
[2025-08-24 01:17:08] [Iter 497/4650] R2[46/600], Temp: 0.9856, Energy: -28.652454+0.001610j
[2025-08-24 01:17:18] [Iter 498/4650] R2[47/600], Temp: 0.9849, Energy: -28.666685-0.002151j
[2025-08-24 01:17:28] [Iter 499/4650] R2[48/600], Temp: 0.9843, Energy: -28.653808+0.000485j
[2025-08-24 01:17:38] [Iter 500/4650] R2[49/600], Temp: 0.9836, Energy: -28.653475-0.001619j
[2025-08-24 01:17:38] ✓ Checkpoint saved: checkpoint_iter_000500.pkl
[2025-08-24 01:17:48] [Iter 501/4650] R2[50/600], Temp: 0.9830, Energy: -28.652568+0.001493j
[2025-08-24 01:17:58] [Iter 502/4650] R2[51/600], Temp: 0.9823, Energy: -28.663101-0.001223j
[2025-08-24 01:18:08] [Iter 503/4650] R2[52/600], Temp: 0.9816, Energy: -28.655945+0.002616j
[2025-08-24 01:18:19] [Iter 504/4650] R2[53/600], Temp: 0.9809, Energy: -28.663640+0.002185j
[2025-08-24 01:18:29] [Iter 505/4650] R2[54/600], Temp: 0.9801, Energy: -28.657552-0.002061j
[2025-08-24 01:18:39] [Iter 506/4650] R2[55/600], Temp: 0.9794, Energy: -28.663354-0.008276j
[2025-08-24 01:18:49] [Iter 507/4650] R2[56/600], Temp: 0.9787, Energy: -28.661111+0.002686j
[2025-08-24 01:18:59] [Iter 508/4650] R2[57/600], Temp: 0.9779, Energy: -28.662912+0.001016j
[2025-08-24 01:19:09] [Iter 509/4650] R2[58/600], Temp: 0.9771, Energy: -28.655990+0.000462j
[2025-08-24 01:19:20] [Iter 510/4650] R2[59/600], Temp: 0.9763, Energy: -28.666340-0.002230j
[2025-08-24 01:19:30] [Iter 511/4650] R2[60/600], Temp: 0.9755, Energy: -28.660666+0.001951j
[2025-08-24 01:19:40] [Iter 512/4650] R2[61/600], Temp: 0.9747, Energy: -28.661093-0.005088j
[2025-08-24 01:19:50] [Iter 513/4650] R2[62/600], Temp: 0.9739, Energy: -28.658159-0.005091j
[2025-08-24 01:20:00] [Iter 514/4650] R2[63/600], Temp: 0.9730, Energy: -28.666794-0.002765j
[2025-08-24 01:20:10] [Iter 515/4650] R2[64/600], Temp: 0.9722, Energy: -28.659960-0.004176j
[2025-08-24 01:20:20] [Iter 516/4650] R2[65/600], Temp: 0.9713, Energy: -28.652976+0.005846j
[2025-08-24 01:20:30] [Iter 517/4650] R2[66/600], Temp: 0.9704, Energy: -28.657484-0.001814j
[2025-08-24 01:20:41] [Iter 518/4650] R2[67/600], Temp: 0.9695, Energy: -28.659810+0.003181j
[2025-08-24 01:20:51] [Iter 519/4650] R2[68/600], Temp: 0.9686, Energy: -28.660275+0.002653j
[2025-08-24 01:21:01] [Iter 520/4650] R2[69/600], Temp: 0.9677, Energy: -28.663142+0.002805j
[2025-08-24 01:21:11] [Iter 521/4650] R2[70/600], Temp: 0.9668, Energy: -28.662232+0.003788j
[2025-08-24 01:21:21] [Iter 522/4650] R2[71/600], Temp: 0.9658, Energy: -28.661656+0.005408j
[2025-08-24 01:21:31] [Iter 523/4650] R2[72/600], Temp: 0.9649, Energy: -28.660945-0.000841j
[2025-08-24 01:21:41] [Iter 524/4650] R2[73/600], Temp: 0.9639, Energy: -28.657188-0.003422j
[2025-08-24 01:21:52] [Iter 525/4650] R2[74/600], Temp: 0.9629, Energy: -28.661608-0.000607j
[2025-08-24 01:22:02] [Iter 526/4650] R2[75/600], Temp: 0.9619, Energy: -28.661410-0.001779j
[2025-08-24 01:22:12] [Iter 527/4650] R2[76/600], Temp: 0.9609, Energy: -28.663667-0.000093j
[2025-08-24 01:22:22] [Iter 528/4650] R2[77/600], Temp: 0.9599, Energy: -28.662496-0.002459j
[2025-08-24 01:22:32] [Iter 529/4650] R2[78/600], Temp: 0.9589, Energy: -28.662369-0.000903j
[2025-08-24 01:22:42] [Iter 530/4650] R2[79/600], Temp: 0.9578, Energy: -28.656240-0.001307j
[2025-08-24 01:22:52] [Iter 531/4650] R2[80/600], Temp: 0.9568, Energy: -28.664497-0.001473j
[2025-08-24 01:23:03] [Iter 532/4650] R2[81/600], Temp: 0.9557, Energy: -28.663236+0.000273j
[2025-08-24 01:23:13] [Iter 533/4650] R2[82/600], Temp: 0.9546, Energy: -28.664787-0.002845j
[2025-08-24 01:23:23] [Iter 534/4650] R2[83/600], Temp: 0.9535, Energy: -28.662678-0.000520j
[2025-08-24 01:23:33] [Iter 535/4650] R2[84/600], Temp: 0.9524, Energy: -28.671623+0.001698j
[2025-08-24 01:23:43] [Iter 536/4650] R2[85/600], Temp: 0.9513, Energy: -28.662995+0.001025j
[2025-08-24 01:23:53] [Iter 537/4650] R2[86/600], Temp: 0.9502, Energy: -28.667934-0.001060j
[2025-08-24 01:24:03] [Iter 538/4650] R2[87/600], Temp: 0.9490, Energy: -28.656869-0.000397j
[2025-08-24 01:24:14] [Iter 539/4650] R2[88/600], Temp: 0.9479, Energy: -28.668124+0.003042j
[2025-08-24 01:24:24] [Iter 540/4650] R2[89/600], Temp: 0.9467, Energy: -28.671146+0.002015j
[2025-08-24 01:24:34] [Iter 541/4650] R2[90/600], Temp: 0.9455, Energy: -28.666847+0.001195j
[2025-08-24 01:24:44] [Iter 542/4650] R2[91/600], Temp: 0.9443, Energy: -28.664678+0.001835j
[2025-08-24 01:24:54] [Iter 543/4650] R2[92/600], Temp: 0.9431, Energy: -28.668569-0.006689j
[2025-08-24 01:25:04] [Iter 544/4650] R2[93/600], Temp: 0.9419, Energy: -28.662251-0.002385j
[2025-08-24 01:25:14] [Iter 545/4650] R2[94/600], Temp: 0.9407, Energy: -28.670275-0.003623j
[2025-08-24 01:25:25] [Iter 546/4650] R2[95/600], Temp: 0.9394, Energy: -28.670094-0.001222j
[2025-08-24 01:25:35] [Iter 547/4650] R2[96/600], Temp: 0.9382, Energy: -28.666742+0.003186j
[2025-08-24 01:25:45] [Iter 548/4650] R2[97/600], Temp: 0.9369, Energy: -28.667176+0.000025j
[2025-08-24 01:25:55] [Iter 549/4650] R2[98/600], Temp: 0.9356, Energy: -28.660481-0.003252j
[2025-08-24 01:26:05] [Iter 550/4650] R2[99/600], Temp: 0.9343, Energy: -28.671514+0.002390j
[2025-08-24 01:26:15] [Iter 551/4650] R2[100/600], Temp: 0.9330, Energy: -28.670159+0.001125j
[2025-08-24 01:26:25] [Iter 552/4650] R2[101/600], Temp: 0.9317, Energy: -28.665565+0.001991j
[2025-08-24 01:26:36] [Iter 553/4650] R2[102/600], Temp: 0.9304, Energy: -28.668210+0.000990j
[2025-08-24 01:26:46] [Iter 554/4650] R2[103/600], Temp: 0.9290, Energy: -28.661766+0.002878j
[2025-08-24 01:26:56] [Iter 555/4650] R2[104/600], Temp: 0.9277, Energy: -28.661252+0.004402j
[2025-08-24 01:27:06] [Iter 556/4650] R2[105/600], Temp: 0.9263, Energy: -28.669630-0.000065j
[2025-08-24 01:27:16] [Iter 557/4650] R2[106/600], Temp: 0.9249, Energy: -28.666125-0.000978j
[2025-08-24 01:27:26] [Iter 558/4650] R2[107/600], Temp: 0.9236, Energy: -28.667973-0.000417j
[2025-08-24 01:27:36] [Iter 559/4650] R2[108/600], Temp: 0.9222, Energy: -28.672619-0.000840j
[2025-08-24 01:27:46] [Iter 560/4650] R2[109/600], Temp: 0.9208, Energy: -28.676164+0.002458j
[2025-08-24 01:27:57] [Iter 561/4650] R2[110/600], Temp: 0.9193, Energy: -28.669559-0.000836j
[2025-08-24 01:28:07] [Iter 562/4650] R2[111/600], Temp: 0.9179, Energy: -28.672905-0.000165j
[2025-08-24 01:28:17] [Iter 563/4650] R2[112/600], Temp: 0.9165, Energy: -28.668882+0.005357j
[2025-08-24 01:28:27] [Iter 564/4650] R2[113/600], Temp: 0.9150, Energy: -28.668869+0.000805j
[2025-08-24 01:28:37] [Iter 565/4650] R2[114/600], Temp: 0.9135, Energy: -28.671257-0.000929j
[2025-08-24 01:28:47] [Iter 566/4650] R2[115/600], Temp: 0.9121, Energy: -28.671015-0.001307j
[2025-08-24 01:28:57] [Iter 567/4650] R2[116/600], Temp: 0.9106, Energy: -28.668117-0.002487j
[2025-08-24 01:29:08] [Iter 568/4650] R2[117/600], Temp: 0.9091, Energy: -28.666327-0.002941j
[2025-08-24 01:29:18] [Iter 569/4650] R2[118/600], Temp: 0.9076, Energy: -28.665769-0.001221j
[2025-08-24 01:29:28] [Iter 570/4650] R2[119/600], Temp: 0.9060, Energy: -28.668448-0.000907j
[2025-08-24 01:29:38] [Iter 571/4650] R2[120/600], Temp: 0.9045, Energy: -28.675982-0.003272j
[2025-08-24 01:29:48] [Iter 572/4650] R2[121/600], Temp: 0.9030, Energy: -28.663504-0.002522j
[2025-08-24 01:29:58] [Iter 573/4650] R2[122/600], Temp: 0.9014, Energy: -28.666165-0.004637j
[2025-08-24 01:30:08] [Iter 574/4650] R2[123/600], Temp: 0.8998, Energy: -28.667587+0.000469j
[2025-08-24 01:30:19] [Iter 575/4650] R2[124/600], Temp: 0.8983, Energy: -28.674840-0.001090j
[2025-08-24 01:30:29] [Iter 576/4650] R2[125/600], Temp: 0.8967, Energy: -28.673861+0.002626j
[2025-08-24 01:30:39] [Iter 577/4650] R2[126/600], Temp: 0.8951, Energy: -28.673168+0.001794j
[2025-08-24 01:30:49] [Iter 578/4650] R2[127/600], Temp: 0.8935, Energy: -28.672734-0.001070j
[2025-08-24 01:30:59] [Iter 579/4650] R2[128/600], Temp: 0.8918, Energy: -28.676799+0.005559j
[2025-08-24 01:31:09] [Iter 580/4650] R2[129/600], Temp: 0.8902, Energy: -28.664784-0.000643j
[2025-08-24 01:31:19] [Iter 581/4650] R2[130/600], Temp: 0.8886, Energy: -28.666558-0.006465j
[2025-08-24 01:31:30] [Iter 582/4650] R2[131/600], Temp: 0.8869, Energy: -28.675195-0.000881j
[2025-08-24 01:31:40] [Iter 583/4650] R2[132/600], Temp: 0.8853, Energy: -28.672716-0.003265j
[2025-08-24 01:31:50] [Iter 584/4650] R2[133/600], Temp: 0.8836, Energy: -28.675453-0.002300j
[2025-08-24 01:32:00] [Iter 585/4650] R2[134/600], Temp: 0.8819, Energy: -28.675475-0.002144j
[2025-08-24 01:32:10] [Iter 586/4650] R2[135/600], Temp: 0.8802, Energy: -28.675556+0.001508j
[2025-08-24 01:32:20] [Iter 587/4650] R2[136/600], Temp: 0.8785, Energy: -28.666156-0.002061j
[2025-08-24 01:32:30] [Iter 588/4650] R2[137/600], Temp: 0.8768, Energy: -28.669426-0.000376j
[2025-08-24 01:32:41] [Iter 589/4650] R2[138/600], Temp: 0.8751, Energy: -28.666676-0.000121j
[2025-08-24 01:32:51] [Iter 590/4650] R2[139/600], Temp: 0.8733, Energy: -28.666505-0.000651j
[2025-08-24 01:33:01] [Iter 591/4650] R2[140/600], Temp: 0.8716, Energy: -28.682517-0.001971j
[2025-08-24 01:33:11] [Iter 592/4650] R2[141/600], Temp: 0.8698, Energy: -28.665770-0.003441j
[2025-08-24 01:33:21] [Iter 593/4650] R2[142/600], Temp: 0.8680, Energy: -28.669707+0.000104j
[2025-08-24 01:33:31] [Iter 594/4650] R2[143/600], Temp: 0.8663, Energy: -28.667412-0.001406j
[2025-08-24 01:33:41] [Iter 595/4650] R2[144/600], Temp: 0.8645, Energy: -28.677936+0.004069j
[2025-08-24 01:33:52] [Iter 596/4650] R2[145/600], Temp: 0.8627, Energy: -28.675969+0.003850j
[2025-08-24 01:34:02] [Iter 597/4650] R2[146/600], Temp: 0.8609, Energy: -28.675773+0.000422j
[2025-08-24 01:34:12] [Iter 598/4650] R2[147/600], Temp: 0.8591, Energy: -28.678976-0.000080j
[2025-08-24 01:34:22] [Iter 599/4650] R2[148/600], Temp: 0.8572, Energy: -28.681775+0.001687j
[2025-08-24 01:34:32] [Iter 600/4650] R2[149/600], Temp: 0.8554, Energy: -28.681422+0.002808j
[2025-08-24 01:34:42] [Iter 601/4650] R2[150/600], Temp: 0.8536, Energy: -28.680289-0.003456j
[2025-08-24 01:34:52] [Iter 602/4650] R2[151/600], Temp: 0.8517, Energy: -28.677587+0.001475j
[2025-08-24 01:35:03] [Iter 603/4650] R2[152/600], Temp: 0.8498, Energy: -28.682130-0.002275j
[2025-08-24 01:35:13] [Iter 604/4650] R2[153/600], Temp: 0.8480, Energy: -28.684146-0.001665j
[2025-08-24 01:35:23] [Iter 605/4650] R2[154/600], Temp: 0.8461, Energy: -28.684366-0.001556j
[2025-08-24 01:35:33] [Iter 606/4650] R2[155/600], Temp: 0.8442, Energy: -28.680906-0.000032j
[2025-08-24 01:35:43] [Iter 607/4650] R2[156/600], Temp: 0.8423, Energy: -28.673091+0.000586j
[2025-08-24 01:35:53] [Iter 608/4650] R2[157/600], Temp: 0.8404, Energy: -28.683076+0.000874j
[2025-08-24 01:36:03] [Iter 609/4650] R2[158/600], Temp: 0.8384, Energy: -28.679148-0.000963j
[2025-08-24 01:36:14] [Iter 610/4650] R2[159/600], Temp: 0.8365, Energy: -28.681382+0.004280j
[2025-08-24 01:36:24] [Iter 611/4650] R2[160/600], Temp: 0.8346, Energy: -28.674057+0.003076j
[2025-08-24 01:36:34] [Iter 612/4650] R2[161/600], Temp: 0.8326, Energy: -28.679879-0.002301j
[2025-08-24 01:36:44] [Iter 613/4650] R2[162/600], Temp: 0.8307, Energy: -28.675833+0.003998j
[2025-08-24 01:36:54] [Iter 614/4650] R2[163/600], Temp: 0.8287, Energy: -28.680135+0.003052j
[2025-08-24 01:37:04] [Iter 615/4650] R2[164/600], Temp: 0.8267, Energy: -28.680301+0.000812j
[2025-08-24 01:37:14] [Iter 616/4650] R2[165/600], Temp: 0.8247, Energy: -28.679226+0.001423j
[2025-08-24 01:37:25] [Iter 617/4650] R2[166/600], Temp: 0.8227, Energy: -28.667086-0.001948j
[2025-08-24 01:37:35] [Iter 618/4650] R2[167/600], Temp: 0.8207, Energy: -28.675733+0.000885j
[2025-08-24 01:37:45] [Iter 619/4650] R2[168/600], Temp: 0.8187, Energy: -28.672352+0.003396j
[2025-08-24 01:37:55] [Iter 620/4650] R2[169/600], Temp: 0.8167, Energy: -28.675876+0.003074j
[2025-08-24 01:38:05] [Iter 621/4650] R2[170/600], Temp: 0.8147, Energy: -28.686103-0.001569j
[2025-08-24 01:38:15] [Iter 622/4650] R2[171/600], Temp: 0.8126, Energy: -28.671854-0.002334j
[2025-08-24 01:38:25] [Iter 623/4650] R2[172/600], Temp: 0.8106, Energy: -28.682914+0.003058j
[2025-08-24 01:38:36] [Iter 624/4650] R2[173/600], Temp: 0.8085, Energy: -28.678731-0.002701j
[2025-08-24 01:38:46] [Iter 625/4650] R2[174/600], Temp: 0.8065, Energy: -28.679855+0.003446j
[2025-08-24 01:38:56] [Iter 626/4650] R2[175/600], Temp: 0.8044, Energy: -28.680793+0.004711j
[2025-08-24 01:39:06] [Iter 627/4650] R2[176/600], Temp: 0.8023, Energy: -28.682741-0.002449j
[2025-08-24 01:39:16] [Iter 628/4650] R2[177/600], Temp: 0.8002, Energy: -28.676448-0.004523j
[2025-08-24 01:39:26] [Iter 629/4650] R2[178/600], Temp: 0.7981, Energy: -28.680382-0.003359j
[2025-08-24 01:39:36] [Iter 630/4650] R2[179/600], Temp: 0.7960, Energy: -28.673782-0.003742j
[2025-08-24 01:39:47] [Iter 631/4650] R2[180/600], Temp: 0.7939, Energy: -28.677886+0.000833j
[2025-08-24 01:39:57] [Iter 632/4650] R2[181/600], Temp: 0.7918, Energy: -28.680053-0.001980j
[2025-08-24 01:40:07] [Iter 633/4650] R2[182/600], Temp: 0.7896, Energy: -28.678362+0.000354j
[2025-08-24 01:40:17] [Iter 634/4650] R2[183/600], Temp: 0.7875, Energy: -28.683234+0.004690j
[2025-08-24 01:40:27] [Iter 635/4650] R2[184/600], Temp: 0.7854, Energy: -28.680764+0.002411j
[2025-08-24 01:40:37] [Iter 636/4650] R2[185/600], Temp: 0.7832, Energy: -28.675878+0.002154j
[2025-08-24 01:40:47] [Iter 637/4650] R2[186/600], Temp: 0.7810, Energy: -28.684962-0.006117j
[2025-08-24 01:40:58] [Iter 638/4650] R2[187/600], Temp: 0.7789, Energy: -28.690355-0.001957j
[2025-08-24 01:41:08] [Iter 639/4650] R2[188/600], Temp: 0.7767, Energy: -28.681154-0.005210j
[2025-08-24 01:41:18] [Iter 640/4650] R2[189/600], Temp: 0.7745, Energy: -28.680555+0.002127j
[2025-08-24 01:41:28] [Iter 641/4650] R2[190/600], Temp: 0.7723, Energy: -28.678389+0.001992j
[2025-08-24 01:41:38] [Iter 642/4650] R2[191/600], Temp: 0.7701, Energy: -28.687610-0.001221j
[2025-08-24 01:41:48] [Iter 643/4650] R2[192/600], Temp: 0.7679, Energy: -28.681343+0.000083j
[2025-08-24 01:41:58] [Iter 644/4650] R2[193/600], Temp: 0.7657, Energy: -28.682768+0.001203j
[2025-08-24 01:42:09] [Iter 645/4650] R2[194/600], Temp: 0.7635, Energy: -28.684000-0.002347j
[2025-08-24 01:42:19] [Iter 646/4650] R2[195/600], Temp: 0.7612, Energy: -28.681162-0.000821j
[2025-08-24 01:42:29] [Iter 647/4650] R2[196/600], Temp: 0.7590, Energy: -28.677058+0.003754j
[2025-08-24 01:42:39] [Iter 648/4650] R2[197/600], Temp: 0.7568, Energy: -28.684659+0.002886j
[2025-08-24 01:42:49] [Iter 649/4650] R2[198/600], Temp: 0.7545, Energy: -28.679688+0.001494j
[2025-08-24 01:42:59] [Iter 650/4650] R2[199/600], Temp: 0.7523, Energy: -28.685676+0.002041j
[2025-08-24 01:43:09] [Iter 651/4650] R2[200/600], Temp: 0.7500, Energy: -28.682435-0.004005j
[2025-08-24 01:43:20] [Iter 652/4650] R2[201/600], Temp: 0.7477, Energy: -28.682412-0.002140j
[2025-08-24 01:43:30] [Iter 653/4650] R2[202/600], Temp: 0.7455, Energy: -28.687236+0.000378j
[2025-08-24 01:43:40] [Iter 654/4650] R2[203/600], Temp: 0.7432, Energy: -28.682729-0.000547j
[2025-08-24 01:43:50] [Iter 655/4650] R2[204/600], Temp: 0.7409, Energy: -28.693423-0.007428j
[2025-08-24 01:44:00] [Iter 656/4650] R2[205/600], Temp: 0.7386, Energy: -28.681681-0.002712j
[2025-08-24 01:44:10] [Iter 657/4650] R2[206/600], Temp: 0.7363, Energy: -28.688214-0.003651j
[2025-08-24 01:44:20] [Iter 658/4650] R2[207/600], Temp: 0.7340, Energy: -28.684303+0.000332j
[2025-08-24 01:44:31] [Iter 659/4650] R2[208/600], Temp: 0.7316, Energy: -28.685172-0.001453j
[2025-08-24 01:44:41] [Iter 660/4650] R2[209/600], Temp: 0.7293, Energy: -28.684927+0.004755j
[2025-08-24 01:44:51] [Iter 661/4650] R2[210/600], Temp: 0.7270, Energy: -28.680232-0.002911j
[2025-08-24 01:45:01] [Iter 662/4650] R2[211/600], Temp: 0.7247, Energy: -28.688117-0.001914j
[2025-08-24 01:45:11] [Iter 663/4650] R2[212/600], Temp: 0.7223, Energy: -28.676283+0.002155j
[2025-08-24 01:45:21] [Iter 664/4650] R2[213/600], Temp: 0.7200, Energy: -28.682334+0.001599j
[2025-08-24 01:45:31] [Iter 665/4650] R2[214/600], Temp: 0.7176, Energy: -28.684545-0.000003j
[2025-08-24 01:45:42] [Iter 666/4650] R2[215/600], Temp: 0.7153, Energy: -28.685470-0.000633j
[2025-08-24 01:45:52] [Iter 667/4650] R2[216/600], Temp: 0.7129, Energy: -28.683573+0.000466j
[2025-08-24 01:46:02] [Iter 668/4650] R2[217/600], Temp: 0.7105, Energy: -28.681682+0.001686j
[2025-08-24 01:46:12] [Iter 669/4650] R2[218/600], Temp: 0.7081, Energy: -28.682228+0.000312j
[2025-08-24 01:46:22] [Iter 670/4650] R2[219/600], Temp: 0.7058, Energy: -28.683427-0.000124j
[2025-08-24 01:46:32] [Iter 671/4650] R2[220/600], Temp: 0.7034, Energy: -28.683156-0.001374j
[2025-08-24 01:46:42] [Iter 672/4650] R2[221/600], Temp: 0.7010, Energy: -28.677847+0.001321j
[2025-08-24 01:46:52] [Iter 673/4650] R2[222/600], Temp: 0.6986, Energy: -28.684679-0.000144j
[2025-08-24 01:47:03] [Iter 674/4650] R2[223/600], Temp: 0.6962, Energy: -28.684731-0.000720j
[2025-08-24 01:47:13] [Iter 675/4650] R2[224/600], Temp: 0.6938, Energy: -28.681161+0.001581j
[2025-08-24 01:47:23] [Iter 676/4650] R2[225/600], Temp: 0.6913, Energy: -28.674268-0.000698j
[2025-08-24 01:47:33] [Iter 677/4650] R2[226/600], Temp: 0.6889, Energy: -28.679430+0.001074j
[2025-08-24 01:47:43] [Iter 678/4650] R2[227/600], Temp: 0.6865, Energy: -28.682450+0.005635j
[2025-08-24 01:47:53] [Iter 679/4650] R2[228/600], Temp: 0.6841, Energy: -28.680967+0.001099j
[2025-08-24 01:48:03] [Iter 680/4650] R2[229/600], Temp: 0.6816, Energy: -28.681680-0.000743j
[2025-08-24 01:48:14] [Iter 681/4650] R2[230/600], Temp: 0.6792, Energy: -28.678659+0.001373j
[2025-08-24 01:48:24] [Iter 682/4650] R2[231/600], Temp: 0.6767, Energy: -28.684983-0.001150j
[2025-08-24 01:48:34] [Iter 683/4650] R2[232/600], Temp: 0.6743, Energy: -28.679837+0.002205j
[2025-08-24 01:48:44] [Iter 684/4650] R2[233/600], Temp: 0.6718, Energy: -28.684318+0.001132j
[2025-08-24 01:48:54] [Iter 685/4650] R2[234/600], Temp: 0.6694, Energy: -28.685836-0.002868j
[2025-08-24 01:49:04] [Iter 686/4650] R2[235/600], Temp: 0.6669, Energy: -28.687322+0.001856j
[2025-08-24 01:49:14] [Iter 687/4650] R2[236/600], Temp: 0.6644, Energy: -28.688105+0.002783j
[2025-08-24 01:49:25] [Iter 688/4650] R2[237/600], Temp: 0.6620, Energy: -28.679630+0.000219j
[2025-08-24 01:49:35] [Iter 689/4650] R2[238/600], Temp: 0.6595, Energy: -28.688367+0.000080j
[2025-08-24 01:49:45] [Iter 690/4650] R2[239/600], Temp: 0.6570, Energy: -28.687464-0.003201j
[2025-08-24 01:49:55] [Iter 691/4650] R2[240/600], Temp: 0.6545, Energy: -28.679055-0.003399j
[2025-08-24 01:50:05] [Iter 692/4650] R2[241/600], Temp: 0.6520, Energy: -28.684203-0.001397j
[2025-08-24 01:50:15] [Iter 693/4650] R2[242/600], Temp: 0.6495, Energy: -28.692056-0.004824j
[2025-08-24 01:50:25] [Iter 694/4650] R2[243/600], Temp: 0.6470, Energy: -28.689051-0.000100j
[2025-08-24 01:50:36] [Iter 695/4650] R2[244/600], Temp: 0.6445, Energy: -28.681930+0.000793j
[2025-08-24 01:50:46] [Iter 696/4650] R2[245/600], Temp: 0.6420, Energy: -28.682262-0.002273j
[2025-08-24 01:50:56] [Iter 697/4650] R2[246/600], Temp: 0.6395, Energy: -28.689224+0.004130j
[2025-08-24 01:51:06] [Iter 698/4650] R2[247/600], Temp: 0.6370, Energy: -28.688645+0.000546j
[2025-08-24 01:51:16] [Iter 699/4650] R2[248/600], Temp: 0.6345, Energy: -28.684749+0.001465j
[2025-08-24 01:51:26] [Iter 700/4650] R2[249/600], Temp: 0.6319, Energy: -28.688768-0.002264j
[2025-08-24 01:51:37] [Iter 701/4650] R2[250/600], Temp: 0.6294, Energy: -28.682527-0.000847j
[2025-08-24 01:51:47] [Iter 702/4650] R2[251/600], Temp: 0.6269, Energy: -28.687448-0.003567j
[2025-08-24 01:51:57] [Iter 703/4650] R2[252/600], Temp: 0.6243, Energy: -28.684863+0.000318j
[2025-08-24 01:52:07] [Iter 704/4650] R2[253/600], Temp: 0.6218, Energy: -28.696858+0.003241j
[2025-08-24 01:52:17] [Iter 705/4650] R2[254/600], Temp: 0.6193, Energy: -28.688429-0.000527j
[2025-08-24 01:52:27] [Iter 706/4650] R2[255/600], Temp: 0.6167, Energy: -28.689983+0.002455j
[2025-08-24 01:52:37] [Iter 707/4650] R2[256/600], Temp: 0.6142, Energy: -28.691623-0.000777j
[2025-08-24 01:52:48] [Iter 708/4650] R2[257/600], Temp: 0.6116, Energy: -28.693066-0.003132j
[2025-08-24 01:52:58] [Iter 709/4650] R2[258/600], Temp: 0.6091, Energy: -28.685937-0.001063j
[2025-08-24 01:53:08] [Iter 710/4650] R2[259/600], Temp: 0.6065, Energy: -28.685429+0.002317j
[2025-08-24 01:53:18] [Iter 711/4650] R2[260/600], Temp: 0.6040, Energy: -28.684394+0.000519j
[2025-08-24 01:53:28] [Iter 712/4650] R2[261/600], Temp: 0.6014, Energy: -28.682873+0.000895j
[2025-08-24 01:53:38] [Iter 713/4650] R2[262/600], Temp: 0.5988, Energy: -28.693774-0.006696j
[2025-08-24 01:53:48] [Iter 714/4650] R2[263/600], Temp: 0.5963, Energy: -28.684549+0.001850j
[2025-08-24 01:53:58] [Iter 715/4650] R2[264/600], Temp: 0.5937, Energy: -28.687899+0.001592j
[2025-08-24 01:54:09] [Iter 716/4650] R2[265/600], Temp: 0.5911, Energy: -28.690781+0.003774j
[2025-08-24 01:54:19] [Iter 717/4650] R2[266/600], Temp: 0.5885, Energy: -28.696168-0.000431j
[2025-08-24 01:54:29] [Iter 718/4650] R2[267/600], Temp: 0.5860, Energy: -28.693458+0.001504j
[2025-08-24 01:54:39] [Iter 719/4650] R2[268/600], Temp: 0.5834, Energy: -28.701428-0.002050j
[2025-08-24 01:54:49] [Iter 720/4650] R2[269/600], Temp: 0.5808, Energy: -28.688401+0.001154j
[2025-08-24 01:54:59] [Iter 721/4650] R2[270/600], Temp: 0.5782, Energy: -28.691743-0.000034j
[2025-08-24 01:55:09] [Iter 722/4650] R2[271/600], Temp: 0.5756, Energy: -28.694203+0.004616j
[2025-08-24 01:55:20] [Iter 723/4650] R2[272/600], Temp: 0.5730, Energy: -28.686824+0.001785j
[2025-08-24 01:55:30] [Iter 724/4650] R2[273/600], Temp: 0.5705, Energy: -28.692240-0.001645j
[2025-08-24 01:55:40] [Iter 725/4650] R2[274/600], Temp: 0.5679, Energy: -28.685182-0.004040j
[2025-08-24 01:55:50] [Iter 726/4650] R2[275/600], Temp: 0.5653, Energy: -28.683342-0.002299j
[2025-08-24 01:56:00] [Iter 727/4650] R2[276/600], Temp: 0.5627, Energy: -28.694429-0.001900j
[2025-08-24 01:56:10] [Iter 728/4650] R2[277/600], Temp: 0.5601, Energy: -28.692903+0.004247j
[2025-08-24 01:56:20] [Iter 729/4650] R2[278/600], Temp: 0.5575, Energy: -28.691890-0.002168j
[2025-08-24 01:56:31] [Iter 730/4650] R2[279/600], Temp: 0.5549, Energy: -28.688475+0.002249j
[2025-08-24 01:56:41] [Iter 731/4650] R2[280/600], Temp: 0.5523, Energy: -28.685760-0.002070j
[2025-08-24 01:56:51] [Iter 732/4650] R2[281/600], Temp: 0.5497, Energy: -28.691128+0.001108j
[2025-08-24 01:57:01] [Iter 733/4650] R2[282/600], Temp: 0.5471, Energy: -28.689392-0.000809j
[2025-08-24 01:57:11] [Iter 734/4650] R2[283/600], Temp: 0.5444, Energy: -28.681867+0.002055j
[2025-08-24 01:57:21] [Iter 735/4650] R2[284/600], Temp: 0.5418, Energy: -28.676117-0.002501j
[2025-08-24 01:57:31] [Iter 736/4650] R2[285/600], Temp: 0.5392, Energy: -28.694968+0.001706j
[2025-08-24 01:57:42] [Iter 737/4650] R2[286/600], Temp: 0.5366, Energy: -28.692411-0.000074j
[2025-08-24 01:57:52] [Iter 738/4650] R2[287/600], Temp: 0.5340, Energy: -28.691077+0.004788j
[2025-08-24 01:58:02] [Iter 739/4650] R2[288/600], Temp: 0.5314, Energy: -28.689466+0.000775j
[2025-08-24 01:58:12] [Iter 740/4650] R2[289/600], Temp: 0.5288, Energy: -28.689045-0.002451j
[2025-08-24 01:58:22] [Iter 741/4650] R2[290/600], Temp: 0.5262, Energy: -28.692394+0.003060j
[2025-08-24 01:58:32] [Iter 742/4650] R2[291/600], Temp: 0.5236, Energy: -28.687350-0.000613j
[2025-08-24 01:58:42] [Iter 743/4650] R2[292/600], Temp: 0.5209, Energy: -28.693956-0.001886j
[2025-08-24 01:58:53] [Iter 744/4650] R2[293/600], Temp: 0.5183, Energy: -28.686702+0.001012j
[2025-08-24 01:59:03] [Iter 745/4650] R2[294/600], Temp: 0.5157, Energy: -28.697810+0.000281j
[2025-08-24 01:59:13] [Iter 746/4650] R2[295/600], Temp: 0.5131, Energy: -28.689190-0.003541j
[2025-08-24 01:59:23] [Iter 747/4650] R2[296/600], Temp: 0.5105, Energy: -28.690576-0.004390j
[2025-08-24 01:59:33] [Iter 748/4650] R2[297/600], Temp: 0.5079, Energy: -28.691503+0.000925j
[2025-08-24 01:59:43] [Iter 749/4650] R2[298/600], Temp: 0.5052, Energy: -28.689025+0.002886j
[2025-08-24 01:59:53] [Iter 750/4650] R2[299/600], Temp: 0.5026, Energy: -28.687660-0.000825j
[2025-08-24 02:00:03] [Iter 751/4650] R2[300/600], Temp: 0.5000, Energy: -28.694355-0.001622j
[2025-08-24 02:00:14] [Iter 752/4650] R2[301/600], Temp: 0.4974, Energy: -28.695709+0.002465j
[2025-08-24 02:00:24] [Iter 753/4650] R2[302/600], Temp: 0.4948, Energy: -28.692001+0.002621j
[2025-08-24 02:00:34] [Iter 754/4650] R2[303/600], Temp: 0.4921, Energy: -28.687932+0.004093j
[2025-08-24 02:00:44] [Iter 755/4650] R2[304/600], Temp: 0.4895, Energy: -28.696961-0.001069j
[2025-08-24 02:00:54] [Iter 756/4650] R2[305/600], Temp: 0.4869, Energy: -28.686960-0.000209j
[2025-08-24 02:01:04] [Iter 757/4650] R2[306/600], Temp: 0.4843, Energy: -28.688240+0.003392j
[2025-08-24 02:01:15] [Iter 758/4650] R2[307/600], Temp: 0.4817, Energy: -28.696147-0.001878j
[2025-08-24 02:01:25] [Iter 759/4650] R2[308/600], Temp: 0.4791, Energy: -28.703192-0.002924j
[2025-08-24 02:01:35] [Iter 760/4650] R2[309/600], Temp: 0.4764, Energy: -28.693840+0.003361j
[2025-08-24 02:01:45] [Iter 761/4650] R2[310/600], Temp: 0.4738, Energy: -28.690563-0.000712j
[2025-08-24 02:01:55] [Iter 762/4650] R2[311/600], Temp: 0.4712, Energy: -28.699090-0.000757j
[2025-08-24 02:02:05] [Iter 763/4650] R2[312/600], Temp: 0.4686, Energy: -28.701057+0.000347j
[2025-08-24 02:02:15] [Iter 764/4650] R2[313/600], Temp: 0.4660, Energy: -28.689101-0.000716j
[2025-08-24 02:02:26] [Iter 765/4650] R2[314/600], Temp: 0.4634, Energy: -28.689196+0.001970j
[2025-08-24 02:02:36] [Iter 766/4650] R2[315/600], Temp: 0.4608, Energy: -28.687600-0.000625j
[2025-08-24 02:02:46] [Iter 767/4650] R2[316/600], Temp: 0.4582, Energy: -28.694445-0.003231j
[2025-08-24 02:02:56] [Iter 768/4650] R2[317/600], Temp: 0.4556, Energy: -28.695557+0.001138j
[2025-08-24 02:03:06] [Iter 769/4650] R2[318/600], Temp: 0.4529, Energy: -28.694273-0.000072j
[2025-08-24 02:03:16] [Iter 770/4650] R2[319/600], Temp: 0.4503, Energy: -28.692063-0.000204j
[2025-08-24 02:03:26] [Iter 771/4650] R2[320/600], Temp: 0.4477, Energy: -28.695031-0.000324j
[2025-08-24 02:03:37] [Iter 772/4650] R2[321/600], Temp: 0.4451, Energy: -28.694396+0.003122j
[2025-08-24 02:03:47] [Iter 773/4650] R2[322/600], Temp: 0.4425, Energy: -28.698238-0.000713j
[2025-08-24 02:03:57] [Iter 774/4650] R2[323/600], Temp: 0.4399, Energy: -28.694098+0.002866j
[2025-08-24 02:04:07] [Iter 775/4650] R2[324/600], Temp: 0.4373, Energy: -28.697324-0.000386j
[2025-08-24 02:04:17] [Iter 776/4650] R2[325/600], Temp: 0.4347, Energy: -28.693068+0.001024j
[2025-08-24 02:04:27] [Iter 777/4650] R2[326/600], Temp: 0.4321, Energy: -28.688147+0.003393j
[2025-08-24 02:04:37] [Iter 778/4650] R2[327/600], Temp: 0.4295, Energy: -28.698599-0.001565j
[2025-08-24 02:04:48] [Iter 779/4650] R2[328/600], Temp: 0.4270, Energy: -28.694017-0.005078j
[2025-08-24 02:04:58] [Iter 780/4650] R2[329/600], Temp: 0.4244, Energy: -28.693268+0.000434j
[2025-08-24 02:05:08] [Iter 781/4650] R2[330/600], Temp: 0.4218, Energy: -28.694940-0.000754j
[2025-08-24 02:05:18] [Iter 782/4650] R2[331/600], Temp: 0.4192, Energy: -28.693304-0.000384j
[2025-08-24 02:05:28] [Iter 783/4650] R2[332/600], Temp: 0.4166, Energy: -28.698318+0.000688j
[2025-08-24 02:05:38] [Iter 784/4650] R2[333/600], Temp: 0.4140, Energy: -28.696437-0.003243j
[2025-08-24 02:05:48] [Iter 785/4650] R2[334/600], Temp: 0.4115, Energy: -28.689720+0.001813j
[2025-08-24 02:05:59] [Iter 786/4650] R2[335/600], Temp: 0.4089, Energy: -28.690987-0.000783j
[2025-08-24 02:06:09] [Iter 787/4650] R2[336/600], Temp: 0.4063, Energy: -28.699017+0.002800j
[2025-08-24 02:06:19] [Iter 788/4650] R2[337/600], Temp: 0.4037, Energy: -28.702321-0.003660j
[2025-08-24 02:06:29] [Iter 789/4650] R2[338/600], Temp: 0.4012, Energy: -28.701854+0.001653j
[2025-08-24 02:06:39] [Iter 790/4650] R2[339/600], Temp: 0.3986, Energy: -28.693543+0.001682j
[2025-08-24 02:06:49] [Iter 791/4650] R2[340/600], Temp: 0.3960, Energy: -28.687784+0.000398j
[2025-08-24 02:06:59] [Iter 792/4650] R2[341/600], Temp: 0.3935, Energy: -28.705574-0.000315j
[2025-08-24 02:07:10] [Iter 793/4650] R2[342/600], Temp: 0.3909, Energy: -28.684341+0.000013j
[2025-08-24 02:07:20] [Iter 794/4650] R2[343/600], Temp: 0.3884, Energy: -28.699809+0.001941j
[2025-08-24 02:07:30] [Iter 795/4650] R2[344/600], Temp: 0.3858, Energy: -28.697626-0.000860j
[2025-08-24 02:07:40] [Iter 796/4650] R2[345/600], Temp: 0.3833, Energy: -28.697903-0.000549j
[2025-08-24 02:07:50] [Iter 797/4650] R2[346/600], Temp: 0.3807, Energy: -28.693537-0.002640j
[2025-08-24 02:08:00] [Iter 798/4650] R2[347/600], Temp: 0.3782, Energy: -28.695619+0.001478j
[2025-08-24 02:08:10] [Iter 799/4650] R2[348/600], Temp: 0.3757, Energy: -28.697853-0.000445j
[2025-08-24 02:08:21] [Iter 800/4650] R2[349/600], Temp: 0.3731, Energy: -28.687322+0.000303j
[2025-08-24 02:08:31] [Iter 801/4650] R2[350/600], Temp: 0.3706, Energy: -28.693653+0.001539j
[2025-08-24 02:08:41] [Iter 802/4650] R2[351/600], Temp: 0.3681, Energy: -28.700167-0.003558j
[2025-08-24 02:08:51] [Iter 803/4650] R2[352/600], Temp: 0.3655, Energy: -28.696013-0.004648j
[2025-08-24 02:09:01] [Iter 804/4650] R2[353/600], Temp: 0.3630, Energy: -28.699252+0.001697j
[2025-08-24 02:09:11] [Iter 805/4650] R2[354/600], Temp: 0.3605, Energy: -28.705215-0.001359j
[2025-08-24 02:09:21] [Iter 806/4650] R2[355/600], Temp: 0.3580, Energy: -28.706131+0.000213j
[2025-08-24 02:09:31] [Iter 807/4650] R2[356/600], Temp: 0.3555, Energy: -28.701932-0.001779j
[2025-08-24 02:09:42] [Iter 808/4650] R2[357/600], Temp: 0.3530, Energy: -28.702652-0.003048j
[2025-08-24 02:09:52] [Iter 809/4650] R2[358/600], Temp: 0.3505, Energy: -28.688693+0.000371j
[2025-08-24 02:10:02] [Iter 810/4650] R2[359/600], Temp: 0.3480, Energy: -28.700542-0.000548j
[2025-08-24 02:10:12] [Iter 811/4650] R2[360/600], Temp: 0.3455, Energy: -28.697207-0.000140j
[2025-08-24 02:10:22] [Iter 812/4650] R2[361/600], Temp: 0.3430, Energy: -28.690329-0.003479j
[2025-08-24 02:10:32] [Iter 813/4650] R2[362/600], Temp: 0.3405, Energy: -28.695500-0.004179j
[2025-08-24 02:10:42] [Iter 814/4650] R2[363/600], Temp: 0.3380, Energy: -28.700823+0.000687j
[2025-08-24 02:10:53] [Iter 815/4650] R2[364/600], Temp: 0.3356, Energy: -28.702942+0.000535j
[2025-08-24 02:11:03] [Iter 816/4650] R2[365/600], Temp: 0.3331, Energy: -28.700692+0.002984j
[2025-08-24 02:11:13] [Iter 817/4650] R2[366/600], Temp: 0.3306, Energy: -28.702143-0.003781j
[2025-08-24 02:11:23] [Iter 818/4650] R2[367/600], Temp: 0.3282, Energy: -28.694838-0.000296j
[2025-08-24 02:11:33] [Iter 819/4650] R2[368/600], Temp: 0.3257, Energy: -28.703265+0.001729j
[2025-08-24 02:11:43] [Iter 820/4650] R2[369/600], Temp: 0.3233, Energy: -28.703378+0.002169j
[2025-08-24 02:11:54] [Iter 821/4650] R2[370/600], Temp: 0.3208, Energy: -28.692477+0.000532j
[2025-08-24 02:12:04] [Iter 822/4650] R2[371/600], Temp: 0.3184, Energy: -28.694995+0.002530j
[2025-08-24 02:12:14] [Iter 823/4650] R2[372/600], Temp: 0.3159, Energy: -28.699204-0.002670j
[2025-08-24 02:12:24] [Iter 824/4650] R2[373/600], Temp: 0.3135, Energy: -28.695303+0.000446j
[2025-08-24 02:12:34] [Iter 825/4650] R2[374/600], Temp: 0.3111, Energy: -28.694879+0.000761j
[2025-08-24 02:12:44] [Iter 826/4650] R2[375/600], Temp: 0.3087, Energy: -28.696055-0.000828j
[2025-08-24 02:12:54] [Iter 827/4650] R2[376/600], Temp: 0.3062, Energy: -28.695407-0.001551j
[2025-08-24 02:13:05] [Iter 828/4650] R2[377/600], Temp: 0.3038, Energy: -28.700033-0.003441j
[2025-08-24 02:13:15] [Iter 829/4650] R2[378/600], Temp: 0.3014, Energy: -28.694576-0.003005j
[2025-08-24 02:13:25] [Iter 830/4650] R2[379/600], Temp: 0.2990, Energy: -28.696030+0.000717j
[2025-08-24 02:13:35] [Iter 831/4650] R2[380/600], Temp: 0.2966, Energy: -28.698682+0.000029j
[2025-08-24 02:13:45] [Iter 832/4650] R2[381/600], Temp: 0.2942, Energy: -28.697160-0.000668j
[2025-08-24 02:13:55] [Iter 833/4650] R2[382/600], Temp: 0.2919, Energy: -28.705001+0.000999j
[2025-08-24 02:14:05] [Iter 834/4650] R2[383/600], Temp: 0.2895, Energy: -28.705564+0.000498j
[2025-08-24 02:14:15] [Iter 835/4650] R2[384/600], Temp: 0.2871, Energy: -28.694635+0.002564j
[2025-08-24 02:14:26] [Iter 836/4650] R2[385/600], Temp: 0.2847, Energy: -28.699037+0.000483j
[2025-08-24 02:14:36] [Iter 837/4650] R2[386/600], Temp: 0.2824, Energy: -28.693833+0.003695j
[2025-08-24 02:14:46] [Iter 838/4650] R2[387/600], Temp: 0.2800, Energy: -28.691994-0.000186j
[2025-08-24 02:14:56] [Iter 839/4650] R2[388/600], Temp: 0.2777, Energy: -28.706353+0.000322j
[2025-08-24 02:15:06] [Iter 840/4650] R2[389/600], Temp: 0.2753, Energy: -28.701835-0.004003j
[2025-08-24 02:15:16] [Iter 841/4650] R2[390/600], Temp: 0.2730, Energy: -28.688649+0.002273j
[2025-08-24 02:15:26] [Iter 842/4650] R2[391/600], Temp: 0.2707, Energy: -28.702455-0.002604j
[2025-08-24 02:15:37] [Iter 843/4650] R2[392/600], Temp: 0.2684, Energy: -28.693956+0.000435j
[2025-08-24 02:15:47] [Iter 844/4650] R2[393/600], Temp: 0.2660, Energy: -28.708654+0.003131j
[2025-08-24 02:15:57] [Iter 845/4650] R2[394/600], Temp: 0.2637, Energy: -28.698476+0.002995j
[2025-08-24 02:16:07] [Iter 846/4650] R2[395/600], Temp: 0.2614, Energy: -28.704781+0.000957j
[2025-08-24 02:16:17] [Iter 847/4650] R2[396/600], Temp: 0.2591, Energy: -28.695681-0.000199j
[2025-08-24 02:16:27] [Iter 848/4650] R2[397/600], Temp: 0.2568, Energy: -28.696203+0.000525j
[2025-08-24 02:16:37] [Iter 849/4650] R2[398/600], Temp: 0.2545, Energy: -28.695416-0.004729j
[2025-08-24 02:16:48] [Iter 850/4650] R2[399/600], Temp: 0.2523, Energy: -28.691006+0.002516j
[2025-08-24 02:16:58] [Iter 851/4650] R2[400/600], Temp: 0.2500, Energy: -28.707115+0.001925j
[2025-08-24 02:17:08] [Iter 852/4650] R2[401/600], Temp: 0.2477, Energy: -28.705037+0.000134j
[2025-08-24 02:17:18] [Iter 853/4650] R2[402/600], Temp: 0.2455, Energy: -28.702070+0.000834j
[2025-08-24 02:17:28] [Iter 854/4650] R2[403/600], Temp: 0.2432, Energy: -28.700331+0.000174j
[2025-08-24 02:17:38] [Iter 855/4650] R2[404/600], Temp: 0.2410, Energy: -28.707003-0.001688j
[2025-08-24 02:17:49] [Iter 856/4650] R2[405/600], Temp: 0.2388, Energy: -28.699491-0.000951j
[2025-08-24 02:17:59] [Iter 857/4650] R2[406/600], Temp: 0.2365, Energy: -28.694908-0.000654j
[2025-08-24 02:18:09] [Iter 858/4650] R2[407/600], Temp: 0.2343, Energy: -28.703221-0.001913j
[2025-08-24 02:18:19] [Iter 859/4650] R2[408/600], Temp: 0.2321, Energy: -28.707664-0.001826j
[2025-08-24 02:18:29] [Iter 860/4650] R2[409/600], Temp: 0.2299, Energy: -28.694209-0.000404j
[2025-08-24 02:18:39] [Iter 861/4650] R2[410/600], Temp: 0.2277, Energy: -28.700742+0.006101j
[2025-08-24 02:18:49] [Iter 862/4650] R2[411/600], Temp: 0.2255, Energy: -28.701320-0.002996j
[2025-08-24 02:18:59] [Iter 863/4650] R2[412/600], Temp: 0.2233, Energy: -28.703929-0.002420j
[2025-08-24 02:19:10] [Iter 864/4650] R2[413/600], Temp: 0.2211, Energy: -28.708237+0.004189j
[2025-08-24 02:19:20] [Iter 865/4650] R2[414/600], Temp: 0.2190, Energy: -28.698553+0.001913j
[2025-08-24 02:19:30] [Iter 866/4650] R2[415/600], Temp: 0.2168, Energy: -28.702398-0.000520j
[2025-08-24 02:19:40] [Iter 867/4650] R2[416/600], Temp: 0.2146, Energy: -28.698691-0.000590j
[2025-08-24 02:19:50] [Iter 868/4650] R2[417/600], Temp: 0.2125, Energy: -28.708295-0.004668j
[2025-08-24 02:20:00] [Iter 869/4650] R2[418/600], Temp: 0.2104, Energy: -28.698010+0.001042j
[2025-08-24 02:20:11] [Iter 870/4650] R2[419/600], Temp: 0.2082, Energy: -28.695757-0.002104j
[2025-08-24 02:20:21] [Iter 871/4650] R2[420/600], Temp: 0.2061, Energy: -28.703456-0.000161j
[2025-08-24 02:20:31] [Iter 872/4650] R2[421/600], Temp: 0.2040, Energy: -28.700312+0.001491j
[2025-08-24 02:20:41] [Iter 873/4650] R2[422/600], Temp: 0.2019, Energy: -28.697388-0.002615j
[2025-08-24 02:20:51] [Iter 874/4650] R2[423/600], Temp: 0.1998, Energy: -28.700272-0.002679j
[2025-08-24 02:21:01] [Iter 875/4650] R2[424/600], Temp: 0.1977, Energy: -28.699801+0.001967j
[2025-08-24 02:21:11] [Iter 876/4650] R2[425/600], Temp: 0.1956, Energy: -28.702409-0.002002j
[2025-08-24 02:21:22] [Iter 877/4650] R2[426/600], Temp: 0.1935, Energy: -28.701042+0.000366j
[2025-08-24 02:21:32] [Iter 878/4650] R2[427/600], Temp: 0.1915, Energy: -28.701469-0.002333j
[2025-08-24 02:21:42] [Iter 879/4650] R2[428/600], Temp: 0.1894, Energy: -28.698059+0.001571j
[2025-08-24 02:21:52] [Iter 880/4650] R2[429/600], Temp: 0.1874, Energy: -28.697891+0.002533j
[2025-08-24 02:22:02] [Iter 881/4650] R2[430/600], Temp: 0.1853, Energy: -28.702096-0.000812j
[2025-08-24 02:22:12] [Iter 882/4650] R2[431/600], Temp: 0.1833, Energy: -28.696324+0.002409j
[2025-08-24 02:22:22] [Iter 883/4650] R2[432/600], Temp: 0.1813, Energy: -28.701039+0.003223j
[2025-08-24 02:22:33] [Iter 884/4650] R2[433/600], Temp: 0.1793, Energy: -28.703032+0.001673j
[2025-08-24 02:22:43] [Iter 885/4650] R2[434/600], Temp: 0.1773, Energy: -28.704314-0.005263j
[2025-08-24 02:22:53] [Iter 886/4650] R2[435/600], Temp: 0.1753, Energy: -28.701290+0.000170j
[2025-08-24 02:23:03] [Iter 887/4650] R2[436/600], Temp: 0.1733, Energy: -28.703698+0.001907j
[2025-08-24 02:23:13] [Iter 888/4650] R2[437/600], Temp: 0.1713, Energy: -28.702524+0.002545j
[2025-08-24 02:23:23] [Iter 889/4650] R2[438/600], Temp: 0.1693, Energy: -28.698351-0.001103j
[2025-08-24 02:23:33] [Iter 890/4650] R2[439/600], Temp: 0.1674, Energy: -28.705121-0.001792j
[2025-08-24 02:23:44] [Iter 891/4650] R2[440/600], Temp: 0.1654, Energy: -28.698738-0.001385j
[2025-08-24 02:23:54] [Iter 892/4650] R2[441/600], Temp: 0.1635, Energy: -28.695530-0.001485j
[2025-08-24 02:24:04] [Iter 893/4650] R2[442/600], Temp: 0.1616, Energy: -28.705383+0.001047j
[2025-08-24 02:24:14] [Iter 894/4650] R2[443/600], Temp: 0.1596, Energy: -28.708162+0.001541j
[2025-08-24 02:24:24] [Iter 895/4650] R2[444/600], Temp: 0.1577, Energy: -28.699389+0.001260j
[2025-08-24 02:24:34] [Iter 896/4650] R2[445/600], Temp: 0.1558, Energy: -28.702144+0.000926j
[2025-08-24 02:24:44] [Iter 897/4650] R2[446/600], Temp: 0.1539, Energy: -28.705363+0.000480j
[2025-08-24 02:24:55] [Iter 898/4650] R2[447/600], Temp: 0.1520, Energy: -28.700504+0.000240j
[2025-08-24 02:25:05] [Iter 899/4650] R2[448/600], Temp: 0.1502, Energy: -28.704807-0.002103j
[2025-08-24 02:25:15] [Iter 900/4650] R2[449/600], Temp: 0.1483, Energy: -28.699833-0.000686j
[2025-08-24 02:25:25] [Iter 901/4650] R2[450/600], Temp: 0.1464, Energy: -28.701086-0.001330j
[2025-08-24 02:25:35] [Iter 902/4650] R2[451/600], Temp: 0.1446, Energy: -28.698725-0.000284j
[2025-08-24 02:25:45] [Iter 903/4650] R2[452/600], Temp: 0.1428, Energy: -28.701512+0.000684j
[2025-08-24 02:25:55] [Iter 904/4650] R2[453/600], Temp: 0.1409, Energy: -28.702260+0.000086j
[2025-08-24 02:26:06] [Iter 905/4650] R2[454/600], Temp: 0.1391, Energy: -28.704106+0.002148j
[2025-08-24 02:26:16] [Iter 906/4650] R2[455/600], Temp: 0.1373, Energy: -28.702563-0.002023j
[2025-08-24 02:26:26] [Iter 907/4650] R2[456/600], Temp: 0.1355, Energy: -28.697778+0.003251j
[2025-08-24 02:26:36] [Iter 908/4650] R2[457/600], Temp: 0.1337, Energy: -28.708668-0.003360j
[2025-08-24 02:26:46] [Iter 909/4650] R2[458/600], Temp: 0.1320, Energy: -28.701065+0.001751j
[2025-08-24 02:26:56] [Iter 910/4650] R2[459/600], Temp: 0.1302, Energy: -28.704231-0.000774j
[2025-08-24 02:27:06] [Iter 911/4650] R2[460/600], Temp: 0.1284, Energy: -28.700257-0.000468j
[2025-08-24 02:27:17] [Iter 912/4650] R2[461/600], Temp: 0.1267, Energy: -28.693709-0.002404j
[2025-08-24 02:27:27] [Iter 913/4650] R2[462/600], Temp: 0.1249, Energy: -28.707663-0.002555j
[2025-08-24 02:27:37] [Iter 914/4650] R2[463/600], Temp: 0.1232, Energy: -28.704877+0.000857j
[2025-08-24 02:27:47] [Iter 915/4650] R2[464/600], Temp: 0.1215, Energy: -28.707717+0.001919j
[2025-08-24 02:27:57] [Iter 916/4650] R2[465/600], Temp: 0.1198, Energy: -28.700371-0.001168j
[2025-08-24 02:28:07] [Iter 917/4650] R2[466/600], Temp: 0.1181, Energy: -28.706680+0.000075j
[2025-08-24 02:28:17] [Iter 918/4650] R2[467/600], Temp: 0.1164, Energy: -28.700779-0.005122j
[2025-08-24 02:28:27] [Iter 919/4650] R2[468/600], Temp: 0.1147, Energy: -28.704669-0.003609j
[2025-08-24 02:28:38] [Iter 920/4650] R2[469/600], Temp: 0.1131, Energy: -28.708005+0.002059j
[2025-08-24 02:28:48] [Iter 921/4650] R2[470/600], Temp: 0.1114, Energy: -28.710983+0.002726j
[2025-08-24 02:28:58] [Iter 922/4650] R2[471/600], Temp: 0.1098, Energy: -28.713425-0.000569j
[2025-08-24 02:29:08] [Iter 923/4650] R2[472/600], Temp: 0.1082, Energy: -28.701579+0.000405j
[2025-08-24 02:29:18] [Iter 924/4650] R2[473/600], Temp: 0.1065, Energy: -28.700664-0.001264j
[2025-08-24 02:29:28] [Iter 925/4650] R2[474/600], Temp: 0.1049, Energy: -28.708149-0.001623j
[2025-08-24 02:29:38] [Iter 926/4650] R2[475/600], Temp: 0.1033, Energy: -28.704855-0.000723j
[2025-08-24 02:29:49] [Iter 927/4650] R2[476/600], Temp: 0.1017, Energy: -28.706298+0.000437j
[2025-08-24 02:29:59] [Iter 928/4650] R2[477/600], Temp: 0.1002, Energy: -28.705082-0.001858j
[2025-08-24 02:30:09] [Iter 929/4650] R2[478/600], Temp: 0.0986, Energy: -28.701881+0.002425j
[2025-08-24 02:30:19] [Iter 930/4650] R2[479/600], Temp: 0.0970, Energy: -28.707476+0.001995j
[2025-08-24 02:30:29] [Iter 931/4650] R2[480/600], Temp: 0.0955, Energy: -28.705956+0.001572j
[2025-08-24 02:30:39] [Iter 932/4650] R2[481/600], Temp: 0.0940, Energy: -28.703856-0.002391j
[2025-08-24 02:30:49] [Iter 933/4650] R2[482/600], Temp: 0.0924, Energy: -28.710597-0.007613j
[2025-08-24 02:31:00] [Iter 934/4650] R2[483/600], Temp: 0.0909, Energy: -28.701993-0.000834j
[2025-08-24 02:31:10] [Iter 935/4650] R2[484/600], Temp: 0.0894, Energy: -28.710007+0.000765j
[2025-08-24 02:31:20] [Iter 936/4650] R2[485/600], Temp: 0.0879, Energy: -28.704032+0.003242j
[2025-08-24 02:31:30] [Iter 937/4650] R2[486/600], Temp: 0.0865, Energy: -28.707917-0.002564j
[2025-08-24 02:31:40] [Iter 938/4650] R2[487/600], Temp: 0.0850, Energy: -28.706977-0.002118j
[2025-08-24 02:31:50] [Iter 939/4650] R2[488/600], Temp: 0.0835, Energy: -28.700415+0.002481j
[2025-08-24 02:32:00] [Iter 940/4650] R2[489/600], Temp: 0.0821, Energy: -28.707212-0.001676j
[2025-08-24 02:32:11] [Iter 941/4650] R2[490/600], Temp: 0.0807, Energy: -28.706543-0.001245j
[2025-08-24 02:32:21] [Iter 942/4650] R2[491/600], Temp: 0.0792, Energy: -28.705573+0.002578j
[2025-08-24 02:32:31] [Iter 943/4650] R2[492/600], Temp: 0.0778, Energy: -28.706390+0.004263j
[2025-08-24 02:32:41] [Iter 944/4650] R2[493/600], Temp: 0.0764, Energy: -28.709867+0.001650j
[2025-08-24 02:32:51] [Iter 945/4650] R2[494/600], Temp: 0.0751, Energy: -28.706210+0.001352j
[2025-08-24 02:33:01] [Iter 946/4650] R2[495/600], Temp: 0.0737, Energy: -28.708383-0.000885j
[2025-08-24 02:33:11] [Iter 947/4650] R2[496/600], Temp: 0.0723, Energy: -28.717086-0.004550j
[2025-08-24 02:33:22] [Iter 948/4650] R2[497/600], Temp: 0.0710, Energy: -28.708715+0.001456j
[2025-08-24 02:33:32] [Iter 949/4650] R2[498/600], Temp: 0.0696, Energy: -28.709557+0.001826j
[2025-08-24 02:33:42] [Iter 950/4650] R2[499/600], Temp: 0.0683, Energy: -28.707553-0.000964j
[2025-08-24 02:33:52] [Iter 951/4650] R2[500/600], Temp: 0.0670, Energy: -28.707192-0.000130j
[2025-08-24 02:34:02] [Iter 952/4650] R2[501/600], Temp: 0.0657, Energy: -28.706701-0.001795j
[2025-08-24 02:34:12] [Iter 953/4650] R2[502/600], Temp: 0.0644, Energy: -28.708058-0.000831j
[2025-08-24 02:34:22] [Iter 954/4650] R2[503/600], Temp: 0.0631, Energy: -28.707256-0.002203j
[2025-08-24 02:34:33] [Iter 955/4650] R2[504/600], Temp: 0.0618, Energy: -28.706702+0.001322j
[2025-08-24 02:34:43] [Iter 956/4650] R2[505/600], Temp: 0.0606, Energy: -28.703726-0.003099j
[2025-08-24 02:34:53] [Iter 957/4650] R2[506/600], Temp: 0.0593, Energy: -28.707445-0.000930j
[2025-08-24 02:35:03] [Iter 958/4650] R2[507/600], Temp: 0.0581, Energy: -28.706182-0.000414j
[2025-08-24 02:35:13] [Iter 959/4650] R2[508/600], Temp: 0.0569, Energy: -28.702165+0.001514j
[2025-08-24 02:35:23] [Iter 960/4650] R2[509/600], Temp: 0.0557, Energy: -28.702104+0.000196j
[2025-08-24 02:35:33] [Iter 961/4650] R2[510/600], Temp: 0.0545, Energy: -28.711534+0.003877j
[2025-08-24 02:35:44] [Iter 962/4650] R2[511/600], Temp: 0.0533, Energy: -28.706421+0.001907j
[2025-08-24 02:35:54] [Iter 963/4650] R2[512/600], Temp: 0.0521, Energy: -28.706681+0.001268j
[2025-08-24 02:36:04] [Iter 964/4650] R2[513/600], Temp: 0.0510, Energy: -28.701881-0.001978j
[2025-08-24 02:36:14] [Iter 965/4650] R2[514/600], Temp: 0.0498, Energy: -28.700382-0.004912j
[2025-08-24 02:36:24] [Iter 966/4650] R2[515/600], Temp: 0.0487, Energy: -28.705828-0.000714j
[2025-08-24 02:36:34] [Iter 967/4650] R2[516/600], Temp: 0.0476, Energy: -28.699368+0.000288j
[2025-08-24 02:36:44] [Iter 968/4650] R2[517/600], Temp: 0.0465, Energy: -28.702998+0.000593j
[2025-08-24 02:36:54] [Iter 969/4650] R2[518/600], Temp: 0.0454, Energy: -28.701203-0.006317j
[2025-08-24 02:37:05] [Iter 970/4650] R2[519/600], Temp: 0.0443, Energy: -28.715256-0.000890j
[2025-08-24 02:37:15] [Iter 971/4650] R2[520/600], Temp: 0.0432, Energy: -28.715101-0.001316j
[2025-08-24 02:37:25] [Iter 972/4650] R2[521/600], Temp: 0.0422, Energy: -28.710482+0.000984j
[2025-08-24 02:37:35] [Iter 973/4650] R2[522/600], Temp: 0.0411, Energy: -28.706521+0.001702j
[2025-08-24 02:37:45] [Iter 974/4650] R2[523/600], Temp: 0.0401, Energy: -28.709403-0.002929j
[2025-08-24 02:37:55] [Iter 975/4650] R2[524/600], Temp: 0.0391, Energy: -28.709522+0.000276j
[2025-08-24 02:38:05] [Iter 976/4650] R2[525/600], Temp: 0.0381, Energy: -28.703822+0.000097j
[2025-08-24 02:38:16] [Iter 977/4650] R2[526/600], Temp: 0.0371, Energy: -28.710140+0.000033j
[2025-08-24 02:38:26] [Iter 978/4650] R2[527/600], Temp: 0.0361, Energy: -28.709485+0.000618j
[2025-08-24 02:38:36] [Iter 979/4650] R2[528/600], Temp: 0.0351, Energy: -28.707865+0.000379j
[2025-08-24 02:38:46] [Iter 980/4650] R2[529/600], Temp: 0.0342, Energy: -28.706375-0.002288j
[2025-08-24 02:38:56] [Iter 981/4650] R2[530/600], Temp: 0.0332, Energy: -28.700438+0.000677j
[2025-08-24 02:39:06] [Iter 982/4650] R2[531/600], Temp: 0.0323, Energy: -28.709073+0.000505j
[2025-08-24 02:39:16] [Iter 983/4650] R2[532/600], Temp: 0.0314, Energy: -28.712912-0.000419j
[2025-08-24 02:39:27] [Iter 984/4650] R2[533/600], Temp: 0.0305, Energy: -28.712799+0.002117j
[2025-08-24 02:39:37] [Iter 985/4650] R2[534/600], Temp: 0.0296, Energy: -28.712789-0.003597j
[2025-08-24 02:39:47] [Iter 986/4650] R2[535/600], Temp: 0.0287, Energy: -28.705227+0.002765j
[2025-08-24 02:39:57] [Iter 987/4650] R2[536/600], Temp: 0.0278, Energy: -28.712370+0.006135j
[2025-08-24 02:40:07] [Iter 988/4650] R2[537/600], Temp: 0.0270, Energy: -28.706527-0.004709j
[2025-08-24 02:40:17] [Iter 989/4650] R2[538/600], Temp: 0.0261, Energy: -28.712068-0.002951j
[2025-08-24 02:40:27] [Iter 990/4650] R2[539/600], Temp: 0.0253, Energy: -28.714132-0.000948j
[2025-08-24 02:40:38] [Iter 991/4650] R2[540/600], Temp: 0.0245, Energy: -28.703687+0.003524j
[2025-08-24 02:40:48] [Iter 992/4650] R2[541/600], Temp: 0.0237, Energy: -28.708522+0.000443j
[2025-08-24 02:40:58] [Iter 993/4650] R2[542/600], Temp: 0.0229, Energy: -28.704521-0.000800j
[2025-08-24 02:41:08] [Iter 994/4650] R2[543/600], Temp: 0.0221, Energy: -28.708067+0.000824j
[2025-08-24 02:41:18] [Iter 995/4650] R2[544/600], Temp: 0.0213, Energy: -28.708820+0.001183j
[2025-08-24 02:41:28] [Iter 996/4650] R2[545/600], Temp: 0.0206, Energy: -28.711736-0.003148j
[2025-08-24 02:41:38] [Iter 997/4650] R2[546/600], Temp: 0.0199, Energy: -28.714490+0.002442j
[2025-08-24 02:41:49] [Iter 998/4650] R2[547/600], Temp: 0.0191, Energy: -28.719009+0.001607j
[2025-08-24 02:41:59] [Iter 999/4650] R2[548/600], Temp: 0.0184, Energy: -28.704826-0.000621j
[2025-08-24 02:42:09] [Iter 1000/4650] R2[549/600], Temp: 0.0177, Energy: -28.712070+0.002956j
[2025-08-24 02:42:09] ✓ Checkpoint saved: checkpoint_iter_001000.pkl
[2025-08-24 02:42:19] [Iter 1001/4650] R2[550/600], Temp: 0.0170, Energy: -28.704625+0.001478j
[2025-08-24 02:42:29] [Iter 1002/4650] R2[551/600], Temp: 0.0164, Energy: -28.715314+0.001294j
[2025-08-24 02:42:39] [Iter 1003/4650] R2[552/600], Temp: 0.0157, Energy: -28.710427+0.001069j
[2025-08-24 02:42:49] [Iter 1004/4650] R2[553/600], Temp: 0.0151, Energy: -28.703623-0.001509j
[2025-08-24 02:43:00] [Iter 1005/4650] R2[554/600], Temp: 0.0144, Energy: -28.703888-0.002869j
[2025-08-24 02:43:10] [Iter 1006/4650] R2[555/600], Temp: 0.0138, Energy: -28.704096-0.001435j
[2025-08-24 02:43:20] [Iter 1007/4650] R2[556/600], Temp: 0.0132, Energy: -28.710035-0.001873j
[2025-08-24 02:43:30] [Iter 1008/4650] R2[557/600], Temp: 0.0126, Energy: -28.716212-0.001046j
[2025-08-24 02:43:40] [Iter 1009/4650] R2[558/600], Temp: 0.0120, Energy: -28.711330+0.001436j
[2025-08-24 02:43:50] [Iter 1010/4650] R2[559/600], Temp: 0.0115, Energy: -28.708570-0.001624j
[2025-08-24 02:44:00] [Iter 1011/4650] R2[560/600], Temp: 0.0109, Energy: -28.709494-0.003247j
[2025-08-24 02:44:11] [Iter 1012/4650] R2[561/600], Temp: 0.0104, Energy: -28.698959-0.001999j
[2025-08-24 02:44:21] [Iter 1013/4650] R2[562/600], Temp: 0.0099, Energy: -28.718990-0.003091j
[2025-08-24 02:44:31] [Iter 1014/4650] R2[563/600], Temp: 0.0094, Energy: -28.708076-0.000600j
[2025-08-24 02:44:41] [Iter 1015/4650] R2[564/600], Temp: 0.0089, Energy: -28.708861-0.000612j
[2025-08-24 02:44:51] [Iter 1016/4650] R2[565/600], Temp: 0.0084, Energy: -28.710013-0.000366j
[2025-08-24 02:45:01] [Iter 1017/4650] R2[566/600], Temp: 0.0079, Energy: -28.702997+0.003699j
[2025-08-24 02:45:11] [Iter 1018/4650] R2[567/600], Temp: 0.0074, Energy: -28.708056+0.000378j
[2025-08-24 02:45:21] [Iter 1019/4650] R2[568/600], Temp: 0.0070, Energy: -28.709254-0.000562j
[2025-08-24 02:45:32] [Iter 1020/4650] R2[569/600], Temp: 0.0066, Energy: -28.707511-0.001403j
[2025-08-24 02:45:42] [Iter 1021/4650] R2[570/600], Temp: 0.0062, Energy: -28.706207-0.001186j
[2025-08-24 02:45:52] [Iter 1022/4650] R2[571/600], Temp: 0.0058, Energy: -28.707243+0.003685j
[2025-08-24 02:46:02] [Iter 1023/4650] R2[572/600], Temp: 0.0054, Energy: -28.710424-0.000929j
[2025-08-24 02:46:12] [Iter 1024/4650] R2[573/600], Temp: 0.0050, Energy: -28.717629-0.000092j
[2025-08-24 02:46:22] [Iter 1025/4650] R2[574/600], Temp: 0.0046, Energy: -28.712514-0.001360j
[2025-08-24 02:46:32] [Iter 1026/4650] R2[575/600], Temp: 0.0043, Energy: -28.708643-0.000432j
[2025-08-24 02:46:43] [Iter 1027/4650] R2[576/600], Temp: 0.0039, Energy: -28.714269+0.000971j
[2025-08-24 02:46:53] [Iter 1028/4650] R2[577/600], Temp: 0.0036, Energy: -28.711950-0.002211j
[2025-08-24 02:47:03] [Iter 1029/4650] R2[578/600], Temp: 0.0033, Energy: -28.713291+0.000882j
[2025-08-24 02:47:13] [Iter 1030/4650] R2[579/600], Temp: 0.0030, Energy: -28.711254+0.001912j
[2025-08-24 02:47:23] [Iter 1031/4650] R2[580/600], Temp: 0.0027, Energy: -28.714005+0.000151j
[2025-08-24 02:47:33] [Iter 1032/4650] R2[581/600], Temp: 0.0025, Energy: -28.711416-0.003554j
[2025-08-24 02:47:43] [Iter 1033/4650] R2[582/600], Temp: 0.0022, Energy: -28.710585-0.000149j
[2025-08-24 02:47:54] [Iter 1034/4650] R2[583/600], Temp: 0.0020, Energy: -28.705553-0.001529j
[2025-08-24 02:48:04] [Iter 1035/4650] R2[584/600], Temp: 0.0018, Energy: -28.714747+0.000875j
[2025-08-24 02:48:14] [Iter 1036/4650] R2[585/600], Temp: 0.0015, Energy: -28.704883+0.004943j
[2025-08-24 02:48:24] [Iter 1037/4650] R2[586/600], Temp: 0.0013, Energy: -28.702796+0.001018j
[2025-08-24 02:48:34] [Iter 1038/4650] R2[587/600], Temp: 0.0012, Energy: -28.720893-0.000648j
[2025-08-24 02:48:44] [Iter 1039/4650] R2[588/600], Temp: 0.0010, Energy: -28.708011+0.000456j
[2025-08-24 02:48:54] [Iter 1040/4650] R2[589/600], Temp: 0.0008, Energy: -28.706733-0.000335j
[2025-08-24 02:49:05] [Iter 1041/4650] R2[590/600], Temp: 0.0007, Energy: -28.712084+0.001070j
[2025-08-24 02:49:15] [Iter 1042/4650] R2[591/600], Temp: 0.0006, Energy: -28.713645+0.000410j
[2025-08-24 02:49:25] [Iter 1043/4650] R2[592/600], Temp: 0.0004, Energy: -28.703618-0.000645j
[2025-08-24 02:49:35] [Iter 1044/4650] R2[593/600], Temp: 0.0003, Energy: -28.704138-0.000937j
[2025-08-24 02:49:45] [Iter 1045/4650] R2[594/600], Temp: 0.0002, Energy: -28.711625+0.000142j
[2025-08-24 02:49:55] [Iter 1046/4650] R2[595/600], Temp: 0.0002, Energy: -28.711280+0.001114j
[2025-08-24 02:50:05] [Iter 1047/4650] R2[596/600], Temp: 0.0001, Energy: -28.712312-0.001019j
[2025-08-24 02:50:16] [Iter 1048/4650] R2[597/600], Temp: 0.0001, Energy: -28.709275+0.002567j
[2025-08-24 02:50:26] [Iter 1049/4650] R2[598/600], Temp: 0.0000, Energy: -28.714073+0.001108j
[2025-08-24 02:50:36] [Iter 1050/4650] R2[599/600], Temp: 0.0000, Energy: -28.707193+0.001895j
[2025-08-24 02:50:36] RESTART #3 | Period: 1200
[2025-08-24 02:50:46] [Iter 1051/4650] R3[0/1200], Temp: 1.0000, Energy: -28.712710+0.000841j
[2025-08-24 02:50:56] [Iter 1052/4650] R3[1/1200], Temp: 1.0000, Energy: -28.709911-0.002962j
[2025-08-24 02:51:06] [Iter 1053/4650] R3[2/1200], Temp: 1.0000, Energy: -28.715217+0.000679j
[2025-08-24 02:51:16] [Iter 1054/4650] R3[3/1200], Temp: 1.0000, Energy: -28.707108+0.000854j
[2025-08-24 02:51:27] [Iter 1055/4650] R3[4/1200], Temp: 1.0000, Energy: -28.712979-0.001622j
[2025-08-24 02:51:37] [Iter 1056/4650] R3[5/1200], Temp: 1.0000, Energy: -28.715213+0.000701j
[2025-08-24 02:51:47] [Iter 1057/4650] R3[6/1200], Temp: 0.9999, Energy: -28.714996+0.003766j
[2025-08-24 02:51:57] [Iter 1058/4650] R3[7/1200], Temp: 0.9999, Energy: -28.710276+0.001924j
[2025-08-24 02:52:07] [Iter 1059/4650] R3[8/1200], Temp: 0.9999, Energy: -28.713408-0.000969j
[2025-08-24 02:52:17] [Iter 1060/4650] R3[9/1200], Temp: 0.9999, Energy: -28.709359-0.002209j
[2025-08-24 02:52:27] [Iter 1061/4650] R3[10/1200], Temp: 0.9998, Energy: -28.710185+0.003189j
[2025-08-24 02:52:38] [Iter 1062/4650] R3[11/1200], Temp: 0.9998, Energy: -28.700081-0.001585j
[2025-08-24 02:52:48] [Iter 1063/4650] R3[12/1200], Temp: 0.9998, Energy: -28.711867+0.003162j
[2025-08-24 02:52:58] [Iter 1064/4650] R3[13/1200], Temp: 0.9997, Energy: -28.710846-0.002496j
[2025-08-24 02:53:08] [Iter 1065/4650] R3[14/1200], Temp: 0.9997, Energy: -28.712302+0.000778j
[2025-08-24 02:53:18] [Iter 1066/4650] R3[15/1200], Temp: 0.9996, Energy: -28.716665+0.001452j
[2025-08-24 02:53:28] [Iter 1067/4650] R3[16/1200], Temp: 0.9996, Energy: -28.707850-0.003666j
[2025-08-24 02:53:38] [Iter 1068/4650] R3[17/1200], Temp: 0.9995, Energy: -28.715776+0.000365j
[2025-08-24 02:53:49] [Iter 1069/4650] R3[18/1200], Temp: 0.9994, Energy: -28.712483-0.000755j
[2025-08-24 02:53:59] [Iter 1070/4650] R3[19/1200], Temp: 0.9994, Energy: -28.720810-0.000834j
[2025-08-24 02:54:09] [Iter 1071/4650] R3[20/1200], Temp: 0.9993, Energy: -28.712317+0.000048j
[2025-08-24 02:54:19] [Iter 1072/4650] R3[21/1200], Temp: 0.9992, Energy: -28.711338+0.001336j
[2025-08-24 02:54:29] [Iter 1073/4650] R3[22/1200], Temp: 0.9992, Energy: -28.718522+0.001037j
[2025-08-24 02:54:39] [Iter 1074/4650] R3[23/1200], Temp: 0.9991, Energy: -28.706850+0.000753j
[2025-08-24 02:54:49] [Iter 1075/4650] R3[24/1200], Temp: 0.9990, Energy: -28.705403+0.001527j
[2025-08-24 02:55:00] [Iter 1076/4650] R3[25/1200], Temp: 0.9989, Energy: -28.716326-0.000511j
[2025-08-24 02:55:10] [Iter 1077/4650] R3[26/1200], Temp: 0.9988, Energy: -28.712939+0.003730j
[2025-08-24 02:55:20] [Iter 1078/4650] R3[27/1200], Temp: 0.9988, Energy: -28.719003-0.000752j
[2025-08-24 02:55:30] [Iter 1079/4650] R3[28/1200], Temp: 0.9987, Energy: -28.717605+0.002380j
[2025-08-24 02:55:40] [Iter 1080/4650] R3[29/1200], Temp: 0.9986, Energy: -28.721578+0.000411j
[2025-08-24 02:55:50] [Iter 1081/4650] R3[30/1200], Temp: 0.9985, Energy: -28.715048+0.000741j
[2025-08-24 02:56:00] [Iter 1082/4650] R3[31/1200], Temp: 0.9984, Energy: -28.711103-0.004540j
[2025-08-24 02:56:11] [Iter 1083/4650] R3[32/1200], Temp: 0.9982, Energy: -28.711825+0.001978j
[2025-08-24 02:56:21] [Iter 1084/4650] R3[33/1200], Temp: 0.9981, Energy: -28.711638+0.005018j
[2025-08-24 02:56:31] [Iter 1085/4650] R3[34/1200], Temp: 0.9980, Energy: -28.712211+0.001263j
[2025-08-24 02:56:41] [Iter 1086/4650] R3[35/1200], Temp: 0.9979, Energy: -28.715105-0.000121j
[2025-08-24 02:56:51] [Iter 1087/4650] R3[36/1200], Temp: 0.9978, Energy: -28.714850-0.000246j
[2025-08-24 02:57:01] [Iter 1088/4650] R3[37/1200], Temp: 0.9977, Energy: -28.707401-0.000548j
[2025-08-24 02:57:11] [Iter 1089/4650] R3[38/1200], Temp: 0.9975, Energy: -28.718324+0.002526j
[2025-08-24 02:57:22] [Iter 1090/4650] R3[39/1200], Temp: 0.9974, Energy: -28.718153+0.005274j
[2025-08-24 02:57:32] [Iter 1091/4650] R3[40/1200], Temp: 0.9973, Energy: -28.711248+0.000750j
[2025-08-24 02:57:42] [Iter 1092/4650] R3[41/1200], Temp: 0.9971, Energy: -28.718244+0.002640j
[2025-08-24 02:57:52] [Iter 1093/4650] R3[42/1200], Temp: 0.9970, Energy: -28.716305+0.001167j
[2025-08-24 02:58:02] [Iter 1094/4650] R3[43/1200], Temp: 0.9968, Energy: -28.709046-0.002771j
[2025-08-24 02:58:12] [Iter 1095/4650] R3[44/1200], Temp: 0.9967, Energy: -28.712502+0.000411j
[2025-08-24 02:58:22] [Iter 1096/4650] R3[45/1200], Temp: 0.9965, Energy: -28.713034+0.000682j
[2025-08-24 02:58:33] [Iter 1097/4650] R3[46/1200], Temp: 0.9964, Energy: -28.715085+0.000302j
[2025-08-24 02:58:43] [Iter 1098/4650] R3[47/1200], Temp: 0.9962, Energy: -28.712653-0.000953j
[2025-08-24 02:58:53] [Iter 1099/4650] R3[48/1200], Temp: 0.9961, Energy: -28.716039-0.000153j
[2025-08-24 02:59:03] [Iter 1100/4650] R3[49/1200], Temp: 0.9959, Energy: -28.709717+0.000294j
[2025-08-24 02:59:13] [Iter 1101/4650] R3[50/1200], Temp: 0.9957, Energy: -28.705415+0.002142j
[2025-08-24 02:59:23] [Iter 1102/4650] R3[51/1200], Temp: 0.9955, Energy: -28.716520+0.000123j
[2025-08-24 02:59:34] [Iter 1103/4650] R3[52/1200], Temp: 0.9954, Energy: -28.711282+0.001841j
[2025-08-24 02:59:44] [Iter 1104/4650] R3[53/1200], Temp: 0.9952, Energy: -28.716599+0.000694j
[2025-08-24 02:59:54] [Iter 1105/4650] R3[54/1200], Temp: 0.9950, Energy: -28.709559+0.001297j
[2025-08-24 03:00:04] [Iter 1106/4650] R3[55/1200], Temp: 0.9948, Energy: -28.707975+0.001230j
[2025-08-24 03:00:14] [Iter 1107/4650] R3[56/1200], Temp: 0.9946, Energy: -28.717998+0.003642j
[2025-08-24 03:00:24] [Iter 1108/4650] R3[57/1200], Temp: 0.9944, Energy: -28.708805+0.002142j
[2025-08-24 03:00:34] [Iter 1109/4650] R3[58/1200], Temp: 0.9942, Energy: -28.714100+0.001383j
[2025-08-24 03:00:44] [Iter 1110/4650] R3[59/1200], Temp: 0.9940, Energy: -28.715473-0.002046j
[2025-08-24 03:00:55] [Iter 1111/4650] R3[60/1200], Temp: 0.9938, Energy: -28.707916-0.001915j
[2025-08-24 03:01:05] [Iter 1112/4650] R3[61/1200], Temp: 0.9936, Energy: -28.718229+0.002157j
[2025-08-24 03:01:15] [Iter 1113/4650] R3[62/1200], Temp: 0.9934, Energy: -28.712496+0.001221j
[2025-08-24 03:01:25] [Iter 1114/4650] R3[63/1200], Temp: 0.9932, Energy: -28.711378+0.000506j
[2025-08-24 03:01:35] [Iter 1115/4650] R3[64/1200], Temp: 0.9930, Energy: -28.712805+0.001000j
[2025-08-24 03:01:45] [Iter 1116/4650] R3[65/1200], Temp: 0.9928, Energy: -28.715199-0.001022j
[2025-08-24 03:01:55] [Iter 1117/4650] R3[66/1200], Temp: 0.9926, Energy: -28.713754+0.000670j
[2025-08-24 03:02:06] [Iter 1118/4650] R3[67/1200], Temp: 0.9923, Energy: -28.709787-0.000125j
[2025-08-24 03:02:16] [Iter 1119/4650] R3[68/1200], Temp: 0.9921, Energy: -28.715071+0.001402j
[2025-08-24 03:02:26] [Iter 1120/4650] R3[69/1200], Temp: 0.9919, Energy: -28.708896-0.002192j
[2025-08-24 03:02:36] [Iter 1121/4650] R3[70/1200], Temp: 0.9916, Energy: -28.711140+0.003677j
[2025-08-24 03:02:46] [Iter 1122/4650] R3[71/1200], Temp: 0.9914, Energy: -28.719611-0.002821j
[2025-08-24 03:02:56] [Iter 1123/4650] R3[72/1200], Temp: 0.9911, Energy: -28.715621+0.002273j
[2025-08-24 03:03:06] [Iter 1124/4650] R3[73/1200], Temp: 0.9909, Energy: -28.711720+0.000413j
[2025-08-24 03:03:17] [Iter 1125/4650] R3[74/1200], Temp: 0.9906, Energy: -28.716941+0.001108j
[2025-08-24 03:03:27] [Iter 1126/4650] R3[75/1200], Temp: 0.9904, Energy: -28.715658+0.000534j
[2025-08-24 03:03:37] [Iter 1127/4650] R3[76/1200], Temp: 0.9901, Energy: -28.721337+0.002322j
[2025-08-24 03:03:47] [Iter 1128/4650] R3[77/1200], Temp: 0.9899, Energy: -28.719357+0.001427j
[2025-08-24 03:03:57] [Iter 1129/4650] R3[78/1200], Temp: 0.9896, Energy: -28.717272+0.001044j
[2025-08-24 03:04:07] [Iter 1130/4650] R3[79/1200], Temp: 0.9893, Energy: -28.716376-0.002858j
[2025-08-24 03:04:17] [Iter 1131/4650] R3[80/1200], Temp: 0.9891, Energy: -28.710786+0.001827j
[2025-08-24 03:04:28] [Iter 1132/4650] R3[81/1200], Temp: 0.9888, Energy: -28.717066+0.001833j
[2025-08-24 03:04:38] [Iter 1133/4650] R3[82/1200], Temp: 0.9885, Energy: -28.712901-0.000452j
[2025-08-24 03:04:48] [Iter 1134/4650] R3[83/1200], Temp: 0.9882, Energy: -28.724067-0.000135j
[2025-08-24 03:04:58] [Iter 1135/4650] R3[84/1200], Temp: 0.9880, Energy: -28.710383-0.000867j
[2025-08-24 03:05:08] [Iter 1136/4650] R3[85/1200], Temp: 0.9877, Energy: -28.715244-0.000591j
[2025-08-24 03:05:18] [Iter 1137/4650] R3[86/1200], Temp: 0.9874, Energy: -28.715818-0.001404j
[2025-08-24 03:05:28] [Iter 1138/4650] R3[87/1200], Temp: 0.9871, Energy: -28.720779-0.003894j
[2025-08-24 03:05:39] [Iter 1139/4650] R3[88/1200], Temp: 0.9868, Energy: -28.716877-0.002257j
[2025-08-24 03:05:49] [Iter 1140/4650] R3[89/1200], Temp: 0.9865, Energy: -28.711794-0.001679j
[2025-08-24 03:05:59] [Iter 1141/4650] R3[90/1200], Temp: 0.9862, Energy: -28.704759+0.002803j
[2025-08-24 03:06:09] [Iter 1142/4650] R3[91/1200], Temp: 0.9859, Energy: -28.714671-0.001657j
[2025-08-24 03:06:19] [Iter 1143/4650] R3[92/1200], Temp: 0.9856, Energy: -28.715972-0.000826j
[2025-08-24 03:06:29] [Iter 1144/4650] R3[93/1200], Temp: 0.9853, Energy: -28.716704-0.000548j
[2025-08-24 03:06:40] [Iter 1145/4650] R3[94/1200], Temp: 0.9849, Energy: -28.716216-0.002259j
[2025-08-24 03:06:50] [Iter 1146/4650] R3[95/1200], Temp: 0.9846, Energy: -28.714267+0.001074j
[2025-08-24 03:07:00] [Iter 1147/4650] R3[96/1200], Temp: 0.9843, Energy: -28.714340+0.002997j
[2025-08-24 03:07:10] [Iter 1148/4650] R3[97/1200], Temp: 0.9840, Energy: -28.708773+0.000226j
[2025-08-24 03:07:20] [Iter 1149/4650] R3[98/1200], Temp: 0.9836, Energy: -28.721132-0.002242j
[2025-08-24 03:07:30] [Iter 1150/4650] R3[99/1200], Temp: 0.9833, Energy: -28.711712-0.001895j
[2025-08-24 03:07:40] [Iter 1151/4650] R3[100/1200], Temp: 0.9830, Energy: -28.716431+0.003481j
[2025-08-24 03:07:51] [Iter 1152/4650] R3[101/1200], Temp: 0.9826, Energy: -28.722547-0.002991j
[2025-08-24 03:08:01] [Iter 1153/4650] R3[102/1200], Temp: 0.9823, Energy: -28.705412+0.004559j
[2025-08-24 03:08:11] [Iter 1154/4650] R3[103/1200], Temp: 0.9819, Energy: -28.718244+0.000235j
[2025-08-24 03:08:21] [Iter 1155/4650] R3[104/1200], Temp: 0.9816, Energy: -28.718172+0.000883j
[2025-08-24 03:08:31] [Iter 1156/4650] R3[105/1200], Temp: 0.9812, Energy: -28.716423-0.002066j
[2025-08-24 03:08:41] [Iter 1157/4650] R3[106/1200], Temp: 0.9809, Energy: -28.725874+0.002292j
[2025-08-24 03:08:51] [Iter 1158/4650] R3[107/1200], Temp: 0.9805, Energy: -28.718127+0.000673j
[2025-08-24 03:09:01] [Iter 1159/4650] R3[108/1200], Temp: 0.9801, Energy: -28.723986-0.000782j
[2025-08-24 03:09:12] [Iter 1160/4650] R3[109/1200], Temp: 0.9798, Energy: -28.715583+0.001312j
[2025-08-24 03:09:22] [Iter 1161/4650] R3[110/1200], Temp: 0.9794, Energy: -28.717236+0.000434j
[2025-08-24 03:09:32] [Iter 1162/4650] R3[111/1200], Temp: 0.9790, Energy: -28.723313-0.003011j
[2025-08-24 03:09:42] [Iter 1163/4650] R3[112/1200], Temp: 0.9787, Energy: -28.716433-0.002194j
[2025-08-24 03:09:52] [Iter 1164/4650] R3[113/1200], Temp: 0.9783, Energy: -28.715370-0.003605j
[2025-08-24 03:10:02] [Iter 1165/4650] R3[114/1200], Temp: 0.9779, Energy: -28.709847+0.001097j
[2025-08-24 03:10:12] [Iter 1166/4650] R3[115/1200], Temp: 0.9775, Energy: -28.716971-0.002381j
[2025-08-24 03:10:23] [Iter 1167/4650] R3[116/1200], Temp: 0.9771, Energy: -28.715312-0.000028j
[2025-08-24 03:10:33] [Iter 1168/4650] R3[117/1200], Temp: 0.9767, Energy: -28.720311-0.004731j
[2025-08-24 03:10:43] [Iter 1169/4650] R3[118/1200], Temp: 0.9763, Energy: -28.723848-0.001064j
[2025-08-24 03:10:53] [Iter 1170/4650] R3[119/1200], Temp: 0.9759, Energy: -28.712213+0.002271j
[2025-08-24 03:11:03] [Iter 1171/4650] R3[120/1200], Temp: 0.9755, Energy: -28.707742+0.001116j
[2025-08-24 03:11:13] [Iter 1172/4650] R3[121/1200], Temp: 0.9751, Energy: -28.711015+0.001494j
[2025-08-24 03:11:23] [Iter 1173/4650] R3[122/1200], Temp: 0.9747, Energy: -28.715003-0.000351j
[2025-08-24 03:11:34] [Iter 1174/4650] R3[123/1200], Temp: 0.9743, Energy: -28.714635+0.000234j
[2025-08-24 03:11:44] [Iter 1175/4650] R3[124/1200], Temp: 0.9739, Energy: -28.707952+0.001189j
[2025-08-24 03:11:54] [Iter 1176/4650] R3[125/1200], Temp: 0.9735, Energy: -28.712182-0.003263j
[2025-08-24 03:12:04] [Iter 1177/4650] R3[126/1200], Temp: 0.9730, Energy: -28.723677+0.000473j
[2025-08-24 03:12:14] [Iter 1178/4650] R3[127/1200], Temp: 0.9726, Energy: -28.717512+0.000952j
[2025-08-24 03:12:24] [Iter 1179/4650] R3[128/1200], Temp: 0.9722, Energy: -28.712028+0.001907j
[2025-08-24 03:12:34] [Iter 1180/4650] R3[129/1200], Temp: 0.9718, Energy: -28.713287-0.003210j
[2025-08-24 03:12:45] [Iter 1181/4650] R3[130/1200], Temp: 0.9713, Energy: -28.719517-0.000314j
[2025-08-24 03:12:55] [Iter 1182/4650] R3[131/1200], Temp: 0.9709, Energy: -28.719080-0.000821j
[2025-08-24 03:13:05] [Iter 1183/4650] R3[132/1200], Temp: 0.9704, Energy: -28.717046-0.002126j
[2025-08-24 03:13:15] [Iter 1184/4650] R3[133/1200], Temp: 0.9700, Energy: -28.724355+0.002432j
[2025-08-24 03:13:25] [Iter 1185/4650] R3[134/1200], Temp: 0.9695, Energy: -28.718494+0.000434j
[2025-08-24 03:13:35] [Iter 1186/4650] R3[135/1200], Temp: 0.9691, Energy: -28.717574-0.001016j
[2025-08-24 03:13:45] [Iter 1187/4650] R3[136/1200], Temp: 0.9686, Energy: -28.719032-0.000872j
[2025-08-24 03:13:56] [Iter 1188/4650] R3[137/1200], Temp: 0.9682, Energy: -28.715541+0.001752j
[2025-08-24 03:14:06] [Iter 1189/4650] R3[138/1200], Temp: 0.9677, Energy: -28.716489+0.001617j
[2025-08-24 03:14:16] [Iter 1190/4650] R3[139/1200], Temp: 0.9673, Energy: -28.717166+0.001400j
[2025-08-24 03:14:26] [Iter 1191/4650] R3[140/1200], Temp: 0.9668, Energy: -28.719248+0.000009j
[2025-08-24 03:14:36] [Iter 1192/4650] R3[141/1200], Temp: 0.9663, Energy: -28.716107+0.001814j
[2025-08-24 03:14:46] [Iter 1193/4650] R3[142/1200], Temp: 0.9658, Energy: -28.720813+0.000190j
[2025-08-24 03:14:56] [Iter 1194/4650] R3[143/1200], Temp: 0.9654, Energy: -28.717847+0.002607j
[2025-08-24 03:15:07] [Iter 1195/4650] R3[144/1200], Temp: 0.9649, Energy: -28.711126+0.001482j
[2025-08-24 03:15:17] [Iter 1196/4650] R3[145/1200], Temp: 0.9644, Energy: -28.716283-0.001873j
[2025-08-24 03:15:27] [Iter 1197/4650] R3[146/1200], Temp: 0.9639, Energy: -28.712528+0.001721j
[2025-08-24 03:15:37] [Iter 1198/4650] R3[147/1200], Temp: 0.9634, Energy: -28.719552+0.000504j
[2025-08-24 03:15:47] [Iter 1199/4650] R3[148/1200], Temp: 0.9629, Energy: -28.707470+0.001855j
[2025-08-24 03:15:57] [Iter 1200/4650] R3[149/1200], Temp: 0.9624, Energy: -28.729918+0.000436j
[2025-08-24 03:16:07] [Iter 1201/4650] R3[150/1200], Temp: 0.9619, Energy: -28.712048+0.002723j
[2025-08-24 03:16:18] [Iter 1202/4650] R3[151/1200], Temp: 0.9614, Energy: -28.713483+0.000926j
[2025-08-24 03:16:28] [Iter 1203/4650] R3[152/1200], Temp: 0.9609, Energy: -28.721504+0.000044j
[2025-08-24 03:16:38] [Iter 1204/4650] R3[153/1200], Temp: 0.9604, Energy: -28.714660-0.002296j
[2025-08-24 03:16:48] [Iter 1205/4650] R3[154/1200], Temp: 0.9599, Energy: -28.717473-0.000484j
[2025-08-24 03:16:58] [Iter 1206/4650] R3[155/1200], Temp: 0.9594, Energy: -28.716553-0.001027j
[2025-08-24 03:17:08] [Iter 1207/4650] R3[156/1200], Temp: 0.9589, Energy: -28.713562-0.006186j
[2025-08-24 03:17:18] [Iter 1208/4650] R3[157/1200], Temp: 0.9584, Energy: -28.725431+0.000761j
[2025-08-24 03:17:29] [Iter 1209/4650] R3[158/1200], Temp: 0.9578, Energy: -28.721177+0.002412j
[2025-08-24 03:17:39] [Iter 1210/4650] R3[159/1200], Temp: 0.9573, Energy: -28.722431-0.001001j
[2025-08-24 03:17:49] [Iter 1211/4650] R3[160/1200], Temp: 0.9568, Energy: -28.715704+0.002543j
[2025-08-24 03:17:59] [Iter 1212/4650] R3[161/1200], Temp: 0.9562, Energy: -28.718498+0.000909j
[2025-08-24 03:18:09] [Iter 1213/4650] R3[162/1200], Temp: 0.9557, Energy: -28.719828+0.000849j
[2025-08-24 03:18:19] [Iter 1214/4650] R3[163/1200], Temp: 0.9552, Energy: -28.724106+0.004123j
[2025-08-24 03:18:29] [Iter 1215/4650] R3[164/1200], Temp: 0.9546, Energy: -28.723754+0.001838j
[2025-08-24 03:18:40] [Iter 1216/4650] R3[165/1200], Temp: 0.9541, Energy: -28.712917-0.004783j
[2025-08-24 03:18:50] [Iter 1217/4650] R3[166/1200], Temp: 0.9535, Energy: -28.723018+0.001347j
[2025-08-24 03:19:00] [Iter 1218/4650] R3[167/1200], Temp: 0.9530, Energy: -28.712839+0.003701j
[2025-08-24 03:19:10] [Iter 1219/4650] R3[168/1200], Temp: 0.9524, Energy: -28.715603+0.001478j
[2025-08-24 03:19:20] [Iter 1220/4650] R3[169/1200], Temp: 0.9519, Energy: -28.716686+0.001040j
[2025-08-24 03:19:30] [Iter 1221/4650] R3[170/1200], Temp: 0.9513, Energy: -28.705982+0.000861j
[2025-08-24 03:19:40] [Iter 1222/4650] R3[171/1200], Temp: 0.9507, Energy: -28.717022-0.000883j
[2025-08-24 03:19:51] [Iter 1223/4650] R3[172/1200], Temp: 0.9502, Energy: -28.719765+0.001099j
[2025-08-24 03:20:01] [Iter 1224/4650] R3[173/1200], Temp: 0.9496, Energy: -28.726462+0.000031j
[2025-08-24 03:20:11] [Iter 1225/4650] R3[174/1200], Temp: 0.9490, Energy: -28.715610-0.001261j
[2025-08-24 03:20:21] [Iter 1226/4650] R3[175/1200], Temp: 0.9484, Energy: -28.724406+0.000753j
[2025-08-24 03:20:31] [Iter 1227/4650] R3[176/1200], Temp: 0.9479, Energy: -28.719970+0.000868j
[2025-08-24 03:20:41] [Iter 1228/4650] R3[177/1200], Temp: 0.9473, Energy: -28.719044+0.001304j
[2025-08-24 03:20:51] [Iter 1229/4650] R3[178/1200], Temp: 0.9467, Energy: -28.723955-0.000678j
[2025-08-24 03:21:02] [Iter 1230/4650] R3[179/1200], Temp: 0.9461, Energy: -28.720788+0.000438j
[2025-08-24 03:21:12] [Iter 1231/4650] R3[180/1200], Temp: 0.9455, Energy: -28.724699-0.000453j
[2025-08-24 03:21:22] [Iter 1232/4650] R3[181/1200], Temp: 0.9449, Energy: -28.715192+0.000706j
[2025-08-24 03:21:32] [Iter 1233/4650] R3[182/1200], Temp: 0.9443, Energy: -28.718196+0.001856j
[2025-08-24 03:21:42] [Iter 1234/4650] R3[183/1200], Temp: 0.9437, Energy: -28.712381-0.000755j
[2025-08-24 03:21:52] [Iter 1235/4650] R3[184/1200], Temp: 0.9431, Energy: -28.715600-0.001622j
[2025-08-24 03:22:02] [Iter 1236/4650] R3[185/1200], Temp: 0.9425, Energy: -28.717966+0.000105j
[2025-08-24 03:22:13] [Iter 1237/4650] R3[186/1200], Temp: 0.9419, Energy: -28.718933+0.000319j
[2025-08-24 03:22:23] [Iter 1238/4650] R3[187/1200], Temp: 0.9413, Energy: -28.719437+0.001293j
[2025-08-24 03:22:33] [Iter 1239/4650] R3[188/1200], Temp: 0.9407, Energy: -28.713147+0.001243j
[2025-08-24 03:22:43] [Iter 1240/4650] R3[189/1200], Temp: 0.9400, Energy: -28.717881-0.001368j
[2025-08-24 03:22:53] [Iter 1241/4650] R3[190/1200], Temp: 0.9394, Energy: -28.723434-0.001137j
[2025-08-24 03:23:03] [Iter 1242/4650] R3[191/1200], Temp: 0.9388, Energy: -28.710000+0.002692j
[2025-08-24 03:23:13] [Iter 1243/4650] R3[192/1200], Temp: 0.9382, Energy: -28.721748+0.000108j
[2025-08-24 03:23:24] [Iter 1244/4650] R3[193/1200], Temp: 0.9375, Energy: -28.724190-0.001745j
[2025-08-24 03:23:34] [Iter 1245/4650] R3[194/1200], Temp: 0.9369, Energy: -28.720040-0.001774j
[2025-08-24 03:23:44] [Iter 1246/4650] R3[195/1200], Temp: 0.9362, Energy: -28.717038+0.000233j
[2025-08-24 03:23:54] [Iter 1247/4650] R3[196/1200], Temp: 0.9356, Energy: -28.725086+0.004466j
[2025-08-24 03:24:04] [Iter 1248/4650] R3[197/1200], Temp: 0.9350, Energy: -28.720871-0.002821j
[2025-08-24 03:24:14] [Iter 1249/4650] R3[198/1200], Temp: 0.9343, Energy: -28.723434+0.000832j
[2025-08-24 03:24:24] [Iter 1250/4650] R3[199/1200], Temp: 0.9337, Energy: -28.720695+0.001909j
[2025-08-24 03:24:35] [Iter 1251/4650] R3[200/1200], Temp: 0.9330, Energy: -28.717989-0.000693j
[2025-08-24 03:24:45] [Iter 1252/4650] R3[201/1200], Temp: 0.9324, Energy: -28.720346+0.003656j
[2025-08-24 03:24:55] [Iter 1253/4650] R3[202/1200], Temp: 0.9317, Energy: -28.721222+0.002644j
[2025-08-24 03:25:05] [Iter 1254/4650] R3[203/1200], Temp: 0.9310, Energy: -28.715581-0.000738j
[2025-08-24 03:25:15] [Iter 1255/4650] R3[204/1200], Temp: 0.9304, Energy: -28.718744-0.003054j
[2025-08-24 03:25:25] [Iter 1256/4650] R3[205/1200], Temp: 0.9297, Energy: -28.712463+0.001356j
[2025-08-24 03:25:35] [Iter 1257/4650] R3[206/1200], Temp: 0.9290, Energy: -28.718597-0.000118j
[2025-08-24 03:25:46] [Iter 1258/4650] R3[207/1200], Temp: 0.9284, Energy: -28.717483+0.001061j
[2025-08-24 03:25:56] [Iter 1259/4650] R3[208/1200], Temp: 0.9277, Energy: -28.720418-0.000955j
[2025-08-24 03:26:06] [Iter 1260/4650] R3[209/1200], Temp: 0.9270, Energy: -28.725032+0.000568j
[2025-08-24 03:26:16] [Iter 1261/4650] R3[210/1200], Temp: 0.9263, Energy: -28.714886-0.000084j
[2025-08-24 03:26:26] [Iter 1262/4650] R3[211/1200], Temp: 0.9256, Energy: -28.721011+0.000585j
[2025-08-24 03:26:36] [Iter 1263/4650] R3[212/1200], Temp: 0.9249, Energy: -28.718811-0.000778j
[2025-08-24 03:26:47] [Iter 1264/4650] R3[213/1200], Temp: 0.9243, Energy: -28.720621-0.001274j
[2025-08-24 03:26:57] [Iter 1265/4650] R3[214/1200], Temp: 0.9236, Energy: -28.725956+0.002450j
[2025-08-24 03:27:07] [Iter 1266/4650] R3[215/1200], Temp: 0.9229, Energy: -28.723407+0.000785j
[2025-08-24 03:27:17] [Iter 1267/4650] R3[216/1200], Temp: 0.9222, Energy: -28.720148-0.001122j
[2025-08-24 03:27:27] [Iter 1268/4650] R3[217/1200], Temp: 0.9215, Energy: -28.726597+0.000940j
[2025-08-24 03:27:37] [Iter 1269/4650] R3[218/1200], Temp: 0.9208, Energy: -28.722372-0.000130j
[2025-08-24 03:27:48] [Iter 1270/4650] R3[219/1200], Temp: 0.9200, Energy: -28.720246-0.000337j
[2025-08-24 03:27:58] [Iter 1271/4650] R3[220/1200], Temp: 0.9193, Energy: -28.729745-0.005071j
[2025-08-24 03:28:08] [Iter 1272/4650] R3[221/1200], Temp: 0.9186, Energy: -28.720046-0.000596j
[2025-08-24 03:28:18] [Iter 1273/4650] R3[222/1200], Temp: 0.9179, Energy: -28.720414-0.003160j
[2025-08-24 03:28:28] [Iter 1274/4650] R3[223/1200], Temp: 0.9172, Energy: -28.732612-0.000304j
[2025-08-24 03:28:38] [Iter 1275/4650] R3[224/1200], Temp: 0.9165, Energy: -28.721006-0.002343j
[2025-08-24 03:28:48] [Iter 1276/4650] R3[225/1200], Temp: 0.9157, Energy: -28.731537+0.004924j
[2025-08-24 03:28:59] [Iter 1277/4650] R3[226/1200], Temp: 0.9150, Energy: -28.725038-0.000383j
[2025-08-24 03:29:09] [Iter 1278/4650] R3[227/1200], Temp: 0.9143, Energy: -28.724766-0.001864j
[2025-08-24 03:29:19] [Iter 1279/4650] R3[228/1200], Temp: 0.9135, Energy: -28.720129-0.000649j
[2025-08-24 03:29:29] [Iter 1280/4650] R3[229/1200], Temp: 0.9128, Energy: -28.726964+0.001932j
[2025-08-24 03:29:39] [Iter 1281/4650] R3[230/1200], Temp: 0.9121, Energy: -28.720940+0.000998j
[2025-08-24 03:29:49] [Iter 1282/4650] R3[231/1200], Temp: 0.9113, Energy: -28.721539+0.001086j
[2025-08-24 03:30:00] [Iter 1283/4650] R3[232/1200], Temp: 0.9106, Energy: -28.717112-0.000566j
[2025-08-24 03:30:10] [Iter 1284/4650] R3[233/1200], Temp: 0.9098, Energy: -28.718488+0.001427j
[2025-08-24 03:30:20] [Iter 1285/4650] R3[234/1200], Temp: 0.9091, Energy: -28.721641+0.002793j
[2025-08-24 03:30:30] [Iter 1286/4650] R3[235/1200], Temp: 0.9083, Energy: -28.714380+0.001041j
[2025-08-24 03:30:40] [Iter 1287/4650] R3[236/1200], Temp: 0.9076, Energy: -28.715614+0.002513j
[2025-08-24 03:30:50] [Iter 1288/4650] R3[237/1200], Temp: 0.9068, Energy: -28.726999+0.001535j
[2025-08-24 03:31:00] [Iter 1289/4650] R3[238/1200], Temp: 0.9060, Energy: -28.715232-0.001455j
[2025-08-24 03:31:11] [Iter 1290/4650] R3[239/1200], Temp: 0.9053, Energy: -28.728025+0.001071j
[2025-08-24 03:31:21] [Iter 1291/4650] R3[240/1200], Temp: 0.9045, Energy: -28.720655-0.000799j
[2025-08-24 03:31:31] [Iter 1292/4650] R3[241/1200], Temp: 0.9037, Energy: -28.722158+0.001809j
[2025-08-24 03:31:41] [Iter 1293/4650] R3[242/1200], Temp: 0.9030, Energy: -28.720509+0.000893j
[2025-08-24 03:31:51] [Iter 1294/4650] R3[243/1200], Temp: 0.9022, Energy: -28.716041-0.000479j
[2025-08-24 03:32:01] [Iter 1295/4650] R3[244/1200], Temp: 0.9014, Energy: -28.729077+0.000598j
[2025-08-24 03:32:12] [Iter 1296/4650] R3[245/1200], Temp: 0.9006, Energy: -28.721624+0.001009j
[2025-08-24 03:32:22] [Iter 1297/4650] R3[246/1200], Temp: 0.8998, Energy: -28.719533-0.001135j
[2025-08-24 03:32:32] [Iter 1298/4650] R3[247/1200], Temp: 0.8991, Energy: -28.722195-0.000358j
[2025-08-24 03:32:42] [Iter 1299/4650] R3[248/1200], Temp: 0.8983, Energy: -28.719247+0.001879j
[2025-08-24 03:32:52] [Iter 1300/4650] R3[249/1200], Temp: 0.8975, Energy: -28.715476-0.000920j
[2025-08-24 03:33:02] [Iter 1301/4650] R3[250/1200], Temp: 0.8967, Energy: -28.715933-0.003616j
[2025-08-24 03:33:13] [Iter 1302/4650] R3[251/1200], Temp: 0.8959, Energy: -28.715582-0.001251j
[2025-08-24 03:33:23] [Iter 1303/4650] R3[252/1200], Temp: 0.8951, Energy: -28.716458+0.001292j
[2025-08-24 03:33:33] [Iter 1304/4650] R3[253/1200], Temp: 0.8943, Energy: -28.719443-0.000739j
[2025-08-24 03:33:43] [Iter 1305/4650] R3[254/1200], Temp: 0.8935, Energy: -28.722676-0.002439j
[2025-08-24 03:33:53] [Iter 1306/4650] R3[255/1200], Temp: 0.8927, Energy: -28.718375-0.000156j
[2025-08-24 03:34:03] [Iter 1307/4650] R3[256/1200], Temp: 0.8918, Energy: -28.718651+0.000893j
[2025-08-24 03:34:14] [Iter 1308/4650] R3[257/1200], Temp: 0.8910, Energy: -28.727938+0.003499j
[2025-08-24 03:34:24] [Iter 1309/4650] R3[258/1200], Temp: 0.8902, Energy: -28.724119-0.000799j
[2025-08-24 03:34:34] [Iter 1310/4650] R3[259/1200], Temp: 0.8894, Energy: -28.723623-0.002052j
[2025-08-24 03:34:44] [Iter 1311/4650] R3[260/1200], Temp: 0.8886, Energy: -28.722959+0.002788j
[2025-08-24 03:34:54] [Iter 1312/4650] R3[261/1200], Temp: 0.8877, Energy: -28.714882+0.001129j
[2025-08-24 03:35:04] [Iter 1313/4650] R3[262/1200], Temp: 0.8869, Energy: -28.718454-0.003145j
[2025-08-24 03:35:14] [Iter 1314/4650] R3[263/1200], Temp: 0.8861, Energy: -28.722567+0.001864j
[2025-08-24 03:35:25] [Iter 1315/4650] R3[264/1200], Temp: 0.8853, Energy: -28.725453-0.002292j
[2025-08-24 03:35:35] [Iter 1316/4650] R3[265/1200], Temp: 0.8844, Energy: -28.722900+0.001903j
[2025-08-24 03:35:45] [Iter 1317/4650] R3[266/1200], Temp: 0.8836, Energy: -28.720825-0.000196j
[2025-08-24 03:35:55] [Iter 1318/4650] R3[267/1200], Temp: 0.8827, Energy: -28.717264-0.001040j
[2025-08-24 03:36:05] [Iter 1319/4650] R3[268/1200], Temp: 0.8819, Energy: -28.719115+0.000653j
[2025-08-24 03:36:15] [Iter 1320/4650] R3[269/1200], Temp: 0.8811, Energy: -28.718467-0.000104j
[2025-08-24 03:36:25] [Iter 1321/4650] R3[270/1200], Temp: 0.8802, Energy: -28.719603-0.000172j
[2025-08-24 03:36:36] [Iter 1322/4650] R3[271/1200], Temp: 0.8794, Energy: -28.721512+0.000831j
[2025-08-24 03:36:46] [Iter 1323/4650] R3[272/1200], Temp: 0.8785, Energy: -28.713449+0.000451j
[2025-08-24 03:36:56] [Iter 1324/4650] R3[273/1200], Temp: 0.8776, Energy: -28.724109+0.000597j
[2025-08-24 03:37:06] [Iter 1325/4650] R3[274/1200], Temp: 0.8768, Energy: -28.721465-0.001094j
[2025-08-24 03:37:16] [Iter 1326/4650] R3[275/1200], Temp: 0.8759, Energy: -28.725930-0.001136j
[2025-08-24 03:37:26] [Iter 1327/4650] R3[276/1200], Temp: 0.8751, Energy: -28.714044+0.000450j
[2025-08-24 03:37:36] [Iter 1328/4650] R3[277/1200], Temp: 0.8742, Energy: -28.716619-0.001970j
[2025-08-24 03:37:47] [Iter 1329/4650] R3[278/1200], Temp: 0.8733, Energy: -28.730106+0.000958j
[2025-08-24 03:37:57] [Iter 1330/4650] R3[279/1200], Temp: 0.8724, Energy: -28.724066-0.000878j
[2025-08-24 03:38:07] [Iter 1331/4650] R3[280/1200], Temp: 0.8716, Energy: -28.717593-0.001283j
[2025-08-24 03:38:17] [Iter 1332/4650] R3[281/1200], Temp: 0.8707, Energy: -28.717491+0.001986j
[2025-08-24 03:38:27] [Iter 1333/4650] R3[282/1200], Temp: 0.8698, Energy: -28.714700+0.002404j
[2025-08-24 03:38:37] [Iter 1334/4650] R3[283/1200], Temp: 0.8689, Energy: -28.729700+0.001734j
[2025-08-24 03:38:47] [Iter 1335/4650] R3[284/1200], Temp: 0.8680, Energy: -28.722141+0.002533j
[2025-08-24 03:38:58] [Iter 1336/4650] R3[285/1200], Temp: 0.8672, Energy: -28.715928+0.002597j
[2025-08-24 03:39:08] [Iter 1337/4650] R3[286/1200], Temp: 0.8663, Energy: -28.717811+0.000927j
[2025-08-24 03:39:18] [Iter 1338/4650] R3[287/1200], Temp: 0.8654, Energy: -28.718057-0.000708j
[2025-08-24 03:39:28] [Iter 1339/4650] R3[288/1200], Temp: 0.8645, Energy: -28.717619+0.000471j
[2025-08-24 03:39:38] [Iter 1340/4650] R3[289/1200], Temp: 0.8636, Energy: -28.727079-0.000349j
[2025-08-24 03:39:48] [Iter 1341/4650] R3[290/1200], Temp: 0.8627, Energy: -28.721583-0.001210j
[2025-08-24 03:39:59] [Iter 1342/4650] R3[291/1200], Temp: 0.8618, Energy: -28.716537+0.000026j
[2025-08-24 03:40:09] [Iter 1343/4650] R3[292/1200], Temp: 0.8609, Energy: -28.715854+0.000051j
[2025-08-24 03:40:19] [Iter 1344/4650] R3[293/1200], Temp: 0.8600, Energy: -28.720326+0.001590j
[2025-08-24 03:40:29] [Iter 1345/4650] R3[294/1200], Temp: 0.8591, Energy: -28.722400+0.003217j
[2025-08-24 03:40:39] [Iter 1346/4650] R3[295/1200], Temp: 0.8582, Energy: -28.725744+0.002233j
[2025-08-24 03:40:49] [Iter 1347/4650] R3[296/1200], Temp: 0.8572, Energy: -28.719808+0.001500j
[2025-08-24 03:40:59] [Iter 1348/4650] R3[297/1200], Temp: 0.8563, Energy: -28.723695+0.002529j
[2025-08-24 03:41:10] [Iter 1349/4650] R3[298/1200], Temp: 0.8554, Energy: -28.728934-0.000957j
[2025-08-24 03:41:20] [Iter 1350/4650] R3[299/1200], Temp: 0.8545, Energy: -28.719243+0.001561j
[2025-08-24 03:41:30] [Iter 1351/4650] R3[300/1200], Temp: 0.8536, Energy: -28.723844+0.002046j
[2025-08-24 03:41:40] [Iter 1352/4650] R3[301/1200], Temp: 0.8526, Energy: -28.723615-0.001018j
[2025-08-24 03:41:50] [Iter 1353/4650] R3[302/1200], Temp: 0.8517, Energy: -28.719415-0.003166j
[2025-08-24 03:42:00] [Iter 1354/4650] R3[303/1200], Temp: 0.8508, Energy: -28.717180-0.003103j
[2025-08-24 03:42:10] [Iter 1355/4650] R3[304/1200], Temp: 0.8498, Energy: -28.727668+0.001150j
[2025-08-24 03:42:20] [Iter 1356/4650] R3[305/1200], Temp: 0.8489, Energy: -28.727764-0.000327j
[2025-08-24 03:42:31] [Iter 1357/4650] R3[306/1200], Temp: 0.8480, Energy: -28.721766-0.001725j
[2025-08-24 03:42:41] [Iter 1358/4650] R3[307/1200], Temp: 0.8470, Energy: -28.717728+0.000361j
[2025-08-24 03:42:51] [Iter 1359/4650] R3[308/1200], Temp: 0.8461, Energy: -28.714503-0.001763j
[2025-08-24 03:43:01] [Iter 1360/4650] R3[309/1200], Temp: 0.8451, Energy: -28.721881+0.000746j
[2025-08-24 03:43:11] [Iter 1361/4650] R3[310/1200], Temp: 0.8442, Energy: -28.718981-0.001906j
[2025-08-24 03:43:21] [Iter 1362/4650] R3[311/1200], Temp: 0.8432, Energy: -28.722805-0.002802j
[2025-08-24 03:43:32] [Iter 1363/4650] R3[312/1200], Temp: 0.8423, Energy: -28.721807-0.000630j
[2025-08-24 03:43:42] [Iter 1364/4650] R3[313/1200], Temp: 0.8413, Energy: -28.720799-0.001918j
[2025-08-24 03:43:52] [Iter 1365/4650] R3[314/1200], Temp: 0.8404, Energy: -28.727608-0.001232j
[2025-08-24 03:44:02] [Iter 1366/4650] R3[315/1200], Temp: 0.8394, Energy: -28.723138+0.003715j
[2025-08-24 03:44:12] [Iter 1367/4650] R3[316/1200], Temp: 0.8384, Energy: -28.725124-0.001895j
[2025-08-24 03:44:22] [Iter 1368/4650] R3[317/1200], Temp: 0.8375, Energy: -28.718468+0.001191j
[2025-08-24 03:44:32] [Iter 1369/4650] R3[318/1200], Temp: 0.8365, Energy: -28.721873+0.000411j
[2025-08-24 03:44:43] [Iter 1370/4650] R3[319/1200], Temp: 0.8355, Energy: -28.719241-0.001387j
[2025-08-24 03:44:53] [Iter 1371/4650] R3[320/1200], Temp: 0.8346, Energy: -28.728576+0.000867j
[2025-08-24 03:45:03] [Iter 1372/4650] R3[321/1200], Temp: 0.8336, Energy: -28.716265-0.000866j
[2025-08-24 03:45:13] [Iter 1373/4650] R3[322/1200], Temp: 0.8326, Energy: -28.722214-0.001093j
[2025-08-24 03:45:23] [Iter 1374/4650] R3[323/1200], Temp: 0.8316, Energy: -28.724135+0.002012j
[2025-08-24 03:45:33] [Iter 1375/4650] R3[324/1200], Temp: 0.8307, Energy: -28.728367+0.000222j
[2025-08-24 03:45:43] [Iter 1376/4650] R3[325/1200], Temp: 0.8297, Energy: -28.722520+0.000131j
[2025-08-24 03:45:54] [Iter 1377/4650] R3[326/1200], Temp: 0.8287, Energy: -28.723608+0.001832j
[2025-08-24 03:46:04] [Iter 1378/4650] R3[327/1200], Temp: 0.8277, Energy: -28.725402-0.002271j
[2025-08-24 03:46:14] [Iter 1379/4650] R3[328/1200], Temp: 0.8267, Energy: -28.724134+0.001924j
[2025-08-24 03:46:24] [Iter 1380/4650] R3[329/1200], Temp: 0.8257, Energy: -28.729746-0.003176j
[2025-08-24 03:46:34] [Iter 1381/4650] R3[330/1200], Temp: 0.8247, Energy: -28.726605+0.004323j
[2025-08-24 03:46:44] [Iter 1382/4650] R3[331/1200], Temp: 0.8237, Energy: -28.723019-0.001512j
[2025-08-24 03:46:54] [Iter 1383/4650] R3[332/1200], Temp: 0.8227, Energy: -28.722769+0.003987j
[2025-08-24 03:47:05] [Iter 1384/4650] R3[333/1200], Temp: 0.8217, Energy: -28.719596-0.000250j
[2025-08-24 03:47:15] [Iter 1385/4650] R3[334/1200], Temp: 0.8207, Energy: -28.718006-0.000445j
[2025-08-24 03:47:25] [Iter 1386/4650] R3[335/1200], Temp: 0.8197, Energy: -28.719220+0.002605j
[2025-08-24 03:47:35] [Iter 1387/4650] R3[336/1200], Temp: 0.8187, Energy: -28.725847-0.000491j
[2025-08-24 03:47:45] [Iter 1388/4650] R3[337/1200], Temp: 0.8177, Energy: -28.717486+0.000652j
[2025-08-24 03:47:55] [Iter 1389/4650] R3[338/1200], Temp: 0.8167, Energy: -28.719962-0.004229j
[2025-08-24 03:48:05] [Iter 1390/4650] R3[339/1200], Temp: 0.8157, Energy: -28.727305-0.004031j
[2025-08-24 03:48:16] [Iter 1391/4650] R3[340/1200], Temp: 0.8147, Energy: -28.723042+0.000399j
[2025-08-24 03:48:26] [Iter 1392/4650] R3[341/1200], Temp: 0.8136, Energy: -28.717009+0.002450j
[2025-08-24 03:48:36] [Iter 1393/4650] R3[342/1200], Temp: 0.8126, Energy: -28.717073+0.000171j
[2025-08-24 03:48:46] [Iter 1394/4650] R3[343/1200], Temp: 0.8116, Energy: -28.730991+0.005258j
[2025-08-24 03:48:56] [Iter 1395/4650] R3[344/1200], Temp: 0.8106, Energy: -28.721243+0.001182j
[2025-08-24 03:49:06] [Iter 1396/4650] R3[345/1200], Temp: 0.8095, Energy: -28.722236-0.002374j
[2025-08-24 03:49:16] [Iter 1397/4650] R3[346/1200], Temp: 0.8085, Energy: -28.724016+0.001918j
[2025-08-24 03:49:27] [Iter 1398/4650] R3[347/1200], Temp: 0.8075, Energy: -28.729400-0.000577j
[2025-08-24 03:49:37] [Iter 1399/4650] R3[348/1200], Temp: 0.8065, Energy: -28.722545-0.003740j
[2025-08-24 03:49:47] [Iter 1400/4650] R3[349/1200], Temp: 0.8054, Energy: -28.727094-0.001456j
[2025-08-24 03:49:57] [Iter 1401/4650] R3[350/1200], Temp: 0.8044, Energy: -28.718864-0.001561j
[2025-08-24 03:50:07] [Iter 1402/4650] R3[351/1200], Temp: 0.8033, Energy: -28.721090-0.003053j
[2025-08-24 03:50:17] [Iter 1403/4650] R3[352/1200], Temp: 0.8023, Energy: -28.722191+0.000511j
[2025-08-24 03:50:27] [Iter 1404/4650] R3[353/1200], Temp: 0.8013, Energy: -28.720219-0.001328j
[2025-08-24 03:50:38] [Iter 1405/4650] R3[354/1200], Temp: 0.8002, Energy: -28.724264-0.000269j
[2025-08-24 03:50:48] [Iter 1406/4650] R3[355/1200], Temp: 0.7992, Energy: -28.733081+0.004487j
[2025-08-24 03:50:58] [Iter 1407/4650] R3[356/1200], Temp: 0.7981, Energy: -28.720583-0.002528j
[2025-08-24 03:51:08] [Iter 1408/4650] R3[357/1200], Temp: 0.7971, Energy: -28.721394+0.001289j
[2025-08-24 03:51:18] [Iter 1409/4650] R3[358/1200], Temp: 0.7960, Energy: -28.723132+0.000025j
[2025-08-24 03:51:28] [Iter 1410/4650] R3[359/1200], Temp: 0.7950, Energy: -28.720556-0.001707j
[2025-08-24 03:51:38] [Iter 1411/4650] R3[360/1200], Temp: 0.7939, Energy: -28.721640-0.003810j
[2025-08-24 03:51:49] [Iter 1412/4650] R3[361/1200], Temp: 0.7928, Energy: -28.727156-0.000073j
[2025-08-24 03:51:59] [Iter 1413/4650] R3[362/1200], Temp: 0.7918, Energy: -28.725168+0.001022j
[2025-08-24 03:52:09] [Iter 1414/4650] R3[363/1200], Temp: 0.7907, Energy: -28.715513-0.000873j
[2025-08-24 03:52:19] [Iter 1415/4650] R3[364/1200], Temp: 0.7896, Energy: -28.726485-0.002018j
[2025-08-24 03:52:29] [Iter 1416/4650] R3[365/1200], Temp: 0.7886, Energy: -28.727288-0.001026j
[2025-08-24 03:52:39] [Iter 1417/4650] R3[366/1200], Temp: 0.7875, Energy: -28.725132+0.001659j
[2025-08-24 03:52:49] [Iter 1418/4650] R3[367/1200], Temp: 0.7864, Energy: -28.725858-0.000937j
[2025-08-24 03:52:59] [Iter 1419/4650] R3[368/1200], Temp: 0.7854, Energy: -28.723283+0.000702j
[2025-08-24 03:53:10] [Iter 1420/4650] R3[369/1200], Temp: 0.7843, Energy: -28.718637-0.001290j
[2025-08-24 03:53:20] [Iter 1421/4650] R3[370/1200], Temp: 0.7832, Energy: -28.718933+0.000060j
[2025-08-24 03:53:30] [Iter 1422/4650] R3[371/1200], Temp: 0.7821, Energy: -28.720894-0.000199j
[2025-08-24 03:53:40] [Iter 1423/4650] R3[372/1200], Temp: 0.7810, Energy: -28.726106-0.005722j
[2025-08-24 03:53:50] [Iter 1424/4650] R3[373/1200], Temp: 0.7800, Energy: -28.731638-0.001121j
[2025-08-24 03:54:00] [Iter 1425/4650] R3[374/1200], Temp: 0.7789, Energy: -28.724377+0.000346j
[2025-08-24 03:54:10] [Iter 1426/4650] R3[375/1200], Temp: 0.7778, Energy: -28.732141+0.000570j
[2025-08-24 03:54:21] [Iter 1427/4650] R3[376/1200], Temp: 0.7767, Energy: -28.721491+0.001017j
[2025-08-24 03:54:31] [Iter 1428/4650] R3[377/1200], Temp: 0.7756, Energy: -28.727965+0.000189j
[2025-08-24 03:54:41] [Iter 1429/4650] R3[378/1200], Temp: 0.7745, Energy: -28.737468+0.001725j
[2025-08-24 03:54:51] [Iter 1430/4650] R3[379/1200], Temp: 0.7734, Energy: -28.731171+0.000033j
[2025-08-24 03:55:01] [Iter 1431/4650] R3[380/1200], Temp: 0.7723, Energy: -28.731906-0.004053j
[2025-08-24 03:55:11] [Iter 1432/4650] R3[381/1200], Temp: 0.7712, Energy: -28.724734+0.001970j
[2025-08-24 03:55:21] [Iter 1433/4650] R3[382/1200], Temp: 0.7701, Energy: -28.728031+0.002097j
[2025-08-24 03:55:32] [Iter 1434/4650] R3[383/1200], Temp: 0.7690, Energy: -28.730703-0.000280j
[2025-08-24 03:55:42] [Iter 1435/4650] R3[384/1200], Temp: 0.7679, Energy: -28.721570+0.000084j
[2025-08-24 03:55:52] [Iter 1436/4650] R3[385/1200], Temp: 0.7668, Energy: -28.726479-0.002147j
[2025-08-24 03:56:02] [Iter 1437/4650] R3[386/1200], Temp: 0.7657, Energy: -28.721833-0.000865j
[2025-08-24 03:56:12] [Iter 1438/4650] R3[387/1200], Temp: 0.7646, Energy: -28.723657+0.000184j
[2025-08-24 03:56:22] [Iter 1439/4650] R3[388/1200], Temp: 0.7635, Energy: -28.724308-0.000125j
[2025-08-24 03:56:32] [Iter 1440/4650] R3[389/1200], Temp: 0.7624, Energy: -28.723964-0.001668j
[2025-08-24 03:56:43] [Iter 1441/4650] R3[390/1200], Temp: 0.7612, Energy: -28.724600-0.000335j
[2025-08-24 03:56:53] [Iter 1442/4650] R3[391/1200], Temp: 0.7601, Energy: -28.728684-0.002834j
[2025-08-24 03:57:03] [Iter 1443/4650] R3[392/1200], Temp: 0.7590, Energy: -28.722904-0.000134j
[2025-08-24 03:57:13] [Iter 1444/4650] R3[393/1200], Temp: 0.7579, Energy: -28.725644-0.004198j
[2025-08-24 03:57:23] [Iter 1445/4650] R3[394/1200], Temp: 0.7568, Energy: -28.718208+0.001163j
[2025-08-24 03:57:33] [Iter 1446/4650] R3[395/1200], Temp: 0.7556, Energy: -28.729559-0.001602j
[2025-08-24 03:57:43] [Iter 1447/4650] R3[396/1200], Temp: 0.7545, Energy: -28.724650-0.004095j
[2025-08-24 03:57:54] [Iter 1448/4650] R3[397/1200], Temp: 0.7534, Energy: -28.722396-0.000178j
[2025-08-24 03:58:04] [Iter 1449/4650] R3[398/1200], Temp: 0.7523, Energy: -28.723570-0.001969j
[2025-08-24 03:58:14] [Iter 1450/4650] R3[399/1200], Temp: 0.7511, Energy: -28.727508+0.002842j
[2025-08-24 03:58:24] [Iter 1451/4650] R3[400/1200], Temp: 0.7500, Energy: -28.724903-0.000261j
[2025-08-24 03:58:34] [Iter 1452/4650] R3[401/1200], Temp: 0.7489, Energy: -28.722684+0.001460j
[2025-08-24 03:58:44] [Iter 1453/4650] R3[402/1200], Temp: 0.7477, Energy: -28.728300+0.000268j
[2025-08-24 03:58:54] [Iter 1454/4650] R3[403/1200], Temp: 0.7466, Energy: -28.723688-0.002003j
[2025-08-24 03:59:04] [Iter 1455/4650] R3[404/1200], Temp: 0.7455, Energy: -28.726893+0.000370j
[2025-08-24 03:59:15] [Iter 1456/4650] R3[405/1200], Temp: 0.7443, Energy: -28.726413-0.002288j
[2025-08-24 03:59:25] [Iter 1457/4650] R3[406/1200], Temp: 0.7432, Energy: -28.729164-0.002187j
[2025-08-24 03:59:35] [Iter 1458/4650] R3[407/1200], Temp: 0.7420, Energy: -28.725228-0.000918j
[2025-08-24 03:59:45] [Iter 1459/4650] R3[408/1200], Temp: 0.7409, Energy: -28.728035+0.001764j
[2025-08-24 03:59:55] [Iter 1460/4650] R3[409/1200], Temp: 0.7397, Energy: -28.719285-0.000111j
[2025-08-24 04:00:05] [Iter 1461/4650] R3[410/1200], Temp: 0.7386, Energy: -28.725660+0.002348j
[2025-08-24 04:00:15] [Iter 1462/4650] R3[411/1200], Temp: 0.7374, Energy: -28.726413-0.000780j
[2025-08-24 04:00:26] [Iter 1463/4650] R3[412/1200], Temp: 0.7363, Energy: -28.728330-0.000674j
[2025-08-24 04:00:36] [Iter 1464/4650] R3[413/1200], Temp: 0.7351, Energy: -28.723159-0.002714j
[2025-08-24 04:00:46] [Iter 1465/4650] R3[414/1200], Temp: 0.7340, Energy: -28.731202-0.000976j
[2025-08-24 04:00:56] [Iter 1466/4650] R3[415/1200], Temp: 0.7328, Energy: -28.724260+0.000760j
[2025-08-24 04:01:06] [Iter 1467/4650] R3[416/1200], Temp: 0.7316, Energy: -28.730355-0.001090j
[2025-08-24 04:01:16] [Iter 1468/4650] R3[417/1200], Temp: 0.7305, Energy: -28.727199+0.001546j
[2025-08-24 04:01:26] [Iter 1469/4650] R3[418/1200], Temp: 0.7293, Energy: -28.725813+0.001879j
[2025-08-24 04:01:37] [Iter 1470/4650] R3[419/1200], Temp: 0.7282, Energy: -28.727902+0.001766j
[2025-08-24 04:01:47] [Iter 1471/4650] R3[420/1200], Temp: 0.7270, Energy: -28.730788+0.000801j
[2025-08-24 04:01:57] [Iter 1472/4650] R3[421/1200], Temp: 0.7258, Energy: -28.728849+0.001291j
[2025-08-24 04:02:07] [Iter 1473/4650] R3[422/1200], Temp: 0.7247, Energy: -28.725096+0.001389j
[2025-08-24 04:02:17] [Iter 1474/4650] R3[423/1200], Temp: 0.7235, Energy: -28.735109+0.000122j
[2025-08-24 04:02:27] [Iter 1475/4650] R3[424/1200], Temp: 0.7223, Energy: -28.727113+0.000036j
[2025-08-24 04:02:37] [Iter 1476/4650] R3[425/1200], Temp: 0.7211, Energy: -28.727049+0.002233j
[2025-08-24 04:02:48] [Iter 1477/4650] R3[426/1200], Temp: 0.7200, Energy: -28.737768-0.002168j
[2025-08-24 04:02:58] [Iter 1478/4650] R3[427/1200], Temp: 0.7188, Energy: -28.734182+0.000066j
[2025-08-24 04:03:08] [Iter 1479/4650] R3[428/1200], Temp: 0.7176, Energy: -28.728020-0.000002j
[2025-08-24 04:03:18] [Iter 1480/4650] R3[429/1200], Temp: 0.7164, Energy: -28.729543+0.000016j
[2025-08-24 04:03:28] [Iter 1481/4650] R3[430/1200], Temp: 0.7153, Energy: -28.734010-0.000989j
[2025-08-24 04:03:38] [Iter 1482/4650] R3[431/1200], Temp: 0.7141, Energy: -28.726803+0.000173j
[2025-08-24 04:03:48] [Iter 1483/4650] R3[432/1200], Temp: 0.7129, Energy: -28.730093-0.002952j
[2025-08-24 04:03:59] [Iter 1484/4650] R3[433/1200], Temp: 0.7117, Energy: -28.727940-0.000738j
[2025-08-24 04:04:09] [Iter 1485/4650] R3[434/1200], Temp: 0.7105, Energy: -28.727322-0.000505j
[2025-08-24 04:04:19] [Iter 1486/4650] R3[435/1200], Temp: 0.7093, Energy: -28.726374-0.001300j
[2025-08-24 04:04:29] [Iter 1487/4650] R3[436/1200], Temp: 0.7081, Energy: -28.725610+0.000698j
[2025-08-24 04:04:39] [Iter 1488/4650] R3[437/1200], Temp: 0.7069, Energy: -28.734206-0.001085j
[2025-08-24 04:04:49] [Iter 1489/4650] R3[438/1200], Temp: 0.7058, Energy: -28.729183-0.002350j
[2025-08-24 04:04:59] [Iter 1490/4650] R3[439/1200], Temp: 0.7046, Energy: -28.722591+0.001041j
[2025-08-24 04:05:10] [Iter 1491/4650] R3[440/1200], Temp: 0.7034, Energy: -28.725689-0.001268j
[2025-08-24 04:05:20] [Iter 1492/4650] R3[441/1200], Temp: 0.7022, Energy: -28.723216+0.001130j
[2025-08-24 04:05:30] [Iter 1493/4650] R3[442/1200], Temp: 0.7010, Energy: -28.721692-0.001076j
[2025-08-24 04:05:40] [Iter 1494/4650] R3[443/1200], Temp: 0.6998, Energy: -28.725437-0.002553j
[2025-08-24 04:05:50] [Iter 1495/4650] R3[444/1200], Temp: 0.6986, Energy: -28.734898-0.001455j
[2025-08-24 04:06:00] [Iter 1496/4650] R3[445/1200], Temp: 0.6974, Energy: -28.729011-0.001452j
[2025-08-24 04:06:10] [Iter 1497/4650] R3[446/1200], Temp: 0.6962, Energy: -28.724728+0.001096j
[2025-08-24 04:06:21] [Iter 1498/4650] R3[447/1200], Temp: 0.6950, Energy: -28.727129+0.001575j
[2025-08-24 04:06:31] [Iter 1499/4650] R3[448/1200], Temp: 0.6938, Energy: -28.727954-0.002448j
[2025-08-24 04:06:41] [Iter 1500/4650] R3[449/1200], Temp: 0.6926, Energy: -28.721480+0.003532j
[2025-08-24 04:06:41] ✓ Checkpoint saved: checkpoint_iter_001500.pkl
[2025-08-24 04:06:51] [Iter 1501/4650] R3[450/1200], Temp: 0.6913, Energy: -28.732096+0.000852j
[2025-08-24 04:07:01] [Iter 1502/4650] R3[451/1200], Temp: 0.6901, Energy: -28.726366+0.001315j
[2025-08-24 04:07:11] [Iter 1503/4650] R3[452/1200], Temp: 0.6889, Energy: -28.731488-0.000752j
[2025-08-24 04:07:22] [Iter 1504/4650] R3[453/1200], Temp: 0.6877, Energy: -28.726915+0.000677j
[2025-08-24 04:07:32] [Iter 1505/4650] R3[454/1200], Temp: 0.6865, Energy: -28.730405+0.001861j
[2025-08-24 04:07:42] [Iter 1506/4650] R3[455/1200], Temp: 0.6853, Energy: -28.723811-0.000346j
[2025-08-24 04:07:52] [Iter 1507/4650] R3[456/1200], Temp: 0.6841, Energy: -28.733171+0.002587j
[2025-08-24 04:08:02] [Iter 1508/4650] R3[457/1200], Temp: 0.6828, Energy: -28.732854-0.002179j
[2025-08-24 04:08:12] [Iter 1509/4650] R3[458/1200], Temp: 0.6816, Energy: -28.719067-0.002062j
[2025-08-24 04:08:22] [Iter 1510/4650] R3[459/1200], Temp: 0.6804, Energy: -28.723012+0.000707j
[2025-08-24 04:08:33] [Iter 1511/4650] R3[460/1200], Temp: 0.6792, Energy: -28.730665+0.000815j
[2025-08-24 04:08:43] [Iter 1512/4650] R3[461/1200], Temp: 0.6780, Energy: -28.725393-0.002101j
[2025-08-24 04:08:53] [Iter 1513/4650] R3[462/1200], Temp: 0.6767, Energy: -28.725879-0.000902j
[2025-08-24 04:09:03] [Iter 1514/4650] R3[463/1200], Temp: 0.6755, Energy: -28.720707-0.001109j
[2025-08-24 04:09:13] [Iter 1515/4650] R3[464/1200], Temp: 0.6743, Energy: -28.726901+0.002647j
[2025-08-24 04:09:23] [Iter 1516/4650] R3[465/1200], Temp: 0.6731, Energy: -28.728050+0.000481j
[2025-08-24 04:09:33] [Iter 1517/4650] R3[466/1200], Temp: 0.6718, Energy: -28.726695-0.003150j
[2025-08-24 04:09:44] [Iter 1518/4650] R3[467/1200], Temp: 0.6706, Energy: -28.734455-0.003350j
[2025-08-24 04:09:54] [Iter 1519/4650] R3[468/1200], Temp: 0.6694, Energy: -28.722936+0.001993j
[2025-08-24 04:10:04] [Iter 1520/4650] R3[469/1200], Temp: 0.6681, Energy: -28.728396+0.002350j
[2025-08-24 04:10:14] [Iter 1521/4650] R3[470/1200], Temp: 0.6669, Energy: -28.727837+0.000350j
[2025-08-24 04:10:24] [Iter 1522/4650] R3[471/1200], Temp: 0.6657, Energy: -28.723653+0.001453j
[2025-08-24 04:10:34] [Iter 1523/4650] R3[472/1200], Temp: 0.6644, Energy: -28.735292-0.002239j
[2025-08-24 04:10:45] [Iter 1524/4650] R3[473/1200], Temp: 0.6632, Energy: -28.728481-0.003366j
[2025-08-24 04:10:55] [Iter 1525/4650] R3[474/1200], Temp: 0.6620, Energy: -28.726290-0.000358j
[2025-08-24 04:11:05] [Iter 1526/4650] R3[475/1200], Temp: 0.6607, Energy: -28.724531-0.001946j
[2025-08-24 04:11:15] [Iter 1527/4650] R3[476/1200], Temp: 0.6595, Energy: -28.733939-0.003330j
[2025-08-24 04:11:25] [Iter 1528/4650] R3[477/1200], Temp: 0.6582, Energy: -28.732558-0.000529j
[2025-08-24 04:11:35] [Iter 1529/4650] R3[478/1200], Temp: 0.6570, Energy: -28.732428-0.000389j
[2025-08-24 04:11:46] [Iter 1530/4650] R3[479/1200], Temp: 0.6558, Energy: -28.726260+0.002732j
[2025-08-24 04:11:56] [Iter 1531/4650] R3[480/1200], Temp: 0.6545, Energy: -28.732700-0.002330j
[2025-08-24 04:12:06] [Iter 1532/4650] R3[481/1200], Temp: 0.6533, Energy: -28.726070-0.000299j
[2025-08-24 04:12:16] [Iter 1533/4650] R3[482/1200], Temp: 0.6520, Energy: -28.726513-0.001879j
[2025-08-24 04:12:26] [Iter 1534/4650] R3[483/1200], Temp: 0.6508, Energy: -28.729562-0.001112j
[2025-08-24 04:12:36] [Iter 1535/4650] R3[484/1200], Temp: 0.6495, Energy: -28.728073-0.001618j
[2025-08-24 04:12:47] [Iter 1536/4650] R3[485/1200], Temp: 0.6483, Energy: -28.719454+0.001050j
[2025-08-24 04:12:57] [Iter 1537/4650] R3[486/1200], Temp: 0.6470, Energy: -28.723627-0.001581j
[2025-08-24 04:13:07] [Iter 1538/4650] R3[487/1200], Temp: 0.6458, Energy: -28.727863-0.003396j
[2025-08-24 04:13:17] [Iter 1539/4650] R3[488/1200], Temp: 0.6445, Energy: -28.731109+0.000187j
[2025-08-24 04:13:27] [Iter 1540/4650] R3[489/1200], Temp: 0.6433, Energy: -28.729219+0.000698j
[2025-08-24 04:13:37] [Iter 1541/4650] R3[490/1200], Temp: 0.6420, Energy: -28.728291-0.000582j
[2025-08-24 04:13:47] [Iter 1542/4650] R3[491/1200], Temp: 0.6408, Energy: -28.731878-0.002540j
[2025-08-24 04:13:58] [Iter 1543/4650] R3[492/1200], Temp: 0.6395, Energy: -28.730979-0.000010j
[2025-08-24 04:14:08] [Iter 1544/4650] R3[493/1200], Temp: 0.6382, Energy: -28.728322+0.002210j
[2025-08-24 04:14:18] [Iter 1545/4650] R3[494/1200], Temp: 0.6370, Energy: -28.729538-0.002788j
[2025-08-24 04:14:28] [Iter 1546/4650] R3[495/1200], Temp: 0.6357, Energy: -28.731460-0.001452j
[2025-08-24 04:14:38] [Iter 1547/4650] R3[496/1200], Temp: 0.6345, Energy: -28.726479-0.002840j
[2025-08-24 04:14:48] [Iter 1548/4650] R3[497/1200], Temp: 0.6332, Energy: -28.732935-0.000264j
[2025-08-24 04:14:59] [Iter 1549/4650] R3[498/1200], Temp: 0.6319, Energy: -28.726288-0.002687j
[2025-08-24 04:15:09] [Iter 1550/4650] R3[499/1200], Temp: 0.6307, Energy: -28.727609-0.002079j
[2025-08-24 04:15:19] [Iter 1551/4650] R3[500/1200], Temp: 0.6294, Energy: -28.727753-0.000026j
[2025-08-24 04:15:29] [Iter 1552/4650] R3[501/1200], Temp: 0.6281, Energy: -28.730578-0.001505j
[2025-08-24 04:15:39] [Iter 1553/4650] R3[502/1200], Temp: 0.6269, Energy: -28.733526+0.000470j
[2025-08-24 04:15:49] [Iter 1554/4650] R3[503/1200], Temp: 0.6256, Energy: -28.722068+0.000047j
[2025-08-24 04:15:59] [Iter 1555/4650] R3[504/1200], Temp: 0.6243, Energy: -28.730801+0.001718j
[2025-08-24 04:16:10] [Iter 1556/4650] R3[505/1200], Temp: 0.6231, Energy: -28.731506-0.003415j
[2025-08-24 04:16:20] [Iter 1557/4650] R3[506/1200], Temp: 0.6218, Energy: -28.731245+0.000819j
[2025-08-24 04:16:30] [Iter 1558/4650] R3[507/1200], Temp: 0.6205, Energy: -28.724303+0.001043j
[2025-08-24 04:16:40] [Iter 1559/4650] R3[508/1200], Temp: 0.6193, Energy: -28.735134-0.000895j
[2025-08-24 04:16:50] [Iter 1560/4650] R3[509/1200], Temp: 0.6180, Energy: -28.725807-0.000873j
[2025-08-24 04:17:00] [Iter 1561/4650] R3[510/1200], Temp: 0.6167, Energy: -28.731373+0.000049j
[2025-08-24 04:17:10] [Iter 1562/4650] R3[511/1200], Temp: 0.6154, Energy: -28.726029-0.000615j
[2025-08-24 04:17:21] [Iter 1563/4650] R3[512/1200], Temp: 0.6142, Energy: -28.724674-0.002403j
[2025-08-24 04:17:31] [Iter 1564/4650] R3[513/1200], Temp: 0.6129, Energy: -28.727669+0.001258j
[2025-08-24 04:17:41] [Iter 1565/4650] R3[514/1200], Temp: 0.6116, Energy: -28.735643-0.000030j
[2025-08-24 04:17:51] [Iter 1566/4650] R3[515/1200], Temp: 0.6103, Energy: -28.725317+0.003197j
[2025-08-24 04:18:01] [Iter 1567/4650] R3[516/1200], Temp: 0.6091, Energy: -28.729838-0.000147j
[2025-08-24 04:18:11] [Iter 1568/4650] R3[517/1200], Temp: 0.6078, Energy: -28.723885-0.000213j
[2025-08-24 04:18:21] [Iter 1569/4650] R3[518/1200], Temp: 0.6065, Energy: -28.730316+0.000891j
[2025-08-24 04:18:32] [Iter 1570/4650] R3[519/1200], Temp: 0.6052, Energy: -28.729615+0.001722j
[2025-08-24 04:18:42] [Iter 1571/4650] R3[520/1200], Temp: 0.6040, Energy: -28.728021+0.001059j
[2025-08-24 04:18:52] [Iter 1572/4650] R3[521/1200], Temp: 0.6027, Energy: -28.730114-0.002043j
[2025-08-24 04:19:02] [Iter 1573/4650] R3[522/1200], Temp: 0.6014, Energy: -28.733933+0.000022j
[2025-08-24 04:19:12] [Iter 1574/4650] R3[523/1200], Temp: 0.6001, Energy: -28.731662-0.000528j
[2025-08-24 04:19:22] [Iter 1575/4650] R3[524/1200], Temp: 0.5988, Energy: -28.730816+0.000536j
[2025-08-24 04:19:32] [Iter 1576/4650] R3[525/1200], Temp: 0.5975, Energy: -28.729376+0.002462j
[2025-08-24 04:19:43] [Iter 1577/4650] R3[526/1200], Temp: 0.5963, Energy: -28.723012+0.002329j
[2025-08-24 04:19:53] [Iter 1578/4650] R3[527/1200], Temp: 0.5950, Energy: -28.732368-0.002907j
[2025-08-24 04:20:03] [Iter 1579/4650] R3[528/1200], Temp: 0.5937, Energy: -28.729986+0.000928j
[2025-08-24 04:20:13] [Iter 1580/4650] R3[529/1200], Temp: 0.5924, Energy: -28.724752-0.004529j
[2025-08-24 04:20:23] [Iter 1581/4650] R3[530/1200], Temp: 0.5911, Energy: -28.730097+0.001539j
[2025-08-24 04:20:33] [Iter 1582/4650] R3[531/1200], Temp: 0.5898, Energy: -28.729631-0.002004j
[2025-08-24 04:20:43] [Iter 1583/4650] R3[532/1200], Temp: 0.5885, Energy: -28.730783-0.001370j
[2025-08-24 04:20:53] [Iter 1584/4650] R3[533/1200], Temp: 0.5873, Energy: -28.723219+0.000138j
[2025-08-24 04:21:04] [Iter 1585/4650] R3[534/1200], Temp: 0.5860, Energy: -28.730380-0.002940j
[2025-08-24 04:21:14] [Iter 1586/4650] R3[535/1200], Temp: 0.5847, Energy: -28.725871-0.000371j
[2025-08-24 04:21:24] [Iter 1587/4650] R3[536/1200], Temp: 0.5834, Energy: -28.727832-0.000114j
[2025-08-24 04:21:34] [Iter 1588/4650] R3[537/1200], Temp: 0.5821, Energy: -28.725451-0.002641j
[2025-08-24 04:21:44] [Iter 1589/4650] R3[538/1200], Temp: 0.5808, Energy: -28.731931-0.000435j
[2025-08-24 04:21:54] [Iter 1590/4650] R3[539/1200], Temp: 0.5795, Energy: -28.727293+0.001492j
[2025-08-24 04:22:04] [Iter 1591/4650] R3[540/1200], Temp: 0.5782, Energy: -28.733860+0.002124j
[2025-08-24 04:22:15] [Iter 1592/4650] R3[541/1200], Temp: 0.5769, Energy: -28.722894+0.000259j
[2025-08-24 04:22:25] [Iter 1593/4650] R3[542/1200], Temp: 0.5756, Energy: -28.733583+0.000503j
[2025-08-24 04:22:35] [Iter 1594/4650] R3[543/1200], Temp: 0.5743, Energy: -28.728282-0.000052j
[2025-08-24 04:22:45] [Iter 1595/4650] R3[544/1200], Temp: 0.5730, Energy: -28.729372+0.000968j
[2025-08-24 04:22:55] [Iter 1596/4650] R3[545/1200], Temp: 0.5717, Energy: -28.728621-0.000005j
[2025-08-24 04:23:05] [Iter 1597/4650] R3[546/1200], Temp: 0.5705, Energy: -28.728990-0.001718j
[2025-08-24 04:23:15] [Iter 1598/4650] R3[547/1200], Temp: 0.5692, Energy: -28.731290-0.002914j
[2025-08-24 04:23:26] [Iter 1599/4650] R3[548/1200], Temp: 0.5679, Energy: -28.730614-0.000164j
[2025-08-24 04:23:36] [Iter 1600/4650] R3[549/1200], Temp: 0.5666, Energy: -28.729446-0.003746j
[2025-08-24 04:23:46] [Iter 1601/4650] R3[550/1200], Temp: 0.5653, Energy: -28.729254-0.001561j
[2025-08-24 04:23:56] [Iter 1602/4650] R3[551/1200], Temp: 0.5640, Energy: -28.734067+0.002975j
[2025-08-24 04:24:06] [Iter 1603/4650] R3[552/1200], Temp: 0.5627, Energy: -28.731435-0.001977j
[2025-08-24 04:24:16] [Iter 1604/4650] R3[553/1200], Temp: 0.5614, Energy: -28.730815+0.000699j
[2025-08-24 04:24:26] [Iter 1605/4650] R3[554/1200], Temp: 0.5601, Energy: -28.734539+0.001578j
[2025-08-24 04:24:37] [Iter 1606/4650] R3[555/1200], Temp: 0.5588, Energy: -28.728955-0.002300j
[2025-08-24 04:24:47] [Iter 1607/4650] R3[556/1200], Temp: 0.5575, Energy: -28.728340+0.000841j
[2025-08-24 04:24:57] [Iter 1608/4650] R3[557/1200], Temp: 0.5562, Energy: -28.735040+0.002220j
[2025-08-24 04:25:07] [Iter 1609/4650] R3[558/1200], Temp: 0.5549, Energy: -28.727654+0.000859j
[2025-08-24 04:25:17] [Iter 1610/4650] R3[559/1200], Temp: 0.5536, Energy: -28.727466-0.000318j
[2025-08-24 04:25:27] [Iter 1611/4650] R3[560/1200], Temp: 0.5523, Energy: -28.731558-0.000118j
[2025-08-24 04:25:37] [Iter 1612/4650] R3[561/1200], Temp: 0.5510, Energy: -28.730560-0.000418j
[2025-08-24 04:25:48] [Iter 1613/4650] R3[562/1200], Temp: 0.5497, Energy: -28.728270-0.002456j
[2025-08-24 04:25:58] [Iter 1614/4650] R3[563/1200], Temp: 0.5484, Energy: -28.732538+0.000403j
[2025-08-24 04:26:08] [Iter 1615/4650] R3[564/1200], Temp: 0.5471, Energy: -28.726645-0.000921j
[2025-08-24 04:26:18] [Iter 1616/4650] R3[565/1200], Temp: 0.5458, Energy: -28.729732+0.001981j
[2025-08-24 04:26:28] [Iter 1617/4650] R3[566/1200], Temp: 0.5444, Energy: -28.730328-0.001357j
[2025-08-24 04:26:38] [Iter 1618/4650] R3[567/1200], Temp: 0.5431, Energy: -28.727774-0.002044j
[2025-08-24 04:26:48] [Iter 1619/4650] R3[568/1200], Temp: 0.5418, Energy: -28.727688+0.000208j
[2025-08-24 04:26:59] [Iter 1620/4650] R3[569/1200], Temp: 0.5405, Energy: -28.727327-0.000219j
[2025-08-24 04:27:09] [Iter 1621/4650] R3[570/1200], Temp: 0.5392, Energy: -28.724720-0.004189j
[2025-08-24 04:27:19] [Iter 1622/4650] R3[571/1200], Temp: 0.5379, Energy: -28.729483-0.001041j
[2025-08-24 04:27:29] [Iter 1623/4650] R3[572/1200], Temp: 0.5366, Energy: -28.723141-0.000919j
[2025-08-24 04:27:39] [Iter 1624/4650] R3[573/1200], Temp: 0.5353, Energy: -28.736410+0.000144j
[2025-08-24 04:27:49] [Iter 1625/4650] R3[574/1200], Temp: 0.5340, Energy: -28.730962+0.001248j
[2025-08-24 04:27:59] [Iter 1626/4650] R3[575/1200], Temp: 0.5327, Energy: -28.730216+0.001084j
[2025-08-24 04:28:09] [Iter 1627/4650] R3[576/1200], Temp: 0.5314, Energy: -28.733920+0.000495j
[2025-08-24 04:28:20] [Iter 1628/4650] R3[577/1200], Temp: 0.5301, Energy: -28.729783-0.001572j
[2025-08-24 04:28:30] [Iter 1629/4650] R3[578/1200], Temp: 0.5288, Energy: -28.736383-0.002074j
[2025-08-24 04:28:40] [Iter 1630/4650] R3[579/1200], Temp: 0.5275, Energy: -28.729777-0.000442j
[2025-08-24 04:28:50] [Iter 1631/4650] R3[580/1200], Temp: 0.5262, Energy: -28.731443+0.002138j
[2025-08-24 04:29:00] [Iter 1632/4650] R3[581/1200], Temp: 0.5249, Energy: -28.725665-0.001643j
[2025-08-24 04:29:10] [Iter 1633/4650] R3[582/1200], Temp: 0.5236, Energy: -28.727755+0.002648j
[2025-08-24 04:29:20] [Iter 1634/4650] R3[583/1200], Temp: 0.5222, Energy: -28.729680-0.000309j
[2025-08-24 04:29:31] [Iter 1635/4650] R3[584/1200], Temp: 0.5209, Energy: -28.733223-0.001743j
[2025-08-24 04:29:41] [Iter 1636/4650] R3[585/1200], Temp: 0.5196, Energy: -28.727845+0.000343j
[2025-08-24 04:29:51] [Iter 1637/4650] R3[586/1200], Temp: 0.5183, Energy: -28.731383-0.001294j
[2025-08-24 04:30:01] [Iter 1638/4650] R3[587/1200], Temp: 0.5170, Energy: -28.734821-0.000618j
[2025-08-24 04:30:11] [Iter 1639/4650] R3[588/1200], Temp: 0.5157, Energy: -28.729118-0.001518j
[2025-08-24 04:30:21] [Iter 1640/4650] R3[589/1200], Temp: 0.5144, Energy: -28.730570+0.000195j
[2025-08-24 04:30:31] [Iter 1641/4650] R3[590/1200], Temp: 0.5131, Energy: -28.728715-0.000030j
[2025-08-24 04:30:41] [Iter 1642/4650] R3[591/1200], Temp: 0.5118, Energy: -28.725997+0.001154j
[2025-08-24 04:30:52] [Iter 1643/4650] R3[592/1200], Temp: 0.5105, Energy: -28.728482-0.000157j
[2025-08-24 04:31:02] [Iter 1644/4650] R3[593/1200], Temp: 0.5092, Energy: -28.729916-0.000569j
[2025-08-24 04:31:12] [Iter 1645/4650] R3[594/1200], Temp: 0.5079, Energy: -28.733106-0.000626j
[2025-08-24 04:31:22] [Iter 1646/4650] R3[595/1200], Temp: 0.5065, Energy: -28.732418+0.000303j
[2025-08-24 04:31:32] [Iter 1647/4650] R3[596/1200], Temp: 0.5052, Energy: -28.726875-0.000120j
[2025-08-24 04:31:42] [Iter 1648/4650] R3[597/1200], Temp: 0.5039, Energy: -28.731016+0.002115j
[2025-08-24 04:31:52] [Iter 1649/4650] R3[598/1200], Temp: 0.5026, Energy: -28.724925-0.000210j
[2025-08-24 04:32:03] [Iter 1650/4650] R3[599/1200], Temp: 0.5013, Energy: -28.736439-0.000274j
[2025-08-24 04:32:13] [Iter 1651/4650] R3[600/1200], Temp: 0.5000, Energy: -28.729938-0.000923j
[2025-08-24 04:32:23] [Iter 1652/4650] R3[601/1200], Temp: 0.4987, Energy: -28.735530-0.000603j
[2025-08-24 04:32:33] [Iter 1653/4650] R3[602/1200], Temp: 0.4974, Energy: -28.724480+0.003602j
[2025-08-24 04:32:43] [Iter 1654/4650] R3[603/1200], Temp: 0.4961, Energy: -28.733025-0.003432j
[2025-08-24 04:32:53] [Iter 1655/4650] R3[604/1200], Temp: 0.4948, Energy: -28.731561-0.001575j
[2025-08-24 04:33:03] [Iter 1656/4650] R3[605/1200], Temp: 0.4935, Energy: -28.732228+0.001912j
[2025-08-24 04:33:14] [Iter 1657/4650] R3[606/1200], Temp: 0.4921, Energy: -28.730287+0.000590j
[2025-08-24 04:33:24] [Iter 1658/4650] R3[607/1200], Temp: 0.4908, Energy: -28.731882-0.000040j
[2025-08-24 04:33:34] [Iter 1659/4650] R3[608/1200], Temp: 0.4895, Energy: -28.733562-0.000771j
[2025-08-24 04:33:44] [Iter 1660/4650] R3[609/1200], Temp: 0.4882, Energy: -28.735737+0.001485j
[2025-08-24 04:33:54] [Iter 1661/4650] R3[610/1200], Temp: 0.4869, Energy: -28.735032+0.003187j
[2025-08-24 04:34:04] [Iter 1662/4650] R3[611/1200], Temp: 0.4856, Energy: -28.728122-0.001913j
[2025-08-24 04:34:14] [Iter 1663/4650] R3[612/1200], Temp: 0.4843, Energy: -28.733127+0.000577j
[2025-08-24 04:34:25] [Iter 1664/4650] R3[613/1200], Temp: 0.4830, Energy: -28.735387-0.003292j
[2025-08-24 04:34:35] [Iter 1665/4650] R3[614/1200], Temp: 0.4817, Energy: -28.725612+0.000157j
[2025-08-24 04:34:45] [Iter 1666/4650] R3[615/1200], Temp: 0.4804, Energy: -28.729108-0.000026j
[2025-08-24 04:34:55] [Iter 1667/4650] R3[616/1200], Temp: 0.4791, Energy: -28.728991-0.000133j
[2025-08-24 04:35:05] [Iter 1668/4650] R3[617/1200], Temp: 0.4778, Energy: -28.726509-0.001461j
[2025-08-24 04:35:15] [Iter 1669/4650] R3[618/1200], Temp: 0.4764, Energy: -28.730081+0.000900j
[2025-08-24 04:35:25] [Iter 1670/4650] R3[619/1200], Temp: 0.4751, Energy: -28.731186+0.000643j
[2025-08-24 04:35:36] [Iter 1671/4650] R3[620/1200], Temp: 0.4738, Energy: -28.736354+0.000307j
[2025-08-24 04:35:46] [Iter 1672/4650] R3[621/1200], Temp: 0.4725, Energy: -28.736212-0.001461j
[2025-08-24 04:35:56] [Iter 1673/4650] R3[622/1200], Temp: 0.4712, Energy: -28.732345+0.001937j
[2025-08-24 04:36:06] [Iter 1674/4650] R3[623/1200], Temp: 0.4699, Energy: -28.729947-0.000309j
[2025-08-24 04:36:16] [Iter 1675/4650] R3[624/1200], Temp: 0.4686, Energy: -28.728062+0.002140j
[2025-08-24 04:36:26] [Iter 1676/4650] R3[625/1200], Temp: 0.4673, Energy: -28.731849-0.001548j
[2025-08-24 04:36:36] [Iter 1677/4650] R3[626/1200], Temp: 0.4660, Energy: -28.735319+0.000557j
[2025-08-24 04:36:47] [Iter 1678/4650] R3[627/1200], Temp: 0.4647, Energy: -28.727052+0.000563j
[2025-08-24 04:36:57] [Iter 1679/4650] R3[628/1200], Temp: 0.4634, Energy: -28.731810-0.001064j
[2025-08-24 04:37:07] [Iter 1680/4650] R3[629/1200], Temp: 0.4621, Energy: -28.730330+0.000167j
[2025-08-24 04:37:17] [Iter 1681/4650] R3[630/1200], Temp: 0.4608, Energy: -28.728188-0.000243j
[2025-08-24 04:37:27] [Iter 1682/4650] R3[631/1200], Temp: 0.4595, Energy: -28.724774+0.001780j
[2025-08-24 04:37:37] [Iter 1683/4650] R3[632/1200], Temp: 0.4582, Energy: -28.734501+0.000261j
[2025-08-24 04:37:47] [Iter 1684/4650] R3[633/1200], Temp: 0.4569, Energy: -28.737345-0.003306j
[2025-08-24 04:37:58] [Iter 1685/4650] R3[634/1200], Temp: 0.4556, Energy: -28.727319+0.000596j
[2025-08-24 04:38:08] [Iter 1686/4650] R3[635/1200], Temp: 0.4542, Energy: -28.736782-0.000856j
[2025-08-24 04:38:18] [Iter 1687/4650] R3[636/1200], Temp: 0.4529, Energy: -28.734361+0.002617j
[2025-08-24 04:38:28] [Iter 1688/4650] R3[637/1200], Temp: 0.4516, Energy: -28.733101+0.001707j
[2025-08-24 04:38:38] [Iter 1689/4650] R3[638/1200], Temp: 0.4503, Energy: -28.732567-0.000549j
[2025-08-24 04:38:48] [Iter 1690/4650] R3[639/1200], Temp: 0.4490, Energy: -28.724600-0.000332j
[2025-08-24 04:38:58] [Iter 1691/4650] R3[640/1200], Temp: 0.4477, Energy: -28.725913-0.000663j
[2025-08-24 04:39:09] [Iter 1692/4650] R3[641/1200], Temp: 0.4464, Energy: -28.727521+0.001039j
[2025-08-24 04:39:19] [Iter 1693/4650] R3[642/1200], Temp: 0.4451, Energy: -28.728558-0.002862j
[2025-08-24 04:39:29] [Iter 1694/4650] R3[643/1200], Temp: 0.4438, Energy: -28.731273-0.000368j
[2025-08-24 04:39:39] [Iter 1695/4650] R3[644/1200], Temp: 0.4425, Energy: -28.736228+0.002193j
[2025-08-24 04:39:49] [Iter 1696/4650] R3[645/1200], Temp: 0.4412, Energy: -28.738262-0.005242j
[2025-08-24 04:39:59] [Iter 1697/4650] R3[646/1200], Temp: 0.4399, Energy: -28.734192+0.001814j
[2025-08-24 04:40:09] [Iter 1698/4650] R3[647/1200], Temp: 0.4386, Energy: -28.727718-0.000803j
[2025-08-24 04:40:20] [Iter 1699/4650] R3[648/1200], Temp: 0.4373, Energy: -28.727488-0.001240j
[2025-08-24 04:40:30] [Iter 1700/4650] R3[649/1200], Temp: 0.4360, Energy: -28.732123-0.000092j
[2025-08-24 04:40:40] [Iter 1701/4650] R3[650/1200], Temp: 0.4347, Energy: -28.731160-0.000919j
[2025-08-24 04:40:50] [Iter 1702/4650] R3[651/1200], Temp: 0.4334, Energy: -28.740356+0.000252j
[2025-08-24 04:41:00] [Iter 1703/4650] R3[652/1200], Temp: 0.4321, Energy: -28.733416+0.002132j
[2025-08-24 04:41:10] [Iter 1704/4650] R3[653/1200], Temp: 0.4308, Energy: -28.735004+0.000187j
[2025-08-24 04:41:20] [Iter 1705/4650] R3[654/1200], Temp: 0.4295, Energy: -28.731819-0.000819j
[2025-08-24 04:41:31] [Iter 1706/4650] R3[655/1200], Temp: 0.4283, Energy: -28.731137-0.000927j
[2025-08-24 04:41:41] [Iter 1707/4650] R3[656/1200], Temp: 0.4270, Energy: -28.728366+0.002827j
[2025-08-24 04:41:51] [Iter 1708/4650] R3[657/1200], Temp: 0.4257, Energy: -28.728686+0.001837j
[2025-08-24 04:42:01] [Iter 1709/4650] R3[658/1200], Temp: 0.4244, Energy: -28.734357-0.000328j
[2025-08-24 04:42:11] [Iter 1710/4650] R3[659/1200], Temp: 0.4231, Energy: -28.729887+0.000576j
[2025-08-24 04:42:21] [Iter 1711/4650] R3[660/1200], Temp: 0.4218, Energy: -28.729637-0.000236j
[2025-08-24 04:42:31] [Iter 1712/4650] R3[661/1200], Temp: 0.4205, Energy: -28.732276+0.002178j
[2025-08-24 04:42:42] [Iter 1713/4650] R3[662/1200], Temp: 0.4192, Energy: -28.737036+0.000931j
[2025-08-24 04:42:52] [Iter 1714/4650] R3[663/1200], Temp: 0.4179, Energy: -28.738844+0.000446j
[2025-08-24 04:43:02] [Iter 1715/4650] R3[664/1200], Temp: 0.4166, Energy: -28.732984-0.003208j
[2025-08-24 04:43:12] [Iter 1716/4650] R3[665/1200], Temp: 0.4153, Energy: -28.730275+0.000377j
[2025-08-24 04:43:22] [Iter 1717/4650] R3[666/1200], Temp: 0.4140, Energy: -28.726317+0.001272j
[2025-08-24 04:43:32] [Iter 1718/4650] R3[667/1200], Temp: 0.4127, Energy: -28.735798+0.001607j
[2025-08-24 04:43:43] [Iter 1719/4650] R3[668/1200], Temp: 0.4115, Energy: -28.729434-0.001190j
[2025-08-24 04:43:53] [Iter 1720/4650] R3[669/1200], Temp: 0.4102, Energy: -28.734114-0.001206j
[2025-08-24 04:44:03] [Iter 1721/4650] R3[670/1200], Temp: 0.4089, Energy: -28.726684+0.000433j
[2025-08-24 04:44:13] [Iter 1722/4650] R3[671/1200], Temp: 0.4076, Energy: -28.734113-0.000640j
[2025-08-24 04:44:23] [Iter 1723/4650] R3[672/1200], Temp: 0.4063, Energy: -28.729114-0.001742j
[2025-08-24 04:44:33] [Iter 1724/4650] R3[673/1200], Temp: 0.4050, Energy: -28.731860+0.001373j
[2025-08-24 04:44:44] [Iter 1725/4650] R3[674/1200], Temp: 0.4037, Energy: -28.732763+0.004629j
[2025-08-24 04:44:54] [Iter 1726/4650] R3[675/1200], Temp: 0.4025, Energy: -28.734932-0.002355j
[2025-08-24 04:45:04] [Iter 1727/4650] R3[676/1200], Temp: 0.4012, Energy: -28.732359+0.002320j
[2025-08-24 04:45:14] [Iter 1728/4650] R3[677/1200], Temp: 0.3999, Energy: -28.734956-0.000157j
[2025-08-24 04:45:24] [Iter 1729/4650] R3[678/1200], Temp: 0.3986, Energy: -28.725971-0.000894j
[2025-08-24 04:45:34] [Iter 1730/4650] R3[679/1200], Temp: 0.3973, Energy: -28.731435-0.002350j
[2025-08-24 04:45:44] [Iter 1731/4650] R3[680/1200], Temp: 0.3960, Energy: -28.734610+0.000427j
[2025-08-24 04:45:55] [Iter 1732/4650] R3[681/1200], Temp: 0.3948, Energy: -28.730704-0.002587j
[2025-08-24 04:46:05] [Iter 1733/4650] R3[682/1200], Temp: 0.3935, Energy: -28.732726+0.002158j
[2025-08-24 04:46:15] [Iter 1734/4650] R3[683/1200], Temp: 0.3922, Energy: -28.729645+0.003204j
[2025-08-24 04:46:25] [Iter 1735/4650] R3[684/1200], Temp: 0.3909, Energy: -28.731236+0.002903j
[2025-08-24 04:46:35] [Iter 1736/4650] R3[685/1200], Temp: 0.3897, Energy: -28.718501+0.001290j
[2025-08-24 04:46:45] [Iter 1737/4650] R3[686/1200], Temp: 0.3884, Energy: -28.729622-0.001900j
[2025-08-24 04:46:56] [Iter 1738/4650] R3[687/1200], Temp: 0.3871, Energy: -28.736095-0.000735j
[2025-08-24 04:47:06] [Iter 1739/4650] R3[688/1200], Temp: 0.3858, Energy: -28.729759+0.000999j
[2025-08-24 04:47:16] [Iter 1740/4650] R3[689/1200], Temp: 0.3846, Energy: -28.737394+0.000385j
[2025-08-24 04:47:26] [Iter 1741/4650] R3[690/1200], Temp: 0.3833, Energy: -28.737572+0.000091j
[2025-08-24 04:47:36] [Iter 1742/4650] R3[691/1200], Temp: 0.3820, Energy: -28.729860+0.000210j
[2025-08-24 04:47:46] [Iter 1743/4650] R3[692/1200], Temp: 0.3807, Energy: -28.733714-0.000807j
[2025-08-24 04:47:56] [Iter 1744/4650] R3[693/1200], Temp: 0.3795, Energy: -28.730069+0.003402j
[2025-08-24 04:48:07] [Iter 1745/4650] R3[694/1200], Temp: 0.3782, Energy: -28.736062-0.000080j
[2025-08-24 04:48:17] [Iter 1746/4650] R3[695/1200], Temp: 0.3769, Energy: -28.731441+0.000672j
[2025-08-24 04:48:27] [Iter 1747/4650] R3[696/1200], Temp: 0.3757, Energy: -28.729894+0.000197j
[2025-08-24 04:48:37] [Iter 1748/4650] R3[697/1200], Temp: 0.3744, Energy: -28.727009-0.001575j
[2025-08-24 04:48:47] [Iter 1749/4650] R3[698/1200], Temp: 0.3731, Energy: -28.729442-0.000619j
[2025-08-24 04:48:57] [Iter 1750/4650] R3[699/1200], Temp: 0.3719, Energy: -28.736546+0.000523j
[2025-08-24 04:49:07] [Iter 1751/4650] R3[700/1200], Temp: 0.3706, Energy: -28.733100-0.000023j
[2025-08-24 04:49:18] [Iter 1752/4650] R3[701/1200], Temp: 0.3693, Energy: -28.736928-0.000629j
[2025-08-24 04:49:28] [Iter 1753/4650] R3[702/1200], Temp: 0.3681, Energy: -28.736092-0.002507j
[2025-08-24 04:49:38] [Iter 1754/4650] R3[703/1200], Temp: 0.3668, Energy: -28.728835+0.000241j
[2025-08-24 04:49:48] [Iter 1755/4650] R3[704/1200], Temp: 0.3655, Energy: -28.730414-0.000725j
[2025-08-24 04:49:58] [Iter 1756/4650] R3[705/1200], Temp: 0.3643, Energy: -28.733911+0.000215j
[2025-08-24 04:50:08] [Iter 1757/4650] R3[706/1200], Temp: 0.3630, Energy: -28.736807-0.000090j
[2025-08-24 04:50:19] [Iter 1758/4650] R3[707/1200], Temp: 0.3618, Energy: -28.722649+0.002300j
[2025-08-24 04:50:29] [Iter 1759/4650] R3[708/1200], Temp: 0.3605, Energy: -28.733498-0.003192j
[2025-08-24 04:50:39] [Iter 1760/4650] R3[709/1200], Temp: 0.3592, Energy: -28.730757-0.001426j
[2025-08-24 04:50:49] [Iter 1761/4650] R3[710/1200], Temp: 0.3580, Energy: -28.726410-0.000138j
[2025-08-24 04:50:59] [Iter 1762/4650] R3[711/1200], Temp: 0.3567, Energy: -28.727047-0.000031j
[2025-08-24 04:51:09] [Iter 1763/4650] R3[712/1200], Temp: 0.3555, Energy: -28.726678+0.001691j
[2025-08-24 04:51:19] [Iter 1764/4650] R3[713/1200], Temp: 0.3542, Energy: -28.736268-0.000311j
[2025-08-24 04:51:29] [Iter 1765/4650] R3[714/1200], Temp: 0.3530, Energy: -28.733253-0.000517j
[2025-08-24 04:51:40] [Iter 1766/4650] R3[715/1200], Temp: 0.3517, Energy: -28.731692+0.001282j
[2025-08-24 04:51:50] [Iter 1767/4650] R3[716/1200], Temp: 0.3505, Energy: -28.730018-0.000040j
[2025-08-24 04:52:00] [Iter 1768/4650] R3[717/1200], Temp: 0.3492, Energy: -28.731971+0.001344j
[2025-08-24 04:52:10] [Iter 1769/4650] R3[718/1200], Temp: 0.3480, Energy: -28.728973-0.000328j
[2025-08-24 04:52:20] [Iter 1770/4650] R3[719/1200], Temp: 0.3467, Energy: -28.738602+0.001064j
[2025-08-24 04:52:30] [Iter 1771/4650] R3[720/1200], Temp: 0.3455, Energy: -28.733906+0.000547j
[2025-08-24 04:52:40] [Iter 1772/4650] R3[721/1200], Temp: 0.3442, Energy: -28.729279+0.001232j
[2025-08-24 04:52:51] [Iter 1773/4650] R3[722/1200], Temp: 0.3430, Energy: -28.734929-0.000863j
[2025-08-24 04:53:01] [Iter 1774/4650] R3[723/1200], Temp: 0.3418, Energy: -28.734329-0.001125j
[2025-08-24 04:53:11] [Iter 1775/4650] R3[724/1200], Temp: 0.3405, Energy: -28.729259+0.000393j
[2025-08-24 04:53:21] [Iter 1776/4650] R3[725/1200], Temp: 0.3393, Energy: -28.731902-0.001336j
[2025-08-24 04:53:31] [Iter 1777/4650] R3[726/1200], Temp: 0.3380, Energy: -28.735948+0.001259j
[2025-08-24 04:53:41] [Iter 1778/4650] R3[727/1200], Temp: 0.3368, Energy: -28.731956-0.000223j
[2025-08-24 04:53:51] [Iter 1779/4650] R3[728/1200], Temp: 0.3356, Energy: -28.726590-0.000418j
[2025-08-24 04:54:02] [Iter 1780/4650] R3[729/1200], Temp: 0.3343, Energy: -28.733407-0.000675j
[2025-08-24 04:54:12] [Iter 1781/4650] R3[730/1200], Temp: 0.3331, Energy: -28.732465+0.001072j
[2025-08-24 04:54:22] [Iter 1782/4650] R3[731/1200], Temp: 0.3319, Energy: -28.727878-0.002458j
[2025-08-24 04:54:32] [Iter 1783/4650] R3[732/1200], Temp: 0.3306, Energy: -28.734572-0.001210j
[2025-08-24 04:54:42] [Iter 1784/4650] R3[733/1200], Temp: 0.3294, Energy: -28.727767+0.001070j
[2025-08-24 04:54:52] [Iter 1785/4650] R3[734/1200], Temp: 0.3282, Energy: -28.726169-0.002794j
[2025-08-24 04:55:02] [Iter 1786/4650] R3[735/1200], Temp: 0.3269, Energy: -28.738189+0.002802j
[2025-08-24 04:55:12] [Iter 1787/4650] R3[736/1200], Temp: 0.3257, Energy: -28.735833-0.000832j
[2025-08-24 04:55:23] [Iter 1788/4650] R3[737/1200], Temp: 0.3245, Energy: -28.739830-0.000425j
[2025-08-24 04:55:33] [Iter 1789/4650] R3[738/1200], Temp: 0.3233, Energy: -28.739893-0.000479j
[2025-08-24 04:55:43] [Iter 1790/4650] R3[739/1200], Temp: 0.3220, Energy: -28.731830-0.001187j
[2025-08-24 04:55:53] [Iter 1791/4650] R3[740/1200], Temp: 0.3208, Energy: -28.738158-0.000695j
[2025-08-24 04:56:03] [Iter 1792/4650] R3[741/1200], Temp: 0.3196, Energy: -28.734960+0.001172j
[2025-08-24 04:56:13] [Iter 1793/4650] R3[742/1200], Temp: 0.3184, Energy: -28.738461+0.001173j
[2025-08-24 04:56:23] [Iter 1794/4650] R3[743/1200], Temp: 0.3172, Energy: -28.731602+0.000760j
[2025-08-24 04:56:34] [Iter 1795/4650] R3[744/1200], Temp: 0.3159, Energy: -28.732381-0.001820j
[2025-08-24 04:56:44] [Iter 1796/4650] R3[745/1200], Temp: 0.3147, Energy: -28.727746+0.000383j
[2025-08-24 04:56:54] [Iter 1797/4650] R3[746/1200], Temp: 0.3135, Energy: -28.728528-0.001746j
[2025-08-24 04:57:04] [Iter 1798/4650] R3[747/1200], Temp: 0.3123, Energy: -28.731860+0.000027j
[2025-08-24 04:57:14] [Iter 1799/4650] R3[748/1200], Temp: 0.3111, Energy: -28.732771-0.000459j
[2025-08-24 04:57:24] [Iter 1800/4650] R3[749/1200], Temp: 0.3099, Energy: -28.731763+0.002744j
[2025-08-24 04:57:34] [Iter 1801/4650] R3[750/1200], Temp: 0.3087, Energy: -28.729672+0.002586j
[2025-08-24 04:57:45] [Iter 1802/4650] R3[751/1200], Temp: 0.3074, Energy: -28.736005-0.000063j
[2025-08-24 04:57:55] [Iter 1803/4650] R3[752/1200], Temp: 0.3062, Energy: -28.728896-0.002123j
[2025-08-24 04:58:05] [Iter 1804/4650] R3[753/1200], Temp: 0.3050, Energy: -28.733158+0.000330j
[2025-08-24 04:58:15] [Iter 1805/4650] R3[754/1200], Temp: 0.3038, Energy: -28.732469+0.001937j
[2025-08-24 04:58:25] [Iter 1806/4650] R3[755/1200], Temp: 0.3026, Energy: -28.730505+0.000444j
[2025-08-24 04:58:35] [Iter 1807/4650] R3[756/1200], Temp: 0.3014, Energy: -28.729729-0.000804j
[2025-08-24 04:58:45] [Iter 1808/4650] R3[757/1200], Temp: 0.3002, Energy: -28.740471-0.000687j
[2025-08-24 04:58:56] [Iter 1809/4650] R3[758/1200], Temp: 0.2990, Energy: -28.739311-0.001037j
[2025-08-24 04:59:06] [Iter 1810/4650] R3[759/1200], Temp: 0.2978, Energy: -28.730658+0.001571j
[2025-08-24 04:59:16] [Iter 1811/4650] R3[760/1200], Temp: 0.2966, Energy: -28.734285-0.000161j
[2025-08-24 04:59:26] [Iter 1812/4650] R3[761/1200], Temp: 0.2954, Energy: -28.733927-0.001981j
[2025-08-24 04:59:36] [Iter 1813/4650] R3[762/1200], Temp: 0.2942, Energy: -28.725738+0.000138j
[2025-08-24 04:59:46] [Iter 1814/4650] R3[763/1200], Temp: 0.2931, Energy: -28.726223-0.002719j
[2025-08-24 04:59:56] [Iter 1815/4650] R3[764/1200], Temp: 0.2919, Energy: -28.736595-0.000893j
[2025-08-24 05:00:07] [Iter 1816/4650] R3[765/1200], Temp: 0.2907, Energy: -28.732010+0.000781j
[2025-08-24 05:00:17] [Iter 1817/4650] R3[766/1200], Temp: 0.2895, Energy: -28.740025+0.000333j
[2025-08-24 05:00:27] [Iter 1818/4650] R3[767/1200], Temp: 0.2883, Energy: -28.732950-0.001597j
[2025-08-24 05:00:37] [Iter 1819/4650] R3[768/1200], Temp: 0.2871, Energy: -28.735527-0.000373j
[2025-08-24 05:00:47] [Iter 1820/4650] R3[769/1200], Temp: 0.2859, Energy: -28.728160+0.000458j
[2025-08-24 05:00:57] [Iter 1821/4650] R3[770/1200], Temp: 0.2847, Energy: -28.731372-0.000433j
[2025-08-24 05:01:07] [Iter 1822/4650] R3[771/1200], Temp: 0.2836, Energy: -28.737187-0.001166j
[2025-08-24 05:01:18] [Iter 1823/4650] R3[772/1200], Temp: 0.2824, Energy: -28.738480+0.002758j
[2025-08-24 05:01:28] [Iter 1824/4650] R3[773/1200], Temp: 0.2812, Energy: -28.731945+0.002320j
[2025-08-24 05:01:38] [Iter 1825/4650] R3[774/1200], Temp: 0.2800, Energy: -28.732375+0.001129j
[2025-08-24 05:01:48] [Iter 1826/4650] R3[775/1200], Temp: 0.2789, Energy: -28.742007-0.000920j
[2025-08-24 05:01:58] [Iter 1827/4650] R3[776/1200], Temp: 0.2777, Energy: -28.732887-0.000254j
[2025-08-24 05:02:08] [Iter 1828/4650] R3[777/1200], Temp: 0.2765, Energy: -28.731100-0.001404j
[2025-08-24 05:02:18] [Iter 1829/4650] R3[778/1200], Temp: 0.2753, Energy: -28.733777+0.002048j
[2025-08-24 05:02:29] [Iter 1830/4650] R3[779/1200], Temp: 0.2742, Energy: -28.733498-0.000178j
[2025-08-24 05:02:39] [Iter 1831/4650] R3[780/1200], Temp: 0.2730, Energy: -28.735735-0.001663j
[2025-08-24 05:02:49] [Iter 1832/4650] R3[781/1200], Temp: 0.2718, Energy: -28.734689-0.002012j
[2025-08-24 05:02:59] [Iter 1833/4650] R3[782/1200], Temp: 0.2707, Energy: -28.733119-0.000537j
[2025-08-24 05:03:09] [Iter 1834/4650] R3[783/1200], Temp: 0.2695, Energy: -28.739383-0.001160j
[2025-08-24 05:03:19] [Iter 1835/4650] R3[784/1200], Temp: 0.2684, Energy: -28.736258-0.002999j
[2025-08-24 05:03:29] [Iter 1836/4650] R3[785/1200], Temp: 0.2672, Energy: -28.730720+0.004167j
[2025-08-24 05:03:40] [Iter 1837/4650] R3[786/1200], Temp: 0.2660, Energy: -28.740554+0.002143j
[2025-08-24 05:03:50] [Iter 1838/4650] R3[787/1200], Temp: 0.2649, Energy: -28.738108+0.000046j
[2025-08-24 05:04:00] [Iter 1839/4650] R3[788/1200], Temp: 0.2637, Energy: -28.736179+0.000328j
[2025-08-24 05:04:10] [Iter 1840/4650] R3[789/1200], Temp: 0.2626, Energy: -28.732815-0.002741j
[2025-08-24 05:04:20] [Iter 1841/4650] R3[790/1200], Temp: 0.2614, Energy: -28.734378+0.002196j
[2025-08-24 05:04:30] [Iter 1842/4650] R3[791/1200], Temp: 0.2603, Energy: -28.731750+0.003505j
[2025-08-24 05:04:40] [Iter 1843/4650] R3[792/1200], Temp: 0.2591, Energy: -28.733460+0.000166j
[2025-08-24 05:04:51] [Iter 1844/4650] R3[793/1200], Temp: 0.2580, Energy: -28.738662-0.001221j
[2025-08-24 05:05:01] [Iter 1845/4650] R3[794/1200], Temp: 0.2568, Energy: -28.731991+0.003101j
[2025-08-24 05:05:11] [Iter 1846/4650] R3[795/1200], Temp: 0.2557, Energy: -28.731028-0.000006j
[2025-08-24 05:05:21] [Iter 1847/4650] R3[796/1200], Temp: 0.2545, Energy: -28.734909-0.002899j
[2025-08-24 05:05:31] [Iter 1848/4650] R3[797/1200], Temp: 0.2534, Energy: -28.734211-0.002043j
[2025-08-24 05:05:41] [Iter 1849/4650] R3[798/1200], Temp: 0.2523, Energy: -28.727738+0.001923j
[2025-08-24 05:05:51] [Iter 1850/4650] R3[799/1200], Temp: 0.2511, Energy: -28.738155-0.000537j
[2025-08-24 05:06:02] [Iter 1851/4650] R3[800/1200], Temp: 0.2500, Energy: -28.736953-0.002099j
[2025-08-24 05:06:12] [Iter 1852/4650] R3[801/1200], Temp: 0.2489, Energy: -28.729755-0.002910j
[2025-08-24 05:06:22] [Iter 1853/4650] R3[802/1200], Temp: 0.2477, Energy: -28.736587+0.000783j
[2025-08-24 05:06:32] [Iter 1854/4650] R3[803/1200], Temp: 0.2466, Energy: -28.740041-0.001012j
[2025-08-24 05:06:42] [Iter 1855/4650] R3[804/1200], Temp: 0.2455, Energy: -28.734540+0.000084j
[2025-08-24 05:06:52] [Iter 1856/4650] R3[805/1200], Temp: 0.2444, Energy: -28.736770-0.000033j
[2025-08-24 05:07:02] [Iter 1857/4650] R3[806/1200], Temp: 0.2432, Energy: -28.733181-0.002211j
[2025-08-24 05:07:13] [Iter 1858/4650] R3[807/1200], Temp: 0.2421, Energy: -28.735142-0.001215j
[2025-08-24 05:07:23] [Iter 1859/4650] R3[808/1200], Temp: 0.2410, Energy: -28.738526+0.000324j
[2025-08-24 05:07:33] [Iter 1860/4650] R3[809/1200], Temp: 0.2399, Energy: -28.734350+0.000182j
[2025-08-24 05:07:43] [Iter 1861/4650] R3[810/1200], Temp: 0.2388, Energy: -28.737572+0.000007j
[2025-08-24 05:07:53] [Iter 1862/4650] R3[811/1200], Temp: 0.2376, Energy: -28.731784+0.000166j
[2025-08-24 05:08:03] [Iter 1863/4650] R3[812/1200], Temp: 0.2365, Energy: -28.737596+0.001361j
[2025-08-24 05:08:13] [Iter 1864/4650] R3[813/1200], Temp: 0.2354, Energy: -28.738935+0.000215j
[2025-08-24 05:08:24] [Iter 1865/4650] R3[814/1200], Temp: 0.2343, Energy: -28.730670-0.002689j
[2025-08-24 05:08:34] [Iter 1866/4650] R3[815/1200], Temp: 0.2332, Energy: -28.729905-0.000003j
[2025-08-24 05:08:44] [Iter 1867/4650] R3[816/1200], Temp: 0.2321, Energy: -28.730008+0.000923j
[2025-08-24 05:08:54] [Iter 1868/4650] R3[817/1200], Temp: 0.2310, Energy: -28.737706-0.000073j
[2025-08-24 05:09:04] [Iter 1869/4650] R3[818/1200], Temp: 0.2299, Energy: -28.737233-0.000866j
[2025-08-24 05:09:14] [Iter 1870/4650] R3[819/1200], Temp: 0.2288, Energy: -28.736198-0.000008j
[2025-08-24 05:09:24] [Iter 1871/4650] R3[820/1200], Temp: 0.2277, Energy: -28.737964-0.000096j
[2025-08-24 05:09:35] [Iter 1872/4650] R3[821/1200], Temp: 0.2266, Energy: -28.735130-0.000665j
[2025-08-24 05:09:45] [Iter 1873/4650] R3[822/1200], Temp: 0.2255, Energy: -28.737813-0.000931j
[2025-08-24 05:09:55] [Iter 1874/4650] R3[823/1200], Temp: 0.2244, Energy: -28.732637-0.001233j
[2025-08-24 05:10:05] [Iter 1875/4650] R3[824/1200], Temp: 0.2233, Energy: -28.734731-0.000263j
[2025-08-24 05:10:15] [Iter 1876/4650] R3[825/1200], Temp: 0.2222, Energy: -28.728663+0.000814j
[2025-08-24 05:10:25] [Iter 1877/4650] R3[826/1200], Temp: 0.2211, Energy: -28.735695+0.000852j
[2025-08-24 05:10:35] [Iter 1878/4650] R3[827/1200], Temp: 0.2200, Energy: -28.732271-0.000779j
[2025-08-24 05:10:46] [Iter 1879/4650] R3[828/1200], Temp: 0.2190, Energy: -28.736340-0.000048j
[2025-08-24 05:10:56] [Iter 1880/4650] R3[829/1200], Temp: 0.2179, Energy: -28.734675+0.002311j
[2025-08-24 05:11:06] [Iter 1881/4650] R3[830/1200], Temp: 0.2168, Energy: -28.732091+0.003171j
[2025-08-24 05:11:16] [Iter 1882/4650] R3[831/1200], Temp: 0.2157, Energy: -28.734851-0.000436j
[2025-08-24 05:11:26] [Iter 1883/4650] R3[832/1200], Temp: 0.2146, Energy: -28.737915-0.003023j
[2025-08-24 05:11:36] [Iter 1884/4650] R3[833/1200], Temp: 0.2136, Energy: -28.735552-0.002040j
[2025-08-24 05:11:46] [Iter 1885/4650] R3[834/1200], Temp: 0.2125, Energy: -28.732024+0.000339j
[2025-08-24 05:11:57] [Iter 1886/4650] R3[835/1200], Temp: 0.2114, Energy: -28.736866+0.000135j
[2025-08-24 05:12:07] [Iter 1887/4650] R3[836/1200], Temp: 0.2104, Energy: -28.736693+0.000776j
[2025-08-24 05:12:17] [Iter 1888/4650] R3[837/1200], Temp: 0.2093, Energy: -28.730903-0.000758j
[2025-08-24 05:12:27] [Iter 1889/4650] R3[838/1200], Temp: 0.2082, Energy: -28.728054+0.000820j
[2025-08-24 05:12:37] [Iter 1890/4650] R3[839/1200], Temp: 0.2072, Energy: -28.733905-0.001147j
[2025-08-24 05:12:47] [Iter 1891/4650] R3[840/1200], Temp: 0.2061, Energy: -28.734624-0.001439j
[2025-08-24 05:12:57] [Iter 1892/4650] R3[841/1200], Temp: 0.2050, Energy: -28.731232+0.000416j
[2025-08-24 05:13:08] [Iter 1893/4650] R3[842/1200], Temp: 0.2040, Energy: -28.733443+0.000063j
[2025-08-24 05:13:18] [Iter 1894/4650] R3[843/1200], Temp: 0.2029, Energy: -28.736582-0.000310j
[2025-08-24 05:13:28] [Iter 1895/4650] R3[844/1200], Temp: 0.2019, Energy: -28.733317-0.001922j
[2025-08-24 05:13:38] [Iter 1896/4650] R3[845/1200], Temp: 0.2008, Energy: -28.742833+0.001114j
[2025-08-24 05:13:48] [Iter 1897/4650] R3[846/1200], Temp: 0.1998, Energy: -28.735893+0.000082j
[2025-08-24 05:13:58] [Iter 1898/4650] R3[847/1200], Temp: 0.1987, Energy: -28.740956-0.001183j
[2025-08-24 05:14:08] [Iter 1899/4650] R3[848/1200], Temp: 0.1977, Energy: -28.738658-0.001069j
[2025-08-24 05:14:19] [Iter 1900/4650] R3[849/1200], Temp: 0.1967, Energy: -28.733187+0.000652j
[2025-08-24 05:14:29] [Iter 1901/4650] R3[850/1200], Temp: 0.1956, Energy: -28.737976+0.000145j
[2025-08-24 05:14:39] [Iter 1902/4650] R3[851/1200], Temp: 0.1946, Energy: -28.741482+0.000131j
[2025-08-24 05:14:49] [Iter 1903/4650] R3[852/1200], Temp: 0.1935, Energy: -28.734063-0.001724j
[2025-08-24 05:14:59] [Iter 1904/4650] R3[853/1200], Temp: 0.1925, Energy: -28.742608-0.001899j
[2025-08-24 05:15:09] [Iter 1905/4650] R3[854/1200], Temp: 0.1915, Energy: -28.733774-0.000406j
[2025-08-24 05:15:19] [Iter 1906/4650] R3[855/1200], Temp: 0.1905, Energy: -28.735890+0.001396j
[2025-08-24 05:15:30] [Iter 1907/4650] R3[856/1200], Temp: 0.1894, Energy: -28.737097-0.000447j
[2025-08-24 05:15:40] [Iter 1908/4650] R3[857/1200], Temp: 0.1884, Energy: -28.739294-0.001570j
[2025-08-24 05:15:50] [Iter 1909/4650] R3[858/1200], Temp: 0.1874, Energy: -28.739977-0.001233j
[2025-08-24 05:16:00] [Iter 1910/4650] R3[859/1200], Temp: 0.1864, Energy: -28.732308-0.002327j
[2025-08-24 05:16:10] [Iter 1911/4650] R3[860/1200], Temp: 0.1853, Energy: -28.737985+0.001753j
[2025-08-24 05:16:20] [Iter 1912/4650] R3[861/1200], Temp: 0.1843, Energy: -28.731686-0.001660j
[2025-08-24 05:16:30] [Iter 1913/4650] R3[862/1200], Temp: 0.1833, Energy: -28.735675-0.000332j
[2025-08-24 05:16:41] [Iter 1914/4650] R3[863/1200], Temp: 0.1823, Energy: -28.738286-0.000199j
[2025-08-24 05:16:51] [Iter 1915/4650] R3[864/1200], Temp: 0.1813, Energy: -28.728503-0.000193j
[2025-08-24 05:17:01] [Iter 1916/4650] R3[865/1200], Temp: 0.1803, Energy: -28.735770-0.000193j
[2025-08-24 05:17:11] [Iter 1917/4650] R3[866/1200], Temp: 0.1793, Energy: -28.735186-0.001122j
[2025-08-24 05:17:21] [Iter 1918/4650] R3[867/1200], Temp: 0.1783, Energy: -28.731752+0.002403j
[2025-08-24 05:17:31] [Iter 1919/4650] R3[868/1200], Temp: 0.1773, Energy: -28.740065-0.000774j
[2025-08-24 05:17:41] [Iter 1920/4650] R3[869/1200], Temp: 0.1763, Energy: -28.734713-0.001592j
[2025-08-24 05:17:52] [Iter 1921/4650] R3[870/1200], Temp: 0.1753, Energy: -28.735651+0.000696j
[2025-08-24 05:18:02] [Iter 1922/4650] R3[871/1200], Temp: 0.1743, Energy: -28.738189-0.000197j
[2025-08-24 05:18:12] [Iter 1923/4650] R3[872/1200], Temp: 0.1733, Energy: -28.729187-0.001243j
[2025-08-24 05:18:22] [Iter 1924/4650] R3[873/1200], Temp: 0.1723, Energy: -28.735121-0.000482j
[2025-08-24 05:18:32] [Iter 1925/4650] R3[874/1200], Temp: 0.1713, Energy: -28.732631+0.001755j
[2025-08-24 05:18:42] [Iter 1926/4650] R3[875/1200], Temp: 0.1703, Energy: -28.738551+0.000439j
[2025-08-24 05:18:52] [Iter 1927/4650] R3[876/1200], Temp: 0.1693, Energy: -28.735855-0.000209j
[2025-08-24 05:19:02] [Iter 1928/4650] R3[877/1200], Temp: 0.1684, Energy: -28.736139-0.001383j
[2025-08-24 05:19:13] [Iter 1929/4650] R3[878/1200], Temp: 0.1674, Energy: -28.736161-0.002049j
[2025-08-24 05:19:23] [Iter 1930/4650] R3[879/1200], Temp: 0.1664, Energy: -28.736002-0.000812j
[2025-08-24 05:19:33] [Iter 1931/4650] R3[880/1200], Temp: 0.1654, Energy: -28.738025-0.001355j
[2025-08-24 05:19:43] [Iter 1932/4650] R3[881/1200], Temp: 0.1645, Energy: -28.734593-0.002357j
[2025-08-24 05:19:53] [Iter 1933/4650] R3[882/1200], Temp: 0.1635, Energy: -28.738346+0.000212j
[2025-08-24 05:20:03] [Iter 1934/4650] R3[883/1200], Temp: 0.1625, Energy: -28.738852-0.000656j
[2025-08-24 05:20:13] [Iter 1935/4650] R3[884/1200], Temp: 0.1616, Energy: -28.738718+0.002172j
[2025-08-24 05:20:24] [Iter 1936/4650] R3[885/1200], Temp: 0.1606, Energy: -28.737508-0.000852j
[2025-08-24 05:20:34] [Iter 1937/4650] R3[886/1200], Temp: 0.1596, Energy: -28.730250+0.000771j
[2025-08-24 05:20:44] [Iter 1938/4650] R3[887/1200], Temp: 0.1587, Energy: -28.732422+0.000055j
[2025-08-24 05:20:54] [Iter 1939/4650] R3[888/1200], Temp: 0.1577, Energy: -28.738534-0.000205j
[2025-08-24 05:21:04] [Iter 1940/4650] R3[889/1200], Temp: 0.1568, Energy: -28.743654-0.000816j
[2025-08-24 05:21:14] [Iter 1941/4650] R3[890/1200], Temp: 0.1558, Energy: -28.736532+0.005056j
[2025-08-24 05:21:25] [Iter 1942/4650] R3[891/1200], Temp: 0.1549, Energy: -28.735375+0.001350j
[2025-08-24 05:21:35] [Iter 1943/4650] R3[892/1200], Temp: 0.1539, Energy: -28.734160-0.001238j
[2025-08-24 05:21:45] [Iter 1944/4650] R3[893/1200], Temp: 0.1530, Energy: -28.735285-0.001896j
[2025-08-24 05:21:55] [Iter 1945/4650] R3[894/1200], Temp: 0.1520, Energy: -28.739514-0.002756j
[2025-08-24 05:22:05] [Iter 1946/4650] R3[895/1200], Temp: 0.1511, Energy: -28.735255-0.002029j
[2025-08-24 05:22:15] [Iter 1947/4650] R3[896/1200], Temp: 0.1502, Energy: -28.736796-0.001881j
[2025-08-24 05:22:25] [Iter 1948/4650] R3[897/1200], Temp: 0.1492, Energy: -28.734768-0.003215j
[2025-08-24 05:22:36] [Iter 1949/4650] R3[898/1200], Temp: 0.1483, Energy: -28.736332+0.001269j
[2025-08-24 05:22:46] [Iter 1950/4650] R3[899/1200], Temp: 0.1474, Energy: -28.737112+0.000038j
[2025-08-24 05:22:56] [Iter 1951/4650] R3[900/1200], Temp: 0.1464, Energy: -28.732578+0.001726j
[2025-08-24 05:23:06] [Iter 1952/4650] R3[901/1200], Temp: 0.1455, Energy: -28.736999-0.003478j
[2025-08-24 05:23:16] [Iter 1953/4650] R3[902/1200], Temp: 0.1446, Energy: -28.730015+0.000115j
[2025-08-24 05:23:26] [Iter 1954/4650] R3[903/1200], Temp: 0.1437, Energy: -28.737860+0.000443j
[2025-08-24 05:23:36] [Iter 1955/4650] R3[904/1200], Temp: 0.1428, Energy: -28.742651+0.001902j
[2025-08-24 05:23:47] [Iter 1956/4650] R3[905/1200], Temp: 0.1418, Energy: -28.732162-0.000176j
[2025-08-24 05:23:57] [Iter 1957/4650] R3[906/1200], Temp: 0.1409, Energy: -28.738269+0.000373j
[2025-08-24 05:24:07] [Iter 1958/4650] R3[907/1200], Temp: 0.1400, Energy: -28.738951-0.001023j
[2025-08-24 05:24:17] [Iter 1959/4650] R3[908/1200], Temp: 0.1391, Energy: -28.737174+0.002377j
[2025-08-24 05:24:27] [Iter 1960/4650] R3[909/1200], Temp: 0.1382, Energy: -28.739022-0.001528j
[2025-08-24 05:24:37] [Iter 1961/4650] R3[910/1200], Temp: 0.1373, Energy: -28.734087+0.000574j
[2025-08-24 05:24:47] [Iter 1962/4650] R3[911/1200], Temp: 0.1364, Energy: -28.744544+0.001742j
[2025-08-24 05:24:58] [Iter 1963/4650] R3[912/1200], Temp: 0.1355, Energy: -28.738564+0.000446j
[2025-08-24 05:25:08] [Iter 1964/4650] R3[913/1200], Temp: 0.1346, Energy: -28.733946-0.000022j
[2025-08-24 05:25:18] [Iter 1965/4650] R3[914/1200], Temp: 0.1337, Energy: -28.731593-0.000600j
[2025-08-24 05:25:28] [Iter 1966/4650] R3[915/1200], Temp: 0.1328, Energy: -28.737948-0.002907j
[2025-08-24 05:25:38] [Iter 1967/4650] R3[916/1200], Temp: 0.1320, Energy: -28.733684+0.001448j
[2025-08-24 05:25:48] [Iter 1968/4650] R3[917/1200], Temp: 0.1311, Energy: -28.735945-0.000315j
[2025-08-24 05:25:58] [Iter 1969/4650] R3[918/1200], Temp: 0.1302, Energy: -28.734839+0.002570j
[2025-08-24 05:26:09] [Iter 1970/4650] R3[919/1200], Temp: 0.1293, Energy: -28.738913-0.000706j
[2025-08-24 05:26:19] [Iter 1971/4650] R3[920/1200], Temp: 0.1284, Energy: -28.735277+0.000190j
[2025-08-24 05:26:29] [Iter 1972/4650] R3[921/1200], Temp: 0.1276, Energy: -28.734143-0.000748j
[2025-08-24 05:26:39] [Iter 1973/4650] R3[922/1200], Temp: 0.1267, Energy: -28.735961+0.001182j
[2025-08-24 05:26:49] [Iter 1974/4650] R3[923/1200], Temp: 0.1258, Energy: -28.743483-0.000893j
[2025-08-24 05:26:59] [Iter 1975/4650] R3[924/1200], Temp: 0.1249, Energy: -28.737290-0.000042j
[2025-08-24 05:27:10] [Iter 1976/4650] R3[925/1200], Temp: 0.1241, Energy: -28.738551+0.002906j
[2025-08-24 05:27:20] [Iter 1977/4650] R3[926/1200], Temp: 0.1232, Energy: -28.737525+0.000281j
[2025-08-24 05:27:30] [Iter 1978/4650] R3[927/1200], Temp: 0.1224, Energy: -28.737642+0.001880j
[2025-08-24 05:27:40] [Iter 1979/4650] R3[928/1200], Temp: 0.1215, Energy: -28.744915+0.000257j
[2025-08-24 05:27:50] [Iter 1980/4650] R3[929/1200], Temp: 0.1206, Energy: -28.736343-0.000644j
[2025-08-24 05:28:00] [Iter 1981/4650] R3[930/1200], Temp: 0.1198, Energy: -28.739931-0.003305j
[2025-08-24 05:28:10] [Iter 1982/4650] R3[931/1200], Temp: 0.1189, Energy: -28.741781+0.001636j
[2025-08-24 05:28:21] [Iter 1983/4650] R3[932/1200], Temp: 0.1181, Energy: -28.736615+0.003482j
[2025-08-24 05:28:31] [Iter 1984/4650] R3[933/1200], Temp: 0.1173, Energy: -28.738559-0.001439j
[2025-08-24 05:28:41] [Iter 1985/4650] R3[934/1200], Temp: 0.1164, Energy: -28.738717+0.000068j
[2025-08-24 05:28:51] [Iter 1986/4650] R3[935/1200], Temp: 0.1156, Energy: -28.734416-0.000519j
[2025-08-24 05:29:01] [Iter 1987/4650] R3[936/1200], Temp: 0.1147, Energy: -28.739260-0.002624j
[2025-08-24 05:29:11] [Iter 1988/4650] R3[937/1200], Temp: 0.1139, Energy: -28.734805+0.002612j
[2025-08-24 05:29:21] [Iter 1989/4650] R3[938/1200], Temp: 0.1131, Energy: -28.738082+0.001918j
[2025-08-24 05:29:32] [Iter 1990/4650] R3[939/1200], Temp: 0.1123, Energy: -28.731243-0.000289j
[2025-08-24 05:29:42] [Iter 1991/4650] R3[940/1200], Temp: 0.1114, Energy: -28.736855-0.001350j
[2025-08-24 05:29:52] [Iter 1992/4650] R3[941/1200], Temp: 0.1106, Energy: -28.741767+0.003589j
[2025-08-24 05:30:02] [Iter 1993/4650] R3[942/1200], Temp: 0.1098, Energy: -28.746222-0.000841j
[2025-08-24 05:30:12] [Iter 1994/4650] R3[943/1200], Temp: 0.1090, Energy: -28.731956-0.002037j
[2025-08-24 05:30:22] [Iter 1995/4650] R3[944/1200], Temp: 0.1082, Energy: -28.736062+0.000108j
[2025-08-24 05:30:32] [Iter 1996/4650] R3[945/1200], Temp: 0.1073, Energy: -28.734997+0.002089j
[2025-08-24 05:30:43] [Iter 1997/4650] R3[946/1200], Temp: 0.1065, Energy: -28.741872-0.001345j
[2025-08-24 05:30:53] [Iter 1998/4650] R3[947/1200], Temp: 0.1057, Energy: -28.735943+0.006269j
[2025-08-24 05:31:03] [Iter 1999/4650] R3[948/1200], Temp: 0.1049, Energy: -28.738848+0.000662j
[2025-08-24 05:31:13] [Iter 2000/4650] R3[949/1200], Temp: 0.1041, Energy: -28.734262+0.001464j
[2025-08-24 05:31:13] ✓ Checkpoint saved: checkpoint_iter_002000.pkl
[2025-08-24 05:31:23] [Iter 2001/4650] R3[950/1200], Temp: 0.1033, Energy: -28.743312-0.000639j
[2025-08-24 05:31:33] [Iter 2002/4650] R3[951/1200], Temp: 0.1025, Energy: -28.740756+0.003640j
[2025-08-24 05:31:43] [Iter 2003/4650] R3[952/1200], Temp: 0.1017, Energy: -28.738866+0.000696j
[2025-08-24 05:31:54] [Iter 2004/4650] R3[953/1200], Temp: 0.1009, Energy: -28.736357+0.000274j
[2025-08-24 05:32:04] [Iter 2005/4650] R3[954/1200], Temp: 0.1002, Energy: -28.739535-0.002030j
[2025-08-24 05:32:14] [Iter 2006/4650] R3[955/1200], Temp: 0.0994, Energy: -28.734233-0.001230j
[2025-08-24 05:32:24] [Iter 2007/4650] R3[956/1200], Temp: 0.0986, Energy: -28.732950-0.000941j
[2025-08-24 05:32:34] [Iter 2008/4650] R3[957/1200], Temp: 0.0978, Energy: -28.736006+0.000164j
[2025-08-24 05:32:44] [Iter 2009/4650] R3[958/1200], Temp: 0.0970, Energy: -28.741467-0.000558j
[2025-08-24 05:32:54] [Iter 2010/4650] R3[959/1200], Temp: 0.0963, Energy: -28.734672+0.000436j
[2025-08-24 05:33:05] [Iter 2011/4650] R3[960/1200], Temp: 0.0955, Energy: -28.741523-0.000623j
[2025-08-24 05:33:15] [Iter 2012/4650] R3[961/1200], Temp: 0.0947, Energy: -28.737012-0.002688j
[2025-08-24 05:33:25] [Iter 2013/4650] R3[962/1200], Temp: 0.0940, Energy: -28.744252-0.002436j
[2025-08-24 05:33:35] [Iter 2014/4650] R3[963/1200], Temp: 0.0932, Energy: -28.733883-0.000005j
[2025-08-24 05:33:45] [Iter 2015/4650] R3[964/1200], Temp: 0.0924, Energy: -28.736274-0.000557j
[2025-08-24 05:33:55] [Iter 2016/4650] R3[965/1200], Temp: 0.0917, Energy: -28.739773+0.002473j
[2025-08-24 05:34:05] [Iter 2017/4650] R3[966/1200], Temp: 0.0909, Energy: -28.733355+0.002772j
[2025-08-24 05:34:16] [Iter 2018/4650] R3[967/1200], Temp: 0.0902, Energy: -28.732988-0.001231j
[2025-08-24 05:34:26] [Iter 2019/4650] R3[968/1200], Temp: 0.0894, Energy: -28.736869-0.000500j
[2025-08-24 05:34:36] [Iter 2020/4650] R3[969/1200], Temp: 0.0887, Energy: -28.739922+0.000651j
[2025-08-24 05:34:46] [Iter 2021/4650] R3[970/1200], Temp: 0.0879, Energy: -28.737238+0.000852j
[2025-08-24 05:34:56] [Iter 2022/4650] R3[971/1200], Temp: 0.0872, Energy: -28.734638-0.002921j
[2025-08-24 05:35:06] [Iter 2023/4650] R3[972/1200], Temp: 0.0865, Energy: -28.732265+0.000994j
[2025-08-24 05:35:16] [Iter 2024/4650] R3[973/1200], Temp: 0.0857, Energy: -28.739908+0.002023j
[2025-08-24 05:35:27] [Iter 2025/4650] R3[974/1200], Temp: 0.0850, Energy: -28.740595+0.001660j
[2025-08-24 05:35:37] [Iter 2026/4650] R3[975/1200], Temp: 0.0843, Energy: -28.733989-0.002472j
[2025-08-24 05:35:47] [Iter 2027/4650] R3[976/1200], Temp: 0.0835, Energy: -28.741000+0.000374j
[2025-08-24 05:35:57] [Iter 2028/4650] R3[977/1200], Temp: 0.0828, Energy: -28.734357+0.000915j
[2025-08-24 05:36:07] [Iter 2029/4650] R3[978/1200], Temp: 0.0821, Energy: -28.739788-0.001816j
[2025-08-24 05:36:17] [Iter 2030/4650] R3[979/1200], Temp: 0.0814, Energy: -28.734252-0.004131j
[2025-08-24 05:36:27] [Iter 2031/4650] R3[980/1200], Temp: 0.0807, Energy: -28.735159-0.002148j
[2025-08-24 05:36:38] [Iter 2032/4650] R3[981/1200], Temp: 0.0800, Energy: -28.740565+0.001685j
[2025-08-24 05:36:48] [Iter 2033/4650] R3[982/1200], Temp: 0.0792, Energy: -28.735391+0.002392j
[2025-08-24 05:36:58] [Iter 2034/4650] R3[983/1200], Temp: 0.0785, Energy: -28.739717-0.001147j
[2025-08-24 05:37:08] [Iter 2035/4650] R3[984/1200], Temp: 0.0778, Energy: -28.732071+0.000898j
[2025-08-24 05:37:18] [Iter 2036/4650] R3[985/1200], Temp: 0.0771, Energy: -28.734271+0.000037j
[2025-08-24 05:37:28] [Iter 2037/4650] R3[986/1200], Temp: 0.0764, Energy: -28.736936+0.004391j
[2025-08-24 05:37:38] [Iter 2038/4650] R3[987/1200], Temp: 0.0757, Energy: -28.733079+0.000454j
[2025-08-24 05:37:49] [Iter 2039/4650] R3[988/1200], Temp: 0.0751, Energy: -28.741214+0.001219j
[2025-08-24 05:37:59] [Iter 2040/4650] R3[989/1200], Temp: 0.0744, Energy: -28.736988-0.000020j
[2025-08-24 05:38:09] [Iter 2041/4650] R3[990/1200], Temp: 0.0737, Energy: -28.737675+0.003298j
[2025-08-24 05:38:19] [Iter 2042/4650] R3[991/1200], Temp: 0.0730, Energy: -28.739747+0.001017j
[2025-08-24 05:38:29] [Iter 2043/4650] R3[992/1200], Temp: 0.0723, Energy: -28.734493+0.000136j
[2025-08-24 05:38:39] [Iter 2044/4650] R3[993/1200], Temp: 0.0716, Energy: -28.733473-0.001284j
[2025-08-24 05:38:49] [Iter 2045/4650] R3[994/1200], Temp: 0.0710, Energy: -28.741261-0.000894j
[2025-08-24 05:39:00] [Iter 2046/4650] R3[995/1200], Temp: 0.0703, Energy: -28.744574+0.000525j
[2025-08-24 05:39:10] [Iter 2047/4650] R3[996/1200], Temp: 0.0696, Energy: -28.737214-0.003040j
[2025-08-24 05:39:20] [Iter 2048/4650] R3[997/1200], Temp: 0.0690, Energy: -28.734356-0.000307j
[2025-08-24 05:39:30] [Iter 2049/4650] R3[998/1200], Temp: 0.0683, Energy: -28.738660+0.001938j
[2025-08-24 05:39:40] [Iter 2050/4650] R3[999/1200], Temp: 0.0676, Energy: -28.738517-0.001908j
[2025-08-24 05:39:50] [Iter 2051/4650] R3[1000/1200], Temp: 0.0670, Energy: -28.735376+0.000903j
[2025-08-24 05:40:00] [Iter 2052/4650] R3[1001/1200], Temp: 0.0663, Energy: -28.738453+0.002100j
[2025-08-24 05:40:11] [Iter 2053/4650] R3[1002/1200], Temp: 0.0657, Energy: -28.740685+0.000714j
[2025-08-24 05:40:21] [Iter 2054/4650] R3[1003/1200], Temp: 0.0650, Energy: -28.739276+0.000315j
[2025-08-24 05:40:31] [Iter 2055/4650] R3[1004/1200], Temp: 0.0644, Energy: -28.745498-0.000668j
[2025-08-24 05:40:41] [Iter 2056/4650] R3[1005/1200], Temp: 0.0638, Energy: -28.735388-0.001432j
[2025-08-24 05:40:51] [Iter 2057/4650] R3[1006/1200], Temp: 0.0631, Energy: -28.737578-0.001744j
[2025-08-24 05:41:01] [Iter 2058/4650] R3[1007/1200], Temp: 0.0625, Energy: -28.737623+0.001418j
[2025-08-24 05:41:11] [Iter 2059/4650] R3[1008/1200], Temp: 0.0618, Energy: -28.736337+0.001397j
[2025-08-24 05:41:21] [Iter 2060/4650] R3[1009/1200], Temp: 0.0612, Energy: -28.735942-0.003520j
[2025-08-24 05:41:32] [Iter 2061/4650] R3[1010/1200], Temp: 0.0606, Energy: -28.734736+0.001612j
[2025-08-24 05:41:42] [Iter 2062/4650] R3[1011/1200], Temp: 0.0600, Energy: -28.732459+0.000996j
[2025-08-24 05:41:52] [Iter 2063/4650] R3[1012/1200], Temp: 0.0593, Energy: -28.738269+0.000281j
[2025-08-24 05:42:02] [Iter 2064/4650] R3[1013/1200], Temp: 0.0587, Energy: -28.733491-0.000133j
[2025-08-24 05:42:12] [Iter 2065/4650] R3[1014/1200], Temp: 0.0581, Energy: -28.737276-0.000264j
[2025-08-24 05:42:22] [Iter 2066/4650] R3[1015/1200], Temp: 0.0575, Energy: -28.732974-0.001681j
[2025-08-24 05:42:32] [Iter 2067/4650] R3[1016/1200], Temp: 0.0569, Energy: -28.739083+0.001328j
[2025-08-24 05:42:43] [Iter 2068/4650] R3[1017/1200], Temp: 0.0563, Energy: -28.735381+0.001495j
[2025-08-24 05:42:53] [Iter 2069/4650] R3[1018/1200], Temp: 0.0557, Energy: -28.736187-0.003273j
[2025-08-24 05:43:03] [Iter 2070/4650] R3[1019/1200], Temp: 0.0551, Energy: -28.743815+0.000696j
[2025-08-24 05:43:13] [Iter 2071/4650] R3[1020/1200], Temp: 0.0545, Energy: -28.736936+0.003374j
[2025-08-24 05:43:23] [Iter 2072/4650] R3[1021/1200], Temp: 0.0539, Energy: -28.736291+0.001745j
[2025-08-24 05:43:33] [Iter 2073/4650] R3[1022/1200], Temp: 0.0533, Energy: -28.740933-0.000423j
[2025-08-24 05:43:43] [Iter 2074/4650] R3[1023/1200], Temp: 0.0527, Energy: -28.741975-0.000165j
[2025-08-24 05:43:54] [Iter 2075/4650] R3[1024/1200], Temp: 0.0521, Energy: -28.735212+0.001324j
[2025-08-24 05:44:04] [Iter 2076/4650] R3[1025/1200], Temp: 0.0516, Energy: -28.733116+0.000286j
[2025-08-24 05:44:14] [Iter 2077/4650] R3[1026/1200], Temp: 0.0510, Energy: -28.737757-0.001672j
[2025-08-24 05:44:24] [Iter 2078/4650] R3[1027/1200], Temp: 0.0504, Energy: -28.739213+0.000517j
[2025-08-24 05:44:34] [Iter 2079/4650] R3[1028/1200], Temp: 0.0498, Energy: -28.735044-0.001581j
[2025-08-24 05:44:44] [Iter 2080/4650] R3[1029/1200], Temp: 0.0493, Energy: -28.733638+0.001499j
[2025-08-24 05:44:54] [Iter 2081/4650] R3[1030/1200], Temp: 0.0487, Energy: -28.740047+0.000078j
[2025-08-24 05:45:05] [Iter 2082/4650] R3[1031/1200], Temp: 0.0481, Energy: -28.733151+0.000319j
[2025-08-24 05:45:15] [Iter 2083/4650] R3[1032/1200], Temp: 0.0476, Energy: -28.732702+0.000977j
[2025-08-24 05:45:25] [Iter 2084/4650] R3[1033/1200], Temp: 0.0470, Energy: -28.729333+0.000219j
[2025-08-24 05:45:35] [Iter 2085/4650] R3[1034/1200], Temp: 0.0465, Energy: -28.738587-0.000038j
[2025-08-24 05:45:45] [Iter 2086/4650] R3[1035/1200], Temp: 0.0459, Energy: -28.739346-0.001513j
[2025-08-24 05:45:55] [Iter 2087/4650] R3[1036/1200], Temp: 0.0454, Energy: -28.739832-0.000047j
[2025-08-24 05:46:05] [Iter 2088/4650] R3[1037/1200], Temp: 0.0448, Energy: -28.740854-0.000591j
[2025-08-24 05:46:16] [Iter 2089/4650] R3[1038/1200], Temp: 0.0443, Energy: -28.736443+0.000911j
[2025-08-24 05:46:26] [Iter 2090/4650] R3[1039/1200], Temp: 0.0438, Energy: -28.736159-0.002127j
[2025-08-24 05:46:36] [Iter 2091/4650] R3[1040/1200], Temp: 0.0432, Energy: -28.739323-0.002221j
[2025-08-24 05:46:46] [Iter 2092/4650] R3[1041/1200], Temp: 0.0427, Energy: -28.743608-0.000428j
[2025-08-24 05:46:56] [Iter 2093/4650] R3[1042/1200], Temp: 0.0422, Energy: -28.737578+0.001388j
[2025-08-24 05:47:06] [Iter 2094/4650] R3[1043/1200], Temp: 0.0416, Energy: -28.740396+0.001557j
[2025-08-24 05:47:16] [Iter 2095/4650] R3[1044/1200], Temp: 0.0411, Energy: -28.740359-0.000909j
[2025-08-24 05:47:27] [Iter 2096/4650] R3[1045/1200], Temp: 0.0406, Energy: -28.741051+0.001030j
[2025-08-24 05:47:37] [Iter 2097/4650] R3[1046/1200], Temp: 0.0401, Energy: -28.742917+0.000468j
[2025-08-24 05:47:47] [Iter 2098/4650] R3[1047/1200], Temp: 0.0396, Energy: -28.737005-0.001965j
[2025-08-24 05:47:57] [Iter 2099/4650] R3[1048/1200], Temp: 0.0391, Energy: -28.740389-0.002892j
[2025-08-24 05:48:07] [Iter 2100/4650] R3[1049/1200], Temp: 0.0386, Energy: -28.736362-0.000382j
[2025-08-24 05:48:17] [Iter 2101/4650] R3[1050/1200], Temp: 0.0381, Energy: -28.740695-0.003785j
[2025-08-24 05:48:27] [Iter 2102/4650] R3[1051/1200], Temp: 0.0376, Energy: -28.739478+0.000001j
[2025-08-24 05:48:37] [Iter 2103/4650] R3[1052/1200], Temp: 0.0371, Energy: -28.739732+0.000674j
[2025-08-24 05:48:48] [Iter 2104/4650] R3[1053/1200], Temp: 0.0366, Energy: -28.741534+0.001812j
[2025-08-24 05:48:58] [Iter 2105/4650] R3[1054/1200], Temp: 0.0361, Energy: -28.742667-0.000577j
[2025-08-24 05:49:08] [Iter 2106/4650] R3[1055/1200], Temp: 0.0356, Energy: -28.737909-0.002021j
[2025-08-24 05:49:18] [Iter 2107/4650] R3[1056/1200], Temp: 0.0351, Energy: -28.738789-0.000524j
[2025-08-24 05:49:28] [Iter 2108/4650] R3[1057/1200], Temp: 0.0346, Energy: -28.738634-0.001820j
[2025-08-24 05:49:38] [Iter 2109/4650] R3[1058/1200], Temp: 0.0342, Energy: -28.738209+0.002330j
[2025-08-24 05:49:48] [Iter 2110/4650] R3[1059/1200], Temp: 0.0337, Energy: -28.736959+0.003194j
[2025-08-24 05:49:59] [Iter 2111/4650] R3[1060/1200], Temp: 0.0332, Energy: -28.743811+0.003190j
[2025-08-24 05:50:09] [Iter 2112/4650] R3[1061/1200], Temp: 0.0327, Energy: -28.736911-0.001513j
[2025-08-24 05:50:19] [Iter 2113/4650] R3[1062/1200], Temp: 0.0323, Energy: -28.736250+0.000569j
[2025-08-24 05:50:29] [Iter 2114/4650] R3[1063/1200], Temp: 0.0318, Energy: -28.741311-0.000662j
[2025-08-24 05:50:39] [Iter 2115/4650] R3[1064/1200], Temp: 0.0314, Energy: -28.742019-0.001639j
[2025-08-24 05:50:49] [Iter 2116/4650] R3[1065/1200], Temp: 0.0309, Energy: -28.739773+0.001191j
[2025-08-24 05:50:59] [Iter 2117/4650] R3[1066/1200], Temp: 0.0305, Energy: -28.746018+0.000900j
[2025-08-24 05:51:10] [Iter 2118/4650] R3[1067/1200], Temp: 0.0300, Energy: -28.735257+0.000803j
[2025-08-24 05:51:20] [Iter 2119/4650] R3[1068/1200], Temp: 0.0296, Energy: -28.736641+0.000380j
[2025-08-24 05:51:30] [Iter 2120/4650] R3[1069/1200], Temp: 0.0291, Energy: -28.733968-0.000799j
[2025-08-24 05:51:40] [Iter 2121/4650] R3[1070/1200], Temp: 0.0287, Energy: -28.736698+0.001604j
[2025-08-24 05:51:50] [Iter 2122/4650] R3[1071/1200], Temp: 0.0282, Energy: -28.748636+0.000421j
[2025-08-24 05:52:00] [Iter 2123/4650] R3[1072/1200], Temp: 0.0278, Energy: -28.743879-0.002890j
[2025-08-24 05:52:10] [Iter 2124/4650] R3[1073/1200], Temp: 0.0274, Energy: -28.737957+0.001398j
[2025-08-24 05:52:21] [Iter 2125/4650] R3[1074/1200], Temp: 0.0270, Energy: -28.736659-0.000688j
[2025-08-24 05:52:31] [Iter 2126/4650] R3[1075/1200], Temp: 0.0265, Energy: -28.737801-0.000969j
[2025-08-24 05:52:41] [Iter 2127/4650] R3[1076/1200], Temp: 0.0261, Energy: -28.737711+0.001773j
[2025-08-24 05:52:51] [Iter 2128/4650] R3[1077/1200], Temp: 0.0257, Energy: -28.734815+0.001607j
[2025-08-24 05:53:01] [Iter 2129/4650] R3[1078/1200], Temp: 0.0253, Energy: -28.740606+0.003832j
[2025-08-24 05:53:11] [Iter 2130/4650] R3[1079/1200], Temp: 0.0249, Energy: -28.741181+0.000597j
[2025-08-24 05:53:21] [Iter 2131/4650] R3[1080/1200], Temp: 0.0245, Energy: -28.733263+0.000618j
[2025-08-24 05:53:32] [Iter 2132/4650] R3[1081/1200], Temp: 0.0241, Energy: -28.738917+0.000994j
[2025-08-24 05:53:42] [Iter 2133/4650] R3[1082/1200], Temp: 0.0237, Energy: -28.734727-0.001500j
[2025-08-24 05:53:52] [Iter 2134/4650] R3[1083/1200], Temp: 0.0233, Energy: -28.746575-0.001520j
[2025-08-24 05:54:02] [Iter 2135/4650] R3[1084/1200], Temp: 0.0229, Energy: -28.740411-0.000018j
[2025-08-24 05:54:12] [Iter 2136/4650] R3[1085/1200], Temp: 0.0225, Energy: -28.738544-0.001571j
[2025-08-24 05:54:22] [Iter 2137/4650] R3[1086/1200], Temp: 0.0221, Energy: -28.739720-0.001105j
[2025-08-24 05:54:32] [Iter 2138/4650] R3[1087/1200], Temp: 0.0217, Energy: -28.737575+0.000459j
[2025-08-24 05:54:43] [Iter 2139/4650] R3[1088/1200], Temp: 0.0213, Energy: -28.733013+0.000770j
[2025-08-24 05:54:53] [Iter 2140/4650] R3[1089/1200], Temp: 0.0210, Energy: -28.736298-0.001955j
[2025-08-24 05:55:03] [Iter 2141/4650] R3[1090/1200], Temp: 0.0206, Energy: -28.745526-0.001040j
[2025-08-24 05:55:13] [Iter 2142/4650] R3[1091/1200], Temp: 0.0202, Energy: -28.739215-0.000368j
[2025-08-24 05:55:23] [Iter 2143/4650] R3[1092/1200], Temp: 0.0199, Energy: -28.737200+0.001645j
[2025-08-24 05:55:33] [Iter 2144/4650] R3[1093/1200], Temp: 0.0195, Energy: -28.740460+0.002517j
[2025-08-24 05:55:43] [Iter 2145/4650] R3[1094/1200], Temp: 0.0191, Energy: -28.746566+0.000358j
[2025-08-24 05:55:53] [Iter 2146/4650] R3[1095/1200], Temp: 0.0188, Energy: -28.736640+0.000596j
[2025-08-24 05:56:04] [Iter 2147/4650] R3[1096/1200], Temp: 0.0184, Energy: -28.746867+0.000114j
[2025-08-24 05:56:14] [Iter 2148/4650] R3[1097/1200], Temp: 0.0181, Energy: -28.738125-0.000095j
[2025-08-24 05:56:24] [Iter 2149/4650] R3[1098/1200], Temp: 0.0177, Energy: -28.740867-0.001201j
[2025-08-24 05:56:34] [Iter 2150/4650] R3[1099/1200], Temp: 0.0174, Energy: -28.740614+0.001723j
[2025-08-24 05:56:44] [Iter 2151/4650] R3[1100/1200], Temp: 0.0170, Energy: -28.745648-0.000854j
[2025-08-24 05:56:54] [Iter 2152/4650] R3[1101/1200], Temp: 0.0167, Energy: -28.739901+0.000724j
[2025-08-24 05:57:05] [Iter 2153/4650] R3[1102/1200], Temp: 0.0164, Energy: -28.742446-0.000239j
[2025-08-24 05:57:15] [Iter 2154/4650] R3[1103/1200], Temp: 0.0160, Energy: -28.732524+0.000556j
[2025-08-24 05:57:25] [Iter 2155/4650] R3[1104/1200], Temp: 0.0157, Energy: -28.739851+0.000293j
[2025-08-24 05:57:35] [Iter 2156/4650] R3[1105/1200], Temp: 0.0154, Energy: -28.732991-0.000953j
[2025-08-24 05:57:45] [Iter 2157/4650] R3[1106/1200], Temp: 0.0151, Energy: -28.744029+0.000737j
[2025-08-24 05:57:55] [Iter 2158/4650] R3[1107/1200], Temp: 0.0147, Energy: -28.740946+0.002088j
[2025-08-24 05:58:05] [Iter 2159/4650] R3[1108/1200], Temp: 0.0144, Energy: -28.739578-0.000191j
[2025-08-24 05:58:15] [Iter 2160/4650] R3[1109/1200], Temp: 0.0141, Energy: -28.742271+0.002268j
[2025-08-24 05:58:26] [Iter 2161/4650] R3[1110/1200], Temp: 0.0138, Energy: -28.741576-0.001914j
[2025-08-24 05:58:36] [Iter 2162/4650] R3[1111/1200], Temp: 0.0135, Energy: -28.736878-0.001082j
[2025-08-24 05:58:46] [Iter 2163/4650] R3[1112/1200], Temp: 0.0132, Energy: -28.732840-0.002331j
[2025-08-24 05:58:56] [Iter 2164/4650] R3[1113/1200], Temp: 0.0129, Energy: -28.735185+0.000402j
[2025-08-24 05:59:06] [Iter 2165/4650] R3[1114/1200], Temp: 0.0126, Energy: -28.736074-0.001310j
[2025-08-24 05:59:16] [Iter 2166/4650] R3[1115/1200], Temp: 0.0123, Energy: -28.745206-0.000336j
[2025-08-24 05:59:26] [Iter 2167/4650] R3[1116/1200], Temp: 0.0120, Energy: -28.737298+0.001533j
[2025-08-24 05:59:37] [Iter 2168/4650] R3[1117/1200], Temp: 0.0118, Energy: -28.735794-0.001728j
[2025-08-24 05:59:47] [Iter 2169/4650] R3[1118/1200], Temp: 0.0115, Energy: -28.738234+0.001161j
[2025-08-24 05:59:57] [Iter 2170/4650] R3[1119/1200], Temp: 0.0112, Energy: -28.741444-0.000961j
[2025-08-24 06:00:07] [Iter 2171/4650] R3[1120/1200], Temp: 0.0109, Energy: -28.742725+0.001497j
[2025-08-24 06:00:17] [Iter 2172/4650] R3[1121/1200], Temp: 0.0107, Energy: -28.738996-0.002170j
[2025-08-24 06:00:27] [Iter 2173/4650] R3[1122/1200], Temp: 0.0104, Energy: -28.743755+0.001185j
[2025-08-24 06:00:37] [Iter 2174/4650] R3[1123/1200], Temp: 0.0101, Energy: -28.739330+0.000714j
[2025-08-24 06:00:48] [Iter 2175/4650] R3[1124/1200], Temp: 0.0099, Energy: -28.734755-0.000884j
[2025-08-24 06:00:58] [Iter 2176/4650] R3[1125/1200], Temp: 0.0096, Energy: -28.739171+0.000033j
[2025-08-24 06:01:08] [Iter 2177/4650] R3[1126/1200], Temp: 0.0094, Energy: -28.735756+0.001265j
[2025-08-24 06:01:18] [Iter 2178/4650] R3[1127/1200], Temp: 0.0091, Energy: -28.734931-0.001772j
[2025-08-24 06:01:28] [Iter 2179/4650] R3[1128/1200], Temp: 0.0089, Energy: -28.743465+0.000709j
[2025-08-24 06:01:38] [Iter 2180/4650] R3[1129/1200], Temp: 0.0086, Energy: -28.737204+0.001948j
[2025-08-24 06:01:48] [Iter 2181/4650] R3[1130/1200], Temp: 0.0084, Energy: -28.741599-0.002747j
[2025-08-24 06:01:59] [Iter 2182/4650] R3[1131/1200], Temp: 0.0081, Energy: -28.739920+0.000466j
[2025-08-24 06:02:09] [Iter 2183/4650] R3[1132/1200], Temp: 0.0079, Energy: -28.733521+0.000121j
[2025-08-24 06:02:19] [Iter 2184/4650] R3[1133/1200], Temp: 0.0077, Energy: -28.745308+0.001429j
[2025-08-24 06:02:29] [Iter 2185/4650] R3[1134/1200], Temp: 0.0074, Energy: -28.738899+0.000986j
[2025-08-24 06:02:39] [Iter 2186/4650] R3[1135/1200], Temp: 0.0072, Energy: -28.743768-0.001063j
[2025-08-24 06:02:49] [Iter 2187/4650] R3[1136/1200], Temp: 0.0070, Energy: -28.739031-0.000297j
[2025-08-24 06:02:59] [Iter 2188/4650] R3[1137/1200], Temp: 0.0068, Energy: -28.737431+0.000013j
[2025-08-24 06:03:09] [Iter 2189/4650] R3[1138/1200], Temp: 0.0066, Energy: -28.741011+0.000101j
[2025-08-24 06:03:20] [Iter 2190/4650] R3[1139/1200], Temp: 0.0064, Energy: -28.745282-0.000126j
[2025-08-24 06:03:30] [Iter 2191/4650] R3[1140/1200], Temp: 0.0062, Energy: -28.744191+0.000765j
[2025-08-24 06:03:40] [Iter 2192/4650] R3[1141/1200], Temp: 0.0060, Energy: -28.744306+0.000837j
[2025-08-24 06:03:50] [Iter 2193/4650] R3[1142/1200], Temp: 0.0058, Energy: -28.738559-0.001477j
[2025-08-24 06:04:00] [Iter 2194/4650] R3[1143/1200], Temp: 0.0056, Energy: -28.740361+0.000266j
[2025-08-24 06:04:10] [Iter 2195/4650] R3[1144/1200], Temp: 0.0054, Energy: -28.742512-0.001593j
[2025-08-24 06:04:21] [Iter 2196/4650] R3[1145/1200], Temp: 0.0052, Energy: -28.738640+0.001580j
[2025-08-24 06:04:31] [Iter 2197/4650] R3[1146/1200], Temp: 0.0050, Energy: -28.735415+0.001718j
[2025-08-24 06:04:41] [Iter 2198/4650] R3[1147/1200], Temp: 0.0048, Energy: -28.735111+0.000070j
[2025-08-24 06:04:51] [Iter 2199/4650] R3[1148/1200], Temp: 0.0046, Energy: -28.739034+0.000389j
[2025-08-24 06:05:01] [Iter 2200/4650] R3[1149/1200], Temp: 0.0045, Energy: -28.740661+0.002611j
[2025-08-24 06:05:11] [Iter 2201/4650] R3[1150/1200], Temp: 0.0043, Energy: -28.738168-0.001175j
[2025-08-24 06:05:21] [Iter 2202/4650] R3[1151/1200], Temp: 0.0041, Energy: -28.737373-0.000483j
[2025-08-24 06:05:31] [Iter 2203/4650] R3[1152/1200], Temp: 0.0039, Energy: -28.739949-0.000845j
[2025-08-24 06:05:42] [Iter 2204/4650] R3[1153/1200], Temp: 0.0038, Energy: -28.743809-0.001009j
[2025-08-24 06:05:52] [Iter 2205/4650] R3[1154/1200], Temp: 0.0036, Energy: -28.745785+0.001906j
[2025-08-24 06:06:02] [Iter 2206/4650] R3[1155/1200], Temp: 0.0035, Energy: -28.733669+0.001472j
[2025-08-24 06:06:12] [Iter 2207/4650] R3[1156/1200], Temp: 0.0033, Energy: -28.749152-0.000161j
[2025-08-24 06:06:22] [Iter 2208/4650] R3[1157/1200], Temp: 0.0032, Energy: -28.742563+0.000970j
[2025-08-24 06:06:32] [Iter 2209/4650] R3[1158/1200], Temp: 0.0030, Energy: -28.741126-0.001604j
[2025-08-24 06:06:42] [Iter 2210/4650] R3[1159/1200], Temp: 0.0029, Energy: -28.737644-0.000416j
[2025-08-24 06:06:53] [Iter 2211/4650] R3[1160/1200], Temp: 0.0027, Energy: -28.743174-0.000482j
[2025-08-24 06:07:03] [Iter 2212/4650] R3[1161/1200], Temp: 0.0026, Energy: -28.740839+0.003347j
[2025-08-24 06:07:13] [Iter 2213/4650] R3[1162/1200], Temp: 0.0025, Energy: -28.740727+0.000477j
[2025-08-24 06:07:23] [Iter 2214/4650] R3[1163/1200], Temp: 0.0023, Energy: -28.744830+0.000921j
[2025-08-24 06:07:33] [Iter 2215/4650] R3[1164/1200], Temp: 0.0022, Energy: -28.738459+0.001617j
[2025-08-24 06:07:43] [Iter 2216/4650] R3[1165/1200], Temp: 0.0021, Energy: -28.743951-0.002638j
[2025-08-24 06:07:54] [Iter 2217/4650] R3[1166/1200], Temp: 0.0020, Energy: -28.741874+0.000656j
[2025-08-24 06:08:04] [Iter 2218/4650] R3[1167/1200], Temp: 0.0019, Energy: -28.745335+0.000413j
[2025-08-24 06:08:14] [Iter 2219/4650] R3[1168/1200], Temp: 0.0018, Energy: -28.751657+0.001271j
[2025-08-24 06:08:24] [Iter 2220/4650] R3[1169/1200], Temp: 0.0016, Energy: -28.739566-0.000782j
[2025-08-24 06:08:34] [Iter 2221/4650] R3[1170/1200], Temp: 0.0015, Energy: -28.737970-0.000852j
[2025-08-24 06:08:44] [Iter 2222/4650] R3[1171/1200], Temp: 0.0014, Energy: -28.739366-0.000093j
[2025-08-24 06:08:54] [Iter 2223/4650] R3[1172/1200], Temp: 0.0013, Energy: -28.737383+0.000299j
[2025-08-24 06:09:05] [Iter 2224/4650] R3[1173/1200], Temp: 0.0012, Energy: -28.748124-0.000363j
[2025-08-24 06:09:15] [Iter 2225/4650] R3[1174/1200], Temp: 0.0012, Energy: -28.744304-0.000662j
[2025-08-24 06:09:25] [Iter 2226/4650] R3[1175/1200], Temp: 0.0011, Energy: -28.739220-0.002043j
[2025-08-24 06:09:35] [Iter 2227/4650] R3[1176/1200], Temp: 0.0010, Energy: -28.741106+0.001806j
[2025-08-24 06:09:45] [Iter 2228/4650] R3[1177/1200], Temp: 0.0009, Energy: -28.743325+0.002050j
[2025-08-24 06:09:55] [Iter 2229/4650] R3[1178/1200], Temp: 0.0008, Energy: -28.736039+0.001599j
[2025-08-24 06:10:05] [Iter 2230/4650] R3[1179/1200], Temp: 0.0008, Energy: -28.743227-0.002162j
[2025-08-24 06:10:15] [Iter 2231/4650] R3[1180/1200], Temp: 0.0007, Energy: -28.739865-0.000940j
[2025-08-24 06:10:26] [Iter 2232/4650] R3[1181/1200], Temp: 0.0006, Energy: -28.745076+0.000488j
[2025-08-24 06:10:36] [Iter 2233/4650] R3[1182/1200], Temp: 0.0006, Energy: -28.741887-0.000913j
[2025-08-24 06:10:46] [Iter 2234/4650] R3[1183/1200], Temp: 0.0005, Energy: -28.741448+0.000649j
[2025-08-24 06:10:56] [Iter 2235/4650] R3[1184/1200], Temp: 0.0004, Energy: -28.734383+0.000637j
[2025-08-24 06:11:06] [Iter 2236/4650] R3[1185/1200], Temp: 0.0004, Energy: -28.741730+0.001019j
[2025-08-24 06:11:16] [Iter 2237/4650] R3[1186/1200], Temp: 0.0003, Energy: -28.741892-0.000075j
[2025-08-24 06:11:26] [Iter 2238/4650] R3[1187/1200], Temp: 0.0003, Energy: -28.738483+0.001531j
[2025-08-24 06:11:37] [Iter 2239/4650] R3[1188/1200], Temp: 0.0002, Energy: -28.744329+0.001126j
[2025-08-24 06:11:47] [Iter 2240/4650] R3[1189/1200], Temp: 0.0002, Energy: -28.743930-0.002976j
[2025-08-24 06:11:57] [Iter 2241/4650] R3[1190/1200], Temp: 0.0002, Energy: -28.743290+0.004258j
[2025-08-24 06:12:07] [Iter 2242/4650] R3[1191/1200], Temp: 0.0001, Energy: -28.737912-0.001534j
[2025-08-24 06:12:17] [Iter 2243/4650] R3[1192/1200], Temp: 0.0001, Energy: -28.738821-0.000085j
[2025-08-24 06:12:27] [Iter 2244/4650] R3[1193/1200], Temp: 0.0001, Energy: -28.735872-0.001389j
[2025-08-24 06:12:37] [Iter 2245/4650] R3[1194/1200], Temp: 0.0001, Energy: -28.740862-0.001566j
[2025-08-24 06:12:48] [Iter 2246/4650] R3[1195/1200], Temp: 0.0000, Energy: -28.739574+0.000417j
[2025-08-24 06:12:58] [Iter 2247/4650] R3[1196/1200], Temp: 0.0000, Energy: -28.737132-0.000008j
[2025-08-24 06:13:08] [Iter 2248/4650] R3[1197/1200], Temp: 0.0000, Energy: -28.735233+0.001674j
[2025-08-24 06:13:18] [Iter 2249/4650] R3[1198/1200], Temp: 0.0000, Energy: -28.746665-0.000612j
[2025-08-24 06:13:28] [Iter 2250/4650] R3[1199/1200], Temp: 0.0000, Energy: -28.748861+0.001214j
[2025-08-24 06:13:28] RESTART #4 | Period: 2400
[2025-08-24 06:13:38] [Iter 2251/4650] R4[0/2400], Temp: 1.0000, Energy: -28.738592+0.000266j
[2025-08-24 06:13:48] [Iter 2252/4650] R4[1/2400], Temp: 1.0000, Energy: -28.743476+0.002279j
[2025-08-24 06:13:59] [Iter 2253/4650] R4[2/2400], Temp: 1.0000, Energy: -28.740944+0.003802j
[2025-08-24 06:14:09] [Iter 2254/4650] R4[3/2400], Temp: 1.0000, Energy: -28.740875+0.000104j
[2025-08-24 06:14:19] [Iter 2255/4650] R4[4/2400], Temp: 1.0000, Energy: -28.743055+0.001011j
[2025-08-24 06:14:29] [Iter 2256/4650] R4[5/2400], Temp: 1.0000, Energy: -28.732637-0.000290j
[2025-08-24 06:14:39] [Iter 2257/4650] R4[6/2400], Temp: 1.0000, Energy: -28.741846-0.000146j
[2025-08-24 06:14:49] [Iter 2258/4650] R4[7/2400], Temp: 1.0000, Energy: -28.733348-0.000581j
[2025-08-24 06:14:59] [Iter 2259/4650] R4[8/2400], Temp: 1.0000, Energy: -28.738747+0.002904j
[2025-08-24 06:15:10] [Iter 2260/4650] R4[9/2400], Temp: 1.0000, Energy: -28.739449+0.001973j
[2025-08-24 06:15:20] [Iter 2261/4650] R4[10/2400], Temp: 1.0000, Energy: -28.739412-0.001383j
[2025-08-24 06:15:30] [Iter 2262/4650] R4[11/2400], Temp: 0.9999, Energy: -28.742425-0.002090j
[2025-08-24 06:15:40] [Iter 2263/4650] R4[12/2400], Temp: 0.9999, Energy: -28.734590-0.000223j
[2025-08-24 06:15:50] [Iter 2264/4650] R4[13/2400], Temp: 0.9999, Energy: -28.745373+0.000784j
[2025-08-24 06:16:00] [Iter 2265/4650] R4[14/2400], Temp: 0.9999, Energy: -28.736474-0.001111j
[2025-08-24 06:16:10] [Iter 2266/4650] R4[15/2400], Temp: 0.9999, Energy: -28.738206-0.000617j
[2025-08-24 06:16:21] [Iter 2267/4650] R4[16/2400], Temp: 0.9999, Energy: -28.747651+0.001396j
[2025-08-24 06:16:31] [Iter 2268/4650] R4[17/2400], Temp: 0.9999, Energy: -28.738273+0.000033j
[2025-08-24 06:16:41] [Iter 2269/4650] R4[18/2400], Temp: 0.9999, Energy: -28.741812+0.000648j
[2025-08-24 06:16:51] [Iter 2270/4650] R4[19/2400], Temp: 0.9998, Energy: -28.746001+0.003824j
[2025-08-24 06:17:01] [Iter 2271/4650] R4[20/2400], Temp: 0.9998, Energy: -28.741853-0.000246j
[2025-08-24 06:17:11] [Iter 2272/4650] R4[21/2400], Temp: 0.9998, Energy: -28.740953+0.001168j
[2025-08-24 06:17:21] [Iter 2273/4650] R4[22/2400], Temp: 0.9998, Energy: -28.744181-0.001561j
[2025-08-24 06:17:32] [Iter 2274/4650] R4[23/2400], Temp: 0.9998, Energy: -28.737039-0.002266j
[2025-08-24 06:17:42] [Iter 2275/4650] R4[24/2400], Temp: 0.9998, Energy: -28.746776+0.000394j
[2025-08-24 06:17:52] [Iter 2276/4650] R4[25/2400], Temp: 0.9997, Energy: -28.745852+0.000095j
[2025-08-24 06:18:02] [Iter 2277/4650] R4[26/2400], Temp: 0.9997, Energy: -28.739792-0.000698j
[2025-08-24 06:18:12] [Iter 2278/4650] R4[27/2400], Temp: 0.9997, Energy: -28.740636-0.000288j
[2025-08-24 06:18:22] [Iter 2279/4650] R4[28/2400], Temp: 0.9997, Energy: -28.738081+0.000805j
[2025-08-24 06:18:32] [Iter 2280/4650] R4[29/2400], Temp: 0.9996, Energy: -28.744261+0.001226j
[2025-08-24 06:18:43] [Iter 2281/4650] R4[30/2400], Temp: 0.9996, Energy: -28.743944+0.001201j
[2025-08-24 06:18:53] [Iter 2282/4650] R4[31/2400], Temp: 0.9996, Energy: -28.748859-0.001025j
[2025-08-24 06:19:03] [Iter 2283/4650] R4[32/2400], Temp: 0.9996, Energy: -28.735160+0.000953j
[2025-08-24 06:19:13] [Iter 2284/4650] R4[33/2400], Temp: 0.9995, Energy: -28.741782-0.001428j
[2025-08-24 06:19:23] [Iter 2285/4650] R4[34/2400], Temp: 0.9995, Energy: -28.743599-0.002838j
[2025-08-24 06:19:33] [Iter 2286/4650] R4[35/2400], Temp: 0.9995, Energy: -28.740847+0.002281j
[2025-08-24 06:19:43] [Iter 2287/4650] R4[36/2400], Temp: 0.9994, Energy: -28.742161-0.000168j
[2025-08-24 06:19:54] [Iter 2288/4650] R4[37/2400], Temp: 0.9994, Energy: -28.743794-0.001125j
[2025-08-24 06:20:04] [Iter 2289/4650] R4[38/2400], Temp: 0.9994, Energy: -28.748464-0.003421j
[2025-08-24 06:20:14] [Iter 2290/4650] R4[39/2400], Temp: 0.9993, Energy: -28.745768+0.001245j
[2025-08-24 06:20:24] [Iter 2291/4650] R4[40/2400], Temp: 0.9993, Energy: -28.743162-0.000937j
[2025-08-24 06:20:34] [Iter 2292/4650] R4[41/2400], Temp: 0.9993, Energy: -28.744155-0.001680j
[2025-08-24 06:20:44] [Iter 2293/4650] R4[42/2400], Temp: 0.9992, Energy: -28.741610-0.000984j
[2025-08-24 06:20:54] [Iter 2294/4650] R4[43/2400], Temp: 0.9992, Energy: -28.742675+0.003642j
[2025-08-24 06:21:05] [Iter 2295/4650] R4[44/2400], Temp: 0.9992, Energy: -28.738009+0.001101j
[2025-08-24 06:21:15] [Iter 2296/4650] R4[45/2400], Temp: 0.9991, Energy: -28.740266-0.000007j
[2025-08-24 06:21:25] [Iter 2297/4650] R4[46/2400], Temp: 0.9991, Energy: -28.741956-0.000661j
[2025-08-24 06:21:35] [Iter 2298/4650] R4[47/2400], Temp: 0.9991, Energy: -28.741964+0.000728j
[2025-08-24 06:21:45] [Iter 2299/4650] R4[48/2400], Temp: 0.9990, Energy: -28.737589+0.000664j
[2025-08-24 06:21:55] [Iter 2300/4650] R4[49/2400], Temp: 0.9990, Energy: -28.740403-0.001824j
[2025-08-24 06:22:05] [Iter 2301/4650] R4[50/2400], Temp: 0.9989, Energy: -28.740484-0.000021j
[2025-08-24 06:22:16] [Iter 2302/4650] R4[51/2400], Temp: 0.9989, Energy: -28.736572+0.000719j
[2025-08-24 06:22:26] [Iter 2303/4650] R4[52/2400], Temp: 0.9988, Energy: -28.740869-0.000739j
[2025-08-24 06:22:36] [Iter 2304/4650] R4[53/2400], Temp: 0.9988, Energy: -28.741883-0.001035j
[2025-08-24 06:22:46] [Iter 2305/4650] R4[54/2400], Temp: 0.9988, Energy: -28.739618-0.002294j
[2025-08-24 06:22:56] [Iter 2306/4650] R4[55/2400], Temp: 0.9987, Energy: -28.739593-0.001432j
[2025-08-24 06:23:06] [Iter 2307/4650] R4[56/2400], Temp: 0.9987, Energy: -28.740632+0.000126j
[2025-08-24 06:23:16] [Iter 2308/4650] R4[57/2400], Temp: 0.9986, Energy: -28.743587-0.000010j
[2025-08-24 06:23:27] [Iter 2309/4650] R4[58/2400], Temp: 0.9986, Energy: -28.740489+0.000422j
[2025-08-24 06:23:37] [Iter 2310/4650] R4[59/2400], Temp: 0.9985, Energy: -28.736995+0.000625j
[2025-08-24 06:23:47] [Iter 2311/4650] R4[60/2400], Temp: 0.9985, Energy: -28.739321+0.002021j
[2025-08-24 06:23:57] [Iter 2312/4650] R4[61/2400], Temp: 0.9984, Energy: -28.738802-0.001811j
[2025-08-24 06:24:07] [Iter 2313/4650] R4[62/2400], Temp: 0.9984, Energy: -28.742049+0.000349j
[2025-08-24 06:24:17] [Iter 2314/4650] R4[63/2400], Temp: 0.9983, Energy: -28.745764-0.000229j
[2025-08-24 06:24:27] [Iter 2315/4650] R4[64/2400], Temp: 0.9982, Energy: -28.750498-0.002564j
[2025-08-24 06:24:38] [Iter 2316/4650] R4[65/2400], Temp: 0.9982, Energy: -28.743993-0.002367j
[2025-08-24 06:24:48] [Iter 2317/4650] R4[66/2400], Temp: 0.9981, Energy: -28.747925+0.002182j
[2025-08-24 06:24:58] [Iter 2318/4650] R4[67/2400], Temp: 0.9981, Energy: -28.737714+0.000388j
[2025-08-24 06:25:08] [Iter 2319/4650] R4[68/2400], Temp: 0.9980, Energy: -28.737417+0.000539j
[2025-08-24 06:25:18] [Iter 2320/4650] R4[69/2400], Temp: 0.9980, Energy: -28.741632+0.003122j
[2025-08-24 06:25:28] [Iter 2321/4650] R4[70/2400], Temp: 0.9979, Energy: -28.743649+0.000614j
[2025-08-24 06:25:38] [Iter 2322/4650] R4[71/2400], Temp: 0.9978, Energy: -28.743830-0.000867j
[2025-08-24 06:25:49] [Iter 2323/4650] R4[72/2400], Temp: 0.9978, Energy: -28.743686-0.000237j
[2025-08-24 06:25:59] [Iter 2324/4650] R4[73/2400], Temp: 0.9977, Energy: -28.740071-0.000081j
[2025-08-24 06:26:09] [Iter 2325/4650] R4[74/2400], Temp: 0.9977, Energy: -28.739100+0.002798j
[2025-08-24 06:26:19] [Iter 2326/4650] R4[75/2400], Temp: 0.9976, Energy: -28.748894+0.001141j
[2025-08-24 06:26:29] [Iter 2327/4650] R4[76/2400], Temp: 0.9975, Energy: -28.742101-0.001672j
[2025-08-24 06:26:39] [Iter 2328/4650] R4[77/2400], Temp: 0.9975, Energy: -28.740958-0.000047j
[2025-08-24 06:26:49] [Iter 2329/4650] R4[78/2400], Temp: 0.9974, Energy: -28.741043+0.000698j
[2025-08-24 06:27:00] [Iter 2330/4650] R4[79/2400], Temp: 0.9973, Energy: -28.737154+0.002839j
[2025-08-24 06:27:10] [Iter 2331/4650] R4[80/2400], Temp: 0.9973, Energy: -28.739184+0.001082j
[2025-08-24 06:27:20] [Iter 2332/4650] R4[81/2400], Temp: 0.9972, Energy: -28.738175+0.001204j
[2025-08-24 06:27:30] [Iter 2333/4650] R4[82/2400], Temp: 0.9971, Energy: -28.744243+0.001898j
[2025-08-24 06:27:40] [Iter 2334/4650] R4[83/2400], Temp: 0.9971, Energy: -28.741739+0.002003j
[2025-08-24 06:27:50] [Iter 2335/4650] R4[84/2400], Temp: 0.9970, Energy: -28.742358+0.001181j
[2025-08-24 06:28:00] [Iter 2336/4650] R4[85/2400], Temp: 0.9969, Energy: -28.736807-0.000447j
[2025-08-24 06:28:11] [Iter 2337/4650] R4[86/2400], Temp: 0.9968, Energy: -28.741171-0.000334j
[2025-08-24 06:28:21] [Iter 2338/4650] R4[87/2400], Temp: 0.9968, Energy: -28.743888-0.001229j
[2025-08-24 06:28:31] [Iter 2339/4650] R4[88/2400], Temp: 0.9967, Energy: -28.745403+0.000765j
[2025-08-24 06:28:41] [Iter 2340/4650] R4[89/2400], Temp: 0.9966, Energy: -28.738244+0.002152j
[2025-08-24 06:28:51] [Iter 2341/4650] R4[90/2400], Temp: 0.9965, Energy: -28.744540+0.001264j
[2025-08-24 06:29:01] [Iter 2342/4650] R4[91/2400], Temp: 0.9965, Energy: -28.740375+0.001121j
[2025-08-24 06:29:11] [Iter 2343/4650] R4[92/2400], Temp: 0.9964, Energy: -28.742066+0.001389j
[2025-08-24 06:29:22] [Iter 2344/4650] R4[93/2400], Temp: 0.9963, Energy: -28.742789+0.002693j
[2025-08-24 06:29:32] [Iter 2345/4650] R4[94/2400], Temp: 0.9962, Energy: -28.739456+0.000159j
[2025-08-24 06:29:42] [Iter 2346/4650] R4[95/2400], Temp: 0.9961, Energy: -28.745559-0.003588j
[2025-08-24 06:29:52] [Iter 2347/4650] R4[96/2400], Temp: 0.9961, Energy: -28.742657-0.000659j
[2025-08-24 06:30:02] [Iter 2348/4650] R4[97/2400], Temp: 0.9960, Energy: -28.749334+0.000482j
[2025-08-24 06:30:12] [Iter 2349/4650] R4[98/2400], Temp: 0.9959, Energy: -28.743757-0.000849j
[2025-08-24 06:30:22] [Iter 2350/4650] R4[99/2400], Temp: 0.9958, Energy: -28.747927-0.001743j
[2025-08-24 06:30:33] [Iter 2351/4650] R4[100/2400], Temp: 0.9957, Energy: -28.739753-0.001186j
[2025-08-24 06:30:43] [Iter 2352/4650] R4[101/2400], Temp: 0.9956, Energy: -28.744642-0.001549j
[2025-08-24 06:30:53] [Iter 2353/4650] R4[102/2400], Temp: 0.9955, Energy: -28.748001+0.000532j
[2025-08-24 06:31:03] [Iter 2354/4650] R4[103/2400], Temp: 0.9955, Energy: -28.744557-0.001671j
[2025-08-24 06:31:13] [Iter 2355/4650] R4[104/2400], Temp: 0.9954, Energy: -28.742776-0.000085j
[2025-08-24 06:31:23] [Iter 2356/4650] R4[105/2400], Temp: 0.9953, Energy: -28.746528+0.002335j
[2025-08-24 06:31:33] [Iter 2357/4650] R4[106/2400], Temp: 0.9952, Energy: -28.737224+0.000827j
[2025-08-24 06:31:44] [Iter 2358/4650] R4[107/2400], Temp: 0.9951, Energy: -28.739627-0.003111j
[2025-08-24 06:31:54] [Iter 2359/4650] R4[108/2400], Temp: 0.9950, Energy: -28.745682+0.000791j
[2025-08-24 06:32:04] [Iter 2360/4650] R4[109/2400], Temp: 0.9949, Energy: -28.742444+0.002790j
[2025-08-24 06:32:14] [Iter 2361/4650] R4[110/2400], Temp: 0.9948, Energy: -28.745461-0.002018j
[2025-08-24 06:32:24] [Iter 2362/4650] R4[111/2400], Temp: 0.9947, Energy: -28.747122+0.000312j
[2025-08-24 06:32:34] [Iter 2363/4650] R4[112/2400], Temp: 0.9946, Energy: -28.742924+0.000589j
[2025-08-24 06:32:44] [Iter 2364/4650] R4[113/2400], Temp: 0.9945, Energy: -28.746170+0.001040j
[2025-08-24 06:32:55] [Iter 2365/4650] R4[114/2400], Temp: 0.9944, Energy: -28.747100-0.000339j
[2025-08-24 06:33:05] [Iter 2366/4650] R4[115/2400], Temp: 0.9943, Energy: -28.742715+0.003494j
[2025-08-24 06:33:15] [Iter 2367/4650] R4[116/2400], Temp: 0.9942, Energy: -28.742253-0.001818j
[2025-08-24 06:33:25] [Iter 2368/4650] R4[117/2400], Temp: 0.9941, Energy: -28.745772-0.001888j
[2025-08-24 06:33:35] [Iter 2369/4650] R4[118/2400], Temp: 0.9940, Energy: -28.739086+0.000305j
[2025-08-24 06:33:45] [Iter 2370/4650] R4[119/2400], Temp: 0.9939, Energy: -28.743458-0.001715j
[2025-08-24 06:33:55] [Iter 2371/4650] R4[120/2400], Temp: 0.9938, Energy: -28.747591-0.000382j
[2025-08-24 06:34:06] [Iter 2372/4650] R4[121/2400], Temp: 0.9937, Energy: -28.742220+0.000008j
[2025-08-24 06:34:16] [Iter 2373/4650] R4[122/2400], Temp: 0.9936, Energy: -28.743544-0.002752j
[2025-08-24 06:34:26] [Iter 2374/4650] R4[123/2400], Temp: 0.9935, Energy: -28.744009+0.001114j
[2025-08-24 06:34:36] [Iter 2375/4650] R4[124/2400], Temp: 0.9934, Energy: -28.740916+0.001429j
[2025-08-24 06:34:46] [Iter 2376/4650] R4[125/2400], Temp: 0.9933, Energy: -28.747121-0.002548j
[2025-08-24 06:34:56] [Iter 2377/4650] R4[126/2400], Temp: 0.9932, Energy: -28.739506+0.000746j
[2025-08-24 06:35:06] [Iter 2378/4650] R4[127/2400], Temp: 0.9931, Energy: -28.747584+0.001415j
[2025-08-24 06:35:17] [Iter 2379/4650] R4[128/2400], Temp: 0.9930, Energy: -28.747103+0.000012j
[2025-08-24 06:35:27] [Iter 2380/4650] R4[129/2400], Temp: 0.9929, Energy: -28.738868+0.003115j
[2025-08-24 06:35:37] [Iter 2381/4650] R4[130/2400], Temp: 0.9928, Energy: -28.749063-0.001245j
[2025-08-24 06:35:47] [Iter 2382/4650] R4[131/2400], Temp: 0.9927, Energy: -28.742997-0.000704j
[2025-08-24 06:35:57] [Iter 2383/4650] R4[132/2400], Temp: 0.9926, Energy: -28.743077-0.001002j
[2025-08-24 06:36:07] [Iter 2384/4650] R4[133/2400], Temp: 0.9924, Energy: -28.743724-0.000854j
[2025-08-24 06:36:17] [Iter 2385/4650] R4[134/2400], Temp: 0.9923, Energy: -28.745128+0.000581j
[2025-08-24 06:36:28] [Iter 2386/4650] R4[135/2400], Temp: 0.9922, Energy: -28.739850-0.001468j
[2025-08-24 06:36:38] [Iter 2387/4650] R4[136/2400], Temp: 0.9921, Energy: -28.745968-0.001439j
[2025-08-24 06:36:48] [Iter 2388/4650] R4[137/2400], Temp: 0.9920, Energy: -28.747990+0.000283j
[2025-08-24 06:36:58] [Iter 2389/4650] R4[138/2400], Temp: 0.9919, Energy: -28.745212-0.000792j
[2025-08-24 06:37:08] [Iter 2390/4650] R4[139/2400], Temp: 0.9917, Energy: -28.744505+0.001947j
[2025-08-24 06:37:18] [Iter 2391/4650] R4[140/2400], Temp: 0.9916, Energy: -28.739462+0.001459j
[2025-08-24 06:37:28] [Iter 2392/4650] R4[141/2400], Temp: 0.9915, Energy: -28.743748+0.002658j
[2025-08-24 06:37:39] [Iter 2393/4650] R4[142/2400], Temp: 0.9914, Energy: -28.749674+0.004014j
[2025-08-24 06:37:49] [Iter 2394/4650] R4[143/2400], Temp: 0.9913, Energy: -28.742983+0.000803j
[2025-08-24 06:37:59] [Iter 2395/4650] R4[144/2400], Temp: 0.9911, Energy: -28.742610-0.000431j
[2025-08-24 06:38:09] [Iter 2396/4650] R4[145/2400], Temp: 0.9910, Energy: -28.745787+0.000325j
[2025-08-24 06:38:19] [Iter 2397/4650] R4[146/2400], Temp: 0.9909, Energy: -28.737974+0.000958j
[2025-08-24 06:38:29] [Iter 2398/4650] R4[147/2400], Temp: 0.9908, Energy: -28.742102+0.001303j
[2025-08-24 06:38:39] [Iter 2399/4650] R4[148/2400], Temp: 0.9906, Energy: -28.745397-0.002204j
[2025-08-24 06:38:50] [Iter 2400/4650] R4[149/2400], Temp: 0.9905, Energy: -28.740918-0.001689j
[2025-08-24 06:39:00] [Iter 2401/4650] R4[150/2400], Temp: 0.9904, Energy: -28.743891+0.000201j
[2025-08-24 06:39:10] [Iter 2402/4650] R4[151/2400], Temp: 0.9903, Energy: -28.740149-0.000943j
[2025-08-24 06:39:20] [Iter 2403/4650] R4[152/2400], Temp: 0.9901, Energy: -28.746590+0.001230j
[2025-08-24 06:39:30] [Iter 2404/4650] R4[153/2400], Temp: 0.9900, Energy: -28.740789+0.000802j
[2025-08-24 06:39:40] [Iter 2405/4650] R4[154/2400], Temp: 0.9899, Energy: -28.745825+0.000626j
[2025-08-24 06:39:50] [Iter 2406/4650] R4[155/2400], Temp: 0.9897, Energy: -28.745493+0.002745j
[2025-08-24 06:40:01] [Iter 2407/4650] R4[156/2400], Temp: 0.9896, Energy: -28.741993-0.000834j
[2025-08-24 06:40:11] [Iter 2408/4650] R4[157/2400], Temp: 0.9895, Energy: -28.739707-0.000801j
[2025-08-24 06:40:21] [Iter 2409/4650] R4[158/2400], Temp: 0.9893, Energy: -28.749672+0.000657j
[2025-08-24 06:40:31] [Iter 2410/4650] R4[159/2400], Temp: 0.9892, Energy: -28.742276-0.000061j
[2025-08-24 06:40:41] [Iter 2411/4650] R4[160/2400], Temp: 0.9891, Energy: -28.747431-0.002475j
[2025-08-24 06:40:51] [Iter 2412/4650] R4[161/2400], Temp: 0.9889, Energy: -28.746144-0.000519j
[2025-08-24 06:41:01] [Iter 2413/4650] R4[162/2400], Temp: 0.9888, Energy: -28.740151-0.000503j
[2025-08-24 06:41:12] [Iter 2414/4650] R4[163/2400], Temp: 0.9887, Energy: -28.744010+0.001047j
[2025-08-24 06:41:22] [Iter 2415/4650] R4[164/2400], Temp: 0.9885, Energy: -28.744140+0.001157j
[2025-08-24 06:41:32] [Iter 2416/4650] R4[165/2400], Temp: 0.9884, Energy: -28.739070+0.000353j
[2025-08-24 06:41:42] [Iter 2417/4650] R4[166/2400], Temp: 0.9882, Energy: -28.740112-0.002841j
[2025-08-24 06:41:52] [Iter 2418/4650] R4[167/2400], Temp: 0.9881, Energy: -28.743224+0.000735j
[2025-08-24 06:42:02] [Iter 2419/4650] R4[168/2400], Temp: 0.9880, Energy: -28.745595-0.000823j
[2025-08-24 06:42:12] [Iter 2420/4650] R4[169/2400], Temp: 0.9878, Energy: -28.739245-0.001303j
[2025-08-24 06:42:23] [Iter 2421/4650] R4[170/2400], Temp: 0.9877, Energy: -28.743740+0.001688j
[2025-08-24 06:42:33] [Iter 2422/4650] R4[171/2400], Temp: 0.9875, Energy: -28.742903+0.000099j
[2025-08-24 06:42:43] [Iter 2423/4650] R4[172/2400], Temp: 0.9874, Energy: -28.742030+0.000550j
[2025-08-24 06:42:53] [Iter 2424/4650] R4[173/2400], Temp: 0.9872, Energy: -28.747358+0.000041j
[2025-08-24 06:43:03] [Iter 2425/4650] R4[174/2400], Temp: 0.9871, Energy: -28.745894-0.000746j
[2025-08-24 06:43:13] [Iter 2426/4650] R4[175/2400], Temp: 0.9869, Energy: -28.744018-0.000354j
[2025-08-24 06:43:23] [Iter 2427/4650] R4[176/2400], Temp: 0.9868, Energy: -28.740273+0.001282j
[2025-08-24 06:43:34] [Iter 2428/4650] R4[177/2400], Temp: 0.9866, Energy: -28.749727+0.000590j
[2025-08-24 06:43:44] [Iter 2429/4650] R4[178/2400], Temp: 0.9865, Energy: -28.742056-0.001593j
[2025-08-24 06:43:54] [Iter 2430/4650] R4[179/2400], Temp: 0.9863, Energy: -28.743762+0.000494j
[2025-08-24 06:44:04] [Iter 2431/4650] R4[180/2400], Temp: 0.9862, Energy: -28.745645+0.000628j
[2025-08-24 06:44:14] [Iter 2432/4650] R4[181/2400], Temp: 0.9860, Energy: -28.744631-0.002642j
[2025-08-24 06:44:24] [Iter 2433/4650] R4[182/2400], Temp: 0.9859, Energy: -28.746669+0.000418j
[2025-08-24 06:44:34] [Iter 2434/4650] R4[183/2400], Temp: 0.9857, Energy: -28.740958+0.000220j
[2025-08-24 06:44:45] [Iter 2435/4650] R4[184/2400], Temp: 0.9856, Energy: -28.743123+0.000193j
[2025-08-24 06:44:55] [Iter 2436/4650] R4[185/2400], Temp: 0.9854, Energy: -28.745449+0.003318j
[2025-08-24 06:45:05] [Iter 2437/4650] R4[186/2400], Temp: 0.9853, Energy: -28.747866-0.001290j
[2025-08-24 06:45:15] [Iter 2438/4650] R4[187/2400], Temp: 0.9851, Energy: -28.744227+0.001355j
[2025-08-24 06:45:25] [Iter 2439/4650] R4[188/2400], Temp: 0.9849, Energy: -28.738519+0.002355j
[2025-08-24 06:45:35] [Iter 2440/4650] R4[189/2400], Temp: 0.9848, Energy: -28.742687-0.001873j
[2025-08-24 06:45:45] [Iter 2441/4650] R4[190/2400], Temp: 0.9846, Energy: -28.744119+0.001650j
[2025-08-24 06:45:56] [Iter 2442/4650] R4[191/2400], Temp: 0.9845, Energy: -28.742801+0.000718j
[2025-08-24 06:46:06] [Iter 2443/4650] R4[192/2400], Temp: 0.9843, Energy: -28.741124+0.001684j
[2025-08-24 06:46:16] [Iter 2444/4650] R4[193/2400], Temp: 0.9841, Energy: -28.743879-0.003474j
[2025-08-24 06:46:26] [Iter 2445/4650] R4[194/2400], Temp: 0.9840, Energy: -28.740338-0.001474j
[2025-08-24 06:46:36] [Iter 2446/4650] R4[195/2400], Temp: 0.9838, Energy: -28.735768-0.000357j
[2025-08-24 06:46:46] [Iter 2447/4650] R4[196/2400], Temp: 0.9836, Energy: -28.744669+0.000762j
[2025-08-24 06:46:56] [Iter 2448/4650] R4[197/2400], Temp: 0.9835, Energy: -28.748564-0.000993j
[2025-08-24 06:47:07] [Iter 2449/4650] R4[198/2400], Temp: 0.9833, Energy: -28.743245+0.000939j
[2025-08-24 06:47:17] [Iter 2450/4650] R4[199/2400], Temp: 0.9831, Energy: -28.741668+0.000625j
[2025-08-24 06:47:27] [Iter 2451/4650] R4[200/2400], Temp: 0.9830, Energy: -28.746011-0.000452j
[2025-08-24 06:47:37] [Iter 2452/4650] R4[201/2400], Temp: 0.9828, Energy: -28.740328+0.000997j
[2025-08-24 06:47:47] [Iter 2453/4650] R4[202/2400], Temp: 0.9826, Energy: -28.745379-0.001516j
[2025-08-24 06:47:57] [Iter 2454/4650] R4[203/2400], Temp: 0.9825, Energy: -28.742259-0.001418j
[2025-08-24 06:48:07] [Iter 2455/4650] R4[204/2400], Temp: 0.9823, Energy: -28.752126-0.001540j
[2025-08-24 06:48:18] [Iter 2456/4650] R4[205/2400], Temp: 0.9821, Energy: -28.741945-0.000112j
[2025-08-24 06:48:28] [Iter 2457/4650] R4[206/2400], Temp: 0.9819, Energy: -28.747384-0.001058j
[2025-08-24 06:48:38] [Iter 2458/4650] R4[207/2400], Temp: 0.9818, Energy: -28.742711-0.001081j
[2025-08-24 06:48:48] [Iter 2459/4650] R4[208/2400], Temp: 0.9816, Energy: -28.744449+0.000706j
[2025-08-24 06:48:58] [Iter 2460/4650] R4[209/2400], Temp: 0.9814, Energy: -28.746562+0.003564j
[2025-08-24 06:49:08] [Iter 2461/4650] R4[210/2400], Temp: 0.9812, Energy: -28.745157-0.001047j
[2025-08-24 06:49:18] [Iter 2462/4650] R4[211/2400], Temp: 0.9810, Energy: -28.738053-0.001757j
[2025-08-24 06:49:29] [Iter 2463/4650] R4[212/2400], Temp: 0.9809, Energy: -28.747579+0.000525j
[2025-08-24 06:49:39] [Iter 2464/4650] R4[213/2400], Temp: 0.9807, Energy: -28.745256+0.000760j
[2025-08-24 06:49:49] [Iter 2465/4650] R4[214/2400], Temp: 0.9805, Energy: -28.747603-0.002611j
[2025-08-24 06:49:59] [Iter 2466/4650] R4[215/2400], Temp: 0.9803, Energy: -28.743645-0.001507j
[2025-08-24 06:50:09] [Iter 2467/4650] R4[216/2400], Temp: 0.9801, Energy: -28.746458-0.001844j
[2025-08-24 06:50:19] [Iter 2468/4650] R4[217/2400], Temp: 0.9800, Energy: -28.744952+0.000335j
[2025-08-24 06:50:29] [Iter 2469/4650] R4[218/2400], Temp: 0.9798, Energy: -28.746800-0.002393j
[2025-08-24 06:50:40] [Iter 2470/4650] R4[219/2400], Temp: 0.9796, Energy: -28.744117-0.000427j
[2025-08-24 06:50:50] [Iter 2471/4650] R4[220/2400], Temp: 0.9794, Energy: -28.744745-0.001220j
[2025-08-24 06:51:00] [Iter 2472/4650] R4[221/2400], Temp: 0.9792, Energy: -28.745582+0.002555j
[2025-08-24 06:51:10] [Iter 2473/4650] R4[222/2400], Temp: 0.9790, Energy: -28.741299+0.002551j
[2025-08-24 06:51:20] [Iter 2474/4650] R4[223/2400], Temp: 0.9788, Energy: -28.743073-0.000555j
[2025-08-24 06:51:30] [Iter 2475/4650] R4[224/2400], Temp: 0.9787, Energy: -28.747598-0.000737j
[2025-08-24 06:51:40] [Iter 2476/4650] R4[225/2400], Temp: 0.9785, Energy: -28.743131-0.001404j
[2025-08-24 06:51:51] [Iter 2477/4650] R4[226/2400], Temp: 0.9783, Energy: -28.748680+0.000025j
[2025-08-24 06:52:01] [Iter 2478/4650] R4[227/2400], Temp: 0.9781, Energy: -28.738502+0.000156j
[2025-08-24 06:52:11] [Iter 2479/4650] R4[228/2400], Temp: 0.9779, Energy: -28.747442+0.001295j
[2025-08-24 06:52:21] [Iter 2480/4650] R4[229/2400], Temp: 0.9777, Energy: -28.741159-0.000880j
[2025-08-24 06:52:31] [Iter 2481/4650] R4[230/2400], Temp: 0.9775, Energy: -28.739816+0.000382j
[2025-08-24 06:52:41] [Iter 2482/4650] R4[231/2400], Temp: 0.9773, Energy: -28.743298-0.000044j
[2025-08-24 06:52:51] [Iter 2483/4650] R4[232/2400], Temp: 0.9771, Energy: -28.742342+0.000321j
[2025-08-24 06:53:02] [Iter 2484/4650] R4[233/2400], Temp: 0.9769, Energy: -28.741632-0.000944j
[2025-08-24 06:53:12] [Iter 2485/4650] R4[234/2400], Temp: 0.9767, Energy: -28.735421+0.000508j
[2025-08-24 06:53:22] [Iter 2486/4650] R4[235/2400], Temp: 0.9765, Energy: -28.743639+0.000527j
[2025-08-24 06:53:32] [Iter 2487/4650] R4[236/2400], Temp: 0.9763, Energy: -28.739832-0.000899j
[2025-08-24 06:53:42] [Iter 2488/4650] R4[237/2400], Temp: 0.9761, Energy: -28.749703-0.000291j
[2025-08-24 06:53:52] [Iter 2489/4650] R4[238/2400], Temp: 0.9759, Energy: -28.745718+0.000293j
[2025-08-24 06:54:02] [Iter 2490/4650] R4[239/2400], Temp: 0.9757, Energy: -28.737134-0.002073j
[2025-08-24 06:54:13] [Iter 2491/4650] R4[240/2400], Temp: 0.9755, Energy: -28.744223-0.000802j
[2025-08-24 06:54:23] [Iter 2492/4650] R4[241/2400], Temp: 0.9753, Energy: -28.748500+0.001146j
[2025-08-24 06:54:33] [Iter 2493/4650] R4[242/2400], Temp: 0.9751, Energy: -28.746540-0.001315j
[2025-08-24 06:54:43] [Iter 2494/4650] R4[243/2400], Temp: 0.9749, Energy: -28.749406-0.000326j
[2025-08-24 06:54:53] [Iter 2495/4650] R4[244/2400], Temp: 0.9747, Energy: -28.740974+0.000783j
[2025-08-24 06:55:03] [Iter 2496/4650] R4[245/2400], Temp: 0.9745, Energy: -28.744959+0.002314j
[2025-08-24 06:55:13] [Iter 2497/4650] R4[246/2400], Temp: 0.9743, Energy: -28.747201+0.000731j
[2025-08-24 06:55:23] [Iter 2498/4650] R4[247/2400], Temp: 0.9741, Energy: -28.745786+0.000714j
[2025-08-24 06:55:34] [Iter 2499/4650] R4[248/2400], Temp: 0.9739, Energy: -28.742087-0.001723j
[2025-08-24 06:55:44] [Iter 2500/4650] R4[249/2400], Temp: 0.9737, Energy: -28.737972+0.000312j
[2025-08-24 06:55:44] ✓ Checkpoint saved: checkpoint_iter_002500.pkl
[2025-08-24 06:55:54] [Iter 2501/4650] R4[250/2400], Temp: 0.9735, Energy: -28.748382-0.000022j
[2025-08-24 06:56:04] [Iter 2502/4650] R4[251/2400], Temp: 0.9733, Energy: -28.735029+0.002344j
[2025-08-24 06:56:14] [Iter 2503/4650] R4[252/2400], Temp: 0.9730, Energy: -28.741275-0.002228j
[2025-08-24 06:56:24] [Iter 2504/4650] R4[253/2400], Temp: 0.9728, Energy: -28.741331+0.000750j
[2025-08-24 06:56:34] [Iter 2505/4650] R4[254/2400], Temp: 0.9726, Energy: -28.743888-0.001387j
[2025-08-24 06:56:45] [Iter 2506/4650] R4[255/2400], Temp: 0.9724, Energy: -28.735742-0.000109j
[2025-08-24 06:56:55] [Iter 2507/4650] R4[256/2400], Temp: 0.9722, Energy: -28.744353-0.000073j
[2025-08-24 06:57:05] [Iter 2508/4650] R4[257/2400], Temp: 0.9720, Energy: -28.747048+0.001713j
[2025-08-24 06:57:15] [Iter 2509/4650] R4[258/2400], Temp: 0.9718, Energy: -28.744797-0.002779j
[2025-08-24 06:57:25] [Iter 2510/4650] R4[259/2400], Temp: 0.9715, Energy: -28.746298-0.000433j
[2025-08-24 06:57:35] [Iter 2511/4650] R4[260/2400], Temp: 0.9713, Energy: -28.744987+0.001030j
[2025-08-24 06:57:45] [Iter 2512/4650] R4[261/2400], Temp: 0.9711, Energy: -28.746110-0.000257j
[2025-08-24 06:57:56] [Iter 2513/4650] R4[262/2400], Temp: 0.9709, Energy: -28.738507+0.000298j
[2025-08-24 06:58:06] [Iter 2514/4650] R4[263/2400], Temp: 0.9707, Energy: -28.741604-0.001144j
[2025-08-24 06:58:16] [Iter 2515/4650] R4[264/2400], Temp: 0.9704, Energy: -28.746409+0.001672j
[2025-08-24 06:58:26] [Iter 2516/4650] R4[265/2400], Temp: 0.9702, Energy: -28.745470-0.002710j
[2025-08-24 06:58:36] [Iter 2517/4650] R4[266/2400], Temp: 0.9700, Energy: -28.742510-0.000670j
[2025-08-24 06:58:46] [Iter 2518/4650] R4[267/2400], Temp: 0.9698, Energy: -28.740650+0.001876j
[2025-08-24 06:58:56] [Iter 2519/4650] R4[268/2400], Temp: 0.9695, Energy: -28.748744+0.000141j
[2025-08-24 06:59:07] [Iter 2520/4650] R4[269/2400], Temp: 0.9693, Energy: -28.740770-0.001408j
[2025-08-24 06:59:17] [Iter 2521/4650] R4[270/2400], Temp: 0.9691, Energy: -28.751035-0.000153j
[2025-08-24 06:59:27] [Iter 2522/4650] R4[271/2400], Temp: 0.9689, Energy: -28.741665-0.000968j
[2025-08-24 06:59:37] [Iter 2523/4650] R4[272/2400], Temp: 0.9686, Energy: -28.749217-0.001182j
[2025-08-24 06:59:47] [Iter 2524/4650] R4[273/2400], Temp: 0.9684, Energy: -28.741546-0.001659j
[2025-08-24 06:59:57] [Iter 2525/4650] R4[274/2400], Temp: 0.9682, Energy: -28.741198-0.000946j
[2025-08-24 07:00:08] [Iter 2526/4650] R4[275/2400], Temp: 0.9680, Energy: -28.745415-0.000220j
[2025-08-24 07:00:18] [Iter 2527/4650] R4[276/2400], Temp: 0.9677, Energy: -28.739942+0.000569j
[2025-08-24 07:00:28] [Iter 2528/4650] R4[277/2400], Temp: 0.9675, Energy: -28.744788-0.000969j
[2025-08-24 07:00:38] [Iter 2529/4650] R4[278/2400], Temp: 0.9673, Energy: -28.746980-0.000084j
[2025-08-24 07:00:48] [Iter 2530/4650] R4[279/2400], Temp: 0.9670, Energy: -28.742302+0.001173j
[2025-08-24 07:00:58] [Iter 2531/4650] R4[280/2400], Temp: 0.9668, Energy: -28.747969-0.002493j
[2025-08-24 07:01:08] [Iter 2532/4650] R4[281/2400], Temp: 0.9666, Energy: -28.740604+0.000820j
[2025-08-24 07:01:19] [Iter 2533/4650] R4[282/2400], Temp: 0.9663, Energy: -28.743725+0.000208j
[2025-08-24 07:01:29] [Iter 2534/4650] R4[283/2400], Temp: 0.9661, Energy: -28.741195-0.000577j
[2025-08-24 07:01:39] [Iter 2535/4650] R4[284/2400], Temp: 0.9658, Energy: -28.743031-0.001621j
[2025-08-24 07:01:49] [Iter 2536/4650] R4[285/2400], Temp: 0.9656, Energy: -28.748202-0.002113j
[2025-08-24 07:01:59] [Iter 2537/4650] R4[286/2400], Temp: 0.9654, Energy: -28.742785-0.001717j
[2025-08-24 07:02:09] [Iter 2538/4650] R4[287/2400], Temp: 0.9651, Energy: -28.750223-0.001837j
[2025-08-24 07:02:19] [Iter 2539/4650] R4[288/2400], Temp: 0.9649, Energy: -28.748233+0.000736j
[2025-08-24 07:02:30] [Iter 2540/4650] R4[289/2400], Temp: 0.9646, Energy: -28.742223+0.000622j
[2025-08-24 07:02:40] [Iter 2541/4650] R4[290/2400], Temp: 0.9644, Energy: -28.748819+0.002626j
[2025-08-24 07:02:50] [Iter 2542/4650] R4[291/2400], Temp: 0.9642, Energy: -28.749754-0.000391j
[2025-08-24 07:03:00] [Iter 2543/4650] R4[292/2400], Temp: 0.9639, Energy: -28.748925-0.000409j
[2025-08-24 07:03:10] [Iter 2544/4650] R4[293/2400], Temp: 0.9637, Energy: -28.745440+0.000798j
[2025-08-24 07:03:20] [Iter 2545/4650] R4[294/2400], Temp: 0.9634, Energy: -28.738085-0.002001j
[2025-08-24 07:03:30] [Iter 2546/4650] R4[295/2400], Temp: 0.9632, Energy: -28.747450-0.000258j
[2025-08-24 07:03:41] [Iter 2547/4650] R4[296/2400], Temp: 0.9629, Energy: -28.749405+0.002389j
[2025-08-24 07:03:51] [Iter 2548/4650] R4[297/2400], Temp: 0.9627, Energy: -28.743839-0.000556j
[2025-08-24 07:04:01] [Iter 2549/4650] R4[298/2400], Temp: 0.9624, Energy: -28.742904-0.001288j
[2025-08-24 07:04:11] [Iter 2550/4650] R4[299/2400], Temp: 0.9622, Energy: -28.743440+0.001037j
[2025-08-24 07:04:21] [Iter 2551/4650] R4[300/2400], Temp: 0.9619, Energy: -28.743275+0.001059j
[2025-08-24 07:04:31] [Iter 2552/4650] R4[301/2400], Temp: 0.9617, Energy: -28.749437-0.001349j
[2025-08-24 07:04:41] [Iter 2553/4650] R4[302/2400], Temp: 0.9614, Energy: -28.745344-0.001579j
[2025-08-24 07:04:52] [Iter 2554/4650] R4[303/2400], Temp: 0.9612, Energy: -28.750884+0.002894j
[2025-08-24 07:05:02] [Iter 2555/4650] R4[304/2400], Temp: 0.9609, Energy: -28.740869-0.000843j
[2025-08-24 07:05:12] [Iter 2556/4650] R4[305/2400], Temp: 0.9607, Energy: -28.746744+0.000292j
[2025-08-24 07:05:22] [Iter 2557/4650] R4[306/2400], Temp: 0.9604, Energy: -28.736275+0.000863j
[2025-08-24 07:05:32] [Iter 2558/4650] R4[307/2400], Temp: 0.9602, Energy: -28.743729-0.000750j
[2025-08-24 07:05:42] [Iter 2559/4650] R4[308/2400], Temp: 0.9599, Energy: -28.746595-0.001343j
[2025-08-24 07:05:52] [Iter 2560/4650] R4[309/2400], Temp: 0.9597, Energy: -28.749761+0.000038j
[2025-08-24 07:06:03] [Iter 2561/4650] R4[310/2400], Temp: 0.9594, Energy: -28.742623+0.001663j
[2025-08-24 07:06:13] [Iter 2562/4650] R4[311/2400], Temp: 0.9591, Energy: -28.736268-0.000630j
[2025-08-24 07:06:23] [Iter 2563/4650] R4[312/2400], Temp: 0.9589, Energy: -28.746183-0.003088j
[2025-08-24 07:06:33] [Iter 2564/4650] R4[313/2400], Temp: 0.9586, Energy: -28.738757-0.001462j
[2025-08-24 07:06:43] [Iter 2565/4650] R4[314/2400], Temp: 0.9584, Energy: -28.742695+0.001889j
[2025-08-24 07:06:53] [Iter 2566/4650] R4[315/2400], Temp: 0.9581, Energy: -28.743239-0.003666j
[2025-08-24 07:07:03] [Iter 2567/4650] R4[316/2400], Temp: 0.9578, Energy: -28.738429+0.000160j
[2025-08-24 07:07:14] [Iter 2568/4650] R4[317/2400], Temp: 0.9576, Energy: -28.743879+0.002079j
[2025-08-24 07:07:24] [Iter 2569/4650] R4[318/2400], Temp: 0.9573, Energy: -28.742161-0.001035j
[2025-08-24 07:07:34] [Iter 2570/4650] R4[319/2400], Temp: 0.9570, Energy: -28.739745-0.000171j
[2025-08-24 07:07:44] [Iter 2571/4650] R4[320/2400], Temp: 0.9568, Energy: -28.747866+0.000330j
[2025-08-24 07:07:54] [Iter 2572/4650] R4[321/2400], Temp: 0.9565, Energy: -28.746265-0.001076j
[2025-08-24 07:08:04] [Iter 2573/4650] R4[322/2400], Temp: 0.9562, Energy: -28.746458-0.000018j
[2025-08-24 07:08:15] [Iter 2574/4650] R4[323/2400], Temp: 0.9560, Energy: -28.745758+0.002001j
[2025-08-24 07:08:25] [Iter 2575/4650] R4[324/2400], Temp: 0.9557, Energy: -28.744917+0.000284j
[2025-08-24 07:08:35] [Iter 2576/4650] R4[325/2400], Temp: 0.9554, Energy: -28.740943+0.000060j
[2025-08-24 07:08:45] [Iter 2577/4650] R4[326/2400], Temp: 0.9552, Energy: -28.744236-0.001319j
[2025-08-24 07:08:55] [Iter 2578/4650] R4[327/2400], Temp: 0.9549, Energy: -28.739530+0.001403j
[2025-08-24 07:09:05] [Iter 2579/4650] R4[328/2400], Temp: 0.9546, Energy: -28.748553-0.004226j
[2025-08-24 07:09:15] [Iter 2580/4650] R4[329/2400], Temp: 0.9543, Energy: -28.746521+0.000749j
[2025-08-24 07:09:25] [Iter 2581/4650] R4[330/2400], Temp: 0.9541, Energy: -28.742824+0.001282j
[2025-08-24 07:09:36] [Iter 2582/4650] R4[331/2400], Temp: 0.9538, Energy: -28.748787+0.002400j
[2025-08-24 07:09:46] [Iter 2583/4650] R4[332/2400], Temp: 0.9535, Energy: -28.746964+0.001791j
[2025-08-24 07:09:56] [Iter 2584/4650] R4[333/2400], Temp: 0.9532, Energy: -28.745423+0.001450j
[2025-08-24 07:10:06] [Iter 2585/4650] R4[334/2400], Temp: 0.9530, Energy: -28.740872-0.000844j
[2025-08-24 07:10:16] [Iter 2586/4650] R4[335/2400], Temp: 0.9527, Energy: -28.744099-0.000553j
[2025-08-24 07:10:26] [Iter 2587/4650] R4[336/2400], Temp: 0.9524, Energy: -28.740772+0.001095j
[2025-08-24 07:10:37] [Iter 2588/4650] R4[337/2400], Temp: 0.9521, Energy: -28.744180-0.000879j
[2025-08-24 07:10:47] [Iter 2589/4650] R4[338/2400], Temp: 0.9519, Energy: -28.751702+0.001738j
[2025-08-24 07:10:57] [Iter 2590/4650] R4[339/2400], Temp: 0.9516, Energy: -28.744020-0.001700j
[2025-08-24 07:11:07] [Iter 2591/4650] R4[340/2400], Temp: 0.9513, Energy: -28.744264-0.001674j
[2025-08-24 07:11:17] [Iter 2592/4650] R4[341/2400], Temp: 0.9510, Energy: -28.742694-0.000304j
[2025-08-24 07:11:27] [Iter 2593/4650] R4[342/2400], Temp: 0.9507, Energy: -28.744064-0.000983j
[2025-08-24 07:11:37] [Iter 2594/4650] R4[343/2400], Temp: 0.9504, Energy: -28.742689-0.000107j
[2025-08-24 07:11:47] [Iter 2595/4650] R4[344/2400], Temp: 0.9502, Energy: -28.745961-0.002760j
[2025-08-24 07:11:58] [Iter 2596/4650] R4[345/2400], Temp: 0.9499, Energy: -28.749200+0.002704j
[2025-08-24 07:12:08] [Iter 2597/4650] R4[346/2400], Temp: 0.9496, Energy: -28.741592-0.000728j
[2025-08-24 07:12:18] [Iter 2598/4650] R4[347/2400], Temp: 0.9493, Energy: -28.751115+0.000125j
[2025-08-24 07:12:28] [Iter 2599/4650] R4[348/2400], Temp: 0.9490, Energy: -28.742342-0.001461j
[2025-08-24 07:12:38] [Iter 2600/4650] R4[349/2400], Temp: 0.9487, Energy: -28.746591-0.000008j
[2025-08-24 07:12:48] [Iter 2601/4650] R4[350/2400], Temp: 0.9484, Energy: -28.746817+0.000554j
[2025-08-24 07:12:59] [Iter 2602/4650] R4[351/2400], Temp: 0.9481, Energy: -28.746947+0.000590j
[2025-08-24 07:13:09] [Iter 2603/4650] R4[352/2400], Temp: 0.9479, Energy: -28.743744+0.001604j
[2025-08-24 07:13:19] [Iter 2604/4650] R4[353/2400], Temp: 0.9476, Energy: -28.743826+0.000765j
[2025-08-24 07:13:29] [Iter 2605/4650] R4[354/2400], Temp: 0.9473, Energy: -28.745990-0.002417j
[2025-08-24 07:13:39] [Iter 2606/4650] R4[355/2400], Temp: 0.9470, Energy: -28.740123-0.001805j
[2025-08-24 07:13:49] [Iter 2607/4650] R4[356/2400], Temp: 0.9467, Energy: -28.741772-0.001049j
[2025-08-24 07:13:59] [Iter 2608/4650] R4[357/2400], Temp: 0.9464, Energy: -28.753184+0.000422j
[2025-08-24 07:14:10] [Iter 2609/4650] R4[358/2400], Temp: 0.9461, Energy: -28.749014+0.000093j
[2025-08-24 07:14:20] [Iter 2610/4650] R4[359/2400], Temp: 0.9458, Energy: -28.744302+0.001189j
[2025-08-24 07:14:30] [Iter 2611/4650] R4[360/2400], Temp: 0.9455, Energy: -28.744970+0.000407j
[2025-08-24 07:14:40] [Iter 2612/4650] R4[361/2400], Temp: 0.9452, Energy: -28.741794+0.002250j
[2025-08-24 07:14:50] [Iter 2613/4650] R4[362/2400], Temp: 0.9449, Energy: -28.743192+0.001726j
[2025-08-24 07:15:00] [Iter 2614/4650] R4[363/2400], Temp: 0.9446, Energy: -28.748188-0.001530j
[2025-08-24 07:15:10] [Iter 2615/4650] R4[364/2400], Temp: 0.9443, Energy: -28.752657+0.002151j
[2025-08-24 07:15:21] [Iter 2616/4650] R4[365/2400], Temp: 0.9440, Energy: -28.740336-0.003230j
[2025-08-24 07:15:31] [Iter 2617/4650] R4[366/2400], Temp: 0.9437, Energy: -28.748542-0.000002j
[2025-08-24 07:15:41] [Iter 2618/4650] R4[367/2400], Temp: 0.9434, Energy: -28.741959-0.000234j
[2025-08-24 07:15:51] [Iter 2619/4650] R4[368/2400], Temp: 0.9431, Energy: -28.741947-0.001178j
[2025-08-24 07:16:01] [Iter 2620/4650] R4[369/2400], Temp: 0.9428, Energy: -28.755167+0.000221j
[2025-08-24 07:16:11] [Iter 2621/4650] R4[370/2400], Temp: 0.9425, Energy: -28.744283-0.001879j
[2025-08-24 07:16:21] [Iter 2622/4650] R4[371/2400], Temp: 0.9422, Energy: -28.749520-0.000466j
[2025-08-24 07:16:32] [Iter 2623/4650] R4[372/2400], Temp: 0.9419, Energy: -28.744948-0.000882j
[2025-08-24 07:16:42] [Iter 2624/4650] R4[373/2400], Temp: 0.9416, Energy: -28.744367-0.001279j
[2025-08-24 07:16:52] [Iter 2625/4650] R4[374/2400], Temp: 0.9413, Energy: -28.739351-0.000497j
[2025-08-24 07:17:02] [Iter 2626/4650] R4[375/2400], Temp: 0.9410, Energy: -28.738423+0.002583j
[2025-08-24 07:17:12] [Iter 2627/4650] R4[376/2400], Temp: 0.9407, Energy: -28.744856-0.001403j
[2025-08-24 07:17:22] [Iter 2628/4650] R4[377/2400], Temp: 0.9403, Energy: -28.746648-0.001090j
[2025-08-24 07:17:32] [Iter 2629/4650] R4[378/2400], Temp: 0.9400, Energy: -28.742823-0.000281j
[2025-08-24 07:17:42] [Iter 2630/4650] R4[379/2400], Temp: 0.9397, Energy: -28.746318-0.002329j
[2025-08-24 07:17:53] [Iter 2631/4650] R4[380/2400], Temp: 0.9394, Energy: -28.748727+0.001218j
[2025-08-24 07:18:03] [Iter 2632/4650] R4[381/2400], Temp: 0.9391, Energy: -28.744990-0.000568j
[2025-08-24 07:18:13] [Iter 2633/4650] R4[382/2400], Temp: 0.9388, Energy: -28.742501-0.001314j
[2025-08-24 07:18:23] [Iter 2634/4650] R4[383/2400], Temp: 0.9385, Energy: -28.742311-0.001672j
[2025-08-24 07:18:33] [Iter 2635/4650] R4[384/2400], Temp: 0.9382, Energy: -28.744733-0.003866j
[2025-08-24 07:18:43] [Iter 2636/4650] R4[385/2400], Temp: 0.9378, Energy: -28.748213-0.000093j
[2025-08-24 07:18:53] [Iter 2637/4650] R4[386/2400], Temp: 0.9375, Energy: -28.745662+0.000042j
[2025-08-24 07:19:04] [Iter 2638/4650] R4[387/2400], Temp: 0.9372, Energy: -28.745573+0.000072j
[2025-08-24 07:19:14] [Iter 2639/4650] R4[388/2400], Temp: 0.9369, Energy: -28.748444+0.001688j
[2025-08-24 07:19:24] [Iter 2640/4650] R4[389/2400], Temp: 0.9366, Energy: -28.747433+0.000059j
[2025-08-24 07:19:34] [Iter 2641/4650] R4[390/2400], Temp: 0.9362, Energy: -28.743811-0.000512j
[2025-08-24 07:19:44] [Iter 2642/4650] R4[391/2400], Temp: 0.9359, Energy: -28.744444+0.001113j
[2025-08-24 07:19:54] [Iter 2643/4650] R4[392/2400], Temp: 0.9356, Energy: -28.745314+0.000762j
[2025-08-24 07:20:04] [Iter 2644/4650] R4[393/2400], Temp: 0.9353, Energy: -28.745376-0.000983j
[2025-08-24 07:20:15] [Iter 2645/4650] R4[394/2400], Temp: 0.9350, Energy: -28.739899+0.000637j
[2025-08-24 07:20:25] [Iter 2646/4650] R4[395/2400], Temp: 0.9346, Energy: -28.746810+0.000369j
[2025-08-24 07:20:35] [Iter 2647/4650] R4[396/2400], Temp: 0.9343, Energy: -28.750098-0.000766j
[2025-08-24 07:20:45] [Iter 2648/4650] R4[397/2400], Temp: 0.9340, Energy: -28.738826-0.000684j
[2025-08-24 07:20:55] [Iter 2649/4650] R4[398/2400], Temp: 0.9337, Energy: -28.748301-0.003904j
[2025-08-24 07:21:05] [Iter 2650/4650] R4[399/2400], Temp: 0.9333, Energy: -28.746433-0.002451j
[2025-08-24 07:21:15] [Iter 2651/4650] R4[400/2400], Temp: 0.9330, Energy: -28.740240+0.000738j
[2025-08-24 07:21:26] [Iter 2652/4650] R4[401/2400], Temp: 0.9327, Energy: -28.749407-0.001082j
[2025-08-24 07:21:36] [Iter 2653/4650] R4[402/2400], Temp: 0.9324, Energy: -28.750524+0.001955j
[2025-08-24 07:21:46] [Iter 2654/4650] R4[403/2400], Temp: 0.9320, Energy: -28.745118-0.001608j
[2025-08-24 07:21:56] [Iter 2655/4650] R4[404/2400], Temp: 0.9317, Energy: -28.746809-0.001953j
[2025-08-24 07:22:06] [Iter 2656/4650] R4[405/2400], Temp: 0.9314, Energy: -28.746358+0.001596j
[2025-08-24 07:22:16] [Iter 2657/4650] R4[406/2400], Temp: 0.9310, Energy: -28.750996+0.001213j
[2025-08-24 07:22:26] [Iter 2658/4650] R4[407/2400], Temp: 0.9307, Energy: -28.746700-0.001819j
[2025-08-24 07:22:37] [Iter 2659/4650] R4[408/2400], Temp: 0.9304, Energy: -28.742105-0.000842j
[2025-08-24 07:22:47] [Iter 2660/4650] R4[409/2400], Temp: 0.9300, Energy: -28.747351-0.000693j
[2025-08-24 07:22:57] [Iter 2661/4650] R4[410/2400], Temp: 0.9297, Energy: -28.748456-0.000245j
[2025-08-24 07:23:07] [Iter 2662/4650] R4[411/2400], Temp: 0.9294, Energy: -28.738932+0.000002j
[2025-08-24 07:23:17] [Iter 2663/4650] R4[412/2400], Temp: 0.9290, Energy: -28.751719+0.002897j
[2025-08-24 07:23:27] [Iter 2664/4650] R4[413/2400], Temp: 0.9287, Energy: -28.741931+0.001205j
[2025-08-24 07:23:37] [Iter 2665/4650] R4[414/2400], Temp: 0.9284, Energy: -28.750610+0.001452j
[2025-08-24 07:23:48] [Iter 2666/4650] R4[415/2400], Temp: 0.9280, Energy: -28.743697-0.003688j
[2025-08-24 07:23:58] [Iter 2667/4650] R4[416/2400], Temp: 0.9277, Energy: -28.747358+0.000542j
[2025-08-24 07:24:08] [Iter 2668/4650] R4[417/2400], Temp: 0.9273, Energy: -28.744441-0.001235j
[2025-08-24 07:24:18] [Iter 2669/4650] R4[418/2400], Temp: 0.9270, Energy: -28.747269+0.000776j
[2025-08-24 07:24:28] [Iter 2670/4650] R4[419/2400], Temp: 0.9267, Energy: -28.742933-0.002240j
[2025-08-24 07:24:38] [Iter 2671/4650] R4[420/2400], Temp: 0.9263, Energy: -28.747677+0.000157j
[2025-08-24 07:24:48] [Iter 2672/4650] R4[421/2400], Temp: 0.9260, Energy: -28.744646-0.001161j
[2025-08-24 07:24:59] [Iter 2673/4650] R4[422/2400], Temp: 0.9256, Energy: -28.747492+0.000075j
[2025-08-24 07:25:09] [Iter 2674/4650] R4[423/2400], Temp: 0.9253, Energy: -28.746502-0.001756j
[2025-08-24 07:25:19] [Iter 2675/4650] R4[424/2400], Temp: 0.9249, Energy: -28.748413+0.000901j
[2025-08-24 07:25:29] [Iter 2676/4650] R4[425/2400], Temp: 0.9246, Energy: -28.751953+0.001208j
[2025-08-24 07:25:39] [Iter 2677/4650] R4[426/2400], Temp: 0.9243, Energy: -28.742298-0.000430j
[2025-08-24 07:25:49] [Iter 2678/4650] R4[427/2400], Temp: 0.9239, Energy: -28.743808-0.000376j
[2025-08-24 07:26:00] [Iter 2679/4650] R4[428/2400], Temp: 0.9236, Energy: -28.747035-0.000312j
[2025-08-24 07:26:10] [Iter 2680/4650] R4[429/2400], Temp: 0.9232, Energy: -28.746902+0.000222j
[2025-08-24 07:26:20] [Iter 2681/4650] R4[430/2400], Temp: 0.9229, Energy: -28.752802-0.000568j
[2025-08-24 07:26:30] [Iter 2682/4650] R4[431/2400], Temp: 0.9225, Energy: -28.747130-0.002853j
[2025-08-24 07:26:40] [Iter 2683/4650] R4[432/2400], Temp: 0.9222, Energy: -28.740538+0.001481j
[2025-08-24 07:26:50] [Iter 2684/4650] R4[433/2400], Temp: 0.9218, Energy: -28.745346+0.001038j
[2025-08-24 07:27:00] [Iter 2685/4650] R4[434/2400], Temp: 0.9215, Energy: -28.744956-0.001114j
[2025-08-24 07:27:11] [Iter 2686/4650] R4[435/2400], Temp: 0.9211, Energy: -28.744129-0.000927j
[2025-08-24 07:27:21] [Iter 2687/4650] R4[436/2400], Temp: 0.9208, Energy: -28.747428-0.001486j
[2025-08-24 07:27:31] [Iter 2688/4650] R4[437/2400], Temp: 0.9204, Energy: -28.743076-0.001769j
[2025-08-24 07:27:41] [Iter 2689/4650] R4[438/2400], Temp: 0.9200, Energy: -28.748342-0.001203j
[2025-08-24 07:27:51] [Iter 2690/4650] R4[439/2400], Temp: 0.9197, Energy: -28.750720+0.000079j
[2025-08-24 07:28:01] [Iter 2691/4650] R4[440/2400], Temp: 0.9193, Energy: -28.742577+0.000120j
[2025-08-24 07:28:11] [Iter 2692/4650] R4[441/2400], Temp: 0.9190, Energy: -28.739751+0.002197j
[2025-08-24 07:28:22] [Iter 2693/4650] R4[442/2400], Temp: 0.9186, Energy: -28.751928+0.000675j
[2025-08-24 07:28:32] [Iter 2694/4650] R4[443/2400], Temp: 0.9183, Energy: -28.745827-0.001782j
[2025-08-24 07:28:42] [Iter 2695/4650] R4[444/2400], Temp: 0.9179, Energy: -28.750825+0.000464j
[2025-08-24 07:28:52] [Iter 2696/4650] R4[445/2400], Temp: 0.9175, Energy: -28.746758-0.000983j
[2025-08-24 07:29:02] [Iter 2697/4650] R4[446/2400], Temp: 0.9172, Energy: -28.743508-0.000079j
[2025-08-24 07:29:12] [Iter 2698/4650] R4[447/2400], Temp: 0.9168, Energy: -28.739594+0.001758j
[2025-08-24 07:29:22] [Iter 2699/4650] R4[448/2400], Temp: 0.9165, Energy: -28.744358-0.000248j
[2025-08-24 07:29:33] [Iter 2700/4650] R4[449/2400], Temp: 0.9161, Energy: -28.748307+0.000478j
[2025-08-24 07:29:43] [Iter 2701/4650] R4[450/2400], Temp: 0.9157, Energy: -28.738186-0.001907j
[2025-08-24 07:29:53] [Iter 2702/4650] R4[451/2400], Temp: 0.9154, Energy: -28.744504+0.000917j
[2025-08-24 07:30:03] [Iter 2703/4650] R4[452/2400], Temp: 0.9150, Energy: -28.751759-0.001626j
[2025-08-24 07:30:13] [Iter 2704/4650] R4[453/2400], Temp: 0.9146, Energy: -28.747226+0.001275j
[2025-08-24 07:30:23] [Iter 2705/4650] R4[454/2400], Temp: 0.9143, Energy: -28.743402+0.000570j
[2025-08-24 07:30:33] [Iter 2706/4650] R4[455/2400], Temp: 0.9139, Energy: -28.747706+0.001851j
[2025-08-24 07:30:44] [Iter 2707/4650] R4[456/2400], Temp: 0.9135, Energy: -28.746083-0.001067j
[2025-08-24 07:30:54] [Iter 2708/4650] R4[457/2400], Temp: 0.9132, Energy: -28.746351+0.001275j
[2025-08-24 07:31:04] [Iter 2709/4650] R4[458/2400], Temp: 0.9128, Energy: -28.748364+0.000747j
[2025-08-24 07:31:14] [Iter 2710/4650] R4[459/2400], Temp: 0.9124, Energy: -28.748089+0.001311j
[2025-08-24 07:31:24] [Iter 2711/4650] R4[460/2400], Temp: 0.9121, Energy: -28.746002-0.001050j
[2025-08-24 07:31:34] [Iter 2712/4650] R4[461/2400], Temp: 0.9117, Energy: -28.748589-0.001124j
[2025-08-24 07:31:44] [Iter 2713/4650] R4[462/2400], Temp: 0.9113, Energy: -28.749288-0.000030j
[2025-08-24 07:31:55] [Iter 2714/4650] R4[463/2400], Temp: 0.9109, Energy: -28.744812-0.000093j
[2025-08-24 07:32:05] [Iter 2715/4650] R4[464/2400], Temp: 0.9106, Energy: -28.747452+0.001425j
[2025-08-24 07:32:15] [Iter 2716/4650] R4[465/2400], Temp: 0.9102, Energy: -28.747729-0.000272j
[2025-08-24 07:32:25] [Iter 2717/4650] R4[466/2400], Temp: 0.9098, Energy: -28.746091-0.001096j
[2025-08-24 07:32:35] [Iter 2718/4650] R4[467/2400], Temp: 0.9095, Energy: -28.743962+0.000152j
[2025-08-24 07:32:45] [Iter 2719/4650] R4[468/2400], Temp: 0.9091, Energy: -28.750702+0.000435j
[2025-08-24 07:32:55] [Iter 2720/4650] R4[469/2400], Temp: 0.9087, Energy: -28.747334-0.002447j
[2025-08-24 07:33:06] [Iter 2721/4650] R4[470/2400], Temp: 0.9083, Energy: -28.750161+0.001472j
[2025-08-24 07:33:16] [Iter 2722/4650] R4[471/2400], Temp: 0.9079, Energy: -28.749372+0.002848j
[2025-08-24 07:33:26] [Iter 2723/4650] R4[472/2400], Temp: 0.9076, Energy: -28.746096-0.001691j
[2025-08-24 07:33:36] [Iter 2724/4650] R4[473/2400], Temp: 0.9072, Energy: -28.750086+0.000081j
[2025-08-24 07:33:46] [Iter 2725/4650] R4[474/2400], Temp: 0.9068, Energy: -28.746865-0.002360j
[2025-08-24 07:33:56] [Iter 2726/4650] R4[475/2400], Temp: 0.9064, Energy: -28.737317-0.000570j
[2025-08-24 07:34:06] [Iter 2727/4650] R4[476/2400], Temp: 0.9060, Energy: -28.749495-0.000587j
[2025-08-24 07:34:17] [Iter 2728/4650] R4[477/2400], Temp: 0.9057, Energy: -28.750205-0.000523j
[2025-08-24 07:34:27] [Iter 2729/4650] R4[478/2400], Temp: 0.9053, Energy: -28.749505-0.001537j
[2025-08-24 07:34:37] [Iter 2730/4650] R4[479/2400], Temp: 0.9049, Energy: -28.743676+0.000580j
[2025-08-24 07:34:47] [Iter 2731/4650] R4[480/2400], Temp: 0.9045, Energy: -28.743937+0.002507j
[2025-08-24 07:34:57] [Iter 2732/4650] R4[481/2400], Temp: 0.9041, Energy: -28.743129-0.000607j
[2025-08-24 07:35:07] [Iter 2733/4650] R4[482/2400], Temp: 0.9037, Energy: -28.748230+0.000845j
[2025-08-24 07:35:17] [Iter 2734/4650] R4[483/2400], Temp: 0.9034, Energy: -28.748583-0.000043j
[2025-08-24 07:35:28] [Iter 2735/4650] R4[484/2400], Temp: 0.9030, Energy: -28.744764-0.002698j
[2025-08-24 07:35:38] [Iter 2736/4650] R4[485/2400], Temp: 0.9026, Energy: -28.744186-0.000132j
[2025-08-24 07:35:48] [Iter 2737/4650] R4[486/2400], Temp: 0.9022, Energy: -28.745366+0.000372j
[2025-08-24 07:35:58] [Iter 2738/4650] R4[487/2400], Temp: 0.9018, Energy: -28.745019+0.000645j
[2025-08-24 07:36:08] [Iter 2739/4650] R4[488/2400], Temp: 0.9014, Energy: -28.745002+0.002333j
[2025-08-24 07:36:18] [Iter 2740/4650] R4[489/2400], Temp: 0.9010, Energy: -28.752435+0.001461j
[2025-08-24 07:36:28] [Iter 2741/4650] R4[490/2400], Temp: 0.9006, Energy: -28.746325-0.000254j
[2025-08-24 07:36:39] [Iter 2742/4650] R4[491/2400], Temp: 0.9002, Energy: -28.749805+0.001848j
[2025-08-24 07:36:49] [Iter 2743/4650] R4[492/2400], Temp: 0.8998, Energy: -28.752158+0.002787j
[2025-08-24 07:36:59] [Iter 2744/4650] R4[493/2400], Temp: 0.8994, Energy: -28.757095-0.002680j
[2025-08-24 07:37:09] [Iter 2745/4650] R4[494/2400], Temp: 0.8991, Energy: -28.747558+0.001966j
[2025-08-24 07:37:19] [Iter 2746/4650] R4[495/2400], Temp: 0.8987, Energy: -28.749365-0.000614j
[2025-08-24 07:37:29] [Iter 2747/4650] R4[496/2400], Temp: 0.8983, Energy: -28.751517+0.003196j
[2025-08-24 07:37:39] [Iter 2748/4650] R4[497/2400], Temp: 0.8979, Energy: -28.751319+0.000769j
[2025-08-24 07:37:50] [Iter 2749/4650] R4[498/2400], Temp: 0.8975, Energy: -28.749026+0.002023j
[2025-08-24 07:38:00] [Iter 2750/4650] R4[499/2400], Temp: 0.8971, Energy: -28.748242-0.000745j
[2025-08-24 07:38:10] [Iter 2751/4650] R4[500/2400], Temp: 0.8967, Energy: -28.751463-0.000539j
[2025-08-24 07:38:20] [Iter 2752/4650] R4[501/2400], Temp: 0.8963, Energy: -28.752651-0.000451j
[2025-08-24 07:38:30] [Iter 2753/4650] R4[502/2400], Temp: 0.8959, Energy: -28.745349-0.000725j
[2025-08-24 07:38:40] [Iter 2754/4650] R4[503/2400], Temp: 0.8955, Energy: -28.739085+0.001950j
[2025-08-24 07:38:50] [Iter 2755/4650] R4[504/2400], Temp: 0.8951, Energy: -28.741604-0.000583j
[2025-08-24 07:39:01] [Iter 2756/4650] R4[505/2400], Temp: 0.8947, Energy: -28.745004-0.000830j
[2025-08-24 07:39:11] [Iter 2757/4650] R4[506/2400], Temp: 0.8943, Energy: -28.746634-0.002546j
[2025-08-24 07:39:21] [Iter 2758/4650] R4[507/2400], Temp: 0.8939, Energy: -28.752508+0.000434j
[2025-08-24 07:39:31] [Iter 2759/4650] R4[508/2400], Temp: 0.8935, Energy: -28.746274+0.000322j
[2025-08-24 07:39:41] [Iter 2760/4650] R4[509/2400], Temp: 0.8931, Energy: -28.747139+0.002200j
[2025-08-24 07:39:51] [Iter 2761/4650] R4[510/2400], Temp: 0.8927, Energy: -28.745013+0.001548j
[2025-08-24 07:40:01] [Iter 2762/4650] R4[511/2400], Temp: 0.8923, Energy: -28.752529+0.000473j
[2025-08-24 07:40:12] [Iter 2763/4650] R4[512/2400], Temp: 0.8918, Energy: -28.746489-0.001199j
[2025-08-24 07:40:22] [Iter 2764/4650] R4[513/2400], Temp: 0.8914, Energy: -28.744565-0.001521j
[2025-08-24 07:40:32] [Iter 2765/4650] R4[514/2400], Temp: 0.8910, Energy: -28.749392-0.002666j
[2025-08-24 07:40:42] [Iter 2766/4650] R4[515/2400], Temp: 0.8906, Energy: -28.752059+0.001023j
[2025-08-24 07:40:52] [Iter 2767/4650] R4[516/2400], Temp: 0.8902, Energy: -28.745117+0.002883j
[2025-08-24 07:41:02] [Iter 2768/4650] R4[517/2400], Temp: 0.8898, Energy: -28.746922+0.000525j
[2025-08-24 07:41:12] [Iter 2769/4650] R4[518/2400], Temp: 0.8894, Energy: -28.748992+0.001505j
[2025-08-24 07:41:23] [Iter 2770/4650] R4[519/2400], Temp: 0.8890, Energy: -28.748073+0.002593j
[2025-08-24 07:41:33] [Iter 2771/4650] R4[520/2400], Temp: 0.8886, Energy: -28.746214+0.000536j
[2025-08-24 07:41:43] [Iter 2772/4650] R4[521/2400], Temp: 0.8882, Energy: -28.751599+0.000794j
[2025-08-24 07:41:53] [Iter 2773/4650] R4[522/2400], Temp: 0.8877, Energy: -28.750982+0.000415j
[2025-08-24 07:42:03] [Iter 2774/4650] R4[523/2400], Temp: 0.8873, Energy: -28.749162+0.000021j
[2025-08-24 07:42:13] [Iter 2775/4650] R4[524/2400], Temp: 0.8869, Energy: -28.752290+0.001240j
[2025-08-24 07:42:23] [Iter 2776/4650] R4[525/2400], Temp: 0.8865, Energy: -28.747109+0.002092j
[2025-08-24 07:42:33] [Iter 2777/4650] R4[526/2400], Temp: 0.8861, Energy: -28.744030-0.001519j
[2025-08-24 07:42:44] [Iter 2778/4650] R4[527/2400], Temp: 0.8857, Energy: -28.743106-0.000875j
[2025-08-24 07:42:54] [Iter 2779/4650] R4[528/2400], Temp: 0.8853, Energy: -28.744312-0.000959j
[2025-08-24 07:43:04] [Iter 2780/4650] R4[529/2400], Temp: 0.8848, Energy: -28.739879-0.000757j
[2025-08-24 07:43:14] [Iter 2781/4650] R4[530/2400], Temp: 0.8844, Energy: -28.747662-0.001148j
[2025-08-24 07:43:24] [Iter 2782/4650] R4[531/2400], Temp: 0.8840, Energy: -28.750596-0.000363j
[2025-08-24 07:43:34] [Iter 2783/4650] R4[532/2400], Temp: 0.8836, Energy: -28.745774-0.000964j
[2025-08-24 07:43:44] [Iter 2784/4650] R4[533/2400], Temp: 0.8832, Energy: -28.748902-0.000906j
[2025-08-24 07:43:55] [Iter 2785/4650] R4[534/2400], Temp: 0.8827, Energy: -28.753623+0.000440j
[2025-08-24 07:44:05] [Iter 2786/4650] R4[535/2400], Temp: 0.8823, Energy: -28.749573-0.000335j
[2025-08-24 07:44:15] [Iter 2787/4650] R4[536/2400], Temp: 0.8819, Energy: -28.744908+0.000571j
[2025-08-24 07:44:25] [Iter 2788/4650] R4[537/2400], Temp: 0.8815, Energy: -28.750406-0.002036j
[2025-08-24 07:44:35] [Iter 2789/4650] R4[538/2400], Temp: 0.8811, Energy: -28.748698+0.001913j
[2025-08-24 07:44:45] [Iter 2790/4650] R4[539/2400], Temp: 0.8806, Energy: -28.742861+0.000004j
[2025-08-24 07:44:55] [Iter 2791/4650] R4[540/2400], Temp: 0.8802, Energy: -28.748646-0.000759j
[2025-08-24 07:45:06] [Iter 2792/4650] R4[541/2400], Temp: 0.8798, Energy: -28.747864-0.000036j
[2025-08-24 07:45:16] [Iter 2793/4650] R4[542/2400], Temp: 0.8794, Energy: -28.746666-0.000406j
[2025-08-24 07:45:26] [Iter 2794/4650] R4[543/2400], Temp: 0.8789, Energy: -28.746859+0.000521j
[2025-08-24 07:45:36] [Iter 2795/4650] R4[544/2400], Temp: 0.8785, Energy: -28.744364+0.001983j
[2025-08-24 07:45:46] [Iter 2796/4650] R4[545/2400], Temp: 0.8781, Energy: -28.745810+0.001934j
[2025-08-24 07:45:56] [Iter 2797/4650] R4[546/2400], Temp: 0.8776, Energy: -28.746968-0.001270j
[2025-08-24 07:46:06] [Iter 2798/4650] R4[547/2400], Temp: 0.8772, Energy: -28.745269+0.000249j
[2025-08-24 07:46:17] [Iter 2799/4650] R4[548/2400], Temp: 0.8768, Energy: -28.748794-0.000240j
[2025-08-24 07:46:27] [Iter 2800/4650] R4[549/2400], Temp: 0.8764, Energy: -28.752090+0.001820j
[2025-08-24 07:46:37] [Iter 2801/4650] R4[550/2400], Temp: 0.8759, Energy: -28.745896-0.000912j
[2025-08-24 07:46:47] [Iter 2802/4650] R4[551/2400], Temp: 0.8755, Energy: -28.742482+0.002613j
[2025-08-24 07:46:57] [Iter 2803/4650] R4[552/2400], Temp: 0.8751, Energy: -28.748330+0.002304j
[2025-08-24 07:47:07] [Iter 2804/4650] R4[553/2400], Temp: 0.8746, Energy: -28.747735-0.000414j
[2025-08-24 07:47:17] [Iter 2805/4650] R4[554/2400], Temp: 0.8742, Energy: -28.744846-0.000225j
[2025-08-24 07:47:28] [Iter 2806/4650] R4[555/2400], Temp: 0.8738, Energy: -28.750854-0.001614j
[2025-08-24 07:47:38] [Iter 2807/4650] R4[556/2400], Temp: 0.8733, Energy: -28.745616+0.000830j
[2025-08-24 07:47:48] [Iter 2808/4650] R4[557/2400], Temp: 0.8729, Energy: -28.743026+0.001901j
[2025-08-24 07:47:58] [Iter 2809/4650] R4[558/2400], Temp: 0.8724, Energy: -28.744656-0.001914j
[2025-08-24 07:48:08] [Iter 2810/4650] R4[559/2400], Temp: 0.8720, Energy: -28.751338-0.000897j
[2025-08-24 07:48:18] [Iter 2811/4650] R4[560/2400], Temp: 0.8716, Energy: -28.747636+0.000514j
[2025-08-24 07:48:28] [Iter 2812/4650] R4[561/2400], Temp: 0.8711, Energy: -28.748435+0.000335j
[2025-08-24 07:48:39] [Iter 2813/4650] R4[562/2400], Temp: 0.8707, Energy: -28.747132+0.000392j
[2025-08-24 07:48:49] [Iter 2814/4650] R4[563/2400], Temp: 0.8703, Energy: -28.743615-0.000536j
[2025-08-24 07:48:59] [Iter 2815/4650] R4[564/2400], Temp: 0.8698, Energy: -28.754954-0.002502j
[2025-08-24 07:49:09] [Iter 2816/4650] R4[565/2400], Temp: 0.8694, Energy: -28.750391-0.001116j
[2025-08-24 07:49:19] [Iter 2817/4650] R4[566/2400], Temp: 0.8689, Energy: -28.744723+0.001735j
[2025-08-24 07:49:29] [Iter 2818/4650] R4[567/2400], Temp: 0.8685, Energy: -28.748885-0.000387j
[2025-08-24 07:49:39] [Iter 2819/4650] R4[568/2400], Temp: 0.8680, Energy: -28.749231-0.001810j
[2025-08-24 07:49:50] [Iter 2820/4650] R4[569/2400], Temp: 0.8676, Energy: -28.757444+0.001067j
[2025-08-24 07:50:00] [Iter 2821/4650] R4[570/2400], Temp: 0.8672, Energy: -28.745259+0.001484j
[2025-08-24 07:50:10] [Iter 2822/4650] R4[571/2400], Temp: 0.8667, Energy: -28.748266+0.001920j
[2025-08-24 07:50:20] [Iter 2823/4650] R4[572/2400], Temp: 0.8663, Energy: -28.747578-0.000935j
[2025-08-24 07:50:30] [Iter 2824/4650] R4[573/2400], Temp: 0.8658, Energy: -28.747181-0.000691j
[2025-08-24 07:50:40] [Iter 2825/4650] R4[574/2400], Temp: 0.8654, Energy: -28.748813-0.000136j
[2025-08-24 07:50:50] [Iter 2826/4650] R4[575/2400], Temp: 0.8649, Energy: -28.747663-0.000593j
[2025-08-24 07:51:01] [Iter 2827/4650] R4[576/2400], Temp: 0.8645, Energy: -28.747534-0.001367j
[2025-08-24 07:51:11] [Iter 2828/4650] R4[577/2400], Temp: 0.8640, Energy: -28.744993+0.000951j
[2025-08-24 07:51:21] [Iter 2829/4650] R4[578/2400], Temp: 0.8636, Energy: -28.747407-0.001551j
[2025-08-24 07:51:31] [Iter 2830/4650] R4[579/2400], Temp: 0.8631, Energy: -28.747924-0.000046j
[2025-08-24 07:51:41] [Iter 2831/4650] R4[580/2400], Temp: 0.8627, Energy: -28.751045-0.000427j
[2025-08-24 07:51:51] [Iter 2832/4650] R4[581/2400], Temp: 0.8622, Energy: -28.743843-0.001029j
[2025-08-24 07:52:01] [Iter 2833/4650] R4[582/2400], Temp: 0.8618, Energy: -28.749662+0.001067j
[2025-08-24 07:52:11] [Iter 2834/4650] R4[583/2400], Temp: 0.8613, Energy: -28.745817+0.001036j
[2025-08-24 07:52:22] [Iter 2835/4650] R4[584/2400], Temp: 0.8609, Energy: -28.747647-0.000902j
[2025-08-24 07:52:32] [Iter 2836/4650] R4[585/2400], Temp: 0.8604, Energy: -28.748597-0.000770j
[2025-08-24 07:52:42] [Iter 2837/4650] R4[586/2400], Temp: 0.8600, Energy: -28.744306-0.000344j
[2025-08-24 07:52:52] [Iter 2838/4650] R4[587/2400], Temp: 0.8595, Energy: -28.741884-0.002393j
[2025-08-24 07:53:02] [Iter 2839/4650] R4[588/2400], Temp: 0.8591, Energy: -28.740045-0.000521j
[2025-08-24 07:53:12] [Iter 2840/4650] R4[589/2400], Temp: 0.8586, Energy: -28.742111-0.000410j
[2025-08-24 07:53:22] [Iter 2841/4650] R4[590/2400], Temp: 0.8582, Energy: -28.743339+0.000586j
[2025-08-24 07:53:33] [Iter 2842/4650] R4[591/2400], Temp: 0.8577, Energy: -28.746827-0.000436j
[2025-08-24 07:53:43] [Iter 2843/4650] R4[592/2400], Temp: 0.8572, Energy: -28.748311-0.001150j
[2025-08-24 07:53:53] [Iter 2844/4650] R4[593/2400], Temp: 0.8568, Energy: -28.753593+0.000066j
[2025-08-24 07:54:03] [Iter 2845/4650] R4[594/2400], Temp: 0.8563, Energy: -28.752973+0.000316j
[2025-08-24 07:54:13] [Iter 2846/4650] R4[595/2400], Temp: 0.8559, Energy: -28.750458+0.002298j
[2025-08-24 07:54:23] [Iter 2847/4650] R4[596/2400], Temp: 0.8554, Energy: -28.745101+0.002281j
[2025-08-24 07:54:33] [Iter 2848/4650] R4[597/2400], Temp: 0.8549, Energy: -28.750438-0.001212j
[2025-08-24 07:54:44] [Iter 2849/4650] R4[598/2400], Temp: 0.8545, Energy: -28.748363+0.001133j
[2025-08-24 07:54:54] [Iter 2850/4650] R4[599/2400], Temp: 0.8540, Energy: -28.745602+0.000403j
[2025-08-24 07:55:04] [Iter 2851/4650] R4[600/2400], Temp: 0.8536, Energy: -28.745676+0.000174j
[2025-08-24 07:55:14] [Iter 2852/4650] R4[601/2400], Temp: 0.8531, Energy: -28.745026-0.000368j
[2025-08-24 07:55:24] [Iter 2853/4650] R4[602/2400], Temp: 0.8526, Energy: -28.745094-0.001159j
[2025-08-24 07:55:34] [Iter 2854/4650] R4[603/2400], Temp: 0.8522, Energy: -28.747166+0.000620j
[2025-08-24 07:55:44] [Iter 2855/4650] R4[604/2400], Temp: 0.8517, Energy: -28.750248-0.000197j
[2025-08-24 07:55:55] [Iter 2856/4650] R4[605/2400], Temp: 0.8512, Energy: -28.751747-0.001741j
[2025-08-24 07:56:05] [Iter 2857/4650] R4[606/2400], Temp: 0.8508, Energy: -28.751510-0.000177j
[2025-08-24 07:56:15] [Iter 2858/4650] R4[607/2400], Temp: 0.8503, Energy: -28.745921+0.000874j
[2025-08-24 07:56:25] [Iter 2859/4650] R4[608/2400], Temp: 0.8498, Energy: -28.745724+0.001339j
[2025-08-24 07:56:35] [Iter 2860/4650] R4[609/2400], Temp: 0.8494, Energy: -28.744942-0.001516j
[2025-08-24 07:56:45] [Iter 2861/4650] R4[610/2400], Temp: 0.8489, Energy: -28.742611+0.000014j
[2025-08-24 07:56:55] [Iter 2862/4650] R4[611/2400], Temp: 0.8484, Energy: -28.748341+0.001091j
[2025-08-24 07:57:06] [Iter 2863/4650] R4[612/2400], Temp: 0.8480, Energy: -28.746931+0.000110j
[2025-08-24 07:57:16] [Iter 2864/4650] R4[613/2400], Temp: 0.8475, Energy: -28.743649+0.001698j
[2025-08-24 07:57:26] [Iter 2865/4650] R4[614/2400], Temp: 0.8470, Energy: -28.749264-0.000540j
[2025-08-24 07:57:36] [Iter 2866/4650] R4[615/2400], Temp: 0.8465, Energy: -28.752537-0.000607j
[2025-08-24 07:57:46] [Iter 2867/4650] R4[616/2400], Temp: 0.8461, Energy: -28.751094+0.003873j
[2025-08-24 07:57:56] [Iter 2868/4650] R4[617/2400], Temp: 0.8456, Energy: -28.755568-0.003265j
[2025-08-24 07:58:06] [Iter 2869/4650] R4[618/2400], Temp: 0.8451, Energy: -28.746034-0.000662j
[2025-08-24 07:58:17] [Iter 2870/4650] R4[619/2400], Temp: 0.8447, Energy: -28.749589-0.001986j
[2025-08-24 07:58:27] [Iter 2871/4650] R4[620/2400], Temp: 0.8442, Energy: -28.741325-0.000190j
[2025-08-24 07:58:37] [Iter 2872/4650] R4[621/2400], Temp: 0.8437, Energy: -28.740821+0.001354j
[2025-08-24 07:58:47] [Iter 2873/4650] R4[622/2400], Temp: 0.8432, Energy: -28.747731-0.000688j
[2025-08-24 07:58:57] [Iter 2874/4650] R4[623/2400], Temp: 0.8428, Energy: -28.745876+0.001889j
[2025-08-24 07:59:07] [Iter 2875/4650] R4[624/2400], Temp: 0.8423, Energy: -28.749761+0.000168j
[2025-08-24 07:59:17] [Iter 2876/4650] R4[625/2400], Temp: 0.8418, Energy: -28.745983+0.001892j
[2025-08-24 07:59:28] [Iter 2877/4650] R4[626/2400], Temp: 0.8413, Energy: -28.744849-0.002671j
[2025-08-24 07:59:38] [Iter 2878/4650] R4[627/2400], Temp: 0.8408, Energy: -28.743911+0.001078j
[2025-08-24 07:59:48] [Iter 2879/4650] R4[628/2400], Temp: 0.8404, Energy: -28.743899+0.001836j
[2025-08-24 07:59:58] [Iter 2880/4650] R4[629/2400], Temp: 0.8399, Energy: -28.752231+0.001116j
[2025-08-24 08:00:08] [Iter 2881/4650] R4[630/2400], Temp: 0.8394, Energy: -28.749353+0.002156j
[2025-08-24 08:00:18] [Iter 2882/4650] R4[631/2400], Temp: 0.8389, Energy: -28.744177+0.000043j
[2025-08-24 08:00:28] [Iter 2883/4650] R4[632/2400], Temp: 0.8384, Energy: -28.752304+0.000142j
[2025-08-24 08:00:38] [Iter 2884/4650] R4[633/2400], Temp: 0.8380, Energy: -28.754295+0.002181j
[2025-08-24 08:00:49] [Iter 2885/4650] R4[634/2400], Temp: 0.8375, Energy: -28.750215+0.001778j
[2025-08-24 08:00:59] [Iter 2886/4650] R4[635/2400], Temp: 0.8370, Energy: -28.747875+0.000363j
[2025-08-24 08:01:09] [Iter 2887/4650] R4[636/2400], Temp: 0.8365, Energy: -28.749546-0.001106j
[2025-08-24 08:01:19] [Iter 2888/4650] R4[637/2400], Temp: 0.8360, Energy: -28.748171-0.001270j
[2025-08-24 08:01:29] [Iter 2889/4650] R4[638/2400], Temp: 0.8355, Energy: -28.753767+0.000335j
[2025-08-24 08:01:39] [Iter 2890/4650] R4[639/2400], Temp: 0.8351, Energy: -28.750399-0.001084j
[2025-08-24 08:01:49] [Iter 2891/4650] R4[640/2400], Temp: 0.8346, Energy: -28.747305-0.001977j
[2025-08-24 08:02:00] [Iter 2892/4650] R4[641/2400], Temp: 0.8341, Energy: -28.748569+0.000197j
[2025-08-24 08:02:10] [Iter 2893/4650] R4[642/2400], Temp: 0.8336, Energy: -28.739927-0.000648j
[2025-08-24 08:02:20] [Iter 2894/4650] R4[643/2400], Temp: 0.8331, Energy: -28.752599+0.000330j
[2025-08-24 08:02:30] [Iter 2895/4650] R4[644/2400], Temp: 0.8326, Energy: -28.751909+0.003616j
[2025-08-24 08:02:40] [Iter 2896/4650] R4[645/2400], Temp: 0.8321, Energy: -28.749855-0.000976j
[2025-08-24 08:02:50] [Iter 2897/4650] R4[646/2400], Temp: 0.8316, Energy: -28.748833+0.002878j
[2025-08-24 08:03:00] [Iter 2898/4650] R4[647/2400], Temp: 0.8311, Energy: -28.750945+0.000206j
[2025-08-24 08:03:11] [Iter 2899/4650] R4[648/2400], Temp: 0.8307, Energy: -28.745159-0.000753j
[2025-08-24 08:03:21] [Iter 2900/4650] R4[649/2400], Temp: 0.8302, Energy: -28.746143+0.000024j
[2025-08-24 08:03:31] [Iter 2901/4650] R4[650/2400], Temp: 0.8297, Energy: -28.745775+0.002461j
[2025-08-24 08:03:41] [Iter 2902/4650] R4[651/2400], Temp: 0.8292, Energy: -28.746192+0.000714j
[2025-08-24 08:03:51] [Iter 2903/4650] R4[652/2400], Temp: 0.8287, Energy: -28.741031-0.000576j
[2025-08-24 08:04:01] [Iter 2904/4650] R4[653/2400], Temp: 0.8282, Energy: -28.742661+0.001547j
[2025-08-24 08:04:11] [Iter 2905/4650] R4[654/2400], Temp: 0.8277, Energy: -28.748424-0.000251j
[2025-08-24 08:04:22] [Iter 2906/4650] R4[655/2400], Temp: 0.8272, Energy: -28.746535+0.000599j
[2025-08-24 08:04:32] [Iter 2907/4650] R4[656/2400], Temp: 0.8267, Energy: -28.748448-0.000056j
[2025-08-24 08:04:42] [Iter 2908/4650] R4[657/2400], Temp: 0.8262, Energy: -28.745898-0.000049j
[2025-08-24 08:04:52] [Iter 2909/4650] R4[658/2400], Temp: 0.8257, Energy: -28.744749-0.001332j
[2025-08-24 08:05:02] [Iter 2910/4650] R4[659/2400], Temp: 0.8252, Energy: -28.751853-0.001184j
[2025-08-24 08:05:12] [Iter 2911/4650] R4[660/2400], Temp: 0.8247, Energy: -28.745450+0.001293j
[2025-08-24 08:05:22] [Iter 2912/4650] R4[661/2400], Temp: 0.8242, Energy: -28.746467-0.000008j
[2025-08-24 08:05:33] [Iter 2913/4650] R4[662/2400], Temp: 0.8237, Energy: -28.743451-0.001974j
[2025-08-24 08:05:43] [Iter 2914/4650] R4[663/2400], Temp: 0.8232, Energy: -28.750461+0.002289j
[2025-08-24 08:05:53] [Iter 2915/4650] R4[664/2400], Temp: 0.8227, Energy: -28.747115+0.001288j
[2025-08-24 08:06:03] [Iter 2916/4650] R4[665/2400], Temp: 0.8222, Energy: -28.743673+0.002481j
[2025-08-24 08:06:13] [Iter 2917/4650] R4[666/2400], Temp: 0.8217, Energy: -28.747358-0.001672j
[2025-08-24 08:06:23] [Iter 2918/4650] R4[667/2400], Temp: 0.8212, Energy: -28.755312-0.000130j
[2025-08-24 08:06:33] [Iter 2919/4650] R4[668/2400], Temp: 0.8207, Energy: -28.745134+0.001594j
[2025-08-24 08:06:44] [Iter 2920/4650] R4[669/2400], Temp: 0.8202, Energy: -28.749951-0.000589j
[2025-08-24 08:06:54] [Iter 2921/4650] R4[670/2400], Temp: 0.8197, Energy: -28.750109-0.000368j
[2025-08-24 08:07:04] [Iter 2922/4650] R4[671/2400], Temp: 0.8192, Energy: -28.747669+0.000423j
[2025-08-24 08:07:14] [Iter 2923/4650] R4[672/2400], Temp: 0.8187, Energy: -28.747175-0.001579j
[2025-08-24 08:07:24] [Iter 2924/4650] R4[673/2400], Temp: 0.8182, Energy: -28.745857+0.000765j
[2025-08-24 08:07:34] [Iter 2925/4650] R4[674/2400], Temp: 0.8177, Energy: -28.748026+0.001749j
[2025-08-24 08:07:44] [Iter 2926/4650] R4[675/2400], Temp: 0.8172, Energy: -28.743664+0.000929j
[2025-08-24 08:07:55] [Iter 2927/4650] R4[676/2400], Temp: 0.8167, Energy: -28.742913-0.001411j
[2025-08-24 08:08:05] [Iter 2928/4650] R4[677/2400], Temp: 0.8162, Energy: -28.746199+0.000791j
[2025-08-24 08:08:15] [Iter 2929/4650] R4[678/2400], Temp: 0.8157, Energy: -28.742289-0.000826j
[2025-08-24 08:08:25] [Iter 2930/4650] R4[679/2400], Temp: 0.8152, Energy: -28.750973+0.000842j
[2025-08-24 08:08:35] [Iter 2931/4650] R4[680/2400], Temp: 0.8147, Energy: -28.745071+0.000021j
[2025-08-24 08:08:45] [Iter 2932/4650] R4[681/2400], Temp: 0.8142, Energy: -28.751560+0.000942j
[2025-08-24 08:08:55] [Iter 2933/4650] R4[682/2400], Temp: 0.8136, Energy: -28.750637+0.002651j
[2025-08-24 08:09:05] [Iter 2934/4650] R4[683/2400], Temp: 0.8131, Energy: -28.748746-0.004470j
[2025-08-24 08:09:16] [Iter 2935/4650] R4[684/2400], Temp: 0.8126, Energy: -28.743006+0.001161j
[2025-08-24 08:09:26] [Iter 2936/4650] R4[685/2400], Temp: 0.8121, Energy: -28.749458-0.001225j
[2025-08-24 08:09:36] [Iter 2937/4650] R4[686/2400], Temp: 0.8116, Energy: -28.751146+0.001669j
[2025-08-24 08:09:46] [Iter 2938/4650] R4[687/2400], Temp: 0.8111, Energy: -28.750735+0.000330j
[2025-08-24 08:09:56] [Iter 2939/4650] R4[688/2400], Temp: 0.8106, Energy: -28.744863+0.002375j
[2025-08-24 08:10:06] [Iter 2940/4650] R4[689/2400], Temp: 0.8101, Energy: -28.739990+0.000631j
[2025-08-24 08:10:16] [Iter 2941/4650] R4[690/2400], Temp: 0.8095, Energy: -28.743338-0.003452j
[2025-08-24 08:10:27] [Iter 2942/4650] R4[691/2400], Temp: 0.8090, Energy: -28.747399+0.002068j
[2025-08-24 08:10:37] [Iter 2943/4650] R4[692/2400], Temp: 0.8085, Energy: -28.755815+0.001163j
[2025-08-24 08:10:47] [Iter 2944/4650] R4[693/2400], Temp: 0.8080, Energy: -28.751504+0.001277j
[2025-08-24 08:10:57] [Iter 2945/4650] R4[694/2400], Temp: 0.8075, Energy: -28.754127-0.001012j
[2025-08-24 08:11:07] [Iter 2946/4650] R4[695/2400], Temp: 0.8070, Energy: -28.754825+0.001296j
[2025-08-24 08:11:17] [Iter 2947/4650] R4[696/2400], Temp: 0.8065, Energy: -28.749421-0.000054j
[2025-08-24 08:11:27] [Iter 2948/4650] R4[697/2400], Temp: 0.8059, Energy: -28.753547-0.001176j
[2025-08-24 08:11:38] [Iter 2949/4650] R4[698/2400], Temp: 0.8054, Energy: -28.751047+0.000077j
[2025-08-24 08:11:48] [Iter 2950/4650] R4[699/2400], Temp: 0.8049, Energy: -28.747161+0.000838j
[2025-08-24 08:11:58] [Iter 2951/4650] R4[700/2400], Temp: 0.8044, Energy: -28.746368+0.001456j
[2025-08-24 08:12:08] [Iter 2952/4650] R4[701/2400], Temp: 0.8039, Energy: -28.748095+0.000605j
[2025-08-24 08:12:18] [Iter 2953/4650] R4[702/2400], Temp: 0.8033, Energy: -28.753170+0.000265j
[2025-08-24 08:12:28] [Iter 2954/4650] R4[703/2400], Temp: 0.8028, Energy: -28.751658+0.001550j
[2025-08-24 08:12:38] [Iter 2955/4650] R4[704/2400], Temp: 0.8023, Energy: -28.752809-0.000406j
[2025-08-24 08:12:49] [Iter 2956/4650] R4[705/2400], Temp: 0.8018, Energy: -28.754478-0.001968j
[2025-08-24 08:12:59] [Iter 2957/4650] R4[706/2400], Temp: 0.8013, Energy: -28.750519+0.000464j
[2025-08-24 08:13:09] [Iter 2958/4650] R4[707/2400], Temp: 0.8007, Energy: -28.748327-0.000115j
[2025-08-24 08:13:19] [Iter 2959/4650] R4[708/2400], Temp: 0.8002, Energy: -28.749840-0.001375j
[2025-08-24 08:13:29] [Iter 2960/4650] R4[709/2400], Temp: 0.7997, Energy: -28.748091+0.000655j
[2025-08-24 08:13:39] [Iter 2961/4650] R4[710/2400], Temp: 0.7992, Energy: -28.751557-0.001716j
[2025-08-24 08:13:49] [Iter 2962/4650] R4[711/2400], Temp: 0.7986, Energy: -28.749602-0.001202j
[2025-08-24 08:14:00] [Iter 2963/4650] R4[712/2400], Temp: 0.7981, Energy: -28.748916+0.001455j
[2025-08-24 08:14:10] [Iter 2964/4650] R4[713/2400], Temp: 0.7976, Energy: -28.748104+0.001408j
[2025-08-24 08:14:20] [Iter 2965/4650] R4[714/2400], Temp: 0.7971, Energy: -28.749385-0.001259j
[2025-08-24 08:14:30] [Iter 2966/4650] R4[715/2400], Temp: 0.7965, Energy: -28.744466-0.000704j
[2025-08-24 08:14:40] [Iter 2967/4650] R4[716/2400], Temp: 0.7960, Energy: -28.751060-0.001297j
[2025-08-24 08:14:50] [Iter 2968/4650] R4[717/2400], Temp: 0.7955, Energy: -28.741227-0.000672j
[2025-08-24 08:15:00] [Iter 2969/4650] R4[718/2400], Temp: 0.7950, Energy: -28.749337-0.000014j
[2025-08-24 08:15:11] [Iter 2970/4650] R4[719/2400], Temp: 0.7944, Energy: -28.750374-0.001307j
[2025-08-24 08:15:21] [Iter 2971/4650] R4[720/2400], Temp: 0.7939, Energy: -28.740780+0.000910j
[2025-08-24 08:15:31] [Iter 2972/4650] R4[721/2400], Temp: 0.7934, Energy: -28.752162-0.000651j
[2025-08-24 08:15:41] [Iter 2973/4650] R4[722/2400], Temp: 0.7928, Energy: -28.756210+0.001852j
[2025-08-24 08:15:51] [Iter 2974/4650] R4[723/2400], Temp: 0.7923, Energy: -28.744210-0.001069j
[2025-08-24 08:16:01] [Iter 2975/4650] R4[724/2400], Temp: 0.7918, Energy: -28.751191+0.000327j
[2025-08-24 08:16:11] [Iter 2976/4650] R4[725/2400], Temp: 0.7912, Energy: -28.746255-0.000194j
[2025-08-24 08:16:22] [Iter 2977/4650] R4[726/2400], Temp: 0.7907, Energy: -28.745997+0.001098j
[2025-08-24 08:16:32] [Iter 2978/4650] R4[727/2400], Temp: 0.7902, Energy: -28.752292-0.000417j
[2025-08-24 08:16:42] [Iter 2979/4650] R4[728/2400], Temp: 0.7896, Energy: -28.747725-0.002694j
[2025-08-24 08:16:52] [Iter 2980/4650] R4[729/2400], Temp: 0.7891, Energy: -28.748119-0.000971j
[2025-08-24 08:17:02] [Iter 2981/4650] R4[730/2400], Temp: 0.7886, Energy: -28.747843+0.002687j
[2025-08-24 08:17:12] [Iter 2982/4650] R4[731/2400], Temp: 0.7880, Energy: -28.746189-0.002367j
[2025-08-24 08:17:22] [Iter 2983/4650] R4[732/2400], Temp: 0.7875, Energy: -28.744484-0.000265j
[2025-08-24 08:17:33] [Iter 2984/4650] R4[733/2400], Temp: 0.7870, Energy: -28.753561-0.001538j
[2025-08-24 08:17:43] [Iter 2985/4650] R4[734/2400], Temp: 0.7864, Energy: -28.748206+0.000220j
[2025-08-24 08:17:53] [Iter 2986/4650] R4[735/2400], Temp: 0.7859, Energy: -28.754953-0.001356j
[2025-08-24 08:18:03] [Iter 2987/4650] R4[736/2400], Temp: 0.7854, Energy: -28.741294+0.000022j
[2025-08-24 08:18:13] [Iter 2988/4650] R4[737/2400], Temp: 0.7848, Energy: -28.744748-0.000934j
[2025-08-24 08:18:23] [Iter 2989/4650] R4[738/2400], Temp: 0.7843, Energy: -28.747150-0.000104j
[2025-08-24 08:18:33] [Iter 2990/4650] R4[739/2400], Temp: 0.7837, Energy: -28.751342-0.001592j
[2025-08-24 08:18:44] [Iter 2991/4650] R4[740/2400], Temp: 0.7832, Energy: -28.750780+0.000376j
[2025-08-24 08:18:54] [Iter 2992/4650] R4[741/2400], Temp: 0.7827, Energy: -28.746310+0.001064j
[2025-08-24 08:19:04] [Iter 2993/4650] R4[742/2400], Temp: 0.7821, Energy: -28.753165+0.002437j
[2025-08-24 08:19:14] [Iter 2994/4650] R4[743/2400], Temp: 0.7816, Energy: -28.748402-0.001634j
[2025-08-24 08:19:24] [Iter 2995/4650] R4[744/2400], Temp: 0.7810, Energy: -28.749598-0.001135j
[2025-08-24 08:19:34] [Iter 2996/4650] R4[745/2400], Temp: 0.7805, Energy: -28.754337+0.000226j
[2025-08-24 08:19:44] [Iter 2997/4650] R4[746/2400], Temp: 0.7800, Energy: -28.748826+0.002884j
[2025-08-24 08:19:55] [Iter 2998/4650] R4[747/2400], Temp: 0.7794, Energy: -28.743354+0.001396j
[2025-08-24 08:20:05] [Iter 2999/4650] R4[748/2400], Temp: 0.7789, Energy: -28.747717+0.000117j
[2025-08-24 08:20:15] [Iter 3000/4650] R4[749/2400], Temp: 0.7783, Energy: -28.751514-0.000057j
[2025-08-24 08:20:15] ✓ Checkpoint saved: checkpoint_iter_003000.pkl
[2025-08-24 08:20:25] [Iter 3001/4650] R4[750/2400], Temp: 0.7778, Energy: -28.751308-0.001254j
[2025-08-24 08:20:35] [Iter 3002/4650] R4[751/2400], Temp: 0.7772, Energy: -28.748500-0.000638j
[2025-08-24 08:20:45] [Iter 3003/4650] R4[752/2400], Temp: 0.7767, Energy: -28.751691+0.001738j
[2025-08-24 08:20:55] [Iter 3004/4650] R4[753/2400], Temp: 0.7762, Energy: -28.748578+0.000472j
[2025-08-24 08:21:05] [Iter 3005/4650] R4[754/2400], Temp: 0.7756, Energy: -28.751235-0.001272j
[2025-08-24 08:21:16] [Iter 3006/4650] R4[755/2400], Temp: 0.7751, Energy: -28.748116+0.000937j
[2025-08-24 08:21:26] [Iter 3007/4650] R4[756/2400], Temp: 0.7745, Energy: -28.749294+0.000419j
[2025-08-24 08:21:36] [Iter 3008/4650] R4[757/2400], Temp: 0.7740, Energy: -28.750150-0.000262j
[2025-08-24 08:21:46] [Iter 3009/4650] R4[758/2400], Temp: 0.7734, Energy: -28.750744+0.002945j
[2025-08-24 08:21:56] [Iter 3010/4650] R4[759/2400], Temp: 0.7729, Energy: -28.752767-0.000172j
[2025-08-24 08:22:06] [Iter 3011/4650] R4[760/2400], Temp: 0.7723, Energy: -28.748141+0.005119j
[2025-08-24 08:22:16] [Iter 3012/4650] R4[761/2400], Temp: 0.7718, Energy: -28.746542+0.000119j
[2025-08-24 08:22:27] [Iter 3013/4650] R4[762/2400], Temp: 0.7712, Energy: -28.745449-0.000623j
[2025-08-24 08:22:37] [Iter 3014/4650] R4[763/2400], Temp: 0.7707, Energy: -28.747679+0.002439j
[2025-08-24 08:22:47] [Iter 3015/4650] R4[764/2400], Temp: 0.7701, Energy: -28.741004+0.000795j
[2025-08-24 08:22:57] [Iter 3016/4650] R4[765/2400], Temp: 0.7696, Energy: -28.744166+0.000052j
[2025-08-24 08:23:07] [Iter 3017/4650] R4[766/2400], Temp: 0.7690, Energy: -28.749205+0.000885j
[2025-08-24 08:23:17] [Iter 3018/4650] R4[767/2400], Temp: 0.7685, Energy: -28.743146-0.000114j
[2025-08-24 08:23:27] [Iter 3019/4650] R4[768/2400], Temp: 0.7679, Energy: -28.751311-0.004463j
[2025-08-24 08:23:38] [Iter 3020/4650] R4[769/2400], Temp: 0.7674, Energy: -28.748422-0.000833j
[2025-08-24 08:23:48] [Iter 3021/4650] R4[770/2400], Temp: 0.7668, Energy: -28.750123+0.000025j
[2025-08-24 08:23:58] [Iter 3022/4650] R4[771/2400], Temp: 0.7663, Energy: -28.738558-0.000872j
[2025-08-24 08:24:08] [Iter 3023/4650] R4[772/2400], Temp: 0.7657, Energy: -28.742887-0.000115j
[2025-08-24 08:24:18] [Iter 3024/4650] R4[773/2400], Temp: 0.7651, Energy: -28.748232-0.001546j
[2025-08-24 08:24:28] [Iter 3025/4650] R4[774/2400], Temp: 0.7646, Energy: -28.747302+0.000373j
[2025-08-24 08:24:38] [Iter 3026/4650] R4[775/2400], Temp: 0.7640, Energy: -28.753456-0.000720j
[2025-08-24 08:24:49] [Iter 3027/4650] R4[776/2400], Temp: 0.7635, Energy: -28.747039-0.001018j
[2025-08-24 08:24:59] [Iter 3028/4650] R4[777/2400], Temp: 0.7629, Energy: -28.750970-0.000426j
[2025-08-24 08:25:09] [Iter 3029/4650] R4[778/2400], Temp: 0.7624, Energy: -28.746907-0.000030j
[2025-08-24 08:25:19] [Iter 3030/4650] R4[779/2400], Temp: 0.7618, Energy: -28.748731-0.000911j
[2025-08-24 08:25:29] [Iter 3031/4650] R4[780/2400], Temp: 0.7612, Energy: -28.750875-0.001486j
[2025-08-24 08:25:39] [Iter 3032/4650] R4[781/2400], Temp: 0.7607, Energy: -28.750931+0.000151j
[2025-08-24 08:25:49] [Iter 3033/4650] R4[782/2400], Temp: 0.7601, Energy: -28.748220-0.001714j
[2025-08-24 08:26:00] [Iter 3034/4650] R4[783/2400], Temp: 0.7596, Energy: -28.746664+0.000868j
[2025-08-24 08:26:10] [Iter 3035/4650] R4[784/2400], Temp: 0.7590, Energy: -28.746206-0.001969j
[2025-08-24 08:26:20] [Iter 3036/4650] R4[785/2400], Temp: 0.7585, Energy: -28.745646-0.002118j
[2025-08-24 08:26:30] [Iter 3037/4650] R4[786/2400], Temp: 0.7579, Energy: -28.757064-0.001600j
[2025-08-24 08:26:40] [Iter 3038/4650] R4[787/2400], Temp: 0.7573, Energy: -28.743264+0.001653j
[2025-08-24 08:26:50] [Iter 3039/4650] R4[788/2400], Temp: 0.7568, Energy: -28.750369+0.001116j
[2025-08-24 08:27:00] [Iter 3040/4650] R4[789/2400], Temp: 0.7562, Energy: -28.750977-0.000716j
[2025-08-24 08:27:11] [Iter 3041/4650] R4[790/2400], Temp: 0.7556, Energy: -28.753461+0.001733j
[2025-08-24 08:27:21] [Iter 3042/4650] R4[791/2400], Temp: 0.7551, Energy: -28.752801+0.000465j
[2025-08-24 08:27:31] [Iter 3043/4650] R4[792/2400], Temp: 0.7545, Energy: -28.750701+0.003288j
[2025-08-24 08:27:41] [Iter 3044/4650] R4[793/2400], Temp: 0.7540, Energy: -28.745897-0.000757j
[2025-08-24 08:27:51] [Iter 3045/4650] R4[794/2400], Temp: 0.7534, Energy: -28.754363+0.000259j
[2025-08-24 08:28:01] [Iter 3046/4650] R4[795/2400], Temp: 0.7528, Energy: -28.748687-0.000585j
[2025-08-24 08:28:11] [Iter 3047/4650] R4[796/2400], Temp: 0.7523, Energy: -28.744280+0.000515j
[2025-08-24 08:28:22] [Iter 3048/4650] R4[797/2400], Temp: 0.7517, Energy: -28.755765-0.000273j
[2025-08-24 08:28:32] [Iter 3049/4650] R4[798/2400], Temp: 0.7511, Energy: -28.748706-0.000829j
[2025-08-24 08:28:42] [Iter 3050/4650] R4[799/2400], Temp: 0.7506, Energy: -28.747288+0.000902j
[2025-08-24 08:28:52] [Iter 3051/4650] R4[800/2400], Temp: 0.7500, Energy: -28.748326+0.001823j
[2025-08-24 08:29:02] [Iter 3052/4650] R4[801/2400], Temp: 0.7494, Energy: -28.750039-0.005691j
[2025-08-24 08:29:12] [Iter 3053/4650] R4[802/2400], Temp: 0.7489, Energy: -28.754309-0.002207j
[2025-08-24 08:29:22] [Iter 3054/4650] R4[803/2400], Temp: 0.7483, Energy: -28.745819-0.001064j
[2025-08-24 08:29:33] [Iter 3055/4650] R4[804/2400], Temp: 0.7477, Energy: -28.751530+0.002216j
[2025-08-24 08:29:43] [Iter 3056/4650] R4[805/2400], Temp: 0.7472, Energy: -28.746614-0.003093j
[2025-08-24 08:29:53] [Iter 3057/4650] R4[806/2400], Temp: 0.7466, Energy: -28.752935-0.000812j
[2025-08-24 08:30:03] [Iter 3058/4650] R4[807/2400], Temp: 0.7460, Energy: -28.751844-0.002304j
[2025-08-24 08:30:13] [Iter 3059/4650] R4[808/2400], Temp: 0.7455, Energy: -28.750418-0.000918j
[2025-08-24 08:30:23] [Iter 3060/4650] R4[809/2400], Temp: 0.7449, Energy: -28.752751-0.000381j
[2025-08-24 08:30:33] [Iter 3061/4650] R4[810/2400], Temp: 0.7443, Energy: -28.746330+0.001615j
[2025-08-24 08:30:43] [Iter 3062/4650] R4[811/2400], Temp: 0.7437, Energy: -28.755760+0.001564j
[2025-08-24 08:30:54] [Iter 3063/4650] R4[812/2400], Temp: 0.7432, Energy: -28.752764-0.000677j
[2025-08-24 08:31:04] [Iter 3064/4650] R4[813/2400], Temp: 0.7426, Energy: -28.746526-0.001847j
[2025-08-24 08:31:14] [Iter 3065/4650] R4[814/2400], Temp: 0.7420, Energy: -28.753027+0.000008j
[2025-08-24 08:31:24] [Iter 3066/4650] R4[815/2400], Temp: 0.7415, Energy: -28.748622+0.000191j
[2025-08-24 08:31:34] [Iter 3067/4650] R4[816/2400], Temp: 0.7409, Energy: -28.753804+0.000982j
[2025-08-24 08:31:44] [Iter 3068/4650] R4[817/2400], Temp: 0.7403, Energy: -28.754211+0.002893j
[2025-08-24 08:31:54] [Iter 3069/4650] R4[818/2400], Temp: 0.7397, Energy: -28.747720-0.000149j
[2025-08-24 08:32:05] [Iter 3070/4650] R4[819/2400], Temp: 0.7392, Energy: -28.750937+0.002702j
[2025-08-24 08:32:15] [Iter 3071/4650] R4[820/2400], Temp: 0.7386, Energy: -28.750356+0.001099j
[2025-08-24 08:32:25] [Iter 3072/4650] R4[821/2400], Temp: 0.7380, Energy: -28.753416+0.001104j
[2025-08-24 08:32:35] [Iter 3073/4650] R4[822/2400], Temp: 0.7374, Energy: -28.747462-0.001144j
[2025-08-24 08:32:45] [Iter 3074/4650] R4[823/2400], Temp: 0.7369, Energy: -28.748338+0.000420j
[2025-08-24 08:32:55] [Iter 3075/4650] R4[824/2400], Temp: 0.7363, Energy: -28.747193+0.001063j
[2025-08-24 08:33:05] [Iter 3076/4650] R4[825/2400], Temp: 0.7357, Energy: -28.754544-0.001103j
[2025-08-24 08:33:16] [Iter 3077/4650] R4[826/2400], Temp: 0.7351, Energy: -28.750829-0.000532j
[2025-08-24 08:33:26] [Iter 3078/4650] R4[827/2400], Temp: 0.7345, Energy: -28.757362+0.002437j
[2025-08-24 08:33:36] [Iter 3079/4650] R4[828/2400], Temp: 0.7340, Energy: -28.745337-0.001093j
[2025-08-24 08:33:46] [Iter 3080/4650] R4[829/2400], Temp: 0.7334, Energy: -28.747309-0.000595j
[2025-08-24 08:33:56] [Iter 3081/4650] R4[830/2400], Temp: 0.7328, Energy: -28.741376-0.001492j
[2025-08-24 08:34:06] [Iter 3082/4650] R4[831/2400], Temp: 0.7322, Energy: -28.749785+0.000439j
[2025-08-24 08:34:16] [Iter 3083/4650] R4[832/2400], Temp: 0.7316, Energy: -28.752187+0.000116j
[2025-08-24 08:34:27] [Iter 3084/4650] R4[833/2400], Temp: 0.7311, Energy: -28.749292+0.001300j
[2025-08-24 08:34:37] [Iter 3085/4650] R4[834/2400], Temp: 0.7305, Energy: -28.746989+0.000473j
[2025-08-24 08:34:47] [Iter 3086/4650] R4[835/2400], Temp: 0.7299, Energy: -28.744299-0.000169j
[2025-08-24 08:34:57] [Iter 3087/4650] R4[836/2400], Temp: 0.7293, Energy: -28.753159-0.002195j
[2025-08-24 08:35:07] [Iter 3088/4650] R4[837/2400], Temp: 0.7287, Energy: -28.748966+0.003463j
[2025-08-24 08:35:17] [Iter 3089/4650] R4[838/2400], Temp: 0.7282, Energy: -28.752342-0.001615j
[2025-08-24 08:35:27] [Iter 3090/4650] R4[839/2400], Temp: 0.7276, Energy: -28.750678+0.000976j
[2025-08-24 08:35:37] [Iter 3091/4650] R4[840/2400], Temp: 0.7270, Energy: -28.749471-0.000342j
[2025-08-24 08:35:48] [Iter 3092/4650] R4[841/2400], Temp: 0.7264, Energy: -28.745620-0.001228j
[2025-08-24 08:35:58] [Iter 3093/4650] R4[842/2400], Temp: 0.7258, Energy: -28.747272+0.000980j
[2025-08-24 08:36:08] [Iter 3094/4650] R4[843/2400], Temp: 0.7252, Energy: -28.749531+0.001689j
[2025-08-24 08:36:18] [Iter 3095/4650] R4[844/2400], Temp: 0.7247, Energy: -28.747484+0.002011j
[2025-08-24 08:36:28] [Iter 3096/4650] R4[845/2400], Temp: 0.7241, Energy: -28.750433-0.000138j
[2025-08-24 08:36:38] [Iter 3097/4650] R4[846/2400], Temp: 0.7235, Energy: -28.749737-0.002797j
[2025-08-24 08:36:48] [Iter 3098/4650] R4[847/2400], Temp: 0.7229, Energy: -28.749703-0.001353j
[2025-08-24 08:36:59] [Iter 3099/4650] R4[848/2400], Temp: 0.7223, Energy: -28.747628+0.001745j
[2025-08-24 08:37:09] [Iter 3100/4650] R4[849/2400], Temp: 0.7217, Energy: -28.752625+0.000979j
[2025-08-24 08:37:19] [Iter 3101/4650] R4[850/2400], Temp: 0.7211, Energy: -28.754274+0.000408j
[2025-08-24 08:37:29] [Iter 3102/4650] R4[851/2400], Temp: 0.7206, Energy: -28.748232-0.001905j
[2025-08-24 08:37:39] [Iter 3103/4650] R4[852/2400], Temp: 0.7200, Energy: -28.749091+0.000321j
[2025-08-24 08:37:49] [Iter 3104/4650] R4[853/2400], Temp: 0.7194, Energy: -28.748447-0.000725j
[2025-08-24 08:37:59] [Iter 3105/4650] R4[854/2400], Temp: 0.7188, Energy: -28.749561+0.000326j
[2025-08-24 08:38:10] [Iter 3106/4650] R4[855/2400], Temp: 0.7182, Energy: -28.748825-0.000187j
[2025-08-24 08:38:20] [Iter 3107/4650] R4[856/2400], Temp: 0.7176, Energy: -28.751921+0.001333j
[2025-08-24 08:38:30] [Iter 3108/4650] R4[857/2400], Temp: 0.7170, Energy: -28.750963-0.000963j
[2025-08-24 08:38:40] [Iter 3109/4650] R4[858/2400], Temp: 0.7164, Energy: -28.752806+0.000233j
[2025-08-24 08:38:50] [Iter 3110/4650] R4[859/2400], Temp: 0.7158, Energy: -28.749644-0.000739j
[2025-08-24 08:39:00] [Iter 3111/4650] R4[860/2400], Temp: 0.7153, Energy: -28.749553+0.001731j
[2025-08-24 08:39:10] [Iter 3112/4650] R4[861/2400], Temp: 0.7147, Energy: -28.750766+0.000561j
[2025-08-24 08:39:21] [Iter 3113/4650] R4[862/2400], Temp: 0.7141, Energy: -28.747697+0.001235j
[2025-08-24 08:39:31] [Iter 3114/4650] R4[863/2400], Temp: 0.7135, Energy: -28.753409+0.000849j
[2025-08-24 08:39:41] [Iter 3115/4650] R4[864/2400], Temp: 0.7129, Energy: -28.754636-0.000833j
[2025-08-24 08:39:51] [Iter 3116/4650] R4[865/2400], Temp: 0.7123, Energy: -28.752096-0.002713j
[2025-08-24 08:40:01] [Iter 3117/4650] R4[866/2400], Temp: 0.7117, Energy: -28.754137-0.002202j
[2025-08-24 08:40:11] [Iter 3118/4650] R4[867/2400], Temp: 0.7111, Energy: -28.747442-0.000591j
[2025-08-24 08:40:21] [Iter 3119/4650] R4[868/2400], Temp: 0.7105, Energy: -28.747442-0.000244j
[2025-08-24 08:40:32] [Iter 3120/4650] R4[869/2400], Temp: 0.7099, Energy: -28.751954-0.001347j
[2025-08-24 08:40:42] [Iter 3121/4650] R4[870/2400], Temp: 0.7093, Energy: -28.749150-0.000066j
[2025-08-24 08:40:52] [Iter 3122/4650] R4[871/2400], Temp: 0.7087, Energy: -28.745778-0.000788j
[2025-08-24 08:41:02] [Iter 3123/4650] R4[872/2400], Temp: 0.7081, Energy: -28.750412-0.000165j
[2025-08-24 08:41:12] [Iter 3124/4650] R4[873/2400], Temp: 0.7075, Energy: -28.752099-0.000733j
[2025-08-24 08:41:22] [Iter 3125/4650] R4[874/2400], Temp: 0.7069, Energy: -28.749651-0.001277j
[2025-08-24 08:41:32] [Iter 3126/4650] R4[875/2400], Temp: 0.7064, Energy: -28.750298+0.001158j
[2025-08-24 08:41:43] [Iter 3127/4650] R4[876/2400], Temp: 0.7058, Energy: -28.749720+0.000546j
[2025-08-24 08:41:53] [Iter 3128/4650] R4[877/2400], Temp: 0.7052, Energy: -28.751729+0.000924j
[2025-08-24 08:42:03] [Iter 3129/4650] R4[878/2400], Temp: 0.7046, Energy: -28.750070-0.001853j
[2025-08-24 08:42:13] [Iter 3130/4650] R4[879/2400], Temp: 0.7040, Energy: -28.748648-0.000357j
[2025-08-24 08:42:23] [Iter 3131/4650] R4[880/2400], Temp: 0.7034, Energy: -28.747659+0.000251j
[2025-08-24 08:42:33] [Iter 3132/4650] R4[881/2400], Temp: 0.7028, Energy: -28.745896-0.000528j
[2025-08-24 08:42:44] [Iter 3133/4650] R4[882/2400], Temp: 0.7022, Energy: -28.746940+0.001729j
[2025-08-24 08:42:54] [Iter 3134/4650] R4[883/2400], Temp: 0.7016, Energy: -28.748464-0.001225j
[2025-08-24 08:43:04] [Iter 3135/4650] R4[884/2400], Temp: 0.7010, Energy: -28.747114-0.001768j
[2025-08-24 08:43:14] [Iter 3136/4650] R4[885/2400], Temp: 0.7004, Energy: -28.753968-0.000931j
[2025-08-24 08:43:24] [Iter 3137/4650] R4[886/2400], Temp: 0.6998, Energy: -28.754594-0.000625j
[2025-08-24 08:43:34] [Iter 3138/4650] R4[887/2400], Temp: 0.6992, Energy: -28.751313+0.000546j
[2025-08-24 08:43:44] [Iter 3139/4650] R4[888/2400], Temp: 0.6986, Energy: -28.745129-0.000253j
[2025-08-24 08:43:54] [Iter 3140/4650] R4[889/2400], Temp: 0.6980, Energy: -28.752731-0.002362j
[2025-08-24 08:44:05] [Iter 3141/4650] R4[890/2400], Temp: 0.6974, Energy: -28.748889+0.001605j
[2025-08-24 08:44:15] [Iter 3142/4650] R4[891/2400], Temp: 0.6968, Energy: -28.749948-0.002196j
[2025-08-24 08:44:25] [Iter 3143/4650] R4[892/2400], Temp: 0.6962, Energy: -28.748450+0.001820j
[2025-08-24 08:44:35] [Iter 3144/4650] R4[893/2400], Temp: 0.6956, Energy: -28.747020+0.003279j
[2025-08-24 08:44:45] [Iter 3145/4650] R4[894/2400], Temp: 0.6950, Energy: -28.753694+0.001111j
[2025-08-24 08:44:55] [Iter 3146/4650] R4[895/2400], Temp: 0.6944, Energy: -28.750985+0.001944j
[2025-08-24 08:45:05] [Iter 3147/4650] R4[896/2400], Temp: 0.6938, Energy: -28.749732+0.000123j
[2025-08-24 08:45:16] [Iter 3148/4650] R4[897/2400], Temp: 0.6932, Energy: -28.751159-0.000233j
[2025-08-24 08:45:26] [Iter 3149/4650] R4[898/2400], Temp: 0.6926, Energy: -28.753514+0.001465j
[2025-08-24 08:45:36] [Iter 3150/4650] R4[899/2400], Temp: 0.6919, Energy: -28.748781+0.002185j
[2025-08-24 08:45:46] [Iter 3151/4650] R4[900/2400], Temp: 0.6913, Energy: -28.752452+0.003290j
[2025-08-24 08:45:56] [Iter 3152/4650] R4[901/2400], Temp: 0.6907, Energy: -28.749070+0.001608j
[2025-08-24 08:46:06] [Iter 3153/4650] R4[902/2400], Temp: 0.6901, Energy: -28.749561-0.000956j
[2025-08-24 08:46:17] [Iter 3154/4650] R4[903/2400], Temp: 0.6895, Energy: -28.745882+0.001040j
[2025-08-24 08:46:27] [Iter 3155/4650] R4[904/2400], Temp: 0.6889, Energy: -28.756128+0.001582j
[2025-08-24 08:46:37] [Iter 3156/4650] R4[905/2400], Temp: 0.6883, Energy: -28.749632-0.002453j
[2025-08-24 08:46:47] [Iter 3157/4650] R4[906/2400], Temp: 0.6877, Energy: -28.753255+0.000440j
[2025-08-24 08:46:57] [Iter 3158/4650] R4[907/2400], Temp: 0.6871, Energy: -28.753067-0.001182j
[2025-08-24 08:47:07] [Iter 3159/4650] R4[908/2400], Temp: 0.6865, Energy: -28.751631-0.001229j
[2025-08-24 08:47:17] [Iter 3160/4650] R4[909/2400], Temp: 0.6859, Energy: -28.751441+0.000417j
[2025-08-24 08:47:27] [Iter 3161/4650] R4[910/2400], Temp: 0.6853, Energy: -28.754898-0.000573j
[2025-08-24 08:47:38] [Iter 3162/4650] R4[911/2400], Temp: 0.6847, Energy: -28.753184+0.000232j
[2025-08-24 08:47:48] [Iter 3163/4650] R4[912/2400], Temp: 0.6841, Energy: -28.758065+0.001336j
[2025-08-24 08:47:58] [Iter 3164/4650] R4[913/2400], Temp: 0.6835, Energy: -28.749340+0.002699j
[2025-08-24 08:48:08] [Iter 3165/4650] R4[914/2400], Temp: 0.6828, Energy: -28.749866-0.002817j
[2025-08-24 08:48:18] [Iter 3166/4650] R4[915/2400], Temp: 0.6822, Energy: -28.750188+0.001404j
[2025-08-24 08:48:28] [Iter 3167/4650] R4[916/2400], Temp: 0.6816, Energy: -28.753050-0.002097j
[2025-08-24 08:48:38] [Iter 3168/4650] R4[917/2400], Temp: 0.6810, Energy: -28.750595-0.000868j
[2025-08-24 08:48:49] [Iter 3169/4650] R4[918/2400], Temp: 0.6804, Energy: -28.748071-0.000351j
[2025-08-24 08:48:59] [Iter 3170/4650] R4[919/2400], Temp: 0.6798, Energy: -28.757813+0.001076j
[2025-08-24 08:49:09] [Iter 3171/4650] R4[920/2400], Temp: 0.6792, Energy: -28.748746-0.002853j
[2025-08-24 08:49:19] [Iter 3172/4650] R4[921/2400], Temp: 0.6786, Energy: -28.749718+0.000287j
[2025-08-24 08:49:29] [Iter 3173/4650] R4[922/2400], Temp: 0.6780, Energy: -28.751078+0.000879j
[2025-08-24 08:49:39] [Iter 3174/4650] R4[923/2400], Temp: 0.6773, Energy: -28.756115+0.001260j
[2025-08-24 08:49:49] [Iter 3175/4650] R4[924/2400], Temp: 0.6767, Energy: -28.749094+0.001398j
[2025-08-24 08:50:00] [Iter 3176/4650] R4[925/2400], Temp: 0.6761, Energy: -28.742574-0.001296j
[2025-08-24 08:50:10] [Iter 3177/4650] R4[926/2400], Temp: 0.6755, Energy: -28.747634-0.000536j
[2025-08-24 08:50:20] [Iter 3178/4650] R4[927/2400], Temp: 0.6749, Energy: -28.758373-0.001914j
[2025-08-24 08:50:30] [Iter 3179/4650] R4[928/2400], Temp: 0.6743, Energy: -28.751259-0.000404j
[2025-08-24 08:50:40] [Iter 3180/4650] R4[929/2400], Temp: 0.6737, Energy: -28.753745+0.002421j
[2025-08-24 08:50:50] [Iter 3181/4650] R4[930/2400], Temp: 0.6731, Energy: -28.755251-0.000979j
[2025-08-24 08:51:00] [Iter 3182/4650] R4[931/2400], Temp: 0.6724, Energy: -28.744715+0.001147j
[2025-08-24 08:51:10] [Iter 3183/4650] R4[932/2400], Temp: 0.6718, Energy: -28.749668+0.001537j
[2025-08-24 08:51:21] [Iter 3184/4650] R4[933/2400], Temp: 0.6712, Energy: -28.750754-0.001878j
[2025-08-24 08:51:31] [Iter 3185/4650] R4[934/2400], Temp: 0.6706, Energy: -28.744003+0.000620j
[2025-08-24 08:51:41] [Iter 3186/4650] R4[935/2400], Temp: 0.6700, Energy: -28.745050+0.000103j
[2025-08-24 08:51:51] [Iter 3187/4650] R4[936/2400], Temp: 0.6694, Energy: -28.756677-0.001689j
[2025-08-24 08:52:01] [Iter 3188/4650] R4[937/2400], Temp: 0.6688, Energy: -28.756041+0.000505j
[2025-08-24 08:52:11] [Iter 3189/4650] R4[938/2400], Temp: 0.6681, Energy: -28.750942-0.000683j
[2025-08-24 08:52:21] [Iter 3190/4650] R4[939/2400], Temp: 0.6675, Energy: -28.748751+0.002109j
[2025-08-24 08:52:32] [Iter 3191/4650] R4[940/2400], Temp: 0.6669, Energy: -28.755025-0.002258j
[2025-08-24 08:52:42] [Iter 3192/4650] R4[941/2400], Temp: 0.6663, Energy: -28.749658-0.001460j
[2025-08-24 08:52:52] [Iter 3193/4650] R4[942/2400], Temp: 0.6657, Energy: -28.754462-0.000157j
[2025-08-24 08:53:02] [Iter 3194/4650] R4[943/2400], Temp: 0.6651, Energy: -28.753805-0.001334j
[2025-08-24 08:53:12] [Iter 3195/4650] R4[944/2400], Temp: 0.6644, Energy: -28.749773-0.000487j
[2025-08-24 08:53:22] [Iter 3196/4650] R4[945/2400], Temp: 0.6638, Energy: -28.745395+0.003003j
[2025-08-24 08:53:32] [Iter 3197/4650] R4[946/2400], Temp: 0.6632, Energy: -28.747529-0.000999j
[2025-08-24 08:53:43] [Iter 3198/4650] R4[947/2400], Temp: 0.6626, Energy: -28.753637-0.001540j
[2025-08-24 08:53:53] [Iter 3199/4650] R4[948/2400], Temp: 0.6620, Energy: -28.748367+0.003646j
[2025-08-24 08:54:03] [Iter 3200/4650] R4[949/2400], Temp: 0.6613, Energy: -28.748174-0.001012j
[2025-08-24 08:54:13] [Iter 3201/4650] R4[950/2400], Temp: 0.6607, Energy: -28.749454-0.000827j
[2025-08-24 08:54:23] [Iter 3202/4650] R4[951/2400], Temp: 0.6601, Energy: -28.750492-0.002234j
[2025-08-24 08:54:33] [Iter 3203/4650] R4[952/2400], Temp: 0.6595, Energy: -28.745417+0.002628j
[2025-08-24 08:54:43] [Iter 3204/4650] R4[953/2400], Temp: 0.6589, Energy: -28.748483-0.002089j
[2025-08-24 08:54:54] [Iter 3205/4650] R4[954/2400], Temp: 0.6582, Energy: -28.751509-0.000247j
[2025-08-24 08:55:04] [Iter 3206/4650] R4[955/2400], Temp: 0.6576, Energy: -28.755806+0.000551j
[2025-08-24 08:55:14] [Iter 3207/4650] R4[956/2400], Temp: 0.6570, Energy: -28.748652-0.000885j
[2025-08-24 08:55:24] [Iter 3208/4650] R4[957/2400], Temp: 0.6564, Energy: -28.754506-0.003933j
[2025-08-24 08:55:34] [Iter 3209/4650] R4[958/2400], Temp: 0.6558, Energy: -28.755141+0.001260j
[2025-08-24 08:55:44] [Iter 3210/4650] R4[959/2400], Temp: 0.6551, Energy: -28.748489+0.000607j
[2025-08-24 08:55:54] [Iter 3211/4650] R4[960/2400], Temp: 0.6545, Energy: -28.755297+0.000007j
[2025-08-24 08:56:05] [Iter 3212/4650] R4[961/2400], Temp: 0.6539, Energy: -28.748763+0.000262j
[2025-08-24 08:56:15] [Iter 3213/4650] R4[962/2400], Temp: 0.6533, Energy: -28.752110+0.001317j
[2025-08-24 08:56:25] [Iter 3214/4650] R4[963/2400], Temp: 0.6526, Energy: -28.749642+0.001203j
[2025-08-24 08:56:35] [Iter 3215/4650] R4[964/2400], Temp: 0.6520, Energy: -28.743648+0.000812j
[2025-08-24 08:56:45] [Iter 3216/4650] R4[965/2400], Temp: 0.6514, Energy: -28.753178+0.000468j
[2025-08-24 08:56:55] [Iter 3217/4650] R4[966/2400], Temp: 0.6508, Energy: -28.756069+0.002576j
[2025-08-24 08:57:05] [Iter 3218/4650] R4[967/2400], Temp: 0.6501, Energy: -28.753235-0.000004j
[2025-08-24 08:57:16] [Iter 3219/4650] R4[968/2400], Temp: 0.6495, Energy: -28.748021-0.000816j
[2025-08-24 08:57:26] [Iter 3220/4650] R4[969/2400], Temp: 0.6489, Energy: -28.758851-0.001232j
[2025-08-24 08:57:36] [Iter 3221/4650] R4[970/2400], Temp: 0.6483, Energy: -28.751943-0.001105j
[2025-08-24 08:57:46] [Iter 3222/4650] R4[971/2400], Temp: 0.6476, Energy: -28.748876-0.000792j
[2025-08-24 08:57:56] [Iter 3223/4650] R4[972/2400], Temp: 0.6470, Energy: -28.748780-0.001878j
[2025-08-24 08:58:06] [Iter 3224/4650] R4[973/2400], Temp: 0.6464, Energy: -28.750048+0.001144j
[2025-08-24 08:58:16] [Iter 3225/4650] R4[974/2400], Temp: 0.6458, Energy: -28.749327-0.001468j
[2025-08-24 08:58:27] [Iter 3226/4650] R4[975/2400], Temp: 0.6451, Energy: -28.754930+0.001487j
[2025-08-24 08:58:37] [Iter 3227/4650] R4[976/2400], Temp: 0.6445, Energy: -28.747653+0.000145j
[2025-08-24 08:58:47] [Iter 3228/4650] R4[977/2400], Temp: 0.6439, Energy: -28.756118-0.001125j
[2025-08-24 08:58:57] [Iter 3229/4650] R4[978/2400], Temp: 0.6433, Energy: -28.746015-0.000962j
[2025-08-24 08:59:07] [Iter 3230/4650] R4[979/2400], Temp: 0.6426, Energy: -28.747828+0.000109j
[2025-08-24 08:59:17] [Iter 3231/4650] R4[980/2400], Temp: 0.6420, Energy: -28.755802+0.000269j
[2025-08-24 08:59:27] [Iter 3232/4650] R4[981/2400], Temp: 0.6414, Energy: -28.754478-0.000997j
[2025-08-24 08:59:38] [Iter 3233/4650] R4[982/2400], Temp: 0.6408, Energy: -28.753002+0.000031j
[2025-08-24 08:59:48] [Iter 3234/4650] R4[983/2400], Temp: 0.6401, Energy: -28.752551-0.000313j
[2025-08-24 08:59:58] [Iter 3235/4650] R4[984/2400], Temp: 0.6395, Energy: -28.753996+0.000047j
[2025-08-24 09:00:08] [Iter 3236/4650] R4[985/2400], Temp: 0.6389, Energy: -28.748720+0.000140j
[2025-08-24 09:00:18] [Iter 3237/4650] R4[986/2400], Temp: 0.6382, Energy: -28.754300-0.000937j
[2025-08-24 09:00:28] [Iter 3238/4650] R4[987/2400], Temp: 0.6376, Energy: -28.751125-0.001289j
[2025-08-24 09:00:38] [Iter 3239/4650] R4[988/2400], Temp: 0.6370, Energy: -28.745921-0.001593j
[2025-08-24 09:00:49] [Iter 3240/4650] R4[989/2400], Temp: 0.6364, Energy: -28.750815+0.000442j
[2025-08-24 09:00:59] [Iter 3241/4650] R4[990/2400], Temp: 0.6357, Energy: -28.750225+0.000817j
[2025-08-24 09:01:09] [Iter 3242/4650] R4[991/2400], Temp: 0.6351, Energy: -28.751367-0.001866j
[2025-08-24 09:01:19] [Iter 3243/4650] R4[992/2400], Temp: 0.6345, Energy: -28.751579-0.001128j
[2025-08-24 09:01:29] [Iter 3244/4650] R4[993/2400], Temp: 0.6338, Energy: -28.746911-0.000839j
[2025-08-24 09:01:39] [Iter 3245/4650] R4[994/2400], Temp: 0.6332, Energy: -28.755018+0.001584j
[2025-08-24 09:01:49] [Iter 3246/4650] R4[995/2400], Temp: 0.6326, Energy: -28.752133-0.000739j
[2025-08-24 09:02:00] [Iter 3247/4650] R4[996/2400], Temp: 0.6319, Energy: -28.751729+0.000545j
[2025-08-24 09:02:10] [Iter 3248/4650] R4[997/2400], Temp: 0.6313, Energy: -28.748782-0.000477j
[2025-08-24 09:02:20] [Iter 3249/4650] R4[998/2400], Temp: 0.6307, Energy: -28.750606-0.000042j
[2025-08-24 09:02:30] [Iter 3250/4650] R4[999/2400], Temp: 0.6300, Energy: -28.749168+0.000075j
[2025-08-24 09:02:40] [Iter 3251/4650] R4[1000/2400], Temp: 0.6294, Energy: -28.755136+0.001605j
[2025-08-24 09:02:50] [Iter 3252/4650] R4[1001/2400], Temp: 0.6288, Energy: -28.758146+0.000260j
[2025-08-24 09:03:00] [Iter 3253/4650] R4[1002/2400], Temp: 0.6281, Energy: -28.754701-0.003293j
[2025-08-24 09:03:10] [Iter 3254/4650] R4[1003/2400], Temp: 0.6275, Energy: -28.752912+0.000228j
[2025-08-24 09:03:21] [Iter 3255/4650] R4[1004/2400], Temp: 0.6269, Energy: -28.754499-0.002918j
[2025-08-24 09:03:31] [Iter 3256/4650] R4[1005/2400], Temp: 0.6262, Energy: -28.750108-0.001034j
[2025-08-24 09:03:41] [Iter 3257/4650] R4[1006/2400], Temp: 0.6256, Energy: -28.752228+0.000866j
[2025-08-24 09:03:51] [Iter 3258/4650] R4[1007/2400], Temp: 0.6250, Energy: -28.750793+0.000919j
[2025-08-24 09:04:01] [Iter 3259/4650] R4[1008/2400], Temp: 0.6243, Energy: -28.753205+0.001952j
[2025-08-24 09:04:11] [Iter 3260/4650] R4[1009/2400], Temp: 0.6237, Energy: -28.756132+0.001508j
[2025-08-24 09:04:21] [Iter 3261/4650] R4[1010/2400], Temp: 0.6231, Energy: -28.754174-0.001032j
[2025-08-24 09:04:32] [Iter 3262/4650] R4[1011/2400], Temp: 0.6224, Energy: -28.751871-0.001416j
[2025-08-24 09:04:42] [Iter 3263/4650] R4[1012/2400], Temp: 0.6218, Energy: -28.750966+0.000590j
[2025-08-24 09:04:52] [Iter 3264/4650] R4[1013/2400], Temp: 0.6212, Energy: -28.749126-0.001672j
[2025-08-24 09:05:02] [Iter 3265/4650] R4[1014/2400], Temp: 0.6205, Energy: -28.744663+0.001273j
[2025-08-24 09:05:12] [Iter 3266/4650] R4[1015/2400], Temp: 0.6199, Energy: -28.756294-0.000592j
[2025-08-24 09:05:22] [Iter 3267/4650] R4[1016/2400], Temp: 0.6193, Energy: -28.750275-0.001294j
[2025-08-24 09:05:32] [Iter 3268/4650] R4[1017/2400], Temp: 0.6186, Energy: -28.756424+0.000309j
[2025-08-24 09:05:43] [Iter 3269/4650] R4[1018/2400], Temp: 0.6180, Energy: -28.750751+0.001067j
[2025-08-24 09:05:53] [Iter 3270/4650] R4[1019/2400], Temp: 0.6174, Energy: -28.750915+0.000683j
[2025-08-24 09:06:03] [Iter 3271/4650] R4[1020/2400], Temp: 0.6167, Energy: -28.749141-0.000500j
[2025-08-24 09:06:13] [Iter 3272/4650] R4[1021/2400], Temp: 0.6161, Energy: -28.750833+0.001517j
[2025-08-24 09:06:23] [Iter 3273/4650] R4[1022/2400], Temp: 0.6154, Energy: -28.751880+0.000230j
[2025-08-24 09:06:33] [Iter 3274/4650] R4[1023/2400], Temp: 0.6148, Energy: -28.752160+0.001667j
[2025-08-24 09:06:43] [Iter 3275/4650] R4[1024/2400], Temp: 0.6142, Energy: -28.746275+0.000921j
[2025-08-24 09:06:54] [Iter 3276/4650] R4[1025/2400], Temp: 0.6135, Energy: -28.755583-0.000095j
[2025-08-24 09:07:04] [Iter 3277/4650] R4[1026/2400], Temp: 0.6129, Energy: -28.745757-0.000751j
[2025-08-24 09:07:14] [Iter 3278/4650] R4[1027/2400], Temp: 0.6123, Energy: -28.756770+0.000545j
[2025-08-24 09:07:24] [Iter 3279/4650] R4[1028/2400], Temp: 0.6116, Energy: -28.749534+0.000116j
[2025-08-24 09:07:34] [Iter 3280/4650] R4[1029/2400], Temp: 0.6110, Energy: -28.752326-0.000306j
[2025-08-24 09:07:44] [Iter 3281/4650] R4[1030/2400], Temp: 0.6103, Energy: -28.748513+0.001254j
[2025-08-24 09:07:54] [Iter 3282/4650] R4[1031/2400], Temp: 0.6097, Energy: -28.748210+0.004214j
[2025-08-24 09:08:05] [Iter 3283/4650] R4[1032/2400], Temp: 0.6091, Energy: -28.753538-0.000290j
[2025-08-24 09:08:15] [Iter 3284/4650] R4[1033/2400], Temp: 0.6084, Energy: -28.748762-0.002216j
[2025-08-24 09:08:25] [Iter 3285/4650] R4[1034/2400], Temp: 0.6078, Energy: -28.747596-0.000856j
[2025-08-24 09:08:35] [Iter 3286/4650] R4[1035/2400], Temp: 0.6072, Energy: -28.747181-0.000379j
[2025-08-24 09:08:45] [Iter 3287/4650] R4[1036/2400], Temp: 0.6065, Energy: -28.748089-0.000572j
[2025-08-24 09:08:55] [Iter 3288/4650] R4[1037/2400], Temp: 0.6059, Energy: -28.754652+0.000981j
[2025-08-24 09:09:05] [Iter 3289/4650] R4[1038/2400], Temp: 0.6052, Energy: -28.748849+0.001805j
[2025-08-24 09:09:16] [Iter 3290/4650] R4[1039/2400], Temp: 0.6046, Energy: -28.753137+0.000753j
[2025-08-24 09:09:26] [Iter 3291/4650] R4[1040/2400], Temp: 0.6040, Energy: -28.751066+0.003488j
[2025-08-24 09:09:36] [Iter 3292/4650] R4[1041/2400], Temp: 0.6033, Energy: -28.754635+0.000134j
[2025-08-24 09:09:46] [Iter 3293/4650] R4[1042/2400], Temp: 0.6027, Energy: -28.749365+0.001305j
[2025-08-24 09:09:56] [Iter 3294/4650] R4[1043/2400], Temp: 0.6020, Energy: -28.757757-0.000696j
[2025-08-24 09:10:06] [Iter 3295/4650] R4[1044/2400], Temp: 0.6014, Energy: -28.749552+0.000308j
[2025-08-24 09:10:16] [Iter 3296/4650] R4[1045/2400], Temp: 0.6008, Energy: -28.746297+0.001178j
[2025-08-24 09:10:27] [Iter 3297/4650] R4[1046/2400], Temp: 0.6001, Energy: -28.751306+0.001495j
[2025-08-24 09:10:37] [Iter 3298/4650] R4[1047/2400], Temp: 0.5995, Energy: -28.754440+0.000778j
[2025-08-24 09:10:47] [Iter 3299/4650] R4[1048/2400], Temp: 0.5988, Energy: -28.748141+0.001314j
[2025-08-24 09:10:57] [Iter 3300/4650] R4[1049/2400], Temp: 0.5982, Energy: -28.744755+0.006098j
[2025-08-24 09:11:07] [Iter 3301/4650] R4[1050/2400], Temp: 0.5975, Energy: -28.746165+0.000196j
[2025-08-24 09:11:17] [Iter 3302/4650] R4[1051/2400], Temp: 0.5969, Energy: -28.750435-0.002152j
[2025-08-24 09:11:28] [Iter 3303/4650] R4[1052/2400], Temp: 0.5963, Energy: -28.748626-0.001048j
[2025-08-24 09:11:38] [Iter 3304/4650] R4[1053/2400], Temp: 0.5956, Energy: -28.751047+0.000323j
[2025-08-24 09:11:48] [Iter 3305/4650] R4[1054/2400], Temp: 0.5950, Energy: -28.747790+0.000018j
[2025-08-24 09:11:58] [Iter 3306/4650] R4[1055/2400], Temp: 0.5943, Energy: -28.752625-0.000128j
[2025-08-24 09:12:08] [Iter 3307/4650] R4[1056/2400], Temp: 0.5937, Energy: -28.756286-0.000389j
[2025-08-24 09:12:18] [Iter 3308/4650] R4[1057/2400], Temp: 0.5930, Energy: -28.750422-0.001288j
[2025-08-24 09:12:28] [Iter 3309/4650] R4[1058/2400], Temp: 0.5924, Energy: -28.751920-0.000573j
[2025-08-24 09:12:38] [Iter 3310/4650] R4[1059/2400], Temp: 0.5918, Energy: -28.752908+0.000487j
[2025-08-24 09:12:49] [Iter 3311/4650] R4[1060/2400], Temp: 0.5911, Energy: -28.748031+0.001154j
[2025-08-24 09:12:59] [Iter 3312/4650] R4[1061/2400], Temp: 0.5905, Energy: -28.753266-0.001813j
[2025-08-24 09:13:09] [Iter 3313/4650] R4[1062/2400], Temp: 0.5898, Energy: -28.744401+0.001464j
[2025-08-24 09:13:19] [Iter 3314/4650] R4[1063/2400], Temp: 0.5892, Energy: -28.748570+0.000656j
[2025-08-24 09:13:29] [Iter 3315/4650] R4[1064/2400], Temp: 0.5885, Energy: -28.755277-0.000838j
[2025-08-24 09:13:39] [Iter 3316/4650] R4[1065/2400], Temp: 0.5879, Energy: -28.751147+0.000202j
[2025-08-24 09:13:49] [Iter 3317/4650] R4[1066/2400], Temp: 0.5873, Energy: -28.751787-0.000105j
[2025-08-24 09:14:00] [Iter 3318/4650] R4[1067/2400], Temp: 0.5866, Energy: -28.753455+0.000259j
[2025-08-24 09:14:10] [Iter 3319/4650] R4[1068/2400], Temp: 0.5860, Energy: -28.755235-0.000154j
[2025-08-24 09:14:20] [Iter 3320/4650] R4[1069/2400], Temp: 0.5853, Energy: -28.752124+0.001267j
[2025-08-24 09:14:30] [Iter 3321/4650] R4[1070/2400], Temp: 0.5847, Energy: -28.751692+0.000621j
[2025-08-24 09:14:40] [Iter 3322/4650] R4[1071/2400], Temp: 0.5840, Energy: -28.753002-0.001089j
[2025-08-24 09:14:50] [Iter 3323/4650] R4[1072/2400], Temp: 0.5834, Energy: -28.753219-0.000086j
[2025-08-24 09:15:00] [Iter 3324/4650] R4[1073/2400], Temp: 0.5827, Energy: -28.750981-0.000238j
[2025-08-24 09:15:11] [Iter 3325/4650] R4[1074/2400], Temp: 0.5821, Energy: -28.752706-0.000456j
[2025-08-24 09:15:21] [Iter 3326/4650] R4[1075/2400], Temp: 0.5814, Energy: -28.748681+0.001283j
[2025-08-24 09:15:31] [Iter 3327/4650] R4[1076/2400], Temp: 0.5808, Energy: -28.748957-0.002141j
[2025-08-24 09:15:41] [Iter 3328/4650] R4[1077/2400], Temp: 0.5802, Energy: -28.752431-0.000271j
[2025-08-24 09:15:51] [Iter 3329/4650] R4[1078/2400], Temp: 0.5795, Energy: -28.757504-0.001465j
[2025-08-24 09:16:01] [Iter 3330/4650] R4[1079/2400], Temp: 0.5789, Energy: -28.749094+0.002296j
[2025-08-24 09:16:11] [Iter 3331/4650] R4[1080/2400], Temp: 0.5782, Energy: -28.749662+0.000112j
[2025-08-24 09:16:22] [Iter 3332/4650] R4[1081/2400], Temp: 0.5776, Energy: -28.752461-0.001003j
[2025-08-24 09:16:32] [Iter 3333/4650] R4[1082/2400], Temp: 0.5769, Energy: -28.756488-0.000952j
[2025-08-24 09:16:42] [Iter 3334/4650] R4[1083/2400], Temp: 0.5763, Energy: -28.753037+0.000347j
[2025-08-24 09:16:52] [Iter 3335/4650] R4[1084/2400], Temp: 0.5756, Energy: -28.750331-0.000129j
[2025-08-24 09:17:02] [Iter 3336/4650] R4[1085/2400], Temp: 0.5750, Energy: -28.750411-0.001736j
[2025-08-24 09:17:12] [Iter 3337/4650] R4[1086/2400], Temp: 0.5743, Energy: -28.749245-0.001150j
[2025-08-24 09:17:22] [Iter 3338/4650] R4[1087/2400], Temp: 0.5737, Energy: -28.754644-0.001374j
[2025-08-24 09:17:33] [Iter 3339/4650] R4[1088/2400], Temp: 0.5730, Energy: -28.751881+0.002442j
[2025-08-24 09:17:43] [Iter 3340/4650] R4[1089/2400], Temp: 0.5724, Energy: -28.751602+0.000409j
[2025-08-24 09:17:53] [Iter 3341/4650] R4[1090/2400], Temp: 0.5717, Energy: -28.752777-0.000254j
[2025-08-24 09:18:03] [Iter 3342/4650] R4[1091/2400], Temp: 0.5711, Energy: -28.751532-0.001357j
[2025-08-24 09:18:13] [Iter 3343/4650] R4[1092/2400], Temp: 0.5705, Energy: -28.749790+0.000256j
[2025-08-24 09:18:23] [Iter 3344/4650] R4[1093/2400], Temp: 0.5698, Energy: -28.749063+0.000871j
[2025-08-24 09:18:33] [Iter 3345/4650] R4[1094/2400], Temp: 0.5692, Energy: -28.747190-0.000848j
[2025-08-24 09:18:44] [Iter 3346/4650] R4[1095/2400], Temp: 0.5685, Energy: -28.753706+0.000120j
[2025-08-24 09:18:54] [Iter 3347/4650] R4[1096/2400], Temp: 0.5679, Energy: -28.749714+0.000870j
[2025-08-24 09:19:04] [Iter 3348/4650] R4[1097/2400], Temp: 0.5672, Energy: -28.752950-0.000271j
[2025-08-24 09:19:14] [Iter 3349/4650] R4[1098/2400], Temp: 0.5666, Energy: -28.751782-0.000257j
[2025-08-24 09:19:24] [Iter 3350/4650] R4[1099/2400], Temp: 0.5659, Energy: -28.759342+0.000778j
[2025-08-24 09:19:34] [Iter 3351/4650] R4[1100/2400], Temp: 0.5653, Energy: -28.754322-0.002411j
[2025-08-24 09:19:44] [Iter 3352/4650] R4[1101/2400], Temp: 0.5646, Energy: -28.744833+0.001615j
[2025-08-24 09:19:55] [Iter 3353/4650] R4[1102/2400], Temp: 0.5640, Energy: -28.751852-0.001185j
[2025-08-24 09:20:05] [Iter 3354/4650] R4[1103/2400], Temp: 0.5633, Energy: -28.752347-0.001401j
[2025-08-24 09:20:15] [Iter 3355/4650] R4[1104/2400], Temp: 0.5627, Energy: -28.753614+0.001158j
[2025-08-24 09:20:25] [Iter 3356/4650] R4[1105/2400], Temp: 0.5620, Energy: -28.748031+0.000099j
[2025-08-24 09:20:35] [Iter 3357/4650] R4[1106/2400], Temp: 0.5614, Energy: -28.749140-0.000259j
[2025-08-24 09:20:45] [Iter 3358/4650] R4[1107/2400], Temp: 0.5607, Energy: -28.759177+0.000727j
[2025-08-24 09:20:55] [Iter 3359/4650] R4[1108/2400], Temp: 0.5601, Energy: -28.751912+0.001542j
[2025-08-24 09:21:06] [Iter 3360/4650] R4[1109/2400], Temp: 0.5594, Energy: -28.752632-0.000754j
[2025-08-24 09:21:16] [Iter 3361/4650] R4[1110/2400], Temp: 0.5588, Energy: -28.751338+0.000135j
[2025-08-24 09:21:26] [Iter 3362/4650] R4[1111/2400], Temp: 0.5581, Energy: -28.757469-0.000971j
[2025-08-24 09:21:36] [Iter 3363/4650] R4[1112/2400], Temp: 0.5575, Energy: -28.752592-0.000955j
[2025-08-24 09:21:46] [Iter 3364/4650] R4[1113/2400], Temp: 0.5568, Energy: -28.755475+0.000925j
[2025-08-24 09:21:56] [Iter 3365/4650] R4[1114/2400], Temp: 0.5562, Energy: -28.749522+0.000313j
[2025-08-24 09:22:06] [Iter 3366/4650] R4[1115/2400], Temp: 0.5555, Energy: -28.757108-0.000436j
[2025-08-24 09:22:17] [Iter 3367/4650] R4[1116/2400], Temp: 0.5549, Energy: -28.756520-0.000751j
[2025-08-24 09:22:27] [Iter 3368/4650] R4[1117/2400], Temp: 0.5542, Energy: -28.751187+0.002342j
[2025-08-24 09:22:37] [Iter 3369/4650] R4[1118/2400], Temp: 0.5536, Energy: -28.753330+0.002395j
[2025-08-24 09:22:47] [Iter 3370/4650] R4[1119/2400], Temp: 0.5529, Energy: -28.753363-0.000200j
[2025-08-24 09:22:57] [Iter 3371/4650] R4[1120/2400], Temp: 0.5523, Energy: -28.752639-0.001012j
[2025-08-24 09:23:07] [Iter 3372/4650] R4[1121/2400], Temp: 0.5516, Energy: -28.756603-0.000658j
[2025-08-24 09:23:17] [Iter 3373/4650] R4[1122/2400], Temp: 0.5510, Energy: -28.751328+0.001873j
[2025-08-24 09:23:28] [Iter 3374/4650] R4[1123/2400], Temp: 0.5503, Energy: -28.750727+0.000446j
[2025-08-24 09:23:38] [Iter 3375/4650] R4[1124/2400], Temp: 0.5497, Energy: -28.748277+0.000996j
[2025-08-24 09:23:48] [Iter 3376/4650] R4[1125/2400], Temp: 0.5490, Energy: -28.748412+0.001067j
[2025-08-24 09:23:58] [Iter 3377/4650] R4[1126/2400], Temp: 0.5484, Energy: -28.754672+0.001592j
[2025-08-24 09:24:08] [Iter 3378/4650] R4[1127/2400], Temp: 0.5477, Energy: -28.754849-0.000612j
[2025-08-24 09:24:18] [Iter 3379/4650] R4[1128/2400], Temp: 0.5471, Energy: -28.756086-0.000696j
[2025-08-24 09:24:28] [Iter 3380/4650] R4[1129/2400], Temp: 0.5464, Energy: -28.746341+0.000988j
[2025-08-24 09:24:39] [Iter 3381/4650] R4[1130/2400], Temp: 0.5458, Energy: -28.754911-0.001878j
[2025-08-24 09:24:49] [Iter 3382/4650] R4[1131/2400], Temp: 0.5451, Energy: -28.752832-0.001328j
[2025-08-24 09:24:59] [Iter 3383/4650] R4[1132/2400], Temp: 0.5444, Energy: -28.748760-0.001334j
[2025-08-24 09:25:09] [Iter 3384/4650] R4[1133/2400], Temp: 0.5438, Energy: -28.752655-0.000053j
[2025-08-24 09:25:19] [Iter 3385/4650] R4[1134/2400], Temp: 0.5431, Energy: -28.752976-0.000057j
[2025-08-24 09:25:29] [Iter 3386/4650] R4[1135/2400], Temp: 0.5425, Energy: -28.753439+0.001359j
[2025-08-24 09:25:39] [Iter 3387/4650] R4[1136/2400], Temp: 0.5418, Energy: -28.749120+0.000978j
[2025-08-24 09:25:50] [Iter 3388/4650] R4[1137/2400], Temp: 0.5412, Energy: -28.748724+0.000504j
[2025-08-24 09:26:00] [Iter 3389/4650] R4[1138/2400], Temp: 0.5405, Energy: -28.756250-0.001624j
[2025-08-24 09:26:10] [Iter 3390/4650] R4[1139/2400], Temp: 0.5399, Energy: -28.754754+0.000733j
[2025-08-24 09:26:20] [Iter 3391/4650] R4[1140/2400], Temp: 0.5392, Energy: -28.752166-0.000732j
[2025-08-24 09:26:30] [Iter 3392/4650] R4[1141/2400], Temp: 0.5386, Energy: -28.755818-0.000428j
[2025-08-24 09:26:40] [Iter 3393/4650] R4[1142/2400], Temp: 0.5379, Energy: -28.751984-0.000533j
[2025-08-24 09:26:50] [Iter 3394/4650] R4[1143/2400], Temp: 0.5373, Energy: -28.751293+0.000089j
[2025-08-24 09:27:01] [Iter 3395/4650] R4[1144/2400], Temp: 0.5366, Energy: -28.755344+0.000340j
[2025-08-24 09:27:11] [Iter 3396/4650] R4[1145/2400], Temp: 0.5360, Energy: -28.752622-0.000291j
[2025-08-24 09:27:21] [Iter 3397/4650] R4[1146/2400], Temp: 0.5353, Energy: -28.750072+0.000453j
[2025-08-24 09:27:31] [Iter 3398/4650] R4[1147/2400], Temp: 0.5347, Energy: -28.753516+0.000445j
[2025-08-24 09:27:41] [Iter 3399/4650] R4[1148/2400], Temp: 0.5340, Energy: -28.756126-0.000192j
[2025-08-24 09:27:51] [Iter 3400/4650] R4[1149/2400], Temp: 0.5334, Energy: -28.754999+0.002281j
[2025-08-24 09:28:01] [Iter 3401/4650] R4[1150/2400], Temp: 0.5327, Energy: -28.751139+0.001348j
[2025-08-24 09:28:12] [Iter 3402/4650] R4[1151/2400], Temp: 0.5320, Energy: -28.755018-0.000313j
[2025-08-24 09:28:22] [Iter 3403/4650] R4[1152/2400], Temp: 0.5314, Energy: -28.751974-0.000166j
[2025-08-24 09:28:32] [Iter 3404/4650] R4[1153/2400], Temp: 0.5307, Energy: -28.753243+0.000498j
[2025-08-24 09:28:42] [Iter 3405/4650] R4[1154/2400], Temp: 0.5301, Energy: -28.749335+0.000782j
[2025-08-24 09:28:52] [Iter 3406/4650] R4[1155/2400], Temp: 0.5294, Energy: -28.753036+0.001788j
[2025-08-24 09:29:02] [Iter 3407/4650] R4[1156/2400], Temp: 0.5288, Energy: -28.753767+0.001399j
[2025-08-24 09:29:12] [Iter 3408/4650] R4[1157/2400], Temp: 0.5281, Energy: -28.752809+0.003212j
[2025-08-24 09:29:23] [Iter 3409/4650] R4[1158/2400], Temp: 0.5275, Energy: -28.751568+0.000162j
[2025-08-24 09:29:33] [Iter 3410/4650] R4[1159/2400], Temp: 0.5268, Energy: -28.753042+0.000061j
[2025-08-24 09:29:43] [Iter 3411/4650] R4[1160/2400], Temp: 0.5262, Energy: -28.746453-0.001153j
[2025-08-24 09:29:53] [Iter 3412/4650] R4[1161/2400], Temp: 0.5255, Energy: -28.756242-0.000459j
[2025-08-24 09:30:03] [Iter 3413/4650] R4[1162/2400], Temp: 0.5249, Energy: -28.751476+0.000475j
[2025-08-24 09:30:13] [Iter 3414/4650] R4[1163/2400], Temp: 0.5242, Energy: -28.750488-0.000668j
[2025-08-24 09:30:23] [Iter 3415/4650] R4[1164/2400], Temp: 0.5236, Energy: -28.748295+0.000421j
[2025-08-24 09:30:34] [Iter 3416/4650] R4[1165/2400], Temp: 0.5229, Energy: -28.745013-0.001974j
[2025-08-24 09:30:44] [Iter 3417/4650] R4[1166/2400], Temp: 0.5222, Energy: -28.751239-0.000844j
[2025-08-24 09:30:54] [Iter 3418/4650] R4[1167/2400], Temp: 0.5216, Energy: -28.750185+0.000510j
[2025-08-24 09:31:04] [Iter 3419/4650] R4[1168/2400], Temp: 0.5209, Energy: -28.751264+0.001030j
[2025-08-24 09:31:14] [Iter 3420/4650] R4[1169/2400], Temp: 0.5203, Energy: -28.756942-0.000658j
[2025-08-24 09:31:24] [Iter 3421/4650] R4[1170/2400], Temp: 0.5196, Energy: -28.754667+0.001147j
[2025-08-24 09:31:34] [Iter 3422/4650] R4[1171/2400], Temp: 0.5190, Energy: -28.756011-0.004328j
[2025-08-24 09:31:45] [Iter 3423/4650] R4[1172/2400], Temp: 0.5183, Energy: -28.748422+0.001018j
[2025-08-24 09:31:55] [Iter 3424/4650] R4[1173/2400], Temp: 0.5177, Energy: -28.754442+0.001852j
[2025-08-24 09:32:05] [Iter 3425/4650] R4[1174/2400], Temp: 0.5170, Energy: -28.757665+0.000411j
[2025-08-24 09:32:15] [Iter 3426/4650] R4[1175/2400], Temp: 0.5164, Energy: -28.753735+0.000877j
[2025-08-24 09:32:25] [Iter 3427/4650] R4[1176/2400], Temp: 0.5157, Energy: -28.754811-0.001060j
[2025-08-24 09:32:35] [Iter 3428/4650] R4[1177/2400], Temp: 0.5151, Energy: -28.751082+0.001320j
[2025-08-24 09:32:45] [Iter 3429/4650] R4[1178/2400], Temp: 0.5144, Energy: -28.755539-0.000111j
[2025-08-24 09:32:56] [Iter 3430/4650] R4[1179/2400], Temp: 0.5137, Energy: -28.754488+0.000633j
[2025-08-24 09:33:06] [Iter 3431/4650] R4[1180/2400], Temp: 0.5131, Energy: -28.751540+0.001342j
[2025-08-24 09:33:16] [Iter 3432/4650] R4[1181/2400], Temp: 0.5124, Energy: -28.750695+0.001689j
[2025-08-24 09:33:26] [Iter 3433/4650] R4[1182/2400], Temp: 0.5118, Energy: -28.754068-0.000305j
[2025-08-24 09:33:36] [Iter 3434/4650] R4[1183/2400], Temp: 0.5111, Energy: -28.755885-0.000839j
[2025-08-24 09:33:46] [Iter 3435/4650] R4[1184/2400], Temp: 0.5105, Energy: -28.750462+0.002484j
[2025-08-24 09:33:56] [Iter 3436/4650] R4[1185/2400], Temp: 0.5098, Energy: -28.753100-0.001027j
[2025-08-24 09:34:07] [Iter 3437/4650] R4[1186/2400], Temp: 0.5092, Energy: -28.747729+0.000617j
[2025-08-24 09:34:17] [Iter 3438/4650] R4[1187/2400], Temp: 0.5085, Energy: -28.751430+0.000756j
[2025-08-24 09:34:27] [Iter 3439/4650] R4[1188/2400], Temp: 0.5079, Energy: -28.751035+0.002703j
[2025-08-24 09:34:37] [Iter 3440/4650] R4[1189/2400], Temp: 0.5072, Energy: -28.750065-0.000865j
[2025-08-24 09:34:47] [Iter 3441/4650] R4[1190/2400], Temp: 0.5065, Energy: -28.758758-0.001307j
[2025-08-24 09:34:57] [Iter 3442/4650] R4[1191/2400], Temp: 0.5059, Energy: -28.755258+0.001013j
[2025-08-24 09:35:07] [Iter 3443/4650] R4[1192/2400], Temp: 0.5052, Energy: -28.752059+0.000296j
[2025-08-24 09:35:17] [Iter 3444/4650] R4[1193/2400], Temp: 0.5046, Energy: -28.751321-0.000439j
[2025-08-24 09:35:28] [Iter 3445/4650] R4[1194/2400], Temp: 0.5039, Energy: -28.757555-0.000535j
[2025-08-24 09:35:38] [Iter 3446/4650] R4[1195/2400], Temp: 0.5033, Energy: -28.753681+0.002415j
[2025-08-24 09:35:48] [Iter 3447/4650] R4[1196/2400], Temp: 0.5026, Energy: -28.759207+0.000059j
[2025-08-24 09:35:58] [Iter 3448/4650] R4[1197/2400], Temp: 0.5020, Energy: -28.753580-0.000677j
[2025-08-24 09:36:08] [Iter 3449/4650] R4[1198/2400], Temp: 0.5013, Energy: -28.756529+0.001278j
[2025-08-24 09:36:18] [Iter 3450/4650] R4[1199/2400], Temp: 0.5007, Energy: -28.754098+0.000686j
[2025-08-24 09:36:28] [Iter 3451/4650] R4[1200/2400], Temp: 0.5000, Energy: -28.755765+0.000533j
[2025-08-24 09:36:39] [Iter 3452/4650] R4[1201/2400], Temp: 0.4993, Energy: -28.747780+0.000421j
[2025-08-24 09:36:49] [Iter 3453/4650] R4[1202/2400], Temp: 0.4987, Energy: -28.749508+0.001128j
[2025-08-24 09:36:59] [Iter 3454/4650] R4[1203/2400], Temp: 0.4980, Energy: -28.753420-0.000288j
[2025-08-24 09:37:09] [Iter 3455/4650] R4[1204/2400], Temp: 0.4974, Energy: -28.750922-0.001220j
[2025-08-24 09:37:19] [Iter 3456/4650] R4[1205/2400], Temp: 0.4967, Energy: -28.756098-0.002166j
[2025-08-24 09:37:29] [Iter 3457/4650] R4[1206/2400], Temp: 0.4961, Energy: -28.755790-0.000567j
[2025-08-24 09:37:39] [Iter 3458/4650] R4[1207/2400], Temp: 0.4954, Energy: -28.749201+0.001652j
[2025-08-24 09:37:50] [Iter 3459/4650] R4[1208/2400], Temp: 0.4948, Energy: -28.749391-0.000888j
[2025-08-24 09:38:00] [Iter 3460/4650] R4[1209/2400], Temp: 0.4941, Energy: -28.756853+0.000181j
[2025-08-24 09:38:10] [Iter 3461/4650] R4[1210/2400], Temp: 0.4935, Energy: -28.746186-0.001557j
[2025-08-24 09:38:20] [Iter 3462/4650] R4[1211/2400], Temp: 0.4928, Energy: -28.757434-0.000024j
[2025-08-24 09:38:30] [Iter 3463/4650] R4[1212/2400], Temp: 0.4921, Energy: -28.750682+0.001507j
[2025-08-24 09:38:40] [Iter 3464/4650] R4[1213/2400], Temp: 0.4915, Energy: -28.754928+0.000723j
[2025-08-24 09:38:50] [Iter 3465/4650] R4[1214/2400], Temp: 0.4908, Energy: -28.755862+0.001610j
[2025-08-24 09:39:01] [Iter 3466/4650] R4[1215/2400], Temp: 0.4902, Energy: -28.759184+0.000305j
[2025-08-24 09:39:11] [Iter 3467/4650] R4[1216/2400], Temp: 0.4895, Energy: -28.752527-0.001271j
[2025-08-24 09:39:21] [Iter 3468/4650] R4[1217/2400], Temp: 0.4889, Energy: -28.754549-0.001043j
[2025-08-24 09:39:31] [Iter 3469/4650] R4[1218/2400], Temp: 0.4882, Energy: -28.753097+0.000697j
[2025-08-24 09:39:41] [Iter 3470/4650] R4[1219/2400], Temp: 0.4876, Energy: -28.745028-0.000080j
[2025-08-24 09:39:51] [Iter 3471/4650] R4[1220/2400], Temp: 0.4869, Energy: -28.750946-0.000279j
[2025-08-24 09:40:01] [Iter 3472/4650] R4[1221/2400], Temp: 0.4863, Energy: -28.753024-0.000016j
[2025-08-24 09:40:12] [Iter 3473/4650] R4[1222/2400], Temp: 0.4856, Energy: -28.755461+0.001134j
[2025-08-24 09:40:22] [Iter 3474/4650] R4[1223/2400], Temp: 0.4849, Energy: -28.751968-0.000278j
[2025-08-24 09:40:32] [Iter 3475/4650] R4[1224/2400], Temp: 0.4843, Energy: -28.752501-0.001008j
[2025-08-24 09:40:42] [Iter 3476/4650] R4[1225/2400], Temp: 0.4836, Energy: -28.749708+0.001448j
[2025-08-24 09:40:52] [Iter 3477/4650] R4[1226/2400], Temp: 0.4830, Energy: -28.748075-0.000645j
[2025-08-24 09:41:02] [Iter 3478/4650] R4[1227/2400], Temp: 0.4823, Energy: -28.751675-0.000154j
[2025-08-24 09:41:12] [Iter 3479/4650] R4[1228/2400], Temp: 0.4817, Energy: -28.755255+0.001197j
[2025-08-24 09:41:23] [Iter 3480/4650] R4[1229/2400], Temp: 0.4810, Energy: -28.754359+0.000915j
[2025-08-24 09:41:33] [Iter 3481/4650] R4[1230/2400], Temp: 0.4804, Energy: -28.750247-0.000678j
[2025-08-24 09:41:43] [Iter 3482/4650] R4[1231/2400], Temp: 0.4797, Energy: -28.750671-0.000709j
[2025-08-24 09:41:53] [Iter 3483/4650] R4[1232/2400], Temp: 0.4791, Energy: -28.753250-0.001033j
[2025-08-24 09:42:03] [Iter 3484/4650] R4[1233/2400], Temp: 0.4784, Energy: -28.751647+0.000521j
[2025-08-24 09:42:13] [Iter 3485/4650] R4[1234/2400], Temp: 0.4778, Energy: -28.750136+0.000315j
[2025-08-24 09:42:23] [Iter 3486/4650] R4[1235/2400], Temp: 0.4771, Energy: -28.747783-0.001998j
[2025-08-24 09:42:33] [Iter 3487/4650] R4[1236/2400], Temp: 0.4764, Energy: -28.753104+0.000328j
[2025-08-24 09:42:44] [Iter 3488/4650] R4[1237/2400], Temp: 0.4758, Energy: -28.755150-0.000185j
[2025-08-24 09:42:54] [Iter 3489/4650] R4[1238/2400], Temp: 0.4751, Energy: -28.754919+0.000157j
[2025-08-24 09:43:04] [Iter 3490/4650] R4[1239/2400], Temp: 0.4745, Energy: -28.754727+0.002092j
[2025-08-24 09:43:14] [Iter 3491/4650] R4[1240/2400], Temp: 0.4738, Energy: -28.757298-0.001145j
[2025-08-24 09:43:24] [Iter 3492/4650] R4[1241/2400], Temp: 0.4732, Energy: -28.753095-0.001736j
[2025-08-24 09:43:34] [Iter 3493/4650] R4[1242/2400], Temp: 0.4725, Energy: -28.753095-0.000976j
[2025-08-24 09:43:44] [Iter 3494/4650] R4[1243/2400], Temp: 0.4719, Energy: -28.754393-0.000725j
[2025-08-24 09:43:55] [Iter 3495/4650] R4[1244/2400], Temp: 0.4712, Energy: -28.759447-0.000025j
[2025-08-24 09:44:05] [Iter 3496/4650] R4[1245/2400], Temp: 0.4706, Energy: -28.754533-0.001042j
[2025-08-24 09:44:15] [Iter 3497/4650] R4[1246/2400], Temp: 0.4699, Energy: -28.750074+0.000670j
[2025-08-24 09:44:25] [Iter 3498/4650] R4[1247/2400], Temp: 0.4693, Energy: -28.754679+0.003508j
[2025-08-24 09:44:35] [Iter 3499/4650] R4[1248/2400], Temp: 0.4686, Energy: -28.753239+0.000400j
[2025-08-24 09:44:45] [Iter 3500/4650] R4[1249/2400], Temp: 0.4680, Energy: -28.756077+0.001195j
[2025-08-24 09:44:45] ✓ Checkpoint saved: checkpoint_iter_003500.pkl
[2025-08-24 09:44:56] [Iter 3501/4650] R4[1250/2400], Temp: 0.4673, Energy: -28.755008-0.000090j
[2025-08-24 09:45:06] [Iter 3502/4650] R4[1251/2400], Temp: 0.4666, Energy: -28.751509-0.002340j
[2025-08-24 09:45:16] [Iter 3503/4650] R4[1252/2400], Temp: 0.4660, Energy: -28.753737-0.001789j
[2025-08-24 09:45:26] [Iter 3504/4650] R4[1253/2400], Temp: 0.4653, Energy: -28.751431+0.000234j
[2025-08-24 09:45:36] [Iter 3505/4650] R4[1254/2400], Temp: 0.4647, Energy: -28.755104-0.000511j
[2025-08-24 09:45:46] [Iter 3506/4650] R4[1255/2400], Temp: 0.4640, Energy: -28.755073-0.002698j
[2025-08-24 09:45:56] [Iter 3507/4650] R4[1256/2400], Temp: 0.4634, Energy: -28.748005+0.000678j
[2025-08-24 09:46:07] [Iter 3508/4650] R4[1257/2400], Temp: 0.4627, Energy: -28.756317-0.001218j
[2025-08-24 09:46:17] [Iter 3509/4650] R4[1258/2400], Temp: 0.4621, Energy: -28.755749+0.001738j
[2025-08-24 09:46:27] [Iter 3510/4650] R4[1259/2400], Temp: 0.4614, Energy: -28.759352+0.001276j
[2025-08-24 09:46:37] [Iter 3511/4650] R4[1260/2400], Temp: 0.4608, Energy: -28.752216+0.001264j
[2025-08-24 09:46:47] [Iter 3512/4650] R4[1261/2400], Temp: 0.4601, Energy: -28.750551-0.001636j
[2025-08-24 09:46:57] [Iter 3513/4650] R4[1262/2400], Temp: 0.4595, Energy: -28.753537+0.001185j
[2025-08-24 09:47:07] [Iter 3514/4650] R4[1263/2400], Temp: 0.4588, Energy: -28.753278-0.000855j
[2025-08-24 09:47:18] [Iter 3515/4650] R4[1264/2400], Temp: 0.4582, Energy: -28.752639+0.000837j
[2025-08-24 09:47:28] [Iter 3516/4650] R4[1265/2400], Temp: 0.4575, Energy: -28.753879-0.000681j
[2025-08-24 09:47:38] [Iter 3517/4650] R4[1266/2400], Temp: 0.4569, Energy: -28.749837-0.000230j
[2025-08-24 09:47:48] [Iter 3518/4650] R4[1267/2400], Temp: 0.4562, Energy: -28.758813+0.000275j
[2025-08-24 09:47:58] [Iter 3519/4650] R4[1268/2400], Temp: 0.4556, Energy: -28.748424-0.000162j
[2025-08-24 09:48:08] [Iter 3520/4650] R4[1269/2400], Temp: 0.4549, Energy: -28.749998+0.001047j
[2025-08-24 09:48:18] [Iter 3521/4650] R4[1270/2400], Temp: 0.4542, Energy: -28.753400-0.000807j
[2025-08-24 09:48:29] [Iter 3522/4650] R4[1271/2400], Temp: 0.4536, Energy: -28.748418-0.003018j
[2025-08-24 09:48:39] [Iter 3523/4650] R4[1272/2400], Temp: 0.4529, Energy: -28.746008-0.001788j
[2025-08-24 09:48:49] [Iter 3524/4650] R4[1273/2400], Temp: 0.4523, Energy: -28.748884+0.001948j
[2025-08-24 09:48:59] [Iter 3525/4650] R4[1274/2400], Temp: 0.4516, Energy: -28.751772+0.000891j
[2025-08-24 09:49:09] [Iter 3526/4650] R4[1275/2400], Temp: 0.4510, Energy: -28.753181-0.000561j
[2025-08-24 09:49:19] [Iter 3527/4650] R4[1276/2400], Temp: 0.4503, Energy: -28.755515+0.003580j
[2025-08-24 09:49:29] [Iter 3528/4650] R4[1277/2400], Temp: 0.4497, Energy: -28.752220-0.001298j
[2025-08-24 09:49:39] [Iter 3529/4650] R4[1278/2400], Temp: 0.4490, Energy: -28.758138-0.003172j
[2025-08-24 09:49:50] [Iter 3530/4650] R4[1279/2400], Temp: 0.4484, Energy: -28.755894-0.001357j
[2025-08-24 09:50:00] [Iter 3531/4650] R4[1280/2400], Temp: 0.4477, Energy: -28.753169-0.001678j
[2025-08-24 09:50:10] [Iter 3532/4650] R4[1281/2400], Temp: 0.4471, Energy: -28.756971+0.001432j
[2025-08-24 09:50:20] [Iter 3533/4650] R4[1282/2400], Temp: 0.4464, Energy: -28.753339-0.003788j
[2025-08-24 09:50:30] [Iter 3534/4650] R4[1283/2400], Temp: 0.4458, Energy: -28.754282-0.000586j
[2025-08-24 09:50:40] [Iter 3535/4650] R4[1284/2400], Temp: 0.4451, Energy: -28.755008-0.000897j
[2025-08-24 09:50:50] [Iter 3536/4650] R4[1285/2400], Temp: 0.4445, Energy: -28.752766-0.000581j
[2025-08-24 09:51:01] [Iter 3537/4650] R4[1286/2400], Temp: 0.4438, Energy: -28.751559-0.001811j
[2025-08-24 09:51:11] [Iter 3538/4650] R4[1287/2400], Temp: 0.4432, Energy: -28.752943-0.001367j
[2025-08-24 09:51:21] [Iter 3539/4650] R4[1288/2400], Temp: 0.4425, Energy: -28.757608-0.001738j
[2025-08-24 09:51:31] [Iter 3540/4650] R4[1289/2400], Temp: 0.4419, Energy: -28.752524+0.002131j
[2025-08-24 09:51:41] [Iter 3541/4650] R4[1290/2400], Temp: 0.4412, Energy: -28.752454-0.001116j
[2025-08-24 09:51:51] [Iter 3542/4650] R4[1291/2400], Temp: 0.4406, Energy: -28.753946-0.001800j
[2025-08-24 09:52:01] [Iter 3543/4650] R4[1292/2400], Temp: 0.4399, Energy: -28.754514-0.000192j
[2025-08-24 09:52:12] [Iter 3544/4650] R4[1293/2400], Temp: 0.4393, Energy: -28.750974-0.000223j
[2025-08-24 09:52:22] [Iter 3545/4650] R4[1294/2400], Temp: 0.4386, Energy: -28.755329-0.002060j
[2025-08-24 09:52:32] [Iter 3546/4650] R4[1295/2400], Temp: 0.4380, Energy: -28.753514-0.000695j
[2025-08-24 09:52:42] [Iter 3547/4650] R4[1296/2400], Temp: 0.4373, Energy: -28.753698+0.003495j
[2025-08-24 09:52:52] [Iter 3548/4650] R4[1297/2400], Temp: 0.4367, Energy: -28.752762+0.000769j
[2025-08-24 09:53:02] [Iter 3549/4650] R4[1298/2400], Temp: 0.4360, Energy: -28.752056+0.002748j
[2025-08-24 09:53:12] [Iter 3550/4650] R4[1299/2400], Temp: 0.4354, Energy: -28.758690+0.000692j
[2025-08-24 09:53:23] [Iter 3551/4650] R4[1300/2400], Temp: 0.4347, Energy: -28.755262+0.000443j
[2025-08-24 09:53:33] [Iter 3552/4650] R4[1301/2400], Temp: 0.4341, Energy: -28.748414-0.000621j
[2025-08-24 09:53:43] [Iter 3553/4650] R4[1302/2400], Temp: 0.4334, Energy: -28.756339+0.000557j
[2025-08-24 09:53:53] [Iter 3554/4650] R4[1303/2400], Temp: 0.4328, Energy: -28.748853-0.000842j
[2025-08-24 09:54:03] [Iter 3555/4650] R4[1304/2400], Temp: 0.4321, Energy: -28.752706+0.000446j
[2025-08-24 09:54:13] [Iter 3556/4650] R4[1305/2400], Temp: 0.4315, Energy: -28.752748+0.000912j
[2025-08-24 09:54:24] [Iter 3557/4650] R4[1306/2400], Temp: 0.4308, Energy: -28.748638+0.000351j
[2025-08-24 09:54:34] [Iter 3558/4650] R4[1307/2400], Temp: 0.4302, Energy: -28.758347-0.000388j
[2025-08-24 09:54:44] [Iter 3559/4650] R4[1308/2400], Temp: 0.4295, Energy: -28.752347-0.001526j
[2025-08-24 09:54:54] [Iter 3560/4650] R4[1309/2400], Temp: 0.4289, Energy: -28.749467-0.000657j
[2025-08-24 09:55:04] [Iter 3561/4650] R4[1310/2400], Temp: 0.4283, Energy: -28.758385-0.002726j
[2025-08-24 09:55:14] [Iter 3562/4650] R4[1311/2400], Temp: 0.4276, Energy: -28.750142+0.000174j
[2025-08-24 09:55:24] [Iter 3563/4650] R4[1312/2400], Temp: 0.4270, Energy: -28.753265+0.000451j
[2025-08-24 09:55:35] [Iter 3564/4650] R4[1313/2400], Temp: 0.4263, Energy: -28.751818+0.001491j
[2025-08-24 09:55:45] [Iter 3565/4650] R4[1314/2400], Temp: 0.4257, Energy: -28.749727-0.001089j
[2025-08-24 09:55:55] [Iter 3566/4650] R4[1315/2400], Temp: 0.4250, Energy: -28.749228+0.000352j
[2025-08-24 09:56:05] [Iter 3567/4650] R4[1316/2400], Temp: 0.4244, Energy: -28.757799+0.000194j
[2025-08-24 09:56:15] [Iter 3568/4650] R4[1317/2400], Temp: 0.4237, Energy: -28.758055+0.000025j
[2025-08-24 09:56:25] [Iter 3569/4650] R4[1318/2400], Temp: 0.4231, Energy: -28.753968+0.000552j
[2025-08-24 09:56:35] [Iter 3570/4650] R4[1319/2400], Temp: 0.4224, Energy: -28.754674-0.002290j
[2025-08-24 09:56:46] [Iter 3571/4650] R4[1320/2400], Temp: 0.4218, Energy: -28.754930-0.002632j
[2025-08-24 09:56:56] [Iter 3572/4650] R4[1321/2400], Temp: 0.4211, Energy: -28.749309+0.002983j
[2025-08-24 09:57:06] [Iter 3573/4650] R4[1322/2400], Temp: 0.4205, Energy: -28.751137+0.000247j
[2025-08-24 09:57:16] [Iter 3574/4650] R4[1323/2400], Temp: 0.4198, Energy: -28.754755+0.000216j
[2025-08-24 09:57:26] [Iter 3575/4650] R4[1324/2400], Temp: 0.4192, Energy: -28.752158+0.000389j
[2025-08-24 09:57:36] [Iter 3576/4650] R4[1325/2400], Temp: 0.4186, Energy: -28.753980-0.000877j
[2025-08-24 09:57:46] [Iter 3577/4650] R4[1326/2400], Temp: 0.4179, Energy: -28.754740+0.001706j
[2025-08-24 09:57:57] [Iter 3578/4650] R4[1327/2400], Temp: 0.4173, Energy: -28.755352+0.001395j
[2025-08-24 09:58:07] [Iter 3579/4650] R4[1328/2400], Temp: 0.4166, Energy: -28.758122-0.000143j
[2025-08-24 09:58:17] [Iter 3580/4650] R4[1329/2400], Temp: 0.4160, Energy: -28.749626+0.000500j
[2025-08-24 09:58:27] [Iter 3581/4650] R4[1330/2400], Temp: 0.4153, Energy: -28.749113-0.002065j
[2025-08-24 09:58:37] [Iter 3582/4650] R4[1331/2400], Temp: 0.4147, Energy: -28.750355+0.001697j
[2025-08-24 09:58:47] [Iter 3583/4650] R4[1332/2400], Temp: 0.4140, Energy: -28.754037+0.001323j
[2025-08-24 09:58:57] [Iter 3584/4650] R4[1333/2400], Temp: 0.4134, Energy: -28.754193+0.001367j
[2025-08-24 09:59:07] [Iter 3585/4650] R4[1334/2400], Temp: 0.4127, Energy: -28.756831-0.000887j
[2025-08-24 09:59:18] [Iter 3586/4650] R4[1335/2400], Temp: 0.4121, Energy: -28.747572+0.000690j
[2025-08-24 09:59:28] [Iter 3587/4650] R4[1336/2400], Temp: 0.4115, Energy: -28.754571+0.002453j
[2025-08-24 09:59:38] [Iter 3588/4650] R4[1337/2400], Temp: 0.4108, Energy: -28.750178+0.000721j
[2025-08-24 09:59:48] [Iter 3589/4650] R4[1338/2400], Temp: 0.4102, Energy: -28.746847-0.000699j
[2025-08-24 09:59:58] [Iter 3590/4650] R4[1339/2400], Temp: 0.4095, Energy: -28.753786+0.000160j
[2025-08-24 10:00:08] [Iter 3591/4650] R4[1340/2400], Temp: 0.4089, Energy: -28.756924+0.001824j
[2025-08-24 10:00:18] [Iter 3592/4650] R4[1341/2400], Temp: 0.4082, Energy: -28.756928+0.000861j
[2025-08-24 10:00:29] [Iter 3593/4650] R4[1342/2400], Temp: 0.4076, Energy: -28.755419-0.002317j
[2025-08-24 10:00:39] [Iter 3594/4650] R4[1343/2400], Temp: 0.4070, Energy: -28.750840-0.002721j
[2025-08-24 10:00:49] [Iter 3595/4650] R4[1344/2400], Temp: 0.4063, Energy: -28.751652-0.000972j
[2025-08-24 10:00:59] [Iter 3596/4650] R4[1345/2400], Temp: 0.4057, Energy: -28.758367+0.001107j
[2025-08-24 10:01:09] [Iter 3597/4650] R4[1346/2400], Temp: 0.4050, Energy: -28.749195+0.002947j
[2025-08-24 10:01:19] [Iter 3598/4650] R4[1347/2400], Temp: 0.4044, Energy: -28.752683-0.000324j
[2025-08-24 10:01:29] [Iter 3599/4650] R4[1348/2400], Temp: 0.4037, Energy: -28.760372+0.001756j
[2025-08-24 10:01:40] [Iter 3600/4650] R4[1349/2400], Temp: 0.4031, Energy: -28.753872-0.000183j
[2025-08-24 10:01:50] [Iter 3601/4650] R4[1350/2400], Temp: 0.4025, Energy: -28.751500+0.000716j
[2025-08-24 10:02:00] [Iter 3602/4650] R4[1351/2400], Temp: 0.4018, Energy: -28.754451+0.001625j
[2025-08-24 10:02:10] [Iter 3603/4650] R4[1352/2400], Temp: 0.4012, Energy: -28.749817+0.001358j
[2025-08-24 10:02:20] [Iter 3604/4650] R4[1353/2400], Temp: 0.4005, Energy: -28.753888-0.000709j
[2025-08-24 10:02:30] [Iter 3605/4650] R4[1354/2400], Temp: 0.3999, Energy: -28.752486+0.000030j
[2025-08-24 10:02:40] [Iter 3606/4650] R4[1355/2400], Temp: 0.3992, Energy: -28.748911+0.000612j
[2025-08-24 10:02:51] [Iter 3607/4650] R4[1356/2400], Temp: 0.3986, Energy: -28.755808+0.003001j
[2025-08-24 10:03:01] [Iter 3608/4650] R4[1357/2400], Temp: 0.3980, Energy: -28.754227+0.001673j
[2025-08-24 10:03:11] [Iter 3609/4650] R4[1358/2400], Temp: 0.3973, Energy: -28.751316+0.000837j
[2025-08-24 10:03:21] [Iter 3610/4650] R4[1359/2400], Temp: 0.3967, Energy: -28.751669-0.001441j
[2025-08-24 10:03:31] [Iter 3611/4650] R4[1360/2400], Temp: 0.3960, Energy: -28.753545-0.002583j
[2025-08-24 10:03:41] [Iter 3612/4650] R4[1361/2400], Temp: 0.3954, Energy: -28.753774+0.001530j
[2025-08-24 10:03:51] [Iter 3613/4650] R4[1362/2400], Temp: 0.3948, Energy: -28.753854+0.001013j
[2025-08-24 10:04:02] [Iter 3614/4650] R4[1363/2400], Temp: 0.3941, Energy: -28.749287-0.002190j
[2025-08-24 10:04:12] [Iter 3615/4650] R4[1364/2400], Temp: 0.3935, Energy: -28.754416+0.000763j
[2025-08-24 10:04:22] [Iter 3616/4650] R4[1365/2400], Temp: 0.3928, Energy: -28.758278-0.000405j
[2025-08-24 10:04:32] [Iter 3617/4650] R4[1366/2400], Temp: 0.3922, Energy: -28.749879-0.000049j
[2025-08-24 10:04:42] [Iter 3618/4650] R4[1367/2400], Temp: 0.3916, Energy: -28.755368+0.000631j
[2025-08-24 10:04:52] [Iter 3619/4650] R4[1368/2400], Temp: 0.3909, Energy: -28.753681-0.001652j
[2025-08-24 10:05:02] [Iter 3620/4650] R4[1369/2400], Temp: 0.3903, Energy: -28.757854-0.000984j
[2025-08-24 10:05:13] [Iter 3621/4650] R4[1370/2400], Temp: 0.3897, Energy: -28.752122-0.002920j
[2025-08-24 10:05:23] [Iter 3622/4650] R4[1371/2400], Temp: 0.3890, Energy: -28.753194-0.001361j
[2025-08-24 10:05:33] [Iter 3623/4650] R4[1372/2400], Temp: 0.3884, Energy: -28.752577-0.000483j
[2025-08-24 10:05:43] [Iter 3624/4650] R4[1373/2400], Temp: 0.3877, Energy: -28.754663-0.000278j
[2025-08-24 10:05:53] [Iter 3625/4650] R4[1374/2400], Temp: 0.3871, Energy: -28.754621-0.000253j
[2025-08-24 10:06:03] [Iter 3626/4650] R4[1375/2400], Temp: 0.3865, Energy: -28.756355+0.000828j
[2025-08-24 10:06:13] [Iter 3627/4650] R4[1376/2400], Temp: 0.3858, Energy: -28.749720+0.000162j
[2025-08-24 10:06:24] [Iter 3628/4650] R4[1377/2400], Temp: 0.3852, Energy: -28.762011+0.001089j
[2025-08-24 10:06:34] [Iter 3629/4650] R4[1378/2400], Temp: 0.3846, Energy: -28.753128+0.000415j
[2025-08-24 10:06:44] [Iter 3630/4650] R4[1379/2400], Temp: 0.3839, Energy: -28.753733+0.000924j
[2025-08-24 10:06:54] [Iter 3631/4650] R4[1380/2400], Temp: 0.3833, Energy: -28.754778-0.001323j
[2025-08-24 10:07:04] [Iter 3632/4650] R4[1381/2400], Temp: 0.3826, Energy: -28.753632-0.000404j
[2025-08-24 10:07:14] [Iter 3633/4650] R4[1382/2400], Temp: 0.3820, Energy: -28.752619+0.000109j
[2025-08-24 10:07:24] [Iter 3634/4650] R4[1383/2400], Temp: 0.3814, Energy: -28.759975+0.000061j
[2025-08-24 10:07:35] [Iter 3635/4650] R4[1384/2400], Temp: 0.3807, Energy: -28.752718-0.001738j
[2025-08-24 10:07:45] [Iter 3636/4650] R4[1385/2400], Temp: 0.3801, Energy: -28.748740-0.001222j
[2025-08-24 10:07:55] [Iter 3637/4650] R4[1386/2400], Temp: 0.3795, Energy: -28.754384-0.000289j
[2025-08-24 10:08:05] [Iter 3638/4650] R4[1387/2400], Temp: 0.3788, Energy: -28.753446+0.000176j
[2025-08-24 10:08:15] [Iter 3639/4650] R4[1388/2400], Temp: 0.3782, Energy: -28.757234-0.000191j
[2025-08-24 10:08:25] [Iter 3640/4650] R4[1389/2400], Temp: 0.3776, Energy: -28.754833+0.000018j
[2025-08-24 10:08:35] [Iter 3641/4650] R4[1390/2400], Temp: 0.3769, Energy: -28.755787+0.000651j
[2025-08-24 10:08:45] [Iter 3642/4650] R4[1391/2400], Temp: 0.3763, Energy: -28.749357-0.002223j
[2025-08-24 10:08:56] [Iter 3643/4650] R4[1392/2400], Temp: 0.3757, Energy: -28.755064+0.000337j
[2025-08-24 10:09:06] [Iter 3644/4650] R4[1393/2400], Temp: 0.3750, Energy: -28.758200-0.000221j
[2025-08-24 10:09:16] [Iter 3645/4650] R4[1394/2400], Temp: 0.3744, Energy: -28.753982-0.000893j
[2025-08-24 10:09:26] [Iter 3646/4650] R4[1395/2400], Temp: 0.3738, Energy: -28.750392+0.000057j
[2025-08-24 10:09:36] [Iter 3647/4650] R4[1396/2400], Temp: 0.3731, Energy: -28.753169+0.000422j
[2025-08-24 10:09:46] [Iter 3648/4650] R4[1397/2400], Temp: 0.3725, Energy: -28.754337+0.001125j
[2025-08-24 10:09:57] [Iter 3649/4650] R4[1398/2400], Temp: 0.3719, Energy: -28.755283-0.001173j
[2025-08-24 10:10:07] [Iter 3650/4650] R4[1399/2400], Temp: 0.3712, Energy: -28.754908-0.000352j
[2025-08-24 10:10:17] [Iter 3651/4650] R4[1400/2400], Temp: 0.3706, Energy: -28.755988-0.002360j
[2025-08-24 10:10:27] [Iter 3652/4650] R4[1401/2400], Temp: 0.3700, Energy: -28.750523+0.000658j
[2025-08-24 10:10:37] [Iter 3653/4650] R4[1402/2400], Temp: 0.3693, Energy: -28.758530+0.000080j
[2025-08-24 10:10:47] [Iter 3654/4650] R4[1403/2400], Temp: 0.3687, Energy: -28.750811-0.000344j
[2025-08-24 10:10:57] [Iter 3655/4650] R4[1404/2400], Temp: 0.3681, Energy: -28.758550-0.001387j
[2025-08-24 10:11:08] [Iter 3656/4650] R4[1405/2400], Temp: 0.3674, Energy: -28.758373-0.000332j
[2025-08-24 10:11:18] [Iter 3657/4650] R4[1406/2400], Temp: 0.3668, Energy: -28.753848-0.002707j
[2025-08-24 10:11:28] [Iter 3658/4650] R4[1407/2400], Temp: 0.3662, Energy: -28.753532+0.001215j
[2025-08-24 10:11:38] [Iter 3659/4650] R4[1408/2400], Temp: 0.3655, Energy: -28.753182-0.000631j
[2025-08-24 10:11:48] [Iter 3660/4650] R4[1409/2400], Temp: 0.3649, Energy: -28.753468+0.001276j
[2025-08-24 10:11:58] [Iter 3661/4650] R4[1410/2400], Temp: 0.3643, Energy: -28.755925-0.000636j
[2025-08-24 10:12:08] [Iter 3662/4650] R4[1411/2400], Temp: 0.3636, Energy: -28.755565-0.001739j
[2025-08-24 10:12:19] [Iter 3663/4650] R4[1412/2400], Temp: 0.3630, Energy: -28.750541+0.001868j
[2025-08-24 10:12:29] [Iter 3664/4650] R4[1413/2400], Temp: 0.3624, Energy: -28.758325+0.000126j
[2025-08-24 10:12:39] [Iter 3665/4650] R4[1414/2400], Temp: 0.3618, Energy: -28.756321+0.000998j
[2025-08-24 10:12:49] [Iter 3666/4650] R4[1415/2400], Temp: 0.3611, Energy: -28.752017-0.000167j
[2025-08-24 10:12:59] [Iter 3667/4650] R4[1416/2400], Temp: 0.3605, Energy: -28.754731-0.000731j
[2025-08-24 10:13:09] [Iter 3668/4650] R4[1417/2400], Temp: 0.3599, Energy: -28.759461-0.001598j
[2025-08-24 10:13:19] [Iter 3669/4650] R4[1418/2400], Temp: 0.3592, Energy: -28.756004-0.001669j
[2025-08-24 10:13:30] [Iter 3670/4650] R4[1419/2400], Temp: 0.3586, Energy: -28.747732-0.001997j
[2025-08-24 10:13:40] [Iter 3671/4650] R4[1420/2400], Temp: 0.3580, Energy: -28.755173+0.000489j
[2025-08-24 10:13:50] [Iter 3672/4650] R4[1421/2400], Temp: 0.3574, Energy: -28.751647-0.000140j
[2025-08-24 10:14:00] [Iter 3673/4650] R4[1422/2400], Temp: 0.3567, Energy: -28.755691+0.000279j
[2025-08-24 10:14:10] [Iter 3674/4650] R4[1423/2400], Temp: 0.3561, Energy: -28.753209-0.001064j
[2025-08-24 10:14:20] [Iter 3675/4650] R4[1424/2400], Temp: 0.3555, Energy: -28.754683+0.001767j
[2025-08-24 10:14:30] [Iter 3676/4650] R4[1425/2400], Temp: 0.3549, Energy: -28.753079-0.000326j
[2025-08-24 10:14:41] [Iter 3677/4650] R4[1426/2400], Temp: 0.3542, Energy: -28.756160+0.001630j
[2025-08-24 10:14:51] [Iter 3678/4650] R4[1427/2400], Temp: 0.3536, Energy: -28.752107-0.000989j
[2025-08-24 10:15:01] [Iter 3679/4650] R4[1428/2400], Temp: 0.3530, Energy: -28.754018-0.001442j
[2025-08-24 10:15:11] [Iter 3680/4650] R4[1429/2400], Temp: 0.3524, Energy: -28.753796-0.000398j
[2025-08-24 10:15:21] [Iter 3681/4650] R4[1430/2400], Temp: 0.3517, Energy: -28.756839-0.000394j
[2025-08-24 10:15:31] [Iter 3682/4650] R4[1431/2400], Temp: 0.3511, Energy: -28.756056-0.001442j
[2025-08-24 10:15:41] [Iter 3683/4650] R4[1432/2400], Temp: 0.3505, Energy: -28.753392+0.000257j
[2025-08-24 10:15:52] [Iter 3684/4650] R4[1433/2400], Temp: 0.3499, Energy: -28.755721-0.000850j
[2025-08-24 10:16:02] [Iter 3685/4650] R4[1434/2400], Temp: 0.3492, Energy: -28.751525-0.001341j
[2025-08-24 10:16:12] [Iter 3686/4650] R4[1435/2400], Temp: 0.3486, Energy: -28.753574-0.000924j
[2025-08-24 10:16:22] [Iter 3687/4650] R4[1436/2400], Temp: 0.3480, Energy: -28.760954+0.000272j
[2025-08-24 10:16:32] [Iter 3688/4650] R4[1437/2400], Temp: 0.3474, Energy: -28.756774+0.002115j
[2025-08-24 10:16:42] [Iter 3689/4650] R4[1438/2400], Temp: 0.3467, Energy: -28.756505-0.000818j
[2025-08-24 10:16:52] [Iter 3690/4650] R4[1439/2400], Temp: 0.3461, Energy: -28.754353+0.001187j
[2025-08-24 10:17:03] [Iter 3691/4650] R4[1440/2400], Temp: 0.3455, Energy: -28.749745-0.000284j
[2025-08-24 10:17:13] [Iter 3692/4650] R4[1441/2400], Temp: 0.3449, Energy: -28.754966+0.001620j
[2025-08-24 10:17:23] [Iter 3693/4650] R4[1442/2400], Temp: 0.3442, Energy: -28.750098+0.001779j
[2025-08-24 10:17:33] [Iter 3694/4650] R4[1443/2400], Temp: 0.3436, Energy: -28.752086+0.000691j
[2025-08-24 10:17:43] [Iter 3695/4650] R4[1444/2400], Temp: 0.3430, Energy: -28.753477-0.000323j
[2025-08-24 10:17:53] [Iter 3696/4650] R4[1445/2400], Temp: 0.3424, Energy: -28.755407+0.000062j
[2025-08-24 10:18:03] [Iter 3697/4650] R4[1446/2400], Temp: 0.3418, Energy: -28.759765-0.000297j
[2025-08-24 10:18:14] [Iter 3698/4650] R4[1447/2400], Temp: 0.3411, Energy: -28.756097+0.000840j
[2025-08-24 10:18:24] [Iter 3699/4650] R4[1448/2400], Temp: 0.3405, Energy: -28.755310+0.000103j
[2025-08-24 10:18:34] [Iter 3700/4650] R4[1449/2400], Temp: 0.3399, Energy: -28.755771+0.001678j
[2025-08-24 10:18:44] [Iter 3701/4650] R4[1450/2400], Temp: 0.3393, Energy: -28.756863-0.003012j
[2025-08-24 10:18:54] [Iter 3702/4650] R4[1451/2400], Temp: 0.3387, Energy: -28.749851+0.001514j
[2025-08-24 10:19:04] [Iter 3703/4650] R4[1452/2400], Temp: 0.3380, Energy: -28.747645-0.001196j
[2025-08-24 10:19:14] [Iter 3704/4650] R4[1453/2400], Temp: 0.3374, Energy: -28.756162+0.001345j
[2025-08-24 10:19:25] [Iter 3705/4650] R4[1454/2400], Temp: 0.3368, Energy: -28.754862+0.001534j
[2025-08-24 10:19:35] [Iter 3706/4650] R4[1455/2400], Temp: 0.3362, Energy: -28.756321+0.001679j
[2025-08-24 10:19:45] [Iter 3707/4650] R4[1456/2400], Temp: 0.3356, Energy: -28.760584-0.000874j
[2025-08-24 10:19:55] [Iter 3708/4650] R4[1457/2400], Temp: 0.3349, Energy: -28.751664-0.001803j
[2025-08-24 10:20:05] [Iter 3709/4650] R4[1458/2400], Temp: 0.3343, Energy: -28.750857-0.000255j
[2025-08-24 10:20:15] [Iter 3710/4650] R4[1459/2400], Temp: 0.3337, Energy: -28.747272-0.000206j
[2025-08-24 10:20:25] [Iter 3711/4650] R4[1460/2400], Temp: 0.3331, Energy: -28.751050+0.000922j
[2025-08-24 10:20:35] [Iter 3712/4650] R4[1461/2400], Temp: 0.3325, Energy: -28.756289+0.001820j
[2025-08-24 10:20:46] [Iter 3713/4650] R4[1462/2400], Temp: 0.3319, Energy: -28.753564+0.000413j
[2025-08-24 10:20:56] [Iter 3714/4650] R4[1463/2400], Temp: 0.3312, Energy: -28.753132+0.000900j
[2025-08-24 10:21:06] [Iter 3715/4650] R4[1464/2400], Temp: 0.3306, Energy: -28.753180-0.000298j
[2025-08-24 10:21:16] [Iter 3716/4650] R4[1465/2400], Temp: 0.3300, Energy: -28.750666+0.000764j
[2025-08-24 10:21:26] [Iter 3717/4650] R4[1466/2400], Temp: 0.3294, Energy: -28.754092-0.001119j
[2025-08-24 10:21:36] [Iter 3718/4650] R4[1467/2400], Temp: 0.3288, Energy: -28.752817+0.001996j
[2025-08-24 10:21:46] [Iter 3719/4650] R4[1468/2400], Temp: 0.3282, Energy: -28.758343+0.001651j
[2025-08-24 10:21:57] [Iter 3720/4650] R4[1469/2400], Temp: 0.3276, Energy: -28.755362+0.000578j
[2025-08-24 10:22:07] [Iter 3721/4650] R4[1470/2400], Temp: 0.3269, Energy: -28.754044-0.001738j
[2025-08-24 10:22:17] [Iter 3722/4650] R4[1471/2400], Temp: 0.3263, Energy: -28.755736+0.000268j
[2025-08-24 10:22:27] [Iter 3723/4650] R4[1472/2400], Temp: 0.3257, Energy: -28.752481-0.000273j
[2025-08-24 10:22:37] [Iter 3724/4650] R4[1473/2400], Temp: 0.3251, Energy: -28.754532-0.000143j
[2025-08-24 10:22:47] [Iter 3725/4650] R4[1474/2400], Temp: 0.3245, Energy: -28.750663-0.000859j
[2025-08-24 10:22:58] [Iter 3726/4650] R4[1475/2400], Temp: 0.3239, Energy: -28.752531+0.000954j
[2025-08-24 10:23:08] [Iter 3727/4650] R4[1476/2400], Temp: 0.3233, Energy: -28.750052+0.000432j
[2025-08-24 10:23:18] [Iter 3728/4650] R4[1477/2400], Temp: 0.3227, Energy: -28.757583-0.000115j
[2025-08-24 10:23:28] [Iter 3729/4650] R4[1478/2400], Temp: 0.3220, Energy: -28.754767+0.000938j
[2025-08-24 10:23:38] [Iter 3730/4650] R4[1479/2400], Temp: 0.3214, Energy: -28.756010+0.002034j
[2025-08-24 10:23:48] [Iter 3731/4650] R4[1480/2400], Temp: 0.3208, Energy: -28.754601-0.000299j
[2025-08-24 10:23:58] [Iter 3732/4650] R4[1481/2400], Temp: 0.3202, Energy: -28.755246-0.000130j
[2025-08-24 10:24:08] [Iter 3733/4650] R4[1482/2400], Temp: 0.3196, Energy: -28.754385-0.000859j
[2025-08-24 10:24:19] [Iter 3734/4650] R4[1483/2400], Temp: 0.3190, Energy: -28.754649-0.001020j
[2025-08-24 10:24:29] [Iter 3735/4650] R4[1484/2400], Temp: 0.3184, Energy: -28.752377-0.001191j
[2025-08-24 10:24:39] [Iter 3736/4650] R4[1485/2400], Temp: 0.3178, Energy: -28.755055+0.000800j
[2025-08-24 10:24:49] [Iter 3737/4650] R4[1486/2400], Temp: 0.3172, Energy: -28.754328+0.000656j
[2025-08-24 10:24:59] [Iter 3738/4650] R4[1487/2400], Temp: 0.3165, Energy: -28.747843-0.000271j
[2025-08-24 10:25:09] [Iter 3739/4650] R4[1488/2400], Temp: 0.3159, Energy: -28.751960-0.000357j
[2025-08-24 10:25:19] [Iter 3740/4650] R4[1489/2400], Temp: 0.3153, Energy: -28.753441-0.000186j
[2025-08-24 10:25:30] [Iter 3741/4650] R4[1490/2400], Temp: 0.3147, Energy: -28.751250-0.001186j
[2025-08-24 10:25:40] [Iter 3742/4650] R4[1491/2400], Temp: 0.3141, Energy: -28.752868+0.000202j
[2025-08-24 10:25:50] [Iter 3743/4650] R4[1492/2400], Temp: 0.3135, Energy: -28.755399-0.000322j
[2025-08-24 10:26:00] [Iter 3744/4650] R4[1493/2400], Temp: 0.3129, Energy: -28.755905+0.000501j
[2025-08-24 10:26:10] [Iter 3745/4650] R4[1494/2400], Temp: 0.3123, Energy: -28.752202-0.000410j
[2025-08-24 10:26:20] [Iter 3746/4650] R4[1495/2400], Temp: 0.3117, Energy: -28.747876+0.000889j
[2025-08-24 10:26:30] [Iter 3747/4650] R4[1496/2400], Temp: 0.3111, Energy: -28.752407+0.001602j
[2025-08-24 10:26:41] [Iter 3748/4650] R4[1497/2400], Temp: 0.3105, Energy: -28.753108+0.000212j
[2025-08-24 10:26:51] [Iter 3749/4650] R4[1498/2400], Temp: 0.3099, Energy: -28.754793-0.001202j
[2025-08-24 10:27:01] [Iter 3750/4650] R4[1499/2400], Temp: 0.3093, Energy: -28.753717-0.001072j
[2025-08-24 10:27:11] [Iter 3751/4650] R4[1500/2400], Temp: 0.3087, Energy: -28.749660-0.001710j
[2025-08-24 10:27:21] [Iter 3752/4650] R4[1501/2400], Temp: 0.3081, Energy: -28.754946-0.000639j
[2025-08-24 10:27:31] [Iter 3753/4650] R4[1502/2400], Temp: 0.3074, Energy: -28.755292-0.000358j
[2025-08-24 10:27:41] [Iter 3754/4650] R4[1503/2400], Temp: 0.3068, Energy: -28.752458-0.000929j
[2025-08-24 10:27:52] [Iter 3755/4650] R4[1504/2400], Temp: 0.3062, Energy: -28.757154+0.001116j
[2025-08-24 10:28:02] [Iter 3756/4650] R4[1505/2400], Temp: 0.3056, Energy: -28.754128-0.001418j
[2025-08-24 10:28:12] [Iter 3757/4650] R4[1506/2400], Temp: 0.3050, Energy: -28.753689+0.000313j
[2025-08-24 10:28:22] [Iter 3758/4650] R4[1507/2400], Temp: 0.3044, Energy: -28.752526+0.000660j
[2025-08-24 10:28:32] [Iter 3759/4650] R4[1508/2400], Temp: 0.3038, Energy: -28.750109-0.001506j
[2025-08-24 10:28:42] [Iter 3760/4650] R4[1509/2400], Temp: 0.3032, Energy: -28.751771+0.000676j
[2025-08-24 10:28:52] [Iter 3761/4650] R4[1510/2400], Temp: 0.3026, Energy: -28.753104+0.002016j
[2025-08-24 10:29:03] [Iter 3762/4650] R4[1511/2400], Temp: 0.3020, Energy: -28.747881-0.000743j
[2025-08-24 10:29:13] [Iter 3763/4650] R4[1512/2400], Temp: 0.3014, Energy: -28.754065+0.000260j
[2025-08-24 10:29:23] [Iter 3764/4650] R4[1513/2400], Temp: 0.3008, Energy: -28.755512+0.000224j
[2025-08-24 10:29:33] [Iter 3765/4650] R4[1514/2400], Temp: 0.3002, Energy: -28.755317-0.001144j
[2025-08-24 10:29:43] [Iter 3766/4650] R4[1515/2400], Temp: 0.2996, Energy: -28.752046+0.001239j
[2025-08-24 10:29:53] [Iter 3767/4650] R4[1516/2400], Temp: 0.2990, Energy: -28.750683-0.001337j
[2025-08-24 10:30:03] [Iter 3768/4650] R4[1517/2400], Temp: 0.2984, Energy: -28.752416+0.000648j
[2025-08-24 10:30:14] [Iter 3769/4650] R4[1518/2400], Temp: 0.2978, Energy: -28.756941-0.001923j
[2025-08-24 10:30:24] [Iter 3770/4650] R4[1519/2400], Temp: 0.2972, Energy: -28.755196+0.000952j
[2025-08-24 10:30:34] [Iter 3771/4650] R4[1520/2400], Temp: 0.2966, Energy: -28.749978+0.001184j
[2025-08-24 10:30:44] [Iter 3772/4650] R4[1521/2400], Temp: 0.2960, Energy: -28.757601-0.001867j
[2025-08-24 10:30:54] [Iter 3773/4650] R4[1522/2400], Temp: 0.2954, Energy: -28.752864+0.000496j
[2025-08-24 10:31:04] [Iter 3774/4650] R4[1523/2400], Temp: 0.2948, Energy: -28.750841-0.002058j
[2025-08-24 10:31:14] [Iter 3775/4650] R4[1524/2400], Temp: 0.2942, Energy: -28.751986+0.000722j
[2025-08-24 10:31:24] [Iter 3776/4650] R4[1525/2400], Temp: 0.2936, Energy: -28.751302+0.001315j
[2025-08-24 10:31:35] [Iter 3777/4650] R4[1526/2400], Temp: 0.2931, Energy: -28.752190-0.000297j
[2025-08-24 10:31:45] [Iter 3778/4650] R4[1527/2400], Temp: 0.2925, Energy: -28.755132+0.000834j
[2025-08-24 10:31:55] [Iter 3779/4650] R4[1528/2400], Temp: 0.2919, Energy: -28.759829+0.000654j
[2025-08-24 10:32:05] [Iter 3780/4650] R4[1529/2400], Temp: 0.2913, Energy: -28.755154-0.000802j
[2025-08-24 10:32:15] [Iter 3781/4650] R4[1530/2400], Temp: 0.2907, Energy: -28.757813+0.002357j
[2025-08-24 10:32:25] [Iter 3782/4650] R4[1531/2400], Temp: 0.2901, Energy: -28.759548-0.001035j
[2025-08-24 10:32:35] [Iter 3783/4650] R4[1532/2400], Temp: 0.2895, Energy: -28.754783+0.001945j
[2025-08-24 10:32:46] [Iter 3784/4650] R4[1533/2400], Temp: 0.2889, Energy: -28.755894-0.001895j
[2025-08-24 10:32:56] [Iter 3785/4650] R4[1534/2400], Temp: 0.2883, Energy: -28.753370-0.001888j
[2025-08-24 10:33:06] [Iter 3786/4650] R4[1535/2400], Temp: 0.2877, Energy: -28.750302+0.000999j
[2025-08-24 10:33:16] [Iter 3787/4650] R4[1536/2400], Temp: 0.2871, Energy: -28.755796+0.000054j
[2025-08-24 10:33:26] [Iter 3788/4650] R4[1537/2400], Temp: 0.2865, Energy: -28.756235-0.000622j
[2025-08-24 10:33:36] [Iter 3789/4650] R4[1538/2400], Temp: 0.2859, Energy: -28.752814+0.000248j
[2025-08-24 10:33:46] [Iter 3790/4650] R4[1539/2400], Temp: 0.2853, Energy: -28.755431+0.001304j
[2025-08-24 10:33:57] [Iter 3791/4650] R4[1540/2400], Temp: 0.2847, Energy: -28.756015-0.000151j
[2025-08-24 10:34:07] [Iter 3792/4650] R4[1541/2400], Temp: 0.2842, Energy: -28.752498-0.000877j
[2025-08-24 10:34:17] [Iter 3793/4650] R4[1542/2400], Temp: 0.2836, Energy: -28.758851-0.001381j
[2025-08-24 10:34:27] [Iter 3794/4650] R4[1543/2400], Temp: 0.2830, Energy: -28.756683+0.000251j
[2025-08-24 10:34:37] [Iter 3795/4650] R4[1544/2400], Temp: 0.2824, Energy: -28.747978-0.000423j
[2025-08-24 10:34:47] [Iter 3796/4650] R4[1545/2400], Temp: 0.2818, Energy: -28.757122+0.000669j
[2025-08-24 10:34:57] [Iter 3797/4650] R4[1546/2400], Temp: 0.2812, Energy: -28.754473-0.000330j
[2025-08-24 10:35:08] [Iter 3798/4650] R4[1547/2400], Temp: 0.2806, Energy: -28.758053+0.001471j
[2025-08-24 10:35:18] [Iter 3799/4650] R4[1548/2400], Temp: 0.2800, Energy: -28.748216+0.001690j
[2025-08-24 10:35:28] [Iter 3800/4650] R4[1549/2400], Temp: 0.2794, Energy: -28.760821+0.000732j
[2025-08-24 10:35:38] [Iter 3801/4650] R4[1550/2400], Temp: 0.2789, Energy: -28.758876+0.001165j
[2025-08-24 10:35:48] [Iter 3802/4650] R4[1551/2400], Temp: 0.2783, Energy: -28.751740-0.001026j
[2025-08-24 10:35:58] [Iter 3803/4650] R4[1552/2400], Temp: 0.2777, Energy: -28.755671+0.000173j
[2025-08-24 10:36:08] [Iter 3804/4650] R4[1553/2400], Temp: 0.2771, Energy: -28.750057-0.000998j
[2025-08-24 10:36:18] [Iter 3805/4650] R4[1554/2400], Temp: 0.2765, Energy: -28.752876-0.001830j
[2025-08-24 10:36:29] [Iter 3806/4650] R4[1555/2400], Temp: 0.2759, Energy: -28.751258+0.001201j
[2025-08-24 10:36:39] [Iter 3807/4650] R4[1556/2400], Temp: 0.2753, Energy: -28.750156+0.000670j
[2025-08-24 10:36:49] [Iter 3808/4650] R4[1557/2400], Temp: 0.2748, Energy: -28.758430-0.001674j
[2025-08-24 10:36:59] [Iter 3809/4650] R4[1558/2400], Temp: 0.2742, Energy: -28.750823-0.001107j
[2025-08-24 10:37:09] [Iter 3810/4650] R4[1559/2400], Temp: 0.2736, Energy: -28.752057+0.001341j
[2025-08-24 10:37:19] [Iter 3811/4650] R4[1560/2400], Temp: 0.2730, Energy: -28.751963+0.001380j
[2025-08-24 10:37:29] [Iter 3812/4650] R4[1561/2400], Temp: 0.2724, Energy: -28.756170+0.000555j
[2025-08-24 10:37:40] [Iter 3813/4650] R4[1562/2400], Temp: 0.2718, Energy: -28.758314-0.000710j
[2025-08-24 10:37:50] [Iter 3814/4650] R4[1563/2400], Temp: 0.2713, Energy: -28.746053-0.001065j
[2025-08-24 10:38:00] [Iter 3815/4650] R4[1564/2400], Temp: 0.2707, Energy: -28.756763+0.001080j
[2025-08-24 10:38:10] [Iter 3816/4650] R4[1565/2400], Temp: 0.2701, Energy: -28.757556-0.002072j
[2025-08-24 10:38:20] [Iter 3817/4650] R4[1566/2400], Temp: 0.2695, Energy: -28.755856+0.000602j
[2025-08-24 10:38:30] [Iter 3818/4650] R4[1567/2400], Temp: 0.2689, Energy: -28.754272-0.001126j
[2025-08-24 10:38:40] [Iter 3819/4650] R4[1568/2400], Temp: 0.2684, Energy: -28.757127+0.000418j
[2025-08-24 10:38:51] [Iter 3820/4650] R4[1569/2400], Temp: 0.2678, Energy: -28.756406+0.000194j
[2025-08-24 10:39:01] [Iter 3821/4650] R4[1570/2400], Temp: 0.2672, Energy: -28.751754-0.002471j
[2025-08-24 10:39:11] [Iter 3822/4650] R4[1571/2400], Temp: 0.2666, Energy: -28.755500+0.000472j
[2025-08-24 10:39:21] [Iter 3823/4650] R4[1572/2400], Temp: 0.2660, Energy: -28.756322-0.001097j
[2025-08-24 10:39:31] [Iter 3824/4650] R4[1573/2400], Temp: 0.2655, Energy: -28.753870-0.000251j
[2025-08-24 10:39:41] [Iter 3825/4650] R4[1574/2400], Temp: 0.2649, Energy: -28.757322+0.002061j
[2025-08-24 10:39:51] [Iter 3826/4650] R4[1575/2400], Temp: 0.2643, Energy: -28.755210-0.001289j
[2025-08-24 10:40:02] [Iter 3827/4650] R4[1576/2400], Temp: 0.2637, Energy: -28.756602+0.001638j
[2025-08-24 10:40:12] [Iter 3828/4650] R4[1577/2400], Temp: 0.2631, Energy: -28.753278-0.000051j
[2025-08-24 10:40:22] [Iter 3829/4650] R4[1578/2400], Temp: 0.2626, Energy: -28.757591+0.002466j
[2025-08-24 10:40:32] [Iter 3830/4650] R4[1579/2400], Temp: 0.2620, Energy: -28.751235+0.000890j
[2025-08-24 10:40:42] [Iter 3831/4650] R4[1580/2400], Temp: 0.2614, Energy: -28.754553-0.000443j
[2025-08-24 10:40:52] [Iter 3832/4650] R4[1581/2400], Temp: 0.2608, Energy: -28.756601-0.000323j
[2025-08-24 10:41:02] [Iter 3833/4650] R4[1582/2400], Temp: 0.2603, Energy: -28.755053-0.002460j
[2025-08-24 10:41:13] [Iter 3834/4650] R4[1583/2400], Temp: 0.2597, Energy: -28.754995+0.000682j
[2025-08-24 10:41:23] [Iter 3835/4650] R4[1584/2400], Temp: 0.2591, Energy: -28.757112+0.000232j
[2025-08-24 10:41:33] [Iter 3836/4650] R4[1585/2400], Temp: 0.2585, Energy: -28.760772+0.002419j
[2025-08-24 10:41:43] [Iter 3837/4650] R4[1586/2400], Temp: 0.2580, Energy: -28.751576-0.000125j
[2025-08-24 10:41:53] [Iter 3838/4650] R4[1587/2400], Temp: 0.2574, Energy: -28.752864+0.001910j
[2025-08-24 10:42:03] [Iter 3839/4650] R4[1588/2400], Temp: 0.2568, Energy: -28.755424+0.000051j
[2025-08-24 10:42:13] [Iter 3840/4650] R4[1589/2400], Temp: 0.2563, Energy: -28.757826-0.000653j
[2025-08-24 10:42:24] [Iter 3841/4650] R4[1590/2400], Temp: 0.2557, Energy: -28.750622-0.000490j
[2025-08-24 10:42:34] [Iter 3842/4650] R4[1591/2400], Temp: 0.2551, Energy: -28.757714-0.000180j
[2025-08-24 10:42:44] [Iter 3843/4650] R4[1592/2400], Temp: 0.2545, Energy: -28.754209+0.000430j
[2025-08-24 10:42:54] [Iter 3844/4650] R4[1593/2400], Temp: 0.2540, Energy: -28.756097-0.001066j
[2025-08-24 10:43:04] [Iter 3845/4650] R4[1594/2400], Temp: 0.2534, Energy: -28.758661-0.000366j
[2025-08-24 10:43:14] [Iter 3846/4650] R4[1595/2400], Temp: 0.2528, Energy: -28.752852-0.000643j
[2025-08-24 10:43:24] [Iter 3847/4650] R4[1596/2400], Temp: 0.2523, Energy: -28.757510-0.001400j
[2025-08-24 10:43:35] [Iter 3848/4650] R4[1597/2400], Temp: 0.2517, Energy: -28.758257+0.002004j
[2025-08-24 10:43:45] [Iter 3849/4650] R4[1598/2400], Temp: 0.2511, Energy: -28.757128-0.000456j
[2025-08-24 10:43:55] [Iter 3850/4650] R4[1599/2400], Temp: 0.2506, Energy: -28.758839-0.000832j
[2025-08-24 10:44:05] [Iter 3851/4650] R4[1600/2400], Temp: 0.2500, Energy: -28.753159-0.000899j
[2025-08-24 10:44:15] [Iter 3852/4650] R4[1601/2400], Temp: 0.2494, Energy: -28.759495-0.000745j
[2025-08-24 10:44:25] [Iter 3853/4650] R4[1602/2400], Temp: 0.2489, Energy: -28.752712-0.000426j
[2025-08-24 10:44:35] [Iter 3854/4650] R4[1603/2400], Temp: 0.2483, Energy: -28.752380-0.000126j
[2025-08-24 10:44:46] [Iter 3855/4650] R4[1604/2400], Temp: 0.2477, Energy: -28.753612-0.003865j
[2025-08-24 10:44:56] [Iter 3856/4650] R4[1605/2400], Temp: 0.2472, Energy: -28.757806+0.000569j
[2025-08-24 10:45:06] [Iter 3857/4650] R4[1606/2400], Temp: 0.2466, Energy: -28.760707-0.000718j
[2025-08-24 10:45:16] [Iter 3858/4650] R4[1607/2400], Temp: 0.2460, Energy: -28.753977+0.001358j
[2025-08-24 10:45:26] [Iter 3859/4650] R4[1608/2400], Temp: 0.2455, Energy: -28.756574-0.000595j
[2025-08-24 10:45:36] [Iter 3860/4650] R4[1609/2400], Temp: 0.2449, Energy: -28.758004-0.001287j
[2025-08-24 10:45:46] [Iter 3861/4650] R4[1610/2400], Temp: 0.2444, Energy: -28.757619-0.000850j
[2025-08-24 10:45:56] [Iter 3862/4650] R4[1611/2400], Temp: 0.2438, Energy: -28.752725-0.002557j
[2025-08-24 10:46:07] [Iter 3863/4650] R4[1612/2400], Temp: 0.2432, Energy: -28.753152-0.000028j
[2025-08-24 10:46:17] [Iter 3864/4650] R4[1613/2400], Temp: 0.2427, Energy: -28.756221+0.000043j
[2025-08-24 10:46:27] [Iter 3865/4650] R4[1614/2400], Temp: 0.2421, Energy: -28.753852+0.001167j
[2025-08-24 10:46:37] [Iter 3866/4650] R4[1615/2400], Temp: 0.2415, Energy: -28.753574+0.000788j
[2025-08-24 10:46:47] [Iter 3867/4650] R4[1616/2400], Temp: 0.2410, Energy: -28.756621+0.001349j
[2025-08-24 10:46:57] [Iter 3868/4650] R4[1617/2400], Temp: 0.2404, Energy: -28.747096+0.001188j
[2025-08-24 10:47:07] [Iter 3869/4650] R4[1618/2400], Temp: 0.2399, Energy: -28.753939-0.000366j
[2025-08-24 10:47:18] [Iter 3870/4650] R4[1619/2400], Temp: 0.2393, Energy: -28.750076-0.002032j
[2025-08-24 10:47:28] [Iter 3871/4650] R4[1620/2400], Temp: 0.2388, Energy: -28.752734+0.000967j
[2025-08-24 10:47:38] [Iter 3872/4650] R4[1621/2400], Temp: 0.2382, Energy: -28.749565+0.000190j
[2025-08-24 10:47:48] [Iter 3873/4650] R4[1622/2400], Temp: 0.2376, Energy: -28.748286-0.002251j
[2025-08-24 10:47:58] [Iter 3874/4650] R4[1623/2400], Temp: 0.2371, Energy: -28.757642+0.001084j
[2025-08-24 10:48:08] [Iter 3875/4650] R4[1624/2400], Temp: 0.2365, Energy: -28.748035-0.001689j
[2025-08-24 10:48:18] [Iter 3876/4650] R4[1625/2400], Temp: 0.2360, Energy: -28.757886+0.000120j
[2025-08-24 10:48:29] [Iter 3877/4650] R4[1626/2400], Temp: 0.2354, Energy: -28.756541+0.000272j
[2025-08-24 10:48:39] [Iter 3878/4650] R4[1627/2400], Temp: 0.2349, Energy: -28.755561+0.000366j
[2025-08-24 10:48:49] [Iter 3879/4650] R4[1628/2400], Temp: 0.2343, Energy: -28.751940+0.000046j
[2025-08-24 10:48:59] [Iter 3880/4650] R4[1629/2400], Temp: 0.2337, Energy: -28.753752+0.002403j
[2025-08-24 10:49:09] [Iter 3881/4650] R4[1630/2400], Temp: 0.2332, Energy: -28.754264+0.002118j
[2025-08-24 10:49:19] [Iter 3882/4650] R4[1631/2400], Temp: 0.2326, Energy: -28.756815+0.000074j
[2025-08-24 10:49:29] [Iter 3883/4650] R4[1632/2400], Temp: 0.2321, Energy: -28.757553-0.001034j
[2025-08-24 10:49:40] [Iter 3884/4650] R4[1633/2400], Temp: 0.2315, Energy: -28.757514+0.000215j
[2025-08-24 10:49:50] [Iter 3885/4650] R4[1634/2400], Temp: 0.2310, Energy: -28.755003-0.003767j
[2025-08-24 10:50:00] [Iter 3886/4650] R4[1635/2400], Temp: 0.2304, Energy: -28.756511+0.000615j
[2025-08-24 10:50:10] [Iter 3887/4650] R4[1636/2400], Temp: 0.2299, Energy: -28.748229-0.000769j
[2025-08-24 10:50:20] [Iter 3888/4650] R4[1637/2400], Temp: 0.2293, Energy: -28.755228-0.000366j
[2025-08-24 10:50:30] [Iter 3889/4650] R4[1638/2400], Temp: 0.2288, Energy: -28.750526+0.001533j
[2025-08-24 10:50:40] [Iter 3890/4650] R4[1639/2400], Temp: 0.2282, Energy: -28.750055+0.001261j
[2025-08-24 10:50:51] [Iter 3891/4650] R4[1640/2400], Temp: 0.2277, Energy: -28.753661-0.000857j
[2025-08-24 10:51:01] [Iter 3892/4650] R4[1641/2400], Temp: 0.2271, Energy: -28.757306-0.001509j
[2025-08-24 10:51:11] [Iter 3893/4650] R4[1642/2400], Temp: 0.2266, Energy: -28.751070+0.001534j
[2025-08-24 10:51:21] [Iter 3894/4650] R4[1643/2400], Temp: 0.2260, Energy: -28.756559-0.001626j
[2025-08-24 10:51:31] [Iter 3895/4650] R4[1644/2400], Temp: 0.2255, Energy: -28.749976-0.000312j
[2025-08-24 10:51:41] [Iter 3896/4650] R4[1645/2400], Temp: 0.2249, Energy: -28.760214-0.000324j
[2025-08-24 10:51:51] [Iter 3897/4650] R4[1646/2400], Temp: 0.2244, Energy: -28.755436+0.002291j
[2025-08-24 10:52:02] [Iter 3898/4650] R4[1647/2400], Temp: 0.2238, Energy: -28.754511+0.001160j
[2025-08-24 10:52:12] [Iter 3899/4650] R4[1648/2400], Temp: 0.2233, Energy: -28.752979-0.000923j
[2025-08-24 10:52:22] [Iter 3900/4650] R4[1649/2400], Temp: 0.2228, Energy: -28.755499-0.000067j
[2025-08-24 10:52:32] [Iter 3901/4650] R4[1650/2400], Temp: 0.2222, Energy: -28.751831-0.000276j
[2025-08-24 10:52:42] [Iter 3902/4650] R4[1651/2400], Temp: 0.2217, Energy: -28.755028+0.000840j
[2025-08-24 10:52:52] [Iter 3903/4650] R4[1652/2400], Temp: 0.2211, Energy: -28.755731+0.000556j
[2025-08-24 10:53:02] [Iter 3904/4650] R4[1653/2400], Temp: 0.2206, Energy: -28.757054+0.000039j
[2025-08-24 10:53:13] [Iter 3905/4650] R4[1654/2400], Temp: 0.2200, Energy: -28.759664-0.000372j
[2025-08-24 10:53:23] [Iter 3906/4650] R4[1655/2400], Temp: 0.2195, Energy: -28.759930+0.000672j
[2025-08-24 10:53:33] [Iter 3907/4650] R4[1656/2400], Temp: 0.2190, Energy: -28.756701+0.000965j
[2025-08-24 10:53:43] [Iter 3908/4650] R4[1657/2400], Temp: 0.2184, Energy: -28.756746+0.001721j
[2025-08-24 10:53:53] [Iter 3909/4650] R4[1658/2400], Temp: 0.2179, Energy: -28.751845+0.000334j
[2025-08-24 10:54:03] [Iter 3910/4650] R4[1659/2400], Temp: 0.2173, Energy: -28.758353-0.001762j
[2025-08-24 10:54:13] [Iter 3911/4650] R4[1660/2400], Temp: 0.2168, Energy: -28.754789-0.002110j
[2025-08-24 10:54:24] [Iter 3912/4650] R4[1661/2400], Temp: 0.2163, Energy: -28.760976-0.002549j
[2025-08-24 10:54:34] [Iter 3913/4650] R4[1662/2400], Temp: 0.2157, Energy: -28.759134+0.000110j
[2025-08-24 10:54:44] [Iter 3914/4650] R4[1663/2400], Temp: 0.2152, Energy: -28.756835+0.001737j
[2025-08-24 10:54:54] [Iter 3915/4650] R4[1664/2400], Temp: 0.2146, Energy: -28.755896+0.000805j
[2025-08-24 10:55:04] [Iter 3916/4650] R4[1665/2400], Temp: 0.2141, Energy: -28.753987-0.000650j
[2025-08-24 10:55:14] [Iter 3917/4650] R4[1666/2400], Temp: 0.2136, Energy: -28.757005+0.000343j
[2025-08-24 10:55:24] [Iter 3918/4650] R4[1667/2400], Temp: 0.2130, Energy: -28.752907-0.001835j
[2025-08-24 10:55:35] [Iter 3919/4650] R4[1668/2400], Temp: 0.2125, Energy: -28.759197-0.001531j
[2025-08-24 10:55:45] [Iter 3920/4650] R4[1669/2400], Temp: 0.2120, Energy: -28.754302-0.002047j
[2025-08-24 10:55:55] [Iter 3921/4650] R4[1670/2400], Temp: 0.2114, Energy: -28.754278-0.000077j
[2025-08-24 10:56:05] [Iter 3922/4650] R4[1671/2400], Temp: 0.2109, Energy: -28.755977+0.000342j
[2025-08-24 10:56:15] [Iter 3923/4650] R4[1672/2400], Temp: 0.2104, Energy: -28.753570+0.000375j
[2025-08-24 10:56:25] [Iter 3924/4650] R4[1673/2400], Temp: 0.2098, Energy: -28.757043+0.001916j
[2025-08-24 10:56:35] [Iter 3925/4650] R4[1674/2400], Temp: 0.2093, Energy: -28.756007+0.000556j
[2025-08-24 10:56:45] [Iter 3926/4650] R4[1675/2400], Temp: 0.2088, Energy: -28.757396-0.000543j
[2025-08-24 10:56:56] [Iter 3927/4650] R4[1676/2400], Temp: 0.2082, Energy: -28.755937-0.000119j
[2025-08-24 10:57:06] [Iter 3928/4650] R4[1677/2400], Temp: 0.2077, Energy: -28.756146-0.000709j
[2025-08-24 10:57:16] [Iter 3929/4650] R4[1678/2400], Temp: 0.2072, Energy: -28.756219-0.000849j
[2025-08-24 10:57:26] [Iter 3930/4650] R4[1679/2400], Temp: 0.2066, Energy: -28.756515-0.000757j
[2025-08-24 10:57:36] [Iter 3931/4650] R4[1680/2400], Temp: 0.2061, Energy: -28.760812-0.000122j
[2025-08-24 10:57:46] [Iter 3932/4650] R4[1681/2400], Temp: 0.2056, Energy: -28.753721+0.001428j
[2025-08-24 10:57:56] [Iter 3933/4650] R4[1682/2400], Temp: 0.2050, Energy: -28.751815-0.001732j
[2025-08-24 10:58:07] [Iter 3934/4650] R4[1683/2400], Temp: 0.2045, Energy: -28.756590-0.000395j
[2025-08-24 10:58:17] [Iter 3935/4650] R4[1684/2400], Temp: 0.2040, Energy: -28.753603-0.000001j
[2025-08-24 10:58:27] [Iter 3936/4650] R4[1685/2400], Temp: 0.2035, Energy: -28.753266-0.001175j
[2025-08-24 10:58:37] [Iter 3937/4650] R4[1686/2400], Temp: 0.2029, Energy: -28.756098+0.000149j
[2025-08-24 10:58:47] [Iter 3938/4650] R4[1687/2400], Temp: 0.2024, Energy: -28.756190+0.002913j
[2025-08-24 10:58:57] [Iter 3939/4650] R4[1688/2400], Temp: 0.2019, Energy: -28.751012-0.000787j
[2025-08-24 10:59:07] [Iter 3940/4650] R4[1689/2400], Temp: 0.2014, Energy: -28.759758+0.001957j
[2025-08-24 10:59:18] [Iter 3941/4650] R4[1690/2400], Temp: 0.2008, Energy: -28.752628-0.001541j
[2025-08-24 10:59:28] [Iter 3942/4650] R4[1691/2400], Temp: 0.2003, Energy: -28.759730+0.000408j
[2025-08-24 10:59:38] [Iter 3943/4650] R4[1692/2400], Temp: 0.1998, Energy: -28.753838+0.000639j
[2025-08-24 10:59:48] [Iter 3944/4650] R4[1693/2400], Temp: 0.1993, Energy: -28.755023-0.000326j
[2025-08-24 10:59:58] [Iter 3945/4650] R4[1694/2400], Temp: 0.1987, Energy: -28.757587+0.000071j
[2025-08-24 11:00:08] [Iter 3946/4650] R4[1695/2400], Temp: 0.1982, Energy: -28.755363+0.000702j
[2025-08-24 11:00:18] [Iter 3947/4650] R4[1696/2400], Temp: 0.1977, Energy: -28.753828+0.000620j
[2025-08-24 11:00:29] [Iter 3948/4650] R4[1697/2400], Temp: 0.1972, Energy: -28.759745+0.000960j
[2025-08-24 11:00:39] [Iter 3949/4650] R4[1698/2400], Temp: 0.1967, Energy: -28.760160-0.000009j
[2025-08-24 11:00:49] [Iter 3950/4650] R4[1699/2400], Temp: 0.1961, Energy: -28.753627+0.000043j
[2025-08-24 11:00:59] [Iter 3951/4650] R4[1700/2400], Temp: 0.1956, Energy: -28.755724-0.000454j
[2025-08-24 11:01:09] [Iter 3952/4650] R4[1701/2400], Temp: 0.1951, Energy: -28.757744+0.001338j
[2025-08-24 11:01:19] [Iter 3953/4650] R4[1702/2400], Temp: 0.1946, Energy: -28.753012-0.001300j
[2025-08-24 11:01:29] [Iter 3954/4650] R4[1703/2400], Temp: 0.1941, Energy: -28.754221+0.001201j
[2025-08-24 11:01:40] [Iter 3955/4650] R4[1704/2400], Temp: 0.1935, Energy: -28.759920+0.000269j
[2025-08-24 11:01:50] [Iter 3956/4650] R4[1705/2400], Temp: 0.1930, Energy: -28.751861-0.000659j
[2025-08-24 11:02:00] [Iter 3957/4650] R4[1706/2400], Temp: 0.1925, Energy: -28.754540+0.000150j
[2025-08-24 11:02:10] [Iter 3958/4650] R4[1707/2400], Temp: 0.1920, Energy: -28.750071-0.000603j
[2025-08-24 11:02:20] [Iter 3959/4650] R4[1708/2400], Temp: 0.1915, Energy: -28.761336+0.000022j
[2025-08-24 11:02:30] [Iter 3960/4650] R4[1709/2400], Temp: 0.1910, Energy: -28.756051+0.000318j
[2025-08-24 11:02:40] [Iter 3961/4650] R4[1710/2400], Temp: 0.1905, Energy: -28.761011+0.000563j
[2025-08-24 11:02:51] [Iter 3962/4650] R4[1711/2400], Temp: 0.1899, Energy: -28.754487+0.001863j
[2025-08-24 11:03:01] [Iter 3963/4650] R4[1712/2400], Temp: 0.1894, Energy: -28.753216+0.000315j
[2025-08-24 11:03:11] [Iter 3964/4650] R4[1713/2400], Temp: 0.1889, Energy: -28.757876+0.000782j
[2025-08-24 11:03:21] [Iter 3965/4650] R4[1714/2400], Temp: 0.1884, Energy: -28.751562+0.000480j
[2025-08-24 11:03:31] [Iter 3966/4650] R4[1715/2400], Temp: 0.1879, Energy: -28.757497+0.000118j
[2025-08-24 11:03:41] [Iter 3967/4650] R4[1716/2400], Temp: 0.1874, Energy: -28.757633-0.000892j
[2025-08-24 11:03:51] [Iter 3968/4650] R4[1717/2400], Temp: 0.1869, Energy: -28.754026+0.000110j
[2025-08-24 11:04:02] [Iter 3969/4650] R4[1718/2400], Temp: 0.1864, Energy: -28.758328-0.001826j
[2025-08-24 11:04:12] [Iter 3970/4650] R4[1719/2400], Temp: 0.1858, Energy: -28.757649-0.000756j
[2025-08-24 11:04:22] [Iter 3971/4650] R4[1720/2400], Temp: 0.1853, Energy: -28.757733-0.001381j
[2025-08-24 11:04:32] [Iter 3972/4650] R4[1721/2400], Temp: 0.1848, Energy: -28.750339-0.001462j
[2025-08-24 11:04:42] [Iter 3973/4650] R4[1722/2400], Temp: 0.1843, Energy: -28.760495+0.000659j
[2025-08-24 11:04:52] [Iter 3974/4650] R4[1723/2400], Temp: 0.1838, Energy: -28.756319+0.000158j
[2025-08-24 11:05:02] [Iter 3975/4650] R4[1724/2400], Temp: 0.1833, Energy: -28.756730+0.000830j
[2025-08-24 11:05:13] [Iter 3976/4650] R4[1725/2400], Temp: 0.1828, Energy: -28.757389-0.001866j
[2025-08-24 11:05:23] [Iter 3977/4650] R4[1726/2400], Temp: 0.1823, Energy: -28.753186+0.000418j
[2025-08-24 11:05:33] [Iter 3978/4650] R4[1727/2400], Temp: 0.1818, Energy: -28.758102+0.000365j
[2025-08-24 11:05:43] [Iter 3979/4650] R4[1728/2400], Temp: 0.1813, Energy: -28.752649-0.000855j
[2025-08-24 11:05:53] [Iter 3980/4650] R4[1729/2400], Temp: 0.1808, Energy: -28.756731+0.000467j
[2025-08-24 11:06:03] [Iter 3981/4650] R4[1730/2400], Temp: 0.1803, Energy: -28.748769-0.001595j
[2025-08-24 11:06:13] [Iter 3982/4650] R4[1731/2400], Temp: 0.1798, Energy: -28.754447+0.000133j
[2025-08-24 11:06:24] [Iter 3983/4650] R4[1732/2400], Temp: 0.1793, Energy: -28.756245+0.001663j
[2025-08-24 11:06:34] [Iter 3984/4650] R4[1733/2400], Temp: 0.1788, Energy: -28.760888+0.000426j
[2025-08-24 11:06:44] [Iter 3985/4650] R4[1734/2400], Temp: 0.1783, Energy: -28.758653+0.000020j
[2025-08-24 11:06:54] [Iter 3986/4650] R4[1735/2400], Temp: 0.1778, Energy: -28.755508-0.000075j
[2025-08-24 11:07:04] [Iter 3987/4650] R4[1736/2400], Temp: 0.1773, Energy: -28.755459-0.001251j
[2025-08-24 11:07:14] [Iter 3988/4650] R4[1737/2400], Temp: 0.1768, Energy: -28.756377+0.002312j
[2025-08-24 11:07:24] [Iter 3989/4650] R4[1738/2400], Temp: 0.1763, Energy: -28.753863-0.000938j
[2025-08-24 11:07:34] [Iter 3990/4650] R4[1739/2400], Temp: 0.1758, Energy: -28.759489+0.001070j
[2025-08-24 11:07:45] [Iter 3991/4650] R4[1740/2400], Temp: 0.1753, Energy: -28.755050+0.000701j
[2025-08-24 11:07:55] [Iter 3992/4650] R4[1741/2400], Temp: 0.1748, Energy: -28.758082+0.000905j
[2025-08-24 11:08:05] [Iter 3993/4650] R4[1742/2400], Temp: 0.1743, Energy: -28.754933-0.000116j
[2025-08-24 11:08:15] [Iter 3994/4650] R4[1743/2400], Temp: 0.1738, Energy: -28.755847-0.002019j
[2025-08-24 11:08:25] [Iter 3995/4650] R4[1744/2400], Temp: 0.1733, Energy: -28.754097-0.002316j
[2025-08-24 11:08:35] [Iter 3996/4650] R4[1745/2400], Temp: 0.1728, Energy: -28.760694+0.000444j
[2025-08-24 11:08:45] [Iter 3997/4650] R4[1746/2400], Temp: 0.1723, Energy: -28.754984-0.001073j
[2025-08-24 11:08:56] [Iter 3998/4650] R4[1747/2400], Temp: 0.1718, Energy: -28.752340+0.001188j
[2025-08-24 11:09:06] [Iter 3999/4650] R4[1748/2400], Temp: 0.1713, Energy: -28.755976+0.000247j
[2025-08-24 11:09:16] [Iter 4000/4650] R4[1749/2400], Temp: 0.1708, Energy: -28.755476+0.000086j
[2025-08-24 11:09:16] ✓ Checkpoint saved: checkpoint_iter_004000.pkl
[2025-08-24 11:09:26] [Iter 4001/4650] R4[1750/2400], Temp: 0.1703, Energy: -28.754221+0.001532j
[2025-08-24 11:09:36] [Iter 4002/4650] R4[1751/2400], Temp: 0.1698, Energy: -28.756003+0.004049j
[2025-08-24 11:09:46] [Iter 4003/4650] R4[1752/2400], Temp: 0.1693, Energy: -28.756943-0.000334j
[2025-08-24 11:09:56] [Iter 4004/4650] R4[1753/2400], Temp: 0.1689, Energy: -28.760085-0.000915j
[2025-08-24 11:10:07] [Iter 4005/4650] R4[1754/2400], Temp: 0.1684, Energy: -28.755052-0.000673j
[2025-08-24 11:10:17] [Iter 4006/4650] R4[1755/2400], Temp: 0.1679, Energy: -28.754124-0.000344j
[2025-08-24 11:10:27] [Iter 4007/4650] R4[1756/2400], Temp: 0.1674, Energy: -28.755156+0.000286j
[2025-08-24 11:10:37] [Iter 4008/4650] R4[1757/2400], Temp: 0.1669, Energy: -28.752623-0.000156j
[2025-08-24 11:10:47] [Iter 4009/4650] R4[1758/2400], Temp: 0.1664, Energy: -28.752059-0.001561j
[2025-08-24 11:10:57] [Iter 4010/4650] R4[1759/2400], Temp: 0.1659, Energy: -28.752487+0.001516j
[2025-08-24 11:11:07] [Iter 4011/4650] R4[1760/2400], Temp: 0.1654, Energy: -28.754522+0.000665j
[2025-08-24 11:11:18] [Iter 4012/4650] R4[1761/2400], Temp: 0.1649, Energy: -28.759304+0.000636j
[2025-08-24 11:11:28] [Iter 4013/4650] R4[1762/2400], Temp: 0.1645, Energy: -28.756396-0.002260j
[2025-08-24 11:11:38] [Iter 4014/4650] R4[1763/2400], Temp: 0.1640, Energy: -28.754445-0.001041j
[2025-08-24 11:11:48] [Iter 4015/4650] R4[1764/2400], Temp: 0.1635, Energy: -28.753493-0.003615j
[2025-08-24 11:11:58] [Iter 4016/4650] R4[1765/2400], Temp: 0.1630, Energy: -28.754647+0.000151j
[2025-08-24 11:12:08] [Iter 4017/4650] R4[1766/2400], Temp: 0.1625, Energy: -28.755374-0.000831j
[2025-08-24 11:12:18] [Iter 4018/4650] R4[1767/2400], Temp: 0.1620, Energy: -28.760672+0.000038j
[2025-08-24 11:12:29] [Iter 4019/4650] R4[1768/2400], Temp: 0.1616, Energy: -28.759254-0.000394j
[2025-08-24 11:12:39] [Iter 4020/4650] R4[1769/2400], Temp: 0.1611, Energy: -28.755010+0.002200j
[2025-08-24 11:12:49] [Iter 4021/4650] R4[1770/2400], Temp: 0.1606, Energy: -28.752220+0.002215j
[2025-08-24 11:12:59] [Iter 4022/4650] R4[1771/2400], Temp: 0.1601, Energy: -28.756265+0.000230j
[2025-08-24 11:13:09] [Iter 4023/4650] R4[1772/2400], Temp: 0.1596, Energy: -28.759299+0.000187j
[2025-08-24 11:13:19] [Iter 4024/4650] R4[1773/2400], Temp: 0.1592, Energy: -28.751802-0.001337j
[2025-08-24 11:13:29] [Iter 4025/4650] R4[1774/2400], Temp: 0.1587, Energy: -28.757084+0.001437j
[2025-08-24 11:13:40] [Iter 4026/4650] R4[1775/2400], Temp: 0.1582, Energy: -28.754824+0.001228j
[2025-08-24 11:13:50] [Iter 4027/4650] R4[1776/2400], Temp: 0.1577, Energy: -28.755615+0.000609j
[2025-08-24 11:14:00] [Iter 4028/4650] R4[1777/2400], Temp: 0.1572, Energy: -28.757234+0.000719j
[2025-08-24 11:14:10] [Iter 4029/4650] R4[1778/2400], Temp: 0.1568, Energy: -28.758531-0.000248j
[2025-08-24 11:14:20] [Iter 4030/4650] R4[1779/2400], Temp: 0.1563, Energy: -28.756668+0.001572j
[2025-08-24 11:14:30] [Iter 4031/4650] R4[1780/2400], Temp: 0.1558, Energy: -28.753245-0.000882j
[2025-08-24 11:14:40] [Iter 4032/4650] R4[1781/2400], Temp: 0.1553, Energy: -28.753916-0.000697j
[2025-08-24 11:14:51] [Iter 4033/4650] R4[1782/2400], Temp: 0.1549, Energy: -28.756145-0.000606j
[2025-08-24 11:15:01] [Iter 4034/4650] R4[1783/2400], Temp: 0.1544, Energy: -28.753478+0.001041j
[2025-08-24 11:15:11] [Iter 4035/4650] R4[1784/2400], Temp: 0.1539, Energy: -28.755917-0.002073j
[2025-08-24 11:15:21] [Iter 4036/4650] R4[1785/2400], Temp: 0.1535, Energy: -28.749670-0.000498j
[2025-08-24 11:15:31] [Iter 4037/4650] R4[1786/2400], Temp: 0.1530, Energy: -28.746488-0.000295j
[2025-08-24 11:15:41] [Iter 4038/4650] R4[1787/2400], Temp: 0.1525, Energy: -28.754421-0.001112j
[2025-08-24 11:15:51] [Iter 4039/4650] R4[1788/2400], Temp: 0.1520, Energy: -28.756978-0.001926j
[2025-08-24 11:16:02] [Iter 4040/4650] R4[1789/2400], Temp: 0.1516, Energy: -28.756304-0.000059j
[2025-08-24 11:16:12] [Iter 4041/4650] R4[1790/2400], Temp: 0.1511, Energy: -28.757684+0.000834j
[2025-08-24 11:16:22] [Iter 4042/4650] R4[1791/2400], Temp: 0.1506, Energy: -28.751639-0.001427j
[2025-08-24 11:16:32] [Iter 4043/4650] R4[1792/2400], Temp: 0.1502, Energy: -28.756532+0.001372j
[2025-08-24 11:16:42] [Iter 4044/4650] R4[1793/2400], Temp: 0.1497, Energy: -28.750868+0.000793j
[2025-08-24 11:16:52] [Iter 4045/4650] R4[1794/2400], Temp: 0.1492, Energy: -28.764045+0.001385j
[2025-08-24 11:17:03] [Iter 4046/4650] R4[1795/2400], Temp: 0.1488, Energy: -28.755895+0.001520j
[2025-08-24 11:17:13] [Iter 4047/4650] R4[1796/2400], Temp: 0.1483, Energy: -28.754611-0.001359j
[2025-08-24 11:17:23] [Iter 4048/4650] R4[1797/2400], Temp: 0.1478, Energy: -28.760867-0.002116j
[2025-08-24 11:17:33] [Iter 4049/4650] R4[1798/2400], Temp: 0.1474, Energy: -28.758040+0.000826j
[2025-08-24 11:17:43] [Iter 4050/4650] R4[1799/2400], Temp: 0.1469, Energy: -28.759684+0.001703j
[2025-08-24 11:17:53] [Iter 4051/4650] R4[1800/2400], Temp: 0.1464, Energy: -28.753046-0.001486j
[2025-08-24 11:18:03] [Iter 4052/4650] R4[1801/2400], Temp: 0.1460, Energy: -28.758053-0.000127j
[2025-08-24 11:18:14] [Iter 4053/4650] R4[1802/2400], Temp: 0.1455, Energy: -28.762701+0.001107j
[2025-08-24 11:18:24] [Iter 4054/4650] R4[1803/2400], Temp: 0.1451, Energy: -28.756414+0.001058j
[2025-08-24 11:18:34] [Iter 4055/4650] R4[1804/2400], Temp: 0.1446, Energy: -28.755179+0.000654j
[2025-08-24 11:18:44] [Iter 4056/4650] R4[1805/2400], Temp: 0.1441, Energy: -28.763858-0.001095j
[2025-08-24 11:18:54] [Iter 4057/4650] R4[1806/2400], Temp: 0.1437, Energy: -28.753834-0.000921j
[2025-08-24 11:19:04] [Iter 4058/4650] R4[1807/2400], Temp: 0.1432, Energy: -28.755821-0.000708j
[2025-08-24 11:19:14] [Iter 4059/4650] R4[1808/2400], Temp: 0.1428, Energy: -28.754322+0.000417j
[2025-08-24 11:19:25] [Iter 4060/4650] R4[1809/2400], Temp: 0.1423, Energy: -28.763019-0.000371j
[2025-08-24 11:19:35] [Iter 4061/4650] R4[1810/2400], Temp: 0.1418, Energy: -28.760400+0.000845j
[2025-08-24 11:19:45] [Iter 4062/4650] R4[1811/2400], Temp: 0.1414, Energy: -28.755966+0.000089j
[2025-08-24 11:19:55] [Iter 4063/4650] R4[1812/2400], Temp: 0.1409, Energy: -28.752610-0.000155j
[2025-08-24 11:20:05] [Iter 4064/4650] R4[1813/2400], Temp: 0.1405, Energy: -28.754022-0.000538j
[2025-08-24 11:20:15] [Iter 4065/4650] R4[1814/2400], Temp: 0.1400, Energy: -28.757374-0.000474j
[2025-08-24 11:20:25] [Iter 4066/4650] R4[1815/2400], Temp: 0.1396, Energy: -28.756518+0.000920j
[2025-08-24 11:20:36] [Iter 4067/4650] R4[1816/2400], Temp: 0.1391, Energy: -28.758030+0.001129j
[2025-08-24 11:20:46] [Iter 4068/4650] R4[1817/2400], Temp: 0.1387, Energy: -28.754630-0.000241j
[2025-08-24 11:20:56] [Iter 4069/4650] R4[1818/2400], Temp: 0.1382, Energy: -28.755850+0.000462j
[2025-08-24 11:21:06] [Iter 4070/4650] R4[1819/2400], Temp: 0.1378, Energy: -28.752372+0.000242j
[2025-08-24 11:21:16] [Iter 4071/4650] R4[1820/2400], Temp: 0.1373, Energy: -28.755047-0.000941j
[2025-08-24 11:21:26] [Iter 4072/4650] R4[1821/2400], Temp: 0.1369, Energy: -28.756148+0.000835j
[2025-08-24 11:21:36] [Iter 4073/4650] R4[1822/2400], Temp: 0.1364, Energy: -28.754762+0.000728j
[2025-08-24 11:21:46] [Iter 4074/4650] R4[1823/2400], Temp: 0.1360, Energy: -28.753020-0.000791j
[2025-08-24 11:21:57] [Iter 4075/4650] R4[1824/2400], Temp: 0.1355, Energy: -28.754262-0.001143j
[2025-08-24 11:22:07] [Iter 4076/4650] R4[1825/2400], Temp: 0.1351, Energy: -28.755682+0.002716j
[2025-08-24 11:22:17] [Iter 4077/4650] R4[1826/2400], Temp: 0.1346, Energy: -28.750874+0.001947j
[2025-08-24 11:22:27] [Iter 4078/4650] R4[1827/2400], Temp: 0.1342, Energy: -28.757889+0.000602j
[2025-08-24 11:22:37] [Iter 4079/4650] R4[1828/2400], Temp: 0.1337, Energy: -28.756696+0.000954j
[2025-08-24 11:22:47] [Iter 4080/4650] R4[1829/2400], Temp: 0.1333, Energy: -28.756758-0.000156j
[2025-08-24 11:22:57] [Iter 4081/4650] R4[1830/2400], Temp: 0.1328, Energy: -28.751203+0.002079j
[2025-08-24 11:23:08] [Iter 4082/4650] R4[1831/2400], Temp: 0.1324, Energy: -28.760075-0.001591j
[2025-08-24 11:23:18] [Iter 4083/4650] R4[1832/2400], Temp: 0.1320, Energy: -28.755194+0.001023j
[2025-08-24 11:23:28] [Iter 4084/4650] R4[1833/2400], Temp: 0.1315, Energy: -28.754263+0.000480j
[2025-08-24 11:23:38] [Iter 4085/4650] R4[1834/2400], Temp: 0.1311, Energy: -28.749789+0.000256j
[2025-08-24 11:23:48] [Iter 4086/4650] R4[1835/2400], Temp: 0.1306, Energy: -28.752739-0.002831j
[2025-08-24 11:23:58] [Iter 4087/4650] R4[1836/2400], Temp: 0.1302, Energy: -28.751790+0.001136j
[2025-08-24 11:24:08] [Iter 4088/4650] R4[1837/2400], Temp: 0.1297, Energy: -28.756245-0.000301j
[2025-08-24 11:24:19] [Iter 4089/4650] R4[1838/2400], Temp: 0.1293, Energy: -28.749920-0.001586j
[2025-08-24 11:24:29] [Iter 4090/4650] R4[1839/2400], Temp: 0.1289, Energy: -28.757030-0.002044j
[2025-08-24 11:24:39] [Iter 4091/4650] R4[1840/2400], Temp: 0.1284, Energy: -28.757027-0.000874j
[2025-08-24 11:24:49] [Iter 4092/4650] R4[1841/2400], Temp: 0.1280, Energy: -28.756483+0.000776j
[2025-08-24 11:24:59] [Iter 4093/4650] R4[1842/2400], Temp: 0.1276, Energy: -28.757256+0.000314j
[2025-08-24 11:25:09] [Iter 4094/4650] R4[1843/2400], Temp: 0.1271, Energy: -28.756805+0.001259j
[2025-08-24 11:25:19] [Iter 4095/4650] R4[1844/2400], Temp: 0.1267, Energy: -28.755288-0.001900j
[2025-08-24 11:25:30] [Iter 4096/4650] R4[1845/2400], Temp: 0.1262, Energy: -28.755650-0.000547j
[2025-08-24 11:25:40] [Iter 4097/4650] R4[1846/2400], Temp: 0.1258, Energy: -28.758236+0.000845j
[2025-08-24 11:25:50] [Iter 4098/4650] R4[1847/2400], Temp: 0.1254, Energy: -28.762996-0.000231j
[2025-08-24 11:26:00] [Iter 4099/4650] R4[1848/2400], Temp: 0.1249, Energy: -28.756970-0.001906j
[2025-08-24 11:26:10] [Iter 4100/4650] R4[1849/2400], Temp: 0.1245, Energy: -28.758876-0.000123j
[2025-08-24 11:26:20] [Iter 4101/4650] R4[1850/2400], Temp: 0.1241, Energy: -28.755046-0.000296j
[2025-08-24 11:26:30] [Iter 4102/4650] R4[1851/2400], Temp: 0.1236, Energy: -28.756721-0.000299j
[2025-08-24 11:26:41] [Iter 4103/4650] R4[1852/2400], Temp: 0.1232, Energy: -28.755899-0.000645j
[2025-08-24 11:26:51] [Iter 4104/4650] R4[1853/2400], Temp: 0.1228, Energy: -28.756037-0.001673j
[2025-08-24 11:27:01] [Iter 4105/4650] R4[1854/2400], Temp: 0.1224, Energy: -28.755761+0.000839j
[2025-08-24 11:27:11] [Iter 4106/4650] R4[1855/2400], Temp: 0.1219, Energy: -28.758719+0.000816j
[2025-08-24 11:27:21] [Iter 4107/4650] R4[1856/2400], Temp: 0.1215, Energy: -28.752989+0.000173j
[2025-08-24 11:27:31] [Iter 4108/4650] R4[1857/2400], Temp: 0.1211, Energy: -28.754090+0.002703j
[2025-08-24 11:27:41] [Iter 4109/4650] R4[1858/2400], Temp: 0.1206, Energy: -28.754599+0.002165j
[2025-08-24 11:27:52] [Iter 4110/4650] R4[1859/2400], Temp: 0.1202, Energy: -28.757164-0.000380j
[2025-08-24 11:28:02] [Iter 4111/4650] R4[1860/2400], Temp: 0.1198, Energy: -28.756624+0.000636j
[2025-08-24 11:28:12] [Iter 4112/4650] R4[1861/2400], Temp: 0.1194, Energy: -28.756149-0.000224j
[2025-08-24 11:28:22] [Iter 4113/4650] R4[1862/2400], Temp: 0.1189, Energy: -28.754852-0.000513j
[2025-08-24 11:28:32] [Iter 4114/4650] R4[1863/2400], Temp: 0.1185, Energy: -28.754535-0.001399j
[2025-08-24 11:28:42] [Iter 4115/4650] R4[1864/2400], Temp: 0.1181, Energy: -28.755287-0.000683j
[2025-08-24 11:28:52] [Iter 4116/4650] R4[1865/2400], Temp: 0.1177, Energy: -28.750113-0.000480j
[2025-08-24 11:29:02] [Iter 4117/4650] R4[1866/2400], Temp: 0.1173, Energy: -28.753451+0.000280j
[2025-08-24 11:29:13] [Iter 4118/4650] R4[1867/2400], Temp: 0.1168, Energy: -28.754898-0.000490j
[2025-08-24 11:29:23] [Iter 4119/4650] R4[1868/2400], Temp: 0.1164, Energy: -28.752507+0.000327j
[2025-08-24 11:29:33] [Iter 4120/4650] R4[1869/2400], Temp: 0.1160, Energy: -28.753036+0.001389j
[2025-08-24 11:29:43] [Iter 4121/4650] R4[1870/2400], Temp: 0.1156, Energy: -28.754282+0.001167j
[2025-08-24 11:29:53] [Iter 4122/4650] R4[1871/2400], Temp: 0.1152, Energy: -28.754134-0.000300j
[2025-08-24 11:30:03] [Iter 4123/4650] R4[1872/2400], Temp: 0.1147, Energy: -28.756574+0.002072j
[2025-08-24 11:30:13] [Iter 4124/4650] R4[1873/2400], Temp: 0.1143, Energy: -28.756777-0.002169j
[2025-08-24 11:30:24] [Iter 4125/4650] R4[1874/2400], Temp: 0.1139, Energy: -28.757178-0.000850j
[2025-08-24 11:30:34] [Iter 4126/4650] R4[1875/2400], Temp: 0.1135, Energy: -28.753960-0.000456j
[2025-08-24 11:30:44] [Iter 4127/4650] R4[1876/2400], Temp: 0.1131, Energy: -28.754769-0.001223j
[2025-08-24 11:30:54] [Iter 4128/4650] R4[1877/2400], Temp: 0.1127, Energy: -28.755564-0.000139j
[2025-08-24 11:31:04] [Iter 4129/4650] R4[1878/2400], Temp: 0.1123, Energy: -28.751680+0.001356j
[2025-08-24 11:31:14] [Iter 4130/4650] R4[1879/2400], Temp: 0.1118, Energy: -28.758776-0.001108j
[2025-08-24 11:31:24] [Iter 4131/4650] R4[1880/2400], Temp: 0.1114, Energy: -28.755384-0.000942j
[2025-08-24 11:31:35] [Iter 4132/4650] R4[1881/2400], Temp: 0.1110, Energy: -28.757373-0.001406j
[2025-08-24 11:31:45] [Iter 4133/4650] R4[1882/2400], Temp: 0.1106, Energy: -28.752203+0.001090j
[2025-08-24 11:31:55] [Iter 4134/4650] R4[1883/2400], Temp: 0.1102, Energy: -28.754795+0.000422j
[2025-08-24 11:32:05] [Iter 4135/4650] R4[1884/2400], Temp: 0.1098, Energy: -28.757417-0.000701j
[2025-08-24 11:32:15] [Iter 4136/4650] R4[1885/2400], Temp: 0.1094, Energy: -28.753297-0.000546j
[2025-08-24 11:32:25] [Iter 4137/4650] R4[1886/2400], Temp: 0.1090, Energy: -28.754965-0.000950j
[2025-08-24 11:32:35] [Iter 4138/4650] R4[1887/2400], Temp: 0.1086, Energy: -28.755322+0.000103j
[2025-08-24 11:32:46] [Iter 4139/4650] R4[1888/2400], Temp: 0.1082, Energy: -28.755851+0.000357j
[2025-08-24 11:32:56] [Iter 4140/4650] R4[1889/2400], Temp: 0.1077, Energy: -28.749452-0.000284j
[2025-08-24 11:33:06] [Iter 4141/4650] R4[1890/2400], Temp: 0.1073, Energy: -28.756883+0.001843j
[2025-08-24 11:33:16] [Iter 4142/4650] R4[1891/2400], Temp: 0.1069, Energy: -28.756385-0.001513j
[2025-08-24 11:33:26] [Iter 4143/4650] R4[1892/2400], Temp: 0.1065, Energy: -28.752292-0.001707j
[2025-08-24 11:33:36] [Iter 4144/4650] R4[1893/2400], Temp: 0.1061, Energy: -28.753040-0.000062j
[2025-08-24 11:33:46] [Iter 4145/4650] R4[1894/2400], Temp: 0.1057, Energy: -28.758424+0.000352j
[2025-08-24 11:33:57] [Iter 4146/4650] R4[1895/2400], Temp: 0.1053, Energy: -28.756516-0.000838j
[2025-08-24 11:34:07] [Iter 4147/4650] R4[1896/2400], Temp: 0.1049, Energy: -28.750310-0.000546j
[2025-08-24 11:34:17] [Iter 4148/4650] R4[1897/2400], Temp: 0.1045, Energy: -28.757027-0.000805j
[2025-08-24 11:34:27] [Iter 4149/4650] R4[1898/2400], Temp: 0.1041, Energy: -28.753893+0.001611j
[2025-08-24 11:34:37] [Iter 4150/4650] R4[1899/2400], Temp: 0.1037, Energy: -28.756054-0.000329j
[2025-08-24 11:34:47] [Iter 4151/4650] R4[1900/2400], Temp: 0.1033, Energy: -28.760567+0.000747j
[2025-08-24 11:34:57] [Iter 4152/4650] R4[1901/2400], Temp: 0.1029, Energy: -28.755934-0.000937j
[2025-08-24 11:35:07] [Iter 4153/4650] R4[1902/2400], Temp: 0.1025, Energy: -28.755878-0.000200j
[2025-08-24 11:35:18] [Iter 4154/4650] R4[1903/2400], Temp: 0.1021, Energy: -28.762109+0.002114j
[2025-08-24 11:35:28] [Iter 4155/4650] R4[1904/2400], Temp: 0.1017, Energy: -28.756266-0.000812j
[2025-08-24 11:35:38] [Iter 4156/4650] R4[1905/2400], Temp: 0.1013, Energy: -28.756782-0.003027j
[2025-08-24 11:35:48] [Iter 4157/4650] R4[1906/2400], Temp: 0.1009, Energy: -28.760687-0.000092j
[2025-08-24 11:35:58] [Iter 4158/4650] R4[1907/2400], Temp: 0.1006, Energy: -28.759857+0.000192j
[2025-08-24 11:36:08] [Iter 4159/4650] R4[1908/2400], Temp: 0.1002, Energy: -28.758435+0.001511j
[2025-08-24 11:36:18] [Iter 4160/4650] R4[1909/2400], Temp: 0.0998, Energy: -28.755565-0.001714j
[2025-08-24 11:36:29] [Iter 4161/4650] R4[1910/2400], Temp: 0.0994, Energy: -28.755323+0.002206j
[2025-08-24 11:36:39] [Iter 4162/4650] R4[1911/2400], Temp: 0.0990, Energy: -28.754302-0.000614j
[2025-08-24 11:36:49] [Iter 4163/4650] R4[1912/2400], Temp: 0.0986, Energy: -28.750502-0.001933j
[2025-08-24 11:36:59] [Iter 4164/4650] R4[1913/2400], Temp: 0.0982, Energy: -28.756131-0.002560j
[2025-08-24 11:37:09] [Iter 4165/4650] R4[1914/2400], Temp: 0.0978, Energy: -28.754075+0.000067j
[2025-08-24 11:37:19] [Iter 4166/4650] R4[1915/2400], Temp: 0.0974, Energy: -28.750693+0.001040j
[2025-08-24 11:37:29] [Iter 4167/4650] R4[1916/2400], Temp: 0.0970, Energy: -28.754844-0.001731j
[2025-08-24 11:37:40] [Iter 4168/4650] R4[1917/2400], Temp: 0.0966, Energy: -28.761658+0.001091j
[2025-08-24 11:37:50] [Iter 4169/4650] R4[1918/2400], Temp: 0.0963, Energy: -28.751983-0.000433j
[2025-08-24 11:38:00] [Iter 4170/4650] R4[1919/2400], Temp: 0.0959, Energy: -28.755323-0.001624j
[2025-08-24 11:38:10] [Iter 4171/4650] R4[1920/2400], Temp: 0.0955, Energy: -28.755865-0.001245j
[2025-08-24 11:38:20] [Iter 4172/4650] R4[1921/2400], Temp: 0.0951, Energy: -28.762042-0.001188j
[2025-08-24 11:38:30] [Iter 4173/4650] R4[1922/2400], Temp: 0.0947, Energy: -28.753611+0.000176j
[2025-08-24 11:38:40] [Iter 4174/4650] R4[1923/2400], Temp: 0.0943, Energy: -28.754706+0.000630j
[2025-08-24 11:38:51] [Iter 4175/4650] R4[1924/2400], Temp: 0.0940, Energy: -28.761338+0.000835j
[2025-08-24 11:39:01] [Iter 4176/4650] R4[1925/2400], Temp: 0.0936, Energy: -28.754148-0.000862j
[2025-08-24 11:39:11] [Iter 4177/4650] R4[1926/2400], Temp: 0.0932, Energy: -28.759058-0.001988j
[2025-08-24 11:39:21] [Iter 4178/4650] R4[1927/2400], Temp: 0.0928, Energy: -28.759933-0.000120j
[2025-08-24 11:39:31] [Iter 4179/4650] R4[1928/2400], Temp: 0.0924, Energy: -28.756418+0.001388j
[2025-08-24 11:39:41] [Iter 4180/4650] R4[1929/2400], Temp: 0.0921, Energy: -28.758497-0.000518j
[2025-08-24 11:39:51] [Iter 4181/4650] R4[1930/2400], Temp: 0.0917, Energy: -28.759133-0.001017j
[2025-08-24 11:40:02] [Iter 4182/4650] R4[1931/2400], Temp: 0.0913, Energy: -28.757803-0.000830j
[2025-08-24 11:40:12] [Iter 4183/4650] R4[1932/2400], Temp: 0.0909, Energy: -28.755238-0.000697j
[2025-08-24 11:40:22] [Iter 4184/4650] R4[1933/2400], Temp: 0.0905, Energy: -28.756296-0.000648j
[2025-08-24 11:40:32] [Iter 4185/4650] R4[1934/2400], Temp: 0.0902, Energy: -28.756992-0.002046j
[2025-08-24 11:40:42] [Iter 4186/4650] R4[1935/2400], Temp: 0.0898, Energy: -28.756575-0.001960j
[2025-08-24 11:40:52] [Iter 4187/4650] R4[1936/2400], Temp: 0.0894, Energy: -28.752930+0.001024j
[2025-08-24 11:41:02] [Iter 4188/4650] R4[1937/2400], Temp: 0.0891, Energy: -28.761207+0.000472j
[2025-08-24 11:41:13] [Iter 4189/4650] R4[1938/2400], Temp: 0.0887, Energy: -28.765054-0.000529j
[2025-08-24 11:41:23] [Iter 4190/4650] R4[1939/2400], Temp: 0.0883, Energy: -28.754625+0.002458j
[2025-08-24 11:41:33] [Iter 4191/4650] R4[1940/2400], Temp: 0.0879, Energy: -28.753099+0.000696j
[2025-08-24 11:41:43] [Iter 4192/4650] R4[1941/2400], Temp: 0.0876, Energy: -28.757958+0.000808j
[2025-08-24 11:41:53] [Iter 4193/4650] R4[1942/2400], Temp: 0.0872, Energy: -28.756409+0.000631j
[2025-08-24 11:42:03] [Iter 4194/4650] R4[1943/2400], Temp: 0.0868, Energy: -28.756034+0.001330j
[2025-08-24 11:42:13] [Iter 4195/4650] R4[1944/2400], Temp: 0.0865, Energy: -28.757279-0.002464j
[2025-08-24 11:42:24] [Iter 4196/4650] R4[1945/2400], Temp: 0.0861, Energy: -28.752421+0.000651j
[2025-08-24 11:42:34] [Iter 4197/4650] R4[1946/2400], Temp: 0.0857, Energy: -28.760364-0.000334j
[2025-08-24 11:42:44] [Iter 4198/4650] R4[1947/2400], Temp: 0.0854, Energy: -28.757116-0.001404j
[2025-08-24 11:42:54] [Iter 4199/4650] R4[1948/2400], Temp: 0.0850, Energy: -28.754253-0.001549j
[2025-08-24 11:43:04] [Iter 4200/4650] R4[1949/2400], Temp: 0.0846, Energy: -28.755918+0.000366j
[2025-08-24 11:43:14] [Iter 4201/4650] R4[1950/2400], Temp: 0.0843, Energy: -28.756861-0.000620j
[2025-08-24 11:43:24] [Iter 4202/4650] R4[1951/2400], Temp: 0.0839, Energy: -28.754221+0.000118j
[2025-08-24 11:43:35] [Iter 4203/4650] R4[1952/2400], Temp: 0.0835, Energy: -28.755504+0.001047j
[2025-08-24 11:43:45] [Iter 4204/4650] R4[1953/2400], Temp: 0.0832, Energy: -28.754591+0.002384j
[2025-08-24 11:43:55] [Iter 4205/4650] R4[1954/2400], Temp: 0.0828, Energy: -28.760772+0.000085j
[2025-08-24 11:44:05] [Iter 4206/4650] R4[1955/2400], Temp: 0.0825, Energy: -28.759385+0.000261j
[2025-08-24 11:44:15] [Iter 4207/4650] R4[1956/2400], Temp: 0.0821, Energy: -28.755869-0.002054j
[2025-08-24 11:44:25] [Iter 4208/4650] R4[1957/2400], Temp: 0.0817, Energy: -28.757258-0.000168j
[2025-08-24 11:44:35] [Iter 4209/4650] R4[1958/2400], Temp: 0.0814, Energy: -28.755651+0.000241j
[2025-08-24 11:44:46] [Iter 4210/4650] R4[1959/2400], Temp: 0.0810, Energy: -28.754823-0.000543j
[2025-08-24 11:44:56] [Iter 4211/4650] R4[1960/2400], Temp: 0.0807, Energy: -28.755263-0.000092j
[2025-08-24 11:45:06] [Iter 4212/4650] R4[1961/2400], Temp: 0.0803, Energy: -28.759183+0.001382j
[2025-08-24 11:45:16] [Iter 4213/4650] R4[1962/2400], Temp: 0.0800, Energy: -28.761703-0.000020j
[2025-08-24 11:45:26] [Iter 4214/4650] R4[1963/2400], Temp: 0.0796, Energy: -28.760677+0.000392j
[2025-08-24 11:45:36] [Iter 4215/4650] R4[1964/2400], Temp: 0.0792, Energy: -28.762110-0.000606j
[2025-08-24 11:45:46] [Iter 4216/4650] R4[1965/2400], Temp: 0.0789, Energy: -28.748563+0.002310j
[2025-08-24 11:45:57] [Iter 4217/4650] R4[1966/2400], Temp: 0.0785, Energy: -28.754194+0.000857j
[2025-08-24 11:46:07] [Iter 4218/4650] R4[1967/2400], Temp: 0.0782, Energy: -28.756222-0.001916j
[2025-08-24 11:46:17] [Iter 4219/4650] R4[1968/2400], Temp: 0.0778, Energy: -28.758200-0.000431j
[2025-08-24 11:46:27] [Iter 4220/4650] R4[1969/2400], Temp: 0.0775, Energy: -28.757303+0.000516j
[2025-08-24 11:46:37] [Iter 4221/4650] R4[1970/2400], Temp: 0.0771, Energy: -28.751300+0.001278j
[2025-08-24 11:46:47] [Iter 4222/4650] R4[1971/2400], Temp: 0.0768, Energy: -28.751512-0.000102j
[2025-08-24 11:46:57] [Iter 4223/4650] R4[1972/2400], Temp: 0.0764, Energy: -28.755998-0.000455j
[2025-08-24 11:47:08] [Iter 4224/4650] R4[1973/2400], Temp: 0.0761, Energy: -28.758911-0.001413j
[2025-08-24 11:47:18] [Iter 4225/4650] R4[1974/2400], Temp: 0.0757, Energy: -28.755839+0.001780j
[2025-08-24 11:47:28] [Iter 4226/4650] R4[1975/2400], Temp: 0.0754, Energy: -28.756613-0.000167j
[2025-08-24 11:47:38] [Iter 4227/4650] R4[1976/2400], Temp: 0.0751, Energy: -28.752233-0.000768j
[2025-08-24 11:47:48] [Iter 4228/4650] R4[1977/2400], Temp: 0.0747, Energy: -28.756587+0.001750j
[2025-08-24 11:47:58] [Iter 4229/4650] R4[1978/2400], Temp: 0.0744, Energy: -28.755053-0.000800j
[2025-08-24 11:48:08] [Iter 4230/4650] R4[1979/2400], Temp: 0.0740, Energy: -28.753775+0.001377j
[2025-08-24 11:48:19] [Iter 4231/4650] R4[1980/2400], Temp: 0.0737, Energy: -28.753316-0.000164j
[2025-08-24 11:48:29] [Iter 4232/4650] R4[1981/2400], Temp: 0.0733, Energy: -28.761843-0.000006j
[2025-08-24 11:48:39] [Iter 4233/4650] R4[1982/2400], Temp: 0.0730, Energy: -28.751551+0.002279j
[2025-08-24 11:48:49] [Iter 4234/4650] R4[1983/2400], Temp: 0.0727, Energy: -28.756406-0.001002j
[2025-08-24 11:48:59] [Iter 4235/4650] R4[1984/2400], Temp: 0.0723, Energy: -28.755487-0.000106j
[2025-08-24 11:49:09] [Iter 4236/4650] R4[1985/2400], Temp: 0.0720, Energy: -28.752151+0.001603j
[2025-08-24 11:49:20] [Iter 4237/4650] R4[1986/2400], Temp: 0.0716, Energy: -28.756511-0.000291j
[2025-08-24 11:49:30] [Iter 4238/4650] R4[1987/2400], Temp: 0.0713, Energy: -28.756078+0.000484j
[2025-08-24 11:49:40] [Iter 4239/4650] R4[1988/2400], Temp: 0.0710, Energy: -28.754931-0.001212j
[2025-08-24 11:49:50] [Iter 4240/4650] R4[1989/2400], Temp: 0.0706, Energy: -28.759523+0.001110j
[2025-08-24 11:50:00] [Iter 4241/4650] R4[1990/2400], Temp: 0.0703, Energy: -28.752629+0.001323j
[2025-08-24 11:50:10] [Iter 4242/4650] R4[1991/2400], Temp: 0.0700, Energy: -28.752250-0.001611j
[2025-08-24 11:50:20] [Iter 4243/4650] R4[1992/2400], Temp: 0.0696, Energy: -28.755564-0.000926j
[2025-08-24 11:50:30] [Iter 4244/4650] R4[1993/2400], Temp: 0.0693, Energy: -28.755693-0.001245j
[2025-08-24 11:50:41] [Iter 4245/4650] R4[1994/2400], Temp: 0.0690, Energy: -28.756682-0.001078j
[2025-08-24 11:50:51] [Iter 4246/4650] R4[1995/2400], Temp: 0.0686, Energy: -28.757174-0.000018j
[2025-08-24 11:51:01] [Iter 4247/4650] R4[1996/2400], Temp: 0.0683, Energy: -28.760132-0.000552j
[2025-08-24 11:51:11] [Iter 4248/4650] R4[1997/2400], Temp: 0.0680, Energy: -28.754698-0.000594j
[2025-08-24 11:51:21] [Iter 4249/4650] R4[1998/2400], Temp: 0.0676, Energy: -28.761178+0.000359j
[2025-08-24 11:51:31] [Iter 4250/4650] R4[1999/2400], Temp: 0.0673, Energy: -28.758269-0.002384j
[2025-08-24 11:51:41] [Iter 4251/4650] R4[2000/2400], Temp: 0.0670, Energy: -28.756252-0.001023j
[2025-08-24 11:51:52] [Iter 4252/4650] R4[2001/2400], Temp: 0.0667, Energy: -28.755761-0.000678j
[2025-08-24 11:52:02] [Iter 4253/4650] R4[2002/2400], Temp: 0.0663, Energy: -28.752683+0.003681j
[2025-08-24 11:52:12] [Iter 4254/4650] R4[2003/2400], Temp: 0.0660, Energy: -28.755102+0.000480j
[2025-08-24 11:52:22] [Iter 4255/4650] R4[2004/2400], Temp: 0.0657, Energy: -28.758355-0.000772j
[2025-08-24 11:52:32] [Iter 4256/4650] R4[2005/2400], Temp: 0.0654, Energy: -28.755185+0.000823j
[2025-08-24 11:52:42] [Iter 4257/4650] R4[2006/2400], Temp: 0.0650, Energy: -28.758193-0.001847j
[2025-08-24 11:52:52] [Iter 4258/4650] R4[2007/2400], Temp: 0.0647, Energy: -28.757600+0.000471j
[2025-08-24 11:53:03] [Iter 4259/4650] R4[2008/2400], Temp: 0.0644, Energy: -28.757001-0.000489j
[2025-08-24 11:53:13] [Iter 4260/4650] R4[2009/2400], Temp: 0.0641, Energy: -28.751086-0.001238j
[2025-08-24 11:53:23] [Iter 4261/4650] R4[2010/2400], Temp: 0.0638, Energy: -28.759349+0.002072j
[2025-08-24 11:53:33] [Iter 4262/4650] R4[2011/2400], Temp: 0.0634, Energy: -28.757247-0.000682j
[2025-08-24 11:53:43] [Iter 4263/4650] R4[2012/2400], Temp: 0.0631, Energy: -28.753791+0.000709j
[2025-08-24 11:53:53] [Iter 4264/4650] R4[2013/2400], Temp: 0.0628, Energy: -28.755802-0.000220j
[2025-08-24 11:54:03] [Iter 4265/4650] R4[2014/2400], Temp: 0.0625, Energy: -28.758496-0.001100j
[2025-08-24 11:54:14] [Iter 4266/4650] R4[2015/2400], Temp: 0.0622, Energy: -28.762957-0.001417j
[2025-08-24 11:54:24] [Iter 4267/4650] R4[2016/2400], Temp: 0.0618, Energy: -28.755175+0.000949j
[2025-08-24 11:54:34] [Iter 4268/4650] R4[2017/2400], Temp: 0.0615, Energy: -28.757512-0.000067j
[2025-08-24 11:54:44] [Iter 4269/4650] R4[2018/2400], Temp: 0.0612, Energy: -28.753040-0.001098j
[2025-08-24 11:54:54] [Iter 4270/4650] R4[2019/2400], Temp: 0.0609, Energy: -28.753892-0.001953j
[2025-08-24 11:55:04] [Iter 4271/4650] R4[2020/2400], Temp: 0.0606, Energy: -28.760908-0.001134j
[2025-08-24 11:55:14] [Iter 4272/4650] R4[2021/2400], Temp: 0.0603, Energy: -28.753591+0.000773j
[2025-08-24 11:55:25] [Iter 4273/4650] R4[2022/2400], Temp: 0.0600, Energy: -28.756760+0.000604j
[2025-08-24 11:55:35] [Iter 4274/4650] R4[2023/2400], Temp: 0.0597, Energy: -28.757788-0.000331j
[2025-08-24 11:55:45] [Iter 4275/4650] R4[2024/2400], Temp: 0.0593, Energy: -28.755027-0.000512j
[2025-08-24 11:55:55] [Iter 4276/4650] R4[2025/2400], Temp: 0.0590, Energy: -28.758904-0.000112j
[2025-08-24 11:56:05] [Iter 4277/4650] R4[2026/2400], Temp: 0.0587, Energy: -28.761267-0.000995j
[2025-08-24 11:56:15] [Iter 4278/4650] R4[2027/2400], Temp: 0.0584, Energy: -28.753944-0.000050j
[2025-08-24 11:56:25] [Iter 4279/4650] R4[2028/2400], Temp: 0.0581, Energy: -28.755558+0.002114j
[2025-08-24 11:56:36] [Iter 4280/4650] R4[2029/2400], Temp: 0.0578, Energy: -28.750830-0.001477j
[2025-08-24 11:56:46] [Iter 4281/4650] R4[2030/2400], Temp: 0.0575, Energy: -28.757470-0.001091j
[2025-08-24 11:56:56] [Iter 4282/4650] R4[2031/2400], Temp: 0.0572, Energy: -28.761416-0.000844j
[2025-08-24 11:57:06] [Iter 4283/4650] R4[2032/2400], Temp: 0.0569, Energy: -28.759634-0.002445j
[2025-08-24 11:57:16] [Iter 4284/4650] R4[2033/2400], Temp: 0.0566, Energy: -28.756773-0.000211j
[2025-08-24 11:57:26] [Iter 4285/4650] R4[2034/2400], Temp: 0.0563, Energy: -28.755057+0.002720j
[2025-08-24 11:57:36] [Iter 4286/4650] R4[2035/2400], Temp: 0.0560, Energy: -28.755038-0.000957j
[2025-08-24 11:57:47] [Iter 4287/4650] R4[2036/2400], Temp: 0.0557, Energy: -28.753180-0.000059j
[2025-08-24 11:57:57] [Iter 4288/4650] R4[2037/2400], Temp: 0.0554, Energy: -28.755896-0.001607j
[2025-08-24 11:58:07] [Iter 4289/4650] R4[2038/2400], Temp: 0.0551, Energy: -28.759254+0.000521j
[2025-08-24 11:58:17] [Iter 4290/4650] R4[2039/2400], Temp: 0.0548, Energy: -28.757443-0.001328j
[2025-08-24 11:58:27] [Iter 4291/4650] R4[2040/2400], Temp: 0.0545, Energy: -28.760678+0.000523j
[2025-08-24 11:58:37] [Iter 4292/4650] R4[2041/2400], Temp: 0.0542, Energy: -28.755967+0.001347j
[2025-08-24 11:58:47] [Iter 4293/4650] R4[2042/2400], Temp: 0.0539, Energy: -28.753597+0.000816j
[2025-08-24 11:58:58] [Iter 4294/4650] R4[2043/2400], Temp: 0.0536, Energy: -28.755454-0.002808j
[2025-08-24 11:59:08] [Iter 4295/4650] R4[2044/2400], Temp: 0.0533, Energy: -28.760933-0.000404j
[2025-08-24 11:59:18] [Iter 4296/4650] R4[2045/2400], Temp: 0.0530, Energy: -28.760187-0.001341j
[2025-08-24 11:59:28] [Iter 4297/4650] R4[2046/2400], Temp: 0.0527, Energy: -28.761175+0.000291j
[2025-08-24 11:59:38] [Iter 4298/4650] R4[2047/2400], Temp: 0.0524, Energy: -28.749969+0.000090j
[2025-08-24 11:59:48] [Iter 4299/4650] R4[2048/2400], Temp: 0.0521, Energy: -28.757349+0.002737j
[2025-08-24 11:59:58] [Iter 4300/4650] R4[2049/2400], Temp: 0.0519, Energy: -28.748696-0.000527j
[2025-08-24 12:00:09] [Iter 4301/4650] R4[2050/2400], Temp: 0.0516, Energy: -28.756767-0.000090j
[2025-08-24 12:00:19] [Iter 4302/4650] R4[2051/2400], Temp: 0.0513, Energy: -28.755279-0.000665j
[2025-08-24 12:00:29] [Iter 4303/4650] R4[2052/2400], Temp: 0.0510, Energy: -28.759021-0.000052j
[2025-08-24 12:00:39] [Iter 4304/4650] R4[2053/2400], Temp: 0.0507, Energy: -28.752454-0.000788j
[2025-08-24 12:00:49] [Iter 4305/4650] R4[2054/2400], Temp: 0.0504, Energy: -28.755590-0.002072j
[2025-08-24 12:00:59] [Iter 4306/4650] R4[2055/2400], Temp: 0.0501, Energy: -28.752161+0.001420j
[2025-08-24 12:01:09] [Iter 4307/4650] R4[2056/2400], Temp: 0.0498, Energy: -28.763106-0.000586j
[2025-08-24 12:01:20] [Iter 4308/4650] R4[2057/2400], Temp: 0.0496, Energy: -28.760371+0.001007j
[2025-08-24 12:01:30] [Iter 4309/4650] R4[2058/2400], Temp: 0.0493, Energy: -28.757123-0.000501j
[2025-08-24 12:01:40] [Iter 4310/4650] R4[2059/2400], Temp: 0.0490, Energy: -28.754722-0.000627j
[2025-08-24 12:01:50] [Iter 4311/4650] R4[2060/2400], Temp: 0.0487, Energy: -28.758238-0.002050j
[2025-08-24 12:02:00] [Iter 4312/4650] R4[2061/2400], Temp: 0.0484, Energy: -28.757105-0.001338j
[2025-08-24 12:02:10] [Iter 4313/4650] R4[2062/2400], Temp: 0.0481, Energy: -28.752086-0.000750j
[2025-08-24 12:02:20] [Iter 4314/4650] R4[2063/2400], Temp: 0.0479, Energy: -28.756539+0.000702j
[2025-08-24 12:02:31] [Iter 4315/4650] R4[2064/2400], Temp: 0.0476, Energy: -28.756628-0.000934j
[2025-08-24 12:02:41] [Iter 4316/4650] R4[2065/2400], Temp: 0.0473, Energy: -28.750181-0.001884j
[2025-08-24 12:02:51] [Iter 4317/4650] R4[2066/2400], Temp: 0.0470, Energy: -28.756363+0.000707j
[2025-08-24 12:03:01] [Iter 4318/4650] R4[2067/2400], Temp: 0.0468, Energy: -28.756364+0.000166j
[2025-08-24 12:03:11] [Iter 4319/4650] R4[2068/2400], Temp: 0.0465, Energy: -28.757121-0.000860j
[2025-08-24 12:03:21] [Iter 4320/4650] R4[2069/2400], Temp: 0.0462, Energy: -28.754751-0.001206j
[2025-08-24 12:03:31] [Iter 4321/4650] R4[2070/2400], Temp: 0.0459, Energy: -28.751560+0.000790j
[2025-08-24 12:03:42] [Iter 4322/4650] R4[2071/2400], Temp: 0.0457, Energy: -28.754822-0.000800j
[2025-08-24 12:03:52] [Iter 4323/4650] R4[2072/2400], Temp: 0.0454, Energy: -28.766092-0.002372j
[2025-08-24 12:04:02] [Iter 4324/4650] R4[2073/2400], Temp: 0.0451, Energy: -28.758285-0.001135j
[2025-08-24 12:04:12] [Iter 4325/4650] R4[2074/2400], Temp: 0.0448, Energy: -28.753529-0.000048j
[2025-08-24 12:04:22] [Iter 4326/4650] R4[2075/2400], Temp: 0.0446, Energy: -28.759325-0.000624j
[2025-08-24 12:04:32] [Iter 4327/4650] R4[2076/2400], Temp: 0.0443, Energy: -28.759067-0.001063j
[2025-08-24 12:04:42] [Iter 4328/4650] R4[2077/2400], Temp: 0.0440, Energy: -28.755489-0.002146j
[2025-08-24 12:04:53] [Iter 4329/4650] R4[2078/2400], Temp: 0.0438, Energy: -28.756167-0.001151j
[2025-08-24 12:05:03] [Iter 4330/4650] R4[2079/2400], Temp: 0.0435, Energy: -28.758115+0.002785j
[2025-08-24 12:05:13] [Iter 4331/4650] R4[2080/2400], Temp: 0.0432, Energy: -28.759744-0.001291j
[2025-08-24 12:05:23] [Iter 4332/4650] R4[2081/2400], Temp: 0.0430, Energy: -28.760321-0.002083j
[2025-08-24 12:05:33] [Iter 4333/4650] R4[2082/2400], Temp: 0.0427, Energy: -28.758965+0.001518j
[2025-08-24 12:05:43] [Iter 4334/4650] R4[2083/2400], Temp: 0.0424, Energy: -28.759820-0.002460j
[2025-08-24 12:05:53] [Iter 4335/4650] R4[2084/2400], Temp: 0.0422, Energy: -28.757244+0.000753j
[2025-08-24 12:06:04] [Iter 4336/4650] R4[2085/2400], Temp: 0.0419, Energy: -28.759356+0.000777j
[2025-08-24 12:06:14] [Iter 4337/4650] R4[2086/2400], Temp: 0.0416, Energy: -28.752882+0.001353j
[2025-08-24 12:06:24] [Iter 4338/4650] R4[2087/2400], Temp: 0.0414, Energy: -28.761492+0.000548j
[2025-08-24 12:06:34] [Iter 4339/4650] R4[2088/2400], Temp: 0.0411, Energy: -28.759302-0.002289j
[2025-08-24 12:06:44] [Iter 4340/4650] R4[2089/2400], Temp: 0.0409, Energy: -28.754832-0.001930j
[2025-08-24 12:06:54] [Iter 4341/4650] R4[2090/2400], Temp: 0.0406, Energy: -28.756264-0.001615j
[2025-08-24 12:07:04] [Iter 4342/4650] R4[2091/2400], Temp: 0.0403, Energy: -28.760086+0.000750j
[2025-08-24 12:07:15] [Iter 4343/4650] R4[2092/2400], Temp: 0.0401, Energy: -28.764716-0.000287j
[2025-08-24 12:07:25] [Iter 4344/4650] R4[2093/2400], Temp: 0.0398, Energy: -28.761924+0.000200j
[2025-08-24 12:07:35] [Iter 4345/4650] R4[2094/2400], Temp: 0.0396, Energy: -28.758447+0.000621j
[2025-08-24 12:07:45] [Iter 4346/4650] R4[2095/2400], Temp: 0.0393, Energy: -28.751190+0.000504j
[2025-08-24 12:07:55] [Iter 4347/4650] R4[2096/2400], Temp: 0.0391, Energy: -28.763366+0.001243j
[2025-08-24 12:08:05] [Iter 4348/4650] R4[2097/2400], Temp: 0.0388, Energy: -28.754586-0.000866j
[2025-08-24 12:08:15] [Iter 4349/4650] R4[2098/2400], Temp: 0.0386, Energy: -28.756096+0.001291j
[2025-08-24 12:08:26] [Iter 4350/4650] R4[2099/2400], Temp: 0.0383, Energy: -28.757784+0.000536j
[2025-08-24 12:08:36] [Iter 4351/4650] R4[2100/2400], Temp: 0.0381, Energy: -28.759929+0.000005j
[2025-08-24 12:08:46] [Iter 4352/4650] R4[2101/2400], Temp: 0.0378, Energy: -28.760709+0.001934j
[2025-08-24 12:08:56] [Iter 4353/4650] R4[2102/2400], Temp: 0.0376, Energy: -28.753997+0.000334j
[2025-08-24 12:09:06] [Iter 4354/4650] R4[2103/2400], Temp: 0.0373, Energy: -28.756755+0.001745j
[2025-08-24 12:09:16] [Iter 4355/4650] R4[2104/2400], Temp: 0.0371, Energy: -28.757638+0.000255j
[2025-08-24 12:09:26] [Iter 4356/4650] R4[2105/2400], Temp: 0.0368, Energy: -28.759047+0.000072j
[2025-08-24 12:09:37] [Iter 4357/4650] R4[2106/2400], Temp: 0.0366, Energy: -28.756452-0.001369j
[2025-08-24 12:09:47] [Iter 4358/4650] R4[2107/2400], Temp: 0.0363, Energy: -28.759373-0.000531j
[2025-08-24 12:09:57] [Iter 4359/4650] R4[2108/2400], Temp: 0.0361, Energy: -28.755016+0.002287j
[2025-08-24 12:10:07] [Iter 4360/4650] R4[2109/2400], Temp: 0.0358, Energy: -28.759316+0.001310j
[2025-08-24 12:10:17] [Iter 4361/4650] R4[2110/2400], Temp: 0.0356, Energy: -28.760804+0.000157j
[2025-08-24 12:10:27] [Iter 4362/4650] R4[2111/2400], Temp: 0.0354, Energy: -28.753080-0.002245j
[2025-08-24 12:10:37] [Iter 4363/4650] R4[2112/2400], Temp: 0.0351, Energy: -28.752845+0.000971j
[2025-08-24 12:10:48] [Iter 4364/4650] R4[2113/2400], Temp: 0.0349, Energy: -28.757413-0.001414j
[2025-08-24 12:10:58] [Iter 4365/4650] R4[2114/2400], Temp: 0.0346, Energy: -28.756220+0.001102j
[2025-08-24 12:11:08] [Iter 4366/4650] R4[2115/2400], Temp: 0.0344, Energy: -28.750778-0.000498j
[2025-08-24 12:11:18] [Iter 4367/4650] R4[2116/2400], Temp: 0.0342, Energy: -28.757765+0.000103j
[2025-08-24 12:11:28] [Iter 4368/4650] R4[2117/2400], Temp: 0.0339, Energy: -28.759169+0.000255j
[2025-08-24 12:11:38] [Iter 4369/4650] R4[2118/2400], Temp: 0.0337, Energy: -28.758677+0.001034j
[2025-08-24 12:11:48] [Iter 4370/4650] R4[2119/2400], Temp: 0.0334, Energy: -28.755354+0.000218j
[2025-08-24 12:11:59] [Iter 4371/4650] R4[2120/2400], Temp: 0.0332, Energy: -28.760352-0.000517j
[2025-08-24 12:12:09] [Iter 4372/4650] R4[2121/2400], Temp: 0.0330, Energy: -28.759726-0.000325j
[2025-08-24 12:12:19] [Iter 4373/4650] R4[2122/2400], Temp: 0.0327, Energy: -28.758480+0.000754j
[2025-08-24 12:12:29] [Iter 4374/4650] R4[2123/2400], Temp: 0.0325, Energy: -28.758730+0.001987j
[2025-08-24 12:12:39] [Iter 4375/4650] R4[2124/2400], Temp: 0.0323, Energy: -28.751743+0.000971j
[2025-08-24 12:12:49] [Iter 4376/4650] R4[2125/2400], Temp: 0.0320, Energy: -28.754410-0.001415j
[2025-08-24 12:12:59] [Iter 4377/4650] R4[2126/2400], Temp: 0.0318, Energy: -28.759742+0.000302j
[2025-08-24 12:13:10] [Iter 4378/4650] R4[2127/2400], Temp: 0.0316, Energy: -28.756169+0.002145j
[2025-08-24 12:13:20] [Iter 4379/4650] R4[2128/2400], Temp: 0.0314, Energy: -28.756036-0.000582j
[2025-08-24 12:13:30] [Iter 4380/4650] R4[2129/2400], Temp: 0.0311, Energy: -28.751804-0.001322j
[2025-08-24 12:13:40] [Iter 4381/4650] R4[2130/2400], Temp: 0.0309, Energy: -28.756630-0.000535j
[2025-08-24 12:13:50] [Iter 4382/4650] R4[2131/2400], Temp: 0.0307, Energy: -28.757722-0.001415j
[2025-08-24 12:14:00] [Iter 4383/4650] R4[2132/2400], Temp: 0.0305, Energy: -28.762882+0.000613j
[2025-08-24 12:14:10] [Iter 4384/4650] R4[2133/2400], Temp: 0.0302, Energy: -28.759224+0.000981j
[2025-08-24 12:14:20] [Iter 4385/4650] R4[2134/2400], Temp: 0.0300, Energy: -28.759663-0.000476j
[2025-08-24 12:14:31] [Iter 4386/4650] R4[2135/2400], Temp: 0.0298, Energy: -28.756429+0.000962j
[2025-08-24 12:14:41] [Iter 4387/4650] R4[2136/2400], Temp: 0.0296, Energy: -28.761961-0.001612j
[2025-08-24 12:14:51] [Iter 4388/4650] R4[2137/2400], Temp: 0.0293, Energy: -28.754662+0.001015j
[2025-08-24 12:15:01] [Iter 4389/4650] R4[2138/2400], Temp: 0.0291, Energy: -28.755904+0.000444j
[2025-08-24 12:15:11] [Iter 4390/4650] R4[2139/2400], Temp: 0.0289, Energy: -28.757525+0.000853j
[2025-08-24 12:15:21] [Iter 4391/4650] R4[2140/2400], Temp: 0.0287, Energy: -28.757520+0.000383j
[2025-08-24 12:15:32] [Iter 4392/4650] R4[2141/2400], Temp: 0.0285, Energy: -28.760125+0.000604j
[2025-08-24 12:15:42] [Iter 4393/4650] R4[2142/2400], Temp: 0.0282, Energy: -28.756410-0.001627j
[2025-08-24 12:15:52] [Iter 4394/4650] R4[2143/2400], Temp: 0.0280, Energy: -28.753192+0.000709j
[2025-08-24 12:16:02] [Iter 4395/4650] R4[2144/2400], Temp: 0.0278, Energy: -28.755250+0.000829j
[2025-08-24 12:16:12] [Iter 4396/4650] R4[2145/2400], Temp: 0.0276, Energy: -28.758976-0.001443j
[2025-08-24 12:16:22] [Iter 4397/4650] R4[2146/2400], Temp: 0.0274, Energy: -28.759784-0.000502j
[2025-08-24 12:16:32] [Iter 4398/4650] R4[2147/2400], Temp: 0.0272, Energy: -28.760742+0.000298j
[2025-08-24 12:16:43] [Iter 4399/4650] R4[2148/2400], Temp: 0.0270, Energy: -28.763578-0.001851j
[2025-08-24 12:16:53] [Iter 4400/4650] R4[2149/2400], Temp: 0.0267, Energy: -28.759611-0.000434j
[2025-08-24 12:17:03] [Iter 4401/4650] R4[2150/2400], Temp: 0.0265, Energy: -28.752865+0.001060j
[2025-08-24 12:17:13] [Iter 4402/4650] R4[2151/2400], Temp: 0.0263, Energy: -28.756870-0.000689j
[2025-08-24 12:17:23] [Iter 4403/4650] R4[2152/2400], Temp: 0.0261, Energy: -28.757994+0.001609j
[2025-08-24 12:17:33] [Iter 4404/4650] R4[2153/2400], Temp: 0.0259, Energy: -28.759182-0.000802j
[2025-08-24 12:17:43] [Iter 4405/4650] R4[2154/2400], Temp: 0.0257, Energy: -28.757550-0.002533j
[2025-08-24 12:17:53] [Iter 4406/4650] R4[2155/2400], Temp: 0.0255, Energy: -28.752921-0.000753j
[2025-08-24 12:18:04] [Iter 4407/4650] R4[2156/2400], Temp: 0.0253, Energy: -28.754627-0.001299j
[2025-08-24 12:18:14] [Iter 4408/4650] R4[2157/2400], Temp: 0.0251, Energy: -28.764508-0.000547j
[2025-08-24 12:18:24] [Iter 4409/4650] R4[2158/2400], Temp: 0.0249, Energy: -28.759496+0.002037j
[2025-08-24 12:18:34] [Iter 4410/4650] R4[2159/2400], Temp: 0.0247, Energy: -28.754125+0.000094j
[2025-08-24 12:18:44] [Iter 4411/4650] R4[2160/2400], Temp: 0.0245, Energy: -28.755877-0.001261j
[2025-08-24 12:18:54] [Iter 4412/4650] R4[2161/2400], Temp: 0.0243, Energy: -28.762136+0.000337j
[2025-08-24 12:19:04] [Iter 4413/4650] R4[2162/2400], Temp: 0.0241, Energy: -28.753941+0.003651j
[2025-08-24 12:19:15] [Iter 4414/4650] R4[2163/2400], Temp: 0.0239, Energy: -28.756141+0.002212j
[2025-08-24 12:19:25] [Iter 4415/4650] R4[2164/2400], Temp: 0.0237, Energy: -28.758378+0.000137j
[2025-08-24 12:19:35] [Iter 4416/4650] R4[2165/2400], Temp: 0.0235, Energy: -28.754323+0.001617j
[2025-08-24 12:19:45] [Iter 4417/4650] R4[2166/2400], Temp: 0.0233, Energy: -28.758628+0.000920j
[2025-08-24 12:19:55] [Iter 4418/4650] R4[2167/2400], Temp: 0.0231, Energy: -28.760843-0.000363j
[2025-08-24 12:20:05] [Iter 4419/4650] R4[2168/2400], Temp: 0.0229, Energy: -28.758974+0.000893j
[2025-08-24 12:20:15] [Iter 4420/4650] R4[2169/2400], Temp: 0.0227, Energy: -28.756701+0.000548j
[2025-08-24 12:20:26] [Iter 4421/4650] R4[2170/2400], Temp: 0.0225, Energy: -28.758202-0.001401j
[2025-08-24 12:20:36] [Iter 4422/4650] R4[2171/2400], Temp: 0.0223, Energy: -28.756043-0.000404j
[2025-08-24 12:20:46] [Iter 4423/4650] R4[2172/2400], Temp: 0.0221, Energy: -28.760699+0.001857j
[2025-08-24 12:20:56] [Iter 4424/4650] R4[2173/2400], Temp: 0.0219, Energy: -28.757530-0.002597j
[2025-08-24 12:21:06] [Iter 4425/4650] R4[2174/2400], Temp: 0.0217, Energy: -28.750377+0.000235j
[2025-08-24 12:21:16] [Iter 4426/4650] R4[2175/2400], Temp: 0.0215, Energy: -28.757916+0.001133j
[2025-08-24 12:21:26] [Iter 4427/4650] R4[2176/2400], Temp: 0.0213, Energy: -28.756690-0.001886j
[2025-08-24 12:21:37] [Iter 4428/4650] R4[2177/2400], Temp: 0.0212, Energy: -28.752406+0.000089j
[2025-08-24 12:21:47] [Iter 4429/4650] R4[2178/2400], Temp: 0.0210, Energy: -28.758881-0.000835j
[2025-08-24 12:21:57] [Iter 4430/4650] R4[2179/2400], Temp: 0.0208, Energy: -28.758479-0.001868j
[2025-08-24 12:22:07] [Iter 4431/4650] R4[2180/2400], Temp: 0.0206, Energy: -28.755807+0.000646j
[2025-08-24 12:22:17] [Iter 4432/4650] R4[2181/2400], Temp: 0.0204, Energy: -28.757750-0.000288j
[2025-08-24 12:22:27] [Iter 4433/4650] R4[2182/2400], Temp: 0.0202, Energy: -28.757978+0.001059j
[2025-08-24 12:22:37] [Iter 4434/4650] R4[2183/2400], Temp: 0.0200, Energy: -28.757599+0.000254j
[2025-08-24 12:22:48] [Iter 4435/4650] R4[2184/2400], Temp: 0.0199, Energy: -28.759379-0.001538j
[2025-08-24 12:22:58] [Iter 4436/4650] R4[2185/2400], Temp: 0.0197, Energy: -28.755957-0.000824j
[2025-08-24 12:23:08] [Iter 4437/4650] R4[2186/2400], Temp: 0.0195, Energy: -28.760360+0.000808j
[2025-08-24 12:23:18] [Iter 4438/4650] R4[2187/2400], Temp: 0.0193, Energy: -28.758025+0.000455j
[2025-08-24 12:23:28] [Iter 4439/4650] R4[2188/2400], Temp: 0.0191, Energy: -28.761783-0.001809j
[2025-08-24 12:23:38] [Iter 4440/4650] R4[2189/2400], Temp: 0.0190, Energy: -28.760749-0.000542j
[2025-08-24 12:23:49] [Iter 4441/4650] R4[2190/2400], Temp: 0.0188, Energy: -28.761410+0.000408j
[2025-08-24 12:23:59] [Iter 4442/4650] R4[2191/2400], Temp: 0.0186, Energy: -28.762125+0.000560j
[2025-08-24 12:24:09] [Iter 4443/4650] R4[2192/2400], Temp: 0.0184, Energy: -28.758838-0.000580j
[2025-08-24 12:24:19] [Iter 4444/4650] R4[2193/2400], Temp: 0.0182, Energy: -28.767023-0.000119j
[2025-08-24 12:24:29] [Iter 4445/4650] R4[2194/2400], Temp: 0.0181, Energy: -28.750706+0.001467j
[2025-08-24 12:24:39] [Iter 4446/4650] R4[2195/2400], Temp: 0.0179, Energy: -28.756358+0.001705j
[2025-08-24 12:24:49] [Iter 4447/4650] R4[2196/2400], Temp: 0.0177, Energy: -28.756342-0.000603j
[2025-08-24 12:25:00] [Iter 4448/4650] R4[2197/2400], Temp: 0.0175, Energy: -28.754592+0.001179j
[2025-08-24 12:25:10] [Iter 4449/4650] R4[2198/2400], Temp: 0.0174, Energy: -28.758871-0.001281j
[2025-08-24 12:25:20] [Iter 4450/4650] R4[2199/2400], Temp: 0.0172, Energy: -28.761420+0.000401j
[2025-08-24 12:25:30] [Iter 4451/4650] R4[2200/2400], Temp: 0.0170, Energy: -28.756586+0.000382j
[2025-08-24 12:25:40] [Iter 4452/4650] R4[2201/2400], Temp: 0.0169, Energy: -28.758740+0.001352j
[2025-08-24 12:25:50] [Iter 4453/4650] R4[2202/2400], Temp: 0.0167, Energy: -28.758479-0.000729j
[2025-08-24 12:26:00] [Iter 4454/4650] R4[2203/2400], Temp: 0.0165, Energy: -28.762027-0.002944j
[2025-08-24 12:26:11] [Iter 4455/4650] R4[2204/2400], Temp: 0.0164, Energy: -28.757340-0.000060j
[2025-08-24 12:26:21] [Iter 4456/4650] R4[2205/2400], Temp: 0.0162, Energy: -28.756988-0.001223j
[2025-08-24 12:26:31] [Iter 4457/4650] R4[2206/2400], Temp: 0.0160, Energy: -28.759146+0.001356j
[2025-08-24 12:26:41] [Iter 4458/4650] R4[2207/2400], Temp: 0.0159, Energy: -28.757656-0.000108j
[2025-08-24 12:26:51] [Iter 4459/4650] R4[2208/2400], Temp: 0.0157, Energy: -28.758037+0.001016j
[2025-08-24 12:27:01] [Iter 4460/4650] R4[2209/2400], Temp: 0.0155, Energy: -28.755878-0.001981j
[2025-08-24 12:27:11] [Iter 4461/4650] R4[2210/2400], Temp: 0.0154, Energy: -28.754586-0.003068j
[2025-08-24 12:27:22] [Iter 4462/4650] R4[2211/2400], Temp: 0.0152, Energy: -28.756482-0.002188j
[2025-08-24 12:27:32] [Iter 4463/4650] R4[2212/2400], Temp: 0.0151, Energy: -28.760010+0.001068j
[2025-08-24 12:27:42] [Iter 4464/4650] R4[2213/2400], Temp: 0.0149, Energy: -28.757400+0.001945j
[2025-08-24 12:27:52] [Iter 4465/4650] R4[2214/2400], Temp: 0.0147, Energy: -28.760319+0.001921j
[2025-08-24 12:28:02] [Iter 4466/4650] R4[2215/2400], Temp: 0.0146, Energy: -28.757272+0.001388j
[2025-08-24 12:28:12] [Iter 4467/4650] R4[2216/2400], Temp: 0.0144, Energy: -28.762057+0.002279j
[2025-08-24 12:28:22] [Iter 4468/4650] R4[2217/2400], Temp: 0.0143, Energy: -28.757951-0.000044j
[2025-08-24 12:28:33] [Iter 4469/4650] R4[2218/2400], Temp: 0.0141, Energy: -28.758595+0.000495j
[2025-08-24 12:28:43] [Iter 4470/4650] R4[2219/2400], Temp: 0.0140, Energy: -28.762930+0.000682j
[2025-08-24 12:28:53] [Iter 4471/4650] R4[2220/2400], Temp: 0.0138, Energy: -28.758079-0.000787j
[2025-08-24 12:29:03] [Iter 4472/4650] R4[2221/2400], Temp: 0.0137, Energy: -28.758452-0.001601j
[2025-08-24 12:29:13] [Iter 4473/4650] R4[2222/2400], Temp: 0.0135, Energy: -28.754748+0.000474j
[2025-08-24 12:29:23] [Iter 4474/4650] R4[2223/2400], Temp: 0.0134, Energy: -28.761574-0.002263j
[2025-08-24 12:29:33] [Iter 4475/4650] R4[2224/2400], Temp: 0.0132, Energy: -28.761505-0.000478j
[2025-08-24 12:29:44] [Iter 4476/4650] R4[2225/2400], Temp: 0.0131, Energy: -28.765299-0.000245j
[2025-08-24 12:29:54] [Iter 4477/4650] R4[2226/2400], Temp: 0.0129, Energy: -28.761324+0.000192j
[2025-08-24 12:30:04] [Iter 4478/4650] R4[2227/2400], Temp: 0.0128, Energy: -28.757715+0.000639j
[2025-08-24 12:30:14] [Iter 4479/4650] R4[2228/2400], Temp: 0.0126, Energy: -28.752365-0.000495j
[2025-08-24 12:30:24] [Iter 4480/4650] R4[2229/2400], Temp: 0.0125, Energy: -28.760421-0.000218j
[2025-08-24 12:30:34] [Iter 4481/4650] R4[2230/2400], Temp: 0.0123, Energy: -28.757904-0.000721j
[2025-08-24 12:30:44] [Iter 4482/4650] R4[2231/2400], Temp: 0.0122, Energy: -28.760179+0.000375j
[2025-08-24 12:30:55] [Iter 4483/4650] R4[2232/2400], Temp: 0.0120, Energy: -28.761146-0.001966j
[2025-08-24 12:31:05] [Iter 4484/4650] R4[2233/2400], Temp: 0.0119, Energy: -28.759445-0.001775j
[2025-08-24 12:31:15] [Iter 4485/4650] R4[2234/2400], Temp: 0.0118, Energy: -28.755899+0.000194j
[2025-08-24 12:31:25] [Iter 4486/4650] R4[2235/2400], Temp: 0.0116, Energy: -28.755618+0.000146j
[2025-08-24 12:31:35] [Iter 4487/4650] R4[2236/2400], Temp: 0.0115, Energy: -28.759875+0.000637j
[2025-08-24 12:31:45] [Iter 4488/4650] R4[2237/2400], Temp: 0.0113, Energy: -28.757879+0.000918j
[2025-08-24 12:31:55] [Iter 4489/4650] R4[2238/2400], Temp: 0.0112, Energy: -28.759224+0.000371j
[2025-08-24 12:32:06] [Iter 4490/4650] R4[2239/2400], Temp: 0.0111, Energy: -28.758493-0.000247j
[2025-08-24 12:32:16] [Iter 4491/4650] R4[2240/2400], Temp: 0.0109, Energy: -28.761193-0.001665j
[2025-08-24 12:32:26] [Iter 4492/4650] R4[2241/2400], Temp: 0.0108, Energy: -28.755797+0.000292j
[2025-08-24 12:32:36] [Iter 4493/4650] R4[2242/2400], Temp: 0.0107, Energy: -28.759351+0.001919j
[2025-08-24 12:32:46] [Iter 4494/4650] R4[2243/2400], Temp: 0.0105, Energy: -28.761470-0.000160j
[2025-08-24 12:32:56] [Iter 4495/4650] R4[2244/2400], Temp: 0.0104, Energy: -28.758002+0.002663j
[2025-08-24 12:33:06] [Iter 4496/4650] R4[2245/2400], Temp: 0.0103, Energy: -28.760345+0.001055j
[2025-08-24 12:33:17] [Iter 4497/4650] R4[2246/2400], Temp: 0.0101, Energy: -28.764630-0.001011j
[2025-08-24 12:33:27] [Iter 4498/4650] R4[2247/2400], Temp: 0.0100, Energy: -28.757757+0.001141j
[2025-08-24 12:33:37] [Iter 4499/4650] R4[2248/2400], Temp: 0.0099, Energy: -28.752934-0.000716j
[2025-08-24 12:33:47] [Iter 4500/4650] R4[2249/2400], Temp: 0.0097, Energy: -28.757573-0.000601j
[2025-08-24 12:33:47] ✓ Checkpoint saved: checkpoint_iter_004500.pkl
[2025-08-24 12:33:57] [Iter 4501/4650] R4[2250/2400], Temp: 0.0096, Energy: -28.765593-0.001164j
[2025-08-24 12:34:07] [Iter 4502/4650] R4[2251/2400], Temp: 0.0095, Energy: -28.760765+0.000020j
[2025-08-24 12:34:17] [Iter 4503/4650] R4[2252/2400], Temp: 0.0094, Energy: -28.758492+0.001233j
[2025-08-24 12:34:28] [Iter 4504/4650] R4[2253/2400], Temp: 0.0092, Energy: -28.756709-0.000573j
[2025-08-24 12:34:38] [Iter 4505/4650] R4[2254/2400], Temp: 0.0091, Energy: -28.759745+0.000708j
[2025-08-24 12:34:48] [Iter 4506/4650] R4[2255/2400], Temp: 0.0090, Energy: -28.765798+0.000271j
[2025-08-24 12:34:58] [Iter 4507/4650] R4[2256/2400], Temp: 0.0089, Energy: -28.760525-0.000085j
[2025-08-24 12:35:08] [Iter 4508/4650] R4[2257/2400], Temp: 0.0087, Energy: -28.757125-0.000468j
[2025-08-24 12:35:18] [Iter 4509/4650] R4[2258/2400], Temp: 0.0086, Energy: -28.756061+0.000549j
[2025-08-24 12:35:28] [Iter 4510/4650] R4[2259/2400], Temp: 0.0085, Energy: -28.753980+0.001609j
[2025-08-24 12:35:39] [Iter 4511/4650] R4[2260/2400], Temp: 0.0084, Energy: -28.758822-0.000234j
[2025-08-24 12:35:49] [Iter 4512/4650] R4[2261/2400], Temp: 0.0083, Energy: -28.752240+0.001136j
[2025-08-24 12:35:59] [Iter 4513/4650] R4[2262/2400], Temp: 0.0081, Energy: -28.753219+0.000596j
[2025-08-24 12:36:09] [Iter 4514/4650] R4[2263/2400], Temp: 0.0080, Energy: -28.755693-0.001638j
[2025-08-24 12:36:19] [Iter 4515/4650] R4[2264/2400], Temp: 0.0079, Energy: -28.759945+0.001288j
[2025-08-24 12:36:29] [Iter 4516/4650] R4[2265/2400], Temp: 0.0078, Energy: -28.757769-0.000323j
[2025-08-24 12:36:39] [Iter 4517/4650] R4[2266/2400], Temp: 0.0077, Energy: -28.758556-0.001725j
[2025-08-24 12:36:50] [Iter 4518/4650] R4[2267/2400], Temp: 0.0076, Energy: -28.755116+0.003523j
[2025-08-24 12:37:00] [Iter 4519/4650] R4[2268/2400], Temp: 0.0074, Energy: -28.754625-0.000509j
[2025-08-24 12:37:10] [Iter 4520/4650] R4[2269/2400], Temp: 0.0073, Energy: -28.756360-0.001186j
[2025-08-24 12:37:20] [Iter 4521/4650] R4[2270/2400], Temp: 0.0072, Energy: -28.759863-0.001132j
[2025-08-24 12:37:30] [Iter 4522/4650] R4[2271/2400], Temp: 0.0071, Energy: -28.759064-0.001074j
[2025-08-24 12:37:40] [Iter 4523/4650] R4[2272/2400], Temp: 0.0070, Energy: -28.754858+0.000426j
[2025-08-24 12:37:50] [Iter 4524/4650] R4[2273/2400], Temp: 0.0069, Energy: -28.754047-0.001787j
[2025-08-24 12:38:01] [Iter 4525/4650] R4[2274/2400], Temp: 0.0068, Energy: -28.752078+0.001049j
[2025-08-24 12:38:11] [Iter 4526/4650] R4[2275/2400], Temp: 0.0067, Energy: -28.756361-0.000057j
[2025-08-24 12:38:21] [Iter 4527/4650] R4[2276/2400], Temp: 0.0066, Energy: -28.763602+0.003415j
[2025-08-24 12:38:31] [Iter 4528/4650] R4[2277/2400], Temp: 0.0065, Energy: -28.755589+0.002658j
[2025-08-24 12:38:41] [Iter 4529/4650] R4[2278/2400], Temp: 0.0064, Energy: -28.758340+0.002013j
[2025-08-24 12:38:51] [Iter 4530/4650] R4[2279/2400], Temp: 0.0063, Energy: -28.757495+0.000158j
[2025-08-24 12:39:01] [Iter 4531/4650] R4[2280/2400], Temp: 0.0062, Energy: -28.753717-0.000578j
[2025-08-24 12:39:12] [Iter 4532/4650] R4[2281/2400], Temp: 0.0061, Energy: -28.760335+0.000122j
[2025-08-24 12:39:22] [Iter 4533/4650] R4[2282/2400], Temp: 0.0060, Energy: -28.766545-0.000358j
[2025-08-24 12:39:32] [Iter 4534/4650] R4[2283/2400], Temp: 0.0059, Energy: -28.756217-0.000462j
[2025-08-24 12:39:42] [Iter 4535/4650] R4[2284/2400], Temp: 0.0058, Energy: -28.756034+0.000794j
[2025-08-24 12:39:52] [Iter 4536/4650] R4[2285/2400], Temp: 0.0057, Energy: -28.755210+0.000591j
[2025-08-24 12:40:02] [Iter 4537/4650] R4[2286/2400], Temp: 0.0056, Energy: -28.758984+0.000278j
[2025-08-24 12:40:12] [Iter 4538/4650] R4[2287/2400], Temp: 0.0055, Energy: -28.752559+0.000420j
[2025-08-24 12:40:23] [Iter 4539/4650] R4[2288/2400], Temp: 0.0054, Energy: -28.760767-0.000864j
[2025-08-24 12:40:33] [Iter 4540/4650] R4[2289/2400], Temp: 0.0053, Energy: -28.752275+0.000536j
[2025-08-24 12:40:43] [Iter 4541/4650] R4[2290/2400], Temp: 0.0052, Energy: -28.758639-0.001745j
[2025-08-24 12:40:53] [Iter 4542/4650] R4[2291/2400], Temp: 0.0051, Energy: -28.761087-0.001518j
[2025-08-24 12:41:03] [Iter 4543/4650] R4[2292/2400], Temp: 0.0050, Energy: -28.758366-0.002802j
[2025-08-24 12:41:13] [Iter 4544/4650] R4[2293/2400], Temp: 0.0049, Energy: -28.756894+0.000957j
[2025-08-24 12:41:23] [Iter 4545/4650] R4[2294/2400], Temp: 0.0048, Energy: -28.762829-0.003166j
[2025-08-24 12:41:34] [Iter 4546/4650] R4[2295/2400], Temp: 0.0047, Energy: -28.760357-0.000290j
[2025-08-24 12:41:44] [Iter 4547/4650] R4[2296/2400], Temp: 0.0046, Energy: -28.757886+0.000487j
[2025-08-24 12:41:54] [Iter 4548/4650] R4[2297/2400], Temp: 0.0045, Energy: -28.758471-0.000014j
[2025-08-24 12:42:04] [Iter 4549/4650] R4[2298/2400], Temp: 0.0045, Energy: -28.756443-0.000807j
[2025-08-24 12:42:14] [Iter 4550/4650] R4[2299/2400], Temp: 0.0044, Energy: -28.757953+0.000476j
[2025-08-24 12:42:24] [Iter 4551/4650] R4[2300/2400], Temp: 0.0043, Energy: -28.760453-0.000340j
[2025-08-24 12:42:34] [Iter 4552/4650] R4[2301/2400], Temp: 0.0042, Energy: -28.759961-0.001358j
[2025-08-24 12:42:45] [Iter 4553/4650] R4[2302/2400], Temp: 0.0041, Energy: -28.758546+0.001604j
[2025-08-24 12:42:55] [Iter 4554/4650] R4[2303/2400], Temp: 0.0040, Energy: -28.755759+0.001562j
[2025-08-24 12:43:05] [Iter 4555/4650] R4[2304/2400], Temp: 0.0039, Energy: -28.763557+0.002024j
[2025-08-24 12:43:15] [Iter 4556/4650] R4[2305/2400], Temp: 0.0039, Energy: -28.759118+0.000110j
[2025-08-24 12:43:25] [Iter 4557/4650] R4[2306/2400], Temp: 0.0038, Energy: -28.762287-0.000974j
[2025-08-24 12:43:35] [Iter 4558/4650] R4[2307/2400], Temp: 0.0037, Energy: -28.756943+0.000100j
[2025-08-24 12:43:46] [Iter 4559/4650] R4[2308/2400], Temp: 0.0036, Energy: -28.763592-0.001816j
[2025-08-24 12:43:56] [Iter 4560/4650] R4[2309/2400], Temp: 0.0035, Energy: -28.760084-0.000800j
[2025-08-24 12:44:06] [Iter 4561/4650] R4[2310/2400], Temp: 0.0035, Energy: -28.756831-0.001433j
[2025-08-24 12:44:16] [Iter 4562/4650] R4[2311/2400], Temp: 0.0034, Energy: -28.754052-0.001086j
[2025-08-24 12:44:26] [Iter 4563/4650] R4[2312/2400], Temp: 0.0033, Energy: -28.757818+0.001868j
[2025-08-24 12:44:36] [Iter 4564/4650] R4[2313/2400], Temp: 0.0032, Energy: -28.760550+0.000994j
[2025-08-24 12:44:46] [Iter 4565/4650] R4[2314/2400], Temp: 0.0032, Energy: -28.759986-0.000701j
[2025-08-24 12:44:57] [Iter 4566/4650] R4[2315/2400], Temp: 0.0031, Energy: -28.761300-0.000747j
[2025-08-24 12:45:07] [Iter 4567/4650] R4[2316/2400], Temp: 0.0030, Energy: -28.756433-0.000673j
[2025-08-24 12:45:17] [Iter 4568/4650] R4[2317/2400], Temp: 0.0029, Energy: -28.757128-0.000729j
[2025-08-24 12:45:27] [Iter 4569/4650] R4[2318/2400], Temp: 0.0029, Energy: -28.762227-0.000603j
[2025-08-24 12:45:37] [Iter 4570/4650] R4[2319/2400], Temp: 0.0028, Energy: -28.758795+0.000265j
[2025-08-24 12:45:47] [Iter 4571/4650] R4[2320/2400], Temp: 0.0027, Energy: -28.760517+0.001080j
[2025-08-24 12:45:57] [Iter 4572/4650] R4[2321/2400], Temp: 0.0027, Energy: -28.756771-0.000537j
[2025-08-24 12:46:08] [Iter 4573/4650] R4[2322/2400], Temp: 0.0026, Energy: -28.758409+0.001789j
[2025-08-24 12:46:18] [Iter 4574/4650] R4[2323/2400], Temp: 0.0025, Energy: -28.760192-0.000493j
[2025-08-24 12:46:28] [Iter 4575/4650] R4[2324/2400], Temp: 0.0025, Energy: -28.762338-0.000378j
[2025-08-24 12:46:38] [Iter 4576/4650] R4[2325/2400], Temp: 0.0024, Energy: -28.759536-0.000581j
[2025-08-24 12:46:48] [Iter 4577/4650] R4[2326/2400], Temp: 0.0023, Energy: -28.757139-0.000764j
[2025-08-24 12:46:58] [Iter 4578/4650] R4[2327/2400], Temp: 0.0023, Energy: -28.761340-0.003279j
[2025-08-24 12:47:08] [Iter 4579/4650] R4[2328/2400], Temp: 0.0022, Energy: -28.757515+0.001178j
[2025-08-24 12:47:19] [Iter 4580/4650] R4[2329/2400], Temp: 0.0022, Energy: -28.759789+0.000993j
[2025-08-24 12:47:29] [Iter 4581/4650] R4[2330/2400], Temp: 0.0021, Energy: -28.758156+0.001020j
[2025-08-24 12:47:39] [Iter 4582/4650] R4[2331/2400], Temp: 0.0020, Energy: -28.764521+0.000519j
[2025-08-24 12:47:49] [Iter 4583/4650] R4[2332/2400], Temp: 0.0020, Energy: -28.754586-0.001615j
[2025-08-24 12:47:59] [Iter 4584/4650] R4[2333/2400], Temp: 0.0019, Energy: -28.759677-0.000274j
[2025-08-24 12:48:09] [Iter 4585/4650] R4[2334/2400], Temp: 0.0019, Energy: -28.762120+0.001207j
[2025-08-24 12:48:19] [Iter 4586/4650] R4[2335/2400], Temp: 0.0018, Energy: -28.760738+0.000234j
[2025-08-24 12:48:30] [Iter 4587/4650] R4[2336/2400], Temp: 0.0018, Energy: -28.758903+0.000369j
[2025-08-24 12:48:40] [Iter 4588/4650] R4[2337/2400], Temp: 0.0017, Energy: -28.756844+0.001014j
[2025-08-24 12:48:50] [Iter 4589/4650] R4[2338/2400], Temp: 0.0016, Energy: -28.756293-0.000133j
[2025-08-24 12:49:00] [Iter 4590/4650] R4[2339/2400], Temp: 0.0016, Energy: -28.754163-0.000755j
[2025-08-24 12:49:10] [Iter 4591/4650] R4[2340/2400], Temp: 0.0015, Energy: -28.758042+0.001055j
[2025-08-24 12:49:20] [Iter 4592/4650] R4[2341/2400], Temp: 0.0015, Energy: -28.757337-0.003369j
[2025-08-24 12:49:30] [Iter 4593/4650] R4[2342/2400], Temp: 0.0014, Energy: -28.762611+0.000574j
[2025-08-24 12:49:41] [Iter 4594/4650] R4[2343/2400], Temp: 0.0014, Energy: -28.758014+0.000488j
[2025-08-24 12:49:51] [Iter 4595/4650] R4[2344/2400], Temp: 0.0013, Energy: -28.757851+0.000367j
[2025-08-24 12:50:01] [Iter 4596/4650] R4[2345/2400], Temp: 0.0013, Energy: -28.758313-0.000114j
[2025-08-24 12:50:11] [Iter 4597/4650] R4[2346/2400], Temp: 0.0012, Energy: -28.756540-0.000266j
[2025-08-24 12:50:21] [Iter 4598/4650] R4[2347/2400], Temp: 0.0012, Energy: -28.764872+0.000257j
[2025-08-24 12:50:31] [Iter 4599/4650] R4[2348/2400], Temp: 0.0012, Energy: -28.761566+0.000911j
[2025-08-24 12:50:41] [Iter 4600/4650] R4[2349/2400], Temp: 0.0011, Energy: -28.756862+0.000797j
[2025-08-24 12:50:52] [Iter 4601/4650] R4[2350/2400], Temp: 0.0011, Energy: -28.763169-0.001132j
[2025-08-24 12:51:02] [Iter 4602/4650] R4[2351/2400], Temp: 0.0010, Energy: -28.754786+0.000463j
[2025-08-24 12:51:12] [Iter 4603/4650] R4[2352/2400], Temp: 0.0010, Energy: -28.757208+0.000338j
[2025-08-24 12:51:23] [Iter 4604/4650] R4[2353/2400], Temp: 0.0009, Energy: -28.761181+0.002575j
[2025-08-24 12:51:33] [Iter 4605/4650] R4[2354/2400], Temp: 0.0009, Energy: -28.754050-0.001350j
[2025-08-24 12:51:43] [Iter 4606/4650] R4[2355/2400], Temp: 0.0009, Energy: -28.759675-0.000653j
[2025-08-24 12:51:53] [Iter 4607/4650] R4[2356/2400], Temp: 0.0008, Energy: -28.755112-0.000854j
[2025-08-24 12:52:03] [Iter 4608/4650] R4[2357/2400], Temp: 0.0008, Energy: -28.762092+0.000136j
[2025-08-24 12:52:13] [Iter 4609/4650] R4[2358/2400], Temp: 0.0008, Energy: -28.756639-0.000827j
[2025-08-24 12:52:23] [Iter 4610/4650] R4[2359/2400], Temp: 0.0007, Energy: -28.756559-0.001639j
[2025-08-24 12:52:34] [Iter 4611/4650] R4[2360/2400], Temp: 0.0007, Energy: -28.758550-0.000838j
[2025-08-24 12:52:44] [Iter 4612/4650] R4[2361/2400], Temp: 0.0007, Energy: -28.756205-0.000029j
[2025-08-24 12:52:54] [Iter 4613/4650] R4[2362/2400], Temp: 0.0006, Energy: -28.757804+0.000404j
[2025-08-24 12:53:04] [Iter 4614/4650] R4[2363/2400], Temp: 0.0006, Energy: -28.758972+0.001114j
[2025-08-24 12:53:14] [Iter 4615/4650] R4[2364/2400], Temp: 0.0006, Energy: -28.755335+0.000063j
[2025-08-24 12:53:24] [Iter 4616/4650] R4[2365/2400], Temp: 0.0005, Energy: -28.754632+0.001302j
[2025-08-24 12:53:34] [Iter 4617/4650] R4[2366/2400], Temp: 0.0005, Energy: -28.758502-0.000002j
[2025-08-24 12:53:45] [Iter 4618/4650] R4[2367/2400], Temp: 0.0005, Energy: -28.759194-0.001197j
[2025-08-24 12:53:55] [Iter 4619/4650] R4[2368/2400], Temp: 0.0004, Energy: -28.759347-0.000471j
[2025-08-24 12:54:05] [Iter 4620/4650] R4[2369/2400], Temp: 0.0004, Energy: -28.756644+0.001742j
[2025-08-24 12:54:15] [Iter 4621/4650] R4[2370/2400], Temp: 0.0004, Energy: -28.756175-0.003814j
[2025-08-24 12:54:25] [Iter 4622/4650] R4[2371/2400], Temp: 0.0004, Energy: -28.758912-0.000224j
[2025-08-24 12:54:35] [Iter 4623/4650] R4[2372/2400], Temp: 0.0003, Energy: -28.758906-0.000195j
[2025-08-24 12:54:45] [Iter 4624/4650] R4[2373/2400], Temp: 0.0003, Energy: -28.763583-0.000260j
[2025-08-24 12:54:56] [Iter 4625/4650] R4[2374/2400], Temp: 0.0003, Energy: -28.760999+0.001691j
[2025-08-24 12:55:06] [Iter 4626/4650] R4[2375/2400], Temp: 0.0003, Energy: -28.764520-0.000714j
[2025-08-24 12:55:16] [Iter 4627/4650] R4[2376/2400], Temp: 0.0002, Energy: -28.766754+0.002565j
[2025-08-24 12:55:26] [Iter 4628/4650] R4[2377/2400], Temp: 0.0002, Energy: -28.751303-0.000765j
[2025-08-24 12:55:36] [Iter 4629/4650] R4[2378/2400], Temp: 0.0002, Energy: -28.755775-0.001533j
[2025-08-24 12:55:46] [Iter 4630/4650] R4[2379/2400], Temp: 0.0002, Energy: -28.756064-0.000541j
[2025-08-24 12:55:57] [Iter 4631/4650] R4[2380/2400], Temp: 0.0002, Energy: -28.759246+0.002371j
[2025-08-24 12:56:07] [Iter 4632/4650] R4[2381/2400], Temp: 0.0002, Energy: -28.760715+0.000593j
[2025-08-24 12:56:17] [Iter 4633/4650] R4[2382/2400], Temp: 0.0001, Energy: -28.758503-0.000363j
[2025-08-24 12:56:27] [Iter 4634/4650] R4[2383/2400], Temp: 0.0001, Energy: -28.758023+0.000467j
[2025-08-24 12:56:37] [Iter 4635/4650] R4[2384/2400], Temp: 0.0001, Energy: -28.761108-0.001446j
[2025-08-24 12:56:47] [Iter 4636/4650] R4[2385/2400], Temp: 0.0001, Energy: -28.756829+0.002081j
[2025-08-24 12:56:57] [Iter 4637/4650] R4[2386/2400], Temp: 0.0001, Energy: -28.757386-0.000561j
[2025-08-24 12:57:08] [Iter 4638/4650] R4[2387/2400], Temp: 0.0001, Energy: -28.759669+0.000677j
[2025-08-24 12:57:18] [Iter 4639/4650] R4[2388/2400], Temp: 0.0001, Energy: -28.755156+0.001008j
[2025-08-24 12:57:28] [Iter 4640/4650] R4[2389/2400], Temp: 0.0001, Energy: -28.761284+0.000543j
[2025-08-24 12:57:38] [Iter 4641/4650] R4[2390/2400], Temp: 0.0000, Energy: -28.757957+0.001207j
[2025-08-24 12:57:48] [Iter 4642/4650] R4[2391/2400], Temp: 0.0000, Energy: -28.756235+0.003714j
[2025-08-24 12:57:58] [Iter 4643/4650] R4[2392/2400], Temp: 0.0000, Energy: -28.758639+0.000778j
[2025-08-24 12:58:08] [Iter 4644/4650] R4[2393/2400], Temp: 0.0000, Energy: -28.759297+0.000531j
[2025-08-24 12:58:19] [Iter 4645/4650] R4[2394/2400], Temp: 0.0000, Energy: -28.759225-0.000857j
[2025-08-24 12:58:29] [Iter 4646/4650] R4[2395/2400], Temp: 0.0000, Energy: -28.758386+0.001789j
[2025-08-24 12:58:39] [Iter 4647/4650] R4[2396/2400], Temp: 0.0000, Energy: -28.761041-0.000081j
[2025-08-24 12:58:49] [Iter 4648/4650] R4[2397/2400], Temp: 0.0000, Energy: -28.759127+0.001498j
[2025-08-24 12:58:59] [Iter 4649/4650] R4[2398/2400], Temp: 0.0000, Energy: -28.760940+0.000144j
[2025-08-24 12:59:09] [Iter 4650/4650] R4[2399/2400], Temp: 0.0000, Energy: -28.760945-0.000622j
[2025-08-24 12:59:09] ✅ Training completed | Restarts: 4
[2025-08-24 12:59:09] ============================================================
[2025-08-24 12:59:09] Training completed | Runtime: 47192.5s
[2025-08-24 12:59:13] ✓ Final state saved: checkpoints/final_GCNN.pkl
[2025-08-24 12:59:13] ============================================================
