[2025-08-26 22:07:36] ✓ 从checkpoint恢复: results/L=4/J2=1.00/J1=0.83/training/checkpoints/final_GCNN.pkl
[2025-08-26 22:07:36]   - 迭代次数: final
[2025-08-26 22:07:36]   - 能量: -29.991501-0.000041j ± 0.006350
[2025-08-26 22:07:36]   - 时间戳: 2025-08-25T23:32:38.218387+08:00
[2025-08-26 22:07:46] ✓ 变分状态参数已从checkpoint恢复
[2025-08-26 22:07:46] ✓ 从final状态恢复, 重置迭代计数为0
[2025-08-26 22:07:46] ==================================================
[2025-08-26 22:07:46] GCNN for Shastry-Sutherland Model
[2025-08-26 22:07:46] ==================================================
[2025-08-26 22:07:46] System parameters:
[2025-08-26 22:07:46]   - System size: L=4, N=64
[2025-08-26 22:07:46]   - System parameters: J1=0.84, J2=1.0, Q=0.0
[2025-08-26 22:07:46] --------------------------------------------------
[2025-08-26 22:07:46] Model parameters:
[2025-08-26 22:07:46]   - Number of layers = 4
[2025-08-26 22:07:46]   - Number of features = 4
[2025-08-26 22:07:46]   - Total parameters = 12572
[2025-08-26 22:07:46] --------------------------------------------------
[2025-08-26 22:07:46] Training parameters:
[2025-08-26 22:07:46]   - Learning rate: 0.015
[2025-08-26 22:07:46]   - Total iterations: 1050
[2025-08-26 22:07:46]   - Annealing cycles: 3
[2025-08-26 22:07:46]   - Initial period: 150
[2025-08-26 22:07:46]   - Period multiplier: 2.0
[2025-08-26 22:07:46]   - Temperature range: 0.0-1.0
[2025-08-26 22:07:46]   - Samples: 4096
[2025-08-26 22:07:46]   - Discarded samples: 0
[2025-08-26 22:07:46]   - Chunk size: 2048
[2025-08-26 22:07:46]   - Diagonal shift: 0.2
[2025-08-26 22:07:46]   - Gradient clipping: 1.0
[2025-08-26 22:07:46]   - Checkpoint enabled: interval=100
[2025-08-26 22:07:46]   - Checkpoint directory: results/L=4/J2=1.00/J1=0.84/training/checkpoints
[2025-08-26 22:07:46] --------------------------------------------------
[2025-08-26 22:07:46] Device status:
[2025-08-26 22:07:46]   - Devices model: NVIDIA H200 NVL
[2025-08-26 22:07:46]   - Number of devices: 1
[2025-08-26 22:07:46]   - Sharding: True
[2025-08-26 22:07:46] ============================================================
[2025-08-26 22:08:24] [Iter 1/1050] R0[0/150], Temp: 1.0000, Energy: -30.410523+0.002953j
[2025-08-26 22:08:45] [Iter 2/1050] R0[1/150], Temp: 0.9999, Energy: -30.403578+0.001711j
[2025-08-26 22:08:48] [Iter 3/1050] R0[2/150], Temp: 0.9996, Energy: -30.413168+0.001605j
[2025-08-26 22:08:50] [Iter 4/1050] R0[3/150], Temp: 0.9990, Energy: -30.411151-0.001720j
[2025-08-26 22:08:53] [Iter 5/1050] R0[4/150], Temp: 0.9982, Energy: -30.409669-0.000937j
[2025-08-26 22:08:55] [Iter 6/1050] R0[5/150], Temp: 0.9973, Energy: -30.416793+0.003237j
[2025-08-26 22:08:58] [Iter 7/1050] R0[6/150], Temp: 0.9961, Energy: -30.407634+0.001578j
[2025-08-26 22:09:01] [Iter 8/1050] R0[7/150], Temp: 0.9946, Energy: -30.418697+0.002157j
[2025-08-26 22:09:03] [Iter 9/1050] R0[8/150], Temp: 0.9930, Energy: -30.416013+0.000561j
[2025-08-26 22:09:06] [Iter 10/1050] R0[9/150], Temp: 0.9911, Energy: -30.413698+0.001545j
[2025-08-26 22:09:09] [Iter 11/1050] R0[10/150], Temp: 0.9891, Energy: -30.411230-0.001857j
[2025-08-26 22:09:11] [Iter 12/1050] R0[11/150], Temp: 0.9868, Energy: -30.410021-0.000499j
[2025-08-26 22:09:14] [Iter 13/1050] R0[12/150], Temp: 0.9843, Energy: -30.408821+0.000630j
[2025-08-26 22:09:16] [Iter 14/1050] R0[13/150], Temp: 0.9816, Energy: -30.411104+0.002320j
[2025-08-26 22:09:19] [Iter 15/1050] R0[14/150], Temp: 0.9787, Energy: -30.406881-0.000566j
[2025-08-26 22:09:22] [Iter 16/1050] R0[15/150], Temp: 0.9755, Energy: -30.411251-0.000505j
[2025-08-26 22:09:24] [Iter 17/1050] R0[16/150], Temp: 0.9722, Energy: -30.409785+0.001378j
[2025-08-26 22:09:27] [Iter 18/1050] R0[17/150], Temp: 0.9686, Energy: -30.410963+0.000259j
[2025-08-26 22:09:29] [Iter 19/1050] R0[18/150], Temp: 0.9649, Energy: -30.412367-0.005227j
[2025-08-26 22:09:32] [Iter 20/1050] R0[19/150], Temp: 0.9609, Energy: -30.417270-0.000691j
[2025-08-26 22:09:35] [Iter 21/1050] R0[20/150], Temp: 0.9568, Energy: -30.405394-0.001430j
[2025-08-26 22:09:37] [Iter 22/1050] R0[21/150], Temp: 0.9524, Energy: -30.414635-0.000528j
[2025-08-26 22:09:40] [Iter 23/1050] R0[22/150], Temp: 0.9479, Energy: -30.409972-0.001597j
[2025-08-26 22:09:42] [Iter 24/1050] R0[23/150], Temp: 0.9431, Energy: -30.411724+0.001239j
[2025-08-26 22:09:45] [Iter 25/1050] R0[24/150], Temp: 0.9382, Energy: -30.413108-0.003343j
[2025-08-26 22:09:48] [Iter 26/1050] R0[25/150], Temp: 0.9330, Energy: -30.413802+0.000276j
[2025-08-26 22:09:51] [Iter 27/1050] R0[26/150], Temp: 0.9277, Energy: -30.414789+0.004259j
[2025-08-26 22:09:54] [Iter 28/1050] R0[27/150], Temp: 0.9222, Energy: -30.412668-0.001343j
[2025-08-26 22:09:57] [Iter 29/1050] R0[28/150], Temp: 0.9165, Energy: -30.413092-0.000771j
[2025-08-26 22:09:59] [Iter 30/1050] R0[29/150], Temp: 0.9106, Energy: -30.411022-0.000102j
[2025-08-26 22:10:02] [Iter 31/1050] R0[30/150], Temp: 0.9045, Energy: -30.417734+0.000067j
[2025-08-26 22:10:04] [Iter 32/1050] R0[31/150], Temp: 0.8983, Energy: -30.411767+0.001361j
[2025-08-26 22:10:07] [Iter 33/1050] R0[32/150], Temp: 0.8918, Energy: -30.418893-0.002538j
[2025-08-26 22:10:10] [Iter 34/1050] R0[33/150], Temp: 0.8853, Energy: -30.410674-0.002392j
[2025-08-26 22:10:12] [Iter 35/1050] R0[34/150], Temp: 0.8785, Energy: -30.409216+0.000457j
[2025-08-26 22:10:15] [Iter 36/1050] R0[35/150], Temp: 0.8716, Energy: -30.417956+0.000727j
[2025-08-26 22:10:17] [Iter 37/1050] R0[36/150], Temp: 0.8645, Energy: -30.421285-0.000101j
[2025-08-26 22:10:20] [Iter 38/1050] R0[37/150], Temp: 0.8572, Energy: -30.418693+0.001890j
[2025-08-26 22:10:23] [Iter 39/1050] R0[38/150], Temp: 0.8498, Energy: -30.413019-0.000466j
[2025-08-26 22:10:25] [Iter 40/1050] R0[39/150], Temp: 0.8423, Energy: -30.417745+0.000396j
[2025-08-26 22:10:28] [Iter 41/1050] R0[40/150], Temp: 0.8346, Energy: -30.410466-0.001148j
[2025-08-26 22:10:30] [Iter 42/1050] R0[41/150], Temp: 0.8267, Energy: -30.402355-0.001677j
[2025-08-26 22:10:33] [Iter 43/1050] R0[42/150], Temp: 0.8187, Energy: -30.406359+0.004798j
[2025-08-26 22:10:36] [Iter 44/1050] R0[43/150], Temp: 0.8106, Energy: -30.404159-0.001272j
[2025-08-26 22:10:38] [Iter 45/1050] R0[44/150], Temp: 0.8023, Energy: -30.414105-0.000228j
[2025-08-26 22:10:41] [Iter 46/1050] R0[45/150], Temp: 0.7939, Energy: -30.397642-0.005595j
[2025-08-26 22:10:43] [Iter 47/1050] R0[46/150], Temp: 0.7854, Energy: -30.400254-0.001407j
[2025-08-26 22:10:46] [Iter 48/1050] R0[47/150], Temp: 0.7767, Energy: -30.403413+0.002146j
[2025-08-26 22:10:49] [Iter 49/1050] R0[48/150], Temp: 0.7679, Energy: -30.406773+0.000303j
[2025-08-26 22:10:51] [Iter 50/1050] R0[49/150], Temp: 0.7590, Energy: -30.412649+0.000335j
[2025-08-26 22:10:54] [Iter 51/1050] R0[50/150], Temp: 0.7500, Energy: -30.408498-0.002424j
[2025-08-26 22:10:57] [Iter 52/1050] R0[51/150], Temp: 0.7409, Energy: -30.420395-0.000854j
[2025-08-26 22:10:59] [Iter 53/1050] R0[52/150], Temp: 0.7316, Energy: -30.420427+0.000334j
[2025-08-26 22:11:02] [Iter 54/1050] R0[53/150], Temp: 0.7223, Energy: -30.416143-0.000406j
[2025-08-26 22:11:04] [Iter 55/1050] R0[54/150], Temp: 0.7129, Energy: -30.407708+0.001408j
[2025-08-26 22:11:07] [Iter 56/1050] R0[55/150], Temp: 0.7034, Energy: -30.411710-0.002204j
[2025-08-26 22:11:10] [Iter 57/1050] R0[56/150], Temp: 0.6938, Energy: -30.410938+0.000123j
[2025-08-26 22:11:12] [Iter 58/1050] R0[57/150], Temp: 0.6841, Energy: -30.410535+0.002953j
[2025-08-26 22:11:15] [Iter 59/1050] R0[58/150], Temp: 0.6743, Energy: -30.417290-0.001975j
[2025-08-26 22:11:17] [Iter 60/1050] R0[59/150], Temp: 0.6644, Energy: -30.408905+0.000948j
[2025-08-26 22:11:20] [Iter 61/1050] R0[60/150], Temp: 0.6545, Energy: -30.413842+0.002578j
[2025-08-26 22:11:23] [Iter 62/1050] R0[61/150], Temp: 0.6445, Energy: -30.410878-0.003850j
[2025-08-26 22:11:25] [Iter 63/1050] R0[62/150], Temp: 0.6345, Energy: -30.420844+0.000051j
[2025-08-26 22:11:28] [Iter 64/1050] R0[63/150], Temp: 0.6243, Energy: -30.406637+0.003308j
[2025-08-26 22:11:30] [Iter 65/1050] R0[64/150], Temp: 0.6142, Energy: -30.407672-0.000236j
[2025-08-26 22:11:33] [Iter 66/1050] R0[65/150], Temp: 0.6040, Energy: -30.413108-0.002521j
[2025-08-26 22:11:36] [Iter 67/1050] R0[66/150], Temp: 0.5937, Energy: -30.412266-0.001260j
[2025-08-26 22:11:38] [Iter 68/1050] R0[67/150], Temp: 0.5834, Energy: -30.416473-0.000887j
[2025-08-26 22:11:41] [Iter 69/1050] R0[68/150], Temp: 0.5730, Energy: -30.405728+0.002946j
[2025-08-26 22:11:43] [Iter 70/1050] R0[69/150], Temp: 0.5627, Energy: -30.414515+0.001589j
[2025-08-26 22:11:46] [Iter 71/1050] R0[70/150], Temp: 0.5523, Energy: -30.415472+0.001034j
[2025-08-26 22:11:49] [Iter 72/1050] R0[71/150], Temp: 0.5418, Energy: -30.414155-0.003745j
[2025-08-26 22:11:51] [Iter 73/1050] R0[72/150], Temp: 0.5314, Energy: -30.419400+0.002070j
[2025-08-26 22:11:54] [Iter 74/1050] R0[73/150], Temp: 0.5209, Energy: -30.416588+0.000776j
[2025-08-26 22:11:56] [Iter 75/1050] R0[74/150], Temp: 0.5105, Energy: -30.413566-0.003227j
[2025-08-26 22:11:59] [Iter 76/1050] R0[75/150], Temp: 0.5000, Energy: -30.410404+0.001156j
[2025-08-26 22:12:02] [Iter 77/1050] R0[76/150], Temp: 0.4895, Energy: -30.414234-0.002057j
[2025-08-26 22:12:04] [Iter 78/1050] R0[77/150], Temp: 0.4791, Energy: -30.423961+0.000638j
[2025-08-26 22:12:07] [Iter 79/1050] R0[78/150], Temp: 0.4686, Energy: -30.416874+0.001202j
[2025-08-26 22:12:10] [Iter 80/1050] R0[79/150], Temp: 0.4582, Energy: -30.415946+0.003322j
[2025-08-26 22:12:12] [Iter 81/1050] R0[80/150], Temp: 0.4477, Energy: -30.415830-0.000682j
[2025-08-26 22:12:15] [Iter 82/1050] R0[81/150], Temp: 0.4373, Energy: -30.408672-0.003177j
[2025-08-26 22:12:17] [Iter 83/1050] R0[82/150], Temp: 0.4270, Energy: -30.415858+0.000652j
[2025-08-26 22:12:20] [Iter 84/1050] R0[83/150], Temp: 0.4166, Energy: -30.416763+0.000937j
[2025-08-26 22:12:23] [Iter 85/1050] R0[84/150], Temp: 0.4063, Energy: -30.407092-0.002014j
[2025-08-26 22:12:25] [Iter 86/1050] R0[85/150], Temp: 0.3960, Energy: -30.409934-0.000403j
[2025-08-26 22:12:28] [Iter 87/1050] R0[86/150], Temp: 0.3858, Energy: -30.416534+0.000719j
[2025-08-26 22:12:30] [Iter 88/1050] R0[87/150], Temp: 0.3757, Energy: -30.419249-0.003121j
[2025-08-26 22:12:33] [Iter 89/1050] R0[88/150], Temp: 0.3655, Energy: -30.414347+0.000241j
[2025-08-26 22:12:36] [Iter 90/1050] R0[89/150], Temp: 0.3555, Energy: -30.412087-0.000469j
[2025-08-26 22:12:38] [Iter 91/1050] R0[90/150], Temp: 0.3455, Energy: -30.411711+0.000262j
[2025-08-26 22:12:41] [Iter 92/1050] R0[91/150], Temp: 0.3356, Energy: -30.406051-0.000039j
[2025-08-26 22:12:43] [Iter 93/1050] R0[92/150], Temp: 0.3257, Energy: -30.412209+0.001287j
[2025-08-26 22:12:46] [Iter 94/1050] R0[93/150], Temp: 0.3159, Energy: -30.419671-0.001408j
[2025-08-26 22:12:49] [Iter 95/1050] R0[94/150], Temp: 0.3062, Energy: -30.405924+0.000641j
[2025-08-26 22:12:51] [Iter 96/1050] R0[95/150], Temp: 0.2966, Energy: -30.417216+0.002406j
[2025-08-26 22:12:54] [Iter 97/1050] R0[96/150], Temp: 0.2871, Energy: -30.412464-0.000989j
[2025-08-26 22:12:56] [Iter 98/1050] R0[97/150], Temp: 0.2777, Energy: -30.411531-0.000438j
[2025-08-26 22:12:59] [Iter 99/1050] R0[98/150], Temp: 0.2684, Energy: -30.421263-0.001825j
[2025-08-26 22:13:02] [Iter 100/1050] R0[99/150], Temp: 0.2591, Energy: -30.409922+0.002155j
[2025-08-26 22:13:02] ✓ Checkpoint saved: checkpoint_iter_000100.pkl
[2025-08-26 22:13:04] [Iter 101/1050] R0[100/150], Temp: 0.2500, Energy: -30.409607-0.001589j
[2025-08-26 22:13:07] [Iter 102/1050] R0[101/150], Temp: 0.2410, Energy: -30.414315+0.003221j
[2025-08-26 22:13:09] [Iter 103/1050] R0[102/150], Temp: 0.2321, Energy: -30.410279-0.001929j
[2025-08-26 22:13:12] [Iter 104/1050] R0[103/150], Temp: 0.2233, Energy: -30.409382+0.001151j
[2025-08-26 22:13:15] [Iter 105/1050] R0[104/150], Temp: 0.2146, Energy: -30.417021+0.002748j
[2025-08-26 22:13:17] [Iter 106/1050] R0[105/150], Temp: 0.2061, Energy: -30.423503-0.000378j
[2025-08-26 22:13:20] [Iter 107/1050] R0[106/150], Temp: 0.1977, Energy: -30.417057-0.000994j
[2025-08-26 22:13:23] [Iter 108/1050] R0[107/150], Temp: 0.1894, Energy: -30.410174+0.002175j
[2025-08-26 22:13:25] [Iter 109/1050] R0[108/150], Temp: 0.1813, Energy: -30.416016-0.003069j
[2025-08-26 22:13:28] [Iter 110/1050] R0[109/150], Temp: 0.1733, Energy: -30.407068+0.002044j
[2025-08-26 22:13:30] [Iter 111/1050] R0[110/150], Temp: 0.1654, Energy: -30.409046+0.000530j
[2025-08-26 22:13:33] [Iter 112/1050] R0[111/150], Temp: 0.1577, Energy: -30.421594-0.001358j
[2025-08-26 22:13:36] [Iter 113/1050] R0[112/150], Temp: 0.1502, Energy: -30.413852-0.001369j
[2025-08-26 22:13:38] [Iter 114/1050] R0[113/150], Temp: 0.1428, Energy: -30.417954+0.001722j
[2025-08-26 22:13:41] [Iter 115/1050] R0[114/150], Temp: 0.1355, Energy: -30.404152+0.001888j
[2025-08-26 22:13:43] [Iter 116/1050] R0[115/150], Temp: 0.1284, Energy: -30.414458-0.002610j
[2025-08-26 22:13:46] [Iter 117/1050] R0[116/150], Temp: 0.1215, Energy: -30.417376+0.000938j
[2025-08-26 22:13:49] [Iter 118/1050] R0[117/150], Temp: 0.1147, Energy: -30.416833+0.000584j
[2025-08-26 22:13:51] [Iter 119/1050] R0[118/150], Temp: 0.1082, Energy: -30.419129-0.000270j
[2025-08-26 22:13:54] [Iter 120/1050] R0[119/150], Temp: 0.1017, Energy: -30.406719-0.000740j
[2025-08-26 22:13:56] [Iter 121/1050] R0[120/150], Temp: 0.0955, Energy: -30.419462+0.002832j
[2025-08-26 22:13:59] [Iter 122/1050] R0[121/150], Temp: 0.0894, Energy: -30.413812+0.000940j
[2025-08-26 22:14:02] [Iter 123/1050] R0[122/150], Temp: 0.0835, Energy: -30.408291-0.001522j
[2025-08-26 22:14:04] [Iter 124/1050] R0[123/150], Temp: 0.0778, Energy: -30.413829+0.000090j
[2025-08-26 22:14:07] [Iter 125/1050] R0[124/150], Temp: 0.0723, Energy: -30.420252+0.001104j
[2025-08-26 22:14:09] [Iter 126/1050] R0[125/150], Temp: 0.0670, Energy: -30.415263+0.000087j
[2025-08-26 22:14:12] [Iter 127/1050] R0[126/150], Temp: 0.0618, Energy: -30.417275-0.002865j
[2025-08-26 22:14:15] [Iter 128/1050] R0[127/150], Temp: 0.0569, Energy: -30.417000-0.000474j
[2025-08-26 22:14:17] [Iter 129/1050] R0[128/150], Temp: 0.0521, Energy: -30.413257+0.001117j
[2025-08-26 22:14:20] [Iter 130/1050] R0[129/150], Temp: 0.0476, Energy: -30.412211-0.000952j
[2025-08-26 22:14:22] [Iter 131/1050] R0[130/150], Temp: 0.0432, Energy: -30.414744-0.001457j
[2025-08-26 22:14:25] [Iter 132/1050] R0[131/150], Temp: 0.0391, Energy: -30.406898-0.001290j
[2025-08-26 22:14:28] [Iter 133/1050] R0[132/150], Temp: 0.0351, Energy: -30.418216-0.001868j
[2025-08-26 22:14:30] [Iter 134/1050] R0[133/150], Temp: 0.0314, Energy: -30.420916-0.001119j
[2025-08-26 22:14:33] [Iter 135/1050] R0[134/150], Temp: 0.0278, Energy: -30.412103+0.001925j
[2025-08-26 22:14:36] [Iter 136/1050] R0[135/150], Temp: 0.0245, Energy: -30.411261-0.000407j
[2025-08-26 22:14:38] [Iter 137/1050] R0[136/150], Temp: 0.0213, Energy: -30.404266+0.000647j
[2025-08-26 22:14:41] [Iter 138/1050] R0[137/150], Temp: 0.0184, Energy: -30.410757-0.002256j
[2025-08-26 22:14:43] [Iter 139/1050] R0[138/150], Temp: 0.0157, Energy: -30.413312+0.002612j
[2025-08-26 22:14:46] [Iter 140/1050] R0[139/150], Temp: 0.0132, Energy: -30.421495+0.000449j
[2025-08-26 22:14:49] [Iter 141/1050] R0[140/150], Temp: 0.0109, Energy: -30.411586-0.001938j
[2025-08-26 22:14:51] [Iter 142/1050] R0[141/150], Temp: 0.0089, Energy: -30.410425+0.000875j
[2025-08-26 22:14:54] [Iter 143/1050] R0[142/150], Temp: 0.0070, Energy: -30.411508+0.001064j
[2025-08-26 22:14:56] [Iter 144/1050] R0[143/150], Temp: 0.0054, Energy: -30.414525-0.000114j
[2025-08-26 22:14:59] [Iter 145/1050] R0[144/150], Temp: 0.0039, Energy: -30.415693-0.002134j
[2025-08-26 22:15:02] [Iter 146/1050] R0[145/150], Temp: 0.0027, Energy: -30.414027+0.000952j
[2025-08-26 22:15:04] [Iter 147/1050] R0[146/150], Temp: 0.0018, Energy: -30.411033-0.002123j
[2025-08-26 22:15:07] [Iter 148/1050] R0[147/150], Temp: 0.0010, Energy: -30.409235-0.000870j
[2025-08-26 22:15:09] [Iter 149/1050] R0[148/150], Temp: 0.0004, Energy: -30.420443-0.000464j
[2025-08-26 22:15:12] [Iter 150/1050] R0[149/150], Temp: 0.0001, Energy: -30.411890+0.000025j
[2025-08-26 22:15:12] RESTART #1 | Period: 300
[2025-08-26 22:15:15] [Iter 151/1050] R1[0/300], Temp: 1.0000, Energy: -30.411542+0.000995j
[2025-08-26 22:15:17] [Iter 152/1050] R1[1/300], Temp: 1.0000, Energy: -30.410610-0.002575j
[2025-08-26 22:15:20] [Iter 153/1050] R1[2/300], Temp: 0.9999, Energy: -30.421306-0.001976j
[2025-08-26 22:15:22] [Iter 154/1050] R1[3/300], Temp: 0.9998, Energy: -30.415044+0.002775j
[2025-08-26 22:15:25] [Iter 155/1050] R1[4/300], Temp: 0.9996, Energy: -30.419409-0.001244j
[2025-08-26 22:15:28] [Iter 156/1050] R1[5/300], Temp: 0.9993, Energy: -30.411870-0.000658j
[2025-08-26 22:15:30] [Iter 157/1050] R1[6/300], Temp: 0.9990, Energy: -30.414525+0.001200j
[2025-08-26 22:15:33] [Iter 158/1050] R1[7/300], Temp: 0.9987, Energy: -30.404210+0.001971j
[2025-08-26 22:15:35] [Iter 159/1050] R1[8/300], Temp: 0.9982, Energy: -30.408530-0.000477j
[2025-08-26 22:15:38] [Iter 160/1050] R1[9/300], Temp: 0.9978, Energy: -30.416683+0.001600j
[2025-08-26 22:15:41] [Iter 161/1050] R1[10/300], Temp: 0.9973, Energy: -30.404707-0.000486j
[2025-08-26 22:15:43] [Iter 162/1050] R1[11/300], Temp: 0.9967, Energy: -30.415713-0.000725j
[2025-08-26 22:15:46] [Iter 163/1050] R1[12/300], Temp: 0.9961, Energy: -30.409551-0.000606j
[2025-08-26 22:15:48] [Iter 164/1050] R1[13/300], Temp: 0.9954, Energy: -30.410958+0.002821j
[2025-08-26 22:15:51] [Iter 165/1050] R1[14/300], Temp: 0.9946, Energy: -30.409636-0.002812j
[2025-08-26 22:15:54] [Iter 166/1050] R1[15/300], Temp: 0.9938, Energy: -30.415079-0.004540j
[2025-08-26 22:15:56] [Iter 167/1050] R1[16/300], Temp: 0.9930, Energy: -30.415766-0.001921j
[2025-08-26 22:15:59] [Iter 168/1050] R1[17/300], Temp: 0.9921, Energy: -30.407936+0.001663j
[2025-08-26 22:16:02] [Iter 169/1050] R1[18/300], Temp: 0.9911, Energy: -30.407777+0.000570j
[2025-08-26 22:16:04] [Iter 170/1050] R1[19/300], Temp: 0.9901, Energy: -30.410245-0.001333j
[2025-08-26 22:16:07] [Iter 171/1050] R1[20/300], Temp: 0.9891, Energy: -30.419756+0.000809j
[2025-08-26 22:16:09] [Iter 172/1050] R1[21/300], Temp: 0.9880, Energy: -30.412181+0.001407j
[2025-08-26 22:16:12] [Iter 173/1050] R1[22/300], Temp: 0.9868, Energy: -30.409080-0.000111j
[2025-08-26 22:16:15] [Iter 174/1050] R1[23/300], Temp: 0.9856, Energy: -30.417720+0.001083j
[2025-08-26 22:16:17] [Iter 175/1050] R1[24/300], Temp: 0.9843, Energy: -30.405984-0.006009j
[2025-08-26 22:16:20] [Iter 176/1050] R1[25/300], Temp: 0.9830, Energy: -30.408035-0.001011j
[2025-08-26 22:16:22] [Iter 177/1050] R1[26/300], Temp: 0.9816, Energy: -30.410414-0.002405j
[2025-08-26 22:16:25] [Iter 178/1050] R1[27/300], Temp: 0.9801, Energy: -30.406742+0.000721j
[2025-08-26 22:16:28] [Iter 179/1050] R1[28/300], Temp: 0.9787, Energy: -30.411730+0.000580j
[2025-08-26 22:16:30] [Iter 180/1050] R1[29/300], Temp: 0.9771, Energy: -30.412426+0.000741j
[2025-08-26 22:16:33] [Iter 181/1050] R1[30/300], Temp: 0.9755, Energy: -30.415654+0.000998j
[2025-08-26 22:16:35] [Iter 182/1050] R1[31/300], Temp: 0.9739, Energy: -30.417568+0.000523j
[2025-08-26 22:16:38] [Iter 183/1050] R1[32/300], Temp: 0.9722, Energy: -30.403992+0.001885j
[2025-08-26 22:16:41] [Iter 184/1050] R1[33/300], Temp: 0.9704, Energy: -30.419682-0.001013j
[2025-08-26 22:16:43] [Iter 185/1050] R1[34/300], Temp: 0.9686, Energy: -30.410773+0.001949j
[2025-08-26 22:16:46] [Iter 186/1050] R1[35/300], Temp: 0.9668, Energy: -30.410227-0.000776j
[2025-08-26 22:16:48] [Iter 187/1050] R1[36/300], Temp: 0.9649, Energy: -30.409963+0.000496j
[2025-08-26 22:16:51] [Iter 188/1050] R1[37/300], Temp: 0.9629, Energy: -30.418176+0.000285j
[2025-08-26 22:16:54] [Iter 189/1050] R1[38/300], Temp: 0.9609, Energy: -30.416957-0.002541j
[2025-08-26 22:16:56] [Iter 190/1050] R1[39/300], Temp: 0.9589, Energy: -30.412000-0.000722j
[2025-08-26 22:16:59] [Iter 191/1050] R1[40/300], Temp: 0.9568, Energy: -30.413336+0.000382j
[2025-08-26 22:17:01] [Iter 192/1050] R1[41/300], Temp: 0.9546, Energy: -30.411950-0.001243j
[2025-08-26 22:17:04] [Iter 193/1050] R1[42/300], Temp: 0.9524, Energy: -30.412465-0.000765j
[2025-08-26 22:17:07] [Iter 194/1050] R1[43/300], Temp: 0.9502, Energy: -30.411294-0.000402j
[2025-08-26 22:17:09] [Iter 195/1050] R1[44/300], Temp: 0.9479, Energy: -30.417693+0.001223j
[2025-08-26 22:17:12] [Iter 196/1050] R1[45/300], Temp: 0.9455, Energy: -30.412233-0.000437j
[2025-08-26 22:17:15] [Iter 197/1050] R1[46/300], Temp: 0.9431, Energy: -30.414333+0.011088j
[2025-08-26 22:17:17] [Iter 198/1050] R1[47/300], Temp: 0.9407, Energy: -30.422137-0.000907j
[2025-08-26 22:17:20] [Iter 199/1050] R1[48/300], Temp: 0.9382, Energy: -30.417029-0.003037j
[2025-08-26 22:17:22] [Iter 200/1050] R1[49/300], Temp: 0.9356, Energy: -30.415478+0.000452j
[2025-08-26 22:17:22] ✓ Checkpoint saved: checkpoint_iter_000200.pkl
[2025-08-26 22:17:25] [Iter 201/1050] R1[50/300], Temp: 0.9330, Energy: -30.412455-0.001603j
[2025-08-26 22:17:28] [Iter 202/1050] R1[51/300], Temp: 0.9304, Energy: -30.413018+0.003409j
[2025-08-26 22:17:30] [Iter 203/1050] R1[52/300], Temp: 0.9277, Energy: -30.415574-0.004850j
[2025-08-26 22:17:33] [Iter 204/1050] R1[53/300], Temp: 0.9249, Energy: -30.415245-0.002594j
[2025-08-26 22:17:35] [Iter 205/1050] R1[54/300], Temp: 0.9222, Energy: -30.413803-0.002322j
[2025-08-26 22:17:38] [Iter 206/1050] R1[55/300], Temp: 0.9193, Energy: -30.413863-0.002642j
[2025-08-26 22:17:41] [Iter 207/1050] R1[56/300], Temp: 0.9165, Energy: -30.418112-0.000653j
[2025-08-26 22:17:43] [Iter 208/1050] R1[57/300], Temp: 0.9135, Energy: -30.411926+0.000863j
[2025-08-26 22:17:46] [Iter 209/1050] R1[58/300], Temp: 0.9106, Energy: -30.412182+0.001984j
[2025-08-26 22:17:48] [Iter 210/1050] R1[59/300], Temp: 0.9076, Energy: -30.409478+0.001402j
[2025-08-26 22:17:51] [Iter 211/1050] R1[60/300], Temp: 0.9045, Energy: -30.417718+0.001211j
[2025-08-26 22:17:54] [Iter 212/1050] R1[61/300], Temp: 0.9014, Energy: -30.415948-0.000351j
[2025-08-26 22:17:56] [Iter 213/1050] R1[62/300], Temp: 0.8983, Energy: -30.417327-0.001939j
[2025-08-26 22:17:59] [Iter 214/1050] R1[63/300], Temp: 0.8951, Energy: -30.413097-0.000509j
[2025-08-26 22:18:01] [Iter 215/1050] R1[64/300], Temp: 0.8918, Energy: -30.412718+0.001351j
[2025-08-26 22:18:04] [Iter 216/1050] R1[65/300], Temp: 0.8886, Energy: -30.410406-0.000796j
[2025-08-26 22:18:07] [Iter 217/1050] R1[66/300], Temp: 0.8853, Energy: -30.413389-0.000796j
[2025-08-26 22:18:09] [Iter 218/1050] R1[67/300], Temp: 0.8819, Energy: -30.413206-0.001494j
[2025-08-26 22:18:12] [Iter 219/1050] R1[68/300], Temp: 0.8785, Energy: -30.408089-0.000961j
[2025-08-26 22:18:14] [Iter 220/1050] R1[69/300], Temp: 0.8751, Energy: -30.410985-0.002919j
[2025-08-26 22:18:17] [Iter 221/1050] R1[70/300], Temp: 0.8716, Energy: -30.412954-0.001947j
[2025-08-26 22:18:20] [Iter 222/1050] R1[71/300], Temp: 0.8680, Energy: -30.408323-0.001914j
[2025-08-26 22:18:22] [Iter 223/1050] R1[72/300], Temp: 0.8645, Energy: -30.420559-0.001976j
[2025-08-26 22:18:25] [Iter 224/1050] R1[73/300], Temp: 0.8609, Energy: -30.418544-0.001976j
[2025-08-26 22:18:28] [Iter 225/1050] R1[74/300], Temp: 0.8572, Energy: -30.416918+0.000414j
[2025-08-26 22:18:30] [Iter 226/1050] R1[75/300], Temp: 0.8536, Energy: -30.417440+0.001311j
[2025-08-26 22:18:33] [Iter 227/1050] R1[76/300], Temp: 0.8498, Energy: -30.416198+0.001525j
[2025-08-26 22:18:35] [Iter 228/1050] R1[77/300], Temp: 0.8461, Energy: -30.406296-0.000417j
[2025-08-26 22:18:38] [Iter 229/1050] R1[78/300], Temp: 0.8423, Energy: -30.419117-0.003763j
[2025-08-26 22:18:41] [Iter 230/1050] R1[79/300], Temp: 0.8384, Energy: -30.406763+0.000766j
[2025-08-26 22:18:43] [Iter 231/1050] R1[80/300], Temp: 0.8346, Energy: -30.408813-0.002162j
[2025-08-26 22:18:46] [Iter 232/1050] R1[81/300], Temp: 0.8307, Energy: -30.415937-0.000502j
[2025-08-26 22:18:48] [Iter 233/1050] R1[82/300], Temp: 0.8267, Energy: -30.416992-0.000010j
[2025-08-26 22:18:51] [Iter 234/1050] R1[83/300], Temp: 0.8227, Energy: -30.415834-0.002198j
[2025-08-26 22:18:54] [Iter 235/1050] R1[84/300], Temp: 0.8187, Energy: -30.412833+0.000863j
[2025-08-26 22:18:56] [Iter 236/1050] R1[85/300], Temp: 0.8147, Energy: -30.417957+0.002510j
[2025-08-26 22:18:59] [Iter 237/1050] R1[86/300], Temp: 0.8106, Energy: -30.412900-0.001892j
[2025-08-26 22:19:01] [Iter 238/1050] R1[87/300], Temp: 0.8065, Energy: -30.410160+0.000082j
[2025-08-26 22:19:04] [Iter 239/1050] R1[88/300], Temp: 0.8023, Energy: -30.412607-0.000018j
[2025-08-26 22:19:07] [Iter 240/1050] R1[89/300], Temp: 0.7981, Energy: -30.415593-0.000909j
[2025-08-26 22:19:09] [Iter 241/1050] R1[90/300], Temp: 0.7939, Energy: -30.411188+0.001341j
[2025-08-26 22:19:12] [Iter 242/1050] R1[91/300], Temp: 0.7896, Energy: -30.417053-0.001181j
[2025-08-26 22:19:14] [Iter 243/1050] R1[92/300], Temp: 0.7854, Energy: -30.410873-0.001715j
[2025-08-26 22:19:17] [Iter 244/1050] R1[93/300], Temp: 0.7810, Energy: -30.410598+0.001049j
[2025-08-26 22:19:20] [Iter 245/1050] R1[94/300], Temp: 0.7767, Energy: -30.418334-0.000372j
[2025-08-26 22:19:22] [Iter 246/1050] R1[95/300], Temp: 0.7723, Energy: -30.411696+0.000200j
[2025-08-26 22:19:25] [Iter 247/1050] R1[96/300], Temp: 0.7679, Energy: -30.410797-0.003495j
[2025-08-26 22:19:27] [Iter 248/1050] R1[97/300], Temp: 0.7635, Energy: -30.416625+0.001874j
[2025-08-26 22:19:30] [Iter 249/1050] R1[98/300], Temp: 0.7590, Energy: -30.414241-0.000565j
[2025-08-26 22:19:33] [Iter 250/1050] R1[99/300], Temp: 0.7545, Energy: -30.402576-0.004270j
[2025-08-26 22:19:35] [Iter 251/1050] R1[100/300], Temp: 0.7500, Energy: -30.419012-0.004219j
[2025-08-26 22:19:38] [Iter 252/1050] R1[101/300], Temp: 0.7455, Energy: -30.412526-0.003599j
[2025-08-26 22:19:40] [Iter 253/1050] R1[102/300], Temp: 0.7409, Energy: -30.415016+0.000867j
[2025-08-26 22:19:43] [Iter 254/1050] R1[103/300], Temp: 0.7363, Energy: -30.416082+0.001207j
[2025-08-26 22:19:46] [Iter 255/1050] R1[104/300], Temp: 0.7316, Energy: -30.419444-0.001530j
[2025-08-26 22:19:48] [Iter 256/1050] R1[105/300], Temp: 0.7270, Energy: -30.414860-0.002574j
[2025-08-26 22:19:51] [Iter 257/1050] R1[106/300], Temp: 0.7223, Energy: -30.404893-0.002275j
[2025-08-26 22:19:54] [Iter 258/1050] R1[107/300], Temp: 0.7176, Energy: -30.409281-0.000234j
[2025-08-26 22:19:56] [Iter 259/1050] R1[108/300], Temp: 0.7129, Energy: -30.416147+0.002219j
[2025-08-26 22:19:59] [Iter 260/1050] R1[109/300], Temp: 0.7081, Energy: -30.412385-0.000170j
[2025-08-26 22:20:01] [Iter 261/1050] R1[110/300], Temp: 0.7034, Energy: -30.402653+0.000847j
[2025-08-26 22:20:04] [Iter 262/1050] R1[111/300], Temp: 0.6986, Energy: -30.409586-0.000946j
[2025-08-26 22:20:07] [Iter 263/1050] R1[112/300], Temp: 0.6938, Energy: -30.418198+0.002209j
[2025-08-26 22:20:09] [Iter 264/1050] R1[113/300], Temp: 0.6889, Energy: -30.409569+0.000376j
[2025-08-26 22:20:12] [Iter 265/1050] R1[114/300], Temp: 0.6841, Energy: -30.411197-0.003377j
[2025-08-26 22:20:14] [Iter 266/1050] R1[115/300], Temp: 0.6792, Energy: -30.408329-0.001075j
[2025-08-26 22:20:17] [Iter 267/1050] R1[116/300], Temp: 0.6743, Energy: -30.414050+0.001894j
[2025-08-26 22:20:20] [Iter 268/1050] R1[117/300], Temp: 0.6694, Energy: -30.415432-0.002479j
[2025-08-26 22:20:22] [Iter 269/1050] R1[118/300], Temp: 0.6644, Energy: -30.408449-0.000956j
[2025-08-26 22:20:25] [Iter 270/1050] R1[119/300], Temp: 0.6595, Energy: -30.413101-0.000594j
[2025-08-26 22:20:27] [Iter 271/1050] R1[120/300], Temp: 0.6545, Energy: -30.403826+0.001255j
[2025-08-26 22:20:30] [Iter 272/1050] R1[121/300], Temp: 0.6495, Energy: -30.406654+0.004866j
[2025-08-26 22:20:33] [Iter 273/1050] R1[122/300], Temp: 0.6445, Energy: -30.416863-0.001503j
[2025-08-26 22:20:35] [Iter 274/1050] R1[123/300], Temp: 0.6395, Energy: -30.412063+0.000361j
[2025-08-26 22:20:38] [Iter 275/1050] R1[124/300], Temp: 0.6345, Energy: -30.417036-0.001301j
[2025-08-26 22:20:40] [Iter 276/1050] R1[125/300], Temp: 0.6294, Energy: -30.407357-0.000600j
[2025-08-26 22:20:43] [Iter 277/1050] R1[126/300], Temp: 0.6243, Energy: -30.413179+0.001509j
[2025-08-26 22:20:46] [Iter 278/1050] R1[127/300], Temp: 0.6193, Energy: -30.399896-0.000626j
[2025-08-26 22:20:48] [Iter 279/1050] R1[128/300], Temp: 0.6142, Energy: -30.414309+0.002325j
[2025-08-26 22:20:51] [Iter 280/1050] R1[129/300], Temp: 0.6091, Energy: -30.415045+0.002139j
[2025-08-26 22:20:53] [Iter 281/1050] R1[130/300], Temp: 0.6040, Energy: -30.414188+0.001009j
[2025-08-26 22:20:56] [Iter 282/1050] R1[131/300], Temp: 0.5988, Energy: -30.419499+0.000646j
[2025-08-26 22:20:59] [Iter 283/1050] R1[132/300], Temp: 0.5937, Energy: -30.417464-0.000939j
[2025-08-26 22:21:01] [Iter 284/1050] R1[133/300], Temp: 0.5885, Energy: -30.418190-0.000190j
[2025-08-26 22:21:04] [Iter 285/1050] R1[134/300], Temp: 0.5834, Energy: -30.415344-0.003273j
[2025-08-26 22:21:06] [Iter 286/1050] R1[135/300], Temp: 0.5782, Energy: -30.419485+0.000398j
[2025-08-26 22:21:09] [Iter 287/1050] R1[136/300], Temp: 0.5730, Energy: -30.413325-0.000300j
[2025-08-26 22:21:12] [Iter 288/1050] R1[137/300], Temp: 0.5679, Energy: -30.408285+0.002675j
[2025-08-26 22:21:14] [Iter 289/1050] R1[138/300], Temp: 0.5627, Energy: -30.402964-0.001561j
[2025-08-26 22:21:17] [Iter 290/1050] R1[139/300], Temp: 0.5575, Energy: -30.415453+0.000290j
[2025-08-26 22:21:19] [Iter 291/1050] R1[140/300], Temp: 0.5523, Energy: -30.408502-0.000517j
[2025-08-26 22:21:22] [Iter 292/1050] R1[141/300], Temp: 0.5471, Energy: -30.412567-0.000088j
[2025-08-26 22:21:25] [Iter 293/1050] R1[142/300], Temp: 0.5418, Energy: -30.408983+0.002514j
[2025-08-26 22:21:27] [Iter 294/1050] R1[143/300], Temp: 0.5366, Energy: -30.416668-0.001334j
[2025-08-26 22:21:30] [Iter 295/1050] R1[144/300], Temp: 0.5314, Energy: -30.418793-0.001468j
[2025-08-26 22:21:33] [Iter 296/1050] R1[145/300], Temp: 0.5262, Energy: -30.404686-0.003152j
[2025-08-26 22:21:35] [Iter 297/1050] R1[146/300], Temp: 0.5209, Energy: -30.417089-0.003250j
[2025-08-26 22:21:38] [Iter 298/1050] R1[147/300], Temp: 0.5157, Energy: -30.418595+0.003027j
[2025-08-26 22:21:40] [Iter 299/1050] R1[148/300], Temp: 0.5105, Energy: -30.419771+0.000217j
[2025-08-26 22:21:43] [Iter 300/1050] R1[149/300], Temp: 0.5052, Energy: -30.417647+0.000289j
[2025-08-26 22:21:43] ✓ Checkpoint saved: checkpoint_iter_000300.pkl
[2025-08-26 22:21:46] [Iter 301/1050] R1[150/300], Temp: 0.5000, Energy: -30.412460+0.000682j
[2025-08-26 22:21:48] [Iter 302/1050] R1[151/300], Temp: 0.4948, Energy: -30.409680-0.001766j
[2025-08-26 22:21:51] [Iter 303/1050] R1[152/300], Temp: 0.4895, Energy: -30.417643-0.001077j
[2025-08-26 22:21:53] [Iter 304/1050] R1[153/300], Temp: 0.4843, Energy: -30.411849-0.001113j
[2025-08-26 22:21:56] [Iter 305/1050] R1[154/300], Temp: 0.4791, Energy: -30.411318-0.000898j
[2025-08-26 22:21:59] [Iter 306/1050] R1[155/300], Temp: 0.4738, Energy: -30.416506-0.001320j
[2025-08-26 22:22:01] [Iter 307/1050] R1[156/300], Temp: 0.4686, Energy: -30.415998-0.000062j
[2025-08-26 22:22:04] [Iter 308/1050] R1[157/300], Temp: 0.4634, Energy: -30.416078-0.000155j
[2025-08-26 22:22:06] [Iter 309/1050] R1[158/300], Temp: 0.4582, Energy: -30.412196+0.003406j
[2025-08-26 22:22:09] [Iter 310/1050] R1[159/300], Temp: 0.4529, Energy: -30.416670-0.000687j
[2025-08-26 22:22:12] [Iter 311/1050] R1[160/300], Temp: 0.4477, Energy: -30.415460-0.000803j
[2025-08-26 22:22:14] [Iter 312/1050] R1[161/300], Temp: 0.4425, Energy: -30.412406-0.001594j
[2025-08-26 22:22:17] [Iter 313/1050] R1[162/300], Temp: 0.4373, Energy: -30.415530-0.001280j
[2025-08-26 22:22:19] [Iter 314/1050] R1[163/300], Temp: 0.4321, Energy: -30.413540+0.000689j
[2025-08-26 22:22:22] [Iter 315/1050] R1[164/300], Temp: 0.4270, Energy: -30.416791-0.000142j
[2025-08-26 22:22:25] [Iter 316/1050] R1[165/300], Temp: 0.4218, Energy: -30.416082+0.000160j
[2025-08-26 22:22:27] [Iter 317/1050] R1[166/300], Temp: 0.4166, Energy: -30.407604-0.000417j
[2025-08-26 22:22:30] [Iter 318/1050] R1[167/300], Temp: 0.4115, Energy: -30.417306-0.000212j
[2025-08-26 22:22:32] [Iter 319/1050] R1[168/300], Temp: 0.4063, Energy: -30.418827-0.004287j
[2025-08-26 22:22:35] [Iter 320/1050] R1[169/300], Temp: 0.4012, Energy: -30.413694+0.001093j
[2025-08-26 22:22:38] [Iter 321/1050] R1[170/300], Temp: 0.3960, Energy: -30.420028+0.001069j
[2025-08-26 22:22:40] [Iter 322/1050] R1[171/300], Temp: 0.3909, Energy: -30.418127+0.000988j
[2025-08-26 22:22:43] [Iter 323/1050] R1[172/300], Temp: 0.3858, Energy: -30.415270-0.001679j
[2025-08-26 22:22:46] [Iter 324/1050] R1[173/300], Temp: 0.3807, Energy: -30.410557-0.000979j
[2025-08-26 22:22:48] [Iter 325/1050] R1[174/300], Temp: 0.3757, Energy: -30.407790+0.000050j
[2025-08-26 22:22:51] [Iter 326/1050] R1[175/300], Temp: 0.3706, Energy: -30.405548-0.001470j
[2025-08-26 22:22:53] [Iter 327/1050] R1[176/300], Temp: 0.3655, Energy: -30.409778-0.001937j
[2025-08-26 22:22:56] [Iter 328/1050] R1[177/300], Temp: 0.3605, Energy: -30.410425+0.003967j
[2025-08-26 22:22:59] [Iter 329/1050] R1[178/300], Temp: 0.3555, Energy: -30.410406+0.001078j
[2025-08-26 22:23:01] [Iter 330/1050] R1[179/300], Temp: 0.3505, Energy: -30.422078-0.000064j
[2025-08-26 22:23:04] [Iter 331/1050] R1[180/300], Temp: 0.3455, Energy: -30.400382+0.003946j
[2025-08-26 22:23:06] [Iter 332/1050] R1[181/300], Temp: 0.3405, Energy: -30.409633-0.000669j
[2025-08-26 22:23:09] [Iter 333/1050] R1[182/300], Temp: 0.3356, Energy: -30.419702-0.002375j
[2025-08-26 22:23:12] [Iter 334/1050] R1[183/300], Temp: 0.3306, Energy: -30.406951-0.000055j
[2025-08-26 22:23:14] [Iter 335/1050] R1[184/300], Temp: 0.3257, Energy: -30.411801+0.001268j
[2025-08-26 22:23:17] [Iter 336/1050] R1[185/300], Temp: 0.3208, Energy: -30.413216+0.004323j
[2025-08-26 22:23:19] [Iter 337/1050] R1[186/300], Temp: 0.3159, Energy: -30.414737+0.001371j
[2025-08-26 22:23:22] [Iter 338/1050] R1[187/300], Temp: 0.3111, Energy: -30.420165+0.001666j
[2025-08-26 22:23:25] [Iter 339/1050] R1[188/300], Temp: 0.3062, Energy: -30.415610-0.002484j
[2025-08-26 22:23:27] [Iter 340/1050] R1[189/300], Temp: 0.3014, Energy: -30.419478+0.000694j
[2025-08-26 22:23:30] [Iter 341/1050] R1[190/300], Temp: 0.2966, Energy: -30.407112-0.000232j
[2025-08-26 22:23:32] [Iter 342/1050] R1[191/300], Temp: 0.2919, Energy: -30.400898+0.004453j
[2025-08-26 22:23:35] [Iter 343/1050] R1[192/300], Temp: 0.2871, Energy: -30.413916+0.002263j
[2025-08-26 22:23:38] [Iter 344/1050] R1[193/300], Temp: 0.2824, Energy: -30.417221+0.001867j
[2025-08-26 22:23:40] [Iter 345/1050] R1[194/300], Temp: 0.2777, Energy: -30.413259-0.002100j
[2025-08-26 22:23:43] [Iter 346/1050] R1[195/300], Temp: 0.2730, Energy: -30.406085+0.000460j
[2025-08-26 22:23:45] [Iter 347/1050] R1[196/300], Temp: 0.2684, Energy: -30.410343+0.000482j
[2025-08-26 22:23:48] [Iter 348/1050] R1[197/300], Temp: 0.2637, Energy: -30.422243-0.001825j
[2025-08-26 22:23:51] [Iter 349/1050] R1[198/300], Temp: 0.2591, Energy: -30.410045+0.002050j
[2025-08-26 22:23:53] [Iter 350/1050] R1[199/300], Temp: 0.2545, Energy: -30.413069-0.001452j
[2025-08-26 22:23:56] [Iter 351/1050] R1[200/300], Temp: 0.2500, Energy: -30.402438-0.005221j
[2025-08-26 22:23:58] [Iter 352/1050] R1[201/300], Temp: 0.2455, Energy: -30.419147-0.002048j
[2025-08-26 22:24:01] [Iter 353/1050] R1[202/300], Temp: 0.2410, Energy: -30.406821-0.002468j
[2025-08-26 22:24:04] [Iter 354/1050] R1[203/300], Temp: 0.2365, Energy: -30.410588+0.002747j
[2025-08-26 22:24:06] [Iter 355/1050] R1[204/300], Temp: 0.2321, Energy: -30.415654+0.001790j
[2025-08-26 22:24:09] [Iter 356/1050] R1[205/300], Temp: 0.2277, Energy: -30.416732-0.002873j
[2025-08-26 22:24:11] [Iter 357/1050] R1[206/300], Temp: 0.2233, Energy: -30.413724-0.001372j
[2025-08-26 22:24:14] [Iter 358/1050] R1[207/300], Temp: 0.2190, Energy: -30.411035+0.001131j
[2025-08-26 22:24:17] [Iter 359/1050] R1[208/300], Temp: 0.2146, Energy: -30.404490-0.000451j
[2025-08-26 22:24:19] [Iter 360/1050] R1[209/300], Temp: 0.2104, Energy: -30.409117-0.000015j
[2025-08-26 22:24:22] [Iter 361/1050] R1[210/300], Temp: 0.2061, Energy: -30.411930+0.000873j
[2025-08-26 22:24:25] [Iter 362/1050] R1[211/300], Temp: 0.2019, Energy: -30.405445-0.000488j
[2025-08-26 22:24:27] [Iter 363/1050] R1[212/300], Temp: 0.1977, Energy: -30.410330+0.000232j
[2025-08-26 22:24:30] [Iter 364/1050] R1[213/300], Temp: 0.1935, Energy: -30.407995-0.002750j
[2025-08-26 22:24:32] [Iter 365/1050] R1[214/300], Temp: 0.1894, Energy: -30.421748-0.001405j
[2025-08-26 22:24:35] [Iter 366/1050] R1[215/300], Temp: 0.1853, Energy: -30.419383+0.000224j
[2025-08-26 22:24:38] [Iter 367/1050] R1[216/300], Temp: 0.1813, Energy: -30.409752-0.004217j
[2025-08-26 22:24:40] [Iter 368/1050] R1[217/300], Temp: 0.1773, Energy: -30.413292+0.000706j
[2025-08-26 22:24:43] [Iter 369/1050] R1[218/300], Temp: 0.1733, Energy: -30.400358+0.000568j
[2025-08-26 22:24:45] [Iter 370/1050] R1[219/300], Temp: 0.1693, Energy: -30.416743+0.002653j
[2025-08-26 22:24:48] [Iter 371/1050] R1[220/300], Temp: 0.1654, Energy: -30.414969-0.001275j
[2025-08-26 22:24:51] [Iter 372/1050] R1[221/300], Temp: 0.1616, Energy: -30.413730+0.001731j
[2025-08-26 22:24:53] [Iter 373/1050] R1[222/300], Temp: 0.1577, Energy: -30.417916-0.000953j
[2025-08-26 22:24:56] [Iter 374/1050] R1[223/300], Temp: 0.1539, Energy: -30.407525+0.000694j
[2025-08-26 22:24:58] [Iter 375/1050] R1[224/300], Temp: 0.1502, Energy: -30.405903+0.002801j
[2025-08-26 22:25:01] [Iter 376/1050] R1[225/300], Temp: 0.1464, Energy: -30.414923-0.002179j
[2025-08-26 22:25:04] [Iter 377/1050] R1[226/300], Temp: 0.1428, Energy: -30.410677+0.003649j
[2025-08-26 22:25:06] [Iter 378/1050] R1[227/300], Temp: 0.1391, Energy: -30.412074+0.002252j
[2025-08-26 22:25:09] [Iter 379/1050] R1[228/300], Temp: 0.1355, Energy: -30.415424+0.001022j
[2025-08-26 22:25:11] [Iter 380/1050] R1[229/300], Temp: 0.1320, Energy: -30.407889-0.002493j
[2025-08-26 22:25:14] [Iter 381/1050] R1[230/300], Temp: 0.1284, Energy: -30.417948-0.001222j
[2025-08-26 22:25:17] [Iter 382/1050] R1[231/300], Temp: 0.1249, Energy: -30.417496+0.001910j
[2025-08-26 22:25:19] [Iter 383/1050] R1[232/300], Temp: 0.1215, Energy: -30.412447+0.001461j
[2025-08-26 22:25:22] [Iter 384/1050] R1[233/300], Temp: 0.1181, Energy: -30.410025-0.000399j
[2025-08-26 22:25:24] [Iter 385/1050] R1[234/300], Temp: 0.1147, Energy: -30.411266-0.002104j
[2025-08-26 22:25:27] [Iter 386/1050] R1[235/300], Temp: 0.1114, Energy: -30.410834-0.000053j
[2025-08-26 22:25:30] [Iter 387/1050] R1[236/300], Temp: 0.1082, Energy: -30.409659+0.003473j
[2025-08-26 22:25:32] [Iter 388/1050] R1[237/300], Temp: 0.1049, Energy: -30.412164+0.002122j
[2025-08-26 22:25:35] [Iter 389/1050] R1[238/300], Temp: 0.1017, Energy: -30.416571-0.003882j
[2025-08-26 22:25:37] [Iter 390/1050] R1[239/300], Temp: 0.0986, Energy: -30.408650-0.000480j
[2025-08-26 22:25:40] [Iter 391/1050] R1[240/300], Temp: 0.0955, Energy: -30.411611-0.001504j
[2025-08-26 22:25:43] [Iter 392/1050] R1[241/300], Temp: 0.0924, Energy: -30.421629-0.006824j
[2025-08-26 22:25:45] [Iter 393/1050] R1[242/300], Temp: 0.0894, Energy: -30.403340-0.002559j
[2025-08-26 22:25:48] [Iter 394/1050] R1[243/300], Temp: 0.0865, Energy: -30.414258-0.000141j
[2025-08-26 22:25:51] [Iter 395/1050] R1[244/300], Temp: 0.0835, Energy: -30.414995-0.003030j
[2025-08-26 22:25:53] [Iter 396/1050] R1[245/300], Temp: 0.0807, Energy: -30.407729-0.002219j
[2025-08-26 22:25:56] [Iter 397/1050] R1[246/300], Temp: 0.0778, Energy: -30.407824-0.002260j
[2025-08-26 22:25:58] [Iter 398/1050] R1[247/300], Temp: 0.0751, Energy: -30.409275-0.002576j
[2025-08-26 22:26:01] [Iter 399/1050] R1[248/300], Temp: 0.0723, Energy: -30.409832+0.001171j
[2025-08-26 22:26:04] [Iter 400/1050] R1[249/300], Temp: 0.0696, Energy: -30.416945-0.001542j
[2025-08-26 22:26:04] ✓ Checkpoint saved: checkpoint_iter_000400.pkl
[2025-08-26 22:26:06] [Iter 401/1050] R1[250/300], Temp: 0.0670, Energy: -30.417663-0.000577j
[2025-08-26 22:26:09] [Iter 402/1050] R1[251/300], Temp: 0.0644, Energy: -30.416107-0.000258j
[2025-08-26 22:26:11] [Iter 403/1050] R1[252/300], Temp: 0.0618, Energy: -30.413759+0.000110j
[2025-08-26 22:26:14] [Iter 404/1050] R1[253/300], Temp: 0.0593, Energy: -30.418005-0.000106j
[2025-08-26 22:26:17] [Iter 405/1050] R1[254/300], Temp: 0.0569, Energy: -30.413679+0.000609j
[2025-08-26 22:26:19] [Iter 406/1050] R1[255/300], Temp: 0.0545, Energy: -30.413207-0.000213j
[2025-08-26 22:26:22] [Iter 407/1050] R1[256/300], Temp: 0.0521, Energy: -30.422417+0.001871j
[2025-08-26 22:26:24] [Iter 408/1050] R1[257/300], Temp: 0.0498, Energy: -30.409059+0.001809j
[2025-08-26 22:26:27] [Iter 409/1050] R1[258/300], Temp: 0.0476, Energy: -30.415072-0.001977j
[2025-08-26 22:26:30] [Iter 410/1050] R1[259/300], Temp: 0.0454, Energy: -30.424785-0.000738j
[2025-08-26 22:26:32] [Iter 411/1050] R1[260/300], Temp: 0.0432, Energy: -30.407314-0.000435j
[2025-08-26 22:26:35] [Iter 412/1050] R1[261/300], Temp: 0.0411, Energy: -30.414864+0.001503j
[2025-08-26 22:26:37] [Iter 413/1050] R1[262/300], Temp: 0.0391, Energy: -30.420616-0.002265j
[2025-08-26 22:26:40] [Iter 414/1050] R1[263/300], Temp: 0.0371, Energy: -30.409240-0.001444j
[2025-08-26 22:26:43] [Iter 415/1050] R1[264/300], Temp: 0.0351, Energy: -30.415462+0.000473j
[2025-08-26 22:26:45] [Iter 416/1050] R1[265/300], Temp: 0.0332, Energy: -30.401786-0.000652j
[2025-08-26 22:26:48] [Iter 417/1050] R1[266/300], Temp: 0.0314, Energy: -30.419192+0.002844j
[2025-08-26 22:26:50] [Iter 418/1050] R1[267/300], Temp: 0.0296, Energy: -30.416383-0.001546j
[2025-08-26 22:26:53] [Iter 419/1050] R1[268/300], Temp: 0.0278, Energy: -30.414663-0.000967j
[2025-08-26 22:26:56] [Iter 420/1050] R1[269/300], Temp: 0.0261, Energy: -30.407609+0.000681j
[2025-08-26 22:26:58] [Iter 421/1050] R1[270/300], Temp: 0.0245, Energy: -30.413112+0.002567j
[2025-08-26 22:27:01] [Iter 422/1050] R1[271/300], Temp: 0.0229, Energy: -30.415122+0.000724j
[2025-08-26 22:27:03] [Iter 423/1050] R1[272/300], Temp: 0.0213, Energy: -30.411809+0.000588j
[2025-08-26 22:27:06] [Iter 424/1050] R1[273/300], Temp: 0.0199, Energy: -30.407233-0.002836j
[2025-08-26 22:27:09] [Iter 425/1050] R1[274/300], Temp: 0.0184, Energy: -30.420072-0.001700j
[2025-08-26 22:27:11] [Iter 426/1050] R1[275/300], Temp: 0.0170, Energy: -30.416795-0.001769j
[2025-08-26 22:27:14] [Iter 427/1050] R1[276/300], Temp: 0.0157, Energy: -30.421056-0.000623j
[2025-08-26 22:27:17] [Iter 428/1050] R1[277/300], Temp: 0.0144, Energy: -30.414341-0.002252j
[2025-08-26 22:27:19] [Iter 429/1050] R1[278/300], Temp: 0.0132, Energy: -30.423082-0.001173j
[2025-08-26 22:27:22] [Iter 430/1050] R1[279/300], Temp: 0.0120, Energy: -30.415905-0.001349j
[2025-08-26 22:27:24] [Iter 431/1050] R1[280/300], Temp: 0.0109, Energy: -30.413456+0.004613j
[2025-08-26 22:27:27] [Iter 432/1050] R1[281/300], Temp: 0.0099, Energy: -30.411127-0.000519j
[2025-08-26 22:27:30] [Iter 433/1050] R1[282/300], Temp: 0.0089, Energy: -30.415365+0.001324j
[2025-08-26 22:27:32] [Iter 434/1050] R1[283/300], Temp: 0.0079, Energy: -30.415299+0.000349j
[2025-08-26 22:27:35] [Iter 435/1050] R1[284/300], Temp: 0.0070, Energy: -30.420111+0.001182j
[2025-08-26 22:27:37] [Iter 436/1050] R1[285/300], Temp: 0.0062, Energy: -30.410544+0.001383j
[2025-08-26 22:27:40] [Iter 437/1050] R1[286/300], Temp: 0.0054, Energy: -30.420377-0.001789j
[2025-08-26 22:27:43] [Iter 438/1050] R1[287/300], Temp: 0.0046, Energy: -30.414235-0.001485j
[2025-08-26 22:27:45] [Iter 439/1050] R1[288/300], Temp: 0.0039, Energy: -30.408126-0.003571j
[2025-08-26 22:27:48] [Iter 440/1050] R1[289/300], Temp: 0.0033, Energy: -30.407129-0.000824j
[2025-08-26 22:27:50] [Iter 441/1050] R1[290/300], Temp: 0.0027, Energy: -30.413321+0.001324j
[2025-08-26 22:27:53] [Iter 442/1050] R1[291/300], Temp: 0.0022, Energy: -30.400040-0.002652j
[2025-08-26 22:27:56] [Iter 443/1050] R1[292/300], Temp: 0.0018, Energy: -30.423741+0.001612j
[2025-08-26 22:27:58] [Iter 444/1050] R1[293/300], Temp: 0.0013, Energy: -30.405318+0.000186j
[2025-08-26 22:28:01] [Iter 445/1050] R1[294/300], Temp: 0.0010, Energy: -30.419176-0.001859j
[2025-08-26 22:28:03] [Iter 446/1050] R1[295/300], Temp: 0.0007, Energy: -30.411232-0.000881j
[2025-08-26 22:28:06] [Iter 447/1050] R1[296/300], Temp: 0.0004, Energy: -30.406395-0.001109j
[2025-08-26 22:28:09] [Iter 448/1050] R1[297/300], Temp: 0.0002, Energy: -30.418726+0.000301j
[2025-08-26 22:28:11] [Iter 449/1050] R1[298/300], Temp: 0.0001, Energy: -30.410501+0.004906j
[2025-08-26 22:28:14] [Iter 450/1050] R1[299/300], Temp: 0.0000, Energy: -30.412917+0.000935j
[2025-08-26 22:28:14] RESTART #2 | Period: 600
[2025-08-26 22:28:16] [Iter 451/1050] R2[0/600], Temp: 1.0000, Energy: -30.409628+0.001115j
[2025-08-26 22:28:19] [Iter 452/1050] R2[1/600], Temp: 1.0000, Energy: -30.421914+0.001260j
[2025-08-26 22:28:22] [Iter 453/1050] R2[2/600], Temp: 1.0000, Energy: -30.412373-0.003793j
[2025-08-26 22:28:24] [Iter 454/1050] R2[3/600], Temp: 0.9999, Energy: -30.416108-0.001273j
[2025-08-26 22:28:27] [Iter 455/1050] R2[4/600], Temp: 0.9999, Energy: -30.406118-0.001610j
[2025-08-26 22:28:29] [Iter 456/1050] R2[5/600], Temp: 0.9998, Energy: -30.407072-0.001256j
[2025-08-26 22:28:32] [Iter 457/1050] R2[6/600], Temp: 0.9998, Energy: -30.410151-0.001639j
[2025-08-26 22:28:35] [Iter 458/1050] R2[7/600], Temp: 0.9997, Energy: -30.418129-0.001233j
[2025-08-26 22:28:37] [Iter 459/1050] R2[8/600], Temp: 0.9996, Energy: -30.408217-0.001245j
[2025-08-26 22:28:40] [Iter 460/1050] R2[9/600], Temp: 0.9994, Energy: -30.408003-0.002355j
[2025-08-26 22:28:43] [Iter 461/1050] R2[10/600], Temp: 0.9993, Energy: -30.412993-0.001846j
[2025-08-26 22:28:45] [Iter 462/1050] R2[11/600], Temp: 0.9992, Energy: -30.413119-0.001829j
[2025-08-26 22:28:48] [Iter 463/1050] R2[12/600], Temp: 0.9990, Energy: -30.410998+0.000591j
[2025-08-26 22:28:50] [Iter 464/1050] R2[13/600], Temp: 0.9988, Energy: -30.418358-0.000799j
[2025-08-26 22:28:53] [Iter 465/1050] R2[14/600], Temp: 0.9987, Energy: -30.408477-0.002366j
[2025-08-26 22:28:56] [Iter 466/1050] R2[15/600], Temp: 0.9985, Energy: -30.408081+0.001020j
[2025-08-26 22:28:58] [Iter 467/1050] R2[16/600], Temp: 0.9982, Energy: -30.413533+0.000534j
[2025-08-26 22:29:01] [Iter 468/1050] R2[17/600], Temp: 0.9980, Energy: -30.417383+0.001703j
[2025-08-26 22:29:03] [Iter 469/1050] R2[18/600], Temp: 0.9978, Energy: -30.417090-0.005185j
[2025-08-26 22:29:06] [Iter 470/1050] R2[19/600], Temp: 0.9975, Energy: -30.416875+0.004156j
[2025-08-26 22:29:09] [Iter 471/1050] R2[20/600], Temp: 0.9973, Energy: -30.420022+0.001960j
[2025-08-26 22:29:11] [Iter 472/1050] R2[21/600], Temp: 0.9970, Energy: -30.418621-0.000470j
[2025-08-26 22:29:14] [Iter 473/1050] R2[22/600], Temp: 0.9967, Energy: -30.407882+0.000894j
[2025-08-26 22:29:16] [Iter 474/1050] R2[23/600], Temp: 0.9964, Energy: -30.409742+0.000344j
[2025-08-26 22:29:19] [Iter 475/1050] R2[24/600], Temp: 0.9961, Energy: -30.403150+0.005512j
[2025-08-26 22:29:22] [Iter 476/1050] R2[25/600], Temp: 0.9957, Energy: -30.415699+0.003967j
[2025-08-26 22:29:24] [Iter 477/1050] R2[26/600], Temp: 0.9954, Energy: -30.414178+0.001135j
[2025-08-26 22:29:27] [Iter 478/1050] R2[27/600], Temp: 0.9950, Energy: -30.408468+0.003099j
[2025-08-26 22:29:29] [Iter 479/1050] R2[28/600], Temp: 0.9946, Energy: -30.416162+0.000881j
[2025-08-26 22:29:32] [Iter 480/1050] R2[29/600], Temp: 0.9942, Energy: -30.416683-0.002970j
[2025-08-26 22:29:35] [Iter 481/1050] R2[30/600], Temp: 0.9938, Energy: -30.410861-0.000729j
[2025-08-26 22:29:37] [Iter 482/1050] R2[31/600], Temp: 0.9934, Energy: -30.403690+0.000349j
[2025-08-26 22:29:40] [Iter 483/1050] R2[32/600], Temp: 0.9930, Energy: -30.405144-0.001361j
[2025-08-26 22:29:42] [Iter 484/1050] R2[33/600], Temp: 0.9926, Energy: -30.421584-0.001931j
[2025-08-26 22:29:45] [Iter 485/1050] R2[34/600], Temp: 0.9921, Energy: -30.408374+0.000953j
[2025-08-26 22:29:48] [Iter 486/1050] R2[35/600], Temp: 0.9916, Energy: -30.417655-0.001645j
[2025-08-26 22:29:50] [Iter 487/1050] R2[36/600], Temp: 0.9911, Energy: -30.406324-0.003441j
[2025-08-26 22:29:53] [Iter 488/1050] R2[37/600], Temp: 0.9906, Energy: -30.418544-0.004335j
[2025-08-26 22:29:55] [Iter 489/1050] R2[38/600], Temp: 0.9901, Energy: -30.417903+0.000027j
[2025-08-26 22:29:58] [Iter 490/1050] R2[39/600], Temp: 0.9896, Energy: -30.427330-0.001697j
[2025-08-26 22:30:01] [Iter 491/1050] R2[40/600], Temp: 0.9891, Energy: -30.403107+0.002226j
[2025-08-26 22:30:03] [Iter 492/1050] R2[41/600], Temp: 0.9885, Energy: -30.418835-0.005030j
[2025-08-26 22:30:06] [Iter 493/1050] R2[42/600], Temp: 0.9880, Energy: -30.405613-0.002728j
[2025-08-26 22:30:08] [Iter 494/1050] R2[43/600], Temp: 0.9874, Energy: -30.406755+0.000100j
[2025-08-26 22:30:11] [Iter 495/1050] R2[44/600], Temp: 0.9868, Energy: -30.415536+0.001229j
[2025-08-26 22:30:14] [Iter 496/1050] R2[45/600], Temp: 0.9862, Energy: -30.417036-0.003779j
[2025-08-26 22:30:16] [Iter 497/1050] R2[46/600], Temp: 0.9856, Energy: -30.409502-0.003030j
[2025-08-26 22:30:19] [Iter 498/1050] R2[47/600], Temp: 0.9849, Energy: -30.413242-0.001181j
[2025-08-26 22:30:22] [Iter 499/1050] R2[48/600], Temp: 0.9843, Energy: -30.410030-0.001560j
[2025-08-26 22:30:24] [Iter 500/1050] R2[49/600], Temp: 0.9836, Energy: -30.409027-0.002463j
[2025-08-26 22:30:24] ✓ Checkpoint saved: checkpoint_iter_000500.pkl
[2025-08-26 22:30:27] [Iter 501/1050] R2[50/600], Temp: 0.9830, Energy: -30.413790-0.003892j
[2025-08-26 22:30:29] [Iter 502/1050] R2[51/600], Temp: 0.9823, Energy: -30.417887+0.004010j
[2025-08-26 22:30:32] [Iter 503/1050] R2[52/600], Temp: 0.9816, Energy: -30.421311+0.000778j
[2025-08-26 22:30:35] [Iter 504/1050] R2[53/600], Temp: 0.9809, Energy: -30.429132-0.001485j
[2025-08-26 22:30:37] [Iter 505/1050] R2[54/600], Temp: 0.9801, Energy: -30.416516-0.004215j
[2025-08-26 22:30:40] [Iter 506/1050] R2[55/600], Temp: 0.9794, Energy: -30.419539+0.002304j
[2025-08-26 22:30:42] [Iter 507/1050] R2[56/600], Temp: 0.9787, Energy: -30.420773-0.001202j
[2025-08-26 22:30:45] [Iter 508/1050] R2[57/600], Temp: 0.9779, Energy: -30.416996-0.003659j
[2025-08-26 22:30:48] [Iter 509/1050] R2[58/600], Temp: 0.9771, Energy: -30.417729+0.000115j
[2025-08-26 22:30:50] [Iter 510/1050] R2[59/600], Temp: 0.9763, Energy: -30.415766+0.001591j
[2025-08-26 22:30:53] [Iter 511/1050] R2[60/600], Temp: 0.9755, Energy: -30.416067-0.001209j
[2025-08-26 22:30:55] [Iter 512/1050] R2[61/600], Temp: 0.9747, Energy: -30.414502-0.004253j
[2025-08-26 22:30:58] [Iter 513/1050] R2[62/600], Temp: 0.9739, Energy: -30.418680-0.000290j
[2025-08-26 22:31:01] [Iter 514/1050] R2[63/600], Temp: 0.9730, Energy: -30.413650-0.000981j
[2025-08-26 22:31:03] [Iter 515/1050] R2[64/600], Temp: 0.9722, Energy: -30.418648+0.002455j
[2025-08-26 22:31:06] [Iter 516/1050] R2[65/600], Temp: 0.9713, Energy: -30.409716-0.000690j
[2025-08-26 22:31:08] [Iter 517/1050] R2[66/600], Temp: 0.9704, Energy: -30.424515-0.002653j
[2025-08-26 22:31:11] [Iter 518/1050] R2[67/600], Temp: 0.9695, Energy: -30.416080+0.000993j
[2025-08-26 22:31:14] [Iter 519/1050] R2[68/600], Temp: 0.9686, Energy: -30.420356-0.001611j
[2025-08-26 22:31:16] [Iter 520/1050] R2[69/600], Temp: 0.9677, Energy: -30.412462-0.000651j
[2025-08-26 22:31:19] [Iter 521/1050] R2[70/600], Temp: 0.9668, Energy: -30.421687+0.002773j
[2025-08-26 22:31:21] [Iter 522/1050] R2[71/600], Temp: 0.9658, Energy: -30.406537-0.002649j
[2025-08-26 22:31:24] [Iter 523/1050] R2[72/600], Temp: 0.9649, Energy: -30.412717+0.000751j
[2025-08-26 22:31:27] [Iter 524/1050] R2[73/600], Temp: 0.9639, Energy: -30.411025-0.000663j
[2025-08-26 22:31:29] [Iter 525/1050] R2[74/600], Temp: 0.9629, Energy: -30.410087+0.000304j
[2025-08-26 22:31:32] [Iter 526/1050] R2[75/600], Temp: 0.9619, Energy: -30.417624-0.000433j
[2025-08-26 22:31:35] [Iter 527/1050] R2[76/600], Temp: 0.9609, Energy: -30.415002-0.000098j
[2025-08-26 22:31:37] [Iter 528/1050] R2[77/600], Temp: 0.9599, Energy: -30.426296+0.001017j
[2025-08-26 22:31:40] [Iter 529/1050] R2[78/600], Temp: 0.9589, Energy: -30.402778-0.000999j
[2025-08-26 22:31:42] [Iter 530/1050] R2[79/600], Temp: 0.9578, Energy: -30.422411-0.001363j
[2025-08-26 22:31:45] [Iter 531/1050] R2[80/600], Temp: 0.9568, Energy: -30.415041-0.000937j
[2025-08-26 22:31:48] [Iter 532/1050] R2[81/600], Temp: 0.9557, Energy: -30.409930-0.002025j
[2025-08-26 22:31:50] [Iter 533/1050] R2[82/600], Temp: 0.9546, Energy: -30.411397-0.000314j
[2025-08-26 22:31:53] [Iter 534/1050] R2[83/600], Temp: 0.9535, Energy: -30.416603-0.000529j
[2025-08-26 22:31:55] [Iter 535/1050] R2[84/600], Temp: 0.9524, Energy: -30.407259-0.000564j
[2025-08-26 22:31:58] [Iter 536/1050] R2[85/600], Temp: 0.9513, Energy: -30.406483-0.002377j
[2025-08-26 22:32:01] [Iter 537/1050] R2[86/600], Temp: 0.9502, Energy: -30.413928-0.002328j
[2025-08-26 22:32:03] [Iter 538/1050] R2[87/600], Temp: 0.9490, Energy: -30.405202-0.000692j
[2025-08-26 22:32:06] [Iter 539/1050] R2[88/600], Temp: 0.9479, Energy: -30.417296+0.001490j
[2025-08-26 22:32:08] [Iter 540/1050] R2[89/600], Temp: 0.9467, Energy: -30.407334+0.000505j
[2025-08-26 22:32:11] [Iter 541/1050] R2[90/600], Temp: 0.9455, Energy: -30.413604+0.000721j
[2025-08-26 22:32:14] [Iter 542/1050] R2[91/600], Temp: 0.9443, Energy: -30.416875-0.000237j
[2025-08-26 22:32:16] [Iter 543/1050] R2[92/600], Temp: 0.9431, Energy: -30.413281-0.000174j
[2025-08-26 22:32:19] [Iter 544/1050] R2[93/600], Temp: 0.9419, Energy: -30.409934-0.002874j
[2025-08-26 22:32:21] [Iter 545/1050] R2[94/600], Temp: 0.9407, Energy: -30.413975+0.001366j
[2025-08-26 22:32:24] [Iter 546/1050] R2[95/600], Temp: 0.9394, Energy: -30.412689+0.000177j
[2025-08-26 22:32:27] [Iter 547/1050] R2[96/600], Temp: 0.9382, Energy: -30.419453-0.001418j
[2025-08-26 22:32:29] [Iter 548/1050] R2[97/600], Temp: 0.9369, Energy: -30.421150+0.000400j
[2025-08-26 22:32:32] [Iter 549/1050] R2[98/600], Temp: 0.9356, Energy: -30.420229-0.003878j
[2025-08-26 22:32:34] [Iter 550/1050] R2[99/600], Temp: 0.9343, Energy: -30.408815+0.000092j
[2025-08-26 22:32:37] [Iter 551/1050] R2[100/600], Temp: 0.9330, Energy: -30.420953-0.000255j
[2025-08-26 22:32:40] [Iter 552/1050] R2[101/600], Temp: 0.9317, Energy: -30.412990-0.000232j
[2025-08-26 22:32:42] [Iter 553/1050] R2[102/600], Temp: 0.9304, Energy: -30.425667+0.002094j
[2025-08-26 22:32:45] [Iter 554/1050] R2[103/600], Temp: 0.9290, Energy: -30.419387-0.000717j
[2025-08-26 22:32:47] [Iter 555/1050] R2[104/600], Temp: 0.9277, Energy: -30.406623-0.000941j
[2025-08-26 22:32:50] [Iter 556/1050] R2[105/600], Temp: 0.9263, Energy: -30.414122-0.000250j
[2025-08-26 22:32:53] [Iter 557/1050] R2[106/600], Temp: 0.9249, Energy: -30.417364-0.000243j
[2025-08-26 22:32:55] [Iter 558/1050] R2[107/600], Temp: 0.9236, Energy: -30.412530-0.001561j
[2025-08-26 22:32:58] [Iter 559/1050] R2[108/600], Temp: 0.9222, Energy: -30.423005+0.002155j
[2025-08-26 22:33:00] [Iter 560/1050] R2[109/600], Temp: 0.9208, Energy: -30.405746-0.000617j
[2025-08-26 22:33:03] [Iter 561/1050] R2[110/600], Temp: 0.9193, Energy: -30.418623+0.000619j
[2025-08-26 22:33:06] [Iter 562/1050] R2[111/600], Temp: 0.9179, Energy: -30.418798-0.000019j
[2025-08-26 22:33:08] [Iter 563/1050] R2[112/600], Temp: 0.9165, Energy: -30.412412-0.001261j
[2025-08-26 22:33:11] [Iter 564/1050] R2[113/600], Temp: 0.9150, Energy: -30.427650-0.003016j
[2025-08-26 22:33:14] [Iter 565/1050] R2[114/600], Temp: 0.9135, Energy: -30.412409-0.002884j
[2025-08-26 22:33:16] [Iter 566/1050] R2[115/600], Temp: 0.9121, Energy: -30.419504-0.001758j
[2025-08-26 22:33:19] [Iter 567/1050] R2[116/600], Temp: 0.9106, Energy: -30.410993+0.000837j
[2025-08-26 22:33:21] [Iter 568/1050] R2[117/600], Temp: 0.9091, Energy: -30.400442+0.001269j
[2025-08-26 22:33:24] [Iter 569/1050] R2[118/600], Temp: 0.9076, Energy: -30.419983-0.000102j
[2025-08-26 22:33:27] [Iter 570/1050] R2[119/600], Temp: 0.9060, Energy: -30.417207+0.002752j
[2025-08-26 22:33:29] [Iter 571/1050] R2[120/600], Temp: 0.9045, Energy: -30.422855-0.002935j
[2025-08-26 22:33:32] [Iter 572/1050] R2[121/600], Temp: 0.9030, Energy: -30.409390+0.000435j
[2025-08-26 22:33:34] [Iter 573/1050] R2[122/600], Temp: 0.9014, Energy: -30.411656+0.000591j
[2025-08-26 22:33:37] [Iter 574/1050] R2[123/600], Temp: 0.8998, Energy: -30.410637+0.001570j
[2025-08-26 22:33:40] [Iter 575/1050] R2[124/600], Temp: 0.8983, Energy: -30.415278-0.001135j
[2025-08-26 22:33:42] [Iter 576/1050] R2[125/600], Temp: 0.8967, Energy: -30.409012-0.001075j
[2025-08-26 22:33:45] [Iter 577/1050] R2[126/600], Temp: 0.8951, Energy: -30.412009-0.001487j
[2025-08-26 22:33:47] [Iter 578/1050] R2[127/600], Temp: 0.8935, Energy: -30.414400+0.002518j
[2025-08-26 22:33:50] [Iter 579/1050] R2[128/600], Temp: 0.8918, Energy: -30.410677+0.000202j
[2025-08-26 22:33:53] [Iter 580/1050] R2[129/600], Temp: 0.8902, Energy: -30.415087+0.001960j
[2025-08-26 22:33:55] [Iter 581/1050] R2[130/600], Temp: 0.8886, Energy: -30.415290-0.000847j
[2025-08-26 22:33:58] [Iter 582/1050] R2[131/600], Temp: 0.8869, Energy: -30.409967+0.000908j
[2025-08-26 22:34:00] [Iter 583/1050] R2[132/600], Temp: 0.8853, Energy: -30.418497-0.001988j
[2025-08-26 22:34:03] [Iter 584/1050] R2[133/600], Temp: 0.8836, Energy: -30.412813-0.000671j
[2025-08-26 22:34:06] [Iter 585/1050] R2[134/600], Temp: 0.8819, Energy: -30.418305-0.001394j
[2025-08-26 22:34:08] [Iter 586/1050] R2[135/600], Temp: 0.8802, Energy: -30.426141-0.002069j
[2025-08-26 22:34:11] [Iter 587/1050] R2[136/600], Temp: 0.8785, Energy: -30.408044-0.000939j
[2025-08-26 22:34:13] [Iter 588/1050] R2[137/600], Temp: 0.8768, Energy: -30.414653+0.002383j
[2025-08-26 22:34:16] [Iter 589/1050] R2[138/600], Temp: 0.8751, Energy: -30.414479+0.002687j
[2025-08-26 22:34:19] [Iter 590/1050] R2[139/600], Temp: 0.8733, Energy: -30.414246+0.001368j
[2025-08-26 22:34:21] [Iter 591/1050] R2[140/600], Temp: 0.8716, Energy: -30.427567+0.002584j
[2025-08-26 22:34:24] [Iter 592/1050] R2[141/600], Temp: 0.8698, Energy: -30.415853+0.005454j
[2025-08-26 22:34:26] [Iter 593/1050] R2[142/600], Temp: 0.8680, Energy: -30.416435+0.002086j
[2025-08-26 22:34:29] [Iter 594/1050] R2[143/600], Temp: 0.8663, Energy: -30.423050-0.000849j
[2025-08-26 22:34:32] [Iter 595/1050] R2[144/600], Temp: 0.8645, Energy: -30.422639+0.000353j
[2025-08-26 22:34:35] [Iter 596/1050] R2[145/600], Temp: 0.8627, Energy: -30.419053-0.001220j
[2025-08-26 22:34:37] [Iter 597/1050] R2[146/600], Temp: 0.8609, Energy: -30.403824-0.001414j
[2025-08-26 22:34:40] [Iter 598/1050] R2[147/600], Temp: 0.8591, Energy: -30.411104+0.001148j
[2025-08-26 22:34:43] [Iter 599/1050] R2[148/600], Temp: 0.8572, Energy: -30.417867+0.001416j
[2025-08-26 22:34:45] [Iter 600/1050] R2[149/600], Temp: 0.8554, Energy: -30.406746+0.003358j
[2025-08-26 22:34:45] ✓ Checkpoint saved: checkpoint_iter_000600.pkl
[2025-08-26 22:34:48] [Iter 601/1050] R2[150/600], Temp: 0.8536, Energy: -30.407424-0.000412j
[2025-08-26 22:34:50] [Iter 602/1050] R2[151/600], Temp: 0.8517, Energy: -30.417768+0.000273j
[2025-08-26 22:34:53] [Iter 603/1050] R2[152/600], Temp: 0.8498, Energy: -30.414270-0.001431j
[2025-08-26 22:34:56] [Iter 604/1050] R2[153/600], Temp: 0.8480, Energy: -30.420461+0.002213j
[2025-08-26 22:34:58] [Iter 605/1050] R2[154/600], Temp: 0.8461, Energy: -30.414937-0.004923j
[2025-08-26 22:35:01] [Iter 606/1050] R2[155/600], Temp: 0.8442, Energy: -30.426800-0.004649j
[2025-08-26 22:35:03] [Iter 607/1050] R2[156/600], Temp: 0.8423, Energy: -30.426022-0.000773j
[2025-08-26 22:35:06] [Iter 608/1050] R2[157/600], Temp: 0.8404, Energy: -30.413954-0.000570j
[2025-08-26 22:35:09] [Iter 609/1050] R2[158/600], Temp: 0.8384, Energy: -30.416936+0.002179j
[2025-08-26 22:35:11] [Iter 610/1050] R2[159/600], Temp: 0.8365, Energy: -30.419728+0.001471j
[2025-08-26 22:35:14] [Iter 611/1050] R2[160/600], Temp: 0.8346, Energy: -30.411687+0.000458j
[2025-08-26 22:35:16] [Iter 612/1050] R2[161/600], Temp: 0.8326, Energy: -30.416357-0.001783j
[2025-08-26 22:35:19] [Iter 613/1050] R2[162/600], Temp: 0.8307, Energy: -30.406232-0.000576j
[2025-08-26 22:35:22] [Iter 614/1050] R2[163/600], Temp: 0.8287, Energy: -30.410459+0.001686j
[2025-08-26 22:35:24] [Iter 615/1050] R2[164/600], Temp: 0.8267, Energy: -30.411186+0.000774j
[2025-08-26 22:35:27] [Iter 616/1050] R2[165/600], Temp: 0.8247, Energy: -30.413553+0.002689j
[2025-08-26 22:35:30] [Iter 617/1050] R2[166/600], Temp: 0.8227, Energy: -30.412845-0.002214j
[2025-08-26 22:35:32] [Iter 618/1050] R2[167/600], Temp: 0.8207, Energy: -30.416735+0.002591j
[2025-08-26 22:35:35] [Iter 619/1050] R2[168/600], Temp: 0.8187, Energy: -30.408881-0.001203j
[2025-08-26 22:35:37] [Iter 620/1050] R2[169/600], Temp: 0.8167, Energy: -30.416828+0.001145j
[2025-08-26 22:35:40] [Iter 621/1050] R2[170/600], Temp: 0.8147, Energy: -30.403026-0.001835j
[2025-08-26 22:35:43] [Iter 622/1050] R2[171/600], Temp: 0.8126, Energy: -30.406864-0.000770j
[2025-08-26 22:35:45] [Iter 623/1050] R2[172/600], Temp: 0.8106, Energy: -30.419015-0.000038j
[2025-08-26 22:35:48] [Iter 624/1050] R2[173/600], Temp: 0.8085, Energy: -30.415438-0.000474j
[2025-08-26 22:35:50] [Iter 625/1050] R2[174/600], Temp: 0.8065, Energy: -30.426746+0.003026j
[2025-08-26 22:35:53] [Iter 626/1050] R2[175/600], Temp: 0.8044, Energy: -30.415939-0.001683j
[2025-08-26 22:35:56] [Iter 627/1050] R2[176/600], Temp: 0.8023, Energy: -30.410066-0.001316j
[2025-08-26 22:35:58] [Iter 628/1050] R2[177/600], Temp: 0.8002, Energy: -30.421813+0.000937j
[2025-08-26 22:36:01] [Iter 629/1050] R2[178/600], Temp: 0.7981, Energy: -30.411376+0.001041j
[2025-08-26 22:36:03] [Iter 630/1050] R2[179/600], Temp: 0.7960, Energy: -30.411805-0.000199j
[2025-08-26 22:36:06] [Iter 631/1050] R2[180/600], Temp: 0.7939, Energy: -30.413859+0.000845j
[2025-08-26 22:36:09] [Iter 632/1050] R2[181/600], Temp: 0.7918, Energy: -30.411813+0.002161j
[2025-08-26 22:36:11] [Iter 633/1050] R2[182/600], Temp: 0.7896, Energy: -30.422764+0.001025j
[2025-08-26 22:36:14] [Iter 634/1050] R2[183/600], Temp: 0.7875, Energy: -30.412084+0.001288j
[2025-08-26 22:36:16] [Iter 635/1050] R2[184/600], Temp: 0.7854, Energy: -30.415846+0.000111j
[2025-08-26 22:36:19] [Iter 636/1050] R2[185/600], Temp: 0.7832, Energy: -30.423522-0.001485j
[2025-08-26 22:36:22] [Iter 637/1050] R2[186/600], Temp: 0.7810, Energy: -30.401271-0.002115j
[2025-08-26 22:36:24] [Iter 638/1050] R2[187/600], Temp: 0.7789, Energy: -30.410937+0.001741j
[2025-08-26 22:36:27] [Iter 639/1050] R2[188/600], Temp: 0.7767, Energy: -30.412653+0.002800j
[2025-08-26 22:36:29] [Iter 640/1050] R2[189/600], Temp: 0.7745, Energy: -30.417324+0.000932j
[2025-08-26 22:36:32] [Iter 641/1050] R2[190/600], Temp: 0.7723, Energy: -30.412846-0.003264j
[2025-08-26 22:36:35] [Iter 642/1050] R2[191/600], Temp: 0.7701, Energy: -30.416814+0.001519j
[2025-08-26 22:36:37] [Iter 643/1050] R2[192/600], Temp: 0.7679, Energy: -30.416920+0.002098j
[2025-08-26 22:36:40] [Iter 644/1050] R2[193/600], Temp: 0.7657, Energy: -30.408815+0.003721j
[2025-08-26 22:36:43] [Iter 645/1050] R2[194/600], Temp: 0.7635, Energy: -30.418202-0.000471j
[2025-08-26 22:36:45] [Iter 646/1050] R2[195/600], Temp: 0.7612, Energy: -30.420603-0.001160j
[2025-08-26 22:36:48] [Iter 647/1050] R2[196/600], Temp: 0.7590, Energy: -30.422449-0.002684j
[2025-08-26 22:36:50] [Iter 648/1050] R2[197/600], Temp: 0.7568, Energy: -30.422480+0.000399j
[2025-08-26 22:36:53] [Iter 649/1050] R2[198/600], Temp: 0.7545, Energy: -30.418856-0.002545j
[2025-08-26 22:36:56] [Iter 650/1050] R2[199/600], Temp: 0.7523, Energy: -30.409431+0.002821j
[2025-08-26 22:36:58] [Iter 651/1050] R2[200/600], Temp: 0.7500, Energy: -30.415867-0.002494j
[2025-08-26 22:37:01] [Iter 652/1050] R2[201/600], Temp: 0.7477, Energy: -30.418976-0.004947j
[2025-08-26 22:37:03] [Iter 653/1050] R2[202/600], Temp: 0.7455, Energy: -30.406385-0.002191j
[2025-08-26 22:37:06] [Iter 654/1050] R2[203/600], Temp: 0.7432, Energy: -30.405963-0.000376j
[2025-08-26 22:37:09] [Iter 655/1050] R2[204/600], Temp: 0.7409, Energy: -30.414240-0.001786j
[2025-08-26 22:37:11] [Iter 656/1050] R2[205/600], Temp: 0.7386, Energy: -30.414039+0.004473j
[2025-08-26 22:37:14] [Iter 657/1050] R2[206/600], Temp: 0.7363, Energy: -30.412812-0.003804j
[2025-08-26 22:37:16] [Iter 658/1050] R2[207/600], Temp: 0.7340, Energy: -30.412532+0.002241j
[2025-08-26 22:37:19] [Iter 659/1050] R2[208/600], Temp: 0.7316, Energy: -30.412865-0.000499j
[2025-08-26 22:37:22] [Iter 660/1050] R2[209/600], Temp: 0.7293, Energy: -30.415300-0.000187j
[2025-08-26 22:37:24] [Iter 661/1050] R2[210/600], Temp: 0.7270, Energy: -30.413036-0.002216j
[2025-08-26 22:37:27] [Iter 662/1050] R2[211/600], Temp: 0.7247, Energy: -30.413884+0.001855j
[2025-08-26 22:37:29] [Iter 663/1050] R2[212/600], Temp: 0.7223, Energy: -30.404106-0.000748j
[2025-08-26 22:37:32] [Iter 664/1050] R2[213/600], Temp: 0.7200, Energy: -30.409276-0.000803j
[2025-08-26 22:37:35] [Iter 665/1050] R2[214/600], Temp: 0.7176, Energy: -30.411337+0.000360j
[2025-08-26 22:37:37] [Iter 666/1050] R2[215/600], Temp: 0.7153, Energy: -30.424429+0.001804j
[2025-08-26 22:37:40] [Iter 667/1050] R2[216/600], Temp: 0.7129, Energy: -30.417975-0.000809j
[2025-08-26 22:37:42] [Iter 668/1050] R2[217/600], Temp: 0.7105, Energy: -30.410965+0.000960j
[2025-08-26 22:37:45] [Iter 669/1050] R2[218/600], Temp: 0.7081, Energy: -30.423539+0.001684j
[2025-08-26 22:37:48] [Iter 670/1050] R2[219/600], Temp: 0.7058, Energy: -30.417266-0.001307j
[2025-08-26 22:37:50] [Iter 671/1050] R2[220/600], Temp: 0.7034, Energy: -30.403410-0.000769j
[2025-08-26 22:37:53] [Iter 672/1050] R2[221/600], Temp: 0.7010, Energy: -30.409224-0.001667j
[2025-08-26 22:37:56] [Iter 673/1050] R2[222/600], Temp: 0.6986, Energy: -30.419226+0.001825j
[2025-08-26 22:37:58] [Iter 674/1050] R2[223/600], Temp: 0.6962, Energy: -30.414221+0.006445j
[2025-08-26 22:38:01] [Iter 675/1050] R2[224/600], Temp: 0.6938, Energy: -30.403633-0.003704j
[2025-08-26 22:38:03] [Iter 676/1050] R2[225/600], Temp: 0.6913, Energy: -30.409914-0.001962j
[2025-08-26 22:38:06] [Iter 677/1050] R2[226/600], Temp: 0.6889, Energy: -30.414529+0.000103j
[2025-08-26 22:38:09] [Iter 678/1050] R2[227/600], Temp: 0.6865, Energy: -30.410979-0.001495j
[2025-08-26 22:38:11] [Iter 679/1050] R2[228/600], Temp: 0.6841, Energy: -30.412559+0.001125j
[2025-08-26 22:38:14] [Iter 680/1050] R2[229/600], Temp: 0.6816, Energy: -30.407387+0.000140j
[2025-08-26 22:38:16] [Iter 681/1050] R2[230/600], Temp: 0.6792, Energy: -30.416499+0.000815j
[2025-08-26 22:38:19] [Iter 682/1050] R2[231/600], Temp: 0.6767, Energy: -30.408475+0.001161j
[2025-08-26 22:38:22] [Iter 683/1050] R2[232/600], Temp: 0.6743, Energy: -30.412301-0.000108j
[2025-08-26 22:38:24] [Iter 684/1050] R2[233/600], Temp: 0.6718, Energy: -30.409932-0.000117j
[2025-08-26 22:38:27] [Iter 685/1050] R2[234/600], Temp: 0.6694, Energy: -30.414604-0.000290j
[2025-08-26 22:38:29] [Iter 686/1050] R2[235/600], Temp: 0.6669, Energy: -30.422406-0.001028j
[2025-08-26 22:38:32] [Iter 687/1050] R2[236/600], Temp: 0.6644, Energy: -30.413410-0.001791j
[2025-08-26 22:38:35] [Iter 688/1050] R2[237/600], Temp: 0.6620, Energy: -30.413617-0.001218j
[2025-08-26 22:38:37] [Iter 689/1050] R2[238/600], Temp: 0.6595, Energy: -30.417495-0.002354j
[2025-08-26 22:38:40] [Iter 690/1050] R2[239/600], Temp: 0.6570, Energy: -30.417357-0.001444j
[2025-08-26 22:38:42] [Iter 691/1050] R2[240/600], Temp: 0.6545, Energy: -30.409552+0.004559j
[2025-08-26 22:38:45] [Iter 692/1050] R2[241/600], Temp: 0.6520, Energy: -30.409349+0.001424j
[2025-08-26 22:38:48] [Iter 693/1050] R2[242/600], Temp: 0.6495, Energy: -30.410881-0.000330j
[2025-08-26 22:38:50] [Iter 694/1050] R2[243/600], Temp: 0.6470, Energy: -30.411711+0.000745j
[2025-08-26 22:38:53] [Iter 695/1050] R2[244/600], Temp: 0.6445, Energy: -30.416230+0.001103j
[2025-08-26 22:38:55] [Iter 696/1050] R2[245/600], Temp: 0.6420, Energy: -30.416826-0.000460j
[2025-08-26 22:38:58] [Iter 697/1050] R2[246/600], Temp: 0.6395, Energy: -30.424134-0.004090j
[2025-08-26 22:39:01] [Iter 698/1050] R2[247/600], Temp: 0.6370, Energy: -30.410183-0.000300j
[2025-08-26 22:39:03] [Iter 699/1050] R2[248/600], Temp: 0.6345, Energy: -30.401286-0.001266j
[2025-08-26 22:39:06] [Iter 700/1050] R2[249/600], Temp: 0.6319, Energy: -30.402770+0.002244j
[2025-08-26 22:39:06] ✓ Checkpoint saved: checkpoint_iter_000700.pkl
[2025-08-26 22:39:08] [Iter 701/1050] R2[250/600], Temp: 0.6294, Energy: -30.407553-0.006268j
[2025-08-26 22:39:11] [Iter 702/1050] R2[251/600], Temp: 0.6269, Energy: -30.409792+0.001468j
[2025-08-26 22:39:14] [Iter 703/1050] R2[252/600], Temp: 0.6243, Energy: -30.423433-0.000079j
[2025-08-26 22:39:16] [Iter 704/1050] R2[253/600], Temp: 0.6218, Energy: -30.419761-0.001979j
[2025-08-26 22:39:19] [Iter 705/1050] R2[254/600], Temp: 0.6193, Energy: -30.416284-0.001534j
[2025-08-26 22:39:22] [Iter 706/1050] R2[255/600], Temp: 0.6167, Energy: -30.405545+0.003190j
[2025-08-26 22:39:24] [Iter 707/1050] R2[256/600], Temp: 0.6142, Energy: -30.412361+0.005595j
[2025-08-26 22:39:27] [Iter 708/1050] R2[257/600], Temp: 0.6116, Energy: -30.419395-0.000276j
[2025-08-26 22:39:29] [Iter 709/1050] R2[258/600], Temp: 0.6091, Energy: -30.406542-0.000947j
[2025-08-26 22:39:32] [Iter 710/1050] R2[259/600], Temp: 0.6065, Energy: -30.407141-0.000852j
[2025-08-26 22:39:35] [Iter 711/1050] R2[260/600], Temp: 0.6040, Energy: -30.415912+0.000835j
[2025-08-26 22:39:37] [Iter 712/1050] R2[261/600], Temp: 0.6014, Energy: -30.411739+0.003104j
[2025-08-26 22:39:40] [Iter 713/1050] R2[262/600], Temp: 0.5988, Energy: -30.406639-0.001948j
[2025-08-26 22:39:42] [Iter 714/1050] R2[263/600], Temp: 0.5963, Energy: -30.414872-0.002980j
[2025-08-26 22:39:45] [Iter 715/1050] R2[264/600], Temp: 0.5937, Energy: -30.421340+0.000353j
[2025-08-26 22:39:48] [Iter 716/1050] R2[265/600], Temp: 0.5911, Energy: -30.407670-0.001145j
[2025-08-26 22:39:50] [Iter 717/1050] R2[266/600], Temp: 0.5885, Energy: -30.408396+0.000837j
[2025-08-26 22:39:53] [Iter 718/1050] R2[267/600], Temp: 0.5860, Energy: -30.411839-0.003837j
[2025-08-26 22:39:55] [Iter 719/1050] R2[268/600], Temp: 0.5834, Energy: -30.405061+0.000346j
[2025-08-26 22:39:58] [Iter 720/1050] R2[269/600], Temp: 0.5808, Energy: -30.425584+0.000527j
[2025-08-26 22:40:01] [Iter 721/1050] R2[270/600], Temp: 0.5782, Energy: -30.412584-0.000103j
[2025-08-26 22:40:03] [Iter 722/1050] R2[271/600], Temp: 0.5756, Energy: -30.416351-0.003379j
[2025-08-26 22:40:06] [Iter 723/1050] R2[272/600], Temp: 0.5730, Energy: -30.395317-0.000882j
[2025-08-26 22:40:08] [Iter 724/1050] R2[273/600], Temp: 0.5705, Energy: -30.417868-0.000406j
[2025-08-26 22:40:11] [Iter 725/1050] R2[274/600], Temp: 0.5679, Energy: -30.411295-0.000674j
[2025-08-26 22:40:14] [Iter 726/1050] R2[275/600], Temp: 0.5653, Energy: -30.402417+0.001924j
[2025-08-26 22:40:16] [Iter 727/1050] R2[276/600], Temp: 0.5627, Energy: -30.414241+0.000910j
[2025-08-26 22:40:19] [Iter 728/1050] R2[277/600], Temp: 0.5601, Energy: -30.424561+0.002019j
[2025-08-26 22:40:21] [Iter 729/1050] R2[278/600], Temp: 0.5575, Energy: -30.412785-0.001420j
[2025-08-26 22:40:24] [Iter 730/1050] R2[279/600], Temp: 0.5549, Energy: -30.416568+0.001771j
[2025-08-26 22:40:27] [Iter 731/1050] R2[280/600], Temp: 0.5523, Energy: -30.411664-0.002211j
[2025-08-26 22:40:29] [Iter 732/1050] R2[281/600], Temp: 0.5497, Energy: -30.413818+0.000719j
[2025-08-26 22:40:32] [Iter 733/1050] R2[282/600], Temp: 0.5471, Energy: -30.412527-0.000459j
[2025-08-26 22:40:35] [Iter 734/1050] R2[283/600], Temp: 0.5444, Energy: -30.416574+0.000137j
[2025-08-26 22:40:37] [Iter 735/1050] R2[284/600], Temp: 0.5418, Energy: -30.422893+0.006584j
[2025-08-26 22:40:40] [Iter 736/1050] R2[285/600], Temp: 0.5392, Energy: -30.412184+0.000577j
[2025-08-26 22:40:42] [Iter 737/1050] R2[286/600], Temp: 0.5366, Energy: -30.416152+0.001052j
[2025-08-26 22:40:45] [Iter 738/1050] R2[287/600], Temp: 0.5340, Energy: -30.416836-0.002141j
[2025-08-26 22:40:48] [Iter 739/1050] R2[288/600], Temp: 0.5314, Energy: -30.418094-0.001158j
[2025-08-26 22:40:50] [Iter 740/1050] R2[289/600], Temp: 0.5288, Energy: -30.412680+0.002233j
[2025-08-26 22:40:53] [Iter 741/1050] R2[290/600], Temp: 0.5262, Energy: -30.422278-0.007591j
[2025-08-26 22:40:55] [Iter 742/1050] R2[291/600], Temp: 0.5236, Energy: -30.410579+0.000770j
[2025-08-26 22:40:58] [Iter 743/1050] R2[292/600], Temp: 0.5209, Energy: -30.408191+0.001913j
[2025-08-26 22:41:01] [Iter 744/1050] R2[293/600], Temp: 0.5183, Energy: -30.411792+0.000306j
[2025-08-26 22:41:03] [Iter 745/1050] R2[294/600], Temp: 0.5157, Energy: -30.412109-0.001461j
[2025-08-26 22:41:06] [Iter 746/1050] R2[295/600], Temp: 0.5131, Energy: -30.414568-0.000630j
[2025-08-26 22:41:08] [Iter 747/1050] R2[296/600], Temp: 0.5105, Energy: -30.404355-0.000264j
[2025-08-26 22:41:11] [Iter 748/1050] R2[297/600], Temp: 0.5079, Energy: -30.410882-0.000242j
[2025-08-26 22:41:14] [Iter 749/1050] R2[298/600], Temp: 0.5052, Energy: -30.416906+0.001579j
[2025-08-26 22:41:16] [Iter 750/1050] R2[299/600], Temp: 0.5026, Energy: -30.405858-0.002375j
[2025-08-26 22:41:19] [Iter 751/1050] R2[300/600], Temp: 0.5000, Energy: -30.414593-0.002389j
[2025-08-26 22:41:21] [Iter 752/1050] R2[301/600], Temp: 0.4974, Energy: -30.427367-0.001767j
[2025-08-26 22:41:24] [Iter 753/1050] R2[302/600], Temp: 0.4948, Energy: -30.410934-0.001730j
[2025-08-26 22:41:27] [Iter 754/1050] R2[303/600], Temp: 0.4921, Energy: -30.407864-0.000824j
[2025-08-26 22:41:29] [Iter 755/1050] R2[304/600], Temp: 0.4895, Energy: -30.415861-0.001799j
[2025-08-26 22:41:32] [Iter 756/1050] R2[305/600], Temp: 0.4869, Energy: -30.413051-0.001882j
[2025-08-26 22:41:34] [Iter 757/1050] R2[306/600], Temp: 0.4843, Energy: -30.413335+0.001032j
[2025-08-26 22:41:37] [Iter 758/1050] R2[307/600], Temp: 0.4817, Energy: -30.418206+0.002618j
[2025-08-26 22:41:40] [Iter 759/1050] R2[308/600], Temp: 0.4791, Energy: -30.413689-0.000899j
[2025-08-26 22:41:42] [Iter 760/1050] R2[309/600], Temp: 0.4764, Energy: -30.413984-0.001635j
[2025-08-26 22:41:45] [Iter 761/1050] R2[310/600], Temp: 0.4738, Energy: -30.412495+0.002820j
[2025-08-26 22:41:47] [Iter 762/1050] R2[311/600], Temp: 0.4712, Energy: -30.412223-0.000031j
[2025-08-26 22:41:50] [Iter 763/1050] R2[312/600], Temp: 0.4686, Energy: -30.412362+0.002587j
[2025-08-26 22:41:53] [Iter 764/1050] R2[313/600], Temp: 0.4660, Energy: -30.418591-0.001716j
[2025-08-26 22:41:55] [Iter 765/1050] R2[314/600], Temp: 0.4634, Energy: -30.411094-0.001667j
[2025-08-26 22:41:58] [Iter 766/1050] R2[315/600], Temp: 0.4608, Energy: -30.421387+0.001296j
[2025-08-26 22:42:01] [Iter 767/1050] R2[316/600], Temp: 0.4582, Energy: -30.417855-0.000637j
[2025-08-26 22:42:03] [Iter 768/1050] R2[317/600], Temp: 0.4556, Energy: -30.406933+0.001763j
[2025-08-26 22:42:06] [Iter 769/1050] R2[318/600], Temp: 0.4529, Energy: -30.406549-0.001453j
[2025-08-26 22:42:08] [Iter 770/1050] R2[319/600], Temp: 0.4503, Energy: -30.408328+0.001658j
[2025-08-26 22:42:11] [Iter 771/1050] R2[320/600], Temp: 0.4477, Energy: -30.419840+0.001860j
[2025-08-26 22:42:14] [Iter 772/1050] R2[321/600], Temp: 0.4451, Energy: -30.415449-0.000269j
[2025-08-26 22:42:16] [Iter 773/1050] R2[322/600], Temp: 0.4425, Energy: -30.406590+0.004071j
[2025-08-26 22:42:19] [Iter 774/1050] R2[323/600], Temp: 0.4399, Energy: -30.414404-0.001418j
[2025-08-26 22:42:21] [Iter 775/1050] R2[324/600], Temp: 0.4373, Energy: -30.417954+0.001222j
[2025-08-26 22:42:24] [Iter 776/1050] R2[325/600], Temp: 0.4347, Energy: -30.417251-0.001451j
[2025-08-26 22:42:27] [Iter 777/1050] R2[326/600], Temp: 0.4321, Energy: -30.406585+0.000032j
[2025-08-26 22:42:29] [Iter 778/1050] R2[327/600], Temp: 0.4295, Energy: -30.410658-0.002637j
[2025-08-26 22:42:32] [Iter 779/1050] R2[328/600], Temp: 0.4270, Energy: -30.415963-0.005409j
[2025-08-26 22:42:34] [Iter 780/1050] R2[329/600], Temp: 0.4244, Energy: -30.408486-0.001491j
[2025-08-26 22:42:37] [Iter 781/1050] R2[330/600], Temp: 0.4218, Energy: -30.413286-0.002431j
[2025-08-26 22:42:40] [Iter 782/1050] R2[331/600], Temp: 0.4192, Energy: -30.421352-0.001489j
[2025-08-26 22:42:42] [Iter 783/1050] R2[332/600], Temp: 0.4166, Energy: -30.415423-0.000971j
[2025-08-26 22:42:45] [Iter 784/1050] R2[333/600], Temp: 0.4140, Energy: -30.409800+0.000967j
[2025-08-26 22:42:47] [Iter 785/1050] R2[334/600], Temp: 0.4115, Energy: -30.410096-0.001301j
[2025-08-26 22:42:50] [Iter 786/1050] R2[335/600], Temp: 0.4089, Energy: -30.417516+0.000295j
[2025-08-26 22:42:53] [Iter 787/1050] R2[336/600], Temp: 0.4063, Energy: -30.416836-0.003138j
[2025-08-26 22:42:55] [Iter 788/1050] R2[337/600], Temp: 0.4037, Energy: -30.419763-0.003445j
[2025-08-26 22:42:58] [Iter 789/1050] R2[338/600], Temp: 0.4012, Energy: -30.415313-0.002328j
[2025-08-26 22:43:00] [Iter 790/1050] R2[339/600], Temp: 0.3986, Energy: -30.417644-0.000008j
[2025-08-26 22:43:03] [Iter 791/1050] R2[340/600], Temp: 0.3960, Energy: -30.417369+0.002328j
[2025-08-26 22:43:06] [Iter 792/1050] R2[341/600], Temp: 0.3935, Energy: -30.421283-0.002651j
[2025-08-26 22:43:08] [Iter 793/1050] R2[342/600], Temp: 0.3909, Energy: -30.421567+0.004899j
[2025-08-26 22:43:11] [Iter 794/1050] R2[343/600], Temp: 0.3884, Energy: -30.412534+0.001472j
[2025-08-26 22:43:13] [Iter 795/1050] R2[344/600], Temp: 0.3858, Energy: -30.414549+0.001751j
[2025-08-26 22:43:16] [Iter 796/1050] R2[345/600], Temp: 0.3833, Energy: -30.411212+0.003852j
[2025-08-26 22:43:19] [Iter 797/1050] R2[346/600], Temp: 0.3807, Energy: -30.409151-0.000170j
[2025-08-26 22:43:21] [Iter 798/1050] R2[347/600], Temp: 0.3782, Energy: -30.406899-0.004782j
[2025-08-26 22:43:24] [Iter 799/1050] R2[348/600], Temp: 0.3757, Energy: -30.407108+0.001459j
[2025-08-26 22:43:27] [Iter 800/1050] R2[349/600], Temp: 0.3731, Energy: -30.416211-0.000007j
[2025-08-26 22:43:27] ✓ Checkpoint saved: checkpoint_iter_000800.pkl
[2025-08-26 22:43:30] [Iter 801/1050] R2[350/600], Temp: 0.3706, Energy: -30.418318+0.001838j
[2025-08-26 22:43:33] [Iter 802/1050] R2[351/600], Temp: 0.3681, Energy: -30.410241+0.000955j
[2025-08-26 22:43:35] [Iter 803/1050] R2[352/600], Temp: 0.3655, Energy: -30.417757+0.002081j
[2025-08-26 22:43:38] [Iter 804/1050] R2[353/600], Temp: 0.3630, Energy: -30.419481+0.003615j
[2025-08-26 22:43:41] [Iter 805/1050] R2[354/600], Temp: 0.3605, Energy: -30.421076-0.001466j
[2025-08-26 22:43:44] [Iter 806/1050] R2[355/600], Temp: 0.3580, Energy: -30.414928-0.001284j
[2025-08-26 22:43:47] [Iter 807/1050] R2[356/600], Temp: 0.3555, Energy: -30.418649+0.000813j
[2025-08-26 22:43:49] [Iter 808/1050] R2[357/600], Temp: 0.3530, Energy: -30.410994-0.002728j
[2025-08-26 22:43:52] [Iter 809/1050] R2[358/600], Temp: 0.3505, Energy: -30.414247+0.000947j
[2025-08-26 22:43:55] [Iter 810/1050] R2[359/600], Temp: 0.3480, Energy: -30.423213-0.000351j
[2025-08-26 22:43:57] [Iter 811/1050] R2[360/600], Temp: 0.3455, Energy: -30.410109-0.000134j
[2025-08-26 22:44:00] [Iter 812/1050] R2[361/600], Temp: 0.3430, Energy: -30.415371-0.001028j
[2025-08-26 22:44:03] [Iter 813/1050] R2[362/600], Temp: 0.3405, Energy: -30.408331+0.002258j
[2025-08-26 22:44:06] [Iter 814/1050] R2[363/600], Temp: 0.3380, Energy: -30.408299-0.002325j
[2025-08-26 22:44:09] [Iter 815/1050] R2[364/600], Temp: 0.3356, Energy: -30.423325+0.001458j
[2025-08-26 22:44:11] [Iter 816/1050] R2[365/600], Temp: 0.3331, Energy: -30.417177-0.001617j
[2025-08-26 22:44:14] [Iter 817/1050] R2[366/600], Temp: 0.3306, Energy: -30.407729-0.001082j
[2025-08-26 22:44:17] [Iter 818/1050] R2[367/600], Temp: 0.3282, Energy: -30.414149-0.000107j
[2025-08-26 22:44:19] [Iter 819/1050] R2[368/600], Temp: 0.3257, Energy: -30.409557-0.000322j
[2025-08-26 22:44:22] [Iter 820/1050] R2[369/600], Temp: 0.3233, Energy: -30.416486-0.001219j
[2025-08-26 22:44:25] [Iter 821/1050] R2[370/600], Temp: 0.3208, Energy: -30.404289+0.000769j
[2025-08-26 22:44:27] [Iter 822/1050] R2[371/600], Temp: 0.3184, Energy: -30.422836-0.003281j
[2025-08-26 22:44:30] [Iter 823/1050] R2[372/600], Temp: 0.3159, Energy: -30.414061+0.001744j
[2025-08-26 22:44:32] [Iter 824/1050] R2[373/600], Temp: 0.3135, Energy: -30.421174-0.001248j
[2025-08-26 22:44:35] [Iter 825/1050] R2[374/600], Temp: 0.3111, Energy: -30.410855-0.001566j
[2025-08-26 22:44:38] [Iter 826/1050] R2[375/600], Temp: 0.3087, Energy: -30.414751+0.000274j
[2025-08-26 22:44:40] [Iter 827/1050] R2[376/600], Temp: 0.3062, Energy: -30.402517-0.001142j
[2025-08-26 22:44:43] [Iter 828/1050] R2[377/600], Temp: 0.3038, Energy: -30.409939+0.000795j
[2025-08-26 22:44:45] [Iter 829/1050] R2[378/600], Temp: 0.3014, Energy: -30.413843+0.002112j
[2025-08-26 22:44:48] [Iter 830/1050] R2[379/600], Temp: 0.2990, Energy: -30.424090+0.001324j
[2025-08-26 22:44:51] [Iter 831/1050] R2[380/600], Temp: 0.2966, Energy: -30.420987-0.001732j
[2025-08-26 22:44:53] [Iter 832/1050] R2[381/600], Temp: 0.2942, Energy: -30.411104+0.000922j
[2025-08-26 22:44:56] [Iter 833/1050] R2[382/600], Temp: 0.2919, Energy: -30.414122-0.001067j
[2025-08-26 22:44:58] [Iter 834/1050] R2[383/600], Temp: 0.2895, Energy: -30.416167+0.000057j
[2025-08-26 22:45:01] [Iter 835/1050] R2[384/600], Temp: 0.2871, Energy: -30.416151+0.000614j
[2025-08-26 22:45:04] [Iter 836/1050] R2[385/600], Temp: 0.2847, Energy: -30.415601+0.000107j
[2025-08-26 22:45:06] [Iter 837/1050] R2[386/600], Temp: 0.2824, Energy: -30.411738+0.001229j
[2025-08-26 22:45:09] [Iter 838/1050] R2[387/600], Temp: 0.2800, Energy: -30.400102+0.001590j
[2025-08-26 22:45:12] [Iter 839/1050] R2[388/600], Temp: 0.2777, Energy: -30.418266-0.001484j
[2025-08-26 22:45:14] [Iter 840/1050] R2[389/600], Temp: 0.2753, Energy: -30.423255+0.001330j
[2025-08-26 22:45:17] [Iter 841/1050] R2[390/600], Temp: 0.2730, Energy: -30.410205-0.000384j
[2025-08-26 22:45:19] [Iter 842/1050] R2[391/600], Temp: 0.2707, Energy: -30.410793+0.001915j
[2025-08-26 22:45:22] [Iter 843/1050] R2[392/600], Temp: 0.2684, Energy: -30.411550+0.001339j
[2025-08-26 22:45:25] [Iter 844/1050] R2[393/600], Temp: 0.2660, Energy: -30.415753+0.000374j
[2025-08-26 22:45:27] [Iter 845/1050] R2[394/600], Temp: 0.2637, Energy: -30.426713-0.002291j
[2025-08-26 22:45:30] [Iter 846/1050] R2[395/600], Temp: 0.2614, Energy: -30.413148+0.000799j
[2025-08-26 22:45:32] [Iter 847/1050] R2[396/600], Temp: 0.2591, Energy: -30.413757-0.000468j
[2025-08-26 22:45:35] [Iter 848/1050] R2[397/600], Temp: 0.2568, Energy: -30.418570-0.000379j
[2025-08-26 22:45:38] [Iter 849/1050] R2[398/600], Temp: 0.2545, Energy: -30.417162-0.002776j
[2025-08-26 22:45:40] [Iter 850/1050] R2[399/600], Temp: 0.2523, Energy: -30.412887+0.002501j
[2025-08-26 22:45:43] [Iter 851/1050] R2[400/600], Temp: 0.2500, Energy: -30.411717-0.001430j
[2025-08-26 22:45:45] [Iter 852/1050] R2[401/600], Temp: 0.2477, Energy: -30.410150-0.000833j
[2025-08-26 22:45:48] [Iter 853/1050] R2[402/600], Temp: 0.2455, Energy: -30.407535+0.001851j
[2025-08-26 22:45:51] [Iter 854/1050] R2[403/600], Temp: 0.2432, Energy: -30.413387-0.000399j
[2025-08-26 22:45:53] [Iter 855/1050] R2[404/600], Temp: 0.2410, Energy: -30.415625-0.001194j
[2025-08-26 22:45:56] [Iter 856/1050] R2[405/600], Temp: 0.2388, Energy: -30.411955-0.000227j
[2025-08-26 22:45:58] [Iter 857/1050] R2[406/600], Temp: 0.2365, Energy: -30.423182+0.002872j
[2025-08-26 22:46:01] [Iter 858/1050] R2[407/600], Temp: 0.2343, Energy: -30.405996+0.000474j
[2025-08-26 22:46:04] [Iter 859/1050] R2[408/600], Temp: 0.2321, Energy: -30.417608+0.002803j
[2025-08-26 22:46:06] [Iter 860/1050] R2[409/600], Temp: 0.2299, Energy: -30.416140+0.003645j
[2025-08-26 22:46:09] [Iter 861/1050] R2[410/600], Temp: 0.2277, Energy: -30.419209+0.000911j
[2025-08-26 22:46:11] [Iter 862/1050] R2[411/600], Temp: 0.2255, Energy: -30.418541-0.000846j
[2025-08-26 22:46:14] [Iter 863/1050] R2[412/600], Temp: 0.2233, Energy: -30.403120-0.003347j
[2025-08-26 22:46:17] [Iter 864/1050] R2[413/600], Temp: 0.2211, Energy: -30.407829-0.002682j
[2025-08-26 22:46:19] [Iter 865/1050] R2[414/600], Temp: 0.2190, Energy: -30.423022-0.001710j
[2025-08-26 22:46:22] [Iter 866/1050] R2[415/600], Temp: 0.2168, Energy: -30.413477+0.001348j
[2025-08-26 22:46:25] [Iter 867/1050] R2[416/600], Temp: 0.2146, Energy: -30.412180-0.002123j
[2025-08-26 22:46:27] [Iter 868/1050] R2[417/600], Temp: 0.2125, Energy: -30.411980-0.001414j
[2025-08-26 22:46:30] [Iter 869/1050] R2[418/600], Temp: 0.2104, Energy: -30.414209+0.002206j
[2025-08-26 22:46:32] [Iter 870/1050] R2[419/600], Temp: 0.2082, Energy: -30.405990-0.002635j
[2025-08-26 22:46:35] [Iter 871/1050] R2[420/600], Temp: 0.2061, Energy: -30.421139-0.002169j
[2025-08-26 22:46:38] [Iter 872/1050] R2[421/600], Temp: 0.2040, Energy: -30.417924+0.000117j
[2025-08-26 22:46:40] [Iter 873/1050] R2[422/600], Temp: 0.2019, Energy: -30.416122-0.001586j
[2025-08-26 22:46:43] [Iter 874/1050] R2[423/600], Temp: 0.1998, Energy: -30.412814+0.003431j
[2025-08-26 22:46:45] [Iter 875/1050] R2[424/600], Temp: 0.1977, Energy: -30.417625+0.002489j
[2025-08-26 22:46:48] [Iter 876/1050] R2[425/600], Temp: 0.1956, Energy: -30.408673+0.001734j
[2025-08-26 22:46:51] [Iter 877/1050] R2[426/600], Temp: 0.1935, Energy: -30.408201-0.000292j
[2025-08-26 22:46:53] [Iter 878/1050] R2[427/600], Temp: 0.1915, Energy: -30.417752+0.001110j
[2025-08-26 22:46:56] [Iter 879/1050] R2[428/600], Temp: 0.1894, Energy: -30.418525+0.003281j
[2025-08-26 22:46:58] [Iter 880/1050] R2[429/600], Temp: 0.1874, Energy: -30.416322+0.002986j
[2025-08-26 22:47:01] [Iter 881/1050] R2[430/600], Temp: 0.1853, Energy: -30.421183+0.002088j
[2025-08-26 22:47:04] [Iter 882/1050] R2[431/600], Temp: 0.1833, Energy: -30.415224+0.000386j
[2025-08-26 22:47:06] [Iter 883/1050] R2[432/600], Temp: 0.1813, Energy: -30.419630-0.001243j
[2025-08-26 22:47:09] [Iter 884/1050] R2[433/600], Temp: 0.1793, Energy: -30.414704+0.001995j
[2025-08-26 22:47:11] [Iter 885/1050] R2[434/600], Temp: 0.1773, Energy: -30.410534+0.003341j
[2025-08-26 22:47:14] [Iter 886/1050] R2[435/600], Temp: 0.1753, Energy: -30.408950-0.002952j
[2025-08-26 22:47:17] [Iter 887/1050] R2[436/600], Temp: 0.1733, Energy: -30.415436-0.000780j
[2025-08-26 22:47:19] [Iter 888/1050] R2[437/600], Temp: 0.1713, Energy: -30.410658+0.000048j
[2025-08-26 22:47:22] [Iter 889/1050] R2[438/600], Temp: 0.1693, Energy: -30.410571-0.001647j
[2025-08-26 22:47:24] [Iter 890/1050] R2[439/600], Temp: 0.1674, Energy: -30.420379+0.000354j
[2025-08-26 22:47:27] [Iter 891/1050] R2[440/600], Temp: 0.1654, Energy: -30.411770-0.000053j
[2025-08-26 22:47:30] [Iter 892/1050] R2[441/600], Temp: 0.1635, Energy: -30.417264-0.000682j
[2025-08-26 22:47:32] [Iter 893/1050] R2[442/600], Temp: 0.1616, Energy: -30.407884-0.003672j
[2025-08-26 22:47:35] [Iter 894/1050] R2[443/600], Temp: 0.1596, Energy: -30.425705-0.002573j
[2025-08-26 22:47:37] [Iter 895/1050] R2[444/600], Temp: 0.1577, Energy: -30.414690+0.001014j
[2025-08-26 22:47:40] [Iter 896/1050] R2[445/600], Temp: 0.1558, Energy: -30.418761+0.000006j
[2025-08-26 22:47:43] [Iter 897/1050] R2[446/600], Temp: 0.1539, Energy: -30.426642+0.000239j
[2025-08-26 22:47:45] [Iter 898/1050] R2[447/600], Temp: 0.1520, Energy: -30.408620+0.001211j
[2025-08-26 22:47:48] [Iter 899/1050] R2[448/600], Temp: 0.1502, Energy: -30.420327-0.002293j
[2025-08-26 22:47:51] [Iter 900/1050] R2[449/600], Temp: 0.1483, Energy: -30.418511-0.001253j
[2025-08-26 22:47:51] ✓ Checkpoint saved: checkpoint_iter_000900.pkl
[2025-08-26 22:47:53] [Iter 901/1050] R2[450/600], Temp: 0.1464, Energy: -30.414220+0.002283j
[2025-08-26 22:47:56] [Iter 902/1050] R2[451/600], Temp: 0.1446, Energy: -30.411163-0.001078j
[2025-08-26 22:47:58] [Iter 903/1050] R2[452/600], Temp: 0.1428, Energy: -30.408321+0.000183j
[2025-08-26 22:48:01] [Iter 904/1050] R2[453/600], Temp: 0.1409, Energy: -30.415744+0.001105j
[2025-08-26 22:48:04] [Iter 905/1050] R2[454/600], Temp: 0.1391, Energy: -30.422164-0.000781j
[2025-08-26 22:48:06] [Iter 906/1050] R2[455/600], Temp: 0.1373, Energy: -30.412896-0.000108j
[2025-08-26 22:48:09] [Iter 907/1050] R2[456/600], Temp: 0.1355, Energy: -30.413986-0.000035j
[2025-08-26 22:48:11] [Iter 908/1050] R2[457/600], Temp: 0.1337, Energy: -30.403854-0.002999j
[2025-08-26 22:48:14] [Iter 909/1050] R2[458/600], Temp: 0.1320, Energy: -30.418944-0.001204j
[2025-08-26 22:48:17] [Iter 910/1050] R2[459/600], Temp: 0.1302, Energy: -30.412279-0.000218j
[2025-08-26 22:48:19] [Iter 911/1050] R2[460/600], Temp: 0.1284, Energy: -30.409157-0.001598j
[2025-08-26 22:48:22] [Iter 912/1050] R2[461/600], Temp: 0.1267, Energy: -30.421374-0.014062j
[2025-08-26 22:48:24] [Iter 913/1050] R2[462/600], Temp: 0.1249, Energy: -30.419257-0.001818j
[2025-08-26 22:48:27] [Iter 914/1050] R2[463/600], Temp: 0.1232, Energy: -30.407982-0.002406j
[2025-08-26 22:48:30] [Iter 915/1050] R2[464/600], Temp: 0.1215, Energy: -30.409407+0.002197j
[2025-08-26 22:48:32] [Iter 916/1050] R2[465/600], Temp: 0.1198, Energy: -30.411101-0.002212j
[2025-08-26 22:48:35] [Iter 917/1050] R2[466/600], Temp: 0.1181, Energy: -30.417179+0.001577j
[2025-08-26 22:48:37] [Iter 918/1050] R2[467/600], Temp: 0.1164, Energy: -30.414506-0.001994j
[2025-08-26 22:48:40] [Iter 919/1050] R2[468/600], Temp: 0.1147, Energy: -30.413538+0.002460j
[2025-08-26 22:48:43] [Iter 920/1050] R2[469/600], Temp: 0.1131, Energy: -30.410972-0.002750j
[2025-08-26 22:48:45] [Iter 921/1050] R2[470/600], Temp: 0.1114, Energy: -30.421099-0.000533j
[2025-08-26 22:48:48] [Iter 922/1050] R2[471/600], Temp: 0.1098, Energy: -30.408693-0.001267j
[2025-08-26 22:48:50] [Iter 923/1050] R2[472/600], Temp: 0.1082, Energy: -30.406256-0.000328j
[2025-08-26 22:48:53] [Iter 924/1050] R2[473/600], Temp: 0.1065, Energy: -30.417242-0.002346j
[2025-08-26 22:48:56] [Iter 925/1050] R2[474/600], Temp: 0.1049, Energy: -30.416027-0.000061j
[2025-08-26 22:48:58] [Iter 926/1050] R2[475/600], Temp: 0.1033, Energy: -30.409202-0.001092j
[2025-08-26 22:49:01] [Iter 927/1050] R2[476/600], Temp: 0.1017, Energy: -30.411124+0.001898j
[2025-08-26 22:49:04] [Iter 928/1050] R2[477/600], Temp: 0.1002, Energy: -30.422503-0.000782j
[2025-08-26 22:49:06] [Iter 929/1050] R2[478/600], Temp: 0.0986, Energy: -30.423671+0.000774j
[2025-08-26 22:49:09] [Iter 930/1050] R2[479/600], Temp: 0.0970, Energy: -30.417387-0.000294j
[2025-08-26 22:49:11] [Iter 931/1050] R2[480/600], Temp: 0.0955, Energy: -30.415217-0.003041j
[2025-08-26 22:49:14] [Iter 932/1050] R2[481/600], Temp: 0.0940, Energy: -30.407352-0.002007j
[2025-08-26 22:49:17] [Iter 933/1050] R2[482/600], Temp: 0.0924, Energy: -30.409225+0.002096j
[2025-08-26 22:49:19] [Iter 934/1050] R2[483/600], Temp: 0.0909, Energy: -30.411335+0.001148j
[2025-08-26 22:49:22] [Iter 935/1050] R2[484/600], Temp: 0.0894, Energy: -30.408012-0.002124j
[2025-08-26 22:49:24] [Iter 936/1050] R2[485/600], Temp: 0.0879, Energy: -30.413714-0.000598j
[2025-08-26 22:49:27] [Iter 937/1050] R2[486/600], Temp: 0.0865, Energy: -30.416174+0.001846j
[2025-08-26 22:49:30] [Iter 938/1050] R2[487/600], Temp: 0.0850, Energy: -30.413492+0.004154j
[2025-08-26 22:49:32] [Iter 939/1050] R2[488/600], Temp: 0.0835, Energy: -30.417174-0.000180j
[2025-08-26 22:49:35] [Iter 940/1050] R2[489/600], Temp: 0.0821, Energy: -30.408207+0.000217j
[2025-08-26 22:49:37] [Iter 941/1050] R2[490/600], Temp: 0.0807, Energy: -30.417883+0.000115j
[2025-08-26 22:49:40] [Iter 942/1050] R2[491/600], Temp: 0.0792, Energy: -30.414234-0.003918j
[2025-08-26 22:49:43] [Iter 943/1050] R2[492/600], Temp: 0.0778, Energy: -30.408158-0.001323j
[2025-08-26 22:49:45] [Iter 944/1050] R2[493/600], Temp: 0.0764, Energy: -30.405910-0.000746j
[2025-08-26 22:49:48] [Iter 945/1050] R2[494/600], Temp: 0.0751, Energy: -30.413682-0.001652j
[2025-08-26 22:49:50] [Iter 946/1050] R2[495/600], Temp: 0.0737, Energy: -30.418875+0.000790j
[2025-08-26 22:49:53] [Iter 947/1050] R2[496/600], Temp: 0.0723, Energy: -30.417294+0.000173j
[2025-08-26 22:49:56] [Iter 948/1050] R2[497/600], Temp: 0.0710, Energy: -30.418038-0.000845j
[2025-08-26 22:49:58] [Iter 949/1050] R2[498/600], Temp: 0.0696, Energy: -30.416667-0.000586j
[2025-08-26 22:50:01] [Iter 950/1050] R2[499/600], Temp: 0.0683, Energy: -30.412519+0.002724j
[2025-08-26 22:50:04] [Iter 951/1050] R2[500/600], Temp: 0.0670, Energy: -30.407311-0.000643j
[2025-08-26 22:50:06] [Iter 952/1050] R2[501/600], Temp: 0.0657, Energy: -30.412573-0.002168j
[2025-08-26 22:50:09] [Iter 953/1050] R2[502/600], Temp: 0.0644, Energy: -30.408460-0.001779j
[2025-08-26 22:50:11] [Iter 954/1050] R2[503/600], Temp: 0.0631, Energy: -30.410843-0.000805j
[2025-08-26 22:50:14] [Iter 955/1050] R2[504/600], Temp: 0.0618, Energy: -30.406024+0.003842j
[2025-08-26 22:50:17] [Iter 956/1050] R2[505/600], Temp: 0.0606, Energy: -30.416823-0.000198j
[2025-08-26 22:50:19] [Iter 957/1050] R2[506/600], Temp: 0.0593, Energy: -30.413959+0.002186j
[2025-08-26 22:50:22] [Iter 958/1050] R2[507/600], Temp: 0.0581, Energy: -30.410425+0.001804j
[2025-08-26 22:50:24] [Iter 959/1050] R2[508/600], Temp: 0.0569, Energy: -30.412172+0.000718j
[2025-08-26 22:50:27] [Iter 960/1050] R2[509/600], Temp: 0.0557, Energy: -30.417307+0.002605j
[2025-08-26 22:50:30] [Iter 961/1050] R2[510/600], Temp: 0.0545, Energy: -30.411328+0.000965j
[2025-08-26 22:50:32] [Iter 962/1050] R2[511/600], Temp: 0.0533, Energy: -30.412911-0.000378j
[2025-08-26 22:50:35] [Iter 963/1050] R2[512/600], Temp: 0.0521, Energy: -30.414466-0.000244j
[2025-08-26 22:50:37] [Iter 964/1050] R2[513/600], Temp: 0.0510, Energy: -30.414334-0.001697j
[2025-08-26 22:50:40] [Iter 965/1050] R2[514/600], Temp: 0.0498, Energy: -30.413716-0.001714j
[2025-08-26 22:50:43] [Iter 966/1050] R2[515/600], Temp: 0.0487, Energy: -30.413117-0.001202j
[2025-08-26 22:50:45] [Iter 967/1050] R2[516/600], Temp: 0.0476, Energy: -30.411214+0.000863j
[2025-08-26 22:50:48] [Iter 968/1050] R2[517/600], Temp: 0.0465, Energy: -30.413901+0.002067j
[2025-08-26 22:50:50] [Iter 969/1050] R2[518/600], Temp: 0.0454, Energy: -30.410810+0.002281j
[2025-08-26 22:50:53] [Iter 970/1050] R2[519/600], Temp: 0.0443, Energy: -30.411225-0.001247j
[2025-08-26 22:50:56] [Iter 971/1050] R2[520/600], Temp: 0.0432, Energy: -30.415631-0.000925j
[2025-08-26 22:50:58] [Iter 972/1050] R2[521/600], Temp: 0.0422, Energy: -30.407138+0.002852j
[2025-08-26 22:51:01] [Iter 973/1050] R2[522/600], Temp: 0.0411, Energy: -30.412490-0.000055j
[2025-08-26 22:51:03] [Iter 974/1050] R2[523/600], Temp: 0.0401, Energy: -30.411325+0.000888j
[2025-08-26 22:51:06] [Iter 975/1050] R2[524/600], Temp: 0.0391, Energy: -30.411297+0.000131j
[2025-08-26 22:51:09] [Iter 976/1050] R2[525/600], Temp: 0.0381, Energy: -30.410866-0.003126j
[2025-08-26 22:51:11] [Iter 977/1050] R2[526/600], Temp: 0.0371, Energy: -30.420479+0.001800j
[2025-08-26 22:51:14] [Iter 978/1050] R2[527/600], Temp: 0.0361, Energy: -30.409083-0.001979j
[2025-08-26 22:51:17] [Iter 979/1050] R2[528/600], Temp: 0.0351, Energy: -30.414902-0.002734j
[2025-08-26 22:51:19] [Iter 980/1050] R2[529/600], Temp: 0.0342, Energy: -30.413682-0.002923j
[2025-08-26 22:51:22] [Iter 981/1050] R2[530/600], Temp: 0.0332, Energy: -30.408874-0.000839j
[2025-08-26 22:51:24] [Iter 982/1050] R2[531/600], Temp: 0.0323, Energy: -30.414556+0.000292j
[2025-08-26 22:51:27] [Iter 983/1050] R2[532/600], Temp: 0.0314, Energy: -30.412098-0.000968j
[2025-08-26 22:51:30] [Iter 984/1050] R2[533/600], Temp: 0.0305, Energy: -30.413228-0.001549j
[2025-08-26 22:51:32] [Iter 985/1050] R2[534/600], Temp: 0.0296, Energy: -30.407678+0.000772j
[2025-08-26 22:51:35] [Iter 986/1050] R2[535/600], Temp: 0.0287, Energy: -30.418006+0.001730j
[2025-08-26 22:51:37] [Iter 987/1050] R2[536/600], Temp: 0.0278, Energy: -30.408529-0.002049j
[2025-08-26 22:51:40] [Iter 988/1050] R2[537/600], Temp: 0.0270, Energy: -30.418985+0.002350j
[2025-08-26 22:51:43] [Iter 989/1050] R2[538/600], Temp: 0.0261, Energy: -30.407076+0.001202j
[2025-08-26 22:51:45] [Iter 990/1050] R2[539/600], Temp: 0.0253, Energy: -30.414688-0.000140j
[2025-08-26 22:51:48] [Iter 991/1050] R2[540/600], Temp: 0.0245, Energy: -30.420674+0.000348j
[2025-08-26 22:51:50] [Iter 992/1050] R2[541/600], Temp: 0.0237, Energy: -30.428656-0.002641j
[2025-08-26 22:51:53] [Iter 993/1050] R2[542/600], Temp: 0.0229, Energy: -30.406699-0.002703j
[2025-08-26 22:51:56] [Iter 994/1050] R2[543/600], Temp: 0.0221, Energy: -30.420777-0.001655j
[2025-08-26 22:51:58] [Iter 995/1050] R2[544/600], Temp: 0.0213, Energy: -30.410127-0.001353j
[2025-08-26 22:52:01] [Iter 996/1050] R2[545/600], Temp: 0.0206, Energy: -30.421993+0.000717j
[2025-08-26 22:52:03] [Iter 997/1050] R2[546/600], Temp: 0.0199, Energy: -30.406424-0.001516j
[2025-08-26 22:52:06] [Iter 998/1050] R2[547/600], Temp: 0.0191, Energy: -30.420719+0.000147j
[2025-08-26 22:52:09] [Iter 999/1050] R2[548/600], Temp: 0.0184, Energy: -30.416231-0.000080j
[2025-08-26 22:52:11] [Iter 1000/1050] R2[549/600], Temp: 0.0177, Energy: -30.407418-0.000891j
[2025-08-26 22:52:11] ✓ Checkpoint saved: checkpoint_iter_001000.pkl
[2025-08-26 22:52:14] [Iter 1001/1050] R2[550/600], Temp: 0.0170, Energy: -30.409605+0.001152j
[2025-08-26 22:52:17] [Iter 1002/1050] R2[551/600], Temp: 0.0164, Energy: -30.420741-0.002687j
[2025-08-26 22:52:19] [Iter 1003/1050] R2[552/600], Temp: 0.0157, Energy: -30.411254+0.001439j
[2025-08-26 22:52:22] [Iter 1004/1050] R2[553/600], Temp: 0.0151, Energy: -30.415276+0.000903j
[2025-08-26 22:52:24] [Iter 1005/1050] R2[554/600], Temp: 0.0144, Energy: -30.420528+0.001744j
[2025-08-26 22:52:27] [Iter 1006/1050] R2[555/600], Temp: 0.0138, Energy: -30.413091-0.000311j
[2025-08-26 22:52:30] [Iter 1007/1050] R2[556/600], Temp: 0.0132, Energy: -30.414052-0.001664j
[2025-08-26 22:52:32] [Iter 1008/1050] R2[557/600], Temp: 0.0126, Energy: -30.423418+0.000297j
[2025-08-26 22:52:35] [Iter 1009/1050] R2[558/600], Temp: 0.0120, Energy: -30.413306+0.000709j
[2025-08-26 22:52:38] [Iter 1010/1050] R2[559/600], Temp: 0.0115, Energy: -30.410928-0.000869j
[2025-08-26 22:52:40] [Iter 1011/1050] R2[560/600], Temp: 0.0109, Energy: -30.405842+0.003514j
[2025-08-26 22:52:43] [Iter 1012/1050] R2[561/600], Temp: 0.0104, Energy: -30.408284+0.002733j
[2025-08-26 22:52:46] [Iter 1013/1050] R2[562/600], Temp: 0.0099, Energy: -30.422629-0.004290j
[2025-08-26 22:52:48] [Iter 1014/1050] R2[563/600], Temp: 0.0094, Energy: -30.414185+0.002768j
[2025-08-26 22:52:51] [Iter 1015/1050] R2[564/600], Temp: 0.0089, Energy: -30.411999+0.005113j
[2025-08-26 22:52:54] [Iter 1016/1050] R2[565/600], Temp: 0.0084, Energy: -30.418714+0.003207j
[2025-08-26 22:52:57] [Iter 1017/1050] R2[566/600], Temp: 0.0079, Energy: -30.418762-0.000996j
[2025-08-26 22:52:59] [Iter 1018/1050] R2[567/600], Temp: 0.0074, Energy: -30.420034+0.000758j
[2025-08-26 22:53:02] [Iter 1019/1050] R2[568/600], Temp: 0.0070, Energy: -30.410863-0.000063j
[2025-08-26 22:53:05] [Iter 1020/1050] R2[569/600], Temp: 0.0066, Energy: -30.412906+0.002534j
[2025-08-26 22:53:08] [Iter 1021/1050] R2[570/600], Temp: 0.0062, Energy: -30.415012-0.000577j
[2025-08-26 22:53:11] [Iter 1022/1050] R2[571/600], Temp: 0.0058, Energy: -30.419469-0.000361j
[2025-08-26 22:53:13] [Iter 1023/1050] R2[572/600], Temp: 0.0054, Energy: -30.407063+0.001183j
[2025-08-26 22:53:16] [Iter 1024/1050] R2[573/600], Temp: 0.0050, Energy: -30.419593-0.000228j
[2025-08-26 22:53:18] [Iter 1025/1050] R2[574/600], Temp: 0.0046, Energy: -30.415598-0.001523j
[2025-08-26 22:53:21] [Iter 1026/1050] R2[575/600], Temp: 0.0043, Energy: -30.413460+0.000344j
[2025-08-26 22:53:24] [Iter 1027/1050] R2[576/600], Temp: 0.0039, Energy: -30.417737+0.002694j
[2025-08-26 22:53:26] [Iter 1028/1050] R2[577/600], Temp: 0.0036, Energy: -30.424967+0.000417j
[2025-08-26 22:53:29] [Iter 1029/1050] R2[578/600], Temp: 0.0033, Energy: -30.412350+0.001405j
[2025-08-26 22:53:31] [Iter 1030/1050] R2[579/600], Temp: 0.0030, Energy: -30.416644-0.000440j
[2025-08-26 22:53:34] [Iter 1031/1050] R2[580/600], Temp: 0.0027, Energy: -30.408315-0.000803j
[2025-08-26 22:53:37] [Iter 1032/1050] R2[581/600], Temp: 0.0025, Energy: -30.417783+0.000812j
[2025-08-26 22:53:39] [Iter 1033/1050] R2[582/600], Temp: 0.0022, Energy: -30.409526+0.001292j
[2025-08-26 22:53:42] [Iter 1034/1050] R2[583/600], Temp: 0.0020, Energy: -30.402695+0.001081j
[2025-08-26 22:53:45] [Iter 1035/1050] R2[584/600], Temp: 0.0018, Energy: -30.416349-0.001439j
[2025-08-26 22:53:47] [Iter 1036/1050] R2[585/600], Temp: 0.0015, Energy: -30.420858-0.001824j
[2025-08-26 22:53:50] [Iter 1037/1050] R2[586/600], Temp: 0.0013, Energy: -30.417021+0.001413j
[2025-08-26 22:53:52] [Iter 1038/1050] R2[587/600], Temp: 0.0012, Energy: -30.411966+0.000354j
[2025-08-26 22:53:55] [Iter 1039/1050] R2[588/600], Temp: 0.0010, Energy: -30.404831-0.000324j
[2025-08-26 22:53:58] [Iter 1040/1050] R2[589/600], Temp: 0.0008, Energy: -30.423760-0.001823j
[2025-08-26 22:54:00] [Iter 1041/1050] R2[590/600], Temp: 0.0007, Energy: -30.416734-0.001492j
[2025-08-26 22:54:03] [Iter 1042/1050] R2[591/600], Temp: 0.0006, Energy: -30.408841-0.002074j
[2025-08-26 22:54:05] [Iter 1043/1050] R2[592/600], Temp: 0.0004, Energy: -30.413474+0.002395j
[2025-08-26 22:54:08] [Iter 1044/1050] R2[593/600], Temp: 0.0003, Energy: -30.416512+0.002502j
[2025-08-26 22:54:11] [Iter 1045/1050] R2[594/600], Temp: 0.0002, Energy: -30.420894-0.001794j
[2025-08-26 22:54:13] [Iter 1046/1050] R2[595/600], Temp: 0.0002, Energy: -30.416083-0.001893j
[2025-08-26 22:54:16] [Iter 1047/1050] R2[596/600], Temp: 0.0001, Energy: -30.407707-0.001491j
[2025-08-26 22:54:18] [Iter 1048/1050] R2[597/600], Temp: 0.0001, Energy: -30.418535+0.003480j
[2025-08-26 22:54:21] [Iter 1049/1050] R2[598/600], Temp: 0.0000, Energy: -30.411363-0.000716j
[2025-08-26 22:54:24] [Iter 1050/1050] R2[599/600], Temp: 0.0000, Energy: -30.408297-0.002500j
[2025-08-26 22:54:24] ✅ Training completed | Restarts: 2
[2025-08-26 22:54:24] ============================================================
[2025-08-26 22:54:24] Training completed | Runtime: 2797.6s
[2025-08-26 22:54:25] ✓ Final state saved: checkpoints/final_GCNN.pkl
[2025-08-26 22:54:25] ============================================================
