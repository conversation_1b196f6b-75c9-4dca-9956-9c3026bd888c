[2025-08-25 20:07:33] ✓ 从checkpoint恢复: results/L=4/J2=1.00/J1=0.79/training/checkpoints/final_GCNN.pkl
[2025-08-25 20:07:33]   - 迭代次数: final
[2025-08-25 20:07:33]   - 能量: -28.355260+0.002725j ± 0.006623
[2025-08-25 20:07:33]   - 时间戳: 2025-08-25T20:07:22.459626+08:00
[2025-08-25 20:07:46] ✓ 变分状态参数已从checkpoint恢复
[2025-08-25 20:07:46] ✓ 从final状态恢复, 重置迭代计数为0
[2025-08-25 20:07:46] ==================================================
[2025-08-25 20:07:46] GCNN for Shastry-Sutherland Model
[2025-08-25 20:07:46] ==================================================
[2025-08-25 20:07:46] System parameters:
[2025-08-25 20:07:46]   - System size: L=4, N=64
[2025-08-25 20:07:46]   - System parameters: J1=0.78, J2=1.0, Q=0.0
[2025-08-25 20:07:46] --------------------------------------------------
[2025-08-25 20:07:46] Model parameters:
[2025-08-25 20:07:46]   - Number of layers = 4
[2025-08-25 20:07:46]   - Number of features = 4
[2025-08-25 20:07:46]   - Total parameters = 12572
[2025-08-25 20:07:46] --------------------------------------------------
[2025-08-25 20:07:46] Training parameters:
[2025-08-25 20:07:46]   - Learning rate: 0.01
[2025-08-25 20:07:46]   - Total iterations: 1050
[2025-08-25 20:07:46]   - Annealing cycles: 3
[2025-08-25 20:07:46]   - Initial period: 150
[2025-08-25 20:07:46]   - Period multiplier: 2.0
[2025-08-25 20:07:46]   - Temperature range: 0.0-1.0
[2025-08-25 20:07:46]   - Samples: 4096
[2025-08-25 20:07:46]   - Discarded samples: 0
[2025-08-25 20:07:46]   - Chunk size: 2048
[2025-08-25 20:07:46]   - Diagonal shift: 0.2
[2025-08-25 20:07:46]   - Gradient clipping: 1.0
[2025-08-25 20:07:46]   - Checkpoint enabled: interval=100
[2025-08-25 20:07:46]   - Checkpoint directory: results/L=4/J2=1.00/J1=0.78/training/checkpoints
[2025-08-25 20:07:46] --------------------------------------------------
[2025-08-25 20:07:46] Device status:
[2025-08-25 20:07:46]   - Devices model: NVIDIA H200 NVL
[2025-08-25 20:07:46]   - Number of devices: 1
[2025-08-25 20:07:46]   - Sharding: True
[2025-08-25 20:07:46] ============================================================
[2025-08-25 20:08:26] [Iter 1/1050] R0[0/150], Temp: 1.0000, Energy: -27.941482+0.003404j
[2025-08-25 20:08:50] [Iter 2/1050] R0[1/150], Temp: 0.9999, Energy: -27.951049-0.002411j
[2025-08-25 20:08:56] [Iter 3/1050] R0[2/150], Temp: 0.9996, Energy: -27.935345-0.002916j
[2025-08-25 20:09:01] [Iter 4/1050] R0[3/150], Temp: 0.9990, Energy: -27.942595+0.003439j
[2025-08-25 20:09:07] [Iter 5/1050] R0[4/150], Temp: 0.9982, Energy: -27.952017+0.000441j
[2025-08-25 20:09:13] [Iter 6/1050] R0[5/150], Temp: 0.9973, Energy: -27.932628+0.000665j
[2025-08-25 20:09:19] [Iter 7/1050] R0[6/150], Temp: 0.9961, Energy: -27.947774-0.000469j
[2025-08-25 20:09:25] [Iter 8/1050] R0[7/150], Temp: 0.9946, Energy: -27.948832-0.003241j
[2025-08-25 20:09:30] [Iter 9/1050] R0[8/150], Temp: 0.9930, Energy: -27.933191-0.000286j
[2025-08-25 20:09:36] [Iter 10/1050] R0[9/150], Temp: 0.9911, Energy: -27.934264-0.001750j
[2025-08-25 20:09:42] [Iter 11/1050] R0[10/150], Temp: 0.9891, Energy: -27.942614+0.000631j
[2025-08-25 20:09:48] [Iter 12/1050] R0[11/150], Temp: 0.9868, Energy: -27.940870+0.002226j
[2025-08-25 20:09:54] [Iter 13/1050] R0[12/150], Temp: 0.9843, Energy: -27.939512+0.000361j
[2025-08-25 20:09:59] [Iter 14/1050] R0[13/150], Temp: 0.9816, Energy: -27.939394+0.003238j
[2025-08-25 20:10:05] [Iter 15/1050] R0[14/150], Temp: 0.9787, Energy: -27.931111+0.003225j
[2025-08-25 20:10:11] [Iter 16/1050] R0[15/150], Temp: 0.9755, Energy: -27.950344-0.000257j
[2025-08-25 20:10:17] [Iter 17/1050] R0[16/150], Temp: 0.9722, Energy: -27.932040-0.001263j
[2025-08-25 20:10:22] [Iter 18/1050] R0[17/150], Temp: 0.9686, Energy: -27.942604+0.000293j
[2025-08-25 20:10:28] [Iter 19/1050] R0[18/150], Temp: 0.9649, Energy: -27.938024-0.003205j
[2025-08-25 20:10:34] [Iter 20/1050] R0[19/150], Temp: 0.9609, Energy: -27.941610+0.001066j
[2025-08-25 20:10:40] [Iter 21/1050] R0[20/150], Temp: 0.9568, Energy: -27.944791-0.001369j
[2025-08-25 20:10:46] [Iter 22/1050] R0[21/150], Temp: 0.9524, Energy: -27.941917-0.001953j
[2025-08-25 20:10:51] [Iter 23/1050] R0[22/150], Temp: 0.9479, Energy: -27.940859-0.000027j
[2025-08-25 20:10:57] [Iter 24/1050] R0[23/150], Temp: 0.9431, Energy: -27.941789-0.000020j
[2025-08-25 20:11:03] [Iter 25/1050] R0[24/150], Temp: 0.9382, Energy: -27.945299-0.001161j
[2025-08-25 20:11:09] [Iter 26/1050] R0[25/150], Temp: 0.9330, Energy: -27.942275+0.002768j
[2025-08-25 20:11:15] [Iter 27/1050] R0[26/150], Temp: 0.9277, Energy: -27.947022+0.008087j
[2025-08-25 20:11:20] [Iter 28/1050] R0[27/150], Temp: 0.9222, Energy: -27.938546-0.004464j
[2025-08-25 20:11:26] [Iter 29/1050] R0[28/150], Temp: 0.9165, Energy: -27.944629+0.002280j
[2025-08-25 20:11:32] [Iter 30/1050] R0[29/150], Temp: 0.9106, Energy: -27.948377+0.002284j
[2025-08-25 20:11:38] [Iter 31/1050] R0[30/150], Temp: 0.9045, Energy: -27.930190+0.005502j
[2025-08-25 20:11:43] [Iter 32/1050] R0[31/150], Temp: 0.8983, Energy: -27.940485-0.004773j
[2025-08-25 20:11:49] [Iter 33/1050] R0[32/150], Temp: 0.8918, Energy: -27.945338-0.001786j
[2025-08-25 20:11:55] [Iter 34/1050] R0[33/150], Temp: 0.8853, Energy: -27.944687+0.001957j
[2025-08-25 20:12:01] [Iter 35/1050] R0[34/150], Temp: 0.8785, Energy: -27.942564+0.000747j
[2025-08-25 20:12:07] [Iter 36/1050] R0[35/150], Temp: 0.8716, Energy: -27.944349-0.000196j
[2025-08-25 20:12:12] [Iter 37/1050] R0[36/150], Temp: 0.8645, Energy: -27.934565+0.002065j
[2025-08-25 20:12:18] [Iter 38/1050] R0[37/150], Temp: 0.8572, Energy: -27.932904+0.000159j
[2025-08-25 20:12:24] [Iter 39/1050] R0[38/150], Temp: 0.8498, Energy: -27.935640-0.001327j
[2025-08-25 20:12:30] [Iter 40/1050] R0[39/150], Temp: 0.8423, Energy: -27.941978-0.002173j
[2025-08-25 20:12:35] [Iter 41/1050] R0[40/150], Temp: 0.8346, Energy: -27.945818+0.000773j
[2025-08-25 20:12:41] [Iter 42/1050] R0[41/150], Temp: 0.8267, Energy: -27.946409-0.002696j
[2025-08-25 20:12:47] [Iter 43/1050] R0[42/150], Temp: 0.8187, Energy: -27.934998-0.001707j
[2025-08-25 20:12:53] [Iter 44/1050] R0[43/150], Temp: 0.8106, Energy: -27.936493+0.000478j
[2025-08-25 20:12:59] [Iter 45/1050] R0[44/150], Temp: 0.8023, Energy: -27.937931-0.001076j
[2025-08-25 20:13:04] [Iter 46/1050] R0[45/150], Temp: 0.7939, Energy: -27.938946-0.001396j
[2025-08-25 20:13:10] [Iter 47/1050] R0[46/150], Temp: 0.7854, Energy: -27.941547-0.002145j
[2025-08-25 20:13:16] [Iter 48/1050] R0[47/150], Temp: 0.7767, Energy: -27.933206+0.002839j
[2025-08-25 20:13:22] [Iter 49/1050] R0[48/150], Temp: 0.7679, Energy: -27.947565-0.002423j
[2025-08-25 20:13:27] [Iter 50/1050] R0[49/150], Temp: 0.7590, Energy: -27.940488-0.002979j
[2025-08-25 20:13:33] [Iter 51/1050] R0[50/150], Temp: 0.7500, Energy: -27.938641+0.000276j
[2025-08-25 20:13:39] [Iter 52/1050] R0[51/150], Temp: 0.7409, Energy: -27.936544+0.002015j
[2025-08-25 20:13:45] [Iter 53/1050] R0[52/150], Temp: 0.7316, Energy: -27.931016-0.000086j
[2025-08-25 20:13:51] [Iter 54/1050] R0[53/150], Temp: 0.7223, Energy: -27.938592-0.001748j
[2025-08-25 20:13:56] [Iter 55/1050] R0[54/150], Temp: 0.7129, Energy: -27.946031+0.000086j
[2025-08-25 20:14:02] [Iter 56/1050] R0[55/150], Temp: 0.7034, Energy: -27.936041-0.000815j
[2025-08-25 20:14:08] [Iter 57/1050] R0[56/150], Temp: 0.6938, Energy: -27.940520+0.001486j
[2025-08-25 20:14:14] [Iter 58/1050] R0[57/150], Temp: 0.6841, Energy: -27.950235+0.001551j
[2025-08-25 20:14:20] [Iter 59/1050] R0[58/150], Temp: 0.6743, Energy: -27.948813-0.000572j
[2025-08-25 20:14:25] [Iter 60/1050] R0[59/150], Temp: 0.6644, Energy: -27.939716+0.004269j
[2025-08-25 20:14:31] [Iter 61/1050] R0[60/150], Temp: 0.6545, Energy: -27.944642-0.000247j
[2025-08-25 20:14:37] [Iter 62/1050] R0[61/150], Temp: 0.6445, Energy: -27.929941-0.001681j
[2025-08-25 20:14:43] [Iter 63/1050] R0[62/150], Temp: 0.6345, Energy: -27.935548+0.003407j
[2025-08-25 20:14:49] [Iter 64/1050] R0[63/150], Temp: 0.6243, Energy: -27.944196-0.001524j
[2025-08-25 20:14:54] [Iter 65/1050] R0[64/150], Temp: 0.6142, Energy: -27.933418+0.000106j
[2025-08-25 20:15:00] [Iter 66/1050] R0[65/150], Temp: 0.6040, Energy: -27.950362-0.000795j
[2025-08-25 20:15:06] [Iter 67/1050] R0[66/150], Temp: 0.5937, Energy: -27.947536-0.001759j
[2025-08-25 20:15:12] [Iter 68/1050] R0[67/150], Temp: 0.5834, Energy: -27.939323-0.001287j
[2025-08-25 20:15:18] [Iter 69/1050] R0[68/150], Temp: 0.5730, Energy: -27.938015-0.000899j
[2025-08-25 20:15:23] [Iter 70/1050] R0[69/150], Temp: 0.5627, Energy: -27.927107+0.000632j
[2025-08-25 20:15:29] [Iter 71/1050] R0[70/150], Temp: 0.5523, Energy: -27.928214+0.003982j
[2025-08-25 20:15:35] [Iter 72/1050] R0[71/150], Temp: 0.5418, Energy: -27.931130+0.000834j
[2025-08-25 20:15:41] [Iter 73/1050] R0[72/150], Temp: 0.5314, Energy: -27.944566+0.003496j
[2025-08-25 20:15:47] [Iter 74/1050] R0[73/150], Temp: 0.5209, Energy: -27.939401+0.001501j
[2025-08-25 20:15:52] [Iter 75/1050] R0[74/150], Temp: 0.5105, Energy: -27.945976-0.004202j
[2025-08-25 20:15:58] [Iter 76/1050] R0[75/150], Temp: 0.5000, Energy: -27.948643-0.001871j
[2025-08-25 20:16:04] [Iter 77/1050] R0[76/150], Temp: 0.4895, Energy: -27.941980-0.003668j
[2025-08-25 20:16:10] [Iter 78/1050] R0[77/150], Temp: 0.4791, Energy: -27.936368-0.000859j
[2025-08-25 20:16:15] [Iter 79/1050] R0[78/150], Temp: 0.4686, Energy: -27.946050+0.000289j
[2025-08-25 20:16:21] [Iter 80/1050] R0[79/150], Temp: 0.4582, Energy: -27.936365+0.000647j
[2025-08-25 20:16:27] [Iter 81/1050] R0[80/150], Temp: 0.4477, Energy: -27.944164+0.002763j
[2025-08-25 20:16:33] [Iter 82/1050] R0[81/150], Temp: 0.4373, Energy: -27.939695-0.001848j
[2025-08-25 20:16:39] [Iter 83/1050] R0[82/150], Temp: 0.4270, Energy: -27.931237-0.003387j
[2025-08-25 20:16:44] [Iter 84/1050] R0[83/150], Temp: 0.4166, Energy: -27.940914-0.000864j
[2025-08-25 20:16:50] [Iter 85/1050] R0[84/150], Temp: 0.4063, Energy: -27.944582+0.001008j
[2025-08-25 20:16:56] [Iter 86/1050] R0[85/150], Temp: 0.3960, Energy: -27.939153-0.001301j
[2025-08-25 20:17:02] [Iter 87/1050] R0[86/150], Temp: 0.3858, Energy: -27.943532-0.000188j
[2025-08-25 20:17:08] [Iter 88/1050] R0[87/150], Temp: 0.3757, Energy: -27.940776+0.002703j
[2025-08-25 20:17:13] [Iter 89/1050] R0[88/150], Temp: 0.3655, Energy: -27.933317+0.002785j
[2025-08-25 20:17:19] [Iter 90/1050] R0[89/150], Temp: 0.3555, Energy: -27.944382+0.000446j
[2025-08-25 20:17:25] [Iter 91/1050] R0[90/150], Temp: 0.3455, Energy: -27.940994-0.002020j
[2025-08-25 20:17:31] [Iter 92/1050] R0[91/150], Temp: 0.3356, Energy: -27.948113-0.000826j
[2025-08-25 20:17:37] [Iter 93/1050] R0[92/150], Temp: 0.3257, Energy: -27.938975+0.003802j
[2025-08-25 20:17:42] [Iter 94/1050] R0[93/150], Temp: 0.3159, Energy: -27.946635+0.002492j
[2025-08-25 20:17:48] [Iter 95/1050] R0[94/150], Temp: 0.3062, Energy: -27.945035+0.001930j
[2025-08-25 20:17:54] [Iter 96/1050] R0[95/150], Temp: 0.2966, Energy: -27.941284-0.000471j
[2025-08-25 20:18:00] [Iter 97/1050] R0[96/150], Temp: 0.2871, Energy: -27.936034-0.003517j
[2025-08-25 20:18:05] [Iter 98/1050] R0[97/150], Temp: 0.2777, Energy: -27.949166+0.006377j
[2025-08-25 20:18:11] [Iter 99/1050] R0[98/150], Temp: 0.2684, Energy: -27.932203-0.001488j
[2025-08-25 20:18:17] [Iter 100/1050] R0[99/150], Temp: 0.2591, Energy: -27.933556-0.001002j
[2025-08-25 20:18:17] ✓ Checkpoint saved: checkpoint_iter_000100.pkl
[2025-08-25 20:18:23] [Iter 101/1050] R0[100/150], Temp: 0.2500, Energy: -27.947430+0.001294j
[2025-08-25 20:18:29] [Iter 102/1050] R0[101/150], Temp: 0.2410, Energy: -27.947827+0.000736j
[2025-08-25 20:18:34] [Iter 103/1050] R0[102/150], Temp: 0.2321, Energy: -27.950445+0.000701j
[2025-08-25 20:18:40] [Iter 104/1050] R0[103/150], Temp: 0.2233, Energy: -27.943567+0.002195j
[2025-08-25 20:18:46] [Iter 105/1050] R0[104/150], Temp: 0.2146, Energy: -27.944251-0.002945j
[2025-08-25 20:18:52] [Iter 106/1050] R0[105/150], Temp: 0.2061, Energy: -27.937779-0.003488j
[2025-08-25 20:18:58] [Iter 107/1050] R0[106/150], Temp: 0.1977, Energy: -27.948342+0.000805j
[2025-08-25 20:19:03] [Iter 108/1050] R0[107/150], Temp: 0.1894, Energy: -27.932153+0.001614j
[2025-08-25 20:19:09] [Iter 109/1050] R0[108/150], Temp: 0.1813, Energy: -27.934241-0.000636j
[2025-08-25 20:19:15] [Iter 110/1050] R0[109/150], Temp: 0.1733, Energy: -27.944157+0.001321j
[2025-08-25 20:19:21] [Iter 111/1050] R0[110/150], Temp: 0.1654, Energy: -27.936524-0.001908j
[2025-08-25 20:19:27] [Iter 112/1050] R0[111/150], Temp: 0.1577, Energy: -27.925903+0.004828j
[2025-08-25 20:19:32] [Iter 113/1050] R0[112/150], Temp: 0.1502, Energy: -27.936670+0.000617j
[2025-08-25 20:19:38] [Iter 114/1050] R0[113/150], Temp: 0.1428, Energy: -27.930187-0.002005j
[2025-08-25 20:19:44] [Iter 115/1050] R0[114/150], Temp: 0.1355, Energy: -27.944468+0.000438j
[2025-08-25 20:19:50] [Iter 116/1050] R0[115/150], Temp: 0.1284, Energy: -27.945920+0.001334j
[2025-08-25 20:19:56] [Iter 117/1050] R0[116/150], Temp: 0.1215, Energy: -27.929905-0.001492j
[2025-08-25 20:20:01] [Iter 118/1050] R0[117/150], Temp: 0.1147, Energy: -27.947327-0.000947j
[2025-08-25 20:20:07] [Iter 119/1050] R0[118/150], Temp: 0.1082, Energy: -27.941185+0.000976j
[2025-08-25 20:20:13] [Iter 120/1050] R0[119/150], Temp: 0.1017, Energy: -27.936118-0.002813j
[2025-08-25 20:20:19] [Iter 121/1050] R0[120/150], Temp: 0.0955, Energy: -27.929383-0.000017j
[2025-08-25 20:20:24] [Iter 122/1050] R0[121/150], Temp: 0.0894, Energy: -27.932705-0.000527j
[2025-08-25 20:20:30] [Iter 123/1050] R0[122/150], Temp: 0.0835, Energy: -27.945915+0.002318j
[2025-08-25 20:20:36] [Iter 124/1050] R0[123/150], Temp: 0.0778, Energy: -27.938801+0.000981j
[2025-08-25 20:20:42] [Iter 125/1050] R0[124/150], Temp: 0.0723, Energy: -27.940903-0.001244j
[2025-08-25 20:20:48] [Iter 126/1050] R0[125/150], Temp: 0.0670, Energy: -27.942822-0.000031j
[2025-08-25 20:20:53] [Iter 127/1050] R0[126/150], Temp: 0.0618, Energy: -27.952948-0.001281j
[2025-08-25 20:20:59] [Iter 128/1050] R0[127/150], Temp: 0.0569, Energy: -27.936770-0.002070j
[2025-08-25 20:21:05] [Iter 129/1050] R0[128/150], Temp: 0.0521, Energy: -27.943787-0.002472j
[2025-08-25 20:21:11] [Iter 130/1050] R0[129/150], Temp: 0.0476, Energy: -27.931870+0.004612j
[2025-08-25 20:21:17] [Iter 131/1050] R0[130/150], Temp: 0.0432, Energy: -27.947866-0.000653j
[2025-08-25 20:21:22] [Iter 132/1050] R0[131/150], Temp: 0.0391, Energy: -27.937689+0.003992j
[2025-08-25 20:21:28] [Iter 133/1050] R0[132/150], Temp: 0.0351, Energy: -27.932864+0.001599j
[2025-08-25 20:21:34] [Iter 134/1050] R0[133/150], Temp: 0.0314, Energy: -27.937725-0.002209j
[2025-08-25 20:21:40] [Iter 135/1050] R0[134/150], Temp: 0.0278, Energy: -27.950068-0.002420j
[2025-08-25 20:21:46] [Iter 136/1050] R0[135/150], Temp: 0.0245, Energy: -27.941231-0.005735j
[2025-08-25 20:21:51] [Iter 137/1050] R0[136/150], Temp: 0.0213, Energy: -27.945048+0.001303j
[2025-08-25 20:21:57] [Iter 138/1050] R0[137/150], Temp: 0.0184, Energy: -27.931926+0.000307j
[2025-08-25 20:22:03] [Iter 139/1050] R0[138/150], Temp: 0.0157, Energy: -27.944162-0.000522j
[2025-08-25 20:22:09] [Iter 140/1050] R0[139/150], Temp: 0.0132, Energy: -27.933980-0.000095j
[2025-08-25 20:22:15] [Iter 141/1050] R0[140/150], Temp: 0.0109, Energy: -27.943580-0.002193j
[2025-08-25 20:22:20] [Iter 142/1050] R0[141/150], Temp: 0.0089, Energy: -27.940431-0.001531j
[2025-08-25 20:22:26] [Iter 143/1050] R0[142/150], Temp: 0.0070, Energy: -27.949275+0.000002j
[2025-08-25 20:22:32] [Iter 144/1050] R0[143/150], Temp: 0.0054, Energy: -27.938530-0.005505j
[2025-08-25 20:22:38] [Iter 145/1050] R0[144/150], Temp: 0.0039, Energy: -27.942501-0.000734j
[2025-08-25 20:22:43] [Iter 146/1050] R0[145/150], Temp: 0.0027, Energy: -27.942893+0.009811j
[2025-08-25 20:22:49] [Iter 147/1050] R0[146/150], Temp: 0.0018, Energy: -27.931304+0.000186j
[2025-08-25 20:22:55] [Iter 148/1050] R0[147/150], Temp: 0.0010, Energy: -27.944712+0.000706j
[2025-08-25 20:23:01] [Iter 149/1050] R0[148/150], Temp: 0.0004, Energy: -27.934968+0.004145j
[2025-08-25 20:23:07] [Iter 150/1050] R0[149/150], Temp: 0.0001, Energy: -27.938955-0.000456j
[2025-08-25 20:23:07] RESTART #1 | Period: 300
[2025-08-25 20:23:12] [Iter 151/1050] R1[0/300], Temp: 1.0000, Energy: -27.941744+0.001889j
[2025-08-25 20:23:18] [Iter 152/1050] R1[1/300], Temp: 1.0000, Energy: -27.944560-0.008718j
[2025-08-25 20:23:24] [Iter 153/1050] R1[2/300], Temp: 0.9999, Energy: -27.937808+0.002562j
[2025-08-25 20:23:30] [Iter 154/1050] R1[3/300], Temp: 0.9998, Energy: -27.946679-0.000288j
[2025-08-25 20:23:36] [Iter 155/1050] R1[4/300], Temp: 0.9996, Energy: -27.941007+0.001303j
[2025-08-25 20:23:41] [Iter 156/1050] R1[5/300], Temp: 0.9993, Energy: -27.934873-0.000464j
[2025-08-25 20:23:47] [Iter 157/1050] R1[6/300], Temp: 0.9990, Energy: -27.935579+0.000274j
[2025-08-25 20:23:53] [Iter 158/1050] R1[7/300], Temp: 0.9987, Energy: -27.946607-0.001754j
[2025-08-25 20:23:59] [Iter 159/1050] R1[8/300], Temp: 0.9982, Energy: -27.936287-0.003715j
[2025-08-25 20:24:05] [Iter 160/1050] R1[9/300], Temp: 0.9978, Energy: -27.928144-0.003144j
[2025-08-25 20:24:10] [Iter 161/1050] R1[10/300], Temp: 0.9973, Energy: -27.941278-0.000071j
[2025-08-25 20:24:16] [Iter 162/1050] R1[11/300], Temp: 0.9967, Energy: -27.942713+0.000617j
[2025-08-25 20:24:22] [Iter 163/1050] R1[12/300], Temp: 0.9961, Energy: -27.937876-0.004157j
[2025-08-25 20:24:28] [Iter 164/1050] R1[13/300], Temp: 0.9954, Energy: -27.945431+0.001284j
[2025-08-25 20:24:33] [Iter 165/1050] R1[14/300], Temp: 0.9946, Energy: -27.949022-0.001450j
[2025-08-25 20:24:39] [Iter 166/1050] R1[15/300], Temp: 0.9938, Energy: -27.937508+0.000041j
[2025-08-25 20:24:45] [Iter 167/1050] R1[16/300], Temp: 0.9930, Energy: -27.950117+0.004159j
[2025-08-25 20:24:51] [Iter 168/1050] R1[17/300], Temp: 0.9921, Energy: -27.942074-0.000739j
[2025-08-25 20:24:57] [Iter 169/1050] R1[18/300], Temp: 0.9911, Energy: -27.936278-0.001661j
[2025-08-25 20:25:02] [Iter 170/1050] R1[19/300], Temp: 0.9901, Energy: -27.937170+0.000734j
[2025-08-25 20:25:08] [Iter 171/1050] R1[20/300], Temp: 0.9891, Energy: -27.948638+0.001123j
[2025-08-25 20:25:14] [Iter 172/1050] R1[21/300], Temp: 0.9880, Energy: -27.934200-0.002964j
[2025-08-25 20:25:20] [Iter 173/1050] R1[22/300], Temp: 0.9868, Energy: -27.935749-0.001606j
[2025-08-25 20:25:26] [Iter 174/1050] R1[23/300], Temp: 0.9856, Energy: -27.942944+0.000718j
[2025-08-25 20:25:31] [Iter 175/1050] R1[24/300], Temp: 0.9843, Energy: -27.930655-0.001276j
[2025-08-25 20:25:37] [Iter 176/1050] R1[25/300], Temp: 0.9830, Energy: -27.947027-0.003118j
[2025-08-25 20:25:43] [Iter 177/1050] R1[26/300], Temp: 0.9816, Energy: -27.944334+0.005018j
[2025-08-25 20:25:49] [Iter 178/1050] R1[27/300], Temp: 0.9801, Energy: -27.938834+0.002387j
[2025-08-25 20:25:55] [Iter 179/1050] R1[28/300], Temp: 0.9787, Energy: -27.935115+0.001566j
[2025-08-25 20:26:00] [Iter 180/1050] R1[29/300], Temp: 0.9771, Energy: -27.945906+0.000728j
[2025-08-25 20:26:06] [Iter 181/1050] R1[30/300], Temp: 0.9755, Energy: -27.944738+0.001297j
[2025-08-25 20:26:12] [Iter 182/1050] R1[31/300], Temp: 0.9739, Energy: -27.938281-0.001270j
[2025-08-25 20:26:18] [Iter 183/1050] R1[32/300], Temp: 0.9722, Energy: -27.944297+0.001477j
[2025-08-25 20:26:24] [Iter 184/1050] R1[33/300], Temp: 0.9704, Energy: -27.948842-0.004812j
[2025-08-25 20:26:29] [Iter 185/1050] R1[34/300], Temp: 0.9686, Energy: -27.949997-0.000977j
[2025-08-25 20:26:35] [Iter 186/1050] R1[35/300], Temp: 0.9668, Energy: -27.948840+0.001100j
[2025-08-25 20:26:41] [Iter 187/1050] R1[36/300], Temp: 0.9649, Energy: -27.937491-0.001687j
[2025-08-25 20:26:47] [Iter 188/1050] R1[37/300], Temp: 0.9629, Energy: -27.936763+0.003066j
[2025-08-25 20:26:53] [Iter 189/1050] R1[38/300], Temp: 0.9609, Energy: -27.941465+0.000955j
[2025-08-25 20:26:58] [Iter 190/1050] R1[39/300], Temp: 0.9589, Energy: -27.942750+0.001895j
[2025-08-25 20:27:04] [Iter 191/1050] R1[40/300], Temp: 0.9568, Energy: -27.943959-0.000216j
[2025-08-25 20:27:10] [Iter 192/1050] R1[41/300], Temp: 0.9546, Energy: -27.939438+0.001158j
[2025-08-25 20:27:16] [Iter 193/1050] R1[42/300], Temp: 0.9524, Energy: -27.934385-0.002874j
[2025-08-25 20:27:21] [Iter 194/1050] R1[43/300], Temp: 0.9502, Energy: -27.937718+0.000669j
[2025-08-25 20:27:27] [Iter 195/1050] R1[44/300], Temp: 0.9479, Energy: -27.937691-0.000348j
[2025-08-25 20:27:33] [Iter 196/1050] R1[45/300], Temp: 0.9455, Energy: -27.939097-0.003239j
[2025-08-25 20:27:39] [Iter 197/1050] R1[46/300], Temp: 0.9431, Energy: -27.941263+0.001182j
[2025-08-25 20:27:45] [Iter 198/1050] R1[47/300], Temp: 0.9407, Energy: -27.953184+0.003129j
[2025-08-25 20:27:50] [Iter 199/1050] R1[48/300], Temp: 0.9382, Energy: -27.946447-0.002988j
[2025-08-25 20:27:56] [Iter 200/1050] R1[49/300], Temp: 0.9356, Energy: -27.936991+0.001302j
[2025-08-25 20:27:56] ✓ Checkpoint saved: checkpoint_iter_000200.pkl
[2025-08-25 20:28:02] [Iter 201/1050] R1[50/300], Temp: 0.9330, Energy: -27.948964+0.002985j
[2025-08-25 20:28:08] [Iter 202/1050] R1[51/300], Temp: 0.9304, Energy: -27.950701+0.000732j
[2025-08-25 20:28:14] [Iter 203/1050] R1[52/300], Temp: 0.9277, Energy: -27.953415+0.000768j
[2025-08-25 20:28:19] [Iter 204/1050] R1[53/300], Temp: 0.9249, Energy: -27.942744+0.004259j
[2025-08-25 20:28:25] [Iter 205/1050] R1[54/300], Temp: 0.9222, Energy: -27.937956-0.003028j
[2025-08-25 20:28:31] [Iter 206/1050] R1[55/300], Temp: 0.9193, Energy: -27.937229+0.002352j
[2025-08-25 20:28:37] [Iter 207/1050] R1[56/300], Temp: 0.9165, Energy: -27.937688+0.000568j
[2025-08-25 20:28:43] [Iter 208/1050] R1[57/300], Temp: 0.9135, Energy: -27.927106+0.001230j
[2025-08-25 20:28:48] [Iter 209/1050] R1[58/300], Temp: 0.9106, Energy: -27.940180-0.003094j
[2025-08-25 20:28:54] [Iter 210/1050] R1[59/300], Temp: 0.9076, Energy: -27.940765-0.001697j
[2025-08-25 20:29:00] [Iter 211/1050] R1[60/300], Temp: 0.9045, Energy: -27.944114+0.000330j
[2025-08-25 20:29:06] [Iter 212/1050] R1[61/300], Temp: 0.9014, Energy: -27.942742+0.000559j
[2025-08-25 20:29:11] [Iter 213/1050] R1[62/300], Temp: 0.8983, Energy: -27.942671+0.000873j
[2025-08-25 20:29:17] [Iter 214/1050] R1[63/300], Temp: 0.8951, Energy: -27.937700+0.001567j
[2025-08-25 20:29:23] [Iter 215/1050] R1[64/300], Temp: 0.8918, Energy: -27.938376-0.000017j
[2025-08-25 20:29:29] [Iter 216/1050] R1[65/300], Temp: 0.8886, Energy: -27.933531+0.000168j
[2025-08-25 20:29:35] [Iter 217/1050] R1[66/300], Temp: 0.8853, Energy: -27.933834-0.000609j
[2025-08-25 20:29:40] [Iter 218/1050] R1[67/300], Temp: 0.8819, Energy: -27.939140+0.003471j
[2025-08-25 20:29:46] [Iter 219/1050] R1[68/300], Temp: 0.8785, Energy: -27.938351+0.000270j
[2025-08-25 20:29:52] [Iter 220/1050] R1[69/300], Temp: 0.8751, Energy: -27.943754+0.000257j
[2025-08-25 20:29:58] [Iter 221/1050] R1[70/300], Temp: 0.8716, Energy: -27.953966+0.001968j
[2025-08-25 20:30:04] [Iter 222/1050] R1[71/300], Temp: 0.8680, Energy: -27.936350+0.000856j
[2025-08-25 20:30:09] [Iter 223/1050] R1[72/300], Temp: 0.8645, Energy: -27.931787+0.001096j
[2025-08-25 20:30:15] [Iter 224/1050] R1[73/300], Temp: 0.8609, Energy: -27.940577-0.000001j
[2025-08-25 20:30:21] [Iter 225/1050] R1[74/300], Temp: 0.8572, Energy: -27.942837-0.004447j
[2025-08-25 20:30:27] [Iter 226/1050] R1[75/300], Temp: 0.8536, Energy: -27.941123-0.001303j
[2025-08-25 20:30:33] [Iter 227/1050] R1[76/300], Temp: 0.8498, Energy: -27.949174+0.003378j
[2025-08-25 20:30:38] [Iter 228/1050] R1[77/300], Temp: 0.8461, Energy: -27.941345-0.000394j
[2025-08-25 20:30:44] [Iter 229/1050] R1[78/300], Temp: 0.8423, Energy: -27.940177+0.002019j
[2025-08-25 20:30:50] [Iter 230/1050] R1[79/300], Temp: 0.8384, Energy: -27.955305+0.005614j
[2025-08-25 20:30:56] [Iter 231/1050] R1[80/300], Temp: 0.8346, Energy: -27.940805+0.003633j
[2025-08-25 20:31:01] [Iter 232/1050] R1[81/300], Temp: 0.8307, Energy: -27.946293-0.000413j
[2025-08-25 20:31:07] [Iter 233/1050] R1[82/300], Temp: 0.8267, Energy: -27.949436+0.003949j
[2025-08-25 20:31:13] [Iter 234/1050] R1[83/300], Temp: 0.8227, Energy: -27.939907-0.002381j
[2025-08-25 20:31:19] [Iter 235/1050] R1[84/300], Temp: 0.8187, Energy: -27.936017-0.001356j
[2025-08-25 20:31:25] [Iter 236/1050] R1[85/300], Temp: 0.8147, Energy: -27.941120+0.003417j
[2025-08-25 20:31:30] [Iter 237/1050] R1[86/300], Temp: 0.8106, Energy: -27.934626+0.002081j
[2025-08-25 20:31:36] [Iter 238/1050] R1[87/300], Temp: 0.8065, Energy: -27.945154-0.006600j
[2025-08-25 20:31:42] [Iter 239/1050] R1[88/300], Temp: 0.8023, Energy: -27.928211-0.000445j
[2025-08-25 20:31:48] [Iter 240/1050] R1[89/300], Temp: 0.7981, Energy: -27.937245-0.000187j
[2025-08-25 20:31:53] [Iter 241/1050] R1[90/300], Temp: 0.7939, Energy: -27.941125-0.003337j
[2025-08-25 20:31:59] [Iter 242/1050] R1[91/300], Temp: 0.7896, Energy: -27.936252+0.002020j
[2025-08-25 20:32:05] [Iter 243/1050] R1[92/300], Temp: 0.7854, Energy: -27.936510+0.002971j
[2025-08-25 20:32:11] [Iter 244/1050] R1[93/300], Temp: 0.7810, Energy: -27.942841-0.001944j
[2025-08-25 20:32:17] [Iter 245/1050] R1[94/300], Temp: 0.7767, Energy: -27.940984-0.000367j
[2025-08-25 20:32:22] [Iter 246/1050] R1[95/300], Temp: 0.7723, Energy: -27.942358-0.003102j
[2025-08-25 20:32:28] [Iter 247/1050] R1[96/300], Temp: 0.7679, Energy: -27.942384-0.000941j
[2025-08-25 20:32:34] [Iter 248/1050] R1[97/300], Temp: 0.7635, Energy: -27.933396-0.002717j
[2025-08-25 20:32:40] [Iter 249/1050] R1[98/300], Temp: 0.7590, Energy: -27.946569+0.003215j
[2025-08-25 20:32:45] [Iter 250/1050] R1[99/300], Temp: 0.7545, Energy: -27.944238+0.003920j
[2025-08-25 20:32:51] [Iter 251/1050] R1[100/300], Temp: 0.7500, Energy: -27.944720+0.003804j
[2025-08-25 20:32:57] [Iter 252/1050] R1[101/300], Temp: 0.7455, Energy: -27.934171+0.000617j
[2025-08-25 20:33:03] [Iter 253/1050] R1[102/300], Temp: 0.7409, Energy: -27.937626+0.002770j
[2025-08-25 20:33:09] [Iter 254/1050] R1[103/300], Temp: 0.7363, Energy: -27.951833+0.000586j
[2025-08-25 20:33:15] [Iter 255/1050] R1[104/300], Temp: 0.7316, Energy: -27.936114-0.001099j
[2025-08-25 20:33:20] [Iter 256/1050] R1[105/300], Temp: 0.7270, Energy: -27.943130-0.001458j
[2025-08-25 20:33:26] [Iter 257/1050] R1[106/300], Temp: 0.7223, Energy: -27.935201-0.009169j
[2025-08-25 20:33:32] [Iter 258/1050] R1[107/300], Temp: 0.7176, Energy: -27.937306-0.003520j
[2025-08-25 20:33:38] [Iter 259/1050] R1[108/300], Temp: 0.7129, Energy: -27.942494-0.004802j
[2025-08-25 20:33:44] [Iter 260/1050] R1[109/300], Temp: 0.7081, Energy: -27.936945+0.001891j
[2025-08-25 20:33:50] [Iter 261/1050] R1[110/300], Temp: 0.7034, Energy: -27.935264+0.003197j
[2025-08-25 20:33:55] [Iter 262/1050] R1[111/300], Temp: 0.6986, Energy: -27.947590+0.002759j
[2025-08-25 20:34:01] [Iter 263/1050] R1[112/300], Temp: 0.6938, Energy: -27.940138+0.000540j
[2025-08-25 20:34:07] [Iter 264/1050] R1[113/300], Temp: 0.6889, Energy: -27.939966+0.000523j
[2025-08-25 20:34:13] [Iter 265/1050] R1[114/300], Temp: 0.6841, Energy: -27.954823-0.002317j
[2025-08-25 20:34:18] [Iter 266/1050] R1[115/300], Temp: 0.6792, Energy: -27.944468-0.000067j
[2025-08-25 20:34:24] [Iter 267/1050] R1[116/300], Temp: 0.6743, Energy: -27.931362+0.001017j
[2025-08-25 20:34:30] [Iter 268/1050] R1[117/300], Temp: 0.6694, Energy: -27.941179+0.000010j
[2025-08-25 20:34:36] [Iter 269/1050] R1[118/300], Temp: 0.6644, Energy: -27.942262-0.001032j
[2025-08-25 20:34:42] [Iter 270/1050] R1[119/300], Temp: 0.6595, Energy: -27.948966+0.000890j
[2025-08-25 20:34:47] [Iter 271/1050] R1[120/300], Temp: 0.6545, Energy: -27.937547-0.002813j
[2025-08-25 20:34:53] [Iter 272/1050] R1[121/300], Temp: 0.6495, Energy: -27.938646+0.000272j
[2025-08-25 20:34:59] [Iter 273/1050] R1[122/300], Temp: 0.6445, Energy: -27.932011-0.000008j
[2025-08-25 20:35:05] [Iter 274/1050] R1[123/300], Temp: 0.6395, Energy: -27.933019+0.004779j
[2025-08-25 20:35:10] [Iter 275/1050] R1[124/300], Temp: 0.6345, Energy: -27.948575+0.002765j
[2025-08-25 20:35:16] [Iter 276/1050] R1[125/300], Temp: 0.6294, Energy: -27.945282+0.002914j
[2025-08-25 20:35:22] [Iter 277/1050] R1[126/300], Temp: 0.6243, Energy: -27.942298-0.003491j
[2025-08-25 20:35:28] [Iter 278/1050] R1[127/300], Temp: 0.6193, Energy: -27.934246-0.000772j
[2025-08-25 20:35:34] [Iter 279/1050] R1[128/300], Temp: 0.6142, Energy: -27.942170+0.001752j
[2025-08-25 20:35:39] [Iter 280/1050] R1[129/300], Temp: 0.6091, Energy: -27.943201-0.006305j
[2025-08-25 20:35:45] [Iter 281/1050] R1[130/300], Temp: 0.6040, Energy: -27.949188-0.004971j
[2025-08-25 20:35:51] [Iter 282/1050] R1[131/300], Temp: 0.5988, Energy: -27.941973+0.000027j
[2025-08-25 20:35:57] [Iter 283/1050] R1[132/300], Temp: 0.5937, Energy: -27.946489-0.001035j
[2025-08-25 20:36:03] [Iter 284/1050] R1[133/300], Temp: 0.5885, Energy: -27.936626+0.000176j
[2025-08-25 20:36:08] [Iter 285/1050] R1[134/300], Temp: 0.5834, Energy: -27.939313-0.003497j
[2025-08-25 20:36:14] [Iter 286/1050] R1[135/300], Temp: 0.5782, Energy: -27.938862+0.002206j
[2025-08-25 20:36:20] [Iter 287/1050] R1[136/300], Temp: 0.5730, Energy: -27.943431+0.002934j
[2025-08-25 20:36:26] [Iter 288/1050] R1[137/300], Temp: 0.5679, Energy: -27.935402-0.000752j
[2025-08-25 20:36:32] [Iter 289/1050] R1[138/300], Temp: 0.5627, Energy: -27.943059+0.000283j
[2025-08-25 20:36:37] [Iter 290/1050] R1[139/300], Temp: 0.5575, Energy: -27.946277+0.000941j
[2025-08-25 20:36:43] [Iter 291/1050] R1[140/300], Temp: 0.5523, Energy: -27.940417+0.001596j
[2025-08-25 20:36:49] [Iter 292/1050] R1[141/300], Temp: 0.5471, Energy: -27.938301-0.004741j
[2025-08-25 20:36:55] [Iter 293/1050] R1[142/300], Temp: 0.5418, Energy: -27.943978-0.000190j
[2025-08-25 20:37:00] [Iter 294/1050] R1[143/300], Temp: 0.5366, Energy: -27.940374-0.001503j
[2025-08-25 20:37:06] [Iter 295/1050] R1[144/300], Temp: 0.5314, Energy: -27.939581+0.001279j
[2025-08-25 20:37:12] [Iter 296/1050] R1[145/300], Temp: 0.5262, Energy: -27.934146-0.001427j
[2025-08-25 20:37:18] [Iter 297/1050] R1[146/300], Temp: 0.5209, Energy: -27.937953-0.002837j
[2025-08-25 20:37:24] [Iter 298/1050] R1[147/300], Temp: 0.5157, Energy: -27.943811+0.000828j
[2025-08-25 20:37:29] [Iter 299/1050] R1[148/300], Temp: 0.5105, Energy: -27.930596+0.000308j
[2025-08-25 20:37:35] [Iter 300/1050] R1[149/300], Temp: 0.5052, Energy: -27.951353-0.000881j
[2025-08-25 20:37:35] ✓ Checkpoint saved: checkpoint_iter_000300.pkl
[2025-08-25 20:37:41] [Iter 301/1050] R1[150/300], Temp: 0.5000, Energy: -27.937222-0.001122j
[2025-08-25 20:37:47] [Iter 302/1050] R1[151/300], Temp: 0.4948, Energy: -27.945469-0.000438j
[2025-08-25 20:37:53] [Iter 303/1050] R1[152/300], Temp: 0.4895, Energy: -27.942290+0.000682j
[2025-08-25 20:37:58] [Iter 304/1050] R1[153/300], Temp: 0.4843, Energy: -27.943386+0.003092j
[2025-08-25 20:38:04] [Iter 305/1050] R1[154/300], Temp: 0.4791, Energy: -27.932595+0.000707j
[2025-08-25 20:38:10] [Iter 306/1050] R1[155/300], Temp: 0.4738, Energy: -27.946239+0.001569j
[2025-08-25 20:38:16] [Iter 307/1050] R1[156/300], Temp: 0.4686, Energy: -27.939177-0.002997j
[2025-08-25 20:38:21] [Iter 308/1050] R1[157/300], Temp: 0.4634, Energy: -27.939999-0.001655j
[2025-08-25 20:38:27] [Iter 309/1050] R1[158/300], Temp: 0.4582, Energy: -27.931847-0.002953j
[2025-08-25 20:38:33] [Iter 310/1050] R1[159/300], Temp: 0.4529, Energy: -27.939496-0.000975j
[2025-08-25 20:38:39] [Iter 311/1050] R1[160/300], Temp: 0.4477, Energy: -27.941499-0.002222j
[2025-08-25 20:38:45] [Iter 312/1050] R1[161/300], Temp: 0.4425, Energy: -27.953554-0.002932j
[2025-08-25 20:38:50] [Iter 313/1050] R1[162/300], Temp: 0.4373, Energy: -27.938956-0.003672j
[2025-08-25 20:38:56] [Iter 314/1050] R1[163/300], Temp: 0.4321, Energy: -27.944688-0.000566j
[2025-08-25 20:39:02] [Iter 315/1050] R1[164/300], Temp: 0.4270, Energy: -27.941806+0.000035j
[2025-08-25 20:39:08] [Iter 316/1050] R1[165/300], Temp: 0.4218, Energy: -27.956358+0.004710j
[2025-08-25 20:39:14] [Iter 317/1050] R1[166/300], Temp: 0.4166, Energy: -27.941582-0.002711j
[2025-08-25 20:39:19] [Iter 318/1050] R1[167/300], Temp: 0.4115, Energy: -27.941481-0.000259j
[2025-08-25 20:39:25] [Iter 319/1050] R1[168/300], Temp: 0.4063, Energy: -27.945180-0.002605j
[2025-08-25 20:39:31] [Iter 320/1050] R1[169/300], Temp: 0.4012, Energy: -27.948743+0.002580j
[2025-08-25 20:39:37] [Iter 321/1050] R1[170/300], Temp: 0.3960, Energy: -27.938919-0.005893j
[2025-08-25 20:39:43] [Iter 322/1050] R1[171/300], Temp: 0.3909, Energy: -27.940760-0.001159j
[2025-08-25 20:39:48] [Iter 323/1050] R1[172/300], Temp: 0.3858, Energy: -27.941062+0.000895j
[2025-08-25 20:39:54] [Iter 324/1050] R1[173/300], Temp: 0.3807, Energy: -27.927690+0.003322j
[2025-08-25 20:40:00] [Iter 325/1050] R1[174/300], Temp: 0.3757, Energy: -27.939483-0.001269j
[2025-08-25 20:40:06] [Iter 326/1050] R1[175/300], Temp: 0.3706, Energy: -27.942964-0.002094j
[2025-08-25 20:40:11] [Iter 327/1050] R1[176/300], Temp: 0.3655, Energy: -27.944138+0.000651j
[2025-08-25 20:40:17] [Iter 328/1050] R1[177/300], Temp: 0.3605, Energy: -27.943797+0.000735j
[2025-08-25 20:40:23] [Iter 329/1050] R1[178/300], Temp: 0.3555, Energy: -27.942902-0.001755j
[2025-08-25 20:40:29] [Iter 330/1050] R1[179/300], Temp: 0.3505, Energy: -27.933465-0.000435j
[2025-08-25 20:40:35] [Iter 331/1050] R1[180/300], Temp: 0.3455, Energy: -27.939879+0.000482j
[2025-08-25 20:40:40] [Iter 332/1050] R1[181/300], Temp: 0.3405, Energy: -27.947551-0.003598j
[2025-08-25 20:40:46] [Iter 333/1050] R1[182/300], Temp: 0.3356, Energy: -27.934633+0.000445j
[2025-08-25 20:40:52] [Iter 334/1050] R1[183/300], Temp: 0.3306, Energy: -27.945952-0.003446j
[2025-08-25 20:40:58] [Iter 335/1050] R1[184/300], Temp: 0.3257, Energy: -27.938665-0.002121j
[2025-08-25 20:41:04] [Iter 336/1050] R1[185/300], Temp: 0.3208, Energy: -27.944884-0.002243j
[2025-08-25 20:41:09] [Iter 337/1050] R1[186/300], Temp: 0.3159, Energy: -27.946098-0.001233j
[2025-08-25 20:41:15] [Iter 338/1050] R1[187/300], Temp: 0.3111, Energy: -27.946647-0.000692j
[2025-08-25 20:41:21] [Iter 339/1050] R1[188/300], Temp: 0.3062, Energy: -27.940317+0.000490j
[2025-08-25 20:41:27] [Iter 340/1050] R1[189/300], Temp: 0.3014, Energy: -27.932808-0.000355j
[2025-08-25 20:41:33] [Iter 341/1050] R1[190/300], Temp: 0.2966, Energy: -27.944780-0.001441j
[2025-08-25 20:41:38] [Iter 342/1050] R1[191/300], Temp: 0.2919, Energy: -27.956589-0.006745j
[2025-08-25 20:41:44] [Iter 343/1050] R1[192/300], Temp: 0.2871, Energy: -27.941833+0.000657j
[2025-08-25 20:41:50] [Iter 344/1050] R1[193/300], Temp: 0.2824, Energy: -27.944294-0.001036j
[2025-08-25 20:41:56] [Iter 345/1050] R1[194/300], Temp: 0.2777, Energy: -27.935495+0.000316j
[2025-08-25 20:42:01] [Iter 346/1050] R1[195/300], Temp: 0.2730, Energy: -27.943590-0.001039j
[2025-08-25 20:42:07] [Iter 347/1050] R1[196/300], Temp: 0.2684, Energy: -27.933136-0.001424j
[2025-08-25 20:42:13] [Iter 348/1050] R1[197/300], Temp: 0.2637, Energy: -27.942905+0.000281j
[2025-08-25 20:42:19] [Iter 349/1050] R1[198/300], Temp: 0.2591, Energy: -27.939405+0.000939j
[2025-08-25 20:42:25] [Iter 350/1050] R1[199/300], Temp: 0.2545, Energy: -27.946520+0.000147j
[2025-08-25 20:42:30] [Iter 351/1050] R1[200/300], Temp: 0.2500, Energy: -27.935085+0.003963j
[2025-08-25 20:42:36] [Iter 352/1050] R1[201/300], Temp: 0.2455, Energy: -27.940450+0.004357j
[2025-08-25 20:42:42] [Iter 353/1050] R1[202/300], Temp: 0.2410, Energy: -27.939576+0.000063j
[2025-08-25 20:42:48] [Iter 354/1050] R1[203/300], Temp: 0.2365, Energy: -27.936097-0.003572j
[2025-08-25 20:42:54] [Iter 355/1050] R1[204/300], Temp: 0.2321, Energy: -27.941270+0.000019j
[2025-08-25 20:42:59] [Iter 356/1050] R1[205/300], Temp: 0.2277, Energy: -27.934327+0.001844j
[2025-08-25 20:43:05] [Iter 357/1050] R1[206/300], Temp: 0.2233, Energy: -27.936585-0.001133j
[2025-08-25 20:43:11] [Iter 358/1050] R1[207/300], Temp: 0.2190, Energy: -27.949615+0.004360j
[2025-08-25 20:43:17] [Iter 359/1050] R1[208/300], Temp: 0.2146, Energy: -27.941824+0.000385j
[2025-08-25 20:43:23] [Iter 360/1050] R1[209/300], Temp: 0.2104, Energy: -27.939009-0.000831j
[2025-08-25 20:43:28] [Iter 361/1050] R1[210/300], Temp: 0.2061, Energy: -27.940141-0.001831j
[2025-08-25 20:43:34] [Iter 362/1050] R1[211/300], Temp: 0.2019, Energy: -27.948561+0.003252j
[2025-08-25 20:43:40] [Iter 363/1050] R1[212/300], Temp: 0.1977, Energy: -27.941473+0.000335j
[2025-08-25 20:43:46] [Iter 364/1050] R1[213/300], Temp: 0.1935, Energy: -27.944371+0.000883j
[2025-08-25 20:43:52] [Iter 365/1050] R1[214/300], Temp: 0.1894, Energy: -27.944728+0.001220j
[2025-08-25 20:43:57] [Iter 366/1050] R1[215/300], Temp: 0.1853, Energy: -27.935357+0.002183j
[2025-08-25 20:44:03] [Iter 367/1050] R1[216/300], Temp: 0.1813, Energy: -27.932185-0.000416j
[2025-08-25 20:44:09] [Iter 368/1050] R1[217/300], Temp: 0.1773, Energy: -27.938957+0.001469j
[2025-08-25 20:44:15] [Iter 369/1050] R1[218/300], Temp: 0.1733, Energy: -27.949415+0.000142j
[2025-08-25 20:44:20] [Iter 370/1050] R1[219/300], Temp: 0.1693, Energy: -27.941831-0.002170j
[2025-08-25 20:44:26] [Iter 371/1050] R1[220/300], Temp: 0.1654, Energy: -27.945462-0.001806j
[2025-08-25 20:44:32] [Iter 372/1050] R1[221/300], Temp: 0.1616, Energy: -27.943138+0.000247j
[2025-08-25 20:44:38] [Iter 373/1050] R1[222/300], Temp: 0.1577, Energy: -27.934704-0.001718j
[2025-08-25 20:44:43] [Iter 374/1050] R1[223/300], Temp: 0.1539, Energy: -27.944484+0.000114j
[2025-08-25 20:44:49] [Iter 375/1050] R1[224/300], Temp: 0.1502, Energy: -27.943657+0.000488j
[2025-08-25 20:44:55] [Iter 376/1050] R1[225/300], Temp: 0.1464, Energy: -27.942387-0.001452j
[2025-08-25 20:45:01] [Iter 377/1050] R1[226/300], Temp: 0.1428, Energy: -27.951165-0.001155j
[2025-08-25 20:45:06] [Iter 378/1050] R1[227/300], Temp: 0.1391, Energy: -27.951131+0.002746j
[2025-08-25 20:45:12] [Iter 379/1050] R1[228/300], Temp: 0.1355, Energy: -27.938176-0.000961j
[2025-08-25 20:45:18] [Iter 380/1050] R1[229/300], Temp: 0.1320, Energy: -27.938294+0.000523j
[2025-08-25 20:45:24] [Iter 381/1050] R1[230/300], Temp: 0.1284, Energy: -27.948122+0.002536j
[2025-08-25 20:45:30] [Iter 382/1050] R1[231/300], Temp: 0.1249, Energy: -27.939263-0.001209j
[2025-08-25 20:45:35] [Iter 383/1050] R1[232/300], Temp: 0.1215, Energy: -27.936050-0.002890j
[2025-08-25 20:45:41] [Iter 384/1050] R1[233/300], Temp: 0.1181, Energy: -27.936105+0.001366j
[2025-08-25 20:45:47] [Iter 385/1050] R1[234/300], Temp: 0.1147, Energy: -27.937102+0.000028j
[2025-08-25 20:45:53] [Iter 386/1050] R1[235/300], Temp: 0.1114, Energy: -27.936043-0.009594j
[2025-08-25 20:45:59] [Iter 387/1050] R1[236/300], Temp: 0.1082, Energy: -27.937560+0.001808j
[2025-08-25 20:46:05] [Iter 388/1050] R1[237/300], Temp: 0.1049, Energy: -27.940916-0.004521j
[2025-08-25 20:46:11] [Iter 389/1050] R1[238/300], Temp: 0.1017, Energy: -27.941194-0.003702j
[2025-08-25 20:46:16] [Iter 390/1050] R1[239/300], Temp: 0.0986, Energy: -27.936781+0.003373j
[2025-08-25 20:46:22] [Iter 391/1050] R1[240/300], Temp: 0.0955, Energy: -27.936286+0.002228j
[2025-08-25 20:46:28] [Iter 392/1050] R1[241/300], Temp: 0.0924, Energy: -27.931213-0.000466j
[2025-08-25 20:46:34] [Iter 393/1050] R1[242/300], Temp: 0.0894, Energy: -27.943310-0.003057j
[2025-08-25 20:46:39] [Iter 394/1050] R1[243/300], Temp: 0.0865, Energy: -27.935544-0.000834j
[2025-08-25 20:46:45] [Iter 395/1050] R1[244/300], Temp: 0.0835, Energy: -27.945304+0.002064j
[2025-08-25 20:46:51] [Iter 396/1050] R1[245/300], Temp: 0.0807, Energy: -27.938727+0.001678j
[2025-08-25 20:46:57] [Iter 397/1050] R1[246/300], Temp: 0.0778, Energy: -27.948923+0.008710j
[2025-08-25 20:47:03] [Iter 398/1050] R1[247/300], Temp: 0.0751, Energy: -27.941871+0.002358j
[2025-08-25 20:47:08] [Iter 399/1050] R1[248/300], Temp: 0.0723, Energy: -27.929725-0.002641j
[2025-08-25 20:47:14] [Iter 400/1050] R1[249/300], Temp: 0.0696, Energy: -27.942939+0.002640j
[2025-08-25 20:47:14] ✓ Checkpoint saved: checkpoint_iter_000400.pkl
[2025-08-25 20:47:20] [Iter 401/1050] R1[250/300], Temp: 0.0670, Energy: -27.942694-0.000980j
[2025-08-25 20:47:26] [Iter 402/1050] R1[251/300], Temp: 0.0644, Energy: -27.950925+0.002877j
[2025-08-25 20:47:31] [Iter 403/1050] R1[252/300], Temp: 0.0618, Energy: -27.937811-0.002973j
[2025-08-25 20:47:37] [Iter 404/1050] R1[253/300], Temp: 0.0593, Energy: -27.940434+0.004305j
[2025-08-25 20:47:43] [Iter 405/1050] R1[254/300], Temp: 0.0569, Energy: -27.961501+0.001748j
[2025-08-25 20:47:49] [Iter 406/1050] R1[255/300], Temp: 0.0545, Energy: -27.942534-0.000267j
[2025-08-25 20:47:54] [Iter 407/1050] R1[256/300], Temp: 0.0521, Energy: -27.950691+0.005767j
[2025-08-25 20:48:00] [Iter 408/1050] R1[257/300], Temp: 0.0498, Energy: -27.937579+0.002975j
[2025-08-25 20:48:06] [Iter 409/1050] R1[258/300], Temp: 0.0476, Energy: -27.943566-0.002686j
[2025-08-25 20:48:12] [Iter 410/1050] R1[259/300], Temp: 0.0454, Energy: -27.944765-0.002297j
[2025-08-25 20:48:18] [Iter 411/1050] R1[260/300], Temp: 0.0432, Energy: -27.944884+0.002135j
[2025-08-25 20:48:23] [Iter 412/1050] R1[261/300], Temp: 0.0411, Energy: -27.951578-0.000318j
[2025-08-25 20:48:29] [Iter 413/1050] R1[262/300], Temp: 0.0391, Energy: -27.940605-0.003589j
[2025-08-25 20:48:35] [Iter 414/1050] R1[263/300], Temp: 0.0371, Energy: -27.928636-0.001231j
[2025-08-25 20:48:41] [Iter 415/1050] R1[264/300], Temp: 0.0351, Energy: -27.942344-0.001809j
[2025-08-25 20:48:46] [Iter 416/1050] R1[265/300], Temp: 0.0332, Energy: -27.942082+0.001347j
[2025-08-25 20:48:52] [Iter 417/1050] R1[266/300], Temp: 0.0314, Energy: -27.945769+0.001608j
[2025-08-25 20:48:58] [Iter 418/1050] R1[267/300], Temp: 0.0296, Energy: -27.937013+0.000468j
[2025-08-25 20:49:04] [Iter 419/1050] R1[268/300], Temp: 0.0278, Energy: -27.933632+0.002480j
[2025-08-25 20:49:09] [Iter 420/1050] R1[269/300], Temp: 0.0261, Energy: -27.953464+0.001230j
[2025-08-25 20:49:15] [Iter 421/1050] R1[270/300], Temp: 0.0245, Energy: -27.936250+0.002807j
[2025-08-25 20:49:21] [Iter 422/1050] R1[271/300], Temp: 0.0229, Energy: -27.938501+0.000926j
[2025-08-25 20:49:27] [Iter 423/1050] R1[272/300], Temp: 0.0213, Energy: -27.936119-0.004727j
[2025-08-25 20:49:32] [Iter 424/1050] R1[273/300], Temp: 0.0199, Energy: -27.951895+0.000953j
[2025-08-25 20:49:38] [Iter 425/1050] R1[274/300], Temp: 0.0184, Energy: -27.947609+0.001880j
[2025-08-25 20:49:44] [Iter 426/1050] R1[275/300], Temp: 0.0170, Energy: -27.947897-0.000268j
[2025-08-25 20:49:50] [Iter 427/1050] R1[276/300], Temp: 0.0157, Energy: -27.936822-0.001804j
[2025-08-25 20:49:55] [Iter 428/1050] R1[277/300], Temp: 0.0144, Energy: -27.948303-0.002031j
[2025-08-25 20:50:01] [Iter 429/1050] R1[278/300], Temp: 0.0132, Energy: -27.944578+0.001044j
[2025-08-25 20:50:07] [Iter 430/1050] R1[279/300], Temp: 0.0120, Energy: -27.939460-0.000898j
[2025-08-25 20:50:13] [Iter 431/1050] R1[280/300], Temp: 0.0109, Energy: -27.948004+0.003190j
[2025-08-25 20:50:19] [Iter 432/1050] R1[281/300], Temp: 0.0099, Energy: -27.937691-0.000019j
[2025-08-25 20:50:24] [Iter 433/1050] R1[282/300], Temp: 0.0089, Energy: -27.936073-0.000904j
[2025-08-25 20:50:30] [Iter 434/1050] R1[283/300], Temp: 0.0079, Energy: -27.947344-0.000334j
[2025-08-25 20:50:36] [Iter 435/1050] R1[284/300], Temp: 0.0070, Energy: -27.933741-0.002985j
[2025-08-25 20:50:42] [Iter 436/1050] R1[285/300], Temp: 0.0062, Energy: -27.937651+0.000035j
[2025-08-25 20:50:47] [Iter 437/1050] R1[286/300], Temp: 0.0054, Energy: -27.945613-0.004626j
[2025-08-25 20:50:53] [Iter 438/1050] R1[287/300], Temp: 0.0046, Energy: -27.948037-0.000590j
[2025-08-25 20:50:59] [Iter 439/1050] R1[288/300], Temp: 0.0039, Energy: -27.935781+0.001670j
[2025-08-25 20:51:05] [Iter 440/1050] R1[289/300], Temp: 0.0033, Energy: -27.940814-0.000220j
[2025-08-25 20:51:10] [Iter 441/1050] R1[290/300], Temp: 0.0027, Energy: -27.937873+0.001694j
[2025-08-25 20:51:16] [Iter 442/1050] R1[291/300], Temp: 0.0022, Energy: -27.940734-0.000605j
[2025-08-25 20:51:22] [Iter 443/1050] R1[292/300], Temp: 0.0018, Energy: -27.936963-0.002966j
[2025-08-25 20:51:28] [Iter 444/1050] R1[293/300], Temp: 0.0013, Energy: -27.932662+0.000883j
[2025-08-25 20:51:33] [Iter 445/1050] R1[294/300], Temp: 0.0010, Energy: -27.944524-0.000532j
[2025-08-25 20:51:39] [Iter 446/1050] R1[295/300], Temp: 0.0007, Energy: -27.933554-0.000733j
[2025-08-25 20:51:45] [Iter 447/1050] R1[296/300], Temp: 0.0004, Energy: -27.929287-0.001245j
[2025-08-25 20:51:51] [Iter 448/1050] R1[297/300], Temp: 0.0002, Energy: -27.937561+0.000562j
[2025-08-25 20:51:57] [Iter 449/1050] R1[298/300], Temp: 0.0001, Energy: -27.944868-0.002635j
[2025-08-25 20:52:02] [Iter 450/1050] R1[299/300], Temp: 0.0000, Energy: -27.950600-0.000717j
[2025-08-25 20:52:02] RESTART #2 | Period: 600
[2025-08-25 20:52:08] [Iter 451/1050] R2[0/600], Temp: 1.0000, Energy: -27.944803+0.001105j
[2025-08-25 20:52:14] [Iter 452/1050] R2[1/600], Temp: 1.0000, Energy: -27.930782+0.000588j
[2025-08-25 20:52:20] [Iter 453/1050] R2[2/600], Temp: 1.0000, Energy: -27.947140+0.001250j
[2025-08-25 20:52:25] [Iter 454/1050] R2[3/600], Temp: 0.9999, Energy: -27.942037+0.006763j
[2025-08-25 20:52:31] [Iter 455/1050] R2[4/600], Temp: 0.9999, Energy: -27.941984-0.002327j
[2025-08-25 20:52:37] [Iter 456/1050] R2[5/600], Temp: 0.9998, Energy: -27.928913+0.003479j
[2025-08-25 20:52:43] [Iter 457/1050] R2[6/600], Temp: 0.9998, Energy: -27.944943+0.003090j
[2025-08-25 20:52:48] [Iter 458/1050] R2[7/600], Temp: 0.9997, Energy: -27.929851-0.003902j
[2025-08-25 20:52:54] [Iter 459/1050] R2[8/600], Temp: 0.9996, Energy: -27.949337+0.004267j
[2025-08-25 20:53:00] [Iter 460/1050] R2[9/600], Temp: 0.9994, Energy: -27.932318+0.003110j
[2025-08-25 20:53:06] [Iter 461/1050] R2[10/600], Temp: 0.9993, Energy: -27.935013-0.002970j
[2025-08-25 20:53:12] [Iter 462/1050] R2[11/600], Temp: 0.9992, Energy: -27.944723-0.001386j
[2025-08-25 20:53:17] [Iter 463/1050] R2[12/600], Temp: 0.9990, Energy: -27.933334+0.000976j
[2025-08-25 20:53:23] [Iter 464/1050] R2[13/600], Temp: 0.9988, Energy: -27.948944-0.001304j
[2025-08-25 20:53:29] [Iter 465/1050] R2[14/600], Temp: 0.9987, Energy: -27.954856-0.002266j
[2025-08-25 20:53:35] [Iter 466/1050] R2[15/600], Temp: 0.9985, Energy: -27.943082+0.007303j
[2025-08-25 20:53:40] [Iter 467/1050] R2[16/600], Temp: 0.9982, Energy: -27.936697-0.002237j
[2025-08-25 20:53:46] [Iter 468/1050] R2[17/600], Temp: 0.9980, Energy: -27.942943-0.004046j
[2025-08-25 20:53:52] [Iter 469/1050] R2[18/600], Temp: 0.9978, Energy: -27.936355-0.001332j
[2025-08-25 20:53:58] [Iter 470/1050] R2[19/600], Temp: 0.9975, Energy: -27.941198+0.001455j
[2025-08-25 20:54:03] [Iter 471/1050] R2[20/600], Temp: 0.9973, Energy: -27.945785-0.001062j
[2025-08-25 20:54:09] [Iter 472/1050] R2[21/600], Temp: 0.9970, Energy: -27.941214+0.001130j
[2025-08-25 20:54:15] [Iter 473/1050] R2[22/600], Temp: 0.9967, Energy: -27.943294+0.002988j
[2025-08-25 20:54:21] [Iter 474/1050] R2[23/600], Temp: 0.9964, Energy: -27.930244+0.004599j
[2025-08-25 20:54:27] [Iter 475/1050] R2[24/600], Temp: 0.9961, Energy: -27.941401+0.001754j
[2025-08-25 20:54:32] [Iter 476/1050] R2[25/600], Temp: 0.9957, Energy: -27.941294+0.001645j
[2025-08-25 20:54:38] [Iter 477/1050] R2[26/600], Temp: 0.9954, Energy: -27.931164-0.000898j
[2025-08-25 20:54:44] [Iter 478/1050] R2[27/600], Temp: 0.9950, Energy: -27.941117-0.002020j
[2025-08-25 20:54:50] [Iter 479/1050] R2[28/600], Temp: 0.9946, Energy: -27.945818+0.000750j
[2025-08-25 20:54:55] [Iter 480/1050] R2[29/600], Temp: 0.9942, Energy: -27.943714+0.001115j
[2025-08-25 20:55:01] [Iter 481/1050] R2[30/600], Temp: 0.9938, Energy: -27.949986-0.000097j
[2025-08-25 20:55:07] [Iter 482/1050] R2[31/600], Temp: 0.9934, Energy: -27.947571-0.000556j
[2025-08-25 20:55:13] [Iter 483/1050] R2[32/600], Temp: 0.9930, Energy: -27.934454-0.000064j
[2025-08-25 20:55:19] [Iter 484/1050] R2[33/600], Temp: 0.9926, Energy: -27.940453-0.000585j
[2025-08-25 20:55:24] [Iter 485/1050] R2[34/600], Temp: 0.9921, Energy: -27.945367+0.001326j
[2025-08-25 20:55:30] [Iter 486/1050] R2[35/600], Temp: 0.9916, Energy: -27.957141+0.000495j
[2025-08-25 20:55:36] [Iter 487/1050] R2[36/600], Temp: 0.9911, Energy: -27.937359+0.006454j
[2025-08-25 20:55:42] [Iter 488/1050] R2[37/600], Temp: 0.9906, Energy: -27.942517-0.002271j
[2025-08-25 20:55:47] [Iter 489/1050] R2[38/600], Temp: 0.9901, Energy: -27.945457+0.001042j
[2025-08-25 20:55:53] [Iter 490/1050] R2[39/600], Temp: 0.9896, Energy: -27.949194+0.001759j
[2025-08-25 20:55:59] [Iter 491/1050] R2[40/600], Temp: 0.9891, Energy: -27.934338+0.000700j
[2025-08-25 20:56:05] [Iter 492/1050] R2[41/600], Temp: 0.9885, Energy: -27.936080+0.000780j
[2025-08-25 20:56:10] [Iter 493/1050] R2[42/600], Temp: 0.9880, Energy: -27.947144+0.000739j
[2025-08-25 20:56:16] [Iter 494/1050] R2[43/600], Temp: 0.9874, Energy: -27.950229+0.001238j
[2025-08-25 20:56:22] [Iter 495/1050] R2[44/600], Temp: 0.9868, Energy: -27.936661-0.000335j
[2025-08-25 20:56:28] [Iter 496/1050] R2[45/600], Temp: 0.9862, Energy: -27.943729-0.002458j
[2025-08-25 20:56:34] [Iter 497/1050] R2[46/600], Temp: 0.9856, Energy: -27.942108+0.003464j
[2025-08-25 20:56:39] [Iter 498/1050] R2[47/600], Temp: 0.9849, Energy: -27.935492+0.001894j
[2025-08-25 20:56:45] [Iter 499/1050] R2[48/600], Temp: 0.9843, Energy: -27.943069-0.001157j
[2025-08-25 20:56:51] [Iter 500/1050] R2[49/600], Temp: 0.9836, Energy: -27.943369-0.001840j
[2025-08-25 20:56:51] ✓ Checkpoint saved: checkpoint_iter_000500.pkl
[2025-08-25 20:56:57] [Iter 501/1050] R2[50/600], Temp: 0.9830, Energy: -27.937417+0.003019j
[2025-08-25 20:57:03] [Iter 502/1050] R2[51/600], Temp: 0.9823, Energy: -27.958000-0.002622j
[2025-08-25 20:57:08] [Iter 503/1050] R2[52/600], Temp: 0.9816, Energy: -27.937793-0.000264j
[2025-08-25 20:57:14] [Iter 504/1050] R2[53/600], Temp: 0.9809, Energy: -27.939189+0.001198j
[2025-08-25 20:57:20] [Iter 505/1050] R2[54/600], Temp: 0.9801, Energy: -27.944149+0.000453j
[2025-08-25 20:57:26] [Iter 506/1050] R2[55/600], Temp: 0.9794, Energy: -27.946542-0.001799j
[2025-08-25 20:57:32] [Iter 507/1050] R2[56/600], Temp: 0.9787, Energy: -27.941863-0.004160j
[2025-08-25 20:57:37] [Iter 508/1050] R2[57/600], Temp: 0.9779, Energy: -27.941267-0.000464j
[2025-08-25 20:57:43] [Iter 509/1050] R2[58/600], Temp: 0.9771, Energy: -27.947670-0.001215j
[2025-08-25 20:57:49] [Iter 510/1050] R2[59/600], Temp: 0.9763, Energy: -27.932798+0.002258j
[2025-08-25 20:57:55] [Iter 511/1050] R2[60/600], Temp: 0.9755, Energy: -27.950461-0.002171j
[2025-08-25 20:58:00] [Iter 512/1050] R2[61/600], Temp: 0.9747, Energy: -27.948311-0.001720j
[2025-08-25 20:58:06] [Iter 513/1050] R2[62/600], Temp: 0.9739, Energy: -27.939677+0.000243j
[2025-08-25 20:58:12] [Iter 514/1050] R2[63/600], Temp: 0.9730, Energy: -27.948142-0.000398j
[2025-08-25 20:58:18] [Iter 515/1050] R2[64/600], Temp: 0.9722, Energy: -27.942636-0.000005j
[2025-08-25 20:58:24] [Iter 516/1050] R2[65/600], Temp: 0.9713, Energy: -27.929000-0.000987j
[2025-08-25 20:58:29] [Iter 517/1050] R2[66/600], Temp: 0.9704, Energy: -27.938924+0.000459j
[2025-08-25 20:58:35] [Iter 518/1050] R2[67/600], Temp: 0.9695, Energy: -27.937716-0.002031j
[2025-08-25 20:58:41] [Iter 519/1050] R2[68/600], Temp: 0.9686, Energy: -27.937651+0.003834j
[2025-08-25 20:58:47] [Iter 520/1050] R2[69/600], Temp: 0.9677, Energy: -27.952758+0.002423j
[2025-08-25 20:58:53] [Iter 521/1050] R2[70/600], Temp: 0.9668, Energy: -27.940938-0.001228j
[2025-08-25 20:58:58] [Iter 522/1050] R2[71/600], Temp: 0.9658, Energy: -27.937992+0.001668j
[2025-08-25 20:59:04] [Iter 523/1050] R2[72/600], Temp: 0.9649, Energy: -27.941695+0.001717j
[2025-08-25 20:59:10] [Iter 524/1050] R2[73/600], Temp: 0.9639, Energy: -27.938003+0.000227j
[2025-08-25 20:59:16] [Iter 525/1050] R2[74/600], Temp: 0.9629, Energy: -27.942023-0.003165j
[2025-08-25 20:59:21] [Iter 526/1050] R2[75/600], Temp: 0.9619, Energy: -27.929149+0.000821j
[2025-08-25 20:59:27] [Iter 527/1050] R2[76/600], Temp: 0.9609, Energy: -27.926780-0.000377j
[2025-08-25 20:59:33] [Iter 528/1050] R2[77/600], Temp: 0.9599, Energy: -27.942224-0.003655j
[2025-08-25 20:59:39] [Iter 529/1050] R2[78/600], Temp: 0.9589, Energy: -27.946699+0.000068j
[2025-08-25 20:59:45] [Iter 530/1050] R2[79/600], Temp: 0.9578, Energy: -27.947633+0.001741j
[2025-08-25 20:59:50] [Iter 531/1050] R2[80/600], Temp: 0.9568, Energy: -27.938672-0.002073j
[2025-08-25 20:59:56] [Iter 532/1050] R2[81/600], Temp: 0.9557, Energy: -27.956766+0.000550j
[2025-08-25 21:00:02] [Iter 533/1050] R2[82/600], Temp: 0.9546, Energy: -27.939946-0.001342j
[2025-08-25 21:00:08] [Iter 534/1050] R2[83/600], Temp: 0.9535, Energy: -27.943178+0.003727j
[2025-08-25 21:00:14] [Iter 535/1050] R2[84/600], Temp: 0.9524, Energy: -27.939620-0.001215j
[2025-08-25 21:00:19] [Iter 536/1050] R2[85/600], Temp: 0.9513, Energy: -27.938800+0.002010j
[2025-08-25 21:00:25] [Iter 537/1050] R2[86/600], Temp: 0.9502, Energy: -27.953456+0.004826j
[2025-08-25 21:00:31] [Iter 538/1050] R2[87/600], Temp: 0.9490, Energy: -27.937743-0.004095j
[2025-08-25 21:00:37] [Iter 539/1050] R2[88/600], Temp: 0.9479, Energy: -27.940178+0.001515j
[2025-08-25 21:00:43] [Iter 540/1050] R2[89/600], Temp: 0.9467, Energy: -27.937556+0.002878j
[2025-08-25 21:00:48] [Iter 541/1050] R2[90/600], Temp: 0.9455, Energy: -27.945168-0.000647j
[2025-08-25 21:00:54] [Iter 542/1050] R2[91/600], Temp: 0.9443, Energy: -27.936671+0.000005j
[2025-08-25 21:01:00] [Iter 543/1050] R2[92/600], Temp: 0.9431, Energy: -27.947586-0.003021j
[2025-08-25 21:01:06] [Iter 544/1050] R2[93/600], Temp: 0.9419, Energy: -27.933448-0.000179j
[2025-08-25 21:01:11] [Iter 545/1050] R2[94/600], Temp: 0.9407, Energy: -27.937807-0.000426j
[2025-08-25 21:01:17] [Iter 546/1050] R2[95/600], Temp: 0.9394, Energy: -27.935022-0.000408j
[2025-08-25 21:01:23] [Iter 547/1050] R2[96/600], Temp: 0.9382, Energy: -27.933590+0.004733j
[2025-08-25 21:01:29] [Iter 548/1050] R2[97/600], Temp: 0.9369, Energy: -27.952691+0.001740j
[2025-08-25 21:01:35] [Iter 549/1050] R2[98/600], Temp: 0.9356, Energy: -27.941721-0.002247j
[2025-08-25 21:01:40] [Iter 550/1050] R2[99/600], Temp: 0.9343, Energy: -27.949731-0.002853j
[2025-08-25 21:01:46] [Iter 551/1050] R2[100/600], Temp: 0.9330, Energy: -27.953023-0.005325j
[2025-08-25 21:01:52] [Iter 552/1050] R2[101/600], Temp: 0.9317, Energy: -27.940721+0.004803j
[2025-08-25 21:01:58] [Iter 553/1050] R2[102/600], Temp: 0.9304, Energy: -27.943016-0.000748j
[2025-08-25 21:02:04] [Iter 554/1050] R2[103/600], Temp: 0.9290, Energy: -27.938446+0.000628j
[2025-08-25 21:02:09] [Iter 555/1050] R2[104/600], Temp: 0.9277, Energy: -27.938936+0.000109j
[2025-08-25 21:02:15] [Iter 556/1050] R2[105/600], Temp: 0.9263, Energy: -27.943403-0.002346j
[2025-08-25 21:02:21] [Iter 557/1050] R2[106/600], Temp: 0.9249, Energy: -27.930464+0.000874j
[2025-08-25 21:02:27] [Iter 558/1050] R2[107/600], Temp: 0.9236, Energy: -27.943918+0.000583j
[2025-08-25 21:02:32] [Iter 559/1050] R2[108/600], Temp: 0.9222, Energy: -27.941426+0.001939j
[2025-08-25 21:02:38] [Iter 560/1050] R2[109/600], Temp: 0.9208, Energy: -27.936595+0.000660j
[2025-08-25 21:02:44] [Iter 561/1050] R2[110/600], Temp: 0.9193, Energy: -27.932478-0.000548j
[2025-08-25 21:02:50] [Iter 562/1050] R2[111/600], Temp: 0.9179, Energy: -27.946013-0.001738j
[2025-08-25 21:02:56] [Iter 563/1050] R2[112/600], Temp: 0.9165, Energy: -27.931045+0.001993j
[2025-08-25 21:03:01] [Iter 564/1050] R2[113/600], Temp: 0.9150, Energy: -27.945540-0.001256j
[2025-08-25 21:03:07] [Iter 565/1050] R2[114/600], Temp: 0.9135, Energy: -27.944470-0.001229j
[2025-08-25 21:03:13] [Iter 566/1050] R2[115/600], Temp: 0.9121, Energy: -27.942441+0.001585j
[2025-08-25 21:03:19] [Iter 567/1050] R2[116/600], Temp: 0.9106, Energy: -27.934383-0.002917j
[2025-08-25 21:03:24] [Iter 568/1050] R2[117/600], Temp: 0.9091, Energy: -27.938401-0.002162j
[2025-08-25 21:03:30] [Iter 569/1050] R2[118/600], Temp: 0.9076, Energy: -27.938671+0.002082j
[2025-08-25 21:03:36] [Iter 570/1050] R2[119/600], Temp: 0.9060, Energy: -27.929202+0.003337j
[2025-08-25 21:03:42] [Iter 571/1050] R2[120/600], Temp: 0.9045, Energy: -27.941667+0.001836j
[2025-08-25 21:03:47] [Iter 572/1050] R2[121/600], Temp: 0.9030, Energy: -27.945299+0.000430j
[2025-08-25 21:03:53] [Iter 573/1050] R2[122/600], Temp: 0.9014, Energy: -27.934084+0.002328j
[2025-08-25 21:03:59] [Iter 574/1050] R2[123/600], Temp: 0.8998, Energy: -27.948362+0.002131j
[2025-08-25 21:04:05] [Iter 575/1050] R2[124/600], Temp: 0.8983, Energy: -27.936295+0.001368j
[2025-08-25 21:04:10] [Iter 576/1050] R2[125/600], Temp: 0.8967, Energy: -27.948059-0.000835j
[2025-08-25 21:04:16] [Iter 577/1050] R2[126/600], Temp: 0.8951, Energy: -27.930617-0.000736j
[2025-08-25 21:04:22] [Iter 578/1050] R2[127/600], Temp: 0.8935, Energy: -27.944282+0.000063j
[2025-08-25 21:04:28] [Iter 579/1050] R2[128/600], Temp: 0.8918, Energy: -27.948288-0.003100j
[2025-08-25 21:04:34] [Iter 580/1050] R2[129/600], Temp: 0.8902, Energy: -27.947239+0.005681j
[2025-08-25 21:04:39] [Iter 581/1050] R2[130/600], Temp: 0.8886, Energy: -27.932587+0.000298j
[2025-08-25 21:04:45] [Iter 582/1050] R2[131/600], Temp: 0.8869, Energy: -27.941627-0.001455j
[2025-08-25 21:04:51] [Iter 583/1050] R2[132/600], Temp: 0.8853, Energy: -27.947302+0.002088j
[2025-08-25 21:04:57] [Iter 584/1050] R2[133/600], Temp: 0.8836, Energy: -27.940552+0.000825j
[2025-08-25 21:05:02] [Iter 585/1050] R2[134/600], Temp: 0.8819, Energy: -27.944541+0.001662j
[2025-08-25 21:05:08] [Iter 586/1050] R2[135/600], Temp: 0.8802, Energy: -27.944628+0.001057j
[2025-08-25 21:05:14] [Iter 587/1050] R2[136/600], Temp: 0.8785, Energy: -27.943643+0.001139j
[2025-08-25 21:05:20] [Iter 588/1050] R2[137/600], Temp: 0.8768, Energy: -27.941757+0.000323j
[2025-08-25 21:05:26] [Iter 589/1050] R2[138/600], Temp: 0.8751, Energy: -27.924099+0.000311j
[2025-08-25 21:05:31] [Iter 590/1050] R2[139/600], Temp: 0.8733, Energy: -27.939207+0.000405j
[2025-08-25 21:05:37] [Iter 591/1050] R2[140/600], Temp: 0.8716, Energy: -27.934354-0.001285j
[2025-08-25 21:05:43] [Iter 592/1050] R2[141/600], Temp: 0.8698, Energy: -27.942254+0.003747j
[2025-08-25 21:05:49] [Iter 593/1050] R2[142/600], Temp: 0.8680, Energy: -27.948432+0.000067j
[2025-08-25 21:05:54] [Iter 594/1050] R2[143/600], Temp: 0.8663, Energy: -27.955551+0.005529j
[2025-08-25 21:06:00] [Iter 595/1050] R2[144/600], Temp: 0.8645, Energy: -27.959717+0.002332j
[2025-08-25 21:06:06] [Iter 596/1050] R2[145/600], Temp: 0.8627, Energy: -27.936441-0.002393j
[2025-08-25 21:06:12] [Iter 597/1050] R2[146/600], Temp: 0.8609, Energy: -27.943471-0.005222j
[2025-08-25 21:06:18] [Iter 598/1050] R2[147/600], Temp: 0.8591, Energy: -27.936092-0.000534j
[2025-08-25 21:06:23] [Iter 599/1050] R2[148/600], Temp: 0.8572, Energy: -27.941902-0.000739j
[2025-08-25 21:06:29] [Iter 600/1050] R2[149/600], Temp: 0.8554, Energy: -27.943252+0.000111j
[2025-08-25 21:06:29] ✓ Checkpoint saved: checkpoint_iter_000600.pkl
[2025-08-25 21:06:35] [Iter 601/1050] R2[150/600], Temp: 0.8536, Energy: -27.944294+0.002113j
[2025-08-25 21:06:41] [Iter 602/1050] R2[151/600], Temp: 0.8517, Energy: -27.935245-0.001059j
[2025-08-25 21:06:47] [Iter 603/1050] R2[152/600], Temp: 0.8498, Energy: -27.941062+0.001602j
[2025-08-25 21:06:52] [Iter 604/1050] R2[153/600], Temp: 0.8480, Energy: -27.954614-0.002370j
[2025-08-25 21:06:58] [Iter 605/1050] R2[154/600], Temp: 0.8461, Energy: -27.943001+0.004398j
[2025-08-25 21:07:04] [Iter 606/1050] R2[155/600], Temp: 0.8442, Energy: -27.948897+0.003869j
[2025-08-25 21:07:10] [Iter 607/1050] R2[156/600], Temp: 0.8423, Energy: -27.947204+0.002510j
[2025-08-25 21:07:15] [Iter 608/1050] R2[157/600], Temp: 0.8404, Energy: -27.944535-0.004628j
[2025-08-25 21:07:21] [Iter 609/1050] R2[158/600], Temp: 0.8384, Energy: -27.937219+0.005282j
[2025-08-25 21:07:27] [Iter 610/1050] R2[159/600], Temp: 0.8365, Energy: -27.942154+0.001116j
[2025-08-25 21:07:33] [Iter 611/1050] R2[160/600], Temp: 0.8346, Energy: -27.937569+0.001094j
[2025-08-25 21:07:39] [Iter 612/1050] R2[161/600], Temp: 0.8326, Energy: -27.937098-0.001971j
[2025-08-25 21:07:44] [Iter 613/1050] R2[162/600], Temp: 0.8307, Energy: -27.945209-0.000514j
[2025-08-25 21:07:50] [Iter 614/1050] R2[163/600], Temp: 0.8287, Energy: -27.938606+0.000891j
[2025-08-25 21:07:56] [Iter 615/1050] R2[164/600], Temp: 0.8267, Energy: -27.935584+0.001497j
[2025-08-25 21:08:02] [Iter 616/1050] R2[165/600], Temp: 0.8247, Energy: -27.940767-0.002789j
[2025-08-25 21:08:07] [Iter 617/1050] R2[166/600], Temp: 0.8227, Energy: -27.946869-0.002166j
[2025-08-25 21:08:13] [Iter 618/1050] R2[167/600], Temp: 0.8207, Energy: -27.947997-0.002864j
[2025-08-25 21:08:19] [Iter 619/1050] R2[168/600], Temp: 0.8187, Energy: -27.928706+0.001512j
[2025-08-25 21:08:25] [Iter 620/1050] R2[169/600], Temp: 0.8167, Energy: -27.937982-0.002794j
[2025-08-25 21:08:31] [Iter 621/1050] R2[170/600], Temp: 0.8147, Energy: -27.932831+0.000417j
[2025-08-25 21:08:36] [Iter 622/1050] R2[171/600], Temp: 0.8126, Energy: -27.938839-0.000877j
[2025-08-25 21:08:42] [Iter 623/1050] R2[172/600], Temp: 0.8106, Energy: -27.945739+0.000592j
[2025-08-25 21:08:48] [Iter 624/1050] R2[173/600], Temp: 0.8085, Energy: -27.947498+0.002767j
[2025-08-25 21:08:54] [Iter 625/1050] R2[174/600], Temp: 0.8065, Energy: -27.944661+0.002958j
[2025-08-25 21:08:59] [Iter 626/1050] R2[175/600], Temp: 0.8044, Energy: -27.934449+0.001983j
[2025-08-25 21:09:05] [Iter 627/1050] R2[176/600], Temp: 0.8023, Energy: -27.943288+0.003398j
[2025-08-25 21:09:11] [Iter 628/1050] R2[177/600], Temp: 0.8002, Energy: -27.943331+0.003430j
[2025-08-25 21:09:17] [Iter 629/1050] R2[178/600], Temp: 0.7981, Energy: -27.937974-0.004078j
[2025-08-25 21:09:23] [Iter 630/1050] R2[179/600], Temp: 0.7960, Energy: -27.955917-0.000482j
[2025-08-25 21:09:28] [Iter 631/1050] R2[180/600], Temp: 0.7939, Energy: -27.944090-0.001257j
[2025-08-25 21:09:34] [Iter 632/1050] R2[181/600], Temp: 0.7918, Energy: -27.951209-0.000356j
[2025-08-25 21:09:40] [Iter 633/1050] R2[182/600], Temp: 0.7896, Energy: -27.936038-0.000469j
[2025-08-25 21:09:46] [Iter 634/1050] R2[183/600], Temp: 0.7875, Energy: -27.958349-0.001932j
[2025-08-25 21:09:51] [Iter 635/1050] R2[184/600], Temp: 0.7854, Energy: -27.946427-0.000790j
[2025-08-25 21:09:57] [Iter 636/1050] R2[185/600], Temp: 0.7832, Energy: -27.937372+0.001829j
[2025-08-25 21:10:03] [Iter 637/1050] R2[186/600], Temp: 0.7810, Energy: -27.947646+0.002650j
[2025-08-25 21:10:09] [Iter 638/1050] R2[187/600], Temp: 0.7789, Energy: -27.946664+0.000735j
[2025-08-25 21:10:15] [Iter 639/1050] R2[188/600], Temp: 0.7767, Energy: -27.952647-0.000976j
[2025-08-25 21:10:20] [Iter 640/1050] R2[189/600], Temp: 0.7745, Energy: -27.931070+0.001799j
[2025-08-25 21:10:26] [Iter 641/1050] R2[190/600], Temp: 0.7723, Energy: -27.943312-0.002275j
[2025-08-25 21:10:32] [Iter 642/1050] R2[191/600], Temp: 0.7701, Energy: -27.935877-0.000641j
[2025-08-25 21:10:38] [Iter 643/1050] R2[192/600], Temp: 0.7679, Energy: -27.942316+0.001348j
[2025-08-25 21:10:44] [Iter 644/1050] R2[193/600], Temp: 0.7657, Energy: -27.939311+0.000373j
[2025-08-25 21:10:49] [Iter 645/1050] R2[194/600], Temp: 0.7635, Energy: -27.937384-0.001706j
[2025-08-25 21:10:55] [Iter 646/1050] R2[195/600], Temp: 0.7612, Energy: -27.950316+0.000840j
[2025-08-25 21:11:01] [Iter 647/1050] R2[196/600], Temp: 0.7590, Energy: -27.935272-0.001732j
[2025-08-25 21:11:07] [Iter 648/1050] R2[197/600], Temp: 0.7568, Energy: -27.934613+0.004764j
[2025-08-25 21:11:13] [Iter 649/1050] R2[198/600], Temp: 0.7545, Energy: -27.941113+0.001164j
[2025-08-25 21:11:18] [Iter 650/1050] R2[199/600], Temp: 0.7523, Energy: -27.943561-0.000176j
[2025-08-25 21:11:24] [Iter 651/1050] R2[200/600], Temp: 0.7500, Energy: -27.949188-0.000389j
[2025-08-25 21:11:30] [Iter 652/1050] R2[201/600], Temp: 0.7477, Energy: -27.947967+0.000330j
[2025-08-25 21:11:36] [Iter 653/1050] R2[202/600], Temp: 0.7455, Energy: -27.946844+0.003991j
[2025-08-25 21:11:42] [Iter 654/1050] R2[203/600], Temp: 0.7432, Energy: -27.940214-0.000242j
[2025-08-25 21:11:47] [Iter 655/1050] R2[204/600], Temp: 0.7409, Energy: -27.944357+0.001075j
[2025-08-25 21:11:53] [Iter 656/1050] R2[205/600], Temp: 0.7386, Energy: -27.946825+0.001179j
[2025-08-25 21:11:59] [Iter 657/1050] R2[206/600], Temp: 0.7363, Energy: -27.943065-0.001531j
[2025-08-25 21:12:05] [Iter 658/1050] R2[207/600], Temp: 0.7340, Energy: -27.954767-0.001132j
[2025-08-25 21:12:10] [Iter 659/1050] R2[208/600], Temp: 0.7316, Energy: -27.927774-0.001195j
[2025-08-25 21:12:16] [Iter 660/1050] R2[209/600], Temp: 0.7293, Energy: -27.949855+0.003679j
[2025-08-25 21:12:22] [Iter 661/1050] R2[210/600], Temp: 0.7270, Energy: -27.938410+0.003153j
[2025-08-25 21:12:28] [Iter 662/1050] R2[211/600], Temp: 0.7247, Energy: -27.950928+0.002397j
[2025-08-25 21:12:34] [Iter 663/1050] R2[212/600], Temp: 0.7223, Energy: -27.941949+0.001582j
[2025-08-25 21:12:39] [Iter 664/1050] R2[213/600], Temp: 0.7200, Energy: -27.932983+0.001859j
[2025-08-25 21:12:45] [Iter 665/1050] R2[214/600], Temp: 0.7176, Energy: -27.946417+0.002321j
[2025-08-25 21:12:51] [Iter 666/1050] R2[215/600], Temp: 0.7153, Energy: -27.938414-0.004300j
[2025-08-25 21:12:57] [Iter 667/1050] R2[216/600], Temp: 0.7129, Energy: -27.945639+0.000734j
[2025-08-25 21:13:03] [Iter 668/1050] R2[217/600], Temp: 0.7105, Energy: -27.942597+0.002309j
[2025-08-25 21:13:08] [Iter 669/1050] R2[218/600], Temp: 0.7081, Energy: -27.934275-0.004323j
[2025-08-25 21:13:14] [Iter 670/1050] R2[219/600], Temp: 0.7058, Energy: -27.934871-0.000617j
[2025-08-25 21:13:20] [Iter 671/1050] R2[220/600], Temp: 0.7034, Energy: -27.951039-0.001530j
[2025-08-25 21:13:26] [Iter 672/1050] R2[221/600], Temp: 0.7010, Energy: -27.950161+0.000964j
[2025-08-25 21:13:32] [Iter 673/1050] R2[222/600], Temp: 0.6986, Energy: -27.949931-0.001415j
[2025-08-25 21:13:37] [Iter 674/1050] R2[223/600], Temp: 0.6962, Energy: -27.933627+0.000424j
[2025-08-25 21:13:43] [Iter 675/1050] R2[224/600], Temp: 0.6938, Energy: -27.948789-0.000843j
[2025-08-25 21:13:49] [Iter 676/1050] R2[225/600], Temp: 0.6913, Energy: -27.943444-0.000779j
[2025-08-25 21:13:55] [Iter 677/1050] R2[226/600], Temp: 0.6889, Energy: -27.930662-0.000546j
[2025-08-25 21:14:01] [Iter 678/1050] R2[227/600], Temp: 0.6865, Energy: -27.941786+0.000243j
[2025-08-25 21:14:06] [Iter 679/1050] R2[228/600], Temp: 0.6841, Energy: -27.945027-0.003676j
[2025-08-25 21:14:12] [Iter 680/1050] R2[229/600], Temp: 0.6816, Energy: -27.932828-0.000911j
[2025-08-25 21:14:18] [Iter 681/1050] R2[230/600], Temp: 0.6792, Energy: -27.947638-0.002558j
[2025-08-25 21:14:24] [Iter 682/1050] R2[231/600], Temp: 0.6767, Energy: -27.944126-0.001106j
[2025-08-25 21:14:29] [Iter 683/1050] R2[232/600], Temp: 0.6743, Energy: -27.954621+0.000196j
[2025-08-25 21:14:35] [Iter 684/1050] R2[233/600], Temp: 0.6718, Energy: -27.938522-0.000209j
[2025-08-25 21:14:41] [Iter 685/1050] R2[234/600], Temp: 0.6694, Energy: -27.948527+0.000445j
[2025-08-25 21:14:47] [Iter 686/1050] R2[235/600], Temp: 0.6669, Energy: -27.938537+0.001087j
[2025-08-25 21:14:53] [Iter 687/1050] R2[236/600], Temp: 0.6644, Energy: -27.938016+0.001829j
[2025-08-25 21:14:58] [Iter 688/1050] R2[237/600], Temp: 0.6620, Energy: -27.951651+0.003548j
[2025-08-25 21:15:04] [Iter 689/1050] R2[238/600], Temp: 0.6595, Energy: -27.940217+0.000237j
[2025-08-25 21:15:10] [Iter 690/1050] R2[239/600], Temp: 0.6570, Energy: -27.935954+0.001438j
[2025-08-25 21:15:16] [Iter 691/1050] R2[240/600], Temp: 0.6545, Energy: -27.946187+0.000927j
[2025-08-25 21:15:21] [Iter 692/1050] R2[241/600], Temp: 0.6520, Energy: -27.938514-0.003471j
[2025-08-25 21:15:27] [Iter 693/1050] R2[242/600], Temp: 0.6495, Energy: -27.939016+0.004164j
[2025-08-25 21:15:33] [Iter 694/1050] R2[243/600], Temp: 0.6470, Energy: -27.940749+0.000696j
[2025-08-25 21:15:39] [Iter 695/1050] R2[244/600], Temp: 0.6445, Energy: -27.941335+0.000892j
[2025-08-25 21:15:45] [Iter 696/1050] R2[245/600], Temp: 0.6420, Energy: -27.955329+0.000783j
[2025-08-25 21:15:50] [Iter 697/1050] R2[246/600], Temp: 0.6395, Energy: -27.938890+0.000634j
[2025-08-25 21:15:56] [Iter 698/1050] R2[247/600], Temp: 0.6370, Energy: -27.948463-0.001668j
[2025-08-25 21:16:02] [Iter 699/1050] R2[248/600], Temp: 0.6345, Energy: -27.940950-0.000777j
[2025-08-25 21:16:08] [Iter 700/1050] R2[249/600], Temp: 0.6319, Energy: -27.952946-0.001964j
[2025-08-25 21:16:08] ✓ Checkpoint saved: checkpoint_iter_000700.pkl
[2025-08-25 21:16:14] [Iter 701/1050] R2[250/600], Temp: 0.6294, Energy: -27.941908+0.005237j
[2025-08-25 21:16:19] [Iter 702/1050] R2[251/600], Temp: 0.6269, Energy: -27.948239-0.002254j
[2025-08-25 21:16:25] [Iter 703/1050] R2[252/600], Temp: 0.6243, Energy: -27.932664+0.002672j
[2025-08-25 21:16:31] [Iter 704/1050] R2[253/600], Temp: 0.6218, Energy: -27.939321-0.002601j
[2025-08-25 21:16:37] [Iter 705/1050] R2[254/600], Temp: 0.6193, Energy: -27.940213+0.002746j
[2025-08-25 21:16:43] [Iter 706/1050] R2[255/600], Temp: 0.6167, Energy: -27.943625+0.001332j
[2025-08-25 21:16:48] [Iter 707/1050] R2[256/600], Temp: 0.6142, Energy: -27.946338+0.002608j
[2025-08-25 21:16:54] [Iter 708/1050] R2[257/600], Temp: 0.6116, Energy: -27.940169+0.000061j
[2025-08-25 21:17:00] [Iter 709/1050] R2[258/600], Temp: 0.6091, Energy: -27.946576+0.003614j
[2025-08-25 21:17:06] [Iter 710/1050] R2[259/600], Temp: 0.6065, Energy: -27.936487-0.003615j
[2025-08-25 21:17:12] [Iter 711/1050] R2[260/600], Temp: 0.6040, Energy: -27.953229+0.003282j
[2025-08-25 21:17:17] [Iter 712/1050] R2[261/600], Temp: 0.6014, Energy: -27.936658-0.003656j
[2025-08-25 21:17:23] [Iter 713/1050] R2[262/600], Temp: 0.5988, Energy: -27.933419+0.004813j
[2025-08-25 21:17:29] [Iter 714/1050] R2[263/600], Temp: 0.5963, Energy: -27.943168+0.002675j
[2025-08-25 21:17:35] [Iter 715/1050] R2[264/600], Temp: 0.5937, Energy: -27.949436+0.002742j
[2025-08-25 21:17:40] [Iter 716/1050] R2[265/600], Temp: 0.5911, Energy: -27.931483+0.001324j
[2025-08-25 21:17:46] [Iter 717/1050] R2[266/600], Temp: 0.5885, Energy: -27.947401-0.000099j
[2025-08-25 21:17:52] [Iter 718/1050] R2[267/600], Temp: 0.5860, Energy: -27.947296+0.001795j
[2025-08-25 21:17:58] [Iter 719/1050] R2[268/600], Temp: 0.5834, Energy: -27.940410+0.002532j
[2025-08-25 21:18:04] [Iter 720/1050] R2[269/600], Temp: 0.5808, Energy: -27.943079-0.000887j
[2025-08-25 21:18:09] [Iter 721/1050] R2[270/600], Temp: 0.5782, Energy: -27.942035-0.001911j
[2025-08-25 21:18:15] [Iter 722/1050] R2[271/600], Temp: 0.5756, Energy: -27.945974-0.000337j
[2025-08-25 21:18:21] [Iter 723/1050] R2[272/600], Temp: 0.5730, Energy: -27.945087-0.000597j
[2025-08-25 21:18:27] [Iter 724/1050] R2[273/600], Temp: 0.5705, Energy: -27.946209-0.000267j
[2025-08-25 21:18:32] [Iter 725/1050] R2[274/600], Temp: 0.5679, Energy: -27.948413-0.001274j
[2025-08-25 21:18:38] [Iter 726/1050] R2[275/600], Temp: 0.5653, Energy: -27.937986-0.000365j
[2025-08-25 21:18:44] [Iter 727/1050] R2[276/600], Temp: 0.5627, Energy: -27.947478+0.000596j
[2025-08-25 21:18:50] [Iter 728/1050] R2[277/600], Temp: 0.5601, Energy: -27.946198+0.000946j
[2025-08-25 21:18:56] [Iter 729/1050] R2[278/600], Temp: 0.5575, Energy: -27.933413+0.007974j
[2025-08-25 21:19:01] [Iter 730/1050] R2[279/600], Temp: 0.5549, Energy: -27.936360+0.005693j
[2025-08-25 21:19:07] [Iter 731/1050] R2[280/600], Temp: 0.5523, Energy: -27.940830-0.000524j
[2025-08-25 21:19:13] [Iter 732/1050] R2[281/600], Temp: 0.5497, Energy: -27.934646-0.001280j
[2025-08-25 21:19:19] [Iter 733/1050] R2[282/600], Temp: 0.5471, Energy: -27.952575-0.002136j
[2025-08-25 21:19:25] [Iter 734/1050] R2[283/600], Temp: 0.5444, Energy: -27.928351+0.002494j
[2025-08-25 21:19:30] [Iter 735/1050] R2[284/600], Temp: 0.5418, Energy: -27.953302+0.000423j
[2025-08-25 21:19:36] [Iter 736/1050] R2[285/600], Temp: 0.5392, Energy: -27.945771+0.000321j
[2025-08-25 21:19:42] [Iter 737/1050] R2[286/600], Temp: 0.5366, Energy: -27.949282+0.000795j
[2025-08-25 21:19:48] [Iter 738/1050] R2[287/600], Temp: 0.5340, Energy: -27.953246+0.003262j
[2025-08-25 21:19:53] [Iter 739/1050] R2[288/600], Temp: 0.5314, Energy: -27.936847-0.001220j
[2025-08-25 21:19:59] [Iter 740/1050] R2[289/600], Temp: 0.5288, Energy: -27.949790-0.001343j
[2025-08-25 21:20:05] [Iter 741/1050] R2[290/600], Temp: 0.5262, Energy: -27.950800+0.001995j
[2025-08-25 21:20:11] [Iter 742/1050] R2[291/600], Temp: 0.5236, Energy: -27.936561+0.002283j
[2025-08-25 21:20:17] [Iter 743/1050] R2[292/600], Temp: 0.5209, Energy: -27.933861+0.002545j
[2025-08-25 21:20:22] [Iter 744/1050] R2[293/600], Temp: 0.5183, Energy: -27.929639+0.000988j
[2025-08-25 21:20:28] [Iter 745/1050] R2[294/600], Temp: 0.5157, Energy: -27.941567+0.003724j
[2025-08-25 21:20:34] [Iter 746/1050] R2[295/600], Temp: 0.5131, Energy: -27.936860+0.003149j
[2025-08-25 21:20:40] [Iter 747/1050] R2[296/600], Temp: 0.5105, Energy: -27.942890-0.001108j
[2025-08-25 21:20:45] [Iter 748/1050] R2[297/600], Temp: 0.5079, Energy: -27.951765+0.001803j
[2025-08-25 21:20:51] [Iter 749/1050] R2[298/600], Temp: 0.5052, Energy: -27.943067-0.000589j
[2025-08-25 21:20:57] [Iter 750/1050] R2[299/600], Temp: 0.5026, Energy: -27.942254+0.002602j
[2025-08-25 21:21:03] [Iter 751/1050] R2[300/600], Temp: 0.5000, Energy: -27.943893-0.001937j
[2025-08-25 21:21:09] [Iter 752/1050] R2[301/600], Temp: 0.4974, Energy: -27.937526-0.000432j
[2025-08-25 21:21:14] [Iter 753/1050] R2[302/600], Temp: 0.4948, Energy: -27.925209-0.000579j
[2025-08-25 21:21:20] [Iter 754/1050] R2[303/600], Temp: 0.4921, Energy: -27.940130-0.002628j
[2025-08-25 21:21:26] [Iter 755/1050] R2[304/600], Temp: 0.4895, Energy: -27.932250-0.001887j
[2025-08-25 21:21:32] [Iter 756/1050] R2[305/600], Temp: 0.4869, Energy: -27.946277+0.000500j
[2025-08-25 21:21:38] [Iter 757/1050] R2[306/600], Temp: 0.4843, Energy: -27.949017-0.002795j
[2025-08-25 21:21:43] [Iter 758/1050] R2[307/600], Temp: 0.4817, Energy: -27.943304+0.001318j
[2025-08-25 21:21:49] [Iter 759/1050] R2[308/600], Temp: 0.4791, Energy: -27.944587-0.000975j
[2025-08-25 21:21:55] [Iter 760/1050] R2[309/600], Temp: 0.4764, Energy: -27.943426-0.004202j
[2025-08-25 21:22:01] [Iter 761/1050] R2[310/600], Temp: 0.4738, Energy: -27.945243+0.000474j
[2025-08-25 21:22:06] [Iter 762/1050] R2[311/600], Temp: 0.4712, Energy: -27.941517+0.001447j
[2025-08-25 21:22:12] [Iter 763/1050] R2[312/600], Temp: 0.4686, Energy: -27.936347+0.001567j
[2025-08-25 21:22:18] [Iter 764/1050] R2[313/600], Temp: 0.4660, Energy: -27.935891+0.004466j
[2025-08-25 21:22:24] [Iter 765/1050] R2[314/600], Temp: 0.4634, Energy: -27.941991-0.002424j
[2025-08-25 21:22:30] [Iter 766/1050] R2[315/600], Temp: 0.4608, Energy: -27.947835+0.003127j
[2025-08-25 21:22:35] [Iter 767/1050] R2[316/600], Temp: 0.4582, Energy: -27.942250-0.001053j
[2025-08-25 21:22:41] [Iter 768/1050] R2[317/600], Temp: 0.4556, Energy: -27.938714-0.000293j
[2025-08-25 21:22:47] [Iter 769/1050] R2[318/600], Temp: 0.4529, Energy: -27.939830-0.006842j
[2025-08-25 21:22:53] [Iter 770/1050] R2[319/600], Temp: 0.4503, Energy: -27.944777+0.001197j
[2025-08-25 21:22:58] [Iter 771/1050] R2[320/600], Temp: 0.4477, Energy: -27.932966-0.002025j
[2025-08-25 21:23:04] [Iter 772/1050] R2[321/600], Temp: 0.4451, Energy: -27.939459+0.001412j
[2025-08-25 21:23:10] [Iter 773/1050] R2[322/600], Temp: 0.4425, Energy: -27.937769-0.001318j
[2025-08-25 21:23:16] [Iter 774/1050] R2[323/600], Temp: 0.4399, Energy: -27.938573-0.000065j
[2025-08-25 21:23:22] [Iter 775/1050] R2[324/600], Temp: 0.4373, Energy: -27.949918+0.002467j
[2025-08-25 21:23:27] [Iter 776/1050] R2[325/600], Temp: 0.4347, Energy: -27.940159+0.000262j
[2025-08-25 21:23:33] [Iter 777/1050] R2[326/600], Temp: 0.4321, Energy: -27.947543+0.002621j
[2025-08-25 21:23:39] [Iter 778/1050] R2[327/600], Temp: 0.4295, Energy: -27.940133-0.000708j
[2025-08-25 21:23:45] [Iter 779/1050] R2[328/600], Temp: 0.4270, Energy: -27.936200+0.002162j
[2025-08-25 21:23:50] [Iter 780/1050] R2[329/600], Temp: 0.4244, Energy: -27.943459+0.001008j
[2025-08-25 21:23:56] [Iter 781/1050] R2[330/600], Temp: 0.4218, Energy: -27.938880+0.000250j
[2025-08-25 21:24:02] [Iter 782/1050] R2[331/600], Temp: 0.4192, Energy: -27.944422+0.000594j
[2025-08-25 21:24:08] [Iter 783/1050] R2[332/600], Temp: 0.4166, Energy: -27.943972+0.000189j
[2025-08-25 21:24:14] [Iter 784/1050] R2[333/600], Temp: 0.4140, Energy: -27.955137-0.001656j
[2025-08-25 21:24:19] [Iter 785/1050] R2[334/600], Temp: 0.4115, Energy: -27.931455-0.011517j
[2025-08-25 21:24:25] [Iter 786/1050] R2[335/600], Temp: 0.4089, Energy: -27.936777-0.001546j
[2025-08-25 21:24:31] [Iter 787/1050] R2[336/600], Temp: 0.4063, Energy: -27.941209-0.001027j
[2025-08-25 21:24:37] [Iter 788/1050] R2[337/600], Temp: 0.4037, Energy: -27.937493+0.003498j
[2025-08-25 21:24:42] [Iter 789/1050] R2[338/600], Temp: 0.4012, Energy: -27.940318-0.002920j
[2025-08-25 21:24:48] [Iter 790/1050] R2[339/600], Temp: 0.3986, Energy: -27.950243+0.000921j
[2025-08-25 21:24:54] [Iter 791/1050] R2[340/600], Temp: 0.3960, Energy: -27.938888-0.000165j
[2025-08-25 21:25:00] [Iter 792/1050] R2[341/600], Temp: 0.3935, Energy: -27.954662+0.002820j
[2025-08-25 21:25:06] [Iter 793/1050] R2[342/600], Temp: 0.3909, Energy: -27.945807+0.000881j
[2025-08-25 21:25:11] [Iter 794/1050] R2[343/600], Temp: 0.3884, Energy: -27.942651-0.002573j
[2025-08-25 21:25:17] [Iter 795/1050] R2[344/600], Temp: 0.3858, Energy: -27.941263-0.002566j
[2025-08-25 21:25:23] [Iter 796/1050] R2[345/600], Temp: 0.3833, Energy: -27.938272-0.000676j
[2025-08-25 21:25:29] [Iter 797/1050] R2[346/600], Temp: 0.3807, Energy: -27.955039+0.005421j
[2025-08-25 21:25:35] [Iter 798/1050] R2[347/600], Temp: 0.3782, Energy: -27.936928-0.003512j
[2025-08-25 21:25:40] [Iter 799/1050] R2[348/600], Temp: 0.3757, Energy: -27.948404+0.002783j
[2025-08-25 21:25:46] [Iter 800/1050] R2[349/600], Temp: 0.3731, Energy: -27.933865+0.001626j
[2025-08-25 21:25:46] ✓ Checkpoint saved: checkpoint_iter_000800.pkl
[2025-08-25 21:25:52] [Iter 801/1050] R2[350/600], Temp: 0.3706, Energy: -27.939950-0.000216j
[2025-08-25 21:25:58] [Iter 802/1050] R2[351/600], Temp: 0.3681, Energy: -27.946385-0.001116j
[2025-08-25 21:26:04] [Iter 803/1050] R2[352/600], Temp: 0.3655, Energy: -27.949579-0.000112j
[2025-08-25 21:26:09] [Iter 804/1050] R2[353/600], Temp: 0.3630, Energy: -27.950490+0.000265j
[2025-08-25 21:26:15] [Iter 805/1050] R2[354/600], Temp: 0.3605, Energy: -27.944598-0.005764j
[2025-08-25 21:26:21] [Iter 806/1050] R2[355/600], Temp: 0.3580, Energy: -27.949065-0.001899j
[2025-08-25 21:26:27] [Iter 807/1050] R2[356/600], Temp: 0.3555, Energy: -27.942533-0.000854j
[2025-08-25 21:26:33] [Iter 808/1050] R2[357/600], Temp: 0.3530, Energy: -27.942291-0.000653j
[2025-08-25 21:26:38] [Iter 809/1050] R2[358/600], Temp: 0.3505, Energy: -27.949879-0.000650j
[2025-08-25 21:26:44] [Iter 810/1050] R2[359/600], Temp: 0.3480, Energy: -27.951765+0.003569j
[2025-08-25 21:26:50] [Iter 811/1050] R2[360/600], Temp: 0.3455, Energy: -27.945228+0.001520j
[2025-08-25 21:26:56] [Iter 812/1050] R2[361/600], Temp: 0.3430, Energy: -27.948550+0.001693j
[2025-08-25 21:27:01] [Iter 813/1050] R2[362/600], Temp: 0.3405, Energy: -27.933757-0.001776j
[2025-08-25 21:27:07] [Iter 814/1050] R2[363/600], Temp: 0.3380, Energy: -27.937105-0.001113j
[2025-08-25 21:27:13] [Iter 815/1050] R2[364/600], Temp: 0.3356, Energy: -27.943435+0.000445j
[2025-08-25 21:27:19] [Iter 816/1050] R2[365/600], Temp: 0.3331, Energy: -27.948829-0.005361j
[2025-08-25 21:27:25] [Iter 817/1050] R2[366/600], Temp: 0.3306, Energy: -27.941947-0.006168j
[2025-08-25 21:27:31] [Iter 818/1050] R2[367/600], Temp: 0.3282, Energy: -27.955776+0.002074j
[2025-08-25 21:27:36] [Iter 819/1050] R2[368/600], Temp: 0.3257, Energy: -27.938448+0.000583j
[2025-08-25 21:27:42] [Iter 820/1050] R2[369/600], Temp: 0.3233, Energy: -27.949029-0.001141j
[2025-08-25 21:27:48] [Iter 821/1050] R2[370/600], Temp: 0.3208, Energy: -27.941993-0.000976j
[2025-08-25 21:27:54] [Iter 822/1050] R2[371/600], Temp: 0.3184, Energy: -27.947457+0.003547j
[2025-08-25 21:28:00] [Iter 823/1050] R2[372/600], Temp: 0.3159, Energy: -27.942268-0.000273j
[2025-08-25 21:28:05] [Iter 824/1050] R2[373/600], Temp: 0.3135, Energy: -27.946916-0.000366j
[2025-08-25 21:28:11] [Iter 825/1050] R2[374/600], Temp: 0.3111, Energy: -27.943343-0.000431j
[2025-08-25 21:28:17] [Iter 826/1050] R2[375/600], Temp: 0.3087, Energy: -27.944788-0.000770j
[2025-08-25 21:28:23] [Iter 827/1050] R2[376/600], Temp: 0.3062, Energy: -27.947161-0.001189j
[2025-08-25 21:28:29] [Iter 828/1050] R2[377/600], Temp: 0.3038, Energy: -27.945675+0.001619j
[2025-08-25 21:28:34] [Iter 829/1050] R2[378/600], Temp: 0.3014, Energy: -27.931834+0.003723j
[2025-08-25 21:28:40] [Iter 830/1050] R2[379/600], Temp: 0.2990, Energy: -27.942759+0.002583j
[2025-08-25 21:28:46] [Iter 831/1050] R2[380/600], Temp: 0.2966, Energy: -27.950733-0.000609j
[2025-08-25 21:28:52] [Iter 832/1050] R2[381/600], Temp: 0.2942, Energy: -27.946795-0.001865j
[2025-08-25 21:28:58] [Iter 833/1050] R2[382/600], Temp: 0.2919, Energy: -27.945026-0.002200j
[2025-08-25 21:29:03] [Iter 834/1050] R2[383/600], Temp: 0.2895, Energy: -27.940642-0.000385j
[2025-08-25 21:29:09] [Iter 835/1050] R2[384/600], Temp: 0.2871, Energy: -27.943722-0.000627j
[2025-08-25 21:29:15] [Iter 836/1050] R2[385/600], Temp: 0.2847, Energy: -27.954426+0.002951j
[2025-08-25 21:29:21] [Iter 837/1050] R2[386/600], Temp: 0.2824, Energy: -27.936752-0.001336j
[2025-08-25 21:29:27] [Iter 838/1050] R2[387/600], Temp: 0.2800, Energy: -27.953956-0.002218j
[2025-08-25 21:29:32] [Iter 839/1050] R2[388/600], Temp: 0.2777, Energy: -27.932011-0.000878j
[2025-08-25 21:29:38] [Iter 840/1050] R2[389/600], Temp: 0.2753, Energy: -27.950368-0.000487j
[2025-08-25 21:29:44] [Iter 841/1050] R2[390/600], Temp: 0.2730, Energy: -27.939937-0.005521j
[2025-08-25 21:29:50] [Iter 842/1050] R2[391/600], Temp: 0.2707, Energy: -27.939702+0.001799j
[2025-08-25 21:29:56] [Iter 843/1050] R2[392/600], Temp: 0.2684, Energy: -27.939434-0.001092j
[2025-08-25 21:30:01] [Iter 844/1050] R2[393/600], Temp: 0.2660, Energy: -27.951694-0.001527j
[2025-08-25 21:30:07] [Iter 845/1050] R2[394/600], Temp: 0.2637, Energy: -27.939895+0.002819j
[2025-08-25 21:30:13] [Iter 846/1050] R2[395/600], Temp: 0.2614, Energy: -27.937212-0.003891j
[2025-08-25 21:30:19] [Iter 847/1050] R2[396/600], Temp: 0.2591, Energy: -27.940297-0.002577j
[2025-08-25 21:30:25] [Iter 848/1050] R2[397/600], Temp: 0.2568, Energy: -27.940884-0.000531j
[2025-08-25 21:30:30] [Iter 849/1050] R2[398/600], Temp: 0.2545, Energy: -27.941700-0.000439j
[2025-08-25 21:30:36] [Iter 850/1050] R2[399/600], Temp: 0.2523, Energy: -27.944029+0.003173j
[2025-08-25 21:30:42] [Iter 851/1050] R2[400/600], Temp: 0.2500, Energy: -27.945756+0.001136j
[2025-08-25 21:30:48] [Iter 852/1050] R2[401/600], Temp: 0.2477, Energy: -27.945865-0.000043j
[2025-08-25 21:30:54] [Iter 853/1050] R2[402/600], Temp: 0.2455, Energy: -27.946135+0.003488j
[2025-08-25 21:30:59] [Iter 854/1050] R2[403/600], Temp: 0.2432, Energy: -27.944863+0.001795j
[2025-08-25 21:31:05] [Iter 855/1050] R2[404/600], Temp: 0.2410, Energy: -27.950368+0.000196j
[2025-08-25 21:31:11] [Iter 856/1050] R2[405/600], Temp: 0.2388, Energy: -27.947206+0.002804j
[2025-08-25 21:31:17] [Iter 857/1050] R2[406/600], Temp: 0.2365, Energy: -27.948332+0.001793j
[2025-08-25 21:31:23] [Iter 858/1050] R2[407/600], Temp: 0.2343, Energy: -27.943488+0.000108j
[2025-08-25 21:31:28] [Iter 859/1050] R2[408/600], Temp: 0.2321, Energy: -27.942795+0.000168j
[2025-08-25 21:31:34] [Iter 860/1050] R2[409/600], Temp: 0.2299, Energy: -27.938889-0.001360j
[2025-08-25 21:31:40] [Iter 861/1050] R2[410/600], Temp: 0.2277, Energy: -27.945113-0.001095j
[2025-08-25 21:31:46] [Iter 862/1050] R2[411/600], Temp: 0.2255, Energy: -27.942752+0.001063j
[2025-08-25 21:31:52] [Iter 863/1050] R2[412/600], Temp: 0.2233, Energy: -27.947435-0.002218j
[2025-08-25 21:31:57] [Iter 864/1050] R2[413/600], Temp: 0.2211, Energy: -27.950319-0.004054j
[2025-08-25 21:32:03] [Iter 865/1050] R2[414/600], Temp: 0.2190, Energy: -27.944415-0.004152j
[2025-08-25 21:32:09] [Iter 866/1050] R2[415/600], Temp: 0.2168, Energy: -27.935282-0.001027j
[2025-08-25 21:32:15] [Iter 867/1050] R2[416/600], Temp: 0.2146, Energy: -27.940977+0.000782j
[2025-08-25 21:32:21] [Iter 868/1050] R2[417/600], Temp: 0.2125, Energy: -27.941609+0.002344j
[2025-08-25 21:32:26] [Iter 869/1050] R2[418/600], Temp: 0.2104, Energy: -27.946884-0.000992j
[2025-08-25 21:32:32] [Iter 870/1050] R2[419/600], Temp: 0.2082, Energy: -27.938329+0.003634j
[2025-08-25 21:32:38] [Iter 871/1050] R2[420/600], Temp: 0.2061, Energy: -27.945120-0.001955j
[2025-08-25 21:32:44] [Iter 872/1050] R2[421/600], Temp: 0.2040, Energy: -27.944722-0.000320j
[2025-08-25 21:32:50] [Iter 873/1050] R2[422/600], Temp: 0.2019, Energy: -27.945055-0.004146j
[2025-08-25 21:32:55] [Iter 874/1050] R2[423/600], Temp: 0.1998, Energy: -27.949517+0.001380j
[2025-08-25 21:33:01] [Iter 875/1050] R2[424/600], Temp: 0.1977, Energy: -27.939134+0.002532j
[2025-08-25 21:33:07] [Iter 876/1050] R2[425/600], Temp: 0.1956, Energy: -27.938150-0.004920j
[2025-08-25 21:33:13] [Iter 877/1050] R2[426/600], Temp: 0.1935, Energy: -27.949501+0.003467j
[2025-08-25 21:33:19] [Iter 878/1050] R2[427/600], Temp: 0.1915, Energy: -27.944013+0.001552j
[2025-08-25 21:33:24] [Iter 879/1050] R2[428/600], Temp: 0.1894, Energy: -27.947122-0.001716j
[2025-08-25 21:33:30] [Iter 880/1050] R2[429/600], Temp: 0.1874, Energy: -27.938306-0.002365j
[2025-08-25 21:33:36] [Iter 881/1050] R2[430/600], Temp: 0.1853, Energy: -27.944777+0.000787j
[2025-08-25 21:33:42] [Iter 882/1050] R2[431/600], Temp: 0.1833, Energy: -27.938708+0.001730j
[2025-08-25 21:33:48] [Iter 883/1050] R2[432/600], Temp: 0.1813, Energy: -27.944169+0.000292j
[2025-08-25 21:33:53] [Iter 884/1050] R2[433/600], Temp: 0.1793, Energy: -27.947279+0.003282j
[2025-08-25 21:33:59] [Iter 885/1050] R2[434/600], Temp: 0.1773, Energy: -27.941591-0.001629j
[2025-08-25 21:34:05] [Iter 886/1050] R2[435/600], Temp: 0.1753, Energy: -27.943974-0.002804j
[2025-08-25 21:34:11] [Iter 887/1050] R2[436/600], Temp: 0.1733, Energy: -27.943670-0.001345j
[2025-08-25 21:34:17] [Iter 888/1050] R2[437/600], Temp: 0.1713, Energy: -27.939894+0.002680j
[2025-08-25 21:34:22] [Iter 889/1050] R2[438/600], Temp: 0.1693, Energy: -27.932598-0.004401j
[2025-08-25 21:34:28] [Iter 890/1050] R2[439/600], Temp: 0.1674, Energy: -27.934647+0.000883j
[2025-08-25 21:34:34] [Iter 891/1050] R2[440/600], Temp: 0.1654, Energy: -27.943774-0.000881j
[2025-08-25 21:34:40] [Iter 892/1050] R2[441/600], Temp: 0.1635, Energy: -27.934855-0.000622j
[2025-08-25 21:34:46] [Iter 893/1050] R2[442/600], Temp: 0.1616, Energy: -27.946461+0.002683j
[2025-08-25 21:34:51] [Iter 894/1050] R2[443/600], Temp: 0.1596, Energy: -27.942645+0.002690j
[2025-08-25 21:34:57] [Iter 895/1050] R2[444/600], Temp: 0.1577, Energy: -27.939510+0.000119j
[2025-08-25 21:35:03] [Iter 896/1050] R2[445/600], Temp: 0.1558, Energy: -27.936716+0.000475j
[2025-08-25 21:35:09] [Iter 897/1050] R2[446/600], Temp: 0.1539, Energy: -27.940241-0.004108j
[2025-08-25 21:35:15] [Iter 898/1050] R2[447/600], Temp: 0.1520, Energy: -27.937243+0.000413j
[2025-08-25 21:35:20] [Iter 899/1050] R2[448/600], Temp: 0.1502, Energy: -27.945721+0.001957j
[2025-08-25 21:35:26] [Iter 900/1050] R2[449/600], Temp: 0.1483, Energy: -27.938932-0.002996j
[2025-08-25 21:35:26] ✓ Checkpoint saved: checkpoint_iter_000900.pkl
[2025-08-25 21:35:32] [Iter 901/1050] R2[450/600], Temp: 0.1464, Energy: -27.944739-0.000510j
[2025-08-25 21:35:38] [Iter 902/1050] R2[451/600], Temp: 0.1446, Energy: -27.944313-0.002984j
[2025-08-25 21:35:44] [Iter 903/1050] R2[452/600], Temp: 0.1428, Energy: -27.947861+0.001028j
[2025-08-25 21:35:49] [Iter 904/1050] R2[453/600], Temp: 0.1409, Energy: -27.937182+0.001541j
[2025-08-25 21:35:55] [Iter 905/1050] R2[454/600], Temp: 0.1391, Energy: -27.943554+0.000064j
[2025-08-25 21:36:01] [Iter 906/1050] R2[455/600], Temp: 0.1373, Energy: -27.931975+0.000167j
[2025-08-25 21:36:07] [Iter 907/1050] R2[456/600], Temp: 0.1355, Energy: -27.940569+0.001467j
[2025-08-25 21:36:13] [Iter 908/1050] R2[457/600], Temp: 0.1337, Energy: -27.947119+0.002071j
[2025-08-25 21:36:18] [Iter 909/1050] R2[458/600], Temp: 0.1320, Energy: -27.936134+0.000701j
[2025-08-25 21:36:24] [Iter 910/1050] R2[459/600], Temp: 0.1302, Energy: -27.955325-0.001783j
[2025-08-25 21:36:30] [Iter 911/1050] R2[460/600], Temp: 0.1284, Energy: -27.940503-0.007189j
[2025-08-25 21:36:36] [Iter 912/1050] R2[461/600], Temp: 0.1267, Energy: -27.950527+0.003316j
[2025-08-25 21:36:42] [Iter 913/1050] R2[462/600], Temp: 0.1249, Energy: -27.945418-0.000625j
[2025-08-25 21:36:47] [Iter 914/1050] R2[463/600], Temp: 0.1232, Energy: -27.945304+0.003897j
[2025-08-25 21:36:53] [Iter 915/1050] R2[464/600], Temp: 0.1215, Energy: -27.944211+0.006729j
[2025-08-25 21:36:59] [Iter 916/1050] R2[465/600], Temp: 0.1198, Energy: -27.932460-0.002269j
[2025-08-25 21:37:05] [Iter 917/1050] R2[466/600], Temp: 0.1181, Energy: -27.940695-0.004853j
[2025-08-25 21:37:11] [Iter 918/1050] R2[467/600], Temp: 0.1164, Energy: -27.949818+0.005607j
[2025-08-25 21:37:16] [Iter 919/1050] R2[468/600], Temp: 0.1147, Energy: -27.944815+0.001018j
[2025-08-25 21:37:22] [Iter 920/1050] R2[469/600], Temp: 0.1131, Energy: -27.940452+0.004511j
[2025-08-25 21:37:28] [Iter 921/1050] R2[470/600], Temp: 0.1114, Energy: -27.944995+0.005185j
[2025-08-25 21:37:34] [Iter 922/1050] R2[471/600], Temp: 0.1098, Energy: -27.948852+0.005588j
[2025-08-25 21:37:40] [Iter 923/1050] R2[472/600], Temp: 0.1082, Energy: -27.945059+0.000990j
[2025-08-25 21:37:45] [Iter 924/1050] R2[473/600], Temp: 0.1065, Energy: -27.947992-0.004130j
[2025-08-25 21:37:51] [Iter 925/1050] R2[474/600], Temp: 0.1049, Energy: -27.935943+0.001467j
[2025-08-25 21:37:57] [Iter 926/1050] R2[475/600], Temp: 0.1033, Energy: -27.945461+0.003352j
[2025-08-25 21:38:03] [Iter 927/1050] R2[476/600], Temp: 0.1017, Energy: -27.952029-0.003536j
[2025-08-25 21:38:09] [Iter 928/1050] R2[477/600], Temp: 0.1002, Energy: -27.945568+0.000581j
[2025-08-25 21:38:15] [Iter 929/1050] R2[478/600], Temp: 0.0986, Energy: -27.935247-0.002601j
[2025-08-25 21:38:20] [Iter 930/1050] R2[479/600], Temp: 0.0970, Energy: -27.946772+0.003230j
[2025-08-25 21:38:26] [Iter 931/1050] R2[480/600], Temp: 0.0955, Energy: -27.943978-0.002771j
[2025-08-25 21:38:32] [Iter 932/1050] R2[481/600], Temp: 0.0940, Energy: -27.938370-0.000316j
[2025-08-25 21:38:38] [Iter 933/1050] R2[482/600], Temp: 0.0924, Energy: -27.935261-0.000593j
[2025-08-25 21:38:43] [Iter 934/1050] R2[483/600], Temp: 0.0909, Energy: -27.942035+0.001821j
[2025-08-25 21:38:49] [Iter 935/1050] R2[484/600], Temp: 0.0894, Energy: -27.941048+0.000145j
[2025-08-25 21:38:55] [Iter 936/1050] R2[485/600], Temp: 0.0879, Energy: -27.934350+0.001304j
[2025-08-25 21:39:01] [Iter 937/1050] R2[486/600], Temp: 0.0865, Energy: -27.946428-0.002033j
[2025-08-25 21:39:07] [Iter 938/1050] R2[487/600], Temp: 0.0850, Energy: -27.945603+0.004049j
[2025-08-25 21:39:13] [Iter 939/1050] R2[488/600], Temp: 0.0835, Energy: -27.936289-0.002244j
[2025-08-25 21:39:18] [Iter 940/1050] R2[489/600], Temp: 0.0821, Energy: -27.944425-0.000840j
[2025-08-25 21:39:24] [Iter 941/1050] R2[490/600], Temp: 0.0807, Energy: -27.949313-0.001058j
[2025-08-25 21:39:30] [Iter 942/1050] R2[491/600], Temp: 0.0792, Energy: -27.943376-0.002016j
[2025-08-25 21:39:36] [Iter 943/1050] R2[492/600], Temp: 0.0778, Energy: -27.947452+0.000502j
[2025-08-25 21:39:42] [Iter 944/1050] R2[493/600], Temp: 0.0764, Energy: -27.945038+0.002053j
[2025-08-25 21:39:47] [Iter 945/1050] R2[494/600], Temp: 0.0751, Energy: -27.940385-0.000521j
[2025-08-25 21:39:53] [Iter 946/1050] R2[495/600], Temp: 0.0737, Energy: -27.941629-0.001550j
[2025-08-25 21:39:59] [Iter 947/1050] R2[496/600], Temp: 0.0723, Energy: -27.954131+0.002208j
[2025-08-25 21:40:05] [Iter 948/1050] R2[497/600], Temp: 0.0710, Energy: -27.939096+0.001175j
[2025-08-25 21:40:10] [Iter 949/1050] R2[498/600], Temp: 0.0696, Energy: -27.941557+0.001009j
[2025-08-25 21:40:16] [Iter 950/1050] R2[499/600], Temp: 0.0683, Energy: -27.945195+0.002425j
[2025-08-25 21:40:22] [Iter 951/1050] R2[500/600], Temp: 0.0670, Energy: -27.944416+0.004167j
[2025-08-25 21:40:28] [Iter 952/1050] R2[501/600], Temp: 0.0657, Energy: -27.950216+0.003385j
[2025-08-25 21:40:34] [Iter 953/1050] R2[502/600], Temp: 0.0644, Energy: -27.945268+0.000132j
[2025-08-25 21:40:39] [Iter 954/1050] R2[503/600], Temp: 0.0631, Energy: -27.936137+0.002213j
[2025-08-25 21:40:45] [Iter 955/1050] R2[504/600], Temp: 0.0618, Energy: -27.933987+0.001939j
[2025-08-25 21:40:51] [Iter 956/1050] R2[505/600], Temp: 0.0606, Energy: -27.936661+0.000856j
[2025-08-25 21:40:57] [Iter 957/1050] R2[506/600], Temp: 0.0593, Energy: -27.943438-0.001503j
[2025-08-25 21:41:03] [Iter 958/1050] R2[507/600], Temp: 0.0581, Energy: -27.957091-0.001741j
[2025-08-25 21:41:08] [Iter 959/1050] R2[508/600], Temp: 0.0569, Energy: -27.941756+0.002037j
[2025-08-25 21:41:14] [Iter 960/1050] R2[509/600], Temp: 0.0557, Energy: -27.936990+0.000574j
[2025-08-25 21:41:20] [Iter 961/1050] R2[510/600], Temp: 0.0545, Energy: -27.936911+0.000958j
[2025-08-25 21:41:26] [Iter 962/1050] R2[511/600], Temp: 0.0533, Energy: -27.946740+0.004589j
[2025-08-25 21:41:32] [Iter 963/1050] R2[512/600], Temp: 0.0521, Energy: -27.941894-0.000912j
[2025-08-25 21:41:37] [Iter 964/1050] R2[513/600], Temp: 0.0510, Energy: -27.956546+0.000663j
[2025-08-25 21:41:43] [Iter 965/1050] R2[514/600], Temp: 0.0498, Energy: -27.944808-0.002640j
[2025-08-25 21:41:49] [Iter 966/1050] R2[515/600], Temp: 0.0487, Energy: -27.952260-0.004639j
[2025-08-25 21:41:55] [Iter 967/1050] R2[516/600], Temp: 0.0476, Energy: -27.943506+0.000140j
[2025-08-25 21:42:01] [Iter 968/1050] R2[517/600], Temp: 0.0465, Energy: -27.947351+0.003522j
[2025-08-25 21:42:06] [Iter 969/1050] R2[518/600], Temp: 0.0454, Energy: -27.935928-0.000523j
[2025-08-25 21:42:12] [Iter 970/1050] R2[519/600], Temp: 0.0443, Energy: -27.937923+0.001357j
[2025-08-25 21:42:18] [Iter 971/1050] R2[520/600], Temp: 0.0432, Energy: -27.943746+0.001170j
[2025-08-25 21:42:24] [Iter 972/1050] R2[521/600], Temp: 0.0422, Energy: -27.944154+0.001493j
[2025-08-25 21:42:30] [Iter 973/1050] R2[522/600], Temp: 0.0411, Energy: -27.940496+0.000516j
[2025-08-25 21:42:35] [Iter 974/1050] R2[523/600], Temp: 0.0401, Energy: -27.938196-0.000708j
[2025-08-25 21:42:41] [Iter 975/1050] R2[524/600], Temp: 0.0391, Energy: -27.947728-0.002240j
[2025-08-25 21:42:47] [Iter 976/1050] R2[525/600], Temp: 0.0381, Energy: -27.941685+0.000320j
[2025-08-25 21:42:53] [Iter 977/1050] R2[526/600], Temp: 0.0371, Energy: -27.946073-0.000059j
[2025-08-25 21:42:59] [Iter 978/1050] R2[527/600], Temp: 0.0361, Energy: -27.931784+0.001163j
[2025-08-25 21:43:04] [Iter 979/1050] R2[528/600], Temp: 0.0351, Energy: -27.944190-0.000312j
[2025-08-25 21:43:10] [Iter 980/1050] R2[529/600], Temp: 0.0342, Energy: -27.941144-0.001968j
[2025-08-25 21:43:16] [Iter 981/1050] R2[530/600], Temp: 0.0332, Energy: -27.940515+0.002574j
[2025-08-25 21:43:22] [Iter 982/1050] R2[531/600], Temp: 0.0323, Energy: -27.935673+0.001297j
[2025-08-25 21:43:28] [Iter 983/1050] R2[532/600], Temp: 0.0314, Energy: -27.939302-0.003020j
[2025-08-25 21:43:33] [Iter 984/1050] R2[533/600], Temp: 0.0305, Energy: -27.947829+0.000965j
[2025-08-25 21:43:39] [Iter 985/1050] R2[534/600], Temp: 0.0296, Energy: -27.945170+0.002621j
[2025-08-25 21:43:45] [Iter 986/1050] R2[535/600], Temp: 0.0287, Energy: -27.939764+0.001634j
[2025-08-25 21:43:51] [Iter 987/1050] R2[536/600], Temp: 0.0278, Energy: -27.932590-0.001165j
[2025-08-25 21:43:57] [Iter 988/1050] R2[537/600], Temp: 0.0270, Energy: -27.954271+0.002497j
[2025-08-25 21:44:02] [Iter 989/1050] R2[538/600], Temp: 0.0261, Energy: -27.936581+0.004932j
[2025-08-25 21:44:08] [Iter 990/1050] R2[539/600], Temp: 0.0253, Energy: -27.946519+0.001057j
[2025-08-25 21:44:14] [Iter 991/1050] R2[540/600], Temp: 0.0245, Energy: -27.952037-0.006272j
[2025-08-25 21:44:20] [Iter 992/1050] R2[541/600], Temp: 0.0237, Energy: -27.930840+0.001448j
[2025-08-25 21:44:25] [Iter 993/1050] R2[542/600], Temp: 0.0229, Energy: -27.946938-0.003370j
[2025-08-25 21:44:31] [Iter 994/1050] R2[543/600], Temp: 0.0221, Energy: -27.936349+0.003237j
[2025-08-25 21:44:37] [Iter 995/1050] R2[544/600], Temp: 0.0213, Energy: -27.935808-0.001075j
[2025-08-25 21:44:43] [Iter 996/1050] R2[545/600], Temp: 0.0206, Energy: -27.951319+0.000238j
[2025-08-25 21:44:49] [Iter 997/1050] R2[546/600], Temp: 0.0199, Energy: -27.935833-0.004289j
[2025-08-25 21:44:54] [Iter 998/1050] R2[547/600], Temp: 0.0191, Energy: -27.930697-0.001597j
[2025-08-25 21:45:00] [Iter 999/1050] R2[548/600], Temp: 0.0184, Energy: -27.947816-0.000352j
[2025-08-25 21:45:06] [Iter 1000/1050] R2[549/600], Temp: 0.0177, Energy: -27.936353-0.001372j
[2025-08-25 21:45:06] ✓ Checkpoint saved: checkpoint_iter_001000.pkl
[2025-08-25 21:45:12] [Iter 1001/1050] R2[550/600], Temp: 0.0170, Energy: -27.944363-0.004975j
[2025-08-25 21:45:18] [Iter 1002/1050] R2[551/600], Temp: 0.0164, Energy: -27.942243-0.004463j
[2025-08-25 21:45:23] [Iter 1003/1050] R2[552/600], Temp: 0.0157, Energy: -27.942078-0.000682j
[2025-08-25 21:45:29] [Iter 1004/1050] R2[553/600], Temp: 0.0151, Energy: -27.935634-0.001023j
[2025-08-25 21:45:35] [Iter 1005/1050] R2[554/600], Temp: 0.0144, Energy: -27.945720-0.001456j
[2025-08-25 21:45:41] [Iter 1006/1050] R2[555/600], Temp: 0.0138, Energy: -27.951877+0.004153j
[2025-08-25 21:45:47] [Iter 1007/1050] R2[556/600], Temp: 0.0132, Energy: -27.940866+0.001584j
[2025-08-25 21:45:52] [Iter 1008/1050] R2[557/600], Temp: 0.0126, Energy: -27.945117+0.000600j
[2025-08-25 21:45:58] [Iter 1009/1050] R2[558/600], Temp: 0.0120, Energy: -27.936295+0.001662j
[2025-08-25 21:46:04] [Iter 1010/1050] R2[559/600], Temp: 0.0115, Energy: -27.942661+0.002565j
[2025-08-25 21:46:10] [Iter 1011/1050] R2[560/600], Temp: 0.0109, Energy: -27.945187-0.005764j
[2025-08-25 21:46:16] [Iter 1012/1050] R2[561/600], Temp: 0.0104, Energy: -27.944294+0.002331j
[2025-08-25 21:46:21] [Iter 1013/1050] R2[562/600], Temp: 0.0099, Energy: -27.945097-0.001708j
[2025-08-25 21:46:27] [Iter 1014/1050] R2[563/600], Temp: 0.0094, Energy: -27.944746-0.001247j
[2025-08-25 21:46:33] [Iter 1015/1050] R2[564/600], Temp: 0.0089, Energy: -27.945558-0.001408j
[2025-08-25 21:46:39] [Iter 1016/1050] R2[565/600], Temp: 0.0084, Energy: -27.938824-0.003568j
[2025-08-25 21:46:45] [Iter 1017/1050] R2[566/600], Temp: 0.0079, Energy: -27.949736-0.001497j
[2025-08-25 21:46:50] [Iter 1018/1050] R2[567/600], Temp: 0.0074, Energy: -27.946468-0.003702j
[2025-08-25 21:46:56] [Iter 1019/1050] R2[568/600], Temp: 0.0070, Energy: -27.928145-0.000089j
[2025-08-25 21:47:02] [Iter 1020/1050] R2[569/600], Temp: 0.0066, Energy: -27.943083+0.002314j
[2025-08-25 21:47:08] [Iter 1021/1050] R2[570/600], Temp: 0.0062, Energy: -27.934777+0.001296j
[2025-08-25 21:47:14] [Iter 1022/1050] R2[571/600], Temp: 0.0058, Energy: -27.943457-0.002701j
[2025-08-25 21:47:19] [Iter 1023/1050] R2[572/600], Temp: 0.0054, Energy: -27.942851-0.001012j
[2025-08-25 21:47:25] [Iter 1024/1050] R2[573/600], Temp: 0.0050, Energy: -27.947568+0.002064j
[2025-08-25 21:47:31] [Iter 1025/1050] R2[574/600], Temp: 0.0046, Energy: -27.942360+0.002282j
[2025-08-25 21:47:37] [Iter 1026/1050] R2[575/600], Temp: 0.0043, Energy: -27.938923+0.001555j
[2025-08-25 21:47:42] [Iter 1027/1050] R2[576/600], Temp: 0.0039, Energy: -27.939322-0.000512j
[2025-08-25 21:47:48] [Iter 1028/1050] R2[577/600], Temp: 0.0036, Energy: -27.944556-0.000016j
[2025-08-25 21:47:54] [Iter 1029/1050] R2[578/600], Temp: 0.0033, Energy: -27.933371-0.002995j
[2025-08-25 21:48:00] [Iter 1030/1050] R2[579/600], Temp: 0.0030, Energy: -27.945393-0.001526j
[2025-08-25 21:48:06] [Iter 1031/1050] R2[580/600], Temp: 0.0027, Energy: -27.947102-0.002007j
[2025-08-25 21:48:11] [Iter 1032/1050] R2[581/600], Temp: 0.0025, Energy: -27.945091-0.000945j
[2025-08-25 21:48:17] [Iter 1033/1050] R2[582/600], Temp: 0.0022, Energy: -27.947039+0.001957j
[2025-08-25 21:48:23] [Iter 1034/1050] R2[583/600], Temp: 0.0020, Energy: -27.939326+0.003043j
[2025-08-25 21:48:29] [Iter 1035/1050] R2[584/600], Temp: 0.0018, Energy: -27.947987-0.005095j
[2025-08-25 21:48:35] [Iter 1036/1050] R2[585/600], Temp: 0.0015, Energy: -27.945539+0.001521j
[2025-08-25 21:48:40] [Iter 1037/1050] R2[586/600], Temp: 0.0013, Energy: -27.936987-0.001798j
[2025-08-25 21:48:46] [Iter 1038/1050] R2[587/600], Temp: 0.0012, Energy: -27.940878+0.001098j
[2025-08-25 21:48:52] [Iter 1039/1050] R2[588/600], Temp: 0.0010, Energy: -27.943621-0.001268j
[2025-08-25 21:48:58] [Iter 1040/1050] R2[589/600], Temp: 0.0008, Energy: -27.937022-0.003888j
[2025-08-25 21:49:04] [Iter 1041/1050] R2[590/600], Temp: 0.0007, Energy: -27.937416-0.002637j
[2025-08-25 21:49:09] [Iter 1042/1050] R2[591/600], Temp: 0.0006, Energy: -27.939188+0.001462j
[2025-08-25 21:49:15] [Iter 1043/1050] R2[592/600], Temp: 0.0004, Energy: -27.945215+0.000973j
[2025-08-25 21:49:21] [Iter 1044/1050] R2[593/600], Temp: 0.0003, Energy: -27.950596+0.001343j
[2025-08-25 21:49:27] [Iter 1045/1050] R2[594/600], Temp: 0.0002, Energy: -27.940523-0.000138j
[2025-08-25 21:49:33] [Iter 1046/1050] R2[595/600], Temp: 0.0002, Energy: -27.946518-0.004322j
[2025-08-25 21:49:38] [Iter 1047/1050] R2[596/600], Temp: 0.0001, Energy: -27.942523+0.001026j
[2025-08-25 21:49:41] [Iter 1048/1050] R2[597/600], Temp: 0.0001, Energy: -27.945371-0.001121j
[2025-08-25 21:49:44] [Iter 1049/1050] R2[598/600], Temp: 0.0000, Energy: -27.945809-0.000792j
[2025-08-25 21:49:46] [Iter 1050/1050] R2[599/600], Temp: 0.0000, Energy: -27.948288+0.003479j
[2025-08-25 21:49:46] ✅ Training completed | Restarts: 2
[2025-08-25 21:49:46] ============================================================
[2025-08-25 21:49:46] Training completed | Runtime: 6119.9s
[2025-08-25 21:49:47] ✓ Final state saved: checkpoints/final_GCNN.pkl
[2025-08-25 21:49:47] ============================================================
