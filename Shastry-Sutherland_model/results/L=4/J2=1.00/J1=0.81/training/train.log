[2025-08-25 18:24:36] ✓ 从checkpoint恢复: results/L=4/J2=1.00/J1=0.80/training/checkpoints/final_GCNN.pkl
[2025-08-25 18:24:36]   - 迭代次数: final
[2025-08-25 18:24:36]   - 能量: -28.760945-0.000622j ± 0.003109
[2025-08-25 18:24:36]   - 时间戳: 2025-08-24T12:59:13.349942+08:00
[2025-08-25 18:24:46] ✓ 变分状态参数已从checkpoint恢复
[2025-08-25 18:24:46] ✓ 从final状态恢复, 重置迭代计数为0
[2025-08-25 18:24:46] ==================================================
[2025-08-25 18:24:46] GCNN for Shastry-Sutherland Model
[2025-08-25 18:24:46] ==================================================
[2025-08-25 18:24:46] System parameters:
[2025-08-25 18:24:46]   - System size: L=4, N=64
[2025-08-25 18:24:46]   - System parameters: J1=0.81, J2=1.0, Q=0.0
[2025-08-25 18:24:46] --------------------------------------------------
[2025-08-25 18:24:46] Model parameters:
[2025-08-25 18:24:46]   - Number of layers = 4
[2025-08-25 18:24:46]   - Number of features = 4
[2025-08-25 18:24:46]   - Total parameters = 12572
[2025-08-25 18:24:46] --------------------------------------------------
[2025-08-25 18:24:46] Training parameters:
[2025-08-25 18:24:46]   - Learning rate: 0.01
[2025-08-25 18:24:46]   - Total iterations: 1050
[2025-08-25 18:24:46]   - Annealing cycles: 3
[2025-08-25 18:24:46]   - Initial period: 150
[2025-08-25 18:24:46]   - Period multiplier: 2.0
[2025-08-25 18:24:46]   - Temperature range: 0.0-1.0
[2025-08-25 18:24:46]   - Samples: 4096
[2025-08-25 18:24:46]   - Discarded samples: 0
[2025-08-25 18:24:46]   - Chunk size: 2048
[2025-08-25 18:24:46]   - Diagonal shift: 0.2
[2025-08-25 18:24:46]   - Gradient clipping: 1.0
[2025-08-25 18:24:46]   - Checkpoint enabled: interval=100
[2025-08-25 18:24:46]   - Checkpoint directory: results/L=4/J2=1.00/J1=0.81/training/checkpoints
[2025-08-25 18:24:46] --------------------------------------------------
[2025-08-25 18:24:46] Device status:
[2025-08-25 18:24:46]   - Devices model: NVIDIA H200 NVL
[2025-08-25 18:24:46]   - Number of devices: 1
[2025-08-25 18:24:46]   - Sharding: True
[2025-08-25 18:24:46] ============================================================
[2025-08-25 18:25:25] [Iter 1/1050] R0[0/150], Temp: 1.0000, Energy: -29.170463-0.001319j
[2025-08-25 18:25:48] [Iter 2/1050] R0[1/150], Temp: 0.9999, Energy: -29.168825-0.000561j
[2025-08-25 18:25:54] [Iter 3/1050] R0[2/150], Temp: 0.9996, Energy: -29.177821-0.003220j
[2025-08-25 18:26:00] [Iter 4/1050] R0[3/150], Temp: 0.9990, Energy: -29.165692-0.002832j
[2025-08-25 18:26:05] [Iter 5/1050] R0[4/150], Temp: 0.9982, Energy: -29.171232-0.001392j
[2025-08-25 18:26:11] [Iter 6/1050] R0[5/150], Temp: 0.9973, Energy: -29.170213+0.004414j
[2025-08-25 18:26:17] [Iter 7/1050] R0[6/150], Temp: 0.9961, Energy: -29.163928-0.000606j
[2025-08-25 18:26:23] [Iter 8/1050] R0[7/150], Temp: 0.9946, Energy: -29.164503-0.000398j
[2025-08-25 18:26:29] [Iter 9/1050] R0[8/150], Temp: 0.9930, Energy: -29.157356-0.001893j
[2025-08-25 18:26:34] [Iter 10/1050] R0[9/150], Temp: 0.9911, Energy: -29.175427+0.003413j
[2025-08-25 18:26:40] [Iter 11/1050] R0[10/150], Temp: 0.9891, Energy: -29.174100+0.001847j
[2025-08-25 18:26:46] [Iter 12/1050] R0[11/150], Temp: 0.9868, Energy: -29.155150-0.002594j
[2025-08-25 18:26:52] [Iter 13/1050] R0[12/150], Temp: 0.9843, Energy: -29.165717+0.000155j
[2025-08-25 18:26:57] [Iter 14/1050] R0[13/150], Temp: 0.9816, Energy: -29.166153+0.000300j
[2025-08-25 18:27:03] [Iter 15/1050] R0[14/150], Temp: 0.9787, Energy: -29.173587-0.005118j
[2025-08-25 18:27:09] [Iter 16/1050] R0[15/150], Temp: 0.9755, Energy: -29.157933+0.001457j
[2025-08-25 18:27:15] [Iter 17/1050] R0[16/150], Temp: 0.9722, Energy: -29.163302+0.000370j
[2025-08-25 18:27:21] [Iter 18/1050] R0[17/150], Temp: 0.9686, Energy: -29.167429-0.003422j
[2025-08-25 18:27:26] [Iter 19/1050] R0[18/150], Temp: 0.9649, Energy: -29.177199+0.002883j
[2025-08-25 18:27:32] [Iter 20/1050] R0[19/150], Temp: 0.9609, Energy: -29.172800+0.001272j
[2025-08-25 18:27:38] [Iter 21/1050] R0[20/150], Temp: 0.9568, Energy: -29.171469+0.000722j
[2025-08-25 18:27:44] [Iter 22/1050] R0[21/150], Temp: 0.9524, Energy: -29.162604-0.001258j
[2025-08-25 18:27:50] [Iter 23/1050] R0[22/150], Temp: 0.9479, Energy: -29.178189+0.002494j
[2025-08-25 18:27:55] [Iter 24/1050] R0[23/150], Temp: 0.9431, Energy: -29.163210-0.005923j
[2025-08-25 18:28:01] [Iter 25/1050] R0[24/150], Temp: 0.9382, Energy: -29.171963+0.000319j
[2025-08-25 18:28:07] [Iter 26/1050] R0[25/150], Temp: 0.9330, Energy: -29.171884-0.000010j
[2025-08-25 18:28:13] [Iter 27/1050] R0[26/150], Temp: 0.9277, Energy: -29.172844+0.001208j
[2025-08-25 18:28:19] [Iter 28/1050] R0[27/150], Temp: 0.9222, Energy: -29.167566-0.002025j
[2025-08-25 18:28:24] [Iter 29/1050] R0[28/150], Temp: 0.9165, Energy: -29.165392+0.002399j
[2025-08-25 18:28:30] [Iter 30/1050] R0[29/150], Temp: 0.9106, Energy: -29.162046-0.002709j
[2025-08-25 18:28:36] [Iter 31/1050] R0[30/150], Temp: 0.9045, Energy: -29.166337-0.000738j
[2025-08-25 18:28:42] [Iter 32/1050] R0[31/150], Temp: 0.8983, Energy: -29.167166+0.001279j
[2025-08-25 18:28:48] [Iter 33/1050] R0[32/150], Temp: 0.8918, Energy: -29.170715+0.002065j
[2025-08-25 18:28:53] [Iter 34/1050] R0[33/150], Temp: 0.8853, Energy: -29.175888-0.002785j
[2025-08-25 18:28:59] [Iter 35/1050] R0[34/150], Temp: 0.8785, Energy: -29.173144+0.000852j
[2025-08-25 18:29:05] [Iter 36/1050] R0[35/150], Temp: 0.8716, Energy: -29.163791-0.002430j
[2025-08-25 18:29:11] [Iter 37/1050] R0[36/150], Temp: 0.8645, Energy: -29.169150-0.001836j
[2025-08-25 18:29:17] [Iter 38/1050] R0[37/150], Temp: 0.8572, Energy: -29.165472+0.001033j
[2025-08-25 18:29:22] [Iter 39/1050] R0[38/150], Temp: 0.8498, Energy: -29.178295-0.002186j
[2025-08-25 18:29:28] [Iter 40/1050] R0[39/150], Temp: 0.8423, Energy: -29.172757+0.003971j
[2025-08-25 18:29:34] [Iter 41/1050] R0[40/150], Temp: 0.8346, Energy: -29.166158+0.000176j
[2025-08-25 18:29:40] [Iter 42/1050] R0[41/150], Temp: 0.8267, Energy: -29.172083-0.000126j
[2025-08-25 18:29:45] [Iter 43/1050] R0[42/150], Temp: 0.8187, Energy: -29.169859+0.000824j
[2025-08-25 18:29:51] [Iter 44/1050] R0[43/150], Temp: 0.8106, Energy: -29.184107-0.002128j
[2025-08-25 18:29:57] [Iter 45/1050] R0[44/150], Temp: 0.8023, Energy: -29.169013-0.001855j
[2025-08-25 18:30:03] [Iter 46/1050] R0[45/150], Temp: 0.7939, Energy: -29.171353+0.002285j
[2025-08-25 18:30:08] [Iter 47/1050] R0[46/150], Temp: 0.7854, Energy: -29.168376+0.002275j
[2025-08-25 18:30:14] [Iter 48/1050] R0[47/150], Temp: 0.7767, Energy: -29.176962-0.001453j
[2025-08-25 18:30:20] [Iter 49/1050] R0[48/150], Temp: 0.7679, Energy: -29.176571-0.001444j
[2025-08-25 18:30:26] [Iter 50/1050] R0[49/150], Temp: 0.7590, Energy: -29.176551-0.000699j
[2025-08-25 18:30:31] [Iter 51/1050] R0[50/150], Temp: 0.7500, Energy: -29.170257+0.000070j
[2025-08-25 18:30:37] [Iter 52/1050] R0[51/150], Temp: 0.7409, Energy: -29.171863+0.002053j
[2025-08-25 18:30:43] [Iter 53/1050] R0[52/150], Temp: 0.7316, Energy: -29.174882-0.000422j
[2025-08-25 18:30:49] [Iter 54/1050] R0[53/150], Temp: 0.7223, Energy: -29.174979+0.000924j
[2025-08-25 18:30:54] [Iter 55/1050] R0[54/150], Temp: 0.7129, Energy: -29.163219+0.002578j
[2025-08-25 18:31:00] [Iter 56/1050] R0[55/150], Temp: 0.7034, Energy: -29.170734-0.002756j
[2025-08-25 18:31:06] [Iter 57/1050] R0[56/150], Temp: 0.6938, Energy: -29.171476+0.001974j
[2025-08-25 18:31:12] [Iter 58/1050] R0[57/150], Temp: 0.6841, Energy: -29.168262-0.002286j
[2025-08-25 18:31:17] [Iter 59/1050] R0[58/150], Temp: 0.6743, Energy: -29.172530+0.004299j
[2025-08-25 18:31:23] [Iter 60/1050] R0[59/150], Temp: 0.6644, Energy: -29.165191-0.004389j
[2025-08-25 18:31:29] [Iter 61/1050] R0[60/150], Temp: 0.6545, Energy: -29.174269+0.000530j
[2025-08-25 18:31:35] [Iter 62/1050] R0[61/150], Temp: 0.6445, Energy: -29.165358-0.002272j
[2025-08-25 18:31:40] [Iter 63/1050] R0[62/150], Temp: 0.6345, Energy: -29.167976+0.000573j
[2025-08-25 18:31:46] [Iter 64/1050] R0[63/150], Temp: 0.6243, Energy: -29.168188-0.004968j
[2025-08-25 18:31:52] [Iter 65/1050] R0[64/150], Temp: 0.6142, Energy: -29.167243-0.003910j
[2025-08-25 18:31:58] [Iter 66/1050] R0[65/150], Temp: 0.6040, Energy: -29.159111-0.002579j
[2025-08-25 18:32:03] [Iter 67/1050] R0[66/150], Temp: 0.5937, Energy: -29.178303-0.001578j
[2025-08-25 18:32:09] [Iter 68/1050] R0[67/150], Temp: 0.5834, Energy: -29.166926-0.000975j
[2025-08-25 18:32:15] [Iter 69/1050] R0[68/150], Temp: 0.5730, Energy: -29.171020-0.001144j
[2025-08-25 18:32:21] [Iter 70/1050] R0[69/150], Temp: 0.5627, Energy: -29.169939+0.000066j
[2025-08-25 18:32:26] [Iter 71/1050] R0[70/150], Temp: 0.5523, Energy: -29.166559+0.001614j
[2025-08-25 18:32:32] [Iter 72/1050] R0[71/150], Temp: 0.5418, Energy: -29.154883+0.004639j
[2025-08-25 18:32:38] [Iter 73/1050] R0[72/150], Temp: 0.5314, Energy: -29.164265+0.001248j
[2025-08-25 18:32:44] [Iter 74/1050] R0[73/150], Temp: 0.5209, Energy: -29.167520-0.000789j
[2025-08-25 18:32:49] [Iter 75/1050] R0[74/150], Temp: 0.5105, Energy: -29.154498-0.004569j
[2025-08-25 18:32:55] [Iter 76/1050] R0[75/150], Temp: 0.5000, Energy: -29.161516+0.000912j
[2025-08-25 18:33:01] [Iter 77/1050] R0[76/150], Temp: 0.4895, Energy: -29.165546+0.001551j
[2025-08-25 18:33:07] [Iter 78/1050] R0[77/150], Temp: 0.4791, Energy: -29.173126-0.002074j
[2025-08-25 18:33:12] [Iter 79/1050] R0[78/150], Temp: 0.4686, Energy: -29.173143-0.002748j
[2025-08-25 18:33:18] [Iter 80/1050] R0[79/150], Temp: 0.4582, Energy: -29.171777+0.000126j
[2025-08-25 18:33:24] [Iter 81/1050] R0[80/150], Temp: 0.4477, Energy: -29.177084-0.000306j
[2025-08-25 18:33:30] [Iter 82/1050] R0[81/150], Temp: 0.4373, Energy: -29.167091+0.000545j
[2025-08-25 18:33:35] [Iter 83/1050] R0[82/150], Temp: 0.4270, Energy: -29.161427-0.000505j
[2025-08-25 18:33:41] [Iter 84/1050] R0[83/150], Temp: 0.4166, Energy: -29.169477-0.000408j
[2025-08-25 18:33:47] [Iter 85/1050] R0[84/150], Temp: 0.4063, Energy: -29.169078+0.000108j
[2025-08-25 18:33:53] [Iter 86/1050] R0[85/150], Temp: 0.3960, Energy: -29.172457+0.002508j
[2025-08-25 18:33:58] [Iter 87/1050] R0[86/150], Temp: 0.3858, Energy: -29.167818-0.002541j
[2025-08-25 18:34:04] [Iter 88/1050] R0[87/150], Temp: 0.3757, Energy: -29.168624-0.000268j
[2025-08-25 18:34:10] [Iter 89/1050] R0[88/150], Temp: 0.3655, Energy: -29.159204+0.002605j
[2025-08-25 18:34:16] [Iter 90/1050] R0[89/150], Temp: 0.3555, Energy: -29.173611-0.000412j
[2025-08-25 18:34:21] [Iter 91/1050] R0[90/150], Temp: 0.3455, Energy: -29.159953-0.001494j
[2025-08-25 18:34:27] [Iter 92/1050] R0[91/150], Temp: 0.3356, Energy: -29.173744-0.002213j
[2025-08-25 18:34:33] [Iter 93/1050] R0[92/150], Temp: 0.3257, Energy: -29.177489-0.000007j
[2025-08-25 18:34:39] [Iter 94/1050] R0[93/150], Temp: 0.3159, Energy: -29.174868+0.000989j
[2025-08-25 18:34:45] [Iter 95/1050] R0[94/150], Temp: 0.3062, Energy: -29.176545-0.000382j
[2025-08-25 18:34:50] [Iter 96/1050] R0[95/150], Temp: 0.2966, Energy: -29.175717-0.001118j
[2025-08-25 18:34:56] [Iter 97/1050] R0[96/150], Temp: 0.2871, Energy: -29.168174+0.004044j
[2025-08-25 18:35:02] [Iter 98/1050] R0[97/150], Temp: 0.2777, Energy: -29.172843+0.003365j
[2025-08-25 18:35:08] [Iter 99/1050] R0[98/150], Temp: 0.2684, Energy: -29.175509+0.001529j
[2025-08-25 18:35:13] [Iter 100/1050] R0[99/150], Temp: 0.2591, Energy: -29.174140+0.000772j
[2025-08-25 18:35:13] ✓ Checkpoint saved: checkpoint_iter_000100.pkl
[2025-08-25 18:35:19] [Iter 101/1050] R0[100/150], Temp: 0.2500, Energy: -29.165153+0.000937j
[2025-08-25 18:35:25] [Iter 102/1050] R0[101/150], Temp: 0.2410, Energy: -29.177330-0.000332j
[2025-08-25 18:35:31] [Iter 103/1050] R0[102/150], Temp: 0.2321, Energy: -29.169569+0.005268j
[2025-08-25 18:35:36] [Iter 104/1050] R0[103/150], Temp: 0.2233, Energy: -29.163538-0.003381j
[2025-08-25 18:35:42] [Iter 105/1050] R0[104/150], Temp: 0.2146, Energy: -29.177973+0.002447j
[2025-08-25 18:35:48] [Iter 106/1050] R0[105/150], Temp: 0.2061, Energy: -29.171559-0.001089j
[2025-08-25 18:35:54] [Iter 107/1050] R0[106/150], Temp: 0.1977, Energy: -29.170883-0.001351j
[2025-08-25 18:35:59] [Iter 108/1050] R0[107/150], Temp: 0.1894, Energy: -29.171926-0.003161j
[2025-08-25 18:36:05] [Iter 109/1050] R0[108/150], Temp: 0.1813, Energy: -29.172427-0.001852j
[2025-08-25 18:36:11] [Iter 110/1050] R0[109/150], Temp: 0.1733, Energy: -29.171268+0.002852j
[2025-08-25 18:36:17] [Iter 111/1050] R0[110/150], Temp: 0.1654, Energy: -29.172808-0.002560j
[2025-08-25 18:36:22] [Iter 112/1050] R0[111/150], Temp: 0.1577, Energy: -29.168562+0.000163j
[2025-08-25 18:36:28] [Iter 113/1050] R0[112/150], Temp: 0.1502, Energy: -29.172826+0.001629j
[2025-08-25 18:36:34] [Iter 114/1050] R0[113/150], Temp: 0.1428, Energy: -29.165285-0.000607j
[2025-08-25 18:36:40] [Iter 115/1050] R0[114/150], Temp: 0.1355, Energy: -29.172381+0.000548j
[2025-08-25 18:36:45] [Iter 116/1050] R0[115/150], Temp: 0.1284, Energy: -29.179122-0.002442j
[2025-08-25 18:36:51] [Iter 117/1050] R0[116/150], Temp: 0.1215, Energy: -29.180595+0.000794j
[2025-08-25 18:36:57] [Iter 118/1050] R0[117/150], Temp: 0.1147, Energy: -29.175576-0.000225j
[2025-08-25 18:37:03] [Iter 119/1050] R0[118/150], Temp: 0.1082, Energy: -29.169161+0.000674j
[2025-08-25 18:37:08] [Iter 120/1050] R0[119/150], Temp: 0.1017, Energy: -29.171396+0.001489j
[2025-08-25 18:37:14] [Iter 121/1050] R0[120/150], Temp: 0.0955, Energy: -29.175217-0.003366j
[2025-08-25 18:37:20] [Iter 122/1050] R0[121/150], Temp: 0.0894, Energy: -29.179562-0.001559j
[2025-08-25 18:37:26] [Iter 123/1050] R0[122/150], Temp: 0.0835, Energy: -29.167797-0.002630j
[2025-08-25 18:37:31] [Iter 124/1050] R0[123/150], Temp: 0.0778, Energy: -29.180011+0.005137j
[2025-08-25 18:37:37] [Iter 125/1050] R0[124/150], Temp: 0.0723, Energy: -29.173560+0.001998j
[2025-08-25 18:37:43] [Iter 126/1050] R0[125/150], Temp: 0.0670, Energy: -29.165562+0.001958j
[2025-08-25 18:37:49] [Iter 127/1050] R0[126/150], Temp: 0.0618, Energy: -29.174774-0.000800j
[2025-08-25 18:37:55] [Iter 128/1050] R0[127/150], Temp: 0.0569, Energy: -29.164717+0.001620j
[2025-08-25 18:38:00] [Iter 129/1050] R0[128/150], Temp: 0.0521, Energy: -29.177786-0.000011j
[2025-08-25 18:38:06] [Iter 130/1050] R0[129/150], Temp: 0.0476, Energy: -29.169125-0.005893j
[2025-08-25 18:38:12] [Iter 131/1050] R0[130/150], Temp: 0.0432, Energy: -29.172595-0.001206j
[2025-08-25 18:38:18] [Iter 132/1050] R0[131/150], Temp: 0.0391, Energy: -29.182241+0.000511j
[2025-08-25 18:38:23] [Iter 133/1050] R0[132/150], Temp: 0.0351, Energy: -29.163749-0.000318j
[2025-08-25 18:38:29] [Iter 134/1050] R0[133/150], Temp: 0.0314, Energy: -29.169036+0.000460j
[2025-08-25 18:38:35] [Iter 135/1050] R0[134/150], Temp: 0.0278, Energy: -29.168319+0.001764j
[2025-08-25 18:38:41] [Iter 136/1050] R0[135/150], Temp: 0.0245, Energy: -29.174432+0.000521j
[2025-08-25 18:38:46] [Iter 137/1050] R0[136/150], Temp: 0.0213, Energy: -29.172423+0.001360j
[2025-08-25 18:38:52] [Iter 138/1050] R0[137/150], Temp: 0.0184, Energy: -29.163892+0.002231j
[2025-08-25 18:38:58] [Iter 139/1050] R0[138/150], Temp: 0.0157, Energy: -29.176179-0.001138j
[2025-08-25 18:39:04] [Iter 140/1050] R0[139/150], Temp: 0.0132, Energy: -29.180167-0.001252j
[2025-08-25 18:39:09] [Iter 141/1050] R0[140/150], Temp: 0.0109, Energy: -29.162524+0.000481j
[2025-08-25 18:39:15] [Iter 142/1050] R0[141/150], Temp: 0.0089, Energy: -29.185150-0.005182j
[2025-08-25 18:39:21] [Iter 143/1050] R0[142/150], Temp: 0.0070, Energy: -29.168662-0.000572j
[2025-08-25 18:39:27] [Iter 144/1050] R0[143/150], Temp: 0.0054, Energy: -29.164220+0.002007j
[2025-08-25 18:39:32] [Iter 145/1050] R0[144/150], Temp: 0.0039, Energy: -29.165164+0.002989j
[2025-08-25 18:39:38] [Iter 146/1050] R0[145/150], Temp: 0.0027, Energy: -29.167078+0.001900j
[2025-08-25 18:39:44] [Iter 147/1050] R0[146/150], Temp: 0.0018, Energy: -29.175527+0.000660j
[2025-08-25 18:39:50] [Iter 148/1050] R0[147/150], Temp: 0.0010, Energy: -29.166374+0.000150j
[2025-08-25 18:39:55] [Iter 149/1050] R0[148/150], Temp: 0.0004, Energy: -29.164104-0.000369j
[2025-08-25 18:40:01] [Iter 150/1050] R0[149/150], Temp: 0.0001, Energy: -29.165347+0.003047j
[2025-08-25 18:40:01] RESTART #1 | Period: 300
[2025-08-25 18:40:07] [Iter 151/1050] R1[0/300], Temp: 1.0000, Energy: -29.163696+0.003623j
[2025-08-25 18:40:13] [Iter 152/1050] R1[1/300], Temp: 1.0000, Energy: -29.171619-0.002729j
[2025-08-25 18:40:18] [Iter 153/1050] R1[2/300], Temp: 0.9999, Energy: -29.166691+0.002270j
[2025-08-25 18:40:24] [Iter 154/1050] R1[3/300], Temp: 0.9998, Energy: -29.169299+0.001608j
[2025-08-25 18:40:30] [Iter 155/1050] R1[4/300], Temp: 0.9996, Energy: -29.169237-0.001801j
[2025-08-25 18:40:36] [Iter 156/1050] R1[5/300], Temp: 0.9993, Energy: -29.173654-0.004270j
[2025-08-25 18:40:41] [Iter 157/1050] R1[6/300], Temp: 0.9990, Energy: -29.175270-0.002785j
[2025-08-25 18:40:47] [Iter 158/1050] R1[7/300], Temp: 0.9987, Energy: -29.177312-0.000425j
[2025-08-25 18:40:53] [Iter 159/1050] R1[8/300], Temp: 0.9982, Energy: -29.169109+0.001823j
[2025-08-25 18:40:59] [Iter 160/1050] R1[9/300], Temp: 0.9978, Energy: -29.167409-0.004310j
[2025-08-25 18:41:04] [Iter 161/1050] R1[10/300], Temp: 0.9973, Energy: -29.175232-0.000643j
[2025-08-25 18:41:10] [Iter 162/1050] R1[11/300], Temp: 0.9967, Energy: -29.177079+0.001625j
[2025-08-25 18:41:16] [Iter 163/1050] R1[12/300], Temp: 0.9961, Energy: -29.175169-0.002931j
[2025-08-25 18:41:22] [Iter 164/1050] R1[13/300], Temp: 0.9954, Energy: -29.161807+0.002517j
[2025-08-25 18:41:28] [Iter 165/1050] R1[14/300], Temp: 0.9946, Energy: -29.171382+0.001414j
[2025-08-25 18:41:33] [Iter 166/1050] R1[15/300], Temp: 0.9938, Energy: -29.174190+0.002990j
[2025-08-25 18:41:39] [Iter 167/1050] R1[16/300], Temp: 0.9930, Energy: -29.180172+0.002125j
[2025-08-25 18:41:45] [Iter 168/1050] R1[17/300], Temp: 0.9921, Energy: -29.175466-0.001522j
[2025-08-25 18:41:51] [Iter 169/1050] R1[18/300], Temp: 0.9911, Energy: -29.170424+0.000903j
[2025-08-25 18:41:56] [Iter 170/1050] R1[19/300], Temp: 0.9901, Energy: -29.175633-0.002396j
[2025-08-25 18:42:02] [Iter 171/1050] R1[20/300], Temp: 0.9891, Energy: -29.165483-0.004204j
[2025-08-25 18:42:08] [Iter 172/1050] R1[21/300], Temp: 0.9880, Energy: -29.162648-0.000417j
[2025-08-25 18:42:14] [Iter 173/1050] R1[22/300], Temp: 0.9868, Energy: -29.173078+0.002991j
[2025-08-25 18:42:19] [Iter 174/1050] R1[23/300], Temp: 0.9856, Energy: -29.171957-0.001471j
[2025-08-25 18:42:25] [Iter 175/1050] R1[24/300], Temp: 0.9843, Energy: -29.163708+0.001269j
[2025-08-25 18:42:31] [Iter 176/1050] R1[25/300], Temp: 0.9830, Energy: -29.170719-0.001164j
[2025-08-25 18:42:37] [Iter 177/1050] R1[26/300], Temp: 0.9816, Energy: -29.167931+0.000490j
[2025-08-25 18:42:42] [Iter 178/1050] R1[27/300], Temp: 0.9801, Energy: -29.168093-0.000539j
[2025-08-25 18:42:48] [Iter 179/1050] R1[28/300], Temp: 0.9787, Energy: -29.173740-0.003065j
[2025-08-25 18:42:54] [Iter 180/1050] R1[29/300], Temp: 0.9771, Energy: -29.170173+0.001196j
[2025-08-25 18:43:00] [Iter 181/1050] R1[30/300], Temp: 0.9755, Energy: -29.176832-0.001858j
[2025-08-25 18:43:05] [Iter 182/1050] R1[31/300], Temp: 0.9739, Energy: -29.176643+0.001645j
[2025-08-25 18:43:11] [Iter 183/1050] R1[32/300], Temp: 0.9722, Energy: -29.173754-0.000316j
[2025-08-25 18:43:17] [Iter 184/1050] R1[33/300], Temp: 0.9704, Energy: -29.167757+0.004918j
[2025-08-25 18:43:23] [Iter 185/1050] R1[34/300], Temp: 0.9686, Energy: -29.169357+0.000645j
[2025-08-25 18:43:28] [Iter 186/1050] R1[35/300], Temp: 0.9668, Energy: -29.168199+0.001358j
[2025-08-25 18:43:34] [Iter 187/1050] R1[36/300], Temp: 0.9649, Energy: -29.170580+0.004220j
[2025-08-25 18:43:40] [Iter 188/1050] R1[37/300], Temp: 0.9629, Energy: -29.168163-0.001615j
[2025-08-25 18:43:46] [Iter 189/1050] R1[38/300], Temp: 0.9609, Energy: -29.171034-0.001609j
[2025-08-25 18:43:51] [Iter 190/1050] R1[39/300], Temp: 0.9589, Energy: -29.171541-0.001474j
[2025-08-25 18:43:57] [Iter 191/1050] R1[40/300], Temp: 0.9568, Energy: -29.174030-0.004072j
[2025-08-25 18:44:03] [Iter 192/1050] R1[41/300], Temp: 0.9546, Energy: -29.167804-0.000006j
[2025-08-25 18:44:09] [Iter 193/1050] R1[42/300], Temp: 0.9524, Energy: -29.174401+0.001605j
[2025-08-25 18:44:14] [Iter 194/1050] R1[43/300], Temp: 0.9502, Energy: -29.169252+0.000838j
[2025-08-25 18:44:20] [Iter 195/1050] R1[44/300], Temp: 0.9479, Energy: -29.178917+0.000230j
[2025-08-25 18:44:26] [Iter 196/1050] R1[45/300], Temp: 0.9455, Energy: -29.169229+0.002302j
[2025-08-25 18:44:32] [Iter 197/1050] R1[46/300], Temp: 0.9431, Energy: -29.172528+0.003846j
[2025-08-25 18:44:37] [Iter 198/1050] R1[47/300], Temp: 0.9407, Energy: -29.173177+0.003920j
[2025-08-25 18:44:43] [Iter 199/1050] R1[48/300], Temp: 0.9382, Energy: -29.168192-0.002853j
[2025-08-25 18:44:49] [Iter 200/1050] R1[49/300], Temp: 0.9356, Energy: -29.176909-0.002343j
[2025-08-25 18:44:49] ✓ Checkpoint saved: checkpoint_iter_000200.pkl
[2025-08-25 18:44:55] [Iter 201/1050] R1[50/300], Temp: 0.9330, Energy: -29.181369-0.001831j
[2025-08-25 18:45:01] [Iter 202/1050] R1[51/300], Temp: 0.9304, Energy: -29.179566-0.004499j
[2025-08-25 18:45:06] [Iter 203/1050] R1[52/300], Temp: 0.9277, Energy: -29.173749-0.002810j
[2025-08-25 18:45:12] [Iter 204/1050] R1[53/300], Temp: 0.9249, Energy: -29.172304+0.000515j
[2025-08-25 18:45:18] [Iter 205/1050] R1[54/300], Temp: 0.9222, Energy: -29.166003-0.000072j
[2025-08-25 18:45:24] [Iter 206/1050] R1[55/300], Temp: 0.9193, Energy: -29.165718-0.002878j
[2025-08-25 18:45:29] [Iter 207/1050] R1[56/300], Temp: 0.9165, Energy: -29.178725-0.000681j
[2025-08-25 18:45:35] [Iter 208/1050] R1[57/300], Temp: 0.9135, Energy: -29.180641-0.000725j
[2025-08-25 18:45:41] [Iter 209/1050] R1[58/300], Temp: 0.9106, Energy: -29.167353-0.000195j
[2025-08-25 18:45:47] [Iter 210/1050] R1[59/300], Temp: 0.9076, Energy: -29.168172-0.001378j
[2025-08-25 18:45:52] [Iter 211/1050] R1[60/300], Temp: 0.9045, Energy: -29.165271-0.001117j
[2025-08-25 18:45:58] [Iter 212/1050] R1[61/300], Temp: 0.9014, Energy: -29.177873+0.002500j
[2025-08-25 18:46:04] [Iter 213/1050] R1[62/300], Temp: 0.8983, Energy: -29.176321-0.001746j
[2025-08-25 18:46:10] [Iter 214/1050] R1[63/300], Temp: 0.8951, Energy: -29.171088-0.000674j
[2025-08-25 18:46:15] [Iter 215/1050] R1[64/300], Temp: 0.8918, Energy: -29.166957+0.002612j
[2025-08-25 18:46:21] [Iter 216/1050] R1[65/300], Temp: 0.8886, Energy: -29.165395+0.004211j
[2025-08-25 18:46:27] [Iter 217/1050] R1[66/300], Temp: 0.8853, Energy: -29.182012-0.002250j
[2025-08-25 18:46:33] [Iter 218/1050] R1[67/300], Temp: 0.8819, Energy: -29.168301-0.002410j
[2025-08-25 18:46:38] [Iter 219/1050] R1[68/300], Temp: 0.8785, Energy: -29.165342-0.002914j
[2025-08-25 18:46:44] [Iter 220/1050] R1[69/300], Temp: 0.8751, Energy: -29.170320-0.000550j
[2025-08-25 18:46:50] [Iter 221/1050] R1[70/300], Temp: 0.8716, Energy: -29.172839+0.001721j
[2025-08-25 18:46:56] [Iter 222/1050] R1[71/300], Temp: 0.8680, Energy: -29.167871-0.001999j
[2025-08-25 18:47:01] [Iter 223/1050] R1[72/300], Temp: 0.8645, Energy: -29.168217-0.001420j
[2025-08-25 18:47:07] [Iter 224/1050] R1[73/300], Temp: 0.8609, Energy: -29.165303+0.000699j
[2025-08-25 18:47:13] [Iter 225/1050] R1[74/300], Temp: 0.8572, Energy: -29.168383+0.002112j
[2025-08-25 18:47:19] [Iter 226/1050] R1[75/300], Temp: 0.8536, Energy: -29.158859+0.001834j
[2025-08-25 18:47:24] [Iter 227/1050] R1[76/300], Temp: 0.8498, Energy: -29.178073+0.000578j
[2025-08-25 18:47:30] [Iter 228/1050] R1[77/300], Temp: 0.8461, Energy: -29.173374+0.000027j
[2025-08-25 18:47:36] [Iter 229/1050] R1[78/300], Temp: 0.8423, Energy: -29.160678-0.001437j
[2025-08-25 18:47:42] [Iter 230/1050] R1[79/300], Temp: 0.8384, Energy: -29.173439-0.000958j
[2025-08-25 18:47:48] [Iter 231/1050] R1[80/300], Temp: 0.8346, Energy: -29.171268-0.003152j
[2025-08-25 18:47:53] [Iter 232/1050] R1[81/300], Temp: 0.8307, Energy: -29.165762-0.000971j
[2025-08-25 18:47:59] [Iter 233/1050] R1[82/300], Temp: 0.8267, Energy: -29.170099-0.001996j
[2025-08-25 18:48:05] [Iter 234/1050] R1[83/300], Temp: 0.8227, Energy: -29.173628+0.003244j
[2025-08-25 18:48:11] [Iter 235/1050] R1[84/300], Temp: 0.8187, Energy: -29.172134+0.002243j
[2025-08-25 18:48:16] [Iter 236/1050] R1[85/300], Temp: 0.8147, Energy: -29.170251-0.001536j
[2025-08-25 18:48:22] [Iter 237/1050] R1[86/300], Temp: 0.8106, Energy: -29.166007+0.000028j
[2025-08-25 18:48:28] [Iter 238/1050] R1[87/300], Temp: 0.8065, Energy: -29.169882+0.001923j
[2025-08-25 18:48:34] [Iter 239/1050] R1[88/300], Temp: 0.8023, Energy: -29.175797+0.002024j
[2025-08-25 18:48:39] [Iter 240/1050] R1[89/300], Temp: 0.7981, Energy: -29.163358+0.000852j
[2025-08-25 18:48:45] [Iter 241/1050] R1[90/300], Temp: 0.7939, Energy: -29.158211-0.002586j
[2025-08-25 18:48:51] [Iter 242/1050] R1[91/300], Temp: 0.7896, Energy: -29.172426-0.001381j
[2025-08-25 18:48:57] [Iter 243/1050] R1[92/300], Temp: 0.7854, Energy: -29.159787-0.001662j
[2025-08-25 18:49:02] [Iter 244/1050] R1[93/300], Temp: 0.7810, Energy: -29.172985-0.001960j
[2025-08-25 18:49:08] [Iter 245/1050] R1[94/300], Temp: 0.7767, Energy: -29.166994+0.002297j
[2025-08-25 18:49:14] [Iter 246/1050] R1[95/300], Temp: 0.7723, Energy: -29.159722-0.005869j
[2025-08-25 18:49:20] [Iter 247/1050] R1[96/300], Temp: 0.7679, Energy: -29.170761+0.002952j
[2025-08-25 18:49:25] [Iter 248/1050] R1[97/300], Temp: 0.7635, Energy: -29.165006-0.000301j
[2025-08-25 18:49:31] [Iter 249/1050] R1[98/300], Temp: 0.7590, Energy: -29.168180-0.001206j
[2025-08-25 18:49:37] [Iter 250/1050] R1[99/300], Temp: 0.7545, Energy: -29.166471+0.000119j
[2025-08-25 18:49:43] [Iter 251/1050] R1[100/300], Temp: 0.7500, Energy: -29.167832-0.000391j
[2025-08-25 18:49:49] [Iter 252/1050] R1[101/300], Temp: 0.7455, Energy: -29.159999-0.002783j
[2025-08-25 18:49:54] [Iter 253/1050] R1[102/300], Temp: 0.7409, Energy: -29.160183-0.001181j
[2025-08-25 18:50:00] [Iter 254/1050] R1[103/300], Temp: 0.7363, Energy: -29.179488+0.000510j
[2025-08-25 18:50:06] [Iter 255/1050] R1[104/300], Temp: 0.7316, Energy: -29.170572-0.001910j
[2025-08-25 18:50:12] [Iter 256/1050] R1[105/300], Temp: 0.7270, Energy: -29.170718+0.003674j
[2025-08-25 18:50:17] [Iter 257/1050] R1[106/300], Temp: 0.7223, Energy: -29.159401-0.001181j
[2025-08-25 18:50:23] [Iter 258/1050] R1[107/300], Temp: 0.7176, Energy: -29.171197+0.000145j
[2025-08-25 18:50:29] [Iter 259/1050] R1[108/300], Temp: 0.7129, Energy: -29.173316-0.000476j
[2025-08-25 18:50:35] [Iter 260/1050] R1[109/300], Temp: 0.7081, Energy: -29.174315-0.000504j
[2025-08-25 18:50:40] [Iter 261/1050] R1[110/300], Temp: 0.7034, Energy: -29.160777+0.003726j
[2025-08-25 18:50:46] [Iter 262/1050] R1[111/300], Temp: 0.6986, Energy: -29.174229+0.002530j
[2025-08-25 18:50:52] [Iter 263/1050] R1[112/300], Temp: 0.6938, Energy: -29.158905-0.002076j
[2025-08-25 18:50:58] [Iter 264/1050] R1[113/300], Temp: 0.6889, Energy: -29.166648-0.000602j
[2025-08-25 18:51:03] [Iter 265/1050] R1[114/300], Temp: 0.6841, Energy: -29.174078+0.001463j
[2025-08-25 18:51:09] [Iter 266/1050] R1[115/300], Temp: 0.6792, Energy: -29.167777-0.001043j
[2025-08-25 18:51:15] [Iter 267/1050] R1[116/300], Temp: 0.6743, Energy: -29.172938-0.001094j
[2025-08-25 18:51:21] [Iter 268/1050] R1[117/300], Temp: 0.6694, Energy: -29.174451-0.002615j
[2025-08-25 18:51:26] [Iter 269/1050] R1[118/300], Temp: 0.6644, Energy: -29.162199-0.001122j
[2025-08-25 18:51:32] [Iter 270/1050] R1[119/300], Temp: 0.6595, Energy: -29.166380+0.002880j
[2025-08-25 18:51:38] [Iter 271/1050] R1[120/300], Temp: 0.6545, Energy: -29.174794-0.001371j
[2025-08-25 18:51:44] [Iter 272/1050] R1[121/300], Temp: 0.6495, Energy: -29.169829-0.001019j
[2025-08-25 18:51:49] [Iter 273/1050] R1[122/300], Temp: 0.6445, Energy: -29.173388+0.001257j
[2025-08-25 18:51:55] [Iter 274/1050] R1[123/300], Temp: 0.6395, Energy: -29.169474-0.000445j
[2025-08-25 18:52:01] [Iter 275/1050] R1[124/300], Temp: 0.6345, Energy: -29.166589-0.001018j
[2025-08-25 18:52:07] [Iter 276/1050] R1[125/300], Temp: 0.6294, Energy: -29.174430+0.002018j
[2025-08-25 18:52:12] [Iter 277/1050] R1[126/300], Temp: 0.6243, Energy: -29.173603-0.000854j
[2025-08-25 18:52:18] [Iter 278/1050] R1[127/300], Temp: 0.6193, Energy: -29.173601+0.001124j
[2025-08-25 18:52:24] [Iter 279/1050] R1[128/300], Temp: 0.6142, Energy: -29.182642-0.000429j
[2025-08-25 18:52:30] [Iter 280/1050] R1[129/300], Temp: 0.6091, Energy: -29.169831-0.000123j
[2025-08-25 18:52:35] [Iter 281/1050] R1[130/300], Temp: 0.6040, Energy: -29.164286-0.001473j
[2025-08-25 18:52:41] [Iter 282/1050] R1[131/300], Temp: 0.5988, Energy: -29.165386-0.003658j
[2025-08-25 18:52:47] [Iter 283/1050] R1[132/300], Temp: 0.5937, Energy: -29.170904+0.001585j
[2025-08-25 18:52:53] [Iter 284/1050] R1[133/300], Temp: 0.5885, Energy: -29.165200-0.001440j
[2025-08-25 18:52:58] [Iter 285/1050] R1[134/300], Temp: 0.5834, Energy: -29.171958+0.000684j
[2025-08-25 18:53:04] [Iter 286/1050] R1[135/300], Temp: 0.5782, Energy: -29.178017+0.009117j
[2025-08-25 18:53:10] [Iter 287/1050] R1[136/300], Temp: 0.5730, Energy: -29.174885+0.002350j
[2025-08-25 18:53:16] [Iter 288/1050] R1[137/300], Temp: 0.5679, Energy: -29.168355+0.002953j
[2025-08-25 18:53:21] [Iter 289/1050] R1[138/300], Temp: 0.5627, Energy: -29.158958-0.004742j
[2025-08-25 18:53:27] [Iter 290/1050] R1[139/300], Temp: 0.5575, Energy: -29.163590+0.001341j
[2025-08-25 18:53:33] [Iter 291/1050] R1[140/300], Temp: 0.5523, Energy: -29.166206-0.000557j
[2025-08-25 18:53:39] [Iter 292/1050] R1[141/300], Temp: 0.5471, Energy: -29.169170+0.003646j
[2025-08-25 18:53:45] [Iter 293/1050] R1[142/300], Temp: 0.5418, Energy: -29.175074-0.002022j
[2025-08-25 18:53:50] [Iter 294/1050] R1[143/300], Temp: 0.5366, Energy: -29.172193-0.000755j
[2025-08-25 18:53:56] [Iter 295/1050] R1[144/300], Temp: 0.5314, Energy: -29.161681-0.001332j
[2025-08-25 18:54:02] [Iter 296/1050] R1[145/300], Temp: 0.5262, Energy: -29.166828-0.002439j
[2025-08-25 18:54:08] [Iter 297/1050] R1[146/300], Temp: 0.5209, Energy: -29.170847+0.002538j
[2025-08-25 18:54:13] [Iter 298/1050] R1[147/300], Temp: 0.5157, Energy: -29.180990-0.003001j
[2025-08-25 18:54:19] [Iter 299/1050] R1[148/300], Temp: 0.5105, Energy: -29.173544-0.003095j
[2025-08-25 18:54:25] [Iter 300/1050] R1[149/300], Temp: 0.5052, Energy: -29.171669+0.000581j
[2025-08-25 18:54:25] ✓ Checkpoint saved: checkpoint_iter_000300.pkl
[2025-08-25 18:54:31] [Iter 301/1050] R1[150/300], Temp: 0.5000, Energy: -29.165092+0.001046j
[2025-08-25 18:54:36] [Iter 302/1050] R1[151/300], Temp: 0.4948, Energy: -29.175289-0.001891j
[2025-08-25 18:54:42] [Iter 303/1050] R1[152/300], Temp: 0.4895, Energy: -29.172397+0.000120j
[2025-08-25 18:54:48] [Iter 304/1050] R1[153/300], Temp: 0.4843, Energy: -29.172469-0.002818j
[2025-08-25 18:54:54] [Iter 305/1050] R1[154/300], Temp: 0.4791, Energy: -29.165305-0.000291j
[2025-08-25 18:54:59] [Iter 306/1050] R1[155/300], Temp: 0.4738, Energy: -29.174252-0.000326j
[2025-08-25 18:55:05] [Iter 307/1050] R1[156/300], Temp: 0.4686, Energy: -29.180665+0.002512j
[2025-08-25 18:55:11] [Iter 308/1050] R1[157/300], Temp: 0.4634, Energy: -29.164054-0.000217j
[2025-08-25 18:55:17] [Iter 309/1050] R1[158/300], Temp: 0.4582, Energy: -29.166252+0.001164j
[2025-08-25 18:55:22] [Iter 310/1050] R1[159/300], Temp: 0.4529, Energy: -29.173562-0.000963j
[2025-08-25 18:55:28] [Iter 311/1050] R1[160/300], Temp: 0.4477, Energy: -29.170489-0.001304j
[2025-08-25 18:55:34] [Iter 312/1050] R1[161/300], Temp: 0.4425, Energy: -29.165850+0.003568j
[2025-08-25 18:55:40] [Iter 313/1050] R1[162/300], Temp: 0.4373, Energy: -29.171320-0.001992j
[2025-08-25 18:55:46] [Iter 314/1050] R1[163/300], Temp: 0.4321, Energy: -29.162288+0.000598j
[2025-08-25 18:55:51] [Iter 315/1050] R1[164/300], Temp: 0.4270, Energy: -29.172597+0.000490j
[2025-08-25 18:55:57] [Iter 316/1050] R1[165/300], Temp: 0.4218, Energy: -29.172223-0.003933j
[2025-08-25 18:56:03] [Iter 317/1050] R1[166/300], Temp: 0.4166, Energy: -29.157175-0.002556j
[2025-08-25 18:56:09] [Iter 318/1050] R1[167/300], Temp: 0.4115, Energy: -29.168146-0.001717j
[2025-08-25 18:56:14] [Iter 319/1050] R1[168/300], Temp: 0.4063, Energy: -29.161297+0.001215j
[2025-08-25 18:56:20] [Iter 320/1050] R1[169/300], Temp: 0.4012, Energy: -29.176366+0.002709j
[2025-08-25 18:56:26] [Iter 321/1050] R1[170/300], Temp: 0.3960, Energy: -29.164581+0.003075j
[2025-08-25 18:56:32] [Iter 322/1050] R1[171/300], Temp: 0.3909, Energy: -29.173607-0.004678j
[2025-08-25 18:56:37] [Iter 323/1050] R1[172/300], Temp: 0.3858, Energy: -29.177202+0.000167j
[2025-08-25 18:56:43] [Iter 324/1050] R1[173/300], Temp: 0.3807, Energy: -29.182772-0.001415j
[2025-08-25 18:56:49] [Iter 325/1050] R1[174/300], Temp: 0.3757, Energy: -29.178713-0.000608j
[2025-08-25 18:56:55] [Iter 326/1050] R1[175/300], Temp: 0.3706, Energy: -29.173451-0.004516j
[2025-08-25 18:57:00] [Iter 327/1050] R1[176/300], Temp: 0.3655, Energy: -29.171984+0.002551j
[2025-08-25 18:57:06] [Iter 328/1050] R1[177/300], Temp: 0.3605, Energy: -29.173617-0.000399j
[2025-08-25 18:57:12] [Iter 329/1050] R1[178/300], Temp: 0.3555, Energy: -29.171177-0.002131j
[2025-08-25 18:57:18] [Iter 330/1050] R1[179/300], Temp: 0.3505, Energy: -29.158012+0.004790j
[2025-08-25 18:57:23] [Iter 331/1050] R1[180/300], Temp: 0.3455, Energy: -29.175286-0.002521j
[2025-08-25 18:57:29] [Iter 332/1050] R1[181/300], Temp: 0.3405, Energy: -29.174649-0.003536j
[2025-08-25 18:57:35] [Iter 333/1050] R1[182/300], Temp: 0.3356, Energy: -29.170245+0.001197j
[2025-08-25 18:57:41] [Iter 334/1050] R1[183/300], Temp: 0.3306, Energy: -29.176089+0.004853j
[2025-08-25 18:57:46] [Iter 335/1050] R1[184/300], Temp: 0.3257, Energy: -29.185817-0.006361j
[2025-08-25 18:57:52] [Iter 336/1050] R1[185/300], Temp: 0.3208, Energy: -29.162388-0.003215j
[2025-08-25 18:57:58] [Iter 337/1050] R1[186/300], Temp: 0.3159, Energy: -29.165350-0.001911j
[2025-08-25 18:58:04] [Iter 338/1050] R1[187/300], Temp: 0.3111, Energy: -29.187521-0.000152j
[2025-08-25 18:58:09] [Iter 339/1050] R1[188/300], Temp: 0.3062, Energy: -29.164329+0.001522j
[2025-08-25 18:58:15] [Iter 340/1050] R1[189/300], Temp: 0.3014, Energy: -29.171088+0.000925j
[2025-08-25 18:58:21] [Iter 341/1050] R1[190/300], Temp: 0.2966, Energy: -29.172921+0.004368j
[2025-08-25 18:58:27] [Iter 342/1050] R1[191/300], Temp: 0.2919, Energy: -29.165052-0.002588j
[2025-08-25 18:58:32] [Iter 343/1050] R1[192/300], Temp: 0.2871, Energy: -29.169946-0.000962j
[2025-08-25 18:58:38] [Iter 344/1050] R1[193/300], Temp: 0.2824, Energy: -29.172835-0.002049j
[2025-08-25 18:58:44] [Iter 345/1050] R1[194/300], Temp: 0.2777, Energy: -29.177779+0.001885j
[2025-08-25 18:58:50] [Iter 346/1050] R1[195/300], Temp: 0.2730, Energy: -29.175758-0.001599j
[2025-08-25 18:58:55] [Iter 347/1050] R1[196/300], Temp: 0.2684, Energy: -29.167488-0.002807j
[2025-08-25 18:59:01] [Iter 348/1050] R1[197/300], Temp: 0.2637, Energy: -29.162791-0.002764j
[2025-08-25 18:59:07] [Iter 349/1050] R1[198/300], Temp: 0.2591, Energy: -29.169143+0.002481j
[2025-08-25 18:59:13] [Iter 350/1050] R1[199/300], Temp: 0.2545, Energy: -29.175853+0.001520j
[2025-08-25 18:59:19] [Iter 351/1050] R1[200/300], Temp: 0.2500, Energy: -29.178905+0.003980j
[2025-08-25 18:59:24] [Iter 352/1050] R1[201/300], Temp: 0.2455, Energy: -29.171149+0.001944j
[2025-08-25 18:59:30] [Iter 353/1050] R1[202/300], Temp: 0.2410, Energy: -29.173846+0.000536j
[2025-08-25 18:59:36] [Iter 354/1050] R1[203/300], Temp: 0.2365, Energy: -29.173647-0.002652j
[2025-08-25 18:59:42] [Iter 355/1050] R1[204/300], Temp: 0.2321, Energy: -29.167950-0.000174j
[2025-08-25 18:59:47] [Iter 356/1050] R1[205/300], Temp: 0.2277, Energy: -29.175559+0.000093j
[2025-08-25 18:59:53] [Iter 357/1050] R1[206/300], Temp: 0.2233, Energy: -29.172004+0.000120j
[2025-08-25 18:59:59] [Iter 358/1050] R1[207/300], Temp: 0.2190, Energy: -29.173027+0.002932j
[2025-08-25 19:00:05] [Iter 359/1050] R1[208/300], Temp: 0.2146, Energy: -29.165733+0.000073j
[2025-08-25 19:00:10] [Iter 360/1050] R1[209/300], Temp: 0.2104, Energy: -29.171466+0.000041j
[2025-08-25 19:00:16] [Iter 361/1050] R1[210/300], Temp: 0.2061, Energy: -29.172131-0.000561j
[2025-08-25 19:00:22] [Iter 362/1050] R1[211/300], Temp: 0.2019, Energy: -29.170109-0.001931j
[2025-08-25 19:00:28] [Iter 363/1050] R1[212/300], Temp: 0.1977, Energy: -29.172426-0.000701j
[2025-08-25 19:00:33] [Iter 364/1050] R1[213/300], Temp: 0.1935, Energy: -29.173296-0.000508j
[2025-08-25 19:00:39] [Iter 365/1050] R1[214/300], Temp: 0.1894, Energy: -29.171195-0.003097j
[2025-08-25 19:00:45] [Iter 366/1050] R1[215/300], Temp: 0.1853, Energy: -29.165095-0.000729j
[2025-08-25 19:00:51] [Iter 367/1050] R1[216/300], Temp: 0.1813, Energy: -29.174088+0.001377j
[2025-08-25 19:00:56] [Iter 368/1050] R1[217/300], Temp: 0.1773, Energy: -29.171192-0.000869j
[2025-08-25 19:01:02] [Iter 369/1050] R1[218/300], Temp: 0.1733, Energy: -29.163679-0.003894j
[2025-08-25 19:01:08] [Iter 370/1050] R1[219/300], Temp: 0.1693, Energy: -29.173950-0.000255j
[2025-08-25 19:01:14] [Iter 371/1050] R1[220/300], Temp: 0.1654, Energy: -29.169166-0.004372j
[2025-08-25 19:01:19] [Iter 372/1050] R1[221/300], Temp: 0.1616, Energy: -29.173562+0.000268j
[2025-08-25 19:01:25] [Iter 373/1050] R1[222/300], Temp: 0.1577, Energy: -29.160986+0.001664j
[2025-08-25 19:01:31] [Iter 374/1050] R1[223/300], Temp: 0.1539, Energy: -29.177022-0.003170j
[2025-08-25 19:01:37] [Iter 375/1050] R1[224/300], Temp: 0.1502, Energy: -29.171276+0.002543j
[2025-08-25 19:01:42] [Iter 376/1050] R1[225/300], Temp: 0.1464, Energy: -29.174718-0.002816j
[2025-08-25 19:01:48] [Iter 377/1050] R1[226/300], Temp: 0.1428, Energy: -29.172397-0.000223j
[2025-08-25 19:01:54] [Iter 378/1050] R1[227/300], Temp: 0.1391, Energy: -29.169146+0.000499j
[2025-08-25 19:02:00] [Iter 379/1050] R1[228/300], Temp: 0.1355, Energy: -29.166022+0.001284j
[2025-08-25 19:02:06] [Iter 380/1050] R1[229/300], Temp: 0.1320, Energy: -29.171441-0.000752j
[2025-08-25 19:02:11] [Iter 381/1050] R1[230/300], Temp: 0.1284, Energy: -29.165495-0.002528j
[2025-08-25 19:02:17] [Iter 382/1050] R1[231/300], Temp: 0.1249, Energy: -29.177286-0.000012j
[2025-08-25 19:02:23] [Iter 383/1050] R1[232/300], Temp: 0.1215, Energy: -29.170710-0.001203j
[2025-08-25 19:02:29] [Iter 384/1050] R1[233/300], Temp: 0.1181, Energy: -29.163093+0.000933j
[2025-08-25 19:02:34] [Iter 385/1050] R1[234/300], Temp: 0.1147, Energy: -29.174484+0.002716j
[2025-08-25 19:02:40] [Iter 386/1050] R1[235/300], Temp: 0.1114, Energy: -29.166224-0.000614j
[2025-08-25 19:02:46] [Iter 387/1050] R1[236/300], Temp: 0.1082, Energy: -29.173268-0.002601j
[2025-08-25 19:02:52] [Iter 388/1050] R1[237/300], Temp: 0.1049, Energy: -29.166974+0.002346j
[2025-08-25 19:02:57] [Iter 389/1050] R1[238/300], Temp: 0.1017, Energy: -29.167604-0.000026j
[2025-08-25 19:03:03] [Iter 390/1050] R1[239/300], Temp: 0.0986, Energy: -29.166672-0.001360j
[2025-08-25 19:03:09] [Iter 391/1050] R1[240/300], Temp: 0.0955, Energy: -29.162994+0.001316j
[2025-08-25 19:03:15] [Iter 392/1050] R1[241/300], Temp: 0.0924, Energy: -29.179797-0.001152j
[2025-08-25 19:03:20] [Iter 393/1050] R1[242/300], Temp: 0.0894, Energy: -29.168975-0.001602j
[2025-08-25 19:03:26] [Iter 394/1050] R1[243/300], Temp: 0.0865, Energy: -29.163894-0.001639j
[2025-08-25 19:03:32] [Iter 395/1050] R1[244/300], Temp: 0.0835, Energy: -29.174522+0.003150j
[2025-08-25 19:03:38] [Iter 396/1050] R1[245/300], Temp: 0.0807, Energy: -29.174175+0.000399j
[2025-08-25 19:03:43] [Iter 397/1050] R1[246/300], Temp: 0.0778, Energy: -29.164353-0.005529j
[2025-08-25 19:03:49] [Iter 398/1050] R1[247/300], Temp: 0.0751, Energy: -29.178421+0.001637j
[2025-08-25 19:03:55] [Iter 399/1050] R1[248/300], Temp: 0.0723, Energy: -29.157266-0.002423j
[2025-08-25 19:04:01] [Iter 400/1050] R1[249/300], Temp: 0.0696, Energy: -29.158699+0.003625j
[2025-08-25 19:04:01] ✓ Checkpoint saved: checkpoint_iter_000400.pkl
[2025-08-25 19:04:06] [Iter 401/1050] R1[250/300], Temp: 0.0670, Energy: -29.165587-0.003349j
[2025-08-25 19:04:12] [Iter 402/1050] R1[251/300], Temp: 0.0644, Energy: -29.164103+0.003146j
[2025-08-25 19:04:18] [Iter 403/1050] R1[252/300], Temp: 0.0618, Energy: -29.161703+0.002461j
[2025-08-25 19:04:24] [Iter 404/1050] R1[253/300], Temp: 0.0593, Energy: -29.172113+0.001337j
[2025-08-25 19:04:29] [Iter 405/1050] R1[254/300], Temp: 0.0569, Energy: -29.170566+0.004214j
[2025-08-25 19:04:35] [Iter 406/1050] R1[255/300], Temp: 0.0545, Energy: -29.162496+0.001818j
[2025-08-25 19:04:41] [Iter 407/1050] R1[256/300], Temp: 0.0521, Energy: -29.174067-0.001252j
[2025-08-25 19:04:47] [Iter 408/1050] R1[257/300], Temp: 0.0498, Energy: -29.168415+0.001218j
[2025-08-25 19:04:52] [Iter 409/1050] R1[258/300], Temp: 0.0476, Energy: -29.165443+0.000528j
[2025-08-25 19:04:58] [Iter 410/1050] R1[259/300], Temp: 0.0454, Energy: -29.175383-0.002752j
[2025-08-25 19:05:04] [Iter 411/1050] R1[260/300], Temp: 0.0432, Energy: -29.175128+0.000866j
[2025-08-25 19:05:10] [Iter 412/1050] R1[261/300], Temp: 0.0411, Energy: -29.167522+0.001077j
[2025-08-25 19:05:15] [Iter 413/1050] R1[262/300], Temp: 0.0391, Energy: -29.174728-0.000351j
[2025-08-25 19:05:21] [Iter 414/1050] R1[263/300], Temp: 0.0371, Energy: -29.178504+0.002565j
[2025-08-25 19:05:27] [Iter 415/1050] R1[264/300], Temp: 0.0351, Energy: -29.171919-0.001285j
[2025-08-25 19:05:33] [Iter 416/1050] R1[265/300], Temp: 0.0332, Energy: -29.177140-0.001370j
[2025-08-25 19:05:38] [Iter 417/1050] R1[266/300], Temp: 0.0314, Energy: -29.170069-0.000687j
[2025-08-25 19:05:44] [Iter 418/1050] R1[267/300], Temp: 0.0296, Energy: -29.162766-0.001637j
[2025-08-25 19:05:50] [Iter 419/1050] R1[268/300], Temp: 0.0278, Energy: -29.174251+0.003468j
[2025-08-25 19:05:56] [Iter 420/1050] R1[269/300], Temp: 0.0261, Energy: -29.170439+0.000081j
[2025-08-25 19:06:02] [Iter 421/1050] R1[270/300], Temp: 0.0245, Energy: -29.172930+0.001353j
[2025-08-25 19:06:07] [Iter 422/1050] R1[271/300], Temp: 0.0229, Energy: -29.169335+0.001017j
[2025-08-25 19:06:13] [Iter 423/1050] R1[272/300], Temp: 0.0213, Energy: -29.187426+0.000818j
[2025-08-25 19:06:19] [Iter 424/1050] R1[273/300], Temp: 0.0199, Energy: -29.179193+0.000966j
[2025-08-25 19:06:25] [Iter 425/1050] R1[274/300], Temp: 0.0184, Energy: -29.176419-0.000122j
[2025-08-25 19:06:30] [Iter 426/1050] R1[275/300], Temp: 0.0170, Energy: -29.161096+0.001360j
[2025-08-25 19:06:36] [Iter 427/1050] R1[276/300], Temp: 0.0157, Energy: -29.163453+0.001035j
[2025-08-25 19:06:42] [Iter 428/1050] R1[277/300], Temp: 0.0144, Energy: -29.169022-0.000168j
[2025-08-25 19:06:48] [Iter 429/1050] R1[278/300], Temp: 0.0132, Energy: -29.176040-0.000166j
[2025-08-25 19:06:53] [Iter 430/1050] R1[279/300], Temp: 0.0120, Energy: -29.164474-0.000103j
[2025-08-25 19:06:59] [Iter 431/1050] R1[280/300], Temp: 0.0109, Energy: -29.165620-0.001164j
[2025-08-25 19:07:05] [Iter 432/1050] R1[281/300], Temp: 0.0099, Energy: -29.153226+0.003378j
[2025-08-25 19:07:11] [Iter 433/1050] R1[282/300], Temp: 0.0089, Energy: -29.169863+0.001272j
[2025-08-25 19:07:16] [Iter 434/1050] R1[283/300], Temp: 0.0079, Energy: -29.165317+0.001056j
[2025-08-25 19:07:22] [Iter 435/1050] R1[284/300], Temp: 0.0070, Energy: -29.166959+0.002953j
[2025-08-25 19:07:28] [Iter 436/1050] R1[285/300], Temp: 0.0062, Energy: -29.184442+0.001152j
[2025-08-25 19:07:34] [Iter 437/1050] R1[286/300], Temp: 0.0054, Energy: -29.167203+0.003848j
[2025-08-25 19:07:39] [Iter 438/1050] R1[287/300], Temp: 0.0046, Energy: -29.178197-0.000705j
[2025-08-25 19:07:45] [Iter 439/1050] R1[288/300], Temp: 0.0039, Energy: -29.165080+0.000938j
[2025-08-25 19:07:51] [Iter 440/1050] R1[289/300], Temp: 0.0033, Energy: -29.175452-0.003431j
[2025-08-25 19:07:57] [Iter 441/1050] R1[290/300], Temp: 0.0027, Energy: -29.172726+0.001754j
[2025-08-25 19:08:02] [Iter 442/1050] R1[291/300], Temp: 0.0022, Energy: -29.180665-0.002347j
[2025-08-25 19:08:08] [Iter 443/1050] R1[292/300], Temp: 0.0018, Energy: -29.174014+0.001233j
[2025-08-25 19:08:14] [Iter 444/1050] R1[293/300], Temp: 0.0013, Energy: -29.159505+0.001908j
[2025-08-25 19:08:20] [Iter 445/1050] R1[294/300], Temp: 0.0010, Energy: -29.156344+0.001423j
[2025-08-25 19:08:25] [Iter 446/1050] R1[295/300], Temp: 0.0007, Energy: -29.170850-0.003252j
[2025-08-25 19:08:31] [Iter 447/1050] R1[296/300], Temp: 0.0004, Energy: -29.157630-0.001339j
[2025-08-25 19:08:37] [Iter 448/1050] R1[297/300], Temp: 0.0002, Energy: -29.178949+0.000694j
[2025-08-25 19:08:43] [Iter 449/1050] R1[298/300], Temp: 0.0001, Energy: -29.160857-0.001255j
[2025-08-25 19:08:48] [Iter 450/1050] R1[299/300], Temp: 0.0000, Energy: -29.173110-0.000048j
[2025-08-25 19:08:48] RESTART #2 | Period: 600
[2025-08-25 19:08:54] [Iter 451/1050] R2[0/600], Temp: 1.0000, Energy: -29.183801+0.003721j
[2025-08-25 19:09:00] [Iter 452/1050] R2[1/600], Temp: 1.0000, Energy: -29.168220-0.000068j
[2025-08-25 19:09:06] [Iter 453/1050] R2[2/600], Temp: 1.0000, Energy: -29.168406-0.002370j
[2025-08-25 19:09:11] [Iter 454/1050] R2[3/600], Temp: 0.9999, Energy: -29.167350-0.002809j
[2025-08-25 19:09:17] [Iter 455/1050] R2[4/600], Temp: 0.9999, Energy: -29.174733+0.003317j
[2025-08-25 19:09:23] [Iter 456/1050] R2[5/600], Temp: 0.9998, Energy: -29.181401+0.002107j
[2025-08-25 19:09:29] [Iter 457/1050] R2[6/600], Temp: 0.9998, Energy: -29.159346+0.002291j
[2025-08-25 19:09:35] [Iter 458/1050] R2[7/600], Temp: 0.9997, Energy: -29.177248+0.002024j
[2025-08-25 19:09:40] [Iter 459/1050] R2[8/600], Temp: 0.9996, Energy: -29.169162-0.004343j
[2025-08-25 19:09:46] [Iter 460/1050] R2[9/600], Temp: 0.9994, Energy: -29.170977+0.001411j
[2025-08-25 19:09:52] [Iter 461/1050] R2[10/600], Temp: 0.9993, Energy: -29.165848-0.001610j
[2025-08-25 19:09:58] [Iter 462/1050] R2[11/600], Temp: 0.9992, Energy: -29.184429+0.000906j
[2025-08-25 19:10:03] [Iter 463/1050] R2[12/600], Temp: 0.9990, Energy: -29.165359-0.002532j
[2025-08-25 19:10:09] [Iter 464/1050] R2[13/600], Temp: 0.9988, Energy: -29.167158+0.000465j
[2025-08-25 19:10:15] [Iter 465/1050] R2[14/600], Temp: 0.9987, Energy: -29.179233-0.000417j
[2025-08-25 19:10:21] [Iter 466/1050] R2[15/600], Temp: 0.9985, Energy: -29.173077+0.000935j
[2025-08-25 19:10:26] [Iter 467/1050] R2[16/600], Temp: 0.9982, Energy: -29.174300-0.001388j
[2025-08-25 19:10:32] [Iter 468/1050] R2[17/600], Temp: 0.9980, Energy: -29.177446+0.003726j
[2025-08-25 19:10:38] [Iter 469/1050] R2[18/600], Temp: 0.9978, Energy: -29.170828-0.000623j
[2025-08-25 19:10:44] [Iter 470/1050] R2[19/600], Temp: 0.9975, Energy: -29.172897+0.001181j
[2025-08-25 19:10:49] [Iter 471/1050] R2[20/600], Temp: 0.9973, Energy: -29.172023-0.001716j
[2025-08-25 19:10:55] [Iter 472/1050] R2[21/600], Temp: 0.9970, Energy: -29.163034+0.001403j
[2025-08-25 19:11:01] [Iter 473/1050] R2[22/600], Temp: 0.9967, Energy: -29.164826-0.001644j
[2025-08-25 19:11:07] [Iter 474/1050] R2[23/600], Temp: 0.9964, Energy: -29.170588-0.003668j
[2025-08-25 19:11:12] [Iter 475/1050] R2[24/600], Temp: 0.9961, Energy: -29.180944-0.001717j
[2025-08-25 19:11:18] [Iter 476/1050] R2[25/600], Temp: 0.9957, Energy: -29.175993+0.002060j
[2025-08-25 19:11:24] [Iter 477/1050] R2[26/600], Temp: 0.9954, Energy: -29.178994-0.000920j
[2025-08-25 19:11:30] [Iter 478/1050] R2[27/600], Temp: 0.9950, Energy: -29.160377-0.001992j
[2025-08-25 19:11:35] [Iter 479/1050] R2[28/600], Temp: 0.9946, Energy: -29.161121+0.000483j
[2025-08-25 19:11:41] [Iter 480/1050] R2[29/600], Temp: 0.9942, Energy: -29.172708+0.006359j
[2025-08-25 19:11:47] [Iter 481/1050] R2[30/600], Temp: 0.9938, Energy: -29.170113-0.002913j
[2025-08-25 19:11:53] [Iter 482/1050] R2[31/600], Temp: 0.9934, Energy: -29.169287-0.000947j
[2025-08-25 19:11:58] [Iter 483/1050] R2[32/600], Temp: 0.9930, Energy: -29.173672+0.000389j
[2025-08-25 19:12:04] [Iter 484/1050] R2[33/600], Temp: 0.9926, Energy: -29.172746+0.000765j
[2025-08-25 19:12:10] [Iter 485/1050] R2[34/600], Temp: 0.9921, Energy: -29.167204-0.001642j
[2025-08-25 19:12:16] [Iter 486/1050] R2[35/600], Temp: 0.9916, Energy: -29.176943+0.001494j
[2025-08-25 19:12:21] [Iter 487/1050] R2[36/600], Temp: 0.9911, Energy: -29.157710-0.006144j
[2025-08-25 19:12:27] [Iter 488/1050] R2[37/600], Temp: 0.9906, Energy: -29.173568-0.000533j
[2025-08-25 19:12:33] [Iter 489/1050] R2[38/600], Temp: 0.9901, Energy: -29.168739+0.001336j
[2025-08-25 19:12:39] [Iter 490/1050] R2[39/600], Temp: 0.9896, Energy: -29.173442-0.000273j
[2025-08-25 19:12:44] [Iter 491/1050] R2[40/600], Temp: 0.9891, Energy: -29.172429-0.001594j
[2025-08-25 19:12:50] [Iter 492/1050] R2[41/600], Temp: 0.9885, Energy: -29.179414+0.000717j
[2025-08-25 19:12:56] [Iter 493/1050] R2[42/600], Temp: 0.9880, Energy: -29.159214+0.000899j
[2025-08-25 19:13:02] [Iter 494/1050] R2[43/600], Temp: 0.9874, Energy: -29.172427+0.001137j
[2025-08-25 19:13:08] [Iter 495/1050] R2[44/600], Temp: 0.9868, Energy: -29.175173-0.000344j
[2025-08-25 19:13:13] [Iter 496/1050] R2[45/600], Temp: 0.9862, Energy: -29.167320+0.000266j
[2025-08-25 19:13:19] [Iter 497/1050] R2[46/600], Temp: 0.9856, Energy: -29.166674+0.001212j
[2025-08-25 19:13:25] [Iter 498/1050] R2[47/600], Temp: 0.9849, Energy: -29.159431-0.000777j
[2025-08-25 19:13:31] [Iter 499/1050] R2[48/600], Temp: 0.9843, Energy: -29.176050+0.001669j
[2025-08-25 19:13:36] [Iter 500/1050] R2[49/600], Temp: 0.9836, Energy: -29.179617-0.001063j
[2025-08-25 19:13:36] ✓ Checkpoint saved: checkpoint_iter_000500.pkl
[2025-08-25 19:13:42] [Iter 501/1050] R2[50/600], Temp: 0.9830, Energy: -29.172059-0.002122j
[2025-08-25 19:13:48] [Iter 502/1050] R2[51/600], Temp: 0.9823, Energy: -29.159718-0.003055j
[2025-08-25 19:13:54] [Iter 503/1050] R2[52/600], Temp: 0.9816, Energy: -29.169106-0.001370j
[2025-08-25 19:13:59] [Iter 504/1050] R2[53/600], Temp: 0.9809, Energy: -29.168589-0.002635j
[2025-08-25 19:14:05] [Iter 505/1050] R2[54/600], Temp: 0.9801, Energy: -29.172464-0.005270j
[2025-08-25 19:14:11] [Iter 506/1050] R2[55/600], Temp: 0.9794, Energy: -29.161828-0.000320j
[2025-08-25 19:14:17] [Iter 507/1050] R2[56/600], Temp: 0.9787, Energy: -29.179603+0.002070j
[2025-08-25 19:14:22] [Iter 508/1050] R2[57/600], Temp: 0.9779, Energy: -29.173179+0.001193j
[2025-08-25 19:14:28] [Iter 509/1050] R2[58/600], Temp: 0.9771, Energy: -29.169967+0.000946j
[2025-08-25 19:14:34] [Iter 510/1050] R2[59/600], Temp: 0.9763, Energy: -29.164810+0.001035j
[2025-08-25 19:14:40] [Iter 511/1050] R2[60/600], Temp: 0.9755, Energy: -29.161519-0.001165j
[2025-08-25 19:14:45] [Iter 512/1050] R2[61/600], Temp: 0.9747, Energy: -29.160605+0.000351j
[2025-08-25 19:14:51] [Iter 513/1050] R2[62/600], Temp: 0.9739, Energy: -29.169339-0.002867j
[2025-08-25 19:14:57] [Iter 514/1050] R2[63/600], Temp: 0.9730, Energy: -29.168421+0.003330j
[2025-08-25 19:15:03] [Iter 515/1050] R2[64/600], Temp: 0.9722, Energy: -29.167144-0.001081j
[2025-08-25 19:15:08] [Iter 516/1050] R2[65/600], Temp: 0.9713, Energy: -29.173775-0.004010j
[2025-08-25 19:15:14] [Iter 517/1050] R2[66/600], Temp: 0.9704, Energy: -29.174830-0.004312j
[2025-08-25 19:15:20] [Iter 518/1050] R2[67/600], Temp: 0.9695, Energy: -29.169323-0.000811j
[2025-08-25 19:15:26] [Iter 519/1050] R2[68/600], Temp: 0.9686, Energy: -29.173551+0.002589j
[2025-08-25 19:15:31] [Iter 520/1050] R2[69/600], Temp: 0.9677, Energy: -29.171488-0.002939j
[2025-08-25 19:15:37] [Iter 521/1050] R2[70/600], Temp: 0.9668, Energy: -29.165999+0.000746j
[2025-08-25 19:15:43] [Iter 522/1050] R2[71/600], Temp: 0.9658, Energy: -29.177663-0.001611j
[2025-08-25 19:15:49] [Iter 523/1050] R2[72/600], Temp: 0.9649, Energy: -29.172820-0.000674j
[2025-08-25 19:15:55] [Iter 524/1050] R2[73/600], Temp: 0.9639, Energy: -29.169035+0.001500j
[2025-08-25 19:16:00] [Iter 525/1050] R2[74/600], Temp: 0.9629, Energy: -29.171639+0.001717j
[2025-08-25 19:16:06] [Iter 526/1050] R2[75/600], Temp: 0.9619, Energy: -29.177026+0.000813j
[2025-08-25 19:16:12] [Iter 527/1050] R2[76/600], Temp: 0.9609, Energy: -29.178431+0.000308j
[2025-08-25 19:16:18] [Iter 528/1050] R2[77/600], Temp: 0.9599, Energy: -29.167182-0.000514j
[2025-08-25 19:16:23] [Iter 529/1050] R2[78/600], Temp: 0.9589, Energy: -29.161800-0.001731j
[2025-08-25 19:16:29] [Iter 530/1050] R2[79/600], Temp: 0.9578, Energy: -29.160297+0.000041j
[2025-08-25 19:16:35] [Iter 531/1050] R2[80/600], Temp: 0.9568, Energy: -29.164222+0.004485j
[2025-08-25 19:16:41] [Iter 532/1050] R2[81/600], Temp: 0.9557, Energy: -29.166117+0.001030j
[2025-08-25 19:16:46] [Iter 533/1050] R2[82/600], Temp: 0.9546, Energy: -29.174490+0.000086j
[2025-08-25 19:16:52] [Iter 534/1050] R2[83/600], Temp: 0.9535, Energy: -29.170502-0.003619j
[2025-08-25 19:16:58] [Iter 535/1050] R2[84/600], Temp: 0.9524, Energy: -29.170234+0.001609j
[2025-08-25 19:17:04] [Iter 536/1050] R2[85/600], Temp: 0.9513, Energy: -29.161519+0.000001j
[2025-08-25 19:17:09] [Iter 537/1050] R2[86/600], Temp: 0.9502, Energy: -29.173655-0.004950j
[2025-08-25 19:17:15] [Iter 538/1050] R2[87/600], Temp: 0.9490, Energy: -29.185741+0.003722j
[2025-08-25 19:17:21] [Iter 539/1050] R2[88/600], Temp: 0.9479, Energy: -29.175676+0.001042j
[2025-08-25 19:17:26] [Iter 540/1050] R2[89/600], Temp: 0.9467, Energy: -29.172164+0.001387j
[2025-08-25 19:17:32] [Iter 541/1050] R2[90/600], Temp: 0.9455, Energy: -29.176351+0.001729j
[2025-08-25 19:17:37] [Iter 542/1050] R2[91/600], Temp: 0.9443, Energy: -29.160617+0.001893j
[2025-08-25 19:17:43] [Iter 543/1050] R2[92/600], Temp: 0.9431, Energy: -29.168930-0.001571j
[2025-08-25 19:17:49] [Iter 544/1050] R2[93/600], Temp: 0.9419, Energy: -29.178087+0.003317j
[2025-08-25 19:17:55] [Iter 545/1050] R2[94/600], Temp: 0.9407, Energy: -29.168987+0.001821j
[2025-08-25 19:18:00] [Iter 546/1050] R2[95/600], Temp: 0.9394, Energy: -29.177775-0.000075j
[2025-08-25 19:18:06] [Iter 547/1050] R2[96/600], Temp: 0.9382, Energy: -29.173560-0.000029j
[2025-08-25 19:18:12] [Iter 548/1050] R2[97/600], Temp: 0.9369, Energy: -29.174691-0.001082j
[2025-08-25 19:18:18] [Iter 549/1050] R2[98/600], Temp: 0.9356, Energy: -29.168245-0.000340j
[2025-08-25 19:18:24] [Iter 550/1050] R2[99/600], Temp: 0.9343, Energy: -29.177824-0.000365j
[2025-08-25 19:18:29] [Iter 551/1050] R2[100/600], Temp: 0.9330, Energy: -29.169802+0.001105j
[2025-08-25 19:18:35] [Iter 552/1050] R2[101/600], Temp: 0.9317, Energy: -29.167600+0.000685j
[2025-08-25 19:18:41] [Iter 553/1050] R2[102/600], Temp: 0.9304, Energy: -29.177214-0.006351j
[2025-08-25 19:18:47] [Iter 554/1050] R2[103/600], Temp: 0.9290, Energy: -29.173439-0.004445j
[2025-08-25 19:18:52] [Iter 555/1050] R2[104/600], Temp: 0.9277, Energy: -29.180657-0.001879j
[2025-08-25 19:18:58] [Iter 556/1050] R2[105/600], Temp: 0.9263, Energy: -29.174258-0.000364j
[2025-08-25 19:19:04] [Iter 557/1050] R2[106/600], Temp: 0.9249, Energy: -29.170710-0.003725j
[2025-08-25 19:19:10] [Iter 558/1050] R2[107/600], Temp: 0.9236, Energy: -29.179569+0.002479j
[2025-08-25 19:19:15] [Iter 559/1050] R2[108/600], Temp: 0.9222, Energy: -29.162710-0.002669j
[2025-08-25 19:19:21] [Iter 560/1050] R2[109/600], Temp: 0.9208, Energy: -29.172533-0.002042j
[2025-08-25 19:19:27] [Iter 561/1050] R2[110/600], Temp: 0.9193, Energy: -29.169130+0.000540j
[2025-08-25 19:19:33] [Iter 562/1050] R2[111/600], Temp: 0.9179, Energy: -29.179921+0.000351j
[2025-08-25 19:19:38] [Iter 563/1050] R2[112/600], Temp: 0.9165, Energy: -29.183282-0.000895j
[2025-08-25 19:19:44] [Iter 564/1050] R2[113/600], Temp: 0.9150, Energy: -29.164847+0.002313j
[2025-08-25 19:19:50] [Iter 565/1050] R2[114/600], Temp: 0.9135, Energy: -29.161031-0.002718j
[2025-08-25 19:19:56] [Iter 566/1050] R2[115/600], Temp: 0.9121, Energy: -29.172416-0.003018j
[2025-08-25 19:20:02] [Iter 567/1050] R2[116/600], Temp: 0.9106, Energy: -29.179289+0.000763j
[2025-08-25 19:20:07] [Iter 568/1050] R2[117/600], Temp: 0.9091, Energy: -29.176849-0.001976j
[2025-08-25 19:20:13] [Iter 569/1050] R2[118/600], Temp: 0.9076, Energy: -29.174627+0.000482j
[2025-08-25 19:20:19] [Iter 570/1050] R2[119/600], Temp: 0.9060, Energy: -29.181061+0.002652j
[2025-08-25 19:20:25] [Iter 571/1050] R2[120/600], Temp: 0.9045, Energy: -29.167497-0.000950j
[2025-08-25 19:20:30] [Iter 572/1050] R2[121/600], Temp: 0.9030, Energy: -29.181935-0.002092j
[2025-08-25 19:20:36] [Iter 573/1050] R2[122/600], Temp: 0.9014, Energy: -29.167691-0.001022j
[2025-08-25 19:20:42] [Iter 574/1050] R2[123/600], Temp: 0.8998, Energy: -29.171772-0.002286j
[2025-08-25 19:20:48] [Iter 575/1050] R2[124/600], Temp: 0.8983, Energy: -29.157599-0.003832j
[2025-08-25 19:20:54] [Iter 576/1050] R2[125/600], Temp: 0.8967, Energy: -29.164781-0.000598j
[2025-08-25 19:20:59] [Iter 577/1050] R2[126/600], Temp: 0.8951, Energy: -29.172375-0.003637j
[2025-08-25 19:21:05] [Iter 578/1050] R2[127/600], Temp: 0.8935, Energy: -29.170697+0.000097j
[2025-08-25 19:21:11] [Iter 579/1050] R2[128/600], Temp: 0.8918, Energy: -29.176124-0.001695j
[2025-08-25 19:21:17] [Iter 580/1050] R2[129/600], Temp: 0.8902, Energy: -29.179934+0.002231j
[2025-08-25 19:21:23] [Iter 581/1050] R2[130/600], Temp: 0.8886, Energy: -29.169863+0.001943j
[2025-08-25 19:21:28] [Iter 582/1050] R2[131/600], Temp: 0.8869, Energy: -29.175344+0.000008j
[2025-08-25 19:21:34] [Iter 583/1050] R2[132/600], Temp: 0.8853, Energy: -29.169936-0.003307j
[2025-08-25 19:21:40] [Iter 584/1050] R2[133/600], Temp: 0.8836, Energy: -29.172621-0.001765j
[2025-08-25 19:21:46] [Iter 585/1050] R2[134/600], Temp: 0.8819, Energy: -29.168031-0.003319j
[2025-08-25 19:21:52] [Iter 586/1050] R2[135/600], Temp: 0.8802, Energy: -29.170064+0.001020j
[2025-08-25 19:21:57] [Iter 587/1050] R2[136/600], Temp: 0.8785, Energy: -29.170837+0.002387j
[2025-08-25 19:22:03] [Iter 588/1050] R2[137/600], Temp: 0.8768, Energy: -29.164905-0.001790j
[2025-08-25 19:22:09] [Iter 589/1050] R2[138/600], Temp: 0.8751, Energy: -29.166787+0.000773j
[2025-08-25 19:22:15] [Iter 590/1050] R2[139/600], Temp: 0.8733, Energy: -29.165094-0.000496j
[2025-08-25 19:22:21] [Iter 591/1050] R2[140/600], Temp: 0.8716, Energy: -29.174981+0.002447j
[2025-08-25 19:22:26] [Iter 592/1050] R2[141/600], Temp: 0.8698, Energy: -29.174497-0.001820j
[2025-08-25 19:22:32] [Iter 593/1050] R2[142/600], Temp: 0.8680, Energy: -29.177873-0.001396j
[2025-08-25 19:22:38] [Iter 594/1050] R2[143/600], Temp: 0.8663, Energy: -29.171151+0.002926j
[2025-08-25 19:22:44] [Iter 595/1050] R2[144/600], Temp: 0.8645, Energy: -29.164990+0.004266j
[2025-08-25 19:22:49] [Iter 596/1050] R2[145/600], Temp: 0.8627, Energy: -29.170705-0.000756j
[2025-08-25 19:22:55] [Iter 597/1050] R2[146/600], Temp: 0.8609, Energy: -29.165992+0.000102j
[2025-08-25 19:23:01] [Iter 598/1050] R2[147/600], Temp: 0.8591, Energy: -29.175241-0.001018j
[2025-08-25 19:23:07] [Iter 599/1050] R2[148/600], Temp: 0.8572, Energy: -29.166497-0.000826j
[2025-08-25 19:23:13] [Iter 600/1050] R2[149/600], Temp: 0.8554, Energy: -29.156978-0.003433j
[2025-08-25 19:23:13] ✓ Checkpoint saved: checkpoint_iter_000600.pkl
[2025-08-25 19:23:18] [Iter 601/1050] R2[150/600], Temp: 0.8536, Energy: -29.167508+0.002345j
[2025-08-25 19:23:24] [Iter 602/1050] R2[151/600], Temp: 0.8517, Energy: -29.185071-0.000242j
[2025-08-25 19:23:30] [Iter 603/1050] R2[152/600], Temp: 0.8498, Energy: -29.172801-0.001602j
[2025-08-25 19:23:36] [Iter 604/1050] R2[153/600], Temp: 0.8480, Energy: -29.163799+0.001925j
[2025-08-25 19:23:42] [Iter 605/1050] R2[154/600], Temp: 0.8461, Energy: -29.172518+0.002613j
[2025-08-25 19:23:47] [Iter 606/1050] R2[155/600], Temp: 0.8442, Energy: -29.179519-0.002192j
[2025-08-25 19:23:53] [Iter 607/1050] R2[156/600], Temp: 0.8423, Energy: -29.165926+0.000522j
[2025-08-25 19:23:59] [Iter 608/1050] R2[157/600], Temp: 0.8404, Energy: -29.164015-0.000798j
[2025-08-25 19:24:05] [Iter 609/1050] R2[158/600], Temp: 0.8384, Energy: -29.170203-0.001488j
[2025-08-25 19:24:10] [Iter 610/1050] R2[159/600], Temp: 0.8365, Energy: -29.175200-0.002905j
[2025-08-25 19:24:16] [Iter 611/1050] R2[160/600], Temp: 0.8346, Energy: -29.168692+0.002436j
[2025-08-25 19:24:22] [Iter 612/1050] R2[161/600], Temp: 0.8326, Energy: -29.164190+0.000855j
[2025-08-25 19:24:28] [Iter 613/1050] R2[162/600], Temp: 0.8307, Energy: -29.168063+0.001665j
[2025-08-25 19:24:34] [Iter 614/1050] R2[163/600], Temp: 0.8287, Energy: -29.171046+0.000362j
[2025-08-25 19:24:39] [Iter 615/1050] R2[164/600], Temp: 0.8267, Energy: -29.177391+0.002185j
[2025-08-25 19:24:45] [Iter 616/1050] R2[165/600], Temp: 0.8247, Energy: -29.171560+0.002483j
[2025-08-25 19:24:51] [Iter 617/1050] R2[166/600], Temp: 0.8227, Energy: -29.184409-0.000281j
[2025-08-25 19:24:57] [Iter 618/1050] R2[167/600], Temp: 0.8207, Energy: -29.163703+0.002819j
[2025-08-25 19:25:03] [Iter 619/1050] R2[168/600], Temp: 0.8187, Energy: -29.168344-0.002203j
[2025-08-25 19:25:08] [Iter 620/1050] R2[169/600], Temp: 0.8167, Energy: -29.173274-0.002746j
[2025-08-25 19:25:14] [Iter 621/1050] R2[170/600], Temp: 0.8147, Energy: -29.177798-0.000120j
[2025-08-25 19:25:20] [Iter 622/1050] R2[171/600], Temp: 0.8126, Energy: -29.175254-0.002606j
[2025-08-25 19:25:26] [Iter 623/1050] R2[172/600], Temp: 0.8106, Energy: -29.177223-0.002451j
[2025-08-25 19:25:32] [Iter 624/1050] R2[173/600], Temp: 0.8085, Energy: -29.181243-0.002708j
[2025-08-25 19:25:37] [Iter 625/1050] R2[174/600], Temp: 0.8065, Energy: -29.176828-0.003702j
[2025-08-25 19:25:43] [Iter 626/1050] R2[175/600], Temp: 0.8044, Energy: -29.176453-0.001311j
[2025-08-25 19:25:49] [Iter 627/1050] R2[176/600], Temp: 0.8023, Energy: -29.172412-0.001566j
[2025-08-25 19:25:55] [Iter 628/1050] R2[177/600], Temp: 0.8002, Energy: -29.165921+0.002297j
[2025-08-25 19:26:00] [Iter 629/1050] R2[178/600], Temp: 0.7981, Energy: -29.171071+0.000786j
[2025-08-25 19:26:06] [Iter 630/1050] R2[179/600], Temp: 0.7960, Energy: -29.173700-0.002811j
[2025-08-25 19:26:12] [Iter 631/1050] R2[180/600], Temp: 0.7939, Energy: -29.171826+0.002258j
[2025-08-25 19:26:18] [Iter 632/1050] R2[181/600], Temp: 0.7918, Energy: -29.168789-0.001720j
[2025-08-25 19:26:23] [Iter 633/1050] R2[182/600], Temp: 0.7896, Energy: -29.164175+0.002886j
[2025-08-25 19:26:29] [Iter 634/1050] R2[183/600], Temp: 0.7875, Energy: -29.166141+0.002874j
[2025-08-25 19:26:34] [Iter 635/1050] R2[184/600], Temp: 0.7854, Energy: -29.171613+0.001966j
[2025-08-25 19:26:40] [Iter 636/1050] R2[185/600], Temp: 0.7832, Energy: -29.171884-0.000502j
[2025-08-25 19:26:46] [Iter 637/1050] R2[186/600], Temp: 0.7810, Energy: -29.165544+0.000641j
[2025-08-25 19:26:51] [Iter 638/1050] R2[187/600], Temp: 0.7789, Energy: -29.176943-0.004526j
[2025-08-25 19:27:00] [Iter 639/1050] R2[188/600], Temp: 0.7767, Energy: -29.175862-0.000939j
[2025-08-25 19:27:05] [Iter 640/1050] R2[189/600], Temp: 0.7745, Energy: -29.170563-0.000978j
[2025-08-25 19:27:12] [Iter 641/1050] R2[190/600], Temp: 0.7723, Energy: -29.163409-0.000802j
[2025-08-25 19:27:18] [Iter 642/1050] R2[191/600], Temp: 0.7701, Energy: -29.171139-0.001548j
[2025-08-25 19:27:24] [Iter 643/1050] R2[192/600], Temp: 0.7679, Energy: -29.171680+0.001416j
[2025-08-25 19:27:29] [Iter 644/1050] R2[193/600], Temp: 0.7657, Energy: -29.175381+0.000712j
[2025-08-25 19:27:36] [Iter 645/1050] R2[194/600], Temp: 0.7635, Energy: -29.162373+0.002476j
[2025-08-25 19:27:41] [Iter 646/1050] R2[195/600], Temp: 0.7612, Energy: -29.163308+0.001158j
[2025-08-25 19:27:47] [Iter 647/1050] R2[196/600], Temp: 0.7590, Energy: -29.176954+0.000333j
[2025-08-25 19:27:53] [Iter 648/1050] R2[197/600], Temp: 0.7568, Energy: -29.169383+0.000466j
[2025-08-25 19:27:59] [Iter 649/1050] R2[198/600], Temp: 0.7545, Energy: -29.168740+0.001524j
[2025-08-25 19:28:05] [Iter 650/1050] R2[199/600], Temp: 0.7523, Energy: -29.173645-0.001522j
[2025-08-25 19:28:10] [Iter 651/1050] R2[200/600], Temp: 0.7500, Energy: -29.179855+0.001260j
[2025-08-25 19:28:16] [Iter 652/1050] R2[201/600], Temp: 0.7477, Energy: -29.175556-0.002856j
[2025-08-25 19:28:22] [Iter 653/1050] R2[202/600], Temp: 0.7455, Energy: -29.168396+0.002438j
[2025-08-25 19:28:28] [Iter 654/1050] R2[203/600], Temp: 0.7432, Energy: -29.174827+0.000414j
[2025-08-25 19:28:33] [Iter 655/1050] R2[204/600], Temp: 0.7409, Energy: -29.172350-0.002968j
[2025-08-25 19:28:39] [Iter 656/1050] R2[205/600], Temp: 0.7386, Energy: -29.175738+0.002774j
[2025-08-25 19:28:45] [Iter 657/1050] R2[206/600], Temp: 0.7363, Energy: -29.182194-0.000604j
[2025-08-25 19:28:51] [Iter 658/1050] R2[207/600], Temp: 0.7340, Energy: -29.173257+0.000892j
[2025-08-25 19:28:57] [Iter 659/1050] R2[208/600], Temp: 0.7316, Energy: -29.175279+0.001115j
[2025-08-25 19:29:02] [Iter 660/1050] R2[209/600], Temp: 0.7293, Energy: -29.175543-0.000425j
[2025-08-25 19:29:08] [Iter 661/1050] R2[210/600], Temp: 0.7270, Energy: -29.169758+0.001773j
[2025-08-25 19:29:14] [Iter 662/1050] R2[211/600], Temp: 0.7247, Energy: -29.171279+0.002721j
[2025-08-25 19:29:20] [Iter 663/1050] R2[212/600], Temp: 0.7223, Energy: -29.167373+0.003824j
[2025-08-25 19:29:26] [Iter 664/1050] R2[213/600], Temp: 0.7200, Energy: -29.176358-0.000111j
[2025-08-25 19:29:31] [Iter 665/1050] R2[214/600], Temp: 0.7176, Energy: -29.178297+0.003837j
[2025-08-25 19:29:37] [Iter 666/1050] R2[215/600], Temp: 0.7153, Energy: -29.155362-0.001288j
[2025-08-25 19:29:43] [Iter 667/1050] R2[216/600], Temp: 0.7129, Energy: -29.180590-0.000403j
[2025-08-25 19:29:49] [Iter 668/1050] R2[217/600], Temp: 0.7105, Energy: -29.167275-0.000692j
[2025-08-25 19:29:55] [Iter 669/1050] R2[218/600], Temp: 0.7081, Energy: -29.182261+0.001910j
[2025-08-25 19:30:00] [Iter 670/1050] R2[219/600], Temp: 0.7058, Energy: -29.171313-0.003980j
[2025-08-25 19:30:06] [Iter 671/1050] R2[220/600], Temp: 0.7034, Energy: -29.173410+0.002809j
[2025-08-25 19:30:12] [Iter 672/1050] R2[221/600], Temp: 0.7010, Energy: -29.166319+0.001112j
[2025-08-25 19:30:18] [Iter 673/1050] R2[222/600], Temp: 0.6986, Energy: -29.162681-0.002701j
[2025-08-25 19:30:23] [Iter 674/1050] R2[223/600], Temp: 0.6962, Energy: -29.166304+0.002653j
[2025-08-25 19:30:29] [Iter 675/1050] R2[224/600], Temp: 0.6938, Energy: -29.174066+0.001812j
[2025-08-25 19:30:35] [Iter 676/1050] R2[225/600], Temp: 0.6913, Energy: -29.170694-0.001033j
[2025-08-25 19:30:41] [Iter 677/1050] R2[226/600], Temp: 0.6889, Energy: -29.167081-0.000381j
[2025-08-25 19:30:47] [Iter 678/1050] R2[227/600], Temp: 0.6865, Energy: -29.168216-0.000531j
[2025-08-25 19:30:52] [Iter 679/1050] R2[228/600], Temp: 0.6841, Energy: -29.169332-0.002053j
[2025-08-25 19:30:58] [Iter 680/1050] R2[229/600], Temp: 0.6816, Energy: -29.172279-0.002976j
[2025-08-25 19:31:04] [Iter 681/1050] R2[230/600], Temp: 0.6792, Energy: -29.174942-0.001109j
[2025-08-25 19:31:10] [Iter 682/1050] R2[231/600], Temp: 0.6767, Energy: -29.173344+0.000595j
[2025-08-25 19:31:16] [Iter 683/1050] R2[232/600], Temp: 0.6743, Energy: -29.173110-0.000056j
[2025-08-25 19:31:21] [Iter 684/1050] R2[233/600], Temp: 0.6718, Energy: -29.184850-0.001121j
[2025-08-25 19:31:27] [Iter 685/1050] R2[234/600], Temp: 0.6694, Energy: -29.165874-0.000147j
[2025-08-25 19:31:33] [Iter 686/1050] R2[235/600], Temp: 0.6669, Energy: -29.171186+0.002488j
[2025-08-25 19:31:39] [Iter 687/1050] R2[236/600], Temp: 0.6644, Energy: -29.174296+0.003984j
[2025-08-25 19:31:45] [Iter 688/1050] R2[237/600], Temp: 0.6620, Energy: -29.171920-0.000343j
[2025-08-25 19:31:50] [Iter 689/1050] R2[238/600], Temp: 0.6595, Energy: -29.165801-0.001391j
[2025-08-25 19:31:56] [Iter 690/1050] R2[239/600], Temp: 0.6570, Energy: -29.167430+0.001076j
[2025-08-25 19:32:02] [Iter 691/1050] R2[240/600], Temp: 0.6545, Energy: -29.169195+0.000364j
[2025-08-25 19:32:08] [Iter 692/1050] R2[241/600], Temp: 0.6520, Energy: -29.187100-0.000715j
[2025-08-25 19:32:14] [Iter 693/1050] R2[242/600], Temp: 0.6495, Energy: -29.173130+0.001095j
[2025-08-25 19:32:19] [Iter 694/1050] R2[243/600], Temp: 0.6470, Energy: -29.165080-0.001551j
[2025-08-25 19:32:25] [Iter 695/1050] R2[244/600], Temp: 0.6445, Energy: -29.181398+0.003641j
[2025-08-25 19:32:31] [Iter 696/1050] R2[245/600], Temp: 0.6420, Energy: -29.174533-0.002379j
[2025-08-25 19:32:37] [Iter 697/1050] R2[246/600], Temp: 0.6395, Energy: -29.161274-0.004189j
[2025-08-25 19:32:43] [Iter 698/1050] R2[247/600], Temp: 0.6370, Energy: -29.174221+0.005104j
[2025-08-25 19:32:48] [Iter 699/1050] R2[248/600], Temp: 0.6345, Energy: -29.166509+0.000266j
[2025-08-25 19:32:54] [Iter 700/1050] R2[249/600], Temp: 0.6319, Energy: -29.171221+0.000281j
[2025-08-25 19:32:54] ✓ Checkpoint saved: checkpoint_iter_000700.pkl
[2025-08-25 19:33:00] [Iter 701/1050] R2[250/600], Temp: 0.6294, Energy: -29.176521+0.002941j
[2025-08-25 19:33:06] [Iter 702/1050] R2[251/600], Temp: 0.6269, Energy: -29.159789-0.000440j
[2025-08-25 19:33:11] [Iter 703/1050] R2[252/600], Temp: 0.6243, Energy: -29.173522-0.000273j
[2025-08-25 19:33:17] [Iter 704/1050] R2[253/600], Temp: 0.6218, Energy: -29.172569+0.003762j
[2025-08-25 19:33:23] [Iter 705/1050] R2[254/600], Temp: 0.6193, Energy: -29.167498+0.000565j
[2025-08-25 19:33:29] [Iter 706/1050] R2[255/600], Temp: 0.6167, Energy: -29.176688+0.000786j
[2025-08-25 19:33:35] [Iter 707/1050] R2[256/600], Temp: 0.6142, Energy: -29.175559-0.001168j
[2025-08-25 19:33:40] [Iter 708/1050] R2[257/600], Temp: 0.6116, Energy: -29.173207-0.002265j
[2025-08-25 19:33:46] [Iter 709/1050] R2[258/600], Temp: 0.6091, Energy: -29.171548-0.002472j
[2025-08-25 19:33:52] [Iter 710/1050] R2[259/600], Temp: 0.6065, Energy: -29.172611+0.003175j
[2025-08-25 19:33:58] [Iter 711/1050] R2[260/600], Temp: 0.6040, Energy: -29.171479-0.000431j
[2025-08-25 19:34:04] [Iter 712/1050] R2[261/600], Temp: 0.6014, Energy: -29.168036+0.000284j
[2025-08-25 19:34:09] [Iter 713/1050] R2[262/600], Temp: 0.5988, Energy: -29.174937+0.001900j
[2025-08-25 19:34:15] [Iter 714/1050] R2[263/600], Temp: 0.5963, Energy: -29.168556+0.003746j
[2025-08-25 19:34:21] [Iter 715/1050] R2[264/600], Temp: 0.5937, Energy: -29.164432+0.003328j
[2025-08-25 19:34:27] [Iter 716/1050] R2[265/600], Temp: 0.5911, Energy: -29.177349+0.002502j
[2025-08-25 19:34:33] [Iter 717/1050] R2[266/600], Temp: 0.5885, Energy: -29.173654+0.002313j
[2025-08-25 19:34:38] [Iter 718/1050] R2[267/600], Temp: 0.5860, Energy: -29.173507+0.001644j
[2025-08-25 19:34:44] [Iter 719/1050] R2[268/600], Temp: 0.5834, Energy: -29.173627-0.005918j
[2025-08-25 19:34:50] [Iter 720/1050] R2[269/600], Temp: 0.5808, Energy: -29.164949-0.002488j
[2025-08-25 19:34:56] [Iter 721/1050] R2[270/600], Temp: 0.5782, Energy: -29.169630+0.002833j
[2025-08-25 19:35:01] [Iter 722/1050] R2[271/600], Temp: 0.5756, Energy: -29.172583-0.001525j
[2025-08-25 19:35:07] [Iter 723/1050] R2[272/600], Temp: 0.5730, Energy: -29.163612-0.001438j
[2025-08-25 19:35:13] [Iter 724/1050] R2[273/600], Temp: 0.5705, Energy: -29.170423-0.002662j
[2025-08-25 19:35:19] [Iter 725/1050] R2[274/600], Temp: 0.5679, Energy: -29.176591-0.003685j
[2025-08-25 19:35:25] [Iter 726/1050] R2[275/600], Temp: 0.5653, Energy: -29.165023-0.002281j
[2025-08-25 19:35:30] [Iter 727/1050] R2[276/600], Temp: 0.5627, Energy: -29.162671-0.000193j
[2025-08-25 19:35:36] [Iter 728/1050] R2[277/600], Temp: 0.5601, Energy: -29.167142+0.004270j
[2025-08-25 19:35:42] [Iter 729/1050] R2[278/600], Temp: 0.5575, Energy: -29.176277+0.000320j
[2025-08-25 19:35:48] [Iter 730/1050] R2[279/600], Temp: 0.5549, Energy: -29.173311-0.000681j
[2025-08-25 19:35:54] [Iter 731/1050] R2[280/600], Temp: 0.5523, Energy: -29.172292+0.001433j
[2025-08-25 19:35:59] [Iter 732/1050] R2[281/600], Temp: 0.5497, Energy: -29.173552+0.000135j
[2025-08-25 19:36:05] [Iter 733/1050] R2[282/600], Temp: 0.5471, Energy: -29.169288-0.001388j
[2025-08-25 19:36:11] [Iter 734/1050] R2[283/600], Temp: 0.5444, Energy: -29.163318+0.000576j
[2025-08-25 19:36:17] [Iter 735/1050] R2[284/600], Temp: 0.5418, Energy: -29.161602+0.004028j
[2025-08-25 19:36:23] [Iter 736/1050] R2[285/600], Temp: 0.5392, Energy: -29.165391+0.003700j
[2025-08-25 19:36:28] [Iter 737/1050] R2[286/600], Temp: 0.5366, Energy: -29.174082-0.000043j
[2025-08-25 19:36:34] [Iter 738/1050] R2[287/600], Temp: 0.5340, Energy: -29.177642-0.000772j
[2025-08-25 19:36:40] [Iter 739/1050] R2[288/600], Temp: 0.5314, Energy: -29.163398-0.000985j
[2025-08-25 19:36:46] [Iter 740/1050] R2[289/600], Temp: 0.5288, Energy: -29.173711+0.001044j
[2025-08-25 19:36:51] [Iter 741/1050] R2[290/600], Temp: 0.5262, Energy: -29.164690+0.001033j
[2025-08-25 19:36:57] [Iter 742/1050] R2[291/600], Temp: 0.5236, Energy: -29.167942+0.002118j
[2025-08-25 19:37:03] [Iter 743/1050] R2[292/600], Temp: 0.5209, Energy: -29.180133-0.002670j
[2025-08-25 19:37:09] [Iter 744/1050] R2[293/600], Temp: 0.5183, Energy: -29.158301-0.000733j
[2025-08-25 19:37:15] [Iter 745/1050] R2[294/600], Temp: 0.5157, Energy: -29.175506-0.003429j
[2025-08-25 19:37:20] [Iter 746/1050] R2[295/600], Temp: 0.5131, Energy: -29.176244+0.000306j
[2025-08-25 19:37:26] [Iter 747/1050] R2[296/600], Temp: 0.5105, Energy: -29.165972-0.000739j
[2025-08-25 19:37:32] [Iter 748/1050] R2[297/600], Temp: 0.5079, Energy: -29.173256+0.001182j
[2025-08-25 19:37:38] [Iter 749/1050] R2[298/600], Temp: 0.5052, Energy: -29.170833+0.000393j
[2025-08-25 19:37:44] [Iter 750/1050] R2[299/600], Temp: 0.5026, Energy: -29.177931-0.003371j
[2025-08-25 19:37:49] [Iter 751/1050] R2[300/600], Temp: 0.5000, Energy: -29.173407+0.000439j
[2025-08-25 19:37:55] [Iter 752/1050] R2[301/600], Temp: 0.4974, Energy: -29.170533+0.001488j
[2025-08-25 19:38:01] [Iter 753/1050] R2[302/600], Temp: 0.4948, Energy: -29.170166+0.001829j
[2025-08-25 19:38:07] [Iter 754/1050] R2[303/600], Temp: 0.4921, Energy: -29.162204+0.000051j
[2025-08-25 19:38:12] [Iter 755/1050] R2[304/600], Temp: 0.4895, Energy: -29.172097-0.002833j
[2025-08-25 19:38:18] [Iter 756/1050] R2[305/600], Temp: 0.4869, Energy: -29.166854+0.003078j
[2025-08-25 19:38:24] [Iter 757/1050] R2[306/600], Temp: 0.4843, Energy: -29.167051+0.001125j
[2025-08-25 19:38:30] [Iter 758/1050] R2[307/600], Temp: 0.4817, Energy: -29.180112+0.000864j
[2025-08-25 19:38:36] [Iter 759/1050] R2[308/600], Temp: 0.4791, Energy: -29.169267-0.003531j
[2025-08-25 19:38:41] [Iter 760/1050] R2[309/600], Temp: 0.4764, Energy: -29.165877+0.000596j
[2025-08-25 19:38:47] [Iter 761/1050] R2[310/600], Temp: 0.4738, Energy: -29.172578-0.002155j
[2025-08-25 19:38:53] [Iter 762/1050] R2[311/600], Temp: 0.4712, Energy: -29.182229+0.001906j
[2025-08-25 19:38:59] [Iter 763/1050] R2[312/600], Temp: 0.4686, Energy: -29.164745+0.002705j
[2025-08-25 19:39:05] [Iter 764/1050] R2[313/600], Temp: 0.4660, Energy: -29.168136-0.003090j
[2025-08-25 19:39:10] [Iter 765/1050] R2[314/600], Temp: 0.4634, Energy: -29.172459-0.000670j
[2025-08-25 19:39:16] [Iter 766/1050] R2[315/600], Temp: 0.4608, Energy: -29.168515+0.000162j
[2025-08-25 19:39:22] [Iter 767/1050] R2[316/600], Temp: 0.4582, Energy: -29.162385+0.000718j
[2025-08-25 19:39:28] [Iter 768/1050] R2[317/600], Temp: 0.4556, Energy: -29.169955-0.004286j
[2025-08-25 19:39:34] [Iter 769/1050] R2[318/600], Temp: 0.4529, Energy: -29.160330-0.000467j
[2025-08-25 19:39:39] [Iter 770/1050] R2[319/600], Temp: 0.4503, Energy: -29.180446+0.000792j
[2025-08-25 19:39:45] [Iter 771/1050] R2[320/600], Temp: 0.4477, Energy: -29.164780+0.002801j
[2025-08-25 19:39:51] [Iter 772/1050] R2[321/600], Temp: 0.4451, Energy: -29.173636+0.002239j
[2025-08-25 19:39:57] [Iter 773/1050] R2[322/600], Temp: 0.4425, Energy: -29.181097-0.001003j
[2025-08-25 19:40:03] [Iter 774/1050] R2[323/600], Temp: 0.4399, Energy: -29.176674-0.002018j
[2025-08-25 19:40:08] [Iter 775/1050] R2[324/600], Temp: 0.4373, Energy: -29.172620-0.001828j
[2025-08-25 19:40:14] [Iter 776/1050] R2[325/600], Temp: 0.4347, Energy: -29.169149+0.000310j
[2025-08-25 19:40:20] [Iter 777/1050] R2[326/600], Temp: 0.4321, Energy: -29.181107+0.000898j
[2025-08-25 19:40:26] [Iter 778/1050] R2[327/600], Temp: 0.4295, Energy: -29.180977-0.008591j
[2025-08-25 19:40:31] [Iter 779/1050] R2[328/600], Temp: 0.4270, Energy: -29.176591+0.001526j
[2025-08-25 19:40:37] [Iter 780/1050] R2[329/600], Temp: 0.4244, Energy: -29.165357-0.000047j
[2025-08-25 19:40:43] [Iter 781/1050] R2[330/600], Temp: 0.4218, Energy: -29.178387-0.000054j
[2025-08-25 19:40:49] [Iter 782/1050] R2[331/600], Temp: 0.4192, Energy: -29.186225-0.004312j
[2025-08-25 19:40:55] [Iter 783/1050] R2[332/600], Temp: 0.4166, Energy: -29.176704+0.002935j
[2025-08-25 19:41:00] [Iter 784/1050] R2[333/600], Temp: 0.4140, Energy: -29.180006-0.003385j
[2025-08-25 19:41:06] [Iter 785/1050] R2[334/600], Temp: 0.4115, Energy: -29.164710+0.001827j
[2025-08-25 19:41:12] [Iter 786/1050] R2[335/600], Temp: 0.4089, Energy: -29.181470+0.000256j
[2025-08-25 19:41:18] [Iter 787/1050] R2[336/600], Temp: 0.4063, Energy: -29.180885+0.003130j
[2025-08-25 19:41:24] [Iter 788/1050] R2[337/600], Temp: 0.4037, Energy: -29.167666-0.001715j
[2025-08-25 19:41:29] [Iter 789/1050] R2[338/600], Temp: 0.4012, Energy: -29.167992+0.001062j
[2025-08-25 19:41:35] [Iter 790/1050] R2[339/600], Temp: 0.3986, Energy: -29.164879-0.000134j
[2025-08-25 19:41:41] [Iter 791/1050] R2[340/600], Temp: 0.3960, Energy: -29.173062+0.001230j
[2025-08-25 19:41:47] [Iter 792/1050] R2[341/600], Temp: 0.3935, Energy: -29.169191+0.000803j
[2025-08-25 19:41:53] [Iter 793/1050] R2[342/600], Temp: 0.3909, Energy: -29.173379-0.002694j
[2025-08-25 19:41:58] [Iter 794/1050] R2[343/600], Temp: 0.3884, Energy: -29.176727+0.000265j
[2025-08-25 19:42:04] [Iter 795/1050] R2[344/600], Temp: 0.3858, Energy: -29.174608-0.001887j
[2025-08-25 19:42:10] [Iter 796/1050] R2[345/600], Temp: 0.3833, Energy: -29.167143-0.000694j
[2025-08-25 19:42:16] [Iter 797/1050] R2[346/600], Temp: 0.3807, Energy: -29.169786-0.001651j
[2025-08-25 19:42:22] [Iter 798/1050] R2[347/600], Temp: 0.3782, Energy: -29.183612-0.003733j
[2025-08-25 19:42:27] [Iter 799/1050] R2[348/600], Temp: 0.3757, Energy: -29.180235-0.000951j
[2025-08-25 19:42:33] [Iter 800/1050] R2[349/600], Temp: 0.3731, Energy: -29.170053-0.002079j
[2025-08-25 19:42:33] ✓ Checkpoint saved: checkpoint_iter_000800.pkl
[2025-08-25 19:42:39] [Iter 801/1050] R2[350/600], Temp: 0.3706, Energy: -29.179013-0.001836j
[2025-08-25 19:42:45] [Iter 802/1050] R2[351/600], Temp: 0.3681, Energy: -29.174728-0.000596j
[2025-08-25 19:42:50] [Iter 803/1050] R2[352/600], Temp: 0.3655, Energy: -29.168135+0.000576j
[2025-08-25 19:42:56] [Iter 804/1050] R2[353/600], Temp: 0.3630, Energy: -29.168713+0.003163j
[2025-08-25 19:43:02] [Iter 805/1050] R2[354/600], Temp: 0.3605, Energy: -29.157941+0.000828j
[2025-08-25 19:43:08] [Iter 806/1050] R2[355/600], Temp: 0.3580, Energy: -29.162840+0.005003j
[2025-08-25 19:43:14] [Iter 807/1050] R2[356/600], Temp: 0.3555, Energy: -29.181865+0.001734j
[2025-08-25 19:43:19] [Iter 808/1050] R2[357/600], Temp: 0.3530, Energy: -29.162571+0.000724j
[2025-08-25 19:43:25] [Iter 809/1050] R2[358/600], Temp: 0.3505, Energy: -29.181770-0.000792j
[2025-08-25 19:43:31] [Iter 810/1050] R2[359/600], Temp: 0.3480, Energy: -29.178433-0.002029j
[2025-08-25 19:43:37] [Iter 811/1050] R2[360/600], Temp: 0.3455, Energy: -29.174167-0.001663j
[2025-08-25 19:43:43] [Iter 812/1050] R2[361/600], Temp: 0.3430, Energy: -29.178936+0.002701j
[2025-08-25 19:43:48] [Iter 813/1050] R2[362/600], Temp: 0.3405, Energy: -29.165602-0.000253j
[2025-08-25 19:43:54] [Iter 814/1050] R2[363/600], Temp: 0.3380, Energy: -29.170497-0.003090j
[2025-08-25 19:44:00] [Iter 815/1050] R2[364/600], Temp: 0.3356, Energy: -29.165628+0.000941j
[2025-08-25 19:44:06] [Iter 816/1050] R2[365/600], Temp: 0.3331, Energy: -29.169628-0.003942j
[2025-08-25 19:44:11] [Iter 817/1050] R2[366/600], Temp: 0.3306, Energy: -29.160293+0.002286j
[2025-08-25 19:44:17] [Iter 818/1050] R2[367/600], Temp: 0.3282, Energy: -29.173441-0.003507j
[2025-08-25 19:44:23] [Iter 819/1050] R2[368/600], Temp: 0.3257, Energy: -29.176561+0.001595j
[2025-08-25 19:44:29] [Iter 820/1050] R2[369/600], Temp: 0.3233, Energy: -29.164366-0.005565j
[2025-08-25 19:44:35] [Iter 821/1050] R2[370/600], Temp: 0.3208, Energy: -29.177859-0.001729j
[2025-08-25 19:44:40] [Iter 822/1050] R2[371/600], Temp: 0.3184, Energy: -29.166164-0.003272j
[2025-08-25 19:44:46] [Iter 823/1050] R2[372/600], Temp: 0.3159, Energy: -29.173268+0.000233j
[2025-08-25 19:44:52] [Iter 824/1050] R2[373/600], Temp: 0.3135, Energy: -29.171188-0.001726j
[2025-08-25 19:44:58] [Iter 825/1050] R2[374/600], Temp: 0.3111, Energy: -29.177968-0.000450j
[2025-08-25 19:45:04] [Iter 826/1050] R2[375/600], Temp: 0.3087, Energy: -29.176366-0.000033j
[2025-08-25 19:45:09] [Iter 827/1050] R2[376/600], Temp: 0.3062, Energy: -29.181142-0.002963j
[2025-08-25 19:45:15] [Iter 828/1050] R2[377/600], Temp: 0.3038, Energy: -29.166423-0.003781j
[2025-08-25 19:45:21] [Iter 829/1050] R2[378/600], Temp: 0.3014, Energy: -29.172242-0.003989j
[2025-08-25 19:45:27] [Iter 830/1050] R2[379/600], Temp: 0.2990, Energy: -29.175228-0.004656j
[2025-08-25 19:45:32] [Iter 831/1050] R2[380/600], Temp: 0.2966, Energy: -29.165765+0.000506j
[2025-08-25 19:45:38] [Iter 832/1050] R2[381/600], Temp: 0.2942, Energy: -29.166868-0.001967j
[2025-08-25 19:45:44] [Iter 833/1050] R2[382/600], Temp: 0.2919, Energy: -29.177187-0.001754j
[2025-08-25 19:45:49] [Iter 834/1050] R2[383/600], Temp: 0.2895, Energy: -29.176832+0.000737j
[2025-08-25 19:45:55] [Iter 835/1050] R2[384/600], Temp: 0.2871, Energy: -29.171812-0.002466j
[2025-08-25 19:46:01] [Iter 836/1050] R2[385/600], Temp: 0.2847, Energy: -29.166839+0.001611j
[2025-08-25 19:46:07] [Iter 837/1050] R2[386/600], Temp: 0.2824, Energy: -29.167662+0.001544j
[2025-08-25 19:46:13] [Iter 838/1050] R2[387/600], Temp: 0.2800, Energy: -29.179837+0.000982j
[2025-08-25 19:46:18] [Iter 839/1050] R2[388/600], Temp: 0.2777, Energy: -29.170586-0.000216j
[2025-08-25 19:46:24] [Iter 840/1050] R2[389/600], Temp: 0.2753, Energy: -29.170339+0.001954j
[2025-08-25 19:46:30] [Iter 841/1050] R2[390/600], Temp: 0.2730, Energy: -29.173541+0.003493j
[2025-08-25 19:46:35] [Iter 842/1050] R2[391/600], Temp: 0.2707, Energy: -29.163713+0.001256j
[2025-08-25 19:46:41] [Iter 843/1050] R2[392/600], Temp: 0.2684, Energy: -29.164908-0.002351j
[2025-08-25 19:46:47] [Iter 844/1050] R2[393/600], Temp: 0.2660, Energy: -29.168574-0.000035j
[2025-08-25 19:46:53] [Iter 845/1050] R2[394/600], Temp: 0.2637, Energy: -29.163811-0.000233j
[2025-08-25 19:46:59] [Iter 846/1050] R2[395/600], Temp: 0.2614, Energy: -29.172570-0.002933j
[2025-08-25 19:47:04] [Iter 847/1050] R2[396/600], Temp: 0.2591, Energy: -29.166866-0.000232j
[2025-08-25 19:47:10] [Iter 848/1050] R2[397/600], Temp: 0.2568, Energy: -29.174910+0.002416j
[2025-08-25 19:47:16] [Iter 849/1050] R2[398/600], Temp: 0.2545, Energy: -29.169712+0.001700j
[2025-08-25 19:47:22] [Iter 850/1050] R2[399/600], Temp: 0.2523, Energy: -29.170180+0.000155j
[2025-08-25 19:47:28] [Iter 851/1050] R2[400/600], Temp: 0.2500, Energy: -29.168781-0.001169j
[2025-08-25 19:47:33] [Iter 852/1050] R2[401/600], Temp: 0.2477, Energy: -29.178514-0.001263j
[2025-08-25 19:47:39] [Iter 853/1050] R2[402/600], Temp: 0.2455, Energy: -29.162896+0.001177j
[2025-08-25 19:47:45] [Iter 854/1050] R2[403/600], Temp: 0.2432, Energy: -29.168928-0.000471j
[2025-08-25 19:47:51] [Iter 855/1050] R2[404/600], Temp: 0.2410, Energy: -29.168857-0.000456j
[2025-08-25 19:47:57] [Iter 856/1050] R2[405/600], Temp: 0.2388, Energy: -29.179007-0.000485j
[2025-08-25 19:48:02] [Iter 857/1050] R2[406/600], Temp: 0.2365, Energy: -29.174727-0.000857j
[2025-08-25 19:48:08] [Iter 858/1050] R2[407/600], Temp: 0.2343, Energy: -29.170874-0.001636j
[2025-08-25 19:48:14] [Iter 859/1050] R2[408/600], Temp: 0.2321, Energy: -29.168881-0.001889j
[2025-08-25 19:48:20] [Iter 860/1050] R2[409/600], Temp: 0.2299, Energy: -29.148948+0.001382j
[2025-08-25 19:48:25] [Iter 861/1050] R2[410/600], Temp: 0.2277, Energy: -29.176920+0.002095j
[2025-08-25 19:48:31] [Iter 862/1050] R2[411/600], Temp: 0.2255, Energy: -29.174345+0.005608j
[2025-08-25 19:48:37] [Iter 863/1050] R2[412/600], Temp: 0.2233, Energy: -29.174959+0.002530j
[2025-08-25 19:48:43] [Iter 864/1050] R2[413/600], Temp: 0.2211, Energy: -29.182693-0.000115j
[2025-08-25 19:48:49] [Iter 865/1050] R2[414/600], Temp: 0.2190, Energy: -29.175256-0.002213j
[2025-08-25 19:48:54] [Iter 866/1050] R2[415/600], Temp: 0.2168, Energy: -29.171745-0.002769j
[2025-08-25 19:49:00] [Iter 867/1050] R2[416/600], Temp: 0.2146, Energy: -29.181260-0.004182j
[2025-08-25 19:49:06] [Iter 868/1050] R2[417/600], Temp: 0.2125, Energy: -29.167831+0.001039j
[2025-08-25 19:49:12] [Iter 869/1050] R2[418/600], Temp: 0.2104, Energy: -29.175805-0.004659j
[2025-08-25 19:49:18] [Iter 870/1050] R2[419/600], Temp: 0.2082, Energy: -29.166398-0.002566j
[2025-08-25 19:49:23] [Iter 871/1050] R2[420/600], Temp: 0.2061, Energy: -29.167629-0.002520j
[2025-08-25 19:49:29] [Iter 872/1050] R2[421/600], Temp: 0.2040, Energy: -29.165821-0.002104j
[2025-08-25 19:49:35] [Iter 873/1050] R2[422/600], Temp: 0.2019, Energy: -29.166783+0.003215j
[2025-08-25 19:49:41] [Iter 874/1050] R2[423/600], Temp: 0.1998, Energy: -29.172891-0.001356j
[2025-08-25 19:49:47] [Iter 875/1050] R2[424/600], Temp: 0.1977, Energy: -29.165787-0.004997j
[2025-08-25 19:49:52] [Iter 876/1050] R2[425/600], Temp: 0.1956, Energy: -29.162250+0.002437j
[2025-08-25 19:49:58] [Iter 877/1050] R2[426/600], Temp: 0.1935, Energy: -29.181029-0.002548j
[2025-08-25 19:50:04] [Iter 878/1050] R2[427/600], Temp: 0.1915, Energy: -29.165908-0.002720j
[2025-08-25 19:50:10] [Iter 879/1050] R2[428/600], Temp: 0.1894, Energy: -29.171059+0.000059j
[2025-08-25 19:50:15] [Iter 880/1050] R2[429/600], Temp: 0.1874, Energy: -29.167957+0.000617j
[2025-08-25 19:50:21] [Iter 881/1050] R2[430/600], Temp: 0.1853, Energy: -29.174109-0.002802j
[2025-08-25 19:50:27] [Iter 882/1050] R2[431/600], Temp: 0.1833, Energy: -29.174593-0.000114j
[2025-08-25 19:50:33] [Iter 883/1050] R2[432/600], Temp: 0.1813, Energy: -29.159994-0.002603j
[2025-08-25 19:50:39] [Iter 884/1050] R2[433/600], Temp: 0.1793, Energy: -29.172789-0.001532j
[2025-08-25 19:50:44] [Iter 885/1050] R2[434/600], Temp: 0.1773, Energy: -29.170860-0.002653j
[2025-08-25 19:50:50] [Iter 886/1050] R2[435/600], Temp: 0.1753, Energy: -29.179212-0.003345j
[2025-08-25 19:50:56] [Iter 887/1050] R2[436/600], Temp: 0.1733, Energy: -29.172637-0.000386j
[2025-08-25 19:51:02] [Iter 888/1050] R2[437/600], Temp: 0.1713, Energy: -29.176893-0.002475j
[2025-08-25 19:51:08] [Iter 889/1050] R2[438/600], Temp: 0.1693, Energy: -29.171699+0.004135j
[2025-08-25 19:51:13] [Iter 890/1050] R2[439/600], Temp: 0.1674, Energy: -29.169814-0.000163j
[2025-08-25 19:51:19] [Iter 891/1050] R2[440/600], Temp: 0.1654, Energy: -29.168390+0.000772j
[2025-08-25 19:51:25] [Iter 892/1050] R2[441/600], Temp: 0.1635, Energy: -29.182297+0.002151j
[2025-08-25 19:51:31] [Iter 893/1050] R2[442/600], Temp: 0.1616, Energy: -29.170210-0.000542j
[2025-08-25 19:51:37] [Iter 894/1050] R2[443/600], Temp: 0.1596, Energy: -29.179818-0.002443j
[2025-08-25 19:51:42] [Iter 895/1050] R2[444/600], Temp: 0.1577, Energy: -29.175610-0.002760j
[2025-08-25 19:51:48] [Iter 896/1050] R2[445/600], Temp: 0.1558, Energy: -29.174320-0.001236j
[2025-08-25 19:51:54] [Iter 897/1050] R2[446/600], Temp: 0.1539, Energy: -29.174661-0.001281j
[2025-08-25 19:52:00] [Iter 898/1050] R2[447/600], Temp: 0.1520, Energy: -29.169298-0.000082j
[2025-08-25 19:52:05] [Iter 899/1050] R2[448/600], Temp: 0.1502, Energy: -29.175328-0.002214j
[2025-08-25 19:52:11] [Iter 900/1050] R2[449/600], Temp: 0.1483, Energy: -29.158171+0.001639j
[2025-08-25 19:52:11] ✓ Checkpoint saved: checkpoint_iter_000900.pkl
[2025-08-25 19:52:17] [Iter 901/1050] R2[450/600], Temp: 0.1464, Energy: -29.177442-0.002213j
[2025-08-25 19:52:23] [Iter 902/1050] R2[451/600], Temp: 0.1446, Energy: -29.161955+0.002681j
[2025-08-25 19:52:29] [Iter 903/1050] R2[452/600], Temp: 0.1428, Energy: -29.177540-0.000538j
[2025-08-25 19:52:34] [Iter 904/1050] R2[453/600], Temp: 0.1409, Energy: -29.185319-0.000721j
[2025-08-25 19:52:40] [Iter 905/1050] R2[454/600], Temp: 0.1391, Energy: -29.166425-0.001709j
[2025-08-25 19:52:46] [Iter 906/1050] R2[455/600], Temp: 0.1373, Energy: -29.170571-0.003406j
[2025-08-25 19:52:52] [Iter 907/1050] R2[456/600], Temp: 0.1355, Energy: -29.163200-0.000582j
[2025-08-25 19:52:58] [Iter 908/1050] R2[457/600], Temp: 0.1337, Energy: -29.168617+0.002921j
[2025-08-25 19:53:03] [Iter 909/1050] R2[458/600], Temp: 0.1320, Energy: -29.174066-0.002741j
[2025-08-25 19:53:09] [Iter 910/1050] R2[459/600], Temp: 0.1302, Energy: -29.167605-0.000534j
[2025-08-25 19:53:15] [Iter 911/1050] R2[460/600], Temp: 0.1284, Energy: -29.160653-0.002922j
[2025-08-25 19:53:21] [Iter 912/1050] R2[461/600], Temp: 0.1267, Energy: -29.168787+0.001664j
[2025-08-25 19:53:26] [Iter 913/1050] R2[462/600], Temp: 0.1249, Energy: -29.167769-0.003115j
[2025-08-25 19:53:32] [Iter 914/1050] R2[463/600], Temp: 0.1232, Energy: -29.170975+0.000018j
[2025-08-25 19:53:38] [Iter 915/1050] R2[464/600], Temp: 0.1215, Energy: -29.176720-0.000033j
[2025-08-25 19:53:44] [Iter 916/1050] R2[465/600], Temp: 0.1198, Energy: -29.168363+0.002962j
[2025-08-25 19:53:50] [Iter 917/1050] R2[466/600], Temp: 0.1181, Energy: -29.173663-0.000715j
[2025-08-25 19:53:55] [Iter 918/1050] R2[467/600], Temp: 0.1164, Energy: -29.178269-0.000644j
[2025-08-25 19:54:01] [Iter 919/1050] R2[468/600], Temp: 0.1147, Energy: -29.168329-0.002040j
[2025-08-25 19:54:07] [Iter 920/1050] R2[469/600], Temp: 0.1131, Energy: -29.170126+0.002432j
[2025-08-25 19:54:13] [Iter 921/1050] R2[470/600], Temp: 0.1114, Energy: -29.178425-0.001384j
[2025-08-25 19:54:19] [Iter 922/1050] R2[471/600], Temp: 0.1098, Energy: -29.178790+0.003376j
[2025-08-25 19:54:24] [Iter 923/1050] R2[472/600], Temp: 0.1082, Energy: -29.183300-0.001176j
[2025-08-25 19:54:30] [Iter 924/1050] R2[473/600], Temp: 0.1065, Energy: -29.177580-0.002607j
[2025-08-25 19:54:36] [Iter 925/1050] R2[474/600], Temp: 0.1049, Energy: -29.174972-0.000941j
[2025-08-25 19:54:42] [Iter 926/1050] R2[475/600], Temp: 0.1033, Energy: -29.169598-0.001384j
[2025-08-25 19:54:47] [Iter 927/1050] R2[476/600], Temp: 0.1017, Energy: -29.160927-0.002414j
[2025-08-25 19:54:53] [Iter 928/1050] R2[477/600], Temp: 0.1002, Energy: -29.169244-0.002952j
[2025-08-25 19:54:59] [Iter 929/1050] R2[478/600], Temp: 0.0986, Energy: -29.170574+0.001266j
[2025-08-25 19:55:05] [Iter 930/1050] R2[479/600], Temp: 0.0970, Energy: -29.167960-0.000609j
[2025-08-25 19:55:11] [Iter 931/1050] R2[480/600], Temp: 0.0955, Energy: -29.168809-0.000178j
[2025-08-25 19:55:16] [Iter 932/1050] R2[481/600], Temp: 0.0940, Energy: -29.177005+0.001160j
[2025-08-25 19:55:22] [Iter 933/1050] R2[482/600], Temp: 0.0924, Energy: -29.172114-0.002524j
[2025-08-25 19:55:28] [Iter 934/1050] R2[483/600], Temp: 0.0909, Energy: -29.162126-0.000999j
[2025-08-25 19:55:34] [Iter 935/1050] R2[484/600], Temp: 0.0894, Energy: -29.169619-0.000221j
[2025-08-25 19:55:40] [Iter 936/1050] R2[485/600], Temp: 0.0879, Energy: -29.171725-0.003086j
[2025-08-25 19:55:45] [Iter 937/1050] R2[486/600], Temp: 0.0865, Energy: -29.164772+0.000094j
[2025-08-25 19:55:51] [Iter 938/1050] R2[487/600], Temp: 0.0850, Energy: -29.176518-0.001343j
[2025-08-25 19:55:57] [Iter 939/1050] R2[488/600], Temp: 0.0835, Energy: -29.164925+0.002086j
[2025-08-25 19:56:03] [Iter 940/1050] R2[489/600], Temp: 0.0821, Energy: -29.163669+0.000089j
[2025-08-25 19:56:08] [Iter 941/1050] R2[490/600], Temp: 0.0807, Energy: -29.171285+0.004433j
[2025-08-25 19:56:14] [Iter 942/1050] R2[491/600], Temp: 0.0792, Energy: -29.174648+0.000730j
[2025-08-25 19:56:20] [Iter 943/1050] R2[492/600], Temp: 0.0778, Energy: -29.179541-0.005001j
[2025-08-25 19:56:26] [Iter 944/1050] R2[493/600], Temp: 0.0764, Energy: -29.174262-0.000331j
[2025-08-25 19:56:32] [Iter 945/1050] R2[494/600], Temp: 0.0751, Energy: -29.172465-0.000556j
[2025-08-25 19:56:37] [Iter 946/1050] R2[495/600], Temp: 0.0737, Energy: -29.171349-0.001333j
[2025-08-25 19:56:43] [Iter 947/1050] R2[496/600], Temp: 0.0723, Energy: -29.165601-0.001583j
[2025-08-25 19:56:49] [Iter 948/1050] R2[497/600], Temp: 0.0710, Energy: -29.175658+0.002778j
[2025-08-25 19:56:55] [Iter 949/1050] R2[498/600], Temp: 0.0696, Energy: -29.161879+0.001458j
[2025-08-25 19:57:01] [Iter 950/1050] R2[499/600], Temp: 0.0683, Energy: -29.166479+0.000501j
[2025-08-25 19:57:06] [Iter 951/1050] R2[500/600], Temp: 0.0670, Energy: -29.182147+0.001389j
[2025-08-25 19:57:12] [Iter 952/1050] R2[501/600], Temp: 0.0657, Energy: -29.173862+0.001582j
[2025-08-25 19:57:18] [Iter 953/1050] R2[502/600], Temp: 0.0644, Energy: -29.169694+0.000544j
[2025-08-25 19:57:24] [Iter 954/1050] R2[503/600], Temp: 0.0631, Energy: -29.169412+0.001857j
[2025-08-25 19:57:30] [Iter 955/1050] R2[504/600], Temp: 0.0618, Energy: -29.169226+0.000553j
[2025-08-25 19:57:35] [Iter 956/1050] R2[505/600], Temp: 0.0606, Energy: -29.175479+0.001897j
[2025-08-25 19:57:41] [Iter 957/1050] R2[506/600], Temp: 0.0593, Energy: -29.169160-0.000234j
[2025-08-25 19:57:47] [Iter 958/1050] R2[507/600], Temp: 0.0581, Energy: -29.166121+0.000090j
[2025-08-25 19:57:53] [Iter 959/1050] R2[508/600], Temp: 0.0569, Energy: -29.163491-0.000584j
[2025-08-25 19:57:58] [Iter 960/1050] R2[509/600], Temp: 0.0557, Energy: -29.173989+0.000983j
[2025-08-25 19:58:04] [Iter 961/1050] R2[510/600], Temp: 0.0545, Energy: -29.163293-0.000661j
[2025-08-25 19:58:10] [Iter 962/1050] R2[511/600], Temp: 0.0533, Energy: -29.177222+0.001061j
[2025-08-25 19:58:16] [Iter 963/1050] R2[512/600], Temp: 0.0521, Energy: -29.170216+0.003532j
[2025-08-25 19:58:22] [Iter 964/1050] R2[513/600], Temp: 0.0510, Energy: -29.162033-0.000445j
[2025-08-25 19:58:27] [Iter 965/1050] R2[514/600], Temp: 0.0498, Energy: -29.175168-0.000241j
[2025-08-25 19:58:33] [Iter 966/1050] R2[515/600], Temp: 0.0487, Energy: -29.175636-0.005128j
[2025-08-25 19:58:39] [Iter 967/1050] R2[516/600], Temp: 0.0476, Energy: -29.181531+0.000628j
[2025-08-25 19:58:45] [Iter 968/1050] R2[517/600], Temp: 0.0465, Energy: -29.167697-0.001213j
[2025-08-25 19:58:51] [Iter 969/1050] R2[518/600], Temp: 0.0454, Energy: -29.171415+0.002205j
[2025-08-25 19:58:56] [Iter 970/1050] R2[519/600], Temp: 0.0443, Energy: -29.176485-0.001112j
[2025-08-25 19:59:02] [Iter 971/1050] R2[520/600], Temp: 0.0432, Energy: -29.179221-0.001849j
[2025-08-25 19:59:08] [Iter 972/1050] R2[521/600], Temp: 0.0422, Energy: -29.167618+0.001528j
[2025-08-25 19:59:14] [Iter 973/1050] R2[522/600], Temp: 0.0411, Energy: -29.179987+0.000280j
[2025-08-25 19:59:19] [Iter 974/1050] R2[523/600], Temp: 0.0401, Energy: -29.172197+0.003471j
[2025-08-25 19:59:25] [Iter 975/1050] R2[524/600], Temp: 0.0391, Energy: -29.176520-0.004364j
[2025-08-25 19:59:31] [Iter 976/1050] R2[525/600], Temp: 0.0381, Energy: -29.174021-0.000037j
[2025-08-25 19:59:37] [Iter 977/1050] R2[526/600], Temp: 0.0371, Energy: -29.175213-0.002991j
[2025-08-25 19:59:43] [Iter 978/1050] R2[527/600], Temp: 0.0361, Energy: -29.157238-0.000046j
[2025-08-25 19:59:48] [Iter 979/1050] R2[528/600], Temp: 0.0351, Energy: -29.165150-0.000984j
[2025-08-25 19:59:54] [Iter 980/1050] R2[529/600], Temp: 0.0342, Energy: -29.180933-0.001478j
[2025-08-25 20:00:00] [Iter 981/1050] R2[530/600], Temp: 0.0332, Energy: -29.170141-0.000912j
[2025-08-25 20:00:06] [Iter 982/1050] R2[531/600], Temp: 0.0323, Energy: -29.171830+0.003332j
[2025-08-25 20:00:12] [Iter 983/1050] R2[532/600], Temp: 0.0314, Energy: -29.175732-0.003821j
[2025-08-25 20:00:17] [Iter 984/1050] R2[533/600], Temp: 0.0305, Energy: -29.167280+0.001961j
[2025-08-25 20:00:23] [Iter 985/1050] R2[534/600], Temp: 0.0296, Energy: -29.168408-0.001761j
[2025-08-25 20:00:29] [Iter 986/1050] R2[535/600], Temp: 0.0287, Energy: -29.172256-0.000680j
[2025-08-25 20:00:35] [Iter 987/1050] R2[536/600], Temp: 0.0278, Energy: -29.183868+0.000143j
[2025-08-25 20:00:41] [Iter 988/1050] R2[537/600], Temp: 0.0270, Energy: -29.164995+0.001593j
[2025-08-25 20:00:46] [Iter 989/1050] R2[538/600], Temp: 0.0261, Energy: -29.180707-0.003669j
[2025-08-25 20:00:52] [Iter 990/1050] R2[539/600], Temp: 0.0253, Energy: -29.166059+0.001290j
[2025-08-25 20:00:58] [Iter 991/1050] R2[540/600], Temp: 0.0245, Energy: -29.173903-0.001489j
[2025-08-25 20:01:04] [Iter 992/1050] R2[541/600], Temp: 0.0237, Energy: -29.155784+0.001811j
[2025-08-25 20:01:10] [Iter 993/1050] R2[542/600], Temp: 0.0229, Energy: -29.170363-0.001444j
[2025-08-25 20:01:15] [Iter 994/1050] R2[543/600], Temp: 0.0221, Energy: -29.169508+0.002614j
[2025-08-25 20:01:21] [Iter 995/1050] R2[544/600], Temp: 0.0213, Energy: -29.171335-0.002233j
[2025-08-25 20:01:27] [Iter 996/1050] R2[545/600], Temp: 0.0206, Energy: -29.169185+0.002922j
[2025-08-25 20:01:33] [Iter 997/1050] R2[546/600], Temp: 0.0199, Energy: -29.167736-0.001426j
[2025-08-25 20:01:38] [Iter 998/1050] R2[547/600], Temp: 0.0191, Energy: -29.186312-0.001583j
[2025-08-25 20:01:44] [Iter 999/1050] R2[548/600], Temp: 0.0184, Energy: -29.163879-0.002571j
[2025-08-25 20:01:50] [Iter 1000/1050] R2[549/600], Temp: 0.0177, Energy: -29.168717+0.000310j
[2025-08-25 20:01:50] ✓ Checkpoint saved: checkpoint_iter_001000.pkl
[2025-08-25 20:01:56] [Iter 1001/1050] R2[550/600], Temp: 0.0170, Energy: -29.167691-0.002884j
[2025-08-25 20:02:02] [Iter 1002/1050] R2[551/600], Temp: 0.0164, Energy: -29.187728+0.000077j
[2025-08-25 20:02:07] [Iter 1003/1050] R2[552/600], Temp: 0.0157, Energy: -29.166745-0.001626j
[2025-08-25 20:02:13] [Iter 1004/1050] R2[553/600], Temp: 0.0151, Energy: -29.174347+0.000060j
[2025-08-25 20:02:19] [Iter 1005/1050] R2[554/600], Temp: 0.0144, Energy: -29.154680+0.002211j
[2025-08-25 20:02:25] [Iter 1006/1050] R2[555/600], Temp: 0.0138, Energy: -29.173685-0.001923j
[2025-08-25 20:02:31] [Iter 1007/1050] R2[556/600], Temp: 0.0132, Energy: -29.166806+0.001241j
[2025-08-25 20:02:36] [Iter 1008/1050] R2[557/600], Temp: 0.0126, Energy: -29.160399+0.001390j
[2025-08-25 20:02:42] [Iter 1009/1050] R2[558/600], Temp: 0.0120, Energy: -29.171705+0.001185j
[2025-08-25 20:02:48] [Iter 1010/1050] R2[559/600], Temp: 0.0115, Energy: -29.165428+0.000600j
[2025-08-25 20:02:54] [Iter 1011/1050] R2[560/600], Temp: 0.0109, Energy: -29.169979-0.004139j
[2025-08-25 20:03:00] [Iter 1012/1050] R2[561/600], Temp: 0.0104, Energy: -29.175251-0.000078j
[2025-08-25 20:03:05] [Iter 1013/1050] R2[562/600], Temp: 0.0099, Energy: -29.172206+0.000806j
[2025-08-25 20:03:11] [Iter 1014/1050] R2[563/600], Temp: 0.0094, Energy: -29.180105-0.000140j
[2025-08-25 20:03:17] [Iter 1015/1050] R2[564/600], Temp: 0.0089, Energy: -29.173105-0.000067j
[2025-08-25 20:03:23] [Iter 1016/1050] R2[565/600], Temp: 0.0084, Energy: -29.174687-0.004010j
[2025-08-25 20:03:28] [Iter 1017/1050] R2[566/600], Temp: 0.0079, Energy: -29.171693+0.000462j
[2025-08-25 20:03:34] [Iter 1018/1050] R2[567/600], Temp: 0.0074, Energy: -29.170065+0.001601j
[2025-08-25 20:03:40] [Iter 1019/1050] R2[568/600], Temp: 0.0070, Energy: -29.166734-0.001844j
[2025-08-25 20:03:46] [Iter 1020/1050] R2[569/600], Temp: 0.0066, Energy: -29.180580-0.001350j
[2025-08-25 20:03:52] [Iter 1021/1050] R2[570/600], Temp: 0.0062, Energy: -29.173337-0.002586j
[2025-08-25 20:03:57] [Iter 1022/1050] R2[571/600], Temp: 0.0058, Energy: -29.168204-0.000439j
[2025-08-25 20:04:03] [Iter 1023/1050] R2[572/600], Temp: 0.0054, Energy: -29.175971+0.001657j
[2025-08-25 20:04:09] [Iter 1024/1050] R2[573/600], Temp: 0.0050, Energy: -29.165045+0.002064j
[2025-08-25 20:04:15] [Iter 1025/1050] R2[574/600], Temp: 0.0046, Energy: -29.175529-0.003337j
[2025-08-25 20:04:21] [Iter 1026/1050] R2[575/600], Temp: 0.0043, Energy: -29.155380+0.001108j
[2025-08-25 20:04:26] [Iter 1027/1050] R2[576/600], Temp: 0.0039, Energy: -29.181054-0.001621j
[2025-08-25 20:04:32] [Iter 1028/1050] R2[577/600], Temp: 0.0036, Energy: -29.173441+0.001419j
[2025-08-25 20:04:38] [Iter 1029/1050] R2[578/600], Temp: 0.0033, Energy: -29.175409+0.001010j
[2025-08-25 20:04:44] [Iter 1030/1050] R2[579/600], Temp: 0.0030, Energy: -29.170589+0.000253j
[2025-08-25 20:04:50] [Iter 1031/1050] R2[580/600], Temp: 0.0027, Energy: -29.170043+0.004742j
[2025-08-25 20:04:55] [Iter 1032/1050] R2[581/600], Temp: 0.0025, Energy: -29.174010-0.004825j
[2025-08-25 20:05:01] [Iter 1033/1050] R2[582/600], Temp: 0.0022, Energy: -29.171448-0.001682j
[2025-08-25 20:05:07] [Iter 1034/1050] R2[583/600], Temp: 0.0020, Energy: -29.169638-0.002772j
[2025-08-25 20:05:13] [Iter 1035/1050] R2[584/600], Temp: 0.0018, Energy: -29.178756+0.001218j
[2025-08-25 20:05:18] [Iter 1036/1050] R2[585/600], Temp: 0.0015, Energy: -29.177314-0.000271j
[2025-08-25 20:05:24] [Iter 1037/1050] R2[586/600], Temp: 0.0013, Energy: -29.165975+0.001081j
[2025-08-25 20:05:30] [Iter 1038/1050] R2[587/600], Temp: 0.0012, Energy: -29.170052-0.002211j
[2025-08-25 20:05:36] [Iter 1039/1050] R2[588/600], Temp: 0.0010, Energy: -29.162175+0.001926j
[2025-08-25 20:05:42] [Iter 1040/1050] R2[589/600], Temp: 0.0008, Energy: -29.180681+0.002760j
[2025-08-25 20:05:47] [Iter 1041/1050] R2[590/600], Temp: 0.0007, Energy: -29.159674-0.001763j
[2025-08-25 20:05:53] [Iter 1042/1050] R2[591/600], Temp: 0.0006, Energy: -29.175668+0.001803j
[2025-08-25 20:05:59] [Iter 1043/1050] R2[592/600], Temp: 0.0004, Energy: -29.170421-0.003865j
[2025-08-25 20:06:05] [Iter 1044/1050] R2[593/600], Temp: 0.0003, Energy: -29.177242-0.002273j
[2025-08-25 20:06:11] [Iter 1045/1050] R2[594/600], Temp: 0.0002, Energy: -29.158994-0.002936j
[2025-08-25 20:06:16] [Iter 1046/1050] R2[595/600], Temp: 0.0002, Energy: -29.160837+0.001557j
[2025-08-25 20:06:22] [Iter 1047/1050] R2[596/600], Temp: 0.0001, Energy: -29.169701-0.000411j
[2025-08-25 20:06:28] [Iter 1048/1050] R2[597/600], Temp: 0.0001, Energy: -29.162104-0.005534j
[2025-08-25 20:06:34] [Iter 1049/1050] R2[598/600], Temp: 0.0000, Energy: -29.178580-0.000016j
[2025-08-25 20:06:40] [Iter 1050/1050] R2[599/600], Temp: 0.0000, Energy: -29.167712-0.000626j
[2025-08-25 20:06:40] ✅ Training completed | Restarts: 2
[2025-08-25 20:06:40] ============================================================
[2025-08-25 20:06:40] Training completed | Runtime: 6113.3s
[2025-08-25 20:06:42] ✓ Final state saved: checkpoints/final_GCNN.pkl
[2025-08-25 20:06:42] ============================================================
