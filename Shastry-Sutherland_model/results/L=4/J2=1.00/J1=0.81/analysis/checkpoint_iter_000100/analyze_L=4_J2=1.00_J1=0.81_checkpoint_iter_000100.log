[2025-08-26 16:01:24] 使用checkpoint文件: results/L=4/J2=1.00/J1=0.81/training/checkpoints/checkpoint_iter_000100.pkl
[2025-08-26 16:01:35] ✓ 从checkpoint加载参数: 100
[2025-08-26 16:01:35]   - 能量: -29.174140+0.000772j ± 0.006763
[2025-08-26 16:01:35] ================================================================================
[2025-08-26 16:01:35] 加载量子态: L=4, J2=1.00, J1=0.81, checkpoint=checkpoint_iter_000100
[2025-08-26 16:01:35] 设置样本数为: 1048576
[2025-08-26 16:01:35] 开始生成共享样本集...
[2025-08-26 16:02:57] 样本生成完成,耗时: 82.747 秒
[2025-08-26 16:02:57] ================================================================================
[2025-08-26 16:02:57] 开始计算自旋结构因子...
[2025-08-26 16:02:57] 初始化操作符缓存...
[2025-08-26 16:02:57] 预构建所有自旋相关操作符...
[2025-08-26 16:02:57] 开始计算自旋相关函数...
[2025-08-26 16:03:05] 自旋算符进度: 1/64, 当前算符: S_0 · S_0, 耗时: 7.544s
[2025-08-26 16:03:14] 自旋算符进度: 2/64, 当前算符: S_0 · S_1, 耗时: 8.947s
[2025-08-26 16:03:18] 自旋算符进度: 3/64, 当前算符: S_0 · S_2, 耗时: 4.243s
[2025-08-26 16:03:22] 自旋算符进度: 4/64, 当前算符: S_0 · S_3, 耗时: 4.261s
[2025-08-26 16:03:27] 自旋算符进度: 5/64, 当前算符: S_0 · S_4, 耗时: 4.277s
[2025-08-26 16:03:31] 自旋算符进度: 6/64, 当前算符: S_0 · S_5, 耗时: 4.259s
[2025-08-26 16:03:35] 自旋算符进度: 7/64, 当前算符: S_0 · S_6, 耗时: 4.240s
[2025-08-26 16:03:39] 自旋算符进度: 8/64, 当前算符: S_0 · S_7, 耗时: 4.281s
[2025-08-26 16:03:44] 自旋算符进度: 9/64, 当前算符: S_0 · S_8, 耗时: 4.241s
[2025-08-26 16:03:48] 自旋算符进度: 10/64, 当前算符: S_0 · S_9, 耗时: 4.282s
[2025-08-26 16:03:52] 自旋算符进度: 11/64, 当前算符: S_0 · S_10, 耗时: 4.256s
[2025-08-26 16:03:57] 自旋算符进度: 12/64, 当前算符: S_0 · S_11, 耗时: 4.279s
[2025-08-26 16:04:01] 自旋算符进度: 13/64, 当前算符: S_0 · S_12, 耗时: 4.242s
[2025-08-26 16:04:05] 自旋算符进度: 14/64, 当前算符: S_0 · S_13, 耗时: 4.261s
[2025-08-26 16:04:09] 自旋算符进度: 15/64, 当前算符: S_0 · S_14, 耗时: 4.277s
[2025-08-26 16:04:14] 自旋算符进度: 16/64, 当前算符: S_0 · S_15, 耗时: 4.247s
[2025-08-26 16:04:18] 自旋算符进度: 17/64, 当前算符: S_0 · S_16, 耗时: 4.259s
[2025-08-26 16:04:22] 自旋算符进度: 18/64, 当前算符: S_0 · S_17, 耗时: 4.280s
[2025-08-26 16:04:26] 自旋算符进度: 19/64, 当前算符: S_0 · S_18, 耗时: 4.244s
[2025-08-26 16:04:31] 自旋算符进度: 20/64, 当前算符: S_0 · S_19, 耗时: 4.281s
[2025-08-26 16:04:35] 自旋算符进度: 21/64, 当前算符: S_0 · S_20, 耗时: 4.260s
[2025-08-26 16:04:39] 自旋算符进度: 22/64, 当前算符: S_0 · S_21, 耗时: 4.280s
[2025-08-26 16:04:44] 自旋算符进度: 23/64, 当前算符: S_0 · S_22, 耗时: 4.243s
[2025-08-26 16:04:48] 自旋算符进度: 24/64, 当前算符: S_0 · S_23, 耗时: 4.261s
[2025-08-26 16:04:52] 自旋算符进度: 25/64, 当前算符: S_0 · S_24, 耗时: 4.250s
[2025-08-26 16:04:56] 自旋算符进度: 26/64, 当前算符: S_0 · S_25, 耗时: 4.261s
[2025-08-26 16:05:01] 自旋算符进度: 27/64, 当前算符: S_0 · S_26, 耗时: 4.244s
[2025-08-26 16:05:05] 自旋算符进度: 28/64, 当前算符: S_0 · S_27, 耗时: 4.282s
[2025-08-26 16:05:09] 自旋算符进度: 29/64, 当前算符: S_0 · S_28, 耗时: 4.251s
[2025-08-26 16:05:13] 自旋算符进度: 30/64, 当前算符: S_0 · S_29, 耗时: 4.242s
[2025-08-26 16:05:18] 自旋算符进度: 31/64, 当前算符: S_0 · S_30, 耗时: 4.243s
[2025-08-26 16:05:22] 自旋算符进度: 32/64, 当前算符: S_0 · S_31, 耗时: 4.258s
[2025-08-26 16:05:26] 自旋算符进度: 33/64, 当前算符: S_0 · S_32, 耗时: 4.258s
[2025-08-26 16:05:30] 自旋算符进度: 34/64, 当前算符: S_0 · S_33, 耗时: 4.260s
[2025-08-26 16:05:35] 自旋算符进度: 35/64, 当前算符: S_0 · S_34, 耗时: 4.249s
[2025-08-26 16:05:39] 自旋算符进度: 36/64, 当前算符: S_0 · S_35, 耗时: 4.259s
[2025-08-26 16:05:43] 自旋算符进度: 37/64, 当前算符: S_0 · S_36, 耗时: 4.245s
[2025-08-26 16:05:47] 自旋算符进度: 38/64, 当前算符: S_0 · S_37, 耗时: 4.260s
[2025-08-26 16:05:52] 自旋算符进度: 39/64, 当前算符: S_0 · S_38, 耗时: 4.243s
[2025-08-26 16:05:56] 自旋算符进度: 40/64, 当前算符: S_0 · S_39, 耗时: 4.280s
[2025-08-26 16:06:00] 自旋算符进度: 41/64, 当前算符: S_0 · S_40, 耗时: 4.243s
[2025-08-26 16:06:05] 自旋算符进度: 42/64, 当前算符: S_0 · S_41, 耗时: 4.250s
[2025-08-26 16:06:09] 自旋算符进度: 43/64, 当前算符: S_0 · S_42, 耗时: 4.243s
[2025-08-26 16:06:13] 自旋算符进度: 44/64, 当前算符: S_0 · S_43, 耗时: 4.261s
[2025-08-26 16:06:17] 自旋算符进度: 45/64, 当前算符: S_0 · S_44, 耗时: 4.243s
[2025-08-26 16:06:22] 自旋算符进度: 46/64, 当前算符: S_0 · S_45, 耗时: 4.243s
[2025-08-26 16:06:26] 自旋算符进度: 47/64, 当前算符: S_0 · S_46, 耗时: 4.279s
[2025-08-26 16:06:30] 自旋算符进度: 48/64, 当前算符: S_0 · S_47, 耗时: 4.244s
[2025-08-26 16:06:34] 自旋算符进度: 49/64, 当前算符: S_0 · S_48, 耗时: 4.279s
[2025-08-26 16:06:39] 自旋算符进度: 50/64, 当前算符: S_0 · S_49, 耗时: 4.281s
[2025-08-26 16:06:43] 自旋算符进度: 51/64, 当前算符: S_0 · S_50, 耗时: 4.242s
[2025-08-26 16:06:47] 自旋算符进度: 52/64, 当前算符: S_0 · S_51, 耗时: 4.281s
[2025-08-26 16:06:51] 自旋算符进度: 53/64, 当前算符: S_0 · S_52, 耗时: 4.247s
[2025-08-26 16:06:56] 自旋算符进度: 54/64, 当前算符: S_0 · S_53, 耗时: 4.261s
[2025-08-26 16:07:00] 自旋算符进度: 55/64, 当前算符: S_0 · S_54, 耗时: 4.241s
[2025-08-26 16:07:04] 自旋算符进度: 56/64, 当前算符: S_0 · S_55, 耗时: 4.284s
[2025-08-26 16:07:08] 自旋算符进度: 57/64, 当前算符: S_0 · S_56, 耗时: 4.241s
[2025-08-26 16:07:13] 自旋算符进度: 58/64, 当前算符: S_0 · S_57, 耗时: 4.241s
[2025-08-26 16:07:17] 自旋算符进度: 59/64, 当前算符: S_0 · S_58, 耗时: 4.243s
[2025-08-26 16:07:21] 自旋算符进度: 60/64, 当前算符: S_0 · S_59, 耗时: 4.282s
[2025-08-26 16:07:26] 自旋算符进度: 61/64, 当前算符: S_0 · S_60, 耗时: 4.247s
[2025-08-26 16:07:30] 自旋算符进度: 62/64, 当前算符: S_0 · S_61, 耗时: 4.242s
[2025-08-26 16:07:34] 自旋算符进度: 63/64, 当前算符: S_0 · S_62, 耗时: 4.279s
[2025-08-26 16:07:38] 自旋算符进度: 64/64, 当前算符: S_0 · S_63, 耗时: 4.247s
[2025-08-26 16:07:38] 自旋相关函数计算完成,总耗时 280.88 秒
[2025-08-26 16:07:38] 计算傅里叶变换...
[2025-08-26 16:07:39] 自旋结构因子计算完成
[2025-08-26 16:07:40] 自旋相关函数平均误差: 0.000551
