[2025-08-26 12:02:59] 使用checkpoint文件: results/L=4/J2=1.00/J1=0.77/training/checkpoints/checkpoint_iter_000700.pkl
[2025-08-26 12:03:10] ✓ 从checkpoint加载参数: 700
[2025-08-26 12:03:10]   - 能量: -27.534824-0.000041j ± 0.006648
[2025-08-26 12:03:10] ================================================================================
[2025-08-26 12:03:10] 加载量子态: L=4, J2=1.00, J1=0.77, checkpoint=checkpoint_iter_000700
[2025-08-26 12:03:10] 设置样本数为: 1048576
[2025-08-26 12:03:10] 开始生成共享样本集...
[2025-08-26 12:04:32] 样本生成完成,耗时: 82.706 秒
[2025-08-26 12:04:32] ================================================================================
[2025-08-26 12:04:32] 开始计算自旋结构因子...
[2025-08-26 12:04:32] 初始化操作符缓存...
[2025-08-26 12:04:32] 预构建所有自旋相关操作符...
[2025-08-26 12:04:33] 开始计算自旋相关函数...
[2025-08-26 12:04:40] 自旋算符进度: 1/64, 当前算符: S_0 · S_0, 耗时: 7.517s
[2025-08-26 12:04:49] 自旋算符进度: 2/64, 当前算符: S_0 · S_1, 耗时: 8.922s
[2025-08-26 12:04:53] 自旋算符进度: 3/64, 当前算符: S_0 · S_2, 耗时: 4.242s
[2025-08-26 12:04:58] 自旋算符进度: 4/64, 当前算符: S_0 · S_3, 耗时: 4.259s
[2025-08-26 12:05:02] 自旋算符进度: 5/64, 当前算符: S_0 · S_4, 耗时: 4.274s
[2025-08-26 12:05:06] 自旋算符进度: 6/64, 当前算符: S_0 · S_5, 耗时: 4.259s
[2025-08-26 12:05:10] 自旋算符进度: 7/64, 当前算符: S_0 · S_6, 耗时: 4.240s
[2025-08-26 12:05:15] 自旋算符进度: 8/64, 当前算符: S_0 · S_7, 耗时: 4.282s
[2025-08-26 12:05:19] 自旋算符进度: 9/64, 当前算符: S_0 · S_8, 耗时: 4.241s
[2025-08-26 12:05:23] 自旋算符进度: 10/64, 当前算符: S_0 · S_9, 耗时: 4.282s
[2025-08-26 12:05:27] 自旋算符进度: 11/64, 当前算符: S_0 · S_10, 耗时: 4.253s
[2025-08-26 12:05:32] 自旋算符进度: 12/64, 当前算符: S_0 · S_11, 耗时: 4.277s
[2025-08-26 12:05:36] 自旋算符进度: 13/64, 当前算符: S_0 · S_12, 耗时: 4.242s
[2025-08-26 12:05:40] 自旋算符进度: 14/64, 当前算符: S_0 · S_13, 耗时: 4.259s
[2025-08-26 12:05:44] 自旋算符进度: 15/64, 当前算符: S_0 · S_14, 耗时: 4.273s
[2025-08-26 12:05:49] 自旋算符进度: 16/64, 当前算符: S_0 · S_15, 耗时: 4.248s
[2025-08-26 12:05:53] 自旋算符进度: 17/64, 当前算符: S_0 · S_16, 耗时: 4.258s
[2025-08-26 12:05:57] 自旋算符进度: 18/64, 当前算符: S_0 · S_17, 耗时: 4.281s
[2025-08-26 12:06:02] 自旋算符进度: 19/64, 当前算符: S_0 · S_18, 耗时: 4.243s
[2025-08-26 12:06:06] 自旋算符进度: 20/64, 当前算符: S_0 · S_19, 耗时: 4.283s
[2025-08-26 12:06:10] 自旋算符进度: 21/64, 当前算符: S_0 · S_20, 耗时: 4.259s
[2025-08-26 12:06:14] 自旋算符进度: 22/64, 当前算符: S_0 · S_21, 耗时: 4.277s
[2025-08-26 12:06:19] 自旋算符进度: 23/64, 当前算符: S_0 · S_22, 耗时: 4.243s
[2025-08-26 12:06:23] 自旋算符进度: 24/64, 当前算符: S_0 · S_23, 耗时: 4.259s
[2025-08-26 12:06:27] 自旋算符进度: 25/64, 当前算符: S_0 · S_24, 耗时: 4.251s
[2025-08-26 12:06:31] 自旋算符进度: 26/64, 当前算符: S_0 · S_25, 耗时: 4.261s
[2025-08-26 12:06:36] 自旋算符进度: 27/64, 当前算符: S_0 · S_26, 耗时: 4.243s
[2025-08-26 12:06:40] 自旋算符进度: 28/64, 当前算符: S_0 · S_27, 耗时: 4.284s
[2025-08-26 12:06:44] 自旋算符进度: 29/64, 当前算符: S_0 · S_28, 耗时: 4.251s
[2025-08-26 12:06:48] 自旋算符进度: 30/64, 当前算符: S_0 · S_29, 耗时: 4.242s
[2025-08-26 12:06:53] 自旋算符进度: 31/64, 当前算符: S_0 · S_30, 耗时: 4.242s
[2025-08-26 12:06:57] 自旋算符进度: 32/64, 当前算符: S_0 · S_31, 耗时: 4.257s
[2025-08-26 12:07:01] 自旋算符进度: 33/64, 当前算符: S_0 · S_32, 耗时: 4.257s
[2025-08-26 12:07:05] 自旋算符进度: 34/64, 当前算符: S_0 · S_33, 耗时: 4.258s
[2025-08-26 12:07:10] 自旋算符进度: 35/64, 当前算符: S_0 · S_34, 耗时: 4.250s
[2025-08-26 12:07:14] 自旋算符进度: 36/64, 当前算符: S_0 · S_35, 耗时: 4.259s
[2025-08-26 12:07:18] 自旋算符进度: 37/64, 当前算符: S_0 · S_36, 耗时: 4.244s
[2025-08-26 12:07:22] 自旋算符进度: 38/64, 当前算符: S_0 · S_37, 耗时: 4.259s
[2025-08-26 12:07:27] 自旋算符进度: 39/64, 当前算符: S_0 · S_38, 耗时: 4.243s
[2025-08-26 12:07:31] 自旋算符进度: 40/64, 当前算符: S_0 · S_39, 耗时: 4.279s
[2025-08-26 12:07:35] 自旋算符进度: 41/64, 当前算符: S_0 · S_40, 耗时: 4.242s
[2025-08-26 12:07:39] 自旋算符进度: 42/64, 当前算符: S_0 · S_41, 耗时: 4.250s
[2025-08-26 12:07:44] 自旋算符进度: 43/64, 当前算符: S_0 · S_42, 耗时: 4.242s
[2025-08-26 12:07:48] 自旋算符进度: 44/64, 当前算符: S_0 · S_43, 耗时: 4.261s
[2025-08-26 12:07:52] 自旋算符进度: 45/64, 当前算符: S_0 · S_44, 耗时: 4.243s
[2025-08-26 12:07:57] 自旋算符进度: 46/64, 当前算符: S_0 · S_45, 耗时: 4.242s
[2025-08-26 12:08:01] 自旋算符进度: 47/64, 当前算符: S_0 · S_46, 耗时: 4.280s
[2025-08-26 12:08:05] 自旋算符进度: 48/64, 当前算符: S_0 · S_47, 耗时: 4.243s
[2025-08-26 12:08:09] 自旋算符进度: 49/64, 当前算符: S_0 · S_48, 耗时: 4.281s
[2025-08-26 12:08:14] 自旋算符进度: 50/64, 当前算符: S_0 · S_49, 耗时: 4.281s
[2025-08-26 12:08:18] 自旋算符进度: 51/64, 当前算符: S_0 · S_50, 耗时: 4.242s
[2025-08-26 12:08:22] 自旋算符进度: 52/64, 当前算符: S_0 · S_51, 耗时: 4.282s
[2025-08-26 12:08:26] 自旋算符进度: 53/64, 当前算符: S_0 · S_52, 耗时: 4.249s
[2025-08-26 12:08:31] 自旋算符进度: 54/64, 当前算符: S_0 · S_53, 耗时: 4.260s
[2025-08-26 12:08:35] 自旋算符进度: 55/64, 当前算符: S_0 · S_54, 耗时: 4.241s
[2025-08-26 12:08:39] 自旋算符进度: 56/64, 当前算符: S_0 · S_55, 耗时: 4.282s
[2025-08-26 12:08:43] 自旋算符进度: 57/64, 当前算符: S_0 · S_56, 耗时: 4.242s
[2025-08-26 12:08:48] 自旋算符进度: 58/64, 当前算符: S_0 · S_57, 耗时: 4.242s
[2025-08-26 12:08:52] 自旋算符进度: 59/64, 当前算符: S_0 · S_58, 耗时: 4.241s
[2025-08-26 12:08:56] 自旋算符进度: 60/64, 当前算符: S_0 · S_59, 耗时: 4.282s
[2025-08-26 12:09:01] 自旋算符进度: 61/64, 当前算符: S_0 · S_60, 耗时: 4.247s
[2025-08-26 12:09:05] 自旋算符进度: 62/64, 当前算符: S_0 · S_61, 耗时: 4.242s
[2025-08-26 12:09:09] 自旋算符进度: 63/64, 当前算符: S_0 · S_62, 耗时: 4.280s
[2025-08-26 12:09:13] 自旋算符进度: 64/64, 当前算符: S_0 · S_63, 耗时: 4.249s
[2025-08-26 12:09:13] 自旋相关函数计算完成,总耗时 280.75 秒
[2025-08-26 12:09:13] 计算傅里叶变换...
[2025-08-26 12:09:14] 自旋结构因子计算完成
[2025-08-26 12:09:15] 自旋相关函数平均误差: 0.000559
