[2025-08-07 17:14:01] ✓ 从checkpoint恢复: results/L=4/J2=0.00/J1=0.03/training/checkpoints/final_GCNN.pkl
[2025-08-07 17:14:01]   - 迭代次数: final
[2025-08-07 17:14:01]   - 能量: -54.209274+0.000559j ± 0.084983
[2025-08-07 17:14:01]   - 时间戳: 2025-08-07T15:01:01.169778+08:00
[2025-08-07 17:14:08] ✓ 变分状态参数已从checkpoint恢复
[2025-08-07 17:14:08] ✓ 从final状态恢复, 重置迭代计数为0
[2025-08-07 17:14:08] ==================================================
[2025-08-07 17:14:08] GCNN for Shastry-Sutherland Model
[2025-08-07 17:14:08] ==================================================
[2025-08-07 17:14:08] System parameters:
[2025-08-07 17:14:08]   - System size: L=4, N=64
[2025-08-07 17:14:08]   - System parameters: J1=0.02, J2=0.0, Q=1.0
[2025-08-07 17:14:08] --------------------------------------------------
[2025-08-07 17:14:08] Model parameters:
[2025-08-07 17:14:08]   - Number of layers = 4
[2025-08-07 17:14:08]   - Number of features = 4
[2025-08-07 17:14:08]   - Total parameters = 12572
[2025-08-07 17:14:08] --------------------------------------------------
[2025-08-07 17:14:08] Training parameters:
[2025-08-07 17:14:08]   - Learning rate: 0.015
[2025-08-07 17:14:08]   - Total iterations: 1050
[2025-08-07 17:14:08]   - Annealing cycles: 3
[2025-08-07 17:14:08]   - Initial period: 150
[2025-08-07 17:14:08]   - Period multiplier: 2.0
[2025-08-07 17:14:08]   - Temperature range: 0.0-1.0
[2025-08-07 17:14:08]   - Samples: 4096
[2025-08-07 17:14:08]   - Discarded samples: 0
[2025-08-07 17:14:08]   - Chunk size: 2048
[2025-08-07 17:14:08]   - Diagonal shift: 0.2
[2025-08-07 17:14:08]   - Gradient clipping: 1.0
[2025-08-07 17:14:08]   - Checkpoint enabled: interval=100
[2025-08-07 17:14:08]   - Checkpoint directory: results/L=4/J2=0.00/J1=0.02/training/checkpoints
[2025-08-07 17:14:08] --------------------------------------------------
[2025-08-07 17:14:08] Device status:
[2025-08-07 17:14:08]   - Devices model: A100
[2025-08-07 17:14:08]   - Number of devices: 1
[2025-08-07 17:14:08]   - Sharding: True
[2025-08-07 17:14:08] ============================================================
[2025-08-07 17:14:40] [Iter 1/1050] R0[0/150], Temp: 1.0000, Energy: -53.966391+0.008916j
[2025-08-07 17:14:58] [Iter 2/1050] R0[1/150], Temp: 0.9999, Energy: -53.847882-0.006810j
[2025-08-07 17:15:02] [Iter 3/1050] R0[2/150], Temp: 0.9996, Energy: -53.841710-0.010148j
[2025-08-07 17:15:06] [Iter 4/1050] R0[3/150], Temp: 0.9990, Energy: -53.911406-0.007266j
[2025-08-07 17:15:11] [Iter 5/1050] R0[4/150], Temp: 0.9982, Energy: -53.838918-0.003122j
[2025-08-07 17:15:15] [Iter 6/1050] R0[5/150], Temp: 0.9973, Energy: -53.819603-0.000055j
[2025-08-07 17:15:19] [Iter 7/1050] R0[6/150], Temp: 0.9961, Energy: -53.694486-0.000209j
[2025-08-07 17:15:23] [Iter 8/1050] R0[7/150], Temp: 0.9946, Energy: -53.652294-0.005511j
[2025-08-07 17:15:27] [Iter 9/1050] R0[8/150], Temp: 0.9930, Energy: -53.682119-0.007424j
[2025-08-07 17:15:31] [Iter 10/1050] R0[9/150], Temp: 0.9911, Energy: -53.808303-0.004638j
[2025-08-07 17:15:35] [Iter 11/1050] R0[10/150], Temp: 0.9891, Energy: -53.699776-0.005686j
[2025-08-07 17:15:40] [Iter 12/1050] R0[11/150], Temp: 0.9868, Energy: -53.701052-0.004212j
[2025-08-07 17:15:44] [Iter 13/1050] R0[12/150], Temp: 0.9843, Energy: -53.611559-0.005460j
[2025-08-07 17:15:48] [Iter 14/1050] R0[13/150], Temp: 0.9816, Energy: -53.687476+0.000608j
[2025-08-07 17:15:52] [Iter 15/1050] R0[14/150], Temp: 0.9787, Energy: -53.753081-0.005952j
[2025-08-07 17:15:56] [Iter 16/1050] R0[15/150], Temp: 0.9755, Energy: -53.654816-0.002717j
[2025-08-07 17:16:00] [Iter 17/1050] R0[16/150], Temp: 0.9722, Energy: -53.656561-0.001689j
[2025-08-07 17:16:04] [Iter 18/1050] R0[17/150], Temp: 0.9686, Energy: -53.762577-0.004509j
[2025-08-07 17:16:08] [Iter 19/1050] R0[18/150], Temp: 0.9649, Energy: -53.759058+0.001058j
[2025-08-07 17:16:12] [Iter 20/1050] R0[19/150], Temp: 0.9609, Energy: -53.734117-0.004052j
[2025-08-07 17:16:17] [Iter 21/1050] R0[20/150], Temp: 0.9568, Energy: -53.863241+0.000457j
[2025-08-07 17:16:21] [Iter 22/1050] R0[21/150], Temp: 0.9524, Energy: -53.770986+0.003203j
[2025-08-07 17:16:25] [Iter 23/1050] R0[22/150], Temp: 0.9479, Energy: -53.764829-0.008540j
[2025-08-07 17:16:29] [Iter 24/1050] R0[23/150], Temp: 0.9431, Energy: -53.775985-0.004597j
[2025-08-07 17:16:33] [Iter 25/1050] R0[24/150], Temp: 0.9382, Energy: -53.814185-0.001327j
[2025-08-07 17:16:37] [Iter 26/1050] R0[25/150], Temp: 0.9330, Energy: -53.873098+0.002037j
[2025-08-07 17:16:41] [Iter 27/1050] R0[26/150], Temp: 0.9277, Energy: -53.934838+0.000859j
[2025-08-07 17:16:45] [Iter 28/1050] R0[27/150], Temp: 0.9222, Energy: -53.905485-0.001176j
[2025-08-07 17:16:50] [Iter 29/1050] R0[28/150], Temp: 0.9165, Energy: -53.940926+0.001635j
[2025-08-07 17:16:54] [Iter 30/1050] R0[29/150], Temp: 0.9106, Energy: -53.852439+0.004768j
[2025-08-07 17:16:58] [Iter 31/1050] R0[30/150], Temp: 0.9045, Energy: -53.752533-0.002535j
[2025-08-07 17:17:02] [Iter 32/1050] R0[31/150], Temp: 0.8983, Energy: -53.941429-0.003550j
[2025-08-07 17:17:06] [Iter 33/1050] R0[32/150], Temp: 0.8918, Energy: -53.853692-0.000562j
[2025-08-07 17:17:10] [Iter 34/1050] R0[33/150], Temp: 0.8853, Energy: -53.830370+0.002338j
[2025-08-07 17:17:14] [Iter 35/1050] R0[34/150], Temp: 0.8785, Energy: -53.916263-0.003764j
[2025-08-07 17:17:18] [Iter 36/1050] R0[35/150], Temp: 0.8716, Energy: -53.875046+0.005230j
[2025-08-07 17:17:22] [Iter 37/1050] R0[36/150], Temp: 0.8645, Energy: -53.849531+0.000130j
[2025-08-07 17:17:27] [Iter 38/1050] R0[37/150], Temp: 0.8572, Energy: -54.017099+0.000648j
[2025-08-07 17:17:31] [Iter 39/1050] R0[38/150], Temp: 0.8498, Energy: -53.930414+0.000143j
[2025-08-07 17:17:35] [Iter 40/1050] R0[39/150], Temp: 0.8423, Energy: -53.821694+0.006133j
[2025-08-07 17:17:39] [Iter 41/1050] R0[40/150], Temp: 0.8346, Energy: -53.775962+0.003592j
[2025-08-07 17:17:43] [Iter 42/1050] R0[41/150], Temp: 0.8267, Energy: -53.832885-0.009127j
[2025-08-07 17:17:47] [Iter 43/1050] R0[42/150], Temp: 0.8187, Energy: -53.811177+0.003719j
[2025-08-07 17:17:52] [Iter 44/1050] R0[43/150], Temp: 0.8106, Energy: -53.838364-0.000450j
[2025-08-07 17:17:56] [Iter 45/1050] R0[44/150], Temp: 0.8023, Energy: -53.708570-0.002073j
[2025-08-07 17:18:00] [Iter 46/1050] R0[45/150], Temp: 0.7939, Energy: -53.810170-0.002764j
[2025-08-07 17:18:04] [Iter 47/1050] R0[46/150], Temp: 0.7854, Energy: -53.692368-0.001516j
[2025-08-07 17:18:08] [Iter 48/1050] R0[47/150], Temp: 0.7767, Energy: -53.739840-0.001951j
[2025-08-07 17:18:12] [Iter 49/1050] R0[48/150], Temp: 0.7679, Energy: -53.787145-0.003919j
[2025-08-07 17:18:16] [Iter 50/1050] R0[49/150], Temp: 0.7590, Energy: -53.915290+0.002197j
[2025-08-07 17:18:20] [Iter 51/1050] R0[50/150], Temp: 0.7500, Energy: -53.785134+0.000575j
[2025-08-07 17:18:25] [Iter 52/1050] R0[51/150], Temp: 0.7409, Energy: -53.805481-0.007312j
[2025-08-07 17:18:29] [Iter 53/1050] R0[52/150], Temp: 0.7316, Energy: -53.804484-0.007039j
[2025-08-07 17:18:33] [Iter 54/1050] R0[53/150], Temp: 0.7223, Energy: -53.840426-0.001670j
[2025-08-07 17:18:37] [Iter 55/1050] R0[54/150], Temp: 0.7129, Energy: -53.915601-0.002780j
[2025-08-07 17:18:41] [Iter 56/1050] R0[55/150], Temp: 0.7034, Energy: -53.777972+0.001439j
[2025-08-07 17:18:45] [Iter 57/1050] R0[56/150], Temp: 0.6938, Energy: -53.870843+0.003259j
[2025-08-07 17:18:49] [Iter 58/1050] R0[57/150], Temp: 0.6841, Energy: -53.943152+0.002737j
[2025-08-07 17:18:53] [Iter 59/1050] R0[58/150], Temp: 0.6743, Energy: -53.825109-0.005661j
[2025-08-07 17:18:57] [Iter 60/1050] R0[59/150], Temp: 0.6644, Energy: -53.801789+0.000845j
[2025-08-07 17:19:02] [Iter 61/1050] R0[60/150], Temp: 0.6545, Energy: -53.888865-0.003113j
[2025-08-07 17:19:06] [Iter 62/1050] R0[61/150], Temp: 0.6445, Energy: -53.893218+0.000913j
[2025-08-07 17:19:10] [Iter 63/1050] R0[62/150], Temp: 0.6345, Energy: -53.906939+0.003398j
[2025-08-07 17:19:14] [Iter 64/1050] R0[63/150], Temp: 0.6243, Energy: -53.842267-0.002280j
[2025-08-07 17:19:18] [Iter 65/1050] R0[64/150], Temp: 0.6142, Energy: -53.755268-0.002571j
[2025-08-07 17:19:22] [Iter 66/1050] R0[65/150], Temp: 0.6040, Energy: -53.752189-0.000879j
[2025-08-07 17:19:26] [Iter 67/1050] R0[66/150], Temp: 0.5937, Energy: -53.725826+0.006812j
[2025-08-07 17:19:30] [Iter 68/1050] R0[67/150], Temp: 0.5834, Energy: -53.794117-0.000278j
[2025-08-07 17:19:35] [Iter 69/1050] R0[68/150], Temp: 0.5730, Energy: -53.846321-0.004680j
[2025-08-07 17:19:39] [Iter 70/1050] R0[69/150], Temp: 0.5627, Energy: -53.705441-0.001180j
[2025-08-07 17:19:43] [Iter 71/1050] R0[70/150], Temp: 0.5523, Energy: -53.752562-0.001163j
[2025-08-07 17:19:47] [Iter 72/1050] R0[71/150], Temp: 0.5418, Energy: -53.796284-0.000287j
[2025-08-07 17:19:51] [Iter 73/1050] R0[72/150], Temp: 0.5314, Energy: -53.752422+0.000540j
[2025-08-07 17:19:55] [Iter 74/1050] R0[73/150], Temp: 0.5209, Energy: -53.680031+0.002906j
[2025-08-07 17:19:59] [Iter 75/1050] R0[74/150], Temp: 0.5105, Energy: -53.719323+0.000426j
[2025-08-07 17:20:03] [Iter 76/1050] R0[75/150], Temp: 0.5000, Energy: -53.828714+0.000265j
[2025-08-07 17:20:08] [Iter 77/1050] R0[76/150], Temp: 0.4895, Energy: -53.822215+0.002074j
[2025-08-07 17:20:12] [Iter 78/1050] R0[77/150], Temp: 0.4791, Energy: -53.772450-0.003035j
[2025-08-07 17:20:16] [Iter 79/1050] R0[78/150], Temp: 0.4686, Energy: -53.754148-0.000228j
[2025-08-07 17:20:20] [Iter 80/1050] R0[79/150], Temp: 0.4582, Energy: -53.796714+0.002271j
[2025-08-07 17:20:24] [Iter 81/1050] R0[80/150], Temp: 0.4477, Energy: -53.850673+0.005673j
[2025-08-07 17:20:28] [Iter 82/1050] R0[81/150], Temp: 0.4373, Energy: -53.844493+0.002600j
[2025-08-07 17:20:32] [Iter 83/1050] R0[82/150], Temp: 0.4270, Energy: -53.784248-0.001324j
[2025-08-07 17:20:36] [Iter 84/1050] R0[83/150], Temp: 0.4166, Energy: -53.858069+0.007666j
[2025-08-07 17:20:40] [Iter 85/1050] R0[84/150], Temp: 0.4063, Energy: -53.861781-0.001719j
[2025-08-07 17:20:45] [Iter 86/1050] R0[85/150], Temp: 0.3960, Energy: -53.770901-0.002153j
[2025-08-07 17:20:49] [Iter 87/1050] R0[86/150], Temp: 0.3858, Energy: -53.772126-0.004089j
[2025-08-07 17:20:53] [Iter 88/1050] R0[87/150], Temp: 0.3757, Energy: -53.820961-0.003387j
[2025-08-07 17:20:57] [Iter 89/1050] R0[88/150], Temp: 0.3655, Energy: -53.733983-0.002762j
[2025-08-07 17:21:01] [Iter 90/1050] R0[89/150], Temp: 0.3555, Energy: -53.714615-0.000731j
[2025-08-07 17:21:05] [Iter 91/1050] R0[90/150], Temp: 0.3455, Energy: -53.663903-0.004754j
[2025-08-07 17:21:09] [Iter 92/1050] R0[91/150], Temp: 0.3356, Energy: -53.640534+0.000028j
[2025-08-07 17:21:13] [Iter 93/1050] R0[92/150], Temp: 0.3257, Energy: -53.655357-0.001487j
[2025-08-07 17:21:18] [Iter 94/1050] R0[93/150], Temp: 0.3159, Energy: -53.704746-0.001635j
[2025-08-07 17:21:22] [Iter 95/1050] R0[94/150], Temp: 0.3062, Energy: -53.738216-0.000341j
[2025-08-07 17:21:26] [Iter 96/1050] R0[95/150], Temp: 0.2966, Energy: -53.663859+0.000415j
[2025-08-07 17:21:30] [Iter 97/1050] R0[96/150], Temp: 0.2871, Energy: -53.646844+0.003947j
[2025-08-07 17:21:34] [Iter 98/1050] R0[97/150], Temp: 0.2777, Energy: -53.694588+0.001055j
[2025-08-07 17:21:38] [Iter 99/1050] R0[98/150], Temp: 0.2684, Energy: -53.783638-0.000053j
[2025-08-07 17:21:42] [Iter 100/1050] R0[99/150], Temp: 0.2591, Energy: -53.667784-0.002396j
[2025-08-07 17:21:42] ✓ Checkpoint saved: checkpoint_iter_000100.pkl
[2025-08-07 17:21:46] [Iter 101/1050] R0[100/150], Temp: 0.2500, Energy: -53.658895+0.002092j
[2025-08-07 17:21:51] [Iter 102/1050] R0[101/150], Temp: 0.2410, Energy: -53.771428-0.001370j
[2025-08-07 17:21:55] [Iter 103/1050] R0[102/150], Temp: 0.2321, Energy: -53.789987-0.004388j
[2025-08-07 17:21:59] [Iter 104/1050] R0[103/150], Temp: 0.2233, Energy: -53.809189-0.002611j
[2025-08-07 17:22:03] [Iter 105/1050] R0[104/150], Temp: 0.2146, Energy: -53.684395-0.004492j
[2025-08-07 17:22:07] [Iter 106/1050] R0[105/150], Temp: 0.2061, Energy: -53.686505-0.000481j
[2025-08-07 17:22:11] [Iter 107/1050] R0[106/150], Temp: 0.1977, Energy: -53.770363+0.001600j
[2025-08-07 17:22:15] [Iter 108/1050] R0[107/150], Temp: 0.1894, Energy: -53.727624-0.001922j
[2025-08-07 17:22:19] [Iter 109/1050] R0[108/150], Temp: 0.1813, Energy: -53.731894+0.001046j
[2025-08-07 17:22:24] [Iter 110/1050] R0[109/150], Temp: 0.1733, Energy: -53.738756+0.000167j
[2025-08-07 17:22:28] [Iter 111/1050] R0[110/150], Temp: 0.1654, Energy: -53.833854-0.000254j
[2025-08-07 17:22:32] [Iter 112/1050] R0[111/150], Temp: 0.1577, Energy: -53.762483+0.002615j
[2025-08-07 17:22:36] [Iter 113/1050] R0[112/150], Temp: 0.1502, Energy: -53.754809-0.003305j
[2025-08-07 17:22:40] [Iter 114/1050] R0[113/150], Temp: 0.1428, Energy: -53.701990-0.000289j
[2025-08-07 17:22:44] [Iter 115/1050] R0[114/150], Temp: 0.1355, Energy: -53.644542+0.001541j
[2025-08-07 17:22:48] [Iter 116/1050] R0[115/150], Temp: 0.1284, Energy: -53.767118+0.001139j
[2025-08-07 17:22:52] [Iter 117/1050] R0[116/150], Temp: 0.1215, Energy: -53.704483+0.003601j
[2025-08-07 17:22:56] [Iter 118/1050] R0[117/150], Temp: 0.1147, Energy: -53.848946+0.007534j
[2025-08-07 17:23:01] [Iter 119/1050] R0[118/150], Temp: 0.1082, Energy: -53.802058+0.001456j
[2025-08-07 17:23:05] [Iter 120/1050] R0[119/150], Temp: 0.1017, Energy: -53.718146+0.000496j
[2025-08-07 17:23:09] [Iter 121/1050] R0[120/150], Temp: 0.0955, Energy: -53.654423-0.001030j
[2025-08-07 17:23:13] [Iter 122/1050] R0[121/150], Temp: 0.0894, Energy: -53.741018+0.001388j
[2025-08-07 17:23:17] [Iter 123/1050] R0[122/150], Temp: 0.0835, Energy: -53.704031+0.003516j
[2025-08-07 17:23:21] [Iter 124/1050] R0[123/150], Temp: 0.0778, Energy: -53.749536-0.003459j
[2025-08-07 17:23:25] [Iter 125/1050] R0[124/150], Temp: 0.0723, Energy: -53.680450-0.003501j
[2025-08-07 17:23:30] [Iter 126/1050] R0[125/150], Temp: 0.0670, Energy: -53.844905+0.001558j
[2025-08-07 17:23:34] [Iter 127/1050] R0[126/150], Temp: 0.0618, Energy: -53.726452-0.001209j
[2025-08-07 17:23:38] [Iter 128/1050] R0[127/150], Temp: 0.0569, Energy: -53.756380+0.003192j
[2025-08-07 17:23:42] [Iter 129/1050] R0[128/150], Temp: 0.0521, Energy: -53.874888+0.002989j
[2025-08-07 17:23:46] [Iter 130/1050] R0[129/150], Temp: 0.0476, Energy: -53.729021+0.005615j
[2025-08-07 17:23:50] [Iter 131/1050] R0[130/150], Temp: 0.0432, Energy: -53.762676+0.002762j
[2025-08-07 17:23:54] [Iter 132/1050] R0[131/150], Temp: 0.0391, Energy: -53.663490-0.001031j
[2025-08-07 17:23:58] [Iter 133/1050] R0[132/150], Temp: 0.0351, Energy: -53.823528-0.005528j
[2025-08-07 17:24:02] [Iter 134/1050] R0[133/150], Temp: 0.0314, Energy: -53.756087-0.002663j
[2025-08-07 17:24:07] [Iter 135/1050] R0[134/150], Temp: 0.0278, Energy: -53.856216-0.002048j
[2025-08-07 17:24:11] [Iter 136/1050] R0[135/150], Temp: 0.0245, Energy: -53.911072-0.002034j
[2025-08-07 17:24:15] [Iter 137/1050] R0[136/150], Temp: 0.0213, Energy: -53.881511+0.000531j
[2025-08-07 17:24:19] [Iter 138/1050] R0[137/150], Temp: 0.0184, Energy: -53.872537+0.000316j
[2025-08-07 17:24:23] [Iter 139/1050] R0[138/150], Temp: 0.0157, Energy: -53.879704-0.000048j
[2025-08-07 17:24:27] [Iter 140/1050] R0[139/150], Temp: 0.0132, Energy: -53.868243+0.001345j
[2025-08-07 17:24:31] [Iter 141/1050] R0[140/150], Temp: 0.0109, Energy: -53.916805+0.000356j
[2025-08-07 17:24:35] [Iter 142/1050] R0[141/150], Temp: 0.0089, Energy: -53.978467+0.000585j
[2025-08-07 17:24:40] [Iter 143/1050] R0[142/150], Temp: 0.0070, Energy: -53.995654-0.000917j
[2025-08-07 17:24:44] [Iter 144/1050] R0[143/150], Temp: 0.0054, Energy: -53.875379+0.002688j
[2025-08-07 17:24:48] [Iter 145/1050] R0[144/150], Temp: 0.0039, Energy: -53.726516+0.003694j
[2025-08-07 17:24:52] [Iter 146/1050] R0[145/150], Temp: 0.0027, Energy: -53.779891-0.001440j
[2025-08-07 17:24:56] [Iter 147/1050] R0[146/150], Temp: 0.0018, Energy: -53.720816+0.003017j
[2025-08-07 17:25:00] [Iter 148/1050] R0[147/150], Temp: 0.0010, Energy: -53.718349+0.000315j
[2025-08-07 17:25:04] [Iter 149/1050] R0[148/150], Temp: 0.0004, Energy: -53.667389+0.003753j
[2025-08-07 17:25:08] [Iter 150/1050] R0[149/150], Temp: 0.0001, Energy: -53.808756+0.000172j
[2025-08-07 17:25:08] RESTART #1 | Period: 300
[2025-08-07 17:25:12] [Iter 151/1050] R1[0/300], Temp: 1.0000, Energy: -53.778686+0.000253j
[2025-08-07 17:25:17] [Iter 152/1050] R1[1/300], Temp: 1.0000, Energy: -53.819597-0.002602j
[2025-08-07 17:25:21] [Iter 153/1050] R1[2/300], Temp: 0.9999, Energy: -53.871861-0.000177j
[2025-08-07 17:25:25] [Iter 154/1050] R1[3/300], Temp: 0.9998, Energy: -53.783063+0.005079j
[2025-08-07 17:25:29] [Iter 155/1050] R1[4/300], Temp: 0.9996, Energy: -53.712798+0.004940j
[2025-08-07 17:25:33] [Iter 156/1050] R1[5/300], Temp: 0.9993, Energy: -53.827979-0.000205j
[2025-08-07 17:25:37] [Iter 157/1050] R1[6/300], Temp: 0.9990, Energy: -53.932213+0.001919j
[2025-08-07 17:25:41] [Iter 158/1050] R1[7/300], Temp: 0.9987, Energy: -53.961527-0.000600j
[2025-08-07 17:25:45] [Iter 159/1050] R1[8/300], Temp: 0.9982, Energy: -53.968103+0.001534j
[2025-08-07 17:25:50] [Iter 160/1050] R1[9/300], Temp: 0.9978, Energy: -53.952486-0.000993j
[2025-08-07 17:25:54] [Iter 161/1050] R1[10/300], Temp: 0.9973, Energy: -53.933402+0.003576j
[2025-08-07 17:25:58] [Iter 162/1050] R1[11/300], Temp: 0.9967, Energy: -53.842617-0.003006j
[2025-08-07 17:26:02] [Iter 163/1050] R1[12/300], Temp: 0.9961, Energy: -53.808044-0.005487j
[2025-08-07 17:26:06] [Iter 164/1050] R1[13/300], Temp: 0.9954, Energy: -53.834997+0.005049j
[2025-08-07 17:26:10] [Iter 165/1050] R1[14/300], Temp: 0.9946, Energy: -53.690704+0.001462j
[2025-08-07 17:26:14] [Iter 166/1050] R1[15/300], Temp: 0.9938, Energy: -53.764978+0.005016j
[2025-08-07 17:26:18] [Iter 167/1050] R1[16/300], Temp: 0.9930, Energy: -53.821007+0.001536j
[2025-08-07 17:26:22] [Iter 168/1050] R1[17/300], Temp: 0.9921, Energy: -53.758644+0.001621j
[2025-08-07 17:26:27] [Iter 169/1050] R1[18/300], Temp: 0.9911, Energy: -53.757103-0.007331j
[2025-08-07 17:26:31] [Iter 170/1050] R1[19/300], Temp: 0.9901, Energy: -53.867953+0.003437j
[2025-08-07 17:26:35] [Iter 171/1050] R1[20/300], Temp: 0.9891, Energy: -53.929130+0.004480j
[2025-08-07 17:26:39] [Iter 172/1050] R1[21/300], Temp: 0.9880, Energy: -53.907176-0.003271j
[2025-08-07 17:26:43] [Iter 173/1050] R1[22/300], Temp: 0.9868, Energy: -53.835197+0.000924j
[2025-08-07 17:26:47] [Iter 174/1050] R1[23/300], Temp: 0.9856, Energy: -53.866014+0.000284j
[2025-08-07 17:26:51] [Iter 175/1050] R1[24/300], Temp: 0.9843, Energy: -53.773274+0.000180j
[2025-08-07 17:26:55] [Iter 176/1050] R1[25/300], Temp: 0.9830, Energy: -53.814024-0.001156j
[2025-08-07 17:27:00] [Iter 177/1050] R1[26/300], Temp: 0.9816, Energy: -53.802769+0.001878j
[2025-08-07 17:27:04] [Iter 178/1050] R1[27/300], Temp: 0.9801, Energy: -53.823457+0.004328j
[2025-08-07 17:27:08] [Iter 179/1050] R1[28/300], Temp: 0.9787, Energy: -53.916932+0.006449j
[2025-08-07 17:27:12] [Iter 180/1050] R1[29/300], Temp: 0.9771, Energy: -53.959941-0.001617j
[2025-08-07 17:27:16] [Iter 181/1050] R1[30/300], Temp: 0.9755, Energy: -53.843542-0.002929j
[2025-08-07 17:27:20] [Iter 182/1050] R1[31/300], Temp: 0.9739, Energy: -53.861732-0.000574j
[2025-08-07 17:27:24] [Iter 183/1050] R1[32/300], Temp: 0.9722, Energy: -53.751852+0.002381j
[2025-08-07 17:27:28] [Iter 184/1050] R1[33/300], Temp: 0.9704, Energy: -53.648063-0.002826j
[2025-08-07 17:27:32] [Iter 185/1050] R1[34/300], Temp: 0.9686, Energy: -53.703044-0.001563j
[2025-08-07 17:27:37] [Iter 186/1050] R1[35/300], Temp: 0.9668, Energy: -53.766613-0.000992j
[2025-08-07 17:27:41] [Iter 187/1050] R1[36/300], Temp: 0.9649, Energy: -53.758375+0.001439j
[2025-08-07 17:27:45] [Iter 188/1050] R1[37/300], Temp: 0.9629, Energy: -53.822306-0.000411j
[2025-08-07 17:27:49] [Iter 189/1050] R1[38/300], Temp: 0.9609, Energy: -53.803335+0.003377j
[2025-08-07 17:27:53] [Iter 190/1050] R1[39/300], Temp: 0.9589, Energy: -53.804367-0.000324j
[2025-08-07 17:27:57] [Iter 191/1050] R1[40/300], Temp: 0.9568, Energy: -53.759525-0.003185j
[2025-08-07 17:28:01] [Iter 192/1050] R1[41/300], Temp: 0.9546, Energy: -53.704967+0.003597j
[2025-08-07 17:28:05] [Iter 193/1050] R1[42/300], Temp: 0.9524, Energy: -53.825640+0.002289j
[2025-08-07 17:28:10] [Iter 194/1050] R1[43/300], Temp: 0.9502, Energy: -53.832056+0.001740j
[2025-08-07 17:28:14] [Iter 195/1050] R1[44/300], Temp: 0.9479, Energy: -53.881367+0.002081j
[2025-08-07 17:28:18] [Iter 196/1050] R1[45/300], Temp: 0.9455, Energy: -53.802879+0.000324j
[2025-08-07 17:28:22] [Iter 197/1050] R1[46/300], Temp: 0.9431, Energy: -53.822750-0.001904j
[2025-08-07 17:28:26] [Iter 198/1050] R1[47/300], Temp: 0.9407, Energy: -53.912815-0.002853j
[2025-08-07 17:28:30] [Iter 199/1050] R1[48/300], Temp: 0.9382, Energy: -53.811808+0.000287j
[2025-08-07 17:28:34] [Iter 200/1050] R1[49/300], Temp: 0.9356, Energy: -53.825690+0.004171j
[2025-08-07 17:28:34] ✓ Checkpoint saved: checkpoint_iter_000200.pkl
[2025-08-07 17:28:38] [Iter 201/1050] R1[50/300], Temp: 0.9330, Energy: -53.909218+0.000286j
[2025-08-07 17:28:42] [Iter 202/1050] R1[51/300], Temp: 0.9304, Energy: -53.871164-0.001308j
[2025-08-07 17:28:47] [Iter 203/1050] R1[52/300], Temp: 0.9277, Energy: -53.863892-0.004955j
[2025-08-07 17:28:51] [Iter 204/1050] R1[53/300], Temp: 0.9249, Energy: -53.855866-0.002043j
[2025-08-07 17:28:55] [Iter 205/1050] R1[54/300], Temp: 0.9222, Energy: -53.870090-0.002294j
[2025-08-07 17:28:59] [Iter 206/1050] R1[55/300], Temp: 0.9193, Energy: -53.937992-0.002865j
[2025-08-07 17:29:03] [Iter 207/1050] R1[56/300], Temp: 0.9165, Energy: -53.777977+0.002143j
[2025-08-07 17:29:07] [Iter 208/1050] R1[57/300], Temp: 0.9135, Energy: -53.820300+0.001775j
[2025-08-07 17:29:11] [Iter 209/1050] R1[58/300], Temp: 0.9106, Energy: -53.771725-0.003142j
[2025-08-07 17:29:15] [Iter 210/1050] R1[59/300], Temp: 0.9076, Energy: -53.883552-0.000050j
[2025-08-07 17:29:20] [Iter 211/1050] R1[60/300], Temp: 0.9045, Energy: -53.895192+0.003218j
[2025-08-07 17:29:24] [Iter 212/1050] R1[61/300], Temp: 0.9014, Energy: -53.998361-0.005537j
[2025-08-07 17:29:28] [Iter 213/1050] R1[62/300], Temp: 0.8983, Energy: -53.933828+0.001782j
[2025-08-07 17:29:32] [Iter 214/1050] R1[63/300], Temp: 0.8951, Energy: -53.929217-0.002586j
[2025-08-07 17:29:36] [Iter 215/1050] R1[64/300], Temp: 0.8918, Energy: -53.923802-0.003177j
[2025-08-07 17:29:40] [Iter 216/1050] R1[65/300], Temp: 0.8886, Energy: -54.020581-0.003991j
[2025-08-07 17:29:44] [Iter 217/1050] R1[66/300], Temp: 0.8853, Energy: -53.859598+0.002074j
[2025-08-07 17:29:48] [Iter 218/1050] R1[67/300], Temp: 0.8819, Energy: -53.877284-0.003641j
[2025-08-07 17:29:52] [Iter 219/1050] R1[68/300], Temp: 0.8785, Energy: -53.854280-0.000462j
[2025-08-07 17:29:57] [Iter 220/1050] R1[69/300], Temp: 0.8751, Energy: -53.922452+0.004298j
[2025-08-07 17:30:01] [Iter 221/1050] R1[70/300], Temp: 0.8716, Energy: -53.864653-0.001766j
[2025-08-07 17:30:05] [Iter 222/1050] R1[71/300], Temp: 0.8680, Energy: -53.820177-0.001976j
[2025-08-07 17:30:09] [Iter 223/1050] R1[72/300], Temp: 0.8645, Energy: -53.850027-0.004661j
[2025-08-07 17:30:13] [Iter 224/1050] R1[73/300], Temp: 0.8609, Energy: -53.731810+0.008127j
[2025-08-07 17:30:17] [Iter 225/1050] R1[74/300], Temp: 0.8572, Energy: -53.731861-0.003651j
[2025-08-07 17:30:21] [Iter 226/1050] R1[75/300], Temp: 0.8536, Energy: -53.820564-0.004495j
[2025-08-07 17:30:25] [Iter 227/1050] R1[76/300], Temp: 0.8498, Energy: -53.891652+0.002935j
[2025-08-07 17:30:30] [Iter 228/1050] R1[77/300], Temp: 0.8461, Energy: -53.872755+0.002625j
[2025-08-07 17:30:34] [Iter 229/1050] R1[78/300], Temp: 0.8423, Energy: -53.915656-0.000059j
[2025-08-07 17:30:38] [Iter 230/1050] R1[79/300], Temp: 0.8384, Energy: -53.780774-0.001707j
[2025-08-07 17:30:42] [Iter 231/1050] R1[80/300], Temp: 0.8346, Energy: -53.860101-0.002399j
[2025-08-07 17:30:46] [Iter 232/1050] R1[81/300], Temp: 0.8307, Energy: -53.945662+0.000589j
[2025-08-07 17:30:50] [Iter 233/1050] R1[82/300], Temp: 0.8267, Energy: -53.793480+0.002139j
[2025-08-07 17:30:54] [Iter 234/1050] R1[83/300], Temp: 0.8227, Energy: -53.890352-0.003285j
[2025-08-07 17:30:58] [Iter 235/1050] R1[84/300], Temp: 0.8187, Energy: -53.905953-0.001067j
[2025-08-07 17:31:02] [Iter 236/1050] R1[85/300], Temp: 0.8147, Energy: -53.864166+0.001801j
[2025-08-07 17:31:07] [Iter 237/1050] R1[86/300], Temp: 0.8106, Energy: -53.786866+0.000382j
[2025-08-07 17:31:11] [Iter 238/1050] R1[87/300], Temp: 0.8065, Energy: -53.806636-0.001402j
[2025-08-07 17:31:15] [Iter 239/1050] R1[88/300], Temp: 0.8023, Energy: -53.886328-0.001433j
[2025-08-07 17:31:19] [Iter 240/1050] R1[89/300], Temp: 0.7981, Energy: -53.773138+0.007731j
[2025-08-07 17:31:23] [Iter 241/1050] R1[90/300], Temp: 0.7939, Energy: -53.873667-0.001512j
[2025-08-07 17:31:27] [Iter 242/1050] R1[91/300], Temp: 0.7896, Energy: -53.909607-0.000354j
[2025-08-07 17:31:31] [Iter 243/1050] R1[92/300], Temp: 0.7854, Energy: -53.904056+0.000468j
[2025-08-07 17:31:35] [Iter 244/1050] R1[93/300], Temp: 0.7810, Energy: -53.894675+0.001805j
[2025-08-07 17:31:40] [Iter 245/1050] R1[94/300], Temp: 0.7767, Energy: -53.872840+0.003694j
[2025-08-07 17:31:44] [Iter 246/1050] R1[95/300], Temp: 0.7723, Energy: -53.890520+0.001242j
[2025-08-07 17:31:48] [Iter 247/1050] R1[96/300], Temp: 0.7679, Energy: -53.922257+0.000579j
[2025-08-07 17:31:52] [Iter 248/1050] R1[97/300], Temp: 0.7635, Energy: -53.910109-0.005168j
[2025-08-07 17:31:56] [Iter 249/1050] R1[98/300], Temp: 0.7590, Energy: -53.865415+0.000248j
[2025-08-07 17:32:00] [Iter 250/1050] R1[99/300], Temp: 0.7545, Energy: -53.886122+0.006458j
[2025-08-07 17:32:04] [Iter 251/1050] R1[100/300], Temp: 0.7500, Energy: -53.886846+0.002613j
[2025-08-07 17:32:08] [Iter 252/1050] R1[101/300], Temp: 0.7455, Energy: -53.897697+0.001835j
[2025-08-07 17:32:12] [Iter 253/1050] R1[102/300], Temp: 0.7409, Energy: -53.868503+0.000972j
[2025-08-07 17:32:17] [Iter 254/1050] R1[103/300], Temp: 0.7363, Energy: -53.808004-0.001496j
[2025-08-07 17:32:21] [Iter 255/1050] R1[104/300], Temp: 0.7316, Energy: -53.670193+0.000237j
[2025-08-07 17:32:25] [Iter 256/1050] R1[105/300], Temp: 0.7270, Energy: -53.713748-0.002171j
[2025-08-07 17:32:29] [Iter 257/1050] R1[106/300], Temp: 0.7223, Energy: -53.656433-0.003469j
[2025-08-07 17:32:33] [Iter 258/1050] R1[107/300], Temp: 0.7176, Energy: -53.789212-0.010660j
[2025-08-07 17:32:37] [Iter 259/1050] R1[108/300], Temp: 0.7129, Energy: -53.852521-0.004587j
[2025-08-07 17:32:41] [Iter 260/1050] R1[109/300], Temp: 0.7081, Energy: -53.866840+0.005301j
[2025-08-07 17:32:45] [Iter 261/1050] R1[110/300], Temp: 0.7034, Energy: -53.844682-0.000882j
[2025-08-07 17:32:50] [Iter 262/1050] R1[111/300], Temp: 0.6986, Energy: -53.748010+0.001075j
[2025-08-07 17:32:54] [Iter 263/1050] R1[112/300], Temp: 0.6938, Energy: -53.796468+0.000362j
[2025-08-07 17:32:58] [Iter 264/1050] R1[113/300], Temp: 0.6889, Energy: -53.813252+0.003420j
[2025-08-07 17:33:02] [Iter 265/1050] R1[114/300], Temp: 0.6841, Energy: -53.854204-0.005095j
[2025-08-07 17:33:06] [Iter 266/1050] R1[115/300], Temp: 0.6792, Energy: -53.832985-0.000210j
[2025-08-07 17:33:10] [Iter 267/1050] R1[116/300], Temp: 0.6743, Energy: -53.785976+0.001299j
[2025-08-07 17:33:14] [Iter 268/1050] R1[117/300], Temp: 0.6694, Energy: -53.754037+0.005534j
[2025-08-07 17:33:18] [Iter 269/1050] R1[118/300], Temp: 0.6644, Energy: -53.761832+0.002560j
[2025-08-07 17:33:22] [Iter 270/1050] R1[119/300], Temp: 0.6595, Energy: -53.840861-0.001889j
[2025-08-07 17:33:27] [Iter 271/1050] R1[120/300], Temp: 0.6545, Energy: -53.831985+0.000802j
[2025-08-07 17:33:31] [Iter 272/1050] R1[121/300], Temp: 0.6495, Energy: -53.912978+0.002971j
[2025-08-07 17:33:35] [Iter 273/1050] R1[122/300], Temp: 0.6445, Energy: -53.790892+0.003334j
[2025-08-07 17:33:39] [Iter 274/1050] R1[123/300], Temp: 0.6395, Energy: -53.858084+0.001417j
[2025-08-07 17:33:43] [Iter 275/1050] R1[124/300], Temp: 0.6345, Energy: -53.863352+0.000098j
[2025-08-07 17:33:47] [Iter 276/1050] R1[125/300], Temp: 0.6294, Energy: -53.831782-0.000784j
[2025-08-07 17:33:51] [Iter 277/1050] R1[126/300], Temp: 0.6243, Energy: -53.718874-0.007483j
[2025-08-07 17:33:55] [Iter 278/1050] R1[127/300], Temp: 0.6193, Energy: -53.646750+0.003392j
[2025-08-07 17:34:00] [Iter 279/1050] R1[128/300], Temp: 0.6142, Energy: -53.712491-0.000078j
[2025-08-07 17:34:04] [Iter 280/1050] R1[129/300], Temp: 0.6091, Energy: -53.792308-0.004220j
[2025-08-07 17:34:08] [Iter 281/1050] R1[130/300], Temp: 0.6040, Energy: -53.731715+0.000785j
[2025-08-07 17:34:12] [Iter 282/1050] R1[131/300], Temp: 0.5988, Energy: -53.798779-0.001294j
[2025-08-07 17:34:16] [Iter 283/1050] R1[132/300], Temp: 0.5937, Energy: -53.780996+0.000904j
[2025-08-07 17:34:20] [Iter 284/1050] R1[133/300], Temp: 0.5885, Energy: -53.762120-0.006250j
[2025-08-07 17:34:24] [Iter 285/1050] R1[134/300], Temp: 0.5834, Energy: -53.675860+0.002540j
[2025-08-07 17:34:28] [Iter 286/1050] R1[135/300], Temp: 0.5782, Energy: -53.781641-0.001996j
[2025-08-07 17:34:32] [Iter 287/1050] R1[136/300], Temp: 0.5730, Energy: -53.750813+0.002821j
[2025-08-07 17:34:37] [Iter 288/1050] R1[137/300], Temp: 0.5679, Energy: -53.777012-0.003167j
[2025-08-07 17:34:41] [Iter 289/1050] R1[138/300], Temp: 0.5627, Energy: -53.676032+0.003768j
[2025-08-07 17:34:45] [Iter 290/1050] R1[139/300], Temp: 0.5575, Energy: -53.808092-0.003397j
[2025-08-07 17:34:49] [Iter 291/1050] R1[140/300], Temp: 0.5523, Energy: -53.848784+0.000201j
[2025-08-07 17:34:53] [Iter 292/1050] R1[141/300], Temp: 0.5471, Energy: -53.808035+0.000438j
[2025-08-07 17:34:57] [Iter 293/1050] R1[142/300], Temp: 0.5418, Energy: -53.867002-0.005127j
[2025-08-07 17:35:01] [Iter 294/1050] R1[143/300], Temp: 0.5366, Energy: -53.892649+0.002215j
[2025-08-07 17:35:05] [Iter 295/1050] R1[144/300], Temp: 0.5314, Energy: -53.881109+0.001406j
[2025-08-07 17:35:10] [Iter 296/1050] R1[145/300], Temp: 0.5262, Energy: -53.888879-0.002398j
[2025-08-07 17:35:14] [Iter 297/1050] R1[146/300], Temp: 0.5209, Energy: -53.735756-0.000181j
[2025-08-07 17:35:18] [Iter 298/1050] R1[147/300], Temp: 0.5157, Energy: -53.869753-0.004071j
[2025-08-07 17:35:22] [Iter 299/1050] R1[148/300], Temp: 0.5105, Energy: -53.923110-0.003377j
[2025-08-07 17:35:26] [Iter 300/1050] R1[149/300], Temp: 0.5052, Energy: -53.912922-0.003943j
[2025-08-07 17:35:26] ✓ Checkpoint saved: checkpoint_iter_000300.pkl
[2025-08-07 17:35:30] [Iter 301/1050] R1[150/300], Temp: 0.5000, Energy: -53.915262-0.002241j
[2025-08-07 17:35:34] [Iter 302/1050] R1[151/300], Temp: 0.4948, Energy: -53.886285-0.000267j
[2025-08-07 17:35:38] [Iter 303/1050] R1[152/300], Temp: 0.4895, Energy: -53.971887-0.006431j
[2025-08-07 17:35:42] [Iter 304/1050] R1[153/300], Temp: 0.4843, Energy: -53.936475+0.001971j
[2025-08-07 17:35:47] [Iter 305/1050] R1[154/300], Temp: 0.4791, Energy: -53.854105-0.004494j
[2025-08-07 17:35:51] [Iter 306/1050] R1[155/300], Temp: 0.4738, Energy: -53.893046+0.003383j
[2025-08-07 17:35:55] [Iter 307/1050] R1[156/300], Temp: 0.4686, Energy: -53.790622+0.000576j
[2025-08-07 17:35:59] [Iter 308/1050] R1[157/300], Temp: 0.4634, Energy: -53.957490-0.005604j
[2025-08-07 17:36:03] [Iter 309/1050] R1[158/300], Temp: 0.4582, Energy: -53.989656-0.001624j
[2025-08-07 17:36:07] [Iter 310/1050] R1[159/300], Temp: 0.4529, Energy: -53.933545-0.000460j
[2025-08-07 17:36:11] [Iter 311/1050] R1[160/300], Temp: 0.4477, Energy: -54.055874+0.004151j
[2025-08-07 17:36:15] [Iter 312/1050] R1[161/300], Temp: 0.4425, Energy: -53.976719+0.003328j
[2025-08-07 17:36:20] [Iter 313/1050] R1[162/300], Temp: 0.4373, Energy: -53.974793-0.000060j
[2025-08-07 17:36:24] [Iter 314/1050] R1[163/300], Temp: 0.4321, Energy: -53.951395-0.000784j
[2025-08-07 17:36:28] [Iter 315/1050] R1[164/300], Temp: 0.4270, Energy: -53.900990-0.001265j
[2025-08-07 17:36:32] [Iter 316/1050] R1[165/300], Temp: 0.4218, Energy: -53.931418-0.003046j
[2025-08-07 17:36:36] [Iter 317/1050] R1[166/300], Temp: 0.4166, Energy: -54.006775+0.000392j
[2025-08-07 17:36:40] [Iter 318/1050] R1[167/300], Temp: 0.4115, Energy: -54.023991-0.001799j
[2025-08-07 17:36:44] [Iter 319/1050] R1[168/300], Temp: 0.4063, Energy: -53.989006+0.008423j
[2025-08-07 17:36:48] [Iter 320/1050] R1[169/300], Temp: 0.4012, Energy: -53.902054+0.003480j
[2025-08-07 17:36:52] [Iter 321/1050] R1[170/300], Temp: 0.3960, Energy: -53.801350+0.001397j
[2025-08-07 17:36:57] [Iter 322/1050] R1[171/300], Temp: 0.3909, Energy: -53.869150+0.004337j
[2025-08-07 17:37:01] [Iter 323/1050] R1[172/300], Temp: 0.3858, Energy: -53.791683+0.007041j
[2025-08-07 17:37:05] [Iter 324/1050] R1[173/300], Temp: 0.3807, Energy: -53.818510+0.000027j
[2025-08-07 17:37:09] [Iter 325/1050] R1[174/300], Temp: 0.3757, Energy: -53.845404+0.000081j
[2025-08-07 17:37:13] [Iter 326/1050] R1[175/300], Temp: 0.3706, Energy: -53.745245-0.000726j
[2025-08-07 17:37:17] [Iter 327/1050] R1[176/300], Temp: 0.3655, Energy: -53.857977-0.000068j
[2025-08-07 17:37:21] [Iter 328/1050] R1[177/300], Temp: 0.3605, Energy: -53.796318+0.002426j
[2025-08-07 17:37:25] [Iter 329/1050] R1[178/300], Temp: 0.3555, Energy: -53.856655+0.001121j
[2025-08-07 17:37:30] [Iter 330/1050] R1[179/300], Temp: 0.3505, Energy: -53.828337+0.000434j
[2025-08-07 17:37:34] [Iter 331/1050] R1[180/300], Temp: 0.3455, Energy: -53.849901-0.002102j
[2025-08-07 17:37:38] [Iter 332/1050] R1[181/300], Temp: 0.3405, Energy: -53.842678+0.000540j
[2025-08-07 17:37:42] [Iter 333/1050] R1[182/300], Temp: 0.3356, Energy: -53.780798-0.006093j
[2025-08-07 17:37:46] [Iter 334/1050] R1[183/300], Temp: 0.3306, Energy: -53.835214+0.001201j
[2025-08-07 17:37:50] [Iter 335/1050] R1[184/300], Temp: 0.3257, Energy: -53.707494-0.000413j
[2025-08-07 17:37:54] [Iter 336/1050] R1[185/300], Temp: 0.3208, Energy: -53.735009-0.001874j
[2025-08-07 17:37:58] [Iter 337/1050] R1[186/300], Temp: 0.3159, Energy: -53.789266-0.000127j
[2025-08-07 17:38:02] [Iter 338/1050] R1[187/300], Temp: 0.3111, Energy: -53.739089+0.003062j
[2025-08-07 17:38:07] [Iter 339/1050] R1[188/300], Temp: 0.3062, Energy: -53.803031+0.002024j
[2025-08-07 17:38:11] [Iter 340/1050] R1[189/300], Temp: 0.3014, Energy: -53.813500-0.003981j
[2025-08-07 17:38:15] [Iter 341/1050] R1[190/300], Temp: 0.2966, Energy: -53.770447+0.001085j
[2025-08-07 17:38:19] [Iter 342/1050] R1[191/300], Temp: 0.2919, Energy: -53.759189+0.000064j
[2025-08-07 17:38:23] [Iter 343/1050] R1[192/300], Temp: 0.2871, Energy: -53.655513+0.000737j
[2025-08-07 17:38:27] [Iter 344/1050] R1[193/300], Temp: 0.2824, Energy: -53.708479+0.001951j
[2025-08-07 17:38:31] [Iter 345/1050] R1[194/300], Temp: 0.2777, Energy: -53.662588+0.001386j
[2025-08-07 17:38:35] [Iter 346/1050] R1[195/300], Temp: 0.2730, Energy: -53.721605-0.001818j
[2025-08-07 17:38:40] [Iter 347/1050] R1[196/300], Temp: 0.2684, Energy: -53.718449+0.004215j
[2025-08-07 17:38:44] [Iter 348/1050] R1[197/300], Temp: 0.2637, Energy: -53.810618-0.001522j
[2025-08-07 17:38:48] [Iter 349/1050] R1[198/300], Temp: 0.2591, Energy: -53.757714+0.003257j
[2025-08-07 17:38:52] [Iter 350/1050] R1[199/300], Temp: 0.2545, Energy: -53.805168-0.004018j
[2025-08-07 17:38:56] [Iter 351/1050] R1[200/300], Temp: 0.2500, Energy: -53.914399-0.000184j
[2025-08-07 17:39:00] [Iter 352/1050] R1[201/300], Temp: 0.2455, Energy: -53.875914-0.001212j
[2025-08-07 17:39:04] [Iter 353/1050] R1[202/300], Temp: 0.2410, Energy: -53.983266-0.002979j
[2025-08-07 17:39:08] [Iter 354/1050] R1[203/300], Temp: 0.2365, Energy: -54.007862-0.002419j
[2025-08-07 17:39:12] [Iter 355/1050] R1[204/300], Temp: 0.2321, Energy: -53.911164+0.004396j
[2025-08-07 17:39:17] [Iter 356/1050] R1[205/300], Temp: 0.2277, Energy: -53.878793+0.001433j
[2025-08-07 17:39:21] [Iter 357/1050] R1[206/300], Temp: 0.2233, Energy: -53.853282+0.001316j
[2025-08-07 17:39:25] [Iter 358/1050] R1[207/300], Temp: 0.2190, Energy: -53.789787-0.001643j
[2025-08-07 17:39:29] [Iter 359/1050] R1[208/300], Temp: 0.2146, Energy: -53.805972+0.001658j
[2025-08-07 17:39:33] [Iter 360/1050] R1[209/300], Temp: 0.2104, Energy: -53.885046-0.001297j
[2025-08-07 17:39:37] [Iter 361/1050] R1[210/300], Temp: 0.2061, Energy: -53.804240+0.005303j
[2025-08-07 17:39:41] [Iter 362/1050] R1[211/300], Temp: 0.2019, Energy: -53.716394+0.001835j
[2025-08-07 17:39:45] [Iter 363/1050] R1[212/300], Temp: 0.1977, Energy: -53.748682+0.002915j
[2025-08-07 17:39:50] [Iter 364/1050] R1[213/300], Temp: 0.1935, Energy: -53.817533+0.008169j
[2025-08-07 17:39:54] [Iter 365/1050] R1[214/300], Temp: 0.1894, Energy: -53.915039+0.006282j
[2025-08-07 17:39:58] [Iter 366/1050] R1[215/300], Temp: 0.1853, Energy: -54.055129-0.001830j
[2025-08-07 17:40:02] [Iter 367/1050] R1[216/300], Temp: 0.1813, Energy: -53.964244-0.005469j
[2025-08-07 17:40:06] [Iter 368/1050] R1[217/300], Temp: 0.1773, Energy: -53.888121-0.002321j
[2025-08-07 17:40:10] [Iter 369/1050] R1[218/300], Temp: 0.1733, Energy: -53.909151-0.001789j
[2025-08-07 17:40:14] [Iter 370/1050] R1[219/300], Temp: 0.1693, Energy: -53.914084-0.001552j
[2025-08-07 17:40:18] [Iter 371/1050] R1[220/300], Temp: 0.1654, Energy: -53.891725-0.001009j
[2025-08-07 17:40:22] [Iter 372/1050] R1[221/300], Temp: 0.1616, Energy: -53.860968+0.000326j
[2025-08-07 17:40:27] [Iter 373/1050] R1[222/300], Temp: 0.1577, Energy: -53.840659+0.000743j
[2025-08-07 17:40:31] [Iter 374/1050] R1[223/300], Temp: 0.1539, Energy: -53.819834-0.003109j
[2025-08-07 17:40:35] [Iter 375/1050] R1[224/300], Temp: 0.1502, Energy: -53.884347+0.003334j
[2025-08-07 17:40:39] [Iter 376/1050] R1[225/300], Temp: 0.1464, Energy: -53.806275+0.000308j
[2025-08-07 17:40:43] [Iter 377/1050] R1[226/300], Temp: 0.1428, Energy: -53.866228-0.002706j
[2025-08-07 17:40:47] [Iter 378/1050] R1[227/300], Temp: 0.1391, Energy: -53.741454-0.000447j
[2025-08-07 17:40:51] [Iter 379/1050] R1[228/300], Temp: 0.1355, Energy: -53.745414+0.000592j
[2025-08-07 17:40:55] [Iter 380/1050] R1[229/300], Temp: 0.1320, Energy: -53.793800-0.004421j
[2025-08-07 17:41:00] [Iter 381/1050] R1[230/300], Temp: 0.1284, Energy: -53.829994-0.002399j
[2025-08-07 17:41:04] [Iter 382/1050] R1[231/300], Temp: 0.1249, Energy: -53.774068-0.004495j
[2025-08-07 17:41:08] [Iter 383/1050] R1[232/300], Temp: 0.1215, Energy: -53.909266+0.001195j
[2025-08-07 17:41:12] [Iter 384/1050] R1[233/300], Temp: 0.1181, Energy: -53.813925+0.001378j
[2025-08-07 17:41:16] [Iter 385/1050] R1[234/300], Temp: 0.1147, Energy: -53.894563+0.000933j
[2025-08-07 17:41:20] [Iter 386/1050] R1[235/300], Temp: 0.1114, Energy: -53.869243+0.001756j
[2025-08-07 17:41:24] [Iter 387/1050] R1[236/300], Temp: 0.1082, Energy: -53.799029-0.002268j
[2025-08-07 17:41:28] [Iter 388/1050] R1[237/300], Temp: 0.1049, Energy: -53.951265+0.004324j
[2025-08-07 17:41:32] [Iter 389/1050] R1[238/300], Temp: 0.1017, Energy: -53.845183+0.001600j
[2025-08-07 17:41:37] [Iter 390/1050] R1[239/300], Temp: 0.0986, Energy: -53.882013-0.002050j
[2025-08-07 17:41:41] [Iter 391/1050] R1[240/300], Temp: 0.0955, Energy: -53.800819+0.004081j
[2025-08-07 17:41:45] [Iter 392/1050] R1[241/300], Temp: 0.0924, Energy: -53.767614-0.003242j
[2025-08-07 17:41:49] [Iter 393/1050] R1[242/300], Temp: 0.0894, Energy: -53.826767-0.000136j
[2025-08-07 17:41:53] [Iter 394/1050] R1[243/300], Temp: 0.0865, Energy: -53.825586-0.004284j
[2025-08-07 17:41:57] [Iter 395/1050] R1[244/300], Temp: 0.0835, Energy: -53.921562-0.001196j
[2025-08-07 17:42:01] [Iter 396/1050] R1[245/300], Temp: 0.0807, Energy: -53.881566-0.001991j
[2025-08-07 17:42:05] [Iter 397/1050] R1[246/300], Temp: 0.0778, Energy: -53.786842-0.000554j
[2025-08-07 17:42:10] [Iter 398/1050] R1[247/300], Temp: 0.0751, Energy: -53.674220-0.000072j
[2025-08-07 17:42:14] [Iter 399/1050] R1[248/300], Temp: 0.0723, Energy: -53.669954+0.007627j
[2025-08-07 17:42:18] [Iter 400/1050] R1[249/300], Temp: 0.0696, Energy: -53.718435+0.001986j
[2025-08-07 17:42:18] ✓ Checkpoint saved: checkpoint_iter_000400.pkl
[2025-08-07 17:42:22] [Iter 401/1050] R1[250/300], Temp: 0.0670, Energy: -53.759650+0.002132j
[2025-08-07 17:42:26] [Iter 402/1050] R1[251/300], Temp: 0.0644, Energy: -53.732879+0.003953j
[2025-08-07 17:42:30] [Iter 403/1050] R1[252/300], Temp: 0.0618, Energy: -53.816058+0.004866j
[2025-08-07 17:42:34] [Iter 404/1050] R1[253/300], Temp: 0.0593, Energy: -53.814288+0.000167j
[2025-08-07 17:42:38] [Iter 405/1050] R1[254/300], Temp: 0.0569, Energy: -53.903198-0.003362j
[2025-08-07 17:42:42] [Iter 406/1050] R1[255/300], Temp: 0.0545, Energy: -53.805535+0.000213j
[2025-08-07 17:42:47] [Iter 407/1050] R1[256/300], Temp: 0.0521, Energy: -53.744802+0.006619j
[2025-08-07 17:42:51] [Iter 408/1050] R1[257/300], Temp: 0.0498, Energy: -53.957339-0.002133j
[2025-08-07 17:42:55] [Iter 409/1050] R1[258/300], Temp: 0.0476, Energy: -53.925829+0.001168j
[2025-08-07 17:42:59] [Iter 410/1050] R1[259/300], Temp: 0.0454, Energy: -53.856524+0.003485j
[2025-08-07 17:43:03] [Iter 411/1050] R1[260/300], Temp: 0.0432, Energy: -53.812396+0.005491j
[2025-08-07 17:43:07] [Iter 412/1050] R1[261/300], Temp: 0.0411, Energy: -53.829934-0.000236j
[2025-08-07 17:43:11] [Iter 413/1050] R1[262/300], Temp: 0.0391, Energy: -53.815265+0.002167j
[2025-08-07 17:43:15] [Iter 414/1050] R1[263/300], Temp: 0.0371, Energy: -53.777107+0.000700j
[2025-08-07 17:43:20] [Iter 415/1050] R1[264/300], Temp: 0.0351, Energy: -53.766474+0.001476j
[2025-08-07 17:43:24] [Iter 416/1050] R1[265/300], Temp: 0.0332, Energy: -53.755195-0.000807j
[2025-08-07 17:43:28] [Iter 417/1050] R1[266/300], Temp: 0.0314, Energy: -53.811529+0.003478j
[2025-08-07 17:43:32] [Iter 418/1050] R1[267/300], Temp: 0.0296, Energy: -53.799272+0.001883j
[2025-08-07 17:43:36] [Iter 419/1050] R1[268/300], Temp: 0.0278, Energy: -53.872175-0.000593j
[2025-08-07 17:43:40] [Iter 420/1050] R1[269/300], Temp: 0.0261, Energy: -53.887667+0.003004j
[2025-08-07 17:43:44] [Iter 421/1050] R1[270/300], Temp: 0.0245, Energy: -53.767724-0.007961j
[2025-08-07 17:43:48] [Iter 422/1050] R1[271/300], Temp: 0.0229, Energy: -53.705173-0.001727j
[2025-08-07 17:43:52] [Iter 423/1050] R1[272/300], Temp: 0.0213, Energy: -53.784950+0.000987j
[2025-08-07 17:43:57] [Iter 424/1050] R1[273/300], Temp: 0.0199, Energy: -53.849478-0.001430j
[2025-08-07 17:44:01] [Iter 425/1050] R1[274/300], Temp: 0.0184, Energy: -53.932643-0.001037j
[2025-08-07 17:44:05] [Iter 426/1050] R1[275/300], Temp: 0.0170, Energy: -53.812103-0.005345j
[2025-08-07 17:44:09] [Iter 427/1050] R1[276/300], Temp: 0.0157, Energy: -53.869589+0.004108j
[2025-08-07 17:44:13] [Iter 428/1050] R1[277/300], Temp: 0.0144, Energy: -53.918617-0.001027j
[2025-08-07 17:44:17] [Iter 429/1050] R1[278/300], Temp: 0.0132, Energy: -53.732697+0.001835j
[2025-08-07 17:44:21] [Iter 430/1050] R1[279/300], Temp: 0.0120, Energy: -53.779122+0.003819j
[2025-08-07 17:44:25] [Iter 431/1050] R1[280/300], Temp: 0.0109, Energy: -53.903658+0.007661j
[2025-08-07 17:44:30] [Iter 432/1050] R1[281/300], Temp: 0.0099, Energy: -53.939147+0.000061j
[2025-08-07 17:44:34] [Iter 433/1050] R1[282/300], Temp: 0.0089, Energy: -53.903330+0.002230j
[2025-08-07 17:44:38] [Iter 434/1050] R1[283/300], Temp: 0.0079, Energy: -53.904715-0.001391j
[2025-08-07 17:44:42] [Iter 435/1050] R1[284/300], Temp: 0.0070, Energy: -53.763552-0.001406j
[2025-08-07 17:44:46] [Iter 436/1050] R1[285/300], Temp: 0.0062, Energy: -53.946068-0.003045j
[2025-08-07 17:44:50] [Iter 437/1050] R1[286/300], Temp: 0.0054, Energy: -53.979639-0.000723j
[2025-08-07 17:44:54] [Iter 438/1050] R1[287/300], Temp: 0.0046, Energy: -53.979112+0.000488j
[2025-08-07 17:44:58] [Iter 439/1050] R1[288/300], Temp: 0.0039, Energy: -53.997214-0.004452j
[2025-08-07 17:45:02] [Iter 440/1050] R1[289/300], Temp: 0.0033, Energy: -54.063424+0.002595j
[2025-08-07 17:45:07] [Iter 441/1050] R1[290/300], Temp: 0.0027, Energy: -54.007782+0.001975j
[2025-08-07 17:45:11] [Iter 442/1050] R1[291/300], Temp: 0.0022, Energy: -53.940327+0.001455j
[2025-08-07 17:45:15] [Iter 443/1050] R1[292/300], Temp: 0.0018, Energy: -53.964544-0.002644j
[2025-08-07 17:45:19] [Iter 444/1050] R1[293/300], Temp: 0.0013, Energy: -53.781930-0.002140j
[2025-08-07 17:45:23] [Iter 445/1050] R1[294/300], Temp: 0.0010, Energy: -53.828614+0.000583j
[2025-08-07 17:45:27] [Iter 446/1050] R1[295/300], Temp: 0.0007, Energy: -53.699347+0.004929j
[2025-08-07 17:45:31] [Iter 447/1050] R1[296/300], Temp: 0.0004, Energy: -53.641725+0.003954j
[2025-08-07 17:45:35] [Iter 448/1050] R1[297/300], Temp: 0.0002, Energy: -53.772077-0.002534j
[2025-08-07 17:45:40] [Iter 449/1050] R1[298/300], Temp: 0.0001, Energy: -53.748797+0.000476j
[2025-08-07 17:45:44] [Iter 450/1050] R1[299/300], Temp: 0.0000, Energy: -53.762817+0.000227j
[2025-08-07 17:45:44] RESTART #2 | Period: 600
[2025-08-07 17:45:48] [Iter 451/1050] R2[0/600], Temp: 1.0000, Energy: -53.899840+0.000751j
[2025-08-07 17:45:52] [Iter 452/1050] R2[1/600], Temp: 1.0000, Energy: -53.923874+0.005919j
[2025-08-07 17:45:56] [Iter 453/1050] R2[2/600], Temp: 1.0000, Energy: -53.820762+0.002521j
[2025-08-07 17:46:00] [Iter 454/1050] R2[3/600], Temp: 0.9999, Energy: -53.836723+0.000570j
[2025-08-07 17:46:04] [Iter 455/1050] R2[4/600], Temp: 0.9999, Energy: -53.784764-0.000499j
[2025-08-07 17:46:08] [Iter 456/1050] R2[5/600], Temp: 0.9998, Energy: -53.780272+0.001177j
[2025-08-07 17:46:12] [Iter 457/1050] R2[6/600], Temp: 0.9998, Energy: -53.812884+0.002806j
[2025-08-07 17:46:17] [Iter 458/1050] R2[7/600], Temp: 0.9997, Energy: -53.869239+0.004180j
[2025-08-07 17:46:21] [Iter 459/1050] R2[8/600], Temp: 0.9996, Energy: -53.801817-0.001425j
[2025-08-07 17:46:25] [Iter 460/1050] R2[9/600], Temp: 0.9994, Energy: -53.811094-0.001945j
[2025-08-07 17:46:29] [Iter 461/1050] R2[10/600], Temp: 0.9993, Energy: -53.731211-0.003235j
[2025-08-07 17:46:33] [Iter 462/1050] R2[11/600], Temp: 0.9992, Energy: -53.765327-0.005871j
[2025-08-07 17:46:37] [Iter 463/1050] R2[12/600], Temp: 0.9990, Energy: -53.685553+0.002171j
[2025-08-07 17:46:41] [Iter 464/1050] R2[13/600], Temp: 0.9988, Energy: -53.667291+0.000942j
[2025-08-07 17:46:45] [Iter 465/1050] R2[14/600], Temp: 0.9987, Energy: -53.788324+0.004986j
[2025-08-07 17:46:50] [Iter 466/1050] R2[15/600], Temp: 0.9985, Energy: -53.750156-0.001574j
[2025-08-07 17:46:54] [Iter 467/1050] R2[16/600], Temp: 0.9982, Energy: -53.949624-0.005016j
[2025-08-07 17:46:58] [Iter 468/1050] R2[17/600], Temp: 0.9980, Energy: -53.924591-0.001043j
[2025-08-07 17:47:02] [Iter 469/1050] R2[18/600], Temp: 0.9978, Energy: -53.861420-0.000115j
[2025-08-07 17:47:06] [Iter 470/1050] R2[19/600], Temp: 0.9975, Energy: -53.850781-0.002870j
[2025-08-07 17:47:10] [Iter 471/1050] R2[20/600], Temp: 0.9973, Energy: -53.857616-0.002733j
[2025-08-07 17:47:14] [Iter 472/1050] R2[21/600], Temp: 0.9970, Energy: -53.905840+0.001390j
[2025-08-07 17:47:18] [Iter 473/1050] R2[22/600], Temp: 0.9967, Energy: -53.801977-0.003613j
[2025-08-07 17:47:22] [Iter 474/1050] R2[23/600], Temp: 0.9964, Energy: -53.839028+0.001961j
[2025-08-07 17:47:27] [Iter 475/1050] R2[24/600], Temp: 0.9961, Energy: -53.799530+0.003801j
[2025-08-07 17:47:31] [Iter 476/1050] R2[25/600], Temp: 0.9957, Energy: -53.843273+0.000844j
[2025-08-07 17:47:35] [Iter 477/1050] R2[26/600], Temp: 0.9954, Energy: -53.857175-0.003384j
[2025-08-07 17:47:39] [Iter 478/1050] R2[27/600], Temp: 0.9950, Energy: -53.814027-0.002159j
[2025-08-07 17:47:43] [Iter 479/1050] R2[28/600], Temp: 0.9946, Energy: -53.815188-0.006952j
[2025-08-07 17:47:47] [Iter 480/1050] R2[29/600], Temp: 0.9942, Energy: -53.886502+0.008280j
[2025-08-07 17:47:51] [Iter 481/1050] R2[30/600], Temp: 0.9938, Energy: -53.765745-0.002835j
[2025-08-07 17:47:55] [Iter 482/1050] R2[31/600], Temp: 0.9934, Energy: -53.875119+0.006069j
[2025-08-07 17:48:00] [Iter 483/1050] R2[32/600], Temp: 0.9930, Energy: -53.881853+0.000113j
[2025-08-07 17:48:04] [Iter 484/1050] R2[33/600], Temp: 0.9926, Energy: -53.856959+0.004264j
[2025-08-07 17:48:08] [Iter 485/1050] R2[34/600], Temp: 0.9921, Energy: -53.839947+0.001819j
[2025-08-07 17:48:12] [Iter 486/1050] R2[35/600], Temp: 0.9916, Energy: -53.858736+0.003365j
[2025-08-07 17:48:16] [Iter 487/1050] R2[36/600], Temp: 0.9911, Energy: -53.876819-0.002040j
[2025-08-07 17:48:20] [Iter 488/1050] R2[37/600], Temp: 0.9906, Energy: -53.794537-0.003270j
[2025-08-07 17:48:24] [Iter 489/1050] R2[38/600], Temp: 0.9901, Energy: -53.728350+0.003739j
[2025-08-07 17:48:28] [Iter 490/1050] R2[39/600], Temp: 0.9896, Energy: -53.795916-0.000388j
[2025-08-07 17:48:32] [Iter 491/1050] R2[40/600], Temp: 0.9891, Energy: -53.808641-0.001366j
[2025-08-07 17:48:37] [Iter 492/1050] R2[41/600], Temp: 0.9885, Energy: -53.739646+0.003460j
[2025-08-07 17:48:41] [Iter 493/1050] R2[42/600], Temp: 0.9880, Energy: -53.708477+0.001711j
[2025-08-07 17:48:45] [Iter 494/1050] R2[43/600], Temp: 0.9874, Energy: -53.690470-0.001242j
[2025-08-07 17:48:49] [Iter 495/1050] R2[44/600], Temp: 0.9868, Energy: -53.829337+0.001389j
[2025-08-07 17:48:53] [Iter 496/1050] R2[45/600], Temp: 0.9862, Energy: -53.774737+0.002665j
[2025-08-07 17:48:57] [Iter 497/1050] R2[46/600], Temp: 0.9856, Energy: -53.839375-0.003922j
[2025-08-07 17:49:01] [Iter 498/1050] R2[47/600], Temp: 0.9849, Energy: -53.881046+0.000450j
[2025-08-07 17:49:05] [Iter 499/1050] R2[48/600], Temp: 0.9843, Energy: -53.953942-0.001889j
[2025-08-07 17:49:10] [Iter 500/1050] R2[49/600], Temp: 0.9836, Energy: -53.875255-0.002674j
[2025-08-07 17:49:10] ✓ Checkpoint saved: checkpoint_iter_000500.pkl
[2025-08-07 17:49:14] [Iter 501/1050] R2[50/600], Temp: 0.9830, Energy: -53.883388+0.004085j
[2025-08-07 17:49:18] [Iter 502/1050] R2[51/600], Temp: 0.9823, Energy: -53.815916-0.002942j
[2025-08-07 17:49:22] [Iter 503/1050] R2[52/600], Temp: 0.9816, Energy: -53.801654-0.000816j
[2025-08-07 17:49:26] [Iter 504/1050] R2[53/600], Temp: 0.9809, Energy: -53.786859-0.001444j
[2025-08-07 17:49:30] [Iter 505/1050] R2[54/600], Temp: 0.9801, Energy: -53.787936+0.005405j
[2025-08-07 17:49:34] [Iter 506/1050] R2[55/600], Temp: 0.9794, Energy: -53.817614-0.006747j
[2025-08-07 17:49:38] [Iter 507/1050] R2[56/600], Temp: 0.9787, Energy: -53.702230-0.002134j
[2025-08-07 17:49:42] [Iter 508/1050] R2[57/600], Temp: 0.9779, Energy: -53.616002+0.003015j
[2025-08-07 17:49:47] [Iter 509/1050] R2[58/600], Temp: 0.9771, Energy: -53.674961+0.000619j
[2025-08-07 17:49:51] [Iter 510/1050] R2[59/600], Temp: 0.9763, Energy: -53.630552+0.001255j
[2025-08-07 17:49:55] [Iter 511/1050] R2[60/600], Temp: 0.9755, Energy: -53.674952+0.007755j
[2025-08-07 17:49:59] [Iter 512/1050] R2[61/600], Temp: 0.9747, Energy: -53.729509+0.005158j
[2025-08-07 17:50:03] [Iter 513/1050] R2[62/600], Temp: 0.9739, Energy: -53.637882+0.001327j
[2025-08-07 17:50:07] [Iter 514/1050] R2[63/600], Temp: 0.9730, Energy: -53.686022-0.002665j
[2025-08-07 17:50:11] [Iter 515/1050] R2[64/600], Temp: 0.9722, Energy: -53.681907-0.000718j
[2025-08-07 17:50:15] [Iter 516/1050] R2[65/600], Temp: 0.9713, Energy: -53.763026+0.000036j
[2025-08-07 17:50:20] [Iter 517/1050] R2[66/600], Temp: 0.9704, Energy: -53.678279+0.000595j
[2025-08-07 17:50:24] [Iter 518/1050] R2[67/600], Temp: 0.9695, Energy: -53.689214-0.002195j
[2025-08-07 17:50:28] [Iter 519/1050] R2[68/600], Temp: 0.9686, Energy: -53.744678-0.001531j
[2025-08-07 17:50:32] [Iter 520/1050] R2[69/600], Temp: 0.9677, Energy: -53.765430+0.002710j
[2025-08-07 17:50:36] [Iter 521/1050] R2[70/600], Temp: 0.9668, Energy: -53.769170+0.002736j
[2025-08-07 17:50:40] [Iter 522/1050] R2[71/600], Temp: 0.9658, Energy: -53.803087+0.001826j
[2025-08-07 17:50:44] [Iter 523/1050] R2[72/600], Temp: 0.9649, Energy: -53.825336-0.005928j
[2025-08-07 17:50:48] [Iter 524/1050] R2[73/600], Temp: 0.9639, Energy: -53.839867-0.003571j
[2025-08-07 17:50:52] [Iter 525/1050] R2[74/600], Temp: 0.9629, Energy: -54.010421-0.003381j
[2025-08-07 17:50:57] [Iter 526/1050] R2[75/600], Temp: 0.9619, Energy: -53.897815-0.004716j
[2025-08-07 17:51:01] [Iter 527/1050] R2[76/600], Temp: 0.9609, Energy: -53.826552-0.002327j
[2025-08-07 17:51:05] [Iter 528/1050] R2[77/600], Temp: 0.9599, Energy: -53.880694+0.001377j
[2025-08-07 17:51:09] [Iter 529/1050] R2[78/600], Temp: 0.9589, Energy: -53.906893+0.005040j
[2025-08-07 17:51:13] [Iter 530/1050] R2[79/600], Temp: 0.9578, Energy: -53.906487+0.002146j
[2025-08-07 17:51:17] [Iter 531/1050] R2[80/600], Temp: 0.9568, Energy: -53.908494-0.000953j
[2025-08-07 17:51:21] [Iter 532/1050] R2[81/600], Temp: 0.9557, Energy: -53.953237-0.000655j
[2025-08-07 17:51:25] [Iter 533/1050] R2[82/600], Temp: 0.9546, Energy: -53.782283+0.005662j
[2025-08-07 17:51:30] [Iter 534/1050] R2[83/600], Temp: 0.9535, Energy: -53.854766+0.004660j
[2025-08-07 17:51:34] [Iter 535/1050] R2[84/600], Temp: 0.9524, Energy: -53.871108+0.001388j
[2025-08-07 17:51:38] [Iter 536/1050] R2[85/600], Temp: 0.9513, Energy: -53.874229+0.001011j
[2025-08-07 17:51:42] [Iter 537/1050] R2[86/600], Temp: 0.9502, Energy: -53.926836+0.000691j
[2025-08-07 17:51:46] [Iter 538/1050] R2[87/600], Temp: 0.9490, Energy: -53.965486+0.004641j
[2025-08-07 17:51:50] [Iter 539/1050] R2[88/600], Temp: 0.9479, Energy: -54.020328+0.000219j
[2025-08-07 17:51:54] [Iter 540/1050] R2[89/600], Temp: 0.9467, Energy: -54.007199-0.005274j
[2025-08-07 17:51:58] [Iter 541/1050] R2[90/600], Temp: 0.9455, Energy: -54.051612-0.001299j
[2025-08-07 17:52:02] [Iter 542/1050] R2[91/600], Temp: 0.9443, Energy: -54.018656+0.003465j
[2025-08-07 17:52:07] [Iter 543/1050] R2[92/600], Temp: 0.9431, Energy: -53.953279+0.004204j
[2025-08-07 17:52:11] [Iter 544/1050] R2[93/600], Temp: 0.9419, Energy: -53.953547-0.000587j
[2025-08-07 17:52:15] [Iter 545/1050] R2[94/600], Temp: 0.9407, Energy: -53.900081-0.008910j
[2025-08-07 17:52:19] [Iter 546/1050] R2[95/600], Temp: 0.9394, Energy: -53.744123-0.002165j
[2025-08-07 17:52:23] [Iter 547/1050] R2[96/600], Temp: 0.9382, Energy: -53.914527-0.004035j
[2025-08-07 17:52:27] [Iter 548/1050] R2[97/600], Temp: 0.9369, Energy: -53.784455+0.003561j
[2025-08-07 17:52:31] [Iter 549/1050] R2[98/600], Temp: 0.9356, Energy: -53.691697-0.001119j
[2025-08-07 17:52:35] [Iter 550/1050] R2[99/600], Temp: 0.9343, Energy: -53.730076+0.002486j
[2025-08-07 17:52:40] [Iter 551/1050] R2[100/600], Temp: 0.9330, Energy: -53.778443+0.002999j
[2025-08-07 17:52:44] [Iter 552/1050] R2[101/600], Temp: 0.9317, Energy: -53.724297-0.000114j
[2025-08-07 17:52:48] [Iter 553/1050] R2[102/600], Temp: 0.9304, Energy: -53.701073-0.001964j
[2025-08-07 17:52:52] [Iter 554/1050] R2[103/600], Temp: 0.9290, Energy: -53.811925-0.000735j
[2025-08-07 17:52:56] [Iter 555/1050] R2[104/600], Temp: 0.9277, Energy: -53.811362+0.000269j
[2025-08-07 17:53:00] [Iter 556/1050] R2[105/600], Temp: 0.9263, Energy: -53.675178-0.003020j
[2025-08-07 17:53:04] [Iter 557/1050] R2[106/600], Temp: 0.9249, Energy: -53.781090-0.002838j
[2025-08-07 17:53:08] [Iter 558/1050] R2[107/600], Temp: 0.9236, Energy: -53.786893+0.002467j
[2025-08-07 17:53:13] [Iter 559/1050] R2[108/600], Temp: 0.9222, Energy: -53.882991+0.003115j
[2025-08-07 17:53:17] [Iter 560/1050] R2[109/600], Temp: 0.9208, Energy: -53.735204+0.002857j
[2025-08-07 17:53:21] [Iter 561/1050] R2[110/600], Temp: 0.9193, Energy: -53.787288+0.000575j
[2025-08-07 17:53:25] [Iter 562/1050] R2[111/600], Temp: 0.9179, Energy: -53.916667+0.004243j
[2025-08-07 17:53:29] [Iter 563/1050] R2[112/600], Temp: 0.9165, Energy: -53.887538+0.000590j
[2025-08-07 17:53:33] [Iter 564/1050] R2[113/600], Temp: 0.9150, Energy: -53.786497+0.006489j
[2025-08-07 17:53:37] [Iter 565/1050] R2[114/600], Temp: 0.9135, Energy: -53.800708+0.004621j
[2025-08-07 17:53:41] [Iter 566/1050] R2[115/600], Temp: 0.9121, Energy: -53.806904+0.002157j
[2025-08-07 17:53:45] [Iter 567/1050] R2[116/600], Temp: 0.9106, Energy: -53.885530-0.001688j
[2025-08-07 17:53:50] [Iter 568/1050] R2[117/600], Temp: 0.9091, Energy: -53.837718+0.001841j
[2025-08-07 17:53:54] [Iter 569/1050] R2[118/600], Temp: 0.9076, Energy: -53.807831-0.007337j
[2025-08-07 17:53:58] [Iter 570/1050] R2[119/600], Temp: 0.9060, Energy: -53.800521+0.004532j
[2025-08-07 17:54:02] [Iter 571/1050] R2[120/600], Temp: 0.9045, Energy: -53.851677+0.001415j
[2025-08-07 17:54:06] [Iter 572/1050] R2[121/600], Temp: 0.9030, Energy: -53.873785+0.001930j
[2025-08-07 17:54:10] [Iter 573/1050] R2[122/600], Temp: 0.9014, Energy: -53.935161+0.000796j
[2025-08-07 17:54:14] [Iter 574/1050] R2[123/600], Temp: 0.8998, Energy: -53.814929-0.000476j
[2025-08-07 17:54:18] [Iter 575/1050] R2[124/600], Temp: 0.8983, Energy: -53.900116-0.000198j
[2025-08-07 17:54:22] [Iter 576/1050] R2[125/600], Temp: 0.8967, Energy: -53.890330+0.002898j
[2025-08-07 17:54:27] [Iter 577/1050] R2[126/600], Temp: 0.8951, Energy: -53.790334-0.004090j
[2025-08-07 17:54:31] [Iter 578/1050] R2[127/600], Temp: 0.8935, Energy: -53.779432+0.000876j
[2025-08-07 17:54:35] [Iter 579/1050] R2[128/600], Temp: 0.8918, Energy: -53.843552-0.000480j
[2025-08-07 17:54:39] [Iter 580/1050] R2[129/600], Temp: 0.8902, Energy: -53.871191-0.001996j
[2025-08-07 17:54:43] [Iter 581/1050] R2[130/600], Temp: 0.8886, Energy: -53.797969-0.006711j
[2025-08-07 17:54:47] [Iter 582/1050] R2[131/600], Temp: 0.8869, Energy: -53.786503-0.004669j
[2025-08-07 17:54:52] [Iter 583/1050] R2[132/600], Temp: 0.8853, Energy: -53.866574-0.004439j
[2025-08-07 17:54:56] [Iter 584/1050] R2[133/600], Temp: 0.8836, Energy: -53.773416-0.001757j
[2025-08-07 17:55:00] [Iter 585/1050] R2[134/600], Temp: 0.8819, Energy: -53.837335+0.005415j
[2025-08-07 17:55:04] [Iter 586/1050] R2[135/600], Temp: 0.8802, Energy: -53.699399-0.001770j
[2025-08-07 17:55:08] [Iter 587/1050] R2[136/600], Temp: 0.8785, Energy: -53.867921-0.005008j
[2025-08-07 17:55:12] [Iter 588/1050] R2[137/600], Temp: 0.8768, Energy: -53.779752-0.009236j
[2025-08-07 17:55:16] [Iter 589/1050] R2[138/600], Temp: 0.8751, Energy: -53.781453-0.004455j
[2025-08-07 17:55:20] [Iter 590/1050] R2[139/600], Temp: 0.8733, Energy: -53.702699-0.005154j
[2025-08-07 17:55:25] [Iter 591/1050] R2[140/600], Temp: 0.8716, Energy: -53.793985+0.000630j
[2025-08-07 17:55:29] [Iter 592/1050] R2[141/600], Temp: 0.8698, Energy: -53.788373+0.004958j
[2025-08-07 17:55:33] [Iter 593/1050] R2[142/600], Temp: 0.8680, Energy: -53.818594-0.000088j
[2025-08-07 17:55:37] [Iter 594/1050] R2[143/600], Temp: 0.8663, Energy: -53.716133-0.007715j
[2025-08-07 17:55:41] [Iter 595/1050] R2[144/600], Temp: 0.8645, Energy: -53.866947+0.002568j
[2025-08-07 17:55:45] [Iter 596/1050] R2[145/600], Temp: 0.8627, Energy: -53.856875+0.003398j
[2025-08-07 17:55:49] [Iter 597/1050] R2[146/600], Temp: 0.8609, Energy: -53.789321+0.002614j
[2025-08-07 17:55:53] [Iter 598/1050] R2[147/600], Temp: 0.8591, Energy: -53.809900-0.002516j
[2025-08-07 17:55:57] [Iter 599/1050] R2[148/600], Temp: 0.8572, Energy: -53.793609-0.000467j
[2025-08-07 17:56:02] [Iter 600/1050] R2[149/600], Temp: 0.8554, Energy: -53.855331+0.002415j
[2025-08-07 17:56:02] ✓ Checkpoint saved: checkpoint_iter_000600.pkl
[2025-08-07 17:56:06] [Iter 601/1050] R2[150/600], Temp: 0.8536, Energy: -53.848603+0.005630j
[2025-08-07 17:56:10] [Iter 602/1050] R2[151/600], Temp: 0.8517, Energy: -53.913973-0.001543j
[2025-08-07 17:56:14] [Iter 603/1050] R2[152/600], Temp: 0.8498, Energy: -53.951462-0.002603j
[2025-08-07 17:56:18] [Iter 604/1050] R2[153/600], Temp: 0.8480, Energy: -53.842715-0.002234j
[2025-08-07 17:56:22] [Iter 605/1050] R2[154/600], Temp: 0.8461, Energy: -53.914667+0.004075j
[2025-08-07 17:56:26] [Iter 606/1050] R2[155/600], Temp: 0.8442, Energy: -53.852019+0.002983j
[2025-08-07 17:56:30] [Iter 607/1050] R2[156/600], Temp: 0.8423, Energy: -53.962753-0.002114j
[2025-08-07 17:56:34] [Iter 608/1050] R2[157/600], Temp: 0.8404, Energy: -54.001310+0.000789j
[2025-08-07 17:56:39] [Iter 609/1050] R2[158/600], Temp: 0.8384, Energy: -53.966399-0.001946j
[2025-08-07 17:56:43] [Iter 610/1050] R2[159/600], Temp: 0.8365, Energy: -53.947214-0.000300j
[2025-08-07 17:56:47] [Iter 611/1050] R2[160/600], Temp: 0.8346, Energy: -53.838651-0.000749j
[2025-08-07 17:56:51] [Iter 612/1050] R2[161/600], Temp: 0.8326, Energy: -53.842480-0.001235j
[2025-08-07 17:56:55] [Iter 613/1050] R2[162/600], Temp: 0.8307, Energy: -53.807534+0.006235j
[2025-08-07 17:56:59] [Iter 614/1050] R2[163/600], Temp: 0.8287, Energy: -53.713321-0.000824j
[2025-08-07 17:57:03] [Iter 615/1050] R2[164/600], Temp: 0.8267, Energy: -53.803103-0.001448j
[2025-08-07 17:57:07] [Iter 616/1050] R2[165/600], Temp: 0.8247, Energy: -53.855065+0.000897j
[2025-08-07 17:57:12] [Iter 617/1050] R2[166/600], Temp: 0.8227, Energy: -53.847162+0.004140j
[2025-08-07 17:57:16] [Iter 618/1050] R2[167/600], Temp: 0.8207, Energy: -53.898389-0.006059j
[2025-08-07 17:57:20] [Iter 619/1050] R2[168/600], Temp: 0.8187, Energy: -53.890803-0.007117j
[2025-08-07 17:57:24] [Iter 620/1050] R2[169/600], Temp: 0.8167, Energy: -53.979706-0.002034j
[2025-08-07 17:57:28] [Iter 621/1050] R2[170/600], Temp: 0.8147, Energy: -53.992721-0.005655j
[2025-08-07 17:57:32] [Iter 622/1050] R2[171/600], Temp: 0.8126, Energy: -53.841258+0.000577j
[2025-08-07 17:57:36] [Iter 623/1050] R2[172/600], Temp: 0.8106, Energy: -53.813429+0.001157j
[2025-08-07 17:57:40] [Iter 624/1050] R2[173/600], Temp: 0.8085, Energy: -53.814173-0.000239j
[2025-08-07 17:57:45] [Iter 625/1050] R2[174/600], Temp: 0.8065, Energy: -53.823413+0.000255j
[2025-08-07 17:57:49] [Iter 626/1050] R2[175/600], Temp: 0.8044, Energy: -53.879523-0.003620j
[2025-08-07 17:57:53] [Iter 627/1050] R2[176/600], Temp: 0.8023, Energy: -53.868345+0.000634j
[2025-08-07 17:57:57] [Iter 628/1050] R2[177/600], Temp: 0.8002, Energy: -53.870246-0.003637j
[2025-08-07 17:58:01] [Iter 629/1050] R2[178/600], Temp: 0.7981, Energy: -53.832355-0.000405j
[2025-08-07 17:58:05] [Iter 630/1050] R2[179/600], Temp: 0.7960, Energy: -53.813290-0.002003j
[2025-08-07 17:58:09] [Iter 631/1050] R2[180/600], Temp: 0.7939, Energy: -53.875325-0.001073j
[2025-08-07 17:58:13] [Iter 632/1050] R2[181/600], Temp: 0.7918, Energy: -53.736791+0.001196j
[2025-08-07 17:58:17] [Iter 633/1050] R2[182/600], Temp: 0.7896, Energy: -53.730082-0.001253j
[2025-08-07 17:58:22] [Iter 634/1050] R2[183/600], Temp: 0.7875, Energy: -53.725648-0.002379j
[2025-08-07 17:58:26] [Iter 635/1050] R2[184/600], Temp: 0.7854, Energy: -53.708744-0.002390j
[2025-08-07 17:58:30] [Iter 636/1050] R2[185/600], Temp: 0.7832, Energy: -53.764458-0.002115j
[2025-08-07 17:58:34] [Iter 637/1050] R2[186/600], Temp: 0.7810, Energy: -53.703665+0.005986j
[2025-08-07 17:58:38] [Iter 638/1050] R2[187/600], Temp: 0.7789, Energy: -53.725764+0.001672j
[2025-08-07 17:58:42] [Iter 639/1050] R2[188/600], Temp: 0.7767, Energy: -53.773810+0.003168j
[2025-08-07 17:58:46] [Iter 640/1050] R2[189/600], Temp: 0.7745, Energy: -53.771514-0.002865j
[2025-08-07 17:58:50] [Iter 641/1050] R2[190/600], Temp: 0.7723, Energy: -53.761419+0.003779j
[2025-08-07 17:58:55] [Iter 642/1050] R2[191/600], Temp: 0.7701, Energy: -53.837511-0.004306j
[2025-08-07 17:58:59] [Iter 643/1050] R2[192/600], Temp: 0.7679, Energy: -53.836287-0.005551j
[2025-08-07 17:59:03] [Iter 644/1050] R2[193/600], Temp: 0.7657, Energy: -53.892543-0.003501j
[2025-08-07 17:59:07] [Iter 645/1050] R2[194/600], Temp: 0.7635, Energy: -53.936989+0.000008j
[2025-08-07 17:59:11] [Iter 646/1050] R2[195/600], Temp: 0.7612, Energy: -53.918884+0.000849j
[2025-08-07 17:59:15] [Iter 647/1050] R2[196/600], Temp: 0.7590, Energy: -53.787835+0.000531j
[2025-08-07 17:59:19] [Iter 648/1050] R2[197/600], Temp: 0.7568, Energy: -53.966788-0.001350j
[2025-08-07 17:59:23] [Iter 649/1050] R2[198/600], Temp: 0.7545, Energy: -53.863652-0.002842j
[2025-08-07 17:59:27] [Iter 650/1050] R2[199/600], Temp: 0.7523, Energy: -53.766711+0.000561j
[2025-08-07 17:59:32] [Iter 651/1050] R2[200/600], Temp: 0.7500, Energy: -53.827508+0.002613j
[2025-08-07 17:59:36] [Iter 652/1050] R2[201/600], Temp: 0.7477, Energy: -53.781795-0.005541j
[2025-08-07 17:59:40] [Iter 653/1050] R2[202/600], Temp: 0.7455, Energy: -53.811740-0.001055j
[2025-08-07 17:59:44] [Iter 654/1050] R2[203/600], Temp: 0.7432, Energy: -53.844097+0.001202j
[2025-08-07 17:59:48] [Iter 655/1050] R2[204/600], Temp: 0.7409, Energy: -53.903252+0.007268j
[2025-08-07 17:59:52] [Iter 656/1050] R2[205/600], Temp: 0.7386, Energy: -53.899948-0.000867j
[2025-08-07 17:59:56] [Iter 657/1050] R2[206/600], Temp: 0.7363, Energy: -53.988425+0.003011j
[2025-08-07 18:00:00] [Iter 658/1050] R2[207/600], Temp: 0.7340, Energy: -53.940720+0.002504j
[2025-08-07 18:00:04] [Iter 659/1050] R2[208/600], Temp: 0.7316, Energy: -53.844610+0.002024j
[2025-08-07 18:00:09] [Iter 660/1050] R2[209/600], Temp: 0.7293, Energy: -53.925580+0.004353j
[2025-08-07 18:00:13] [Iter 661/1050] R2[210/600], Temp: 0.7270, Energy: -53.889733+0.008455j
[2025-08-07 18:00:17] [Iter 662/1050] R2[211/600], Temp: 0.7247, Energy: -53.817574+0.002208j
[2025-08-07 18:00:21] [Iter 663/1050] R2[212/600], Temp: 0.7223, Energy: -53.929810+0.000463j
[2025-08-07 18:00:25] [Iter 664/1050] R2[213/600], Temp: 0.7200, Energy: -53.810457-0.006955j
[2025-08-07 18:00:29] [Iter 665/1050] R2[214/600], Temp: 0.7176, Energy: -53.810803-0.001501j
[2025-08-07 18:00:33] [Iter 666/1050] R2[215/600], Temp: 0.7153, Energy: -53.781232-0.000117j
[2025-08-07 18:00:37] [Iter 667/1050] R2[216/600], Temp: 0.7129, Energy: -53.815894+0.000585j
[2025-08-07 18:00:42] [Iter 668/1050] R2[217/600], Temp: 0.7105, Energy: -53.772841-0.002131j
[2025-08-07 18:00:46] [Iter 669/1050] R2[218/600], Temp: 0.7081, Energy: -53.844616+0.002129j
[2025-08-07 18:00:50] [Iter 670/1050] R2[219/600], Temp: 0.7058, Energy: -53.861694-0.001821j
[2025-08-07 18:00:54] [Iter 671/1050] R2[220/600], Temp: 0.7034, Energy: -53.819818+0.000564j
[2025-08-07 18:00:58] [Iter 672/1050] R2[221/600], Temp: 0.7010, Energy: -54.010380-0.001512j
[2025-08-07 18:01:02] [Iter 673/1050] R2[222/600], Temp: 0.6986, Energy: -53.993371+0.002692j
[2025-08-07 18:01:06] [Iter 674/1050] R2[223/600], Temp: 0.6962, Energy: -53.901304-0.000248j
[2025-08-07 18:01:10] [Iter 675/1050] R2[224/600], Temp: 0.6938, Energy: -53.805132-0.002664j
[2025-08-07 18:01:15] [Iter 676/1050] R2[225/600], Temp: 0.6913, Energy: -53.905958-0.001148j
[2025-08-07 18:01:19] [Iter 677/1050] R2[226/600], Temp: 0.6889, Energy: -53.808184-0.001335j
[2025-08-07 18:01:23] [Iter 678/1050] R2[227/600], Temp: 0.6865, Energy: -53.857079+0.003243j
[2025-08-07 18:01:27] [Iter 679/1050] R2[228/600], Temp: 0.6841, Energy: -53.787666-0.001742j
[2025-08-07 18:01:31] [Iter 680/1050] R2[229/600], Temp: 0.6816, Energy: -53.812991-0.003873j
[2025-08-07 18:01:35] [Iter 681/1050] R2[230/600], Temp: 0.6792, Energy: -53.909469-0.004969j
[2025-08-07 18:01:39] [Iter 682/1050] R2[231/600], Temp: 0.6767, Energy: -53.985013-0.000625j
[2025-08-07 18:01:43] [Iter 683/1050] R2[232/600], Temp: 0.6743, Energy: -53.872599+0.003237j
[2025-08-07 18:01:48] [Iter 684/1050] R2[233/600], Temp: 0.6718, Energy: -53.792838-0.002038j
[2025-08-07 18:01:52] [Iter 685/1050] R2[234/600], Temp: 0.6694, Energy: -53.868681+0.001548j
[2025-08-07 18:01:56] [Iter 686/1050] R2[235/600], Temp: 0.6669, Energy: -53.874335+0.001726j
[2025-08-07 18:02:00] [Iter 687/1050] R2[236/600], Temp: 0.6644, Energy: -53.761267+0.007312j
[2025-08-07 18:02:04] [Iter 688/1050] R2[237/600], Temp: 0.6620, Energy: -53.767836+0.003660j
[2025-08-07 18:02:08] [Iter 689/1050] R2[238/600], Temp: 0.6595, Energy: -53.738730+0.004754j
[2025-08-07 18:02:12] [Iter 690/1050] R2[239/600], Temp: 0.6570, Energy: -53.848300+0.000423j
[2025-08-07 18:02:16] [Iter 691/1050] R2[240/600], Temp: 0.6545, Energy: -53.863276+0.001120j
[2025-08-07 18:02:21] [Iter 692/1050] R2[241/600], Temp: 0.6520, Energy: -53.783922+0.001749j
[2025-08-07 18:02:25] [Iter 693/1050] R2[242/600], Temp: 0.6495, Energy: -53.847762+0.006804j
[2025-08-07 18:02:29] [Iter 694/1050] R2[243/600], Temp: 0.6470, Energy: -53.859668-0.001308j
[2025-08-07 18:02:33] [Iter 695/1050] R2[244/600], Temp: 0.6445, Energy: -53.723583-0.001394j
[2025-08-07 18:02:37] [Iter 696/1050] R2[245/600], Temp: 0.6420, Energy: -53.816802+0.002709j
[2025-08-07 18:02:41] [Iter 697/1050] R2[246/600], Temp: 0.6395, Energy: -53.820246+0.006614j
[2025-08-07 18:02:45] [Iter 698/1050] R2[247/600], Temp: 0.6370, Energy: -53.700883-0.001124j
[2025-08-07 18:02:49] [Iter 699/1050] R2[248/600], Temp: 0.6345, Energy: -53.730963-0.002450j
[2025-08-07 18:02:54] [Iter 700/1050] R2[249/600], Temp: 0.6319, Energy: -53.746057-0.000810j
[2025-08-07 18:02:54] ✓ Checkpoint saved: checkpoint_iter_000700.pkl
[2025-08-07 18:02:58] [Iter 701/1050] R2[250/600], Temp: 0.6294, Energy: -53.785013+0.004491j
[2025-08-07 18:03:02] [Iter 702/1050] R2[251/600], Temp: 0.6269, Energy: -53.782314-0.001295j
[2025-08-07 18:03:06] [Iter 703/1050] R2[252/600], Temp: 0.6243, Energy: -53.856575-0.002402j
[2025-08-07 18:03:10] [Iter 704/1050] R2[253/600], Temp: 0.6218, Energy: -53.870854-0.003577j
[2025-08-07 18:03:14] [Iter 705/1050] R2[254/600], Temp: 0.6193, Energy: -53.893748+0.001919j
[2025-08-07 18:03:18] [Iter 706/1050] R2[255/600], Temp: 0.6167, Energy: -53.964961-0.000038j
[2025-08-07 18:03:22] [Iter 707/1050] R2[256/600], Temp: 0.6142, Energy: -53.937523-0.001378j
[2025-08-07 18:03:26] [Iter 708/1050] R2[257/600], Temp: 0.6116, Energy: -53.950730-0.003950j
[2025-08-07 18:03:31] [Iter 709/1050] R2[258/600], Temp: 0.6091, Energy: -54.001322-0.001457j
[2025-08-07 18:03:35] [Iter 710/1050] R2[259/600], Temp: 0.6065, Energy: -53.829153-0.002797j
[2025-08-07 18:03:39] [Iter 711/1050] R2[260/600], Temp: 0.6040, Energy: -53.808796+0.002537j
[2025-08-07 18:03:43] [Iter 712/1050] R2[261/600], Temp: 0.6014, Energy: -53.924367-0.003433j
[2025-08-07 18:03:47] [Iter 713/1050] R2[262/600], Temp: 0.5988, Energy: -53.848384+0.001999j
[2025-08-07 18:03:51] [Iter 714/1050] R2[263/600], Temp: 0.5963, Energy: -53.850400+0.001374j
[2025-08-07 18:03:55] [Iter 715/1050] R2[264/600], Temp: 0.5937, Energy: -53.854577-0.005567j
[2025-08-07 18:03:59] [Iter 716/1050] R2[265/600], Temp: 0.5911, Energy: -53.731185-0.005456j
[2025-08-07 18:04:04] [Iter 717/1050] R2[266/600], Temp: 0.5885, Energy: -53.717089-0.008019j
[2025-08-07 18:04:08] [Iter 718/1050] R2[267/600], Temp: 0.5860, Energy: -53.715369+0.000015j
[2025-08-07 18:04:12] [Iter 719/1050] R2[268/600], Temp: 0.5834, Energy: -53.830975+0.002933j
[2025-08-07 18:04:16] [Iter 720/1050] R2[269/600], Temp: 0.5808, Energy: -53.777843-0.002979j
[2025-08-07 18:04:20] [Iter 721/1050] R2[270/600], Temp: 0.5782, Energy: -53.848593+0.000421j
[2025-08-07 18:04:24] [Iter 722/1050] R2[271/600], Temp: 0.5756, Energy: -53.774206-0.003117j
[2025-08-07 18:04:28] [Iter 723/1050] R2[272/600], Temp: 0.5730, Energy: -53.691894-0.001628j
[2025-08-07 18:04:32] [Iter 724/1050] R2[273/600], Temp: 0.5705, Energy: -53.837910-0.000911j
[2025-08-07 18:04:36] [Iter 725/1050] R2[274/600], Temp: 0.5679, Energy: -53.852340-0.000572j
[2025-08-07 18:04:41] [Iter 726/1050] R2[275/600], Temp: 0.5653, Energy: -53.846040-0.003873j
[2025-08-07 18:04:45] [Iter 727/1050] R2[276/600], Temp: 0.5627, Energy: -53.868299+0.000091j
[2025-08-07 18:04:49] [Iter 728/1050] R2[277/600], Temp: 0.5601, Energy: -53.970361+0.000155j
[2025-08-07 18:04:53] [Iter 729/1050] R2[278/600], Temp: 0.5575, Energy: -53.967018+0.005095j
[2025-08-07 18:04:57] [Iter 730/1050] R2[279/600], Temp: 0.5549, Energy: -53.857847-0.001756j
[2025-08-07 18:05:01] [Iter 731/1050] R2[280/600], Temp: 0.5523, Energy: -53.885289+0.001541j
[2025-08-07 18:05:05] [Iter 732/1050] R2[281/600], Temp: 0.5497, Energy: -53.826974+0.001200j
[2025-08-07 18:05:10] [Iter 733/1050] R2[282/600], Temp: 0.5471, Energy: -53.899430+0.003183j
[2025-08-07 18:05:14] [Iter 734/1050] R2[283/600], Temp: 0.5444, Energy: -53.923150-0.005029j
[2025-08-07 18:05:18] [Iter 735/1050] R2[284/600], Temp: 0.5418, Energy: -53.925389-0.001541j
[2025-08-07 18:05:22] [Iter 736/1050] R2[285/600], Temp: 0.5392, Energy: -53.822151-0.002029j
[2025-08-07 18:05:26] [Iter 737/1050] R2[286/600], Temp: 0.5366, Energy: -53.748569-0.004641j
[2025-08-07 18:05:30] [Iter 738/1050] R2[287/600], Temp: 0.5340, Energy: -53.909685-0.002514j
[2025-08-07 18:05:34] [Iter 739/1050] R2[288/600], Temp: 0.5314, Energy: -53.904476-0.002350j
[2025-08-07 18:05:38] [Iter 740/1050] R2[289/600], Temp: 0.5288, Energy: -53.902358-0.002366j
[2025-08-07 18:05:43] [Iter 741/1050] R2[290/600], Temp: 0.5262, Energy: -53.942793-0.000531j
[2025-08-07 18:05:47] [Iter 742/1050] R2[291/600], Temp: 0.5236, Energy: -53.809371+0.000111j
[2025-08-07 18:05:51] [Iter 743/1050] R2[292/600], Temp: 0.5209, Energy: -53.770744-0.002620j
[2025-08-07 18:05:55] [Iter 744/1050] R2[293/600], Temp: 0.5183, Energy: -53.831257-0.004121j
[2025-08-07 18:05:59] [Iter 745/1050] R2[294/600], Temp: 0.5157, Energy: -53.810456+0.001203j
[2025-08-07 18:06:03] [Iter 746/1050] R2[295/600], Temp: 0.5131, Energy: -53.735874+0.002347j
[2025-08-07 18:06:07] [Iter 747/1050] R2[296/600], Temp: 0.5105, Energy: -53.796080+0.001510j
[2025-08-07 18:06:11] [Iter 748/1050] R2[297/600], Temp: 0.5079, Energy: -53.752921+0.000090j
[2025-08-07 18:06:15] [Iter 749/1050] R2[298/600], Temp: 0.5052, Energy: -53.880363+0.006545j
[2025-08-07 18:06:20] [Iter 750/1050] R2[299/600], Temp: 0.5026, Energy: -53.824064-0.000954j
[2025-08-07 18:06:24] [Iter 751/1050] R2[300/600], Temp: 0.5000, Energy: -53.883661+0.003184j
[2025-08-07 18:06:28] [Iter 752/1050] R2[301/600], Temp: 0.4974, Energy: -53.926422-0.002824j
[2025-08-07 18:06:32] [Iter 753/1050] R2[302/600], Temp: 0.4948, Energy: -53.889483+0.000697j
[2025-08-07 18:06:36] [Iter 754/1050] R2[303/600], Temp: 0.4921, Energy: -53.992483-0.002166j
[2025-08-07 18:06:40] [Iter 755/1050] R2[304/600], Temp: 0.4895, Energy: -53.980001-0.003183j
[2025-08-07 18:06:44] [Iter 756/1050] R2[305/600], Temp: 0.4869, Energy: -54.064876-0.000210j
[2025-08-07 18:06:48] [Iter 757/1050] R2[306/600], Temp: 0.4843, Energy: -53.964141+0.000586j
[2025-08-07 18:06:52] [Iter 758/1050] R2[307/600], Temp: 0.4817, Energy: -53.972024+0.000039j
[2025-08-07 18:06:57] [Iter 759/1050] R2[308/600], Temp: 0.4791, Energy: -54.060661+0.001803j
[2025-08-07 18:07:01] [Iter 760/1050] R2[309/600], Temp: 0.4764, Energy: -53.982706-0.004950j
[2025-08-07 18:07:05] [Iter 761/1050] R2[310/600], Temp: 0.4738, Energy: -53.821218-0.000765j
[2025-08-07 18:07:09] [Iter 762/1050] R2[311/600], Temp: 0.4712, Energy: -53.805124+0.001847j
[2025-08-07 18:07:13] [Iter 763/1050] R2[312/600], Temp: 0.4686, Energy: -53.875195-0.001097j
[2025-08-07 18:07:17] [Iter 764/1050] R2[313/600], Temp: 0.4660, Energy: -53.918994+0.001634j
[2025-08-07 18:07:21] [Iter 765/1050] R2[314/600], Temp: 0.4634, Energy: -53.921511-0.000959j
[2025-08-07 18:07:25] [Iter 766/1050] R2[315/600], Temp: 0.4608, Energy: -53.964074+0.000611j
[2025-08-07 18:07:30] [Iter 767/1050] R2[316/600], Temp: 0.4582, Energy: -53.878684+0.001414j
[2025-08-07 18:07:34] [Iter 768/1050] R2[317/600], Temp: 0.4556, Energy: -53.947583+0.002251j
[2025-08-07 18:07:38] [Iter 769/1050] R2[318/600], Temp: 0.4529, Energy: -54.007419-0.000225j
[2025-08-07 18:07:42] [Iter 770/1050] R2[319/600], Temp: 0.4503, Energy: -53.982773-0.004337j
[2025-08-07 18:07:46] [Iter 771/1050] R2[320/600], Temp: 0.4477, Energy: -53.891952+0.008098j
[2025-08-07 18:07:50] [Iter 772/1050] R2[321/600], Temp: 0.4451, Energy: -53.960078+0.004425j
[2025-08-07 18:07:54] [Iter 773/1050] R2[322/600], Temp: 0.4425, Energy: -53.925200-0.001356j
[2025-08-07 18:07:58] [Iter 774/1050] R2[323/600], Temp: 0.4399, Energy: -53.890143-0.000279j
[2025-08-07 18:08:02] [Iter 775/1050] R2[324/600], Temp: 0.4373, Energy: -53.884685-0.000625j
[2025-08-07 18:08:07] [Iter 776/1050] R2[325/600], Temp: 0.4347, Energy: -53.777157+0.000978j
[2025-08-07 18:08:11] [Iter 777/1050] R2[326/600], Temp: 0.4321, Energy: -53.833846+0.007334j
[2025-08-07 18:08:15] [Iter 778/1050] R2[327/600], Temp: 0.4295, Energy: -53.827462-0.002871j
[2025-08-07 18:08:19] [Iter 779/1050] R2[328/600], Temp: 0.4270, Energy: -53.805404-0.004512j
[2025-08-07 18:08:23] [Iter 780/1050] R2[329/600], Temp: 0.4244, Energy: -53.776212+0.000386j
[2025-08-07 18:08:27] [Iter 781/1050] R2[330/600], Temp: 0.4218, Energy: -53.827424+0.002212j
[2025-08-07 18:08:31] [Iter 782/1050] R2[331/600], Temp: 0.4192, Energy: -53.792549+0.003494j
[2025-08-07 18:08:35] [Iter 783/1050] R2[332/600], Temp: 0.4166, Energy: -53.841251+0.000001j
[2025-08-07 18:08:40] [Iter 784/1050] R2[333/600], Temp: 0.4140, Energy: -53.781583-0.004206j
[2025-08-07 18:08:44] [Iter 785/1050] R2[334/600], Temp: 0.4115, Energy: -53.815606+0.002290j
[2025-08-07 18:08:48] [Iter 786/1050] R2[335/600], Temp: 0.4089, Energy: -53.873036-0.003389j
[2025-08-07 18:08:52] [Iter 787/1050] R2[336/600], Temp: 0.4063, Energy: -53.873711+0.002396j
[2025-08-07 18:08:56] [Iter 788/1050] R2[337/600], Temp: 0.4037, Energy: -53.747111-0.002140j
[2025-08-07 18:09:00] [Iter 789/1050] R2[338/600], Temp: 0.4012, Energy: -53.846301-0.001236j
[2025-08-07 18:09:04] [Iter 790/1050] R2[339/600], Temp: 0.3986, Energy: -53.916969+0.002915j
[2025-08-07 18:09:08] [Iter 791/1050] R2[340/600], Temp: 0.3960, Energy: -53.964656+0.003713j
[2025-08-07 18:09:12] [Iter 792/1050] R2[341/600], Temp: 0.3935, Energy: -53.871314-0.002980j
[2025-08-07 18:09:17] [Iter 793/1050] R2[342/600], Temp: 0.3909, Energy: -53.826246-0.002053j
[2025-08-07 18:09:21] [Iter 794/1050] R2[343/600], Temp: 0.3884, Energy: -53.799904-0.002948j
[2025-08-07 18:09:25] [Iter 795/1050] R2[344/600], Temp: 0.3858, Energy: -53.828921-0.000997j
[2025-08-07 18:09:29] [Iter 796/1050] R2[345/600], Temp: 0.3833, Energy: -53.845282+0.007714j
[2025-08-07 18:09:33] [Iter 797/1050] R2[346/600], Temp: 0.3807, Energy: -53.818215+0.002798j
[2025-08-07 18:09:37] [Iter 798/1050] R2[347/600], Temp: 0.3782, Energy: -53.887221-0.000413j
[2025-08-07 18:09:41] [Iter 799/1050] R2[348/600], Temp: 0.3757, Energy: -53.881836-0.002333j
[2025-08-07 18:09:45] [Iter 800/1050] R2[349/600], Temp: 0.3731, Energy: -53.895686-0.001077j
[2025-08-07 18:09:45] ✓ Checkpoint saved: checkpoint_iter_000800.pkl
[2025-08-07 18:09:50] [Iter 801/1050] R2[350/600], Temp: 0.3706, Energy: -53.803949+0.003533j
[2025-08-07 18:09:54] [Iter 802/1050] R2[351/600], Temp: 0.3681, Energy: -53.824634-0.000321j
[2025-08-07 18:09:58] [Iter 803/1050] R2[352/600], Temp: 0.3655, Energy: -53.753737-0.003078j
[2025-08-07 18:10:02] [Iter 804/1050] R2[353/600], Temp: 0.3630, Energy: -53.784585-0.004006j
[2025-08-07 18:10:06] [Iter 805/1050] R2[354/600], Temp: 0.3605, Energy: -53.823164-0.002602j
[2025-08-07 18:10:10] [Iter 806/1050] R2[355/600], Temp: 0.3580, Energy: -53.796060-0.007624j
[2025-08-07 18:10:14] [Iter 807/1050] R2[356/600], Temp: 0.3555, Energy: -53.879177-0.003956j
[2025-08-07 18:10:18] [Iter 808/1050] R2[357/600], Temp: 0.3530, Energy: -53.902257+0.003416j
[2025-08-07 18:10:22] [Iter 809/1050] R2[358/600], Temp: 0.3505, Energy: -53.908502-0.007338j
[2025-08-07 18:10:27] [Iter 810/1050] R2[359/600], Temp: 0.3480, Energy: -53.863155-0.000328j
[2025-08-07 18:10:31] [Iter 811/1050] R2[360/600], Temp: 0.3455, Energy: -53.868495-0.007117j
[2025-08-07 18:10:35] [Iter 812/1050] R2[361/600], Temp: 0.3430, Energy: -53.805773-0.003267j
[2025-08-07 18:10:39] [Iter 813/1050] R2[362/600], Temp: 0.3405, Energy: -53.760797-0.000655j
[2025-08-07 18:10:43] [Iter 814/1050] R2[363/600], Temp: 0.3380, Energy: -53.677785-0.001424j
[2025-08-07 18:10:47] [Iter 815/1050] R2[364/600], Temp: 0.3356, Energy: -53.856776-0.001631j
[2025-08-07 18:10:51] [Iter 816/1050] R2[365/600], Temp: 0.3331, Energy: -53.855501-0.004640j
[2025-08-07 18:10:55] [Iter 817/1050] R2[366/600], Temp: 0.3306, Energy: -53.758697-0.000558j
[2025-08-07 18:11:00] [Iter 818/1050] R2[367/600], Temp: 0.3282, Energy: -53.843012-0.004452j
[2025-08-07 18:11:04] [Iter 819/1050] R2[368/600], Temp: 0.3257, Energy: -53.896830-0.001644j
[2025-08-07 18:11:08] [Iter 820/1050] R2[369/600], Temp: 0.3233, Energy: -53.880377+0.002708j
[2025-08-07 18:11:12] [Iter 821/1050] R2[370/600], Temp: 0.3208, Energy: -53.744221+0.006609j
[2025-08-07 18:11:16] [Iter 822/1050] R2[371/600], Temp: 0.3184, Energy: -53.890448+0.002417j
[2025-08-07 18:11:20] [Iter 823/1050] R2[372/600], Temp: 0.3159, Energy: -53.864519-0.003328j
[2025-08-07 18:11:24] [Iter 824/1050] R2[373/600], Temp: 0.3135, Energy: -53.944261+0.001341j
[2025-08-07 18:11:28] [Iter 825/1050] R2[374/600], Temp: 0.3111, Energy: -53.947324+0.008943j
[2025-08-07 18:11:32] [Iter 826/1050] R2[375/600], Temp: 0.3087, Energy: -53.841995-0.000038j
[2025-08-07 18:11:37] [Iter 827/1050] R2[376/600], Temp: 0.3062, Energy: -53.901976-0.000390j
[2025-08-07 18:11:41] [Iter 828/1050] R2[377/600], Temp: 0.3038, Energy: -53.739645+0.000955j
[2025-08-07 18:11:45] [Iter 829/1050] R2[378/600], Temp: 0.3014, Energy: -53.742943+0.006070j
[2025-08-07 18:11:49] [Iter 830/1050] R2[379/600], Temp: 0.2990, Energy: -53.713497+0.001613j
[2025-08-07 18:11:53] [Iter 831/1050] R2[380/600], Temp: 0.2966, Energy: -53.790186-0.000441j
[2025-08-07 18:11:57] [Iter 832/1050] R2[381/600], Temp: 0.2942, Energy: -53.757520-0.004412j
[2025-08-07 18:12:01] [Iter 833/1050] R2[382/600], Temp: 0.2919, Energy: -53.779308-0.001767j
[2025-08-07 18:12:05] [Iter 834/1050] R2[383/600], Temp: 0.2895, Energy: -53.771870-0.007621j
[2025-08-07 18:12:10] [Iter 835/1050] R2[384/600], Temp: 0.2871, Energy: -53.694565+0.003496j
[2025-08-07 18:12:14] [Iter 836/1050] R2[385/600], Temp: 0.2847, Energy: -53.805192-0.000045j
[2025-08-07 18:12:18] [Iter 837/1050] R2[386/600], Temp: 0.2824, Energy: -53.761722+0.000631j
[2025-08-07 18:12:22] [Iter 838/1050] R2[387/600], Temp: 0.2800, Energy: -53.839816-0.001470j
[2025-08-07 18:12:26] [Iter 839/1050] R2[388/600], Temp: 0.2777, Energy: -53.851768+0.000120j
[2025-08-07 18:12:30] [Iter 840/1050] R2[389/600], Temp: 0.2753, Energy: -53.789778+0.001450j
[2025-08-07 18:12:34] [Iter 841/1050] R2[390/600], Temp: 0.2730, Energy: -53.916859+0.001725j
[2025-08-07 18:12:38] [Iter 842/1050] R2[391/600], Temp: 0.2707, Energy: -53.834195-0.001724j
[2025-08-07 18:12:42] [Iter 843/1050] R2[392/600], Temp: 0.2684, Energy: -53.764348-0.004841j
[2025-08-07 18:12:47] [Iter 844/1050] R2[393/600], Temp: 0.2660, Energy: -53.749275-0.000422j
[2025-08-07 18:12:51] [Iter 845/1050] R2[394/600], Temp: 0.2637, Energy: -53.747134-0.000819j
[2025-08-07 18:12:55] [Iter 846/1050] R2[395/600], Temp: 0.2614, Energy: -53.894914+0.000934j
[2025-08-07 18:12:59] [Iter 847/1050] R2[396/600], Temp: 0.2591, Energy: -53.858616+0.006765j
[2025-08-07 18:13:03] [Iter 848/1050] R2[397/600], Temp: 0.2568, Energy: -53.863232-0.002139j
[2025-08-07 18:13:07] [Iter 849/1050] R2[398/600], Temp: 0.2545, Energy: -53.801105-0.001337j
[2025-08-07 18:13:11] [Iter 850/1050] R2[399/600], Temp: 0.2523, Energy: -53.787808-0.003659j
[2025-08-07 18:13:15] [Iter 851/1050] R2[400/600], Temp: 0.2500, Energy: -53.797273-0.001650j
[2025-08-07 18:13:20] [Iter 852/1050] R2[401/600], Temp: 0.2477, Energy: -53.762035-0.002575j
[2025-08-07 18:13:24] [Iter 853/1050] R2[402/600], Temp: 0.2455, Energy: -53.793091-0.000685j
[2025-08-07 18:13:28] [Iter 854/1050] R2[403/600], Temp: 0.2432, Energy: -53.862227+0.001159j
[2025-08-07 18:13:32] [Iter 855/1050] R2[404/600], Temp: 0.2410, Energy: -54.021293+0.003071j
[2025-08-07 18:13:36] [Iter 856/1050] R2[405/600], Temp: 0.2388, Energy: -53.890119-0.010148j
[2025-08-07 18:13:40] [Iter 857/1050] R2[406/600], Temp: 0.2365, Energy: -53.849500-0.000755j
[2025-08-07 18:13:44] [Iter 858/1050] R2[407/600], Temp: 0.2343, Energy: -53.811786+0.001995j
[2025-08-07 18:13:48] [Iter 859/1050] R2[408/600], Temp: 0.2321, Energy: -53.798991-0.003253j
[2025-08-07 18:13:52] [Iter 860/1050] R2[409/600], Temp: 0.2299, Energy: -53.814652+0.000912j
[2025-08-07 18:13:57] [Iter 861/1050] R2[410/600], Temp: 0.2277, Energy: -53.834596-0.000048j
[2025-08-07 18:14:01] [Iter 862/1050] R2[411/600], Temp: 0.2255, Energy: -53.788602-0.000386j
[2025-08-07 18:14:05] [Iter 863/1050] R2[412/600], Temp: 0.2233, Energy: -53.769250+0.001015j
[2025-08-07 18:14:09] [Iter 864/1050] R2[413/600], Temp: 0.2211, Energy: -53.801446-0.002651j
[2025-08-07 18:14:13] [Iter 865/1050] R2[414/600], Temp: 0.2190, Energy: -53.757475-0.000455j
[2025-08-07 18:14:17] [Iter 866/1050] R2[415/600], Temp: 0.2168, Energy: -53.876702+0.000590j
[2025-08-07 18:14:21] [Iter 867/1050] R2[416/600], Temp: 0.2146, Energy: -53.751377+0.000054j
[2025-08-07 18:14:25] [Iter 868/1050] R2[417/600], Temp: 0.2125, Energy: -53.904526-0.000441j
[2025-08-07 18:14:30] [Iter 869/1050] R2[418/600], Temp: 0.2104, Energy: -53.717626-0.002875j
[2025-08-07 18:14:34] [Iter 870/1050] R2[419/600], Temp: 0.2082, Energy: -53.769607-0.001034j
[2025-08-07 18:14:38] [Iter 871/1050] R2[420/600], Temp: 0.2061, Energy: -53.735738+0.002016j
[2025-08-07 18:14:42] [Iter 872/1050] R2[421/600], Temp: 0.2040, Energy: -53.684321-0.002335j
[2025-08-07 18:14:46] [Iter 873/1050] R2[422/600], Temp: 0.2019, Energy: -53.717261-0.002292j
[2025-08-07 18:14:50] [Iter 874/1050] R2[423/600], Temp: 0.1998, Energy: -53.846331-0.004526j
[2025-08-07 18:14:54] [Iter 875/1050] R2[424/600], Temp: 0.1977, Energy: -53.881582+0.003112j
[2025-08-07 18:14:58] [Iter 876/1050] R2[425/600], Temp: 0.1956, Energy: -53.787015+0.006645j
[2025-08-07 18:15:02] [Iter 877/1050] R2[426/600], Temp: 0.1935, Energy: -53.951235-0.003289j
[2025-08-07 18:15:07] [Iter 878/1050] R2[427/600], Temp: 0.1915, Energy: -53.876247+0.002201j
[2025-08-07 18:15:11] [Iter 879/1050] R2[428/600], Temp: 0.1894, Energy: -53.936552+0.005452j
[2025-08-07 18:15:15] [Iter 880/1050] R2[429/600], Temp: 0.1874, Energy: -53.863683+0.000736j
[2025-08-07 18:15:19] [Iter 881/1050] R2[430/600], Temp: 0.1853, Energy: -53.988101-0.001607j
[2025-08-07 18:15:23] [Iter 882/1050] R2[431/600], Temp: 0.1833, Energy: -53.938803+0.002375j
[2025-08-07 18:15:27] [Iter 883/1050] R2[432/600], Temp: 0.1813, Energy: -53.948782-0.001705j
[2025-08-07 18:15:31] [Iter 884/1050] R2[433/600], Temp: 0.1793, Energy: -53.934795-0.000777j
[2025-08-07 18:15:35] [Iter 885/1050] R2[434/600], Temp: 0.1773, Energy: -53.950120-0.003721j
[2025-08-07 18:15:40] [Iter 886/1050] R2[435/600], Temp: 0.1753, Energy: -53.951270-0.001982j
[2025-08-07 18:15:44] [Iter 887/1050] R2[436/600], Temp: 0.1733, Energy: -53.902542-0.005654j
[2025-08-07 18:15:48] [Iter 888/1050] R2[437/600], Temp: 0.1713, Energy: -53.945681-0.008898j
[2025-08-07 18:15:52] [Iter 889/1050] R2[438/600], Temp: 0.1693, Energy: -53.918725+0.001729j
[2025-08-07 18:15:56] [Iter 890/1050] R2[439/600], Temp: 0.1674, Energy: -53.926851-0.005756j
[2025-08-07 18:16:00] [Iter 891/1050] R2[440/600], Temp: 0.1654, Energy: -53.964927+0.004781j
[2025-08-07 18:16:04] [Iter 892/1050] R2[441/600], Temp: 0.1635, Energy: -53.863881+0.001419j
[2025-08-07 18:16:08] [Iter 893/1050] R2[442/600], Temp: 0.1616, Energy: -53.866994+0.003055j
[2025-08-07 18:16:12] [Iter 894/1050] R2[443/600], Temp: 0.1596, Energy: -53.861578+0.005274j
[2025-08-07 18:16:17] [Iter 895/1050] R2[444/600], Temp: 0.1577, Energy: -53.912933+0.002354j
[2025-08-07 18:16:21] [Iter 896/1050] R2[445/600], Temp: 0.1558, Energy: -53.966884+0.002500j
[2025-08-07 18:16:25] [Iter 897/1050] R2[446/600], Temp: 0.1539, Energy: -54.000497+0.003633j
[2025-08-07 18:16:29] [Iter 898/1050] R2[447/600], Temp: 0.1520, Energy: -53.968582+0.003402j
[2025-08-07 18:16:33] [Iter 899/1050] R2[448/600], Temp: 0.1502, Energy: -53.961147+0.003200j
[2025-08-07 18:16:37] [Iter 900/1050] R2[449/600], Temp: 0.1483, Energy: -54.005593-0.001211j
[2025-08-07 18:16:37] ✓ Checkpoint saved: checkpoint_iter_000900.pkl
[2025-08-07 18:16:41] [Iter 901/1050] R2[450/600], Temp: 0.1464, Energy: -53.864636+0.000160j
[2025-08-07 18:16:45] [Iter 902/1050] R2[451/600], Temp: 0.1446, Energy: -53.885301+0.003602j
[2025-08-07 18:16:50] [Iter 903/1050] R2[452/600], Temp: 0.1428, Energy: -53.847366-0.003888j
[2025-08-07 18:16:54] [Iter 904/1050] R2[453/600], Temp: 0.1409, Energy: -53.886839+0.000131j
[2025-08-07 18:16:58] [Iter 905/1050] R2[454/600], Temp: 0.1391, Energy: -54.015898+0.004465j
[2025-08-07 18:17:02] [Iter 906/1050] R2[455/600], Temp: 0.1373, Energy: -53.916123-0.001338j
[2025-08-07 18:17:06] [Iter 907/1050] R2[456/600], Temp: 0.1355, Energy: -53.941432-0.003987j
[2025-08-07 18:17:10] [Iter 908/1050] R2[457/600], Temp: 0.1337, Energy: -53.899676+0.001453j
[2025-08-07 18:17:14] [Iter 909/1050] R2[458/600], Temp: 0.1320, Energy: -53.952470+0.000608j
[2025-08-07 18:17:18] [Iter 910/1050] R2[459/600], Temp: 0.1302, Energy: -53.952079+0.003640j
[2025-08-07 18:17:22] [Iter 911/1050] R2[460/600], Temp: 0.1284, Energy: -53.853716+0.004458j
[2025-08-07 18:17:27] [Iter 912/1050] R2[461/600], Temp: 0.1267, Energy: -53.849156+0.001448j
[2025-08-07 18:17:31] [Iter 913/1050] R2[462/600], Temp: 0.1249, Energy: -53.809492-0.001177j
[2025-08-07 18:17:35] [Iter 914/1050] R2[463/600], Temp: 0.1232, Energy: -53.844184+0.000897j
[2025-08-07 18:17:39] [Iter 915/1050] R2[464/600], Temp: 0.1215, Energy: -53.653170+0.003024j
[2025-08-07 18:17:43] [Iter 916/1050] R2[465/600], Temp: 0.1198, Energy: -53.706459-0.000416j
[2025-08-07 18:17:47] [Iter 917/1050] R2[466/600], Temp: 0.1181, Energy: -53.639183+0.003649j
[2025-08-07 18:17:52] [Iter 918/1050] R2[467/600], Temp: 0.1164, Energy: -53.803261+0.008266j
[2025-08-07 18:17:56] [Iter 919/1050] R2[468/600], Temp: 0.1147, Energy: -53.894450-0.000411j
[2025-08-07 18:18:00] [Iter 920/1050] R2[469/600], Temp: 0.1131, Energy: -53.804480-0.000251j
[2025-08-07 18:18:04] [Iter 921/1050] R2[470/600], Temp: 0.1114, Energy: -53.848647-0.002917j
[2025-08-07 18:18:08] [Iter 922/1050] R2[471/600], Temp: 0.1098, Energy: -53.859010+0.002966j
[2025-08-07 18:18:12] [Iter 923/1050] R2[472/600], Temp: 0.1082, Energy: -53.768076+0.000729j
[2025-08-07 18:18:16] [Iter 924/1050] R2[473/600], Temp: 0.1065, Energy: -53.807980-0.002370j
[2025-08-07 18:18:20] [Iter 925/1050] R2[474/600], Temp: 0.1049, Energy: -53.826027-0.001152j
[2025-08-07 18:18:24] [Iter 926/1050] R2[475/600], Temp: 0.1033, Energy: -53.895395+0.005156j
[2025-08-07 18:18:29] [Iter 927/1050] R2[476/600], Temp: 0.1017, Energy: -54.007684+0.006921j
[2025-08-07 18:18:33] [Iter 928/1050] R2[477/600], Temp: 0.1002, Energy: -53.952145+0.006081j
[2025-08-07 18:18:37] [Iter 929/1050] R2[478/600], Temp: 0.0986, Energy: -54.045157+0.004324j
[2025-08-07 18:18:41] [Iter 930/1050] R2[479/600], Temp: 0.0970, Energy: -54.000779-0.005294j
[2025-08-07 18:18:45] [Iter 931/1050] R2[480/600], Temp: 0.0955, Energy: -53.960316-0.004826j
[2025-08-07 18:18:49] [Iter 932/1050] R2[481/600], Temp: 0.0940, Energy: -53.920968+0.000659j
[2025-08-07 18:18:53] [Iter 933/1050] R2[482/600], Temp: 0.0924, Energy: -53.894765+0.008327j
[2025-08-07 18:18:57] [Iter 934/1050] R2[483/600], Temp: 0.0909, Energy: -53.962275-0.004076j
[2025-08-07 18:19:02] [Iter 935/1050] R2[484/600], Temp: 0.0894, Energy: -53.853648-0.001608j
[2025-08-07 18:19:06] [Iter 936/1050] R2[485/600], Temp: 0.0879, Energy: -53.997010-0.008725j
[2025-08-07 18:19:10] [Iter 937/1050] R2[486/600], Temp: 0.0865, Energy: -53.932313+0.002374j
[2025-08-07 18:19:14] [Iter 938/1050] R2[487/600], Temp: 0.0850, Energy: -53.979055-0.002820j
[2025-08-07 18:19:18] [Iter 939/1050] R2[488/600], Temp: 0.0835, Energy: -53.975856+0.000591j
[2025-08-07 18:19:22] [Iter 940/1050] R2[489/600], Temp: 0.0821, Energy: -53.982574-0.005002j
[2025-08-07 18:19:26] [Iter 941/1050] R2[490/600], Temp: 0.0807, Energy: -53.990360+0.001817j
[2025-08-07 18:19:30] [Iter 942/1050] R2[491/600], Temp: 0.0792, Energy: -53.919282-0.003434j
[2025-08-07 18:19:34] [Iter 943/1050] R2[492/600], Temp: 0.0778, Energy: -54.007553+0.002093j
[2025-08-07 18:19:39] [Iter 944/1050] R2[493/600], Temp: 0.0764, Energy: -53.954436+0.000639j
[2025-08-07 18:19:43] [Iter 945/1050] R2[494/600], Temp: 0.0751, Energy: -53.898464-0.004559j
[2025-08-07 18:19:47] [Iter 946/1050] R2[495/600], Temp: 0.0737, Energy: -53.898315-0.000413j
[2025-08-07 18:19:51] [Iter 947/1050] R2[496/600], Temp: 0.0723, Energy: -53.868488+0.004703j
[2025-08-07 18:19:55] [Iter 948/1050] R2[497/600], Temp: 0.0710, Energy: -53.818647+0.005651j
[2025-08-07 18:19:59] [Iter 949/1050] R2[498/600], Temp: 0.0696, Energy: -53.917640+0.004636j
[2025-08-07 18:20:03] [Iter 950/1050] R2[499/600], Temp: 0.0683, Energy: -53.875540+0.003328j
[2025-08-07 18:20:07] [Iter 951/1050] R2[500/600], Temp: 0.0670, Energy: -53.810897+0.004366j
[2025-08-07 18:20:12] [Iter 952/1050] R2[501/600], Temp: 0.0657, Energy: -53.877725+0.002174j
[2025-08-07 18:20:16] [Iter 953/1050] R2[502/600], Temp: 0.0644, Energy: -53.831081+0.000095j
[2025-08-07 18:20:20] [Iter 954/1050] R2[503/600], Temp: 0.0631, Energy: -53.770806-0.001557j
[2025-08-07 18:20:24] [Iter 955/1050] R2[504/600], Temp: 0.0618, Energy: -53.791075-0.001407j
[2025-08-07 18:20:28] [Iter 956/1050] R2[505/600], Temp: 0.0606, Energy: -53.810606+0.001132j
[2025-08-07 18:20:32] [Iter 957/1050] R2[506/600], Temp: 0.0593, Energy: -53.861165+0.006860j
[2025-08-07 18:20:36] [Iter 958/1050] R2[507/600], Temp: 0.0581, Energy: -53.821684+0.000432j
[2025-08-07 18:20:40] [Iter 959/1050] R2[508/600], Temp: 0.0569, Energy: -53.813685-0.007453j
[2025-08-07 18:20:44] [Iter 960/1050] R2[509/600], Temp: 0.0557, Energy: -53.890660+0.001715j
[2025-08-07 18:20:49] [Iter 961/1050] R2[510/600], Temp: 0.0545, Energy: -53.915562+0.004862j
[2025-08-07 18:20:53] [Iter 962/1050] R2[511/600], Temp: 0.0533, Energy: -53.797811+0.000994j
[2025-08-07 18:20:57] [Iter 963/1050] R2[512/600], Temp: 0.0521, Energy: -53.738260+0.001637j
[2025-08-07 18:21:01] [Iter 964/1050] R2[513/600], Temp: 0.0510, Energy: -53.783044+0.002721j
[2025-08-07 18:21:05] [Iter 965/1050] R2[514/600], Temp: 0.0498, Energy: -53.924304-0.006560j
[2025-08-07 18:21:09] [Iter 966/1050] R2[515/600], Temp: 0.0487, Energy: -53.790056+0.000632j
[2025-08-07 18:21:13] [Iter 967/1050] R2[516/600], Temp: 0.0476, Energy: -53.829653+0.001806j
[2025-08-07 18:21:18] [Iter 968/1050] R2[517/600], Temp: 0.0465, Energy: -53.889595-0.002769j
[2025-08-07 18:21:22] [Iter 969/1050] R2[518/600], Temp: 0.0454, Energy: -53.803179+0.000768j
[2025-08-07 18:21:26] [Iter 970/1050] R2[519/600], Temp: 0.0443, Energy: -53.917553+0.003349j
[2025-08-07 18:21:30] [Iter 971/1050] R2[520/600], Temp: 0.0432, Energy: -53.878139+0.002097j
[2025-08-07 18:21:34] [Iter 972/1050] R2[521/600], Temp: 0.0422, Energy: -53.874816-0.000807j
[2025-08-07 18:21:38] [Iter 973/1050] R2[522/600], Temp: 0.0411, Energy: -53.769518+0.000911j
[2025-08-07 18:21:42] [Iter 974/1050] R2[523/600], Temp: 0.0401, Energy: -53.857049-0.003625j
[2025-08-07 18:21:46] [Iter 975/1050] R2[524/600], Temp: 0.0391, Energy: -53.915802-0.005807j
[2025-08-07 18:21:51] [Iter 976/1050] R2[525/600], Temp: 0.0381, Energy: -53.946405+0.001845j
[2025-08-07 18:21:55] [Iter 977/1050] R2[526/600], Temp: 0.0371, Energy: -53.966161+0.000797j
[2025-08-07 18:21:59] [Iter 978/1050] R2[527/600], Temp: 0.0361, Energy: -53.914201+0.002693j
[2025-08-07 18:22:03] [Iter 979/1050] R2[528/600], Temp: 0.0351, Energy: -53.967606-0.003117j
[2025-08-07 18:22:07] [Iter 980/1050] R2[529/600], Temp: 0.0342, Energy: -53.880041-0.001452j
[2025-08-07 18:22:11] [Iter 981/1050] R2[530/600], Temp: 0.0332, Energy: -53.744998+0.005868j
[2025-08-07 18:22:15] [Iter 982/1050] R2[531/600], Temp: 0.0323, Energy: -53.888308-0.002263j
[2025-08-07 18:22:19] [Iter 983/1050] R2[532/600], Temp: 0.0314, Energy: -53.918206-0.000982j
[2025-08-07 18:22:24] [Iter 984/1050] R2[533/600], Temp: 0.0305, Energy: -53.915554+0.000908j
[2025-08-07 18:22:28] [Iter 985/1050] R2[534/600], Temp: 0.0296, Energy: -53.861786-0.000642j
[2025-08-07 18:22:32] [Iter 986/1050] R2[535/600], Temp: 0.0287, Energy: -53.755225-0.006336j
[2025-08-07 18:22:36] [Iter 987/1050] R2[536/600], Temp: 0.0278, Energy: -53.744298-0.000763j
[2025-08-07 18:22:40] [Iter 988/1050] R2[537/600], Temp: 0.0270, Energy: -53.789758+0.000629j
[2025-08-07 18:22:44] [Iter 989/1050] R2[538/600], Temp: 0.0261, Energy: -53.742209+0.000467j
[2025-08-07 18:22:48] [Iter 990/1050] R2[539/600], Temp: 0.0253, Energy: -53.773627+0.002216j
[2025-08-07 18:22:52] [Iter 991/1050] R2[540/600], Temp: 0.0245, Energy: -53.812356+0.001682j
[2025-08-07 18:22:56] [Iter 992/1050] R2[541/600], Temp: 0.0237, Energy: -53.935292+0.001644j
[2025-08-07 18:23:01] [Iter 993/1050] R2[542/600], Temp: 0.0229, Energy: -53.868405-0.001038j
[2025-08-07 18:23:05] [Iter 994/1050] R2[543/600], Temp: 0.0221, Energy: -53.928854-0.002538j
[2025-08-07 18:23:09] [Iter 995/1050] R2[544/600], Temp: 0.0213, Energy: -53.959664+0.000700j
[2025-08-07 18:23:13] [Iter 996/1050] R2[545/600], Temp: 0.0206, Energy: -53.868740-0.004534j
[2025-08-07 18:23:17] [Iter 997/1050] R2[546/600], Temp: 0.0199, Energy: -53.770743+0.004025j
[2025-08-07 18:23:21] [Iter 998/1050] R2[547/600], Temp: 0.0191, Energy: -53.757976+0.004545j
[2025-08-07 18:23:25] [Iter 999/1050] R2[548/600], Temp: 0.0184, Energy: -53.763216-0.001171j
[2025-08-07 18:23:29] [Iter 1000/1050] R2[549/600], Temp: 0.0177, Energy: -53.771668+0.007012j
[2025-08-07 18:23:29] ✓ Checkpoint saved: checkpoint_iter_001000.pkl
[2025-08-07 18:23:34] [Iter 1001/1050] R2[550/600], Temp: 0.0170, Energy: -53.705767-0.004968j
[2025-08-07 18:23:38] [Iter 1002/1050] R2[551/600], Temp: 0.0164, Energy: -53.688157-0.003820j
[2025-08-07 18:23:42] [Iter 1003/1050] R2[552/600], Temp: 0.0157, Energy: -53.753440-0.001669j
[2025-08-07 18:23:46] [Iter 1004/1050] R2[553/600], Temp: 0.0151, Energy: -53.686565+0.002331j
[2025-08-07 18:23:50] [Iter 1005/1050] R2[554/600], Temp: 0.0144, Energy: -53.807383+0.000590j
[2025-08-07 18:23:54] [Iter 1006/1050] R2[555/600], Temp: 0.0138, Energy: -53.829914+0.003976j
[2025-08-07 18:23:58] [Iter 1007/1050] R2[556/600], Temp: 0.0132, Energy: -53.743908-0.001398j
[2025-08-07 18:24:02] [Iter 1008/1050] R2[557/600], Temp: 0.0126, Energy: -53.748987-0.006397j
[2025-08-07 18:24:06] [Iter 1009/1050] R2[558/600], Temp: 0.0120, Energy: -53.789447-0.001649j
[2025-08-07 18:24:11] [Iter 1010/1050] R2[559/600], Temp: 0.0115, Energy: -53.849727+0.003159j
[2025-08-07 18:24:15] [Iter 1011/1050] R2[560/600], Temp: 0.0109, Energy: -53.818710-0.001654j
[2025-08-07 18:24:19] [Iter 1012/1050] R2[561/600], Temp: 0.0104, Energy: -53.825681-0.000332j
[2025-08-07 18:24:23] [Iter 1013/1050] R2[562/600], Temp: 0.0099, Energy: -53.805999-0.004896j
[2025-08-07 18:24:27] [Iter 1014/1050] R2[563/600], Temp: 0.0094, Energy: -53.802004+0.002353j
[2025-08-07 18:24:31] [Iter 1015/1050] R2[564/600], Temp: 0.0089, Energy: -53.866039+0.000061j
[2025-08-07 18:24:35] [Iter 1016/1050] R2[565/600], Temp: 0.0084, Energy: -53.820323+0.001188j
[2025-08-07 18:24:39] [Iter 1017/1050] R2[566/600], Temp: 0.0079, Energy: -53.829489-0.003174j
[2025-08-07 18:24:44] [Iter 1018/1050] R2[567/600], Temp: 0.0074, Energy: -53.798123-0.004232j
[2025-08-07 18:24:48] [Iter 1019/1050] R2[568/600], Temp: 0.0070, Energy: -53.855351-0.000023j
[2025-08-07 18:24:52] [Iter 1020/1050] R2[569/600], Temp: 0.0066, Energy: -53.815092-0.002767j
[2025-08-07 18:24:56] [Iter 1021/1050] R2[570/600], Temp: 0.0062, Energy: -53.885417-0.000277j
[2025-08-07 18:25:00] [Iter 1022/1050] R2[571/600], Temp: 0.0058, Energy: -53.844975-0.002926j
[2025-08-07 18:25:04] [Iter 1023/1050] R2[572/600], Temp: 0.0054, Energy: -53.746786+0.006308j
[2025-08-07 18:25:08] [Iter 1024/1050] R2[573/600], Temp: 0.0050, Energy: -53.783540-0.001360j
[2025-08-07 18:25:12] [Iter 1025/1050] R2[574/600], Temp: 0.0046, Energy: -53.772561-0.001565j
[2025-08-07 18:25:17] [Iter 1026/1050] R2[575/600], Temp: 0.0043, Energy: -53.861658+0.001293j
[2025-08-07 18:25:21] [Iter 1027/1050] R2[576/600], Temp: 0.0039, Energy: -53.920232-0.004327j
[2025-08-07 18:25:25] [Iter 1028/1050] R2[577/600], Temp: 0.0036, Energy: -53.933844+0.003296j
[2025-08-07 18:25:29] [Iter 1029/1050] R2[578/600], Temp: 0.0033, Energy: -54.005006+0.001892j
[2025-08-07 18:25:33] [Iter 1030/1050] R2[579/600], Temp: 0.0030, Energy: -53.948892-0.009190j
[2025-08-07 18:25:37] [Iter 1031/1050] R2[580/600], Temp: 0.0027, Energy: -53.900734+0.001823j
[2025-08-07 18:25:41] [Iter 1032/1050] R2[581/600], Temp: 0.0025, Energy: -53.850710-0.000010j
[2025-08-07 18:25:45] [Iter 1033/1050] R2[582/600], Temp: 0.0022, Energy: -53.744464+0.005152j
[2025-08-07 18:25:50] [Iter 1034/1050] R2[583/600], Temp: 0.0020, Energy: -53.797136+0.001354j
[2025-08-07 18:25:54] [Iter 1035/1050] R2[584/600], Temp: 0.0018, Energy: -53.841274+0.004836j
[2025-08-07 18:25:58] [Iter 1036/1050] R2[585/600], Temp: 0.0015, Energy: -53.783553+0.002836j
[2025-08-07 18:26:02] [Iter 1037/1050] R2[586/600], Temp: 0.0013, Energy: -53.800853-0.000542j
[2025-08-07 18:26:06] [Iter 1038/1050] R2[587/600], Temp: 0.0012, Energy: -53.880304-0.001307j
[2025-08-07 18:26:10] [Iter 1039/1050] R2[588/600], Temp: 0.0010, Energy: -53.845806-0.000934j
[2025-08-07 18:26:14] [Iter 1040/1050] R2[589/600], Temp: 0.0008, Energy: -53.845115-0.002534j
[2025-08-07 18:26:18] [Iter 1041/1050] R2[590/600], Temp: 0.0007, Energy: -53.888352+0.002923j
[2025-08-07 18:26:23] [Iter 1042/1050] R2[591/600], Temp: 0.0006, Energy: -53.881534-0.002890j
[2025-08-07 18:26:27] [Iter 1043/1050] R2[592/600], Temp: 0.0004, Energy: -53.977984+0.006253j
[2025-08-07 18:26:31] [Iter 1044/1050] R2[593/600], Temp: 0.0003, Energy: -53.869605-0.000838j
[2025-08-07 18:26:35] [Iter 1045/1050] R2[594/600], Temp: 0.0002, Energy: -53.792132+0.003564j
[2025-08-07 18:26:39] [Iter 1046/1050] R2[595/600], Temp: 0.0002, Energy: -53.836124-0.001490j
[2025-08-07 18:26:43] [Iter 1047/1050] R2[596/600], Temp: 0.0001, Energy: -53.751331+0.000014j
[2025-08-07 18:26:47] [Iter 1048/1050] R2[597/600], Temp: 0.0001, Energy: -53.894257-0.002077j
[2025-08-07 18:26:51] [Iter 1049/1050] R2[598/600], Temp: 0.0000, Energy: -53.967599-0.003467j
[2025-08-07 18:26:56] [Iter 1050/1050] R2[599/600], Temp: 0.0000, Energy: -53.954352-0.003053j
[2025-08-07 18:26:56] ✅ Training completed | Restarts: 2
[2025-08-07 18:26:56] ============================================================
[2025-08-07 18:26:56] Training completed | Runtime: 4367.4s
[2025-08-07 18:27:08] ✓ Final state saved: checkpoints/final_GCNN.pkl
[2025-08-07 18:27:08] ============================================================
