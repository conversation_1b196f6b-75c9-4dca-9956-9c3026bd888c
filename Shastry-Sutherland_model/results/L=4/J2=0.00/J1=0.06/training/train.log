[2025-08-07 17:16:26] ✓ 从checkpoint恢复: results/L=4/J2=0.00/J1=0.05/training/checkpoints/final_GCNN.pkl
[2025-08-07 17:16:26]   - 迭代次数: final
[2025-08-07 17:16:26]   - 能量: -55.049166+0.004151j ± 0.085835
[2025-08-07 17:16:26]   - 时间戳: 2025-08-07T14:53:48.712188+08:00
[2025-08-07 17:16:33] ✓ 变分状态参数已从checkpoint恢复
[2025-08-07 17:16:33] ✓ 从final状态恢复, 重置迭代计数为0
[2025-08-07 17:16:33] ==================================================
[2025-08-07 17:16:33] GCNN for Shastry-Sutherland Model
[2025-08-07 17:16:33] ==================================================
[2025-08-07 17:16:33] System parameters:
[2025-08-07 17:16:33]   - System size: L=4, N=64
[2025-08-07 17:16:33]   - System parameters: J1=0.06, J2=0.0, Q=1.0
[2025-08-07 17:16:33] --------------------------------------------------
[2025-08-07 17:16:33] Model parameters:
[2025-08-07 17:16:33]   - Number of layers = 4
[2025-08-07 17:16:33]   - Number of features = 4
[2025-08-07 17:16:33]   - Total parameters = 12572
[2025-08-07 17:16:33] --------------------------------------------------
[2025-08-07 17:16:33] Training parameters:
[2025-08-07 17:16:33]   - Learning rate: 0.015
[2025-08-07 17:16:33]   - Total iterations: 1050
[2025-08-07 17:16:33]   - Annealing cycles: 3
[2025-08-07 17:16:33]   - Initial period: 150
[2025-08-07 17:16:33]   - Period multiplier: 2.0
[2025-08-07 17:16:33]   - Temperature range: 0.0-1.0
[2025-08-07 17:16:33]   - Samples: 4096
[2025-08-07 17:16:33]   - Discarded samples: 0
[2025-08-07 17:16:33]   - Chunk size: 2048
[2025-08-07 17:16:33]   - Diagonal shift: 0.2
[2025-08-07 17:16:33]   - Gradient clipping: 1.0
[2025-08-07 17:16:33]   - Checkpoint enabled: interval=100
[2025-08-07 17:16:33]   - Checkpoint directory: results/L=4/J2=0.00/J1=0.06/training/checkpoints
[2025-08-07 17:16:33] --------------------------------------------------
[2025-08-07 17:16:33] Device status:
[2025-08-07 17:16:33]   - Devices model: A100
[2025-08-07 17:16:33]   - Number of devices: 1
[2025-08-07 17:16:33]   - Sharding: True
[2025-08-07 17:16:33] ============================================================
[2025-08-07 17:17:00] [Iter 1/1050] R0[0/150], Temp: 1.0000, Energy: -55.286318+0.006951j
[2025-08-07 17:17:18] [Iter 2/1050] R0[1/150], Temp: 0.9999, Energy: -55.457400-0.003573j
[2025-08-07 17:17:22] [Iter 3/1050] R0[2/150], Temp: 0.9996, Energy: -55.445782+0.009345j
[2025-08-07 17:17:26] [Iter 4/1050] R0[3/150], Temp: 0.9990, Energy: -55.460920+0.006466j
[2025-08-07 17:17:30] [Iter 5/1050] R0[4/150], Temp: 0.9982, Energy: -55.454104-0.001434j
[2025-08-07 17:17:34] [Iter 6/1050] R0[5/150], Temp: 0.9973, Energy: -55.335061+0.006421j
[2025-08-07 17:17:39] [Iter 7/1050] R0[6/150], Temp: 0.9961, Energy: -55.313813+0.002965j
[2025-08-07 17:17:43] [Iter 8/1050] R0[7/150], Temp: 0.9946, Energy: -55.386470+0.002540j
[2025-08-07 17:17:47] [Iter 9/1050] R0[8/150], Temp: 0.9930, Energy: -55.254276+0.003189j
[2025-08-07 17:17:51] [Iter 10/1050] R0[9/150], Temp: 0.9911, Energy: -55.185205-0.002011j
[2025-08-07 17:17:55] [Iter 11/1050] R0[10/150], Temp: 0.9891, Energy: -55.221999+0.002108j
[2025-08-07 17:17:59] [Iter 12/1050] R0[11/150], Temp: 0.9868, Energy: -55.379421+0.002364j
[2025-08-07 17:18:04] [Iter 13/1050] R0[12/150], Temp: 0.9843, Energy: -55.353470+0.001085j
[2025-08-07 17:18:08] [Iter 14/1050] R0[13/150], Temp: 0.9816, Energy: -55.392925-0.001360j
[2025-08-07 17:18:12] [Iter 15/1050] R0[14/150], Temp: 0.9787, Energy: -55.418375-0.004262j
[2025-08-07 17:18:16] [Iter 16/1050] R0[15/150], Temp: 0.9755, Energy: -55.358400+0.002439j
[2025-08-07 17:18:20] [Iter 17/1050] R0[16/150], Temp: 0.9722, Energy: -55.420776+0.003708j
[2025-08-07 17:18:24] [Iter 18/1050] R0[17/150], Temp: 0.9686, Energy: -55.451125-0.000528j
[2025-08-07 17:18:28] [Iter 19/1050] R0[18/150], Temp: 0.9649, Energy: -55.469863-0.000257j
[2025-08-07 17:18:33] [Iter 20/1050] R0[19/150], Temp: 0.9609, Energy: -55.593936-0.000540j
[2025-08-07 17:18:37] [Iter 21/1050] R0[20/150], Temp: 0.9568, Energy: -55.549934-0.002482j
[2025-08-07 17:18:41] [Iter 22/1050] R0[21/150], Temp: 0.9524, Energy: -55.637982+0.003715j
[2025-08-07 17:18:45] [Iter 23/1050] R0[22/150], Temp: 0.9479, Energy: -55.471749+0.000507j
[2025-08-07 17:18:49] [Iter 24/1050] R0[23/150], Temp: 0.9431, Energy: -55.414368-0.000179j
[2025-08-07 17:18:53] [Iter 25/1050] R0[24/150], Temp: 0.9382, Energy: -55.457610-0.005297j
[2025-08-07 17:18:57] [Iter 26/1050] R0[25/150], Temp: 0.9330, Energy: -55.489281-0.007160j
[2025-08-07 17:19:02] [Iter 27/1050] R0[26/150], Temp: 0.9277, Energy: -55.566157+0.005512j
[2025-08-07 17:19:06] [Iter 28/1050] R0[27/150], Temp: 0.9222, Energy: -55.588129+0.000834j
[2025-08-07 17:19:10] [Iter 29/1050] R0[28/150], Temp: 0.9165, Energy: -55.436139-0.000535j
[2025-08-07 17:19:14] [Iter 30/1050] R0[29/150], Temp: 0.9106, Energy: -55.346871+0.001803j
[2025-08-07 17:19:18] [Iter 31/1050] R0[30/150], Temp: 0.9045, Energy: -55.464945-0.002373j
[2025-08-07 17:19:22] [Iter 32/1050] R0[31/150], Temp: 0.8983, Energy: -55.409979+0.001934j
[2025-08-07 17:19:27] [Iter 33/1050] R0[32/150], Temp: 0.8918, Energy: -55.485544+0.006927j
[2025-08-07 17:19:31] [Iter 34/1050] R0[33/150], Temp: 0.8853, Energy: -55.487651+0.000452j
[2025-08-07 17:19:35] [Iter 35/1050] R0[34/150], Temp: 0.8785, Energy: -55.460944+0.001855j
[2025-08-07 17:19:39] [Iter 36/1050] R0[35/150], Temp: 0.8716, Energy: -55.326689+0.013718j
[2025-08-07 17:19:43] [Iter 37/1050] R0[36/150], Temp: 0.8645, Energy: -55.369852+0.001903j
[2025-08-07 17:19:47] [Iter 38/1050] R0[37/150], Temp: 0.8572, Energy: -55.589111+0.000448j
[2025-08-07 17:19:51] [Iter 39/1050] R0[38/150], Temp: 0.8498, Energy: -55.500156-0.003934j
[2025-08-07 17:19:56] [Iter 40/1050] R0[39/150], Temp: 0.8423, Energy: -55.563096+0.002485j
[2025-08-07 17:20:00] [Iter 41/1050] R0[40/150], Temp: 0.8346, Energy: -55.556014+0.000544j
[2025-08-07 17:20:04] [Iter 42/1050] R0[41/150], Temp: 0.8267, Energy: -55.502436-0.003139j
[2025-08-07 17:20:08] [Iter 43/1050] R0[42/150], Temp: 0.8187, Energy: -55.561997+0.000714j
[2025-08-07 17:20:12] [Iter 44/1050] R0[43/150], Temp: 0.8106, Energy: -55.460919+0.003296j
[2025-08-07 17:20:17] [Iter 45/1050] R0[44/150], Temp: 0.8023, Energy: -55.404812+0.000043j
[2025-08-07 17:20:21] [Iter 46/1050] R0[45/150], Temp: 0.7939, Energy: -55.524439-0.002667j
[2025-08-07 17:20:25] [Iter 47/1050] R0[46/150], Temp: 0.7854, Energy: -55.550430-0.006023j
[2025-08-07 17:20:29] [Iter 48/1050] R0[47/150], Temp: 0.7767, Energy: -55.548535-0.000472j
[2025-08-07 17:20:33] [Iter 49/1050] R0[48/150], Temp: 0.7679, Energy: -55.508744+0.002671j
[2025-08-07 17:20:37] [Iter 50/1050] R0[49/150], Temp: 0.7590, Energy: -55.474939+0.001793j
[2025-08-07 17:20:41] [Iter 51/1050] R0[50/150], Temp: 0.7500, Energy: -55.541027-0.005324j
[2025-08-07 17:20:46] [Iter 52/1050] R0[51/150], Temp: 0.7409, Energy: -55.391924-0.002907j
[2025-08-07 17:20:50] [Iter 53/1050] R0[52/150], Temp: 0.7316, Energy: -55.341499-0.001825j
[2025-08-07 17:20:54] [Iter 54/1050] R0[53/150], Temp: 0.7223, Energy: -55.399105-0.000943j
[2025-08-07 17:20:58] [Iter 55/1050] R0[54/150], Temp: 0.7129, Energy: -55.357572-0.002347j
[2025-08-07 17:21:02] [Iter 56/1050] R0[55/150], Temp: 0.7034, Energy: -55.391149+0.002374j
[2025-08-07 17:21:06] [Iter 57/1050] R0[56/150], Temp: 0.6938, Energy: -55.474620-0.002041j
[2025-08-07 17:21:10] [Iter 58/1050] R0[57/150], Temp: 0.6841, Energy: -55.444354-0.004805j
[2025-08-07 17:21:15] [Iter 59/1050] R0[58/150], Temp: 0.6743, Energy: -55.329659+0.001518j
[2025-08-07 17:21:19] [Iter 60/1050] R0[59/150], Temp: 0.6644, Energy: -55.420149-0.001203j
[2025-08-07 17:21:23] [Iter 61/1050] R0[60/150], Temp: 0.6545, Energy: -55.390765+0.003316j
[2025-08-07 17:21:27] [Iter 62/1050] R0[61/150], Temp: 0.6445, Energy: -55.419103-0.001026j
[2025-08-07 17:21:31] [Iter 63/1050] R0[62/150], Temp: 0.6345, Energy: -55.469177-0.004020j
[2025-08-07 17:21:35] [Iter 64/1050] R0[63/150], Temp: 0.6243, Energy: -55.470936-0.001387j
[2025-08-07 17:21:40] [Iter 65/1050] R0[64/150], Temp: 0.6142, Energy: -55.511746+0.000344j
[2025-08-07 17:21:44] [Iter 66/1050] R0[65/150], Temp: 0.6040, Energy: -55.447899+0.001930j
[2025-08-07 17:21:48] [Iter 67/1050] R0[66/150], Temp: 0.5937, Energy: -55.540502-0.003869j
[2025-08-07 17:21:52] [Iter 68/1050] R0[67/150], Temp: 0.5834, Energy: -55.532043-0.003519j
[2025-08-07 17:21:56] [Iter 69/1050] R0[68/150], Temp: 0.5730, Energy: -55.485126-0.001095j
[2025-08-07 17:22:01] [Iter 70/1050] R0[69/150], Temp: 0.5627, Energy: -55.380932+0.000312j
[2025-08-07 17:22:05] [Iter 71/1050] R0[70/150], Temp: 0.5523, Energy: -55.450543+0.003079j
[2025-08-07 17:22:09] [Iter 72/1050] R0[71/150], Temp: 0.5418, Energy: -55.417570-0.002745j
[2025-08-07 17:22:13] [Iter 73/1050] R0[72/150], Temp: 0.5314, Energy: -55.504076-0.007529j
[2025-08-07 17:22:17] [Iter 74/1050] R0[73/150], Temp: 0.5209, Energy: -55.402032-0.001067j
[2025-08-07 17:22:21] [Iter 75/1050] R0[74/150], Temp: 0.5105, Energy: -55.293590-0.004681j
[2025-08-07 17:22:25] [Iter 76/1050] R0[75/150], Temp: 0.5000, Energy: -55.262432+0.003245j
[2025-08-07 17:22:30] [Iter 77/1050] R0[76/150], Temp: 0.4895, Energy: -55.272043-0.003335j
[2025-08-07 17:22:34] [Iter 78/1050] R0[77/150], Temp: 0.4791, Energy: -55.307453+0.001104j
[2025-08-07 17:22:38] [Iter 79/1050] R0[78/150], Temp: 0.4686, Energy: -55.307665+0.003156j
[2025-08-07 17:22:42] [Iter 80/1050] R0[79/150], Temp: 0.4582, Energy: -55.246068-0.000534j
[2025-08-07 17:22:46] [Iter 81/1050] R0[80/150], Temp: 0.4477, Energy: -55.373559-0.000965j
[2025-08-07 17:22:50] [Iter 82/1050] R0[81/150], Temp: 0.4373, Energy: -55.402376+0.000695j
[2025-08-07 17:22:54] [Iter 83/1050] R0[82/150], Temp: 0.4270, Energy: -55.404761-0.001142j
[2025-08-07 17:22:59] [Iter 84/1050] R0[83/150], Temp: 0.4166, Energy: -55.431350-0.000957j
[2025-08-07 17:23:03] [Iter 85/1050] R0[84/150], Temp: 0.4063, Energy: -55.495119-0.003154j
[2025-08-07 17:23:07] [Iter 86/1050] R0[85/150], Temp: 0.3960, Energy: -55.525960-0.001859j
[2025-08-07 17:23:11] [Iter 87/1050] R0[86/150], Temp: 0.3858, Energy: -55.524089+0.003390j
[2025-08-07 17:23:15] [Iter 88/1050] R0[87/150], Temp: 0.3757, Energy: -55.389251-0.001557j
[2025-08-07 17:23:19] [Iter 89/1050] R0[88/150], Temp: 0.3655, Energy: -55.472351+0.001257j
[2025-08-07 17:23:23] [Iter 90/1050] R0[89/150], Temp: 0.3555, Energy: -55.488859-0.002010j
[2025-08-07 17:23:28] [Iter 91/1050] R0[90/150], Temp: 0.3455, Energy: -55.406563-0.004181j
[2025-08-07 17:23:32] [Iter 92/1050] R0[91/150], Temp: 0.3356, Energy: -55.414167-0.002829j
[2025-08-07 17:23:36] [Iter 93/1050] R0[92/150], Temp: 0.3257, Energy: -55.332618-0.002265j
[2025-08-07 17:23:40] [Iter 94/1050] R0[93/150], Temp: 0.3159, Energy: -55.261373-0.000137j
[2025-08-07 17:23:44] [Iter 95/1050] R0[94/150], Temp: 0.3062, Energy: -55.350762-0.000830j
[2025-08-07 17:23:48] [Iter 96/1050] R0[95/150], Temp: 0.2966, Energy: -55.361180+0.000868j
[2025-08-07 17:23:52] [Iter 97/1050] R0[96/150], Temp: 0.2871, Energy: -55.449336+0.001885j
[2025-08-07 17:23:57] [Iter 98/1050] R0[97/150], Temp: 0.2777, Energy: -55.388710+0.001447j
[2025-08-07 17:24:01] [Iter 99/1050] R0[98/150], Temp: 0.2684, Energy: -55.420486-0.004588j
[2025-08-07 17:24:05] [Iter 100/1050] R0[99/150], Temp: 0.2591, Energy: -55.366253+0.000631j
[2025-08-07 17:24:05] ✓ Checkpoint saved: checkpoint_iter_000100.pkl
[2025-08-07 17:24:09] [Iter 101/1050] R0[100/150], Temp: 0.2500, Energy: -55.369323+0.001639j
[2025-08-07 17:24:13] [Iter 102/1050] R0[101/150], Temp: 0.2410, Energy: -55.451504-0.000344j
[2025-08-07 17:24:17] [Iter 103/1050] R0[102/150], Temp: 0.2321, Energy: -55.399136-0.006546j
[2025-08-07 17:24:22] [Iter 104/1050] R0[103/150], Temp: 0.2233, Energy: -55.482859-0.000482j
[2025-08-07 17:24:26] [Iter 105/1050] R0[104/150], Temp: 0.2146, Energy: -55.481026-0.001613j
[2025-08-07 17:24:30] [Iter 106/1050] R0[105/150], Temp: 0.2061, Energy: -55.514758-0.004259j
[2025-08-07 17:24:34] [Iter 107/1050] R0[106/150], Temp: 0.1977, Energy: -55.394400+0.005217j
[2025-08-07 17:24:38] [Iter 108/1050] R0[107/150], Temp: 0.1894, Energy: -55.431394+0.002497j
[2025-08-07 17:24:42] [Iter 109/1050] R0[108/150], Temp: 0.1813, Energy: -55.456670+0.001610j
[2025-08-07 17:24:47] [Iter 110/1050] R0[109/150], Temp: 0.1733, Energy: -55.504284-0.001033j
[2025-08-07 17:24:51] [Iter 111/1050] R0[110/150], Temp: 0.1654, Energy: -55.495963+0.004151j
[2025-08-07 17:24:55] [Iter 112/1050] R0[111/150], Temp: 0.1577, Energy: -55.589236+0.004615j
[2025-08-07 17:24:59] [Iter 113/1050] R0[112/150], Temp: 0.1502, Energy: -55.579970-0.003996j
[2025-08-07 17:25:03] [Iter 114/1050] R0[113/150], Temp: 0.1428, Energy: -55.522544-0.006386j
[2025-08-07 17:25:07] [Iter 115/1050] R0[114/150], Temp: 0.1355, Energy: -55.396036+0.001345j
[2025-08-07 17:25:12] [Iter 116/1050] R0[115/150], Temp: 0.1284, Energy: -55.382990-0.002279j
[2025-08-07 17:25:16] [Iter 117/1050] R0[116/150], Temp: 0.1215, Energy: -55.348233+0.000962j
[2025-08-07 17:25:20] [Iter 118/1050] R0[117/150], Temp: 0.1147, Energy: -55.431378-0.000400j
[2025-08-07 17:25:24] [Iter 119/1050] R0[118/150], Temp: 0.1082, Energy: -55.460128-0.003574j
[2025-08-07 17:25:28] [Iter 120/1050] R0[119/150], Temp: 0.1017, Energy: -55.473117+0.004532j
[2025-08-07 17:25:32] [Iter 121/1050] R0[120/150], Temp: 0.0955, Energy: -55.362461+0.002938j
[2025-08-07 17:25:36] [Iter 122/1050] R0[121/150], Temp: 0.0894, Energy: -55.466972+0.000416j
[2025-08-07 17:25:41] [Iter 123/1050] R0[122/150], Temp: 0.0835, Energy: -55.426351+0.002627j
[2025-08-07 17:25:45] [Iter 124/1050] R0[123/150], Temp: 0.0778, Energy: -55.399875+0.012681j
[2025-08-07 17:25:49] [Iter 125/1050] R0[124/150], Temp: 0.0723, Energy: -55.456900+0.000709j
[2025-08-07 17:25:53] [Iter 126/1050] R0[125/150], Temp: 0.0670, Energy: -55.430450+0.003652j
[2025-08-07 17:25:57] [Iter 127/1050] R0[126/150], Temp: 0.0618, Energy: -55.378459+0.002667j
[2025-08-07 17:26:01] [Iter 128/1050] R0[127/150], Temp: 0.0569, Energy: -55.451244-0.005892j
[2025-08-07 17:26:05] [Iter 129/1050] R0[128/150], Temp: 0.0521, Energy: -55.451147-0.002028j
[2025-08-07 17:26:10] [Iter 130/1050] R0[129/150], Temp: 0.0476, Energy: -55.486034-0.001491j
[2025-08-07 17:26:14] [Iter 131/1050] R0[130/150], Temp: 0.0432, Energy: -55.480426+0.000952j
[2025-08-07 17:26:18] [Iter 132/1050] R0[131/150], Temp: 0.0391, Energy: -55.471906-0.005935j
[2025-08-07 17:26:22] [Iter 133/1050] R0[132/150], Temp: 0.0351, Energy: -55.423223+0.000346j
[2025-08-07 17:26:26] [Iter 134/1050] R0[133/150], Temp: 0.0314, Energy: -55.489191-0.005195j
[2025-08-07 17:26:30] [Iter 135/1050] R0[134/150], Temp: 0.0278, Energy: -55.456585-0.000128j
[2025-08-07 17:26:35] [Iter 136/1050] R0[135/150], Temp: 0.0245, Energy: -55.373533-0.001404j
[2025-08-07 17:26:39] [Iter 137/1050] R0[136/150], Temp: 0.0213, Energy: -55.384162-0.003656j
[2025-08-07 17:26:43] [Iter 138/1050] R0[137/150], Temp: 0.0184, Energy: -55.266280+0.000242j
[2025-08-07 17:26:47] [Iter 139/1050] R0[138/150], Temp: 0.0157, Energy: -55.414669-0.001205j
[2025-08-07 17:26:51] [Iter 140/1050] R0[139/150], Temp: 0.0132, Energy: -55.239692+0.003432j
[2025-08-07 17:26:55] [Iter 141/1050] R0[140/150], Temp: 0.0109, Energy: -55.275351-0.000646j
[2025-08-07 17:26:59] [Iter 142/1050] R0[141/150], Temp: 0.0089, Energy: -55.182453-0.001927j
[2025-08-07 17:27:04] [Iter 143/1050] R0[142/150], Temp: 0.0070, Energy: -55.388757-0.001519j
[2025-08-07 17:27:08] [Iter 144/1050] R0[143/150], Temp: 0.0054, Energy: -55.439031-0.000755j
[2025-08-07 17:27:12] [Iter 145/1050] R0[144/150], Temp: 0.0039, Energy: -55.406422-0.002161j
[2025-08-07 17:27:16] [Iter 146/1050] R0[145/150], Temp: 0.0027, Energy: -55.339386-0.002340j
[2025-08-07 17:27:20] [Iter 147/1050] R0[146/150], Temp: 0.0018, Energy: -55.361319+0.005791j
[2025-08-07 17:27:24] [Iter 148/1050] R0[147/150], Temp: 0.0010, Energy: -55.496811+0.006286j
[2025-08-07 17:27:28] [Iter 149/1050] R0[148/150], Temp: 0.0004, Energy: -55.443964-0.001730j
[2025-08-07 17:27:33] [Iter 150/1050] R0[149/150], Temp: 0.0001, Energy: -55.557088-0.005090j
[2025-08-07 17:27:33] RESTART #1 | Period: 300
[2025-08-07 17:27:37] [Iter 151/1050] R1[0/300], Temp: 1.0000, Energy: -55.457958-0.001316j
[2025-08-07 17:27:41] [Iter 152/1050] R1[1/300], Temp: 1.0000, Energy: -55.517155+0.000656j
[2025-08-07 17:27:45] [Iter 153/1050] R1[2/300], Temp: 0.9999, Energy: -55.555675-0.001412j
[2025-08-07 17:27:49] [Iter 154/1050] R1[3/300], Temp: 0.9998, Energy: -55.504503-0.002324j
[2025-08-07 17:27:53] [Iter 155/1050] R1[4/300], Temp: 0.9996, Energy: -55.446345-0.001545j
[2025-08-07 17:27:58] [Iter 156/1050] R1[5/300], Temp: 0.9993, Energy: -55.451368+0.000753j
[2025-08-07 17:28:02] [Iter 157/1050] R1[6/300], Temp: 0.9990, Energy: -55.458796+0.001434j
[2025-08-07 17:28:06] [Iter 158/1050] R1[7/300], Temp: 0.9987, Energy: -55.488447+0.003586j
[2025-08-07 17:28:10] [Iter 159/1050] R1[8/300], Temp: 0.9982, Energy: -55.415659+0.000771j
[2025-08-07 17:28:14] [Iter 160/1050] R1[9/300], Temp: 0.9978, Energy: -55.449705-0.001186j
[2025-08-07 17:28:18] [Iter 161/1050] R1[10/300], Temp: 0.9973, Energy: -55.270375+0.000906j
[2025-08-07 17:28:22] [Iter 162/1050] R1[11/300], Temp: 0.9967, Energy: -55.405043-0.000743j
[2025-08-07 17:28:27] [Iter 163/1050] R1[12/300], Temp: 0.9961, Energy: -55.353741+0.002546j
[2025-08-07 17:28:31] [Iter 164/1050] R1[13/300], Temp: 0.9954, Energy: -55.344982+0.000905j
[2025-08-07 17:28:35] [Iter 165/1050] R1[14/300], Temp: 0.9946, Energy: -55.314591+0.000048j
[2025-08-07 17:28:39] [Iter 166/1050] R1[15/300], Temp: 0.9938, Energy: -55.227744-0.004748j
[2025-08-07 17:28:43] [Iter 167/1050] R1[16/300], Temp: 0.9930, Energy: -55.239491+0.001085j
[2025-08-07 17:28:47] [Iter 168/1050] R1[17/300], Temp: 0.9921, Energy: -55.264091-0.002361j
[2025-08-07 17:28:51] [Iter 169/1050] R1[18/300], Temp: 0.9911, Energy: -55.321885+0.003101j
[2025-08-07 17:28:56] [Iter 170/1050] R1[19/300], Temp: 0.9901, Energy: -55.315116+0.001900j
[2025-08-07 17:29:00] [Iter 171/1050] R1[20/300], Temp: 0.9891, Energy: -55.260908-0.001997j
[2025-08-07 17:29:04] [Iter 172/1050] R1[21/300], Temp: 0.9880, Energy: -55.386153-0.003217j
[2025-08-07 17:29:08] [Iter 173/1050] R1[22/300], Temp: 0.9868, Energy: -55.340307-0.005873j
[2025-08-07 17:29:12] [Iter 174/1050] R1[23/300], Temp: 0.9856, Energy: -55.281406+0.002154j
[2025-08-07 17:29:16] [Iter 175/1050] R1[24/300], Temp: 0.9843, Energy: -55.365033+0.003727j
[2025-08-07 17:29:21] [Iter 176/1050] R1[25/300], Temp: 0.9830, Energy: -55.420273+0.004598j
[2025-08-07 17:29:25] [Iter 177/1050] R1[26/300], Temp: 0.9816, Energy: -55.314215-0.000038j
[2025-08-07 17:29:29] [Iter 178/1050] R1[27/300], Temp: 0.9801, Energy: -55.285483+0.005436j
[2025-08-07 17:29:33] [Iter 179/1050] R1[28/300], Temp: 0.9787, Energy: -55.341912-0.001214j
[2025-08-07 17:29:37] [Iter 180/1050] R1[29/300], Temp: 0.9771, Energy: -55.360631+0.002640j
[2025-08-07 17:29:41] [Iter 181/1050] R1[30/300], Temp: 0.9755, Energy: -55.323814-0.000595j
[2025-08-07 17:29:45] [Iter 182/1050] R1[31/300], Temp: 0.9739, Energy: -55.361974-0.001583j
[2025-08-07 17:29:50] [Iter 183/1050] R1[32/300], Temp: 0.9722, Energy: -55.400135+0.000868j
[2025-08-07 17:29:54] [Iter 184/1050] R1[33/300], Temp: 0.9704, Energy: -55.436353+0.001884j
[2025-08-07 17:29:58] [Iter 185/1050] R1[34/300], Temp: 0.9686, Energy: -55.349067-0.000544j
[2025-08-07 17:30:02] [Iter 186/1050] R1[35/300], Temp: 0.9668, Energy: -55.429498+0.003346j
[2025-08-07 17:30:06] [Iter 187/1050] R1[36/300], Temp: 0.9649, Energy: -55.466051+0.001937j
[2025-08-07 17:30:10] [Iter 188/1050] R1[37/300], Temp: 0.9629, Energy: -55.399556-0.000480j
[2025-08-07 17:30:15] [Iter 189/1050] R1[38/300], Temp: 0.9609, Energy: -55.360807+0.005955j
[2025-08-07 17:30:19] [Iter 190/1050] R1[39/300], Temp: 0.9589, Energy: -55.351054+0.000638j
[2025-08-07 17:30:23] [Iter 191/1050] R1[40/300], Temp: 0.9568, Energy: -55.361832+0.000239j
[2025-08-07 17:30:27] [Iter 192/1050] R1[41/300], Temp: 0.9546, Energy: -55.318205+0.001281j
[2025-08-07 17:30:31] [Iter 193/1050] R1[42/300], Temp: 0.9524, Energy: -55.445976+0.001725j
[2025-08-07 17:30:35] [Iter 194/1050] R1[43/300], Temp: 0.9502, Energy: -55.313503+0.008300j
[2025-08-07 17:30:39] [Iter 195/1050] R1[44/300], Temp: 0.9479, Energy: -55.367496-0.000762j
[2025-08-07 17:30:44] [Iter 196/1050] R1[45/300], Temp: 0.9455, Energy: -55.453152-0.004002j
[2025-08-07 17:30:48] [Iter 197/1050] R1[46/300], Temp: 0.9431, Energy: -55.462537-0.001317j
[2025-08-07 17:30:52] [Iter 198/1050] R1[47/300], Temp: 0.9407, Energy: -55.469678+0.001029j
[2025-08-07 17:30:56] [Iter 199/1050] R1[48/300], Temp: 0.9382, Energy: -55.357459+0.001196j
[2025-08-07 17:31:00] [Iter 200/1050] R1[49/300], Temp: 0.9356, Energy: -55.361547+0.006741j
[2025-08-07 17:31:00] ✓ Checkpoint saved: checkpoint_iter_000200.pkl
[2025-08-07 17:31:04] [Iter 201/1050] R1[50/300], Temp: 0.9330, Energy: -55.425477-0.003873j
[2025-08-07 17:31:09] [Iter 202/1050] R1[51/300], Temp: 0.9304, Energy: -55.462721-0.000032j
[2025-08-07 17:31:13] [Iter 203/1050] R1[52/300], Temp: 0.9277, Energy: -55.412598-0.001514j
[2025-08-07 17:31:17] [Iter 204/1050] R1[53/300], Temp: 0.9249, Energy: -55.561356-0.000287j
[2025-08-07 17:31:21] [Iter 205/1050] R1[54/300], Temp: 0.9222, Energy: -55.460447-0.010544j
[2025-08-07 17:31:25] [Iter 206/1050] R1[55/300], Temp: 0.9193, Energy: -55.581095+0.002461j
[2025-08-07 17:31:29] [Iter 207/1050] R1[56/300], Temp: 0.9165, Energy: -55.516552+0.000622j
[2025-08-07 17:31:33] [Iter 208/1050] R1[57/300], Temp: 0.9135, Energy: -55.485503-0.003360j
[2025-08-07 17:31:38] [Iter 209/1050] R1[58/300], Temp: 0.9106, Energy: -55.451798+0.000040j
[2025-08-07 17:31:42] [Iter 210/1050] R1[59/300], Temp: 0.9076, Energy: -55.470570-0.001453j
[2025-08-07 17:31:46] [Iter 211/1050] R1[60/300], Temp: 0.9045, Energy: -55.494068-0.004789j
[2025-08-07 17:31:50] [Iter 212/1050] R1[61/300], Temp: 0.9014, Energy: -55.513583-0.006949j
[2025-08-07 17:31:54] [Iter 213/1050] R1[62/300], Temp: 0.8983, Energy: -55.459308+0.001261j
[2025-08-07 17:31:58] [Iter 214/1050] R1[63/300], Temp: 0.8951, Energy: -55.503251+0.002126j
[2025-08-07 17:32:02] [Iter 215/1050] R1[64/300], Temp: 0.8918, Energy: -55.437768-0.000931j
[2025-08-07 17:32:07] [Iter 216/1050] R1[65/300], Temp: 0.8886, Energy: -55.534841-0.004058j
[2025-08-07 17:32:11] [Iter 217/1050] R1[66/300], Temp: 0.8853, Energy: -55.511170+0.003720j
[2025-08-07 17:32:15] [Iter 218/1050] R1[67/300], Temp: 0.8819, Energy: -55.472124-0.002206j
[2025-08-07 17:32:19] [Iter 219/1050] R1[68/300], Temp: 0.8785, Energy: -55.462965-0.004350j
[2025-08-07 17:32:23] [Iter 220/1050] R1[69/300], Temp: 0.8751, Energy: -55.301731+0.002484j
[2025-08-07 17:32:27] [Iter 221/1050] R1[70/300], Temp: 0.8716, Energy: -55.308247+0.000261j
[2025-08-07 17:32:31] [Iter 222/1050] R1[71/300], Temp: 0.8680, Energy: -55.365629-0.000637j
[2025-08-07 17:32:36] [Iter 223/1050] R1[72/300], Temp: 0.8645, Energy: -55.421737-0.001926j
[2025-08-07 17:32:40] [Iter 224/1050] R1[73/300], Temp: 0.8609, Energy: -55.307849+0.000555j
[2025-08-07 17:32:44] [Iter 225/1050] R1[74/300], Temp: 0.8572, Energy: -55.431442-0.001970j
[2025-08-07 17:32:48] [Iter 226/1050] R1[75/300], Temp: 0.8536, Energy: -55.499387+0.007874j
[2025-08-07 17:32:52] [Iter 227/1050] R1[76/300], Temp: 0.8498, Energy: -55.360271+0.003218j
[2025-08-07 17:32:56] [Iter 228/1050] R1[77/300], Temp: 0.8461, Energy: -55.377502+0.001932j
[2025-08-07 17:33:01] [Iter 229/1050] R1[78/300], Temp: 0.8423, Energy: -55.377100-0.001264j
[2025-08-07 17:33:05] [Iter 230/1050] R1[79/300], Temp: 0.8384, Energy: -55.260762-0.000436j
[2025-08-07 17:33:09] [Iter 231/1050] R1[80/300], Temp: 0.8346, Energy: -55.315910+0.002581j
[2025-08-07 17:33:13] [Iter 232/1050] R1[81/300], Temp: 0.8307, Energy: -55.306332+0.001435j
[2025-08-07 17:33:17] [Iter 233/1050] R1[82/300], Temp: 0.8267, Energy: -55.393274+0.000851j
[2025-08-07 17:33:21] [Iter 234/1050] R1[83/300], Temp: 0.8227, Energy: -55.375114+0.000228j
[2025-08-07 17:33:25] [Iter 235/1050] R1[84/300], Temp: 0.8187, Energy: -55.387019+0.001899j
[2025-08-07 17:33:30] [Iter 236/1050] R1[85/300], Temp: 0.8147, Energy: -55.169334-0.006290j
[2025-08-07 17:33:34] [Iter 237/1050] R1[86/300], Temp: 0.8106, Energy: -55.274442+0.001266j
[2025-08-07 17:33:38] [Iter 238/1050] R1[87/300], Temp: 0.8065, Energy: -55.313486+0.002294j
[2025-08-07 17:33:42] [Iter 239/1050] R1[88/300], Temp: 0.8023, Energy: -55.308787-0.003624j
[2025-08-07 17:33:46] [Iter 240/1050] R1[89/300], Temp: 0.7981, Energy: -55.223067+0.002151j
[2025-08-07 17:33:50] [Iter 241/1050] R1[90/300], Temp: 0.7939, Energy: -55.344328-0.001083j
[2025-08-07 17:33:55] [Iter 242/1050] R1[91/300], Temp: 0.7896, Energy: -55.379531+0.002802j
[2025-08-07 17:33:59] [Iter 243/1050] R1[92/300], Temp: 0.7854, Energy: -55.422887+0.002597j
[2025-08-07 17:34:03] [Iter 244/1050] R1[93/300], Temp: 0.7810, Energy: -55.272262+0.006114j
[2025-08-07 17:34:07] [Iter 245/1050] R1[94/300], Temp: 0.7767, Energy: -55.309265-0.001770j
[2025-08-07 17:34:11] [Iter 246/1050] R1[95/300], Temp: 0.7723, Energy: -55.349044-0.000093j
[2025-08-07 17:34:15] [Iter 247/1050] R1[96/300], Temp: 0.7679, Energy: -55.442514+0.002431j
[2025-08-07 17:34:19] [Iter 248/1050] R1[97/300], Temp: 0.7635, Energy: -55.457891+0.004928j
[2025-08-07 17:34:24] [Iter 249/1050] R1[98/300], Temp: 0.7590, Energy: -55.399932+0.001151j
[2025-08-07 17:34:28] [Iter 250/1050] R1[99/300], Temp: 0.7545, Energy: -55.440626+0.002953j
[2025-08-07 17:34:32] [Iter 251/1050] R1[100/300], Temp: 0.7500, Energy: -55.423214-0.002248j
[2025-08-07 17:34:36] [Iter 252/1050] R1[101/300], Temp: 0.7455, Energy: -55.432294-0.000082j
[2025-08-07 17:34:40] [Iter 253/1050] R1[102/300], Temp: 0.7409, Energy: -55.389257-0.001305j
[2025-08-07 17:34:44] [Iter 254/1050] R1[103/300], Temp: 0.7363, Energy: -55.395600-0.002351j
[2025-08-07 17:34:48] [Iter 255/1050] R1[104/300], Temp: 0.7316, Energy: -55.421608-0.001376j
[2025-08-07 17:34:53] [Iter 256/1050] R1[105/300], Temp: 0.7270, Energy: -55.311341+0.001629j
[2025-08-07 17:34:57] [Iter 257/1050] R1[106/300], Temp: 0.7223, Energy: -55.347030+0.001057j
[2025-08-07 17:35:01] [Iter 258/1050] R1[107/300], Temp: 0.7176, Energy: -55.406926+0.000005j
[2025-08-07 17:35:05] [Iter 259/1050] R1[108/300], Temp: 0.7129, Energy: -55.413087+0.002266j
[2025-08-07 17:35:09] [Iter 260/1050] R1[109/300], Temp: 0.7081, Energy: -55.379378+0.004033j
[2025-08-07 17:35:13] [Iter 261/1050] R1[110/300], Temp: 0.7034, Energy: -55.408449+0.003118j
[2025-08-07 17:35:17] [Iter 262/1050] R1[111/300], Temp: 0.6986, Energy: -55.340475+0.004237j
[2025-08-07 17:35:22] [Iter 263/1050] R1[112/300], Temp: 0.6938, Energy: -55.353632+0.004591j
[2025-08-07 17:35:26] [Iter 264/1050] R1[113/300], Temp: 0.6889, Energy: -55.375448+0.003814j
[2025-08-07 17:35:30] [Iter 265/1050] R1[114/300], Temp: 0.6841, Energy: -55.434110+0.004302j
[2025-08-07 17:35:34] [Iter 266/1050] R1[115/300], Temp: 0.6792, Energy: -55.339927+0.002506j
[2025-08-07 17:35:38] [Iter 267/1050] R1[116/300], Temp: 0.6743, Energy: -55.429237-0.003505j
[2025-08-07 17:35:42] [Iter 268/1050] R1[117/300], Temp: 0.6694, Energy: -55.303691+0.001836j
[2025-08-07 17:35:47] [Iter 269/1050] R1[118/300], Temp: 0.6644, Energy: -55.443761-0.002831j
[2025-08-07 17:35:51] [Iter 270/1050] R1[119/300], Temp: 0.6595, Energy: -55.425981-0.008502j
[2025-08-07 17:35:55] [Iter 271/1050] R1[120/300], Temp: 0.6545, Energy: -55.581800-0.000062j
[2025-08-07 17:35:59] [Iter 272/1050] R1[121/300], Temp: 0.6495, Energy: -55.428028-0.001866j
[2025-08-07 17:36:03] [Iter 273/1050] R1[122/300], Temp: 0.6445, Energy: -55.459578-0.005690j
[2025-08-07 17:36:07] [Iter 274/1050] R1[123/300], Temp: 0.6395, Energy: -55.374194+0.002696j
[2025-08-07 17:36:12] [Iter 275/1050] R1[124/300], Temp: 0.6345, Energy: -55.294654-0.001468j
[2025-08-07 17:36:16] [Iter 276/1050] R1[125/300], Temp: 0.6294, Energy: -55.301504+0.001269j
[2025-08-07 17:36:20] [Iter 277/1050] R1[126/300], Temp: 0.6243, Energy: -55.355219+0.006048j
[2025-08-07 17:36:24] [Iter 278/1050] R1[127/300], Temp: 0.6193, Energy: -55.244693+0.002603j
[2025-08-07 17:36:28] [Iter 279/1050] R1[128/300], Temp: 0.6142, Energy: -55.320041+0.002502j
[2025-08-07 17:36:32] [Iter 280/1050] R1[129/300], Temp: 0.6091, Energy: -55.322210+0.001856j
[2025-08-07 17:36:36] [Iter 281/1050] R1[130/300], Temp: 0.6040, Energy: -55.452516-0.000246j
[2025-08-07 17:36:41] [Iter 282/1050] R1[131/300], Temp: 0.5988, Energy: -55.397679+0.003233j
[2025-08-07 17:36:45] [Iter 283/1050] R1[132/300], Temp: 0.5937, Energy: -55.420607-0.000793j
[2025-08-07 17:36:49] [Iter 284/1050] R1[133/300], Temp: 0.5885, Energy: -55.530555-0.004457j
[2025-08-07 17:36:53] [Iter 285/1050] R1[134/300], Temp: 0.5834, Energy: -55.396094-0.003607j
[2025-08-07 17:36:57] [Iter 286/1050] R1[135/300], Temp: 0.5782, Energy: -55.459227-0.001366j
[2025-08-07 17:37:01] [Iter 287/1050] R1[136/300], Temp: 0.5730, Energy: -55.404182+0.000850j
[2025-08-07 17:37:05] [Iter 288/1050] R1[137/300], Temp: 0.5679, Energy: -55.472140+0.001391j
[2025-08-07 17:37:10] [Iter 289/1050] R1[138/300], Temp: 0.5627, Energy: -55.354832+0.000813j
[2025-08-07 17:37:14] [Iter 290/1050] R1[139/300], Temp: 0.5575, Energy: -55.361474-0.003697j
[2025-08-07 17:37:18] [Iter 291/1050] R1[140/300], Temp: 0.5523, Energy: -55.414365-0.002155j
[2025-08-07 17:37:22] [Iter 292/1050] R1[141/300], Temp: 0.5471, Energy: -55.459993-0.004152j
[2025-08-07 17:37:26] [Iter 293/1050] R1[142/300], Temp: 0.5418, Energy: -55.405071-0.001600j
[2025-08-07 17:37:30] [Iter 294/1050] R1[143/300], Temp: 0.5366, Energy: -55.416154-0.000254j
[2025-08-07 17:37:35] [Iter 295/1050] R1[144/300], Temp: 0.5314, Energy: -55.380886+0.002450j
[2025-08-07 17:37:39] [Iter 296/1050] R1[145/300], Temp: 0.5262, Energy: -55.315072+0.005521j
[2025-08-07 17:37:43] [Iter 297/1050] R1[146/300], Temp: 0.5209, Energy: -55.505675+0.000612j
[2025-08-07 17:37:47] [Iter 298/1050] R1[147/300], Temp: 0.5157, Energy: -55.477200+0.003443j
[2025-08-07 17:37:51] [Iter 299/1050] R1[148/300], Temp: 0.5105, Energy: -55.550357+0.002794j
[2025-08-07 17:37:55] [Iter 300/1050] R1[149/300], Temp: 0.5052, Energy: -55.478909-0.001098j
[2025-08-07 17:37:55] ✓ Checkpoint saved: checkpoint_iter_000300.pkl
[2025-08-07 17:37:59] [Iter 301/1050] R1[150/300], Temp: 0.5000, Energy: -55.372368-0.002336j
[2025-08-07 17:38:04] [Iter 302/1050] R1[151/300], Temp: 0.4948, Energy: -55.309892+0.001030j
[2025-08-07 17:38:08] [Iter 303/1050] R1[152/300], Temp: 0.4895, Energy: -55.445987-0.002994j
[2025-08-07 17:38:12] [Iter 304/1050] R1[153/300], Temp: 0.4843, Energy: -55.417061-0.001649j
[2025-08-07 17:38:16] [Iter 305/1050] R1[154/300], Temp: 0.4791, Energy: -55.403914+0.002837j
[2025-08-07 17:38:20] [Iter 306/1050] R1[155/300], Temp: 0.4738, Energy: -55.396303-0.001633j
[2025-08-07 17:38:24] [Iter 307/1050] R1[156/300], Temp: 0.4686, Energy: -55.463932-0.003075j
[2025-08-07 17:38:29] [Iter 308/1050] R1[157/300], Temp: 0.4634, Energy: -55.472595-0.009839j
[2025-08-07 17:38:33] [Iter 309/1050] R1[158/300], Temp: 0.4582, Energy: -55.449657-0.000810j
[2025-08-07 17:38:37] [Iter 310/1050] R1[159/300], Temp: 0.4529, Energy: -55.441282+0.001323j
[2025-08-07 17:38:41] [Iter 311/1050] R1[160/300], Temp: 0.4477, Energy: -55.519742+0.000651j
[2025-08-07 17:38:45] [Iter 312/1050] R1[161/300], Temp: 0.4425, Energy: -55.445060+0.001129j
[2025-08-07 17:38:49] [Iter 313/1050] R1[162/300], Temp: 0.4373, Energy: -55.381980+0.002285j
[2025-08-07 17:38:53] [Iter 314/1050] R1[163/300], Temp: 0.4321, Energy: -55.368941-0.001249j
[2025-08-07 17:38:58] [Iter 315/1050] R1[164/300], Temp: 0.4270, Energy: -55.247273-0.000551j
[2025-08-07 17:39:02] [Iter 316/1050] R1[165/300], Temp: 0.4218, Energy: -55.300198+0.000326j
[2025-08-07 17:39:06] [Iter 317/1050] R1[166/300], Temp: 0.4166, Energy: -55.271569-0.000396j
[2025-08-07 17:39:10] [Iter 318/1050] R1[167/300], Temp: 0.4115, Energy: -55.340208+0.008118j
[2025-08-07 17:39:14] [Iter 319/1050] R1[168/300], Temp: 0.4063, Energy: -55.427468+0.001100j
[2025-08-07 17:39:18] [Iter 320/1050] R1[169/300], Temp: 0.4012, Energy: -55.346329-0.002909j
[2025-08-07 17:39:22] [Iter 321/1050] R1[170/300], Temp: 0.3960, Energy: -55.404737-0.005243j
[2025-08-07 17:39:27] [Iter 322/1050] R1[171/300], Temp: 0.3909, Energy: -55.326651-0.003180j
[2025-08-07 17:39:31] [Iter 323/1050] R1[172/300], Temp: 0.3858, Energy: -55.334908+0.002713j
[2025-08-07 17:39:35] [Iter 324/1050] R1[173/300], Temp: 0.3807, Energy: -55.230749+0.004708j
[2025-08-07 17:39:39] [Iter 325/1050] R1[174/300], Temp: 0.3757, Energy: -55.205262-0.004488j
[2025-08-07 17:39:43] [Iter 326/1050] R1[175/300], Temp: 0.3706, Energy: -55.290723+0.001977j
[2025-08-07 17:39:47] [Iter 327/1050] R1[176/300], Temp: 0.3655, Energy: -55.349780-0.001574j
[2025-08-07 17:39:51] [Iter 328/1050] R1[177/300], Temp: 0.3605, Energy: -55.359270-0.004397j
[2025-08-07 17:39:56] [Iter 329/1050] R1[178/300], Temp: 0.3555, Energy: -55.413425+0.001933j
[2025-08-07 17:40:00] [Iter 330/1050] R1[179/300], Temp: 0.3505, Energy: -55.426522-0.000067j
[2025-08-07 17:40:04] [Iter 331/1050] R1[180/300], Temp: 0.3455, Energy: -55.447274+0.001587j
[2025-08-07 17:40:08] [Iter 332/1050] R1[181/300], Temp: 0.3405, Energy: -55.386110-0.006554j
[2025-08-07 17:40:12] [Iter 333/1050] R1[182/300], Temp: 0.3356, Energy: -55.320030-0.008964j
[2025-08-07 17:40:16] [Iter 334/1050] R1[183/300], Temp: 0.3306, Energy: -55.311333+0.003184j
[2025-08-07 17:40:20] [Iter 335/1050] R1[184/300], Temp: 0.3257, Energy: -55.387295+0.000033j
[2025-08-07 17:40:25] [Iter 336/1050] R1[185/300], Temp: 0.3208, Energy: -55.393463-0.003490j
[2025-08-07 17:40:29] [Iter 337/1050] R1[186/300], Temp: 0.3159, Energy: -55.397575-0.006819j
[2025-08-07 17:40:33] [Iter 338/1050] R1[187/300], Temp: 0.3111, Energy: -55.402280-0.001633j
[2025-08-07 17:40:37] [Iter 339/1050] R1[188/300], Temp: 0.3062, Energy: -55.363994+0.000473j
[2025-08-07 17:40:41] [Iter 340/1050] R1[189/300], Temp: 0.3014, Energy: -55.409944-0.003289j
[2025-08-07 17:40:45] [Iter 341/1050] R1[190/300], Temp: 0.2966, Energy: -55.403593+0.000094j
[2025-08-07 17:40:50] [Iter 342/1050] R1[191/300], Temp: 0.2919, Energy: -55.426371-0.003781j
[2025-08-07 17:40:54] [Iter 343/1050] R1[192/300], Temp: 0.2871, Energy: -55.389354+0.002320j
[2025-08-07 17:40:58] [Iter 344/1050] R1[193/300], Temp: 0.2824, Energy: -55.381071+0.002093j
[2025-08-07 17:41:02] [Iter 345/1050] R1[194/300], Temp: 0.2777, Energy: -55.417796-0.003220j
[2025-08-07 17:41:06] [Iter 346/1050] R1[195/300], Temp: 0.2730, Energy: -55.416959+0.001943j
[2025-08-07 17:41:10] [Iter 347/1050] R1[196/300], Temp: 0.2684, Energy: -55.386239-0.000816j
[2025-08-07 17:41:14] [Iter 348/1050] R1[197/300], Temp: 0.2637, Energy: -55.457562+0.000901j
[2025-08-07 17:41:19] [Iter 349/1050] R1[198/300], Temp: 0.2591, Energy: -55.290473+0.002151j
[2025-08-07 17:41:23] [Iter 350/1050] R1[199/300], Temp: 0.2545, Energy: -55.375839+0.000645j
[2025-08-07 17:41:27] [Iter 351/1050] R1[200/300], Temp: 0.2500, Energy: -55.479789-0.003217j
[2025-08-07 17:41:31] [Iter 352/1050] R1[201/300], Temp: 0.2455, Energy: -55.361380-0.000119j
[2025-08-07 17:41:35] [Iter 353/1050] R1[202/300], Temp: 0.2410, Energy: -55.324111-0.003435j
[2025-08-07 17:41:39] [Iter 354/1050] R1[203/300], Temp: 0.2365, Energy: -55.346879-0.001042j
[2025-08-07 17:41:44] [Iter 355/1050] R1[204/300], Temp: 0.2321, Energy: -55.429844-0.001137j
[2025-08-07 17:41:48] [Iter 356/1050] R1[205/300], Temp: 0.2277, Energy: -55.332262+0.001073j
[2025-08-07 17:41:52] [Iter 357/1050] R1[206/300], Temp: 0.2233, Energy: -55.422734+0.001683j
[2025-08-07 17:41:56] [Iter 358/1050] R1[207/300], Temp: 0.2190, Energy: -55.419453+0.001998j
[2025-08-07 17:42:00] [Iter 359/1050] R1[208/300], Temp: 0.2146, Energy: -55.360026+0.004124j
[2025-08-07 17:42:04] [Iter 360/1050] R1[209/300], Temp: 0.2104, Energy: -55.346215-0.000868j
[2025-08-07 17:42:08] [Iter 361/1050] R1[210/300], Temp: 0.2061, Energy: -55.434687+0.003232j
[2025-08-07 17:42:13] [Iter 362/1050] R1[211/300], Temp: 0.2019, Energy: -55.393060-0.002312j
[2025-08-07 17:42:17] [Iter 363/1050] R1[212/300], Temp: 0.1977, Energy: -55.467056+0.001187j
[2025-08-07 17:42:21] [Iter 364/1050] R1[213/300], Temp: 0.1935, Energy: -55.482172+0.000938j
[2025-08-07 17:42:25] [Iter 365/1050] R1[214/300], Temp: 0.1894, Energy: -55.505145-0.000836j
[2025-08-07 17:42:29] [Iter 366/1050] R1[215/300], Temp: 0.1853, Energy: -55.431698-0.003333j
[2025-08-07 17:42:33] [Iter 367/1050] R1[216/300], Temp: 0.1813, Energy: -55.375400-0.002987j
[2025-08-07 17:42:37] [Iter 368/1050] R1[217/300], Temp: 0.1773, Energy: -55.352254-0.000722j
[2025-08-07 17:42:42] [Iter 369/1050] R1[218/300], Temp: 0.1733, Energy: -55.539546+0.000099j
[2025-08-07 17:42:46] [Iter 370/1050] R1[219/300], Temp: 0.1693, Energy: -55.394947-0.000775j
[2025-08-07 17:42:50] [Iter 371/1050] R1[220/300], Temp: 0.1654, Energy: -55.382080-0.000587j
[2025-08-07 17:42:54] [Iter 372/1050] R1[221/300], Temp: 0.1616, Energy: -55.321675+0.001368j
[2025-08-07 17:42:58] [Iter 373/1050] R1[222/300], Temp: 0.1577, Energy: -55.279534+0.002725j
[2025-08-07 17:43:02] [Iter 374/1050] R1[223/300], Temp: 0.1539, Energy: -55.346706+0.000743j
[2025-08-07 17:43:06] [Iter 375/1050] R1[224/300], Temp: 0.1502, Energy: -55.350647-0.001612j
[2025-08-07 17:43:11] [Iter 376/1050] R1[225/300], Temp: 0.1464, Energy: -55.350939+0.000173j
[2025-08-07 17:43:15] [Iter 377/1050] R1[226/300], Temp: 0.1428, Energy: -55.299585-0.000074j
[2025-08-07 17:43:19] [Iter 378/1050] R1[227/300], Temp: 0.1391, Energy: -55.391450+0.002438j
[2025-08-07 17:43:23] [Iter 379/1050] R1[228/300], Temp: 0.1355, Energy: -55.474934+0.001235j
[2025-08-07 17:43:27] [Iter 380/1050] R1[229/300], Temp: 0.1320, Energy: -55.345202+0.000904j
[2025-08-07 17:43:31] [Iter 381/1050] R1[230/300], Temp: 0.1284, Energy: -55.349056-0.000690j
[2025-08-07 17:43:35] [Iter 382/1050] R1[231/300], Temp: 0.1249, Energy: -55.328374+0.000564j
[2025-08-07 17:43:40] [Iter 383/1050] R1[232/300], Temp: 0.1215, Energy: -55.267176+0.001591j
[2025-08-07 17:43:44] [Iter 384/1050] R1[233/300], Temp: 0.1181, Energy: -55.354939+0.002414j
[2025-08-07 17:43:48] [Iter 385/1050] R1[234/300], Temp: 0.1147, Energy: -55.322986+0.004608j
[2025-08-07 17:43:52] [Iter 386/1050] R1[235/300], Temp: 0.1114, Energy: -55.367796+0.002834j
[2025-08-07 17:43:56] [Iter 387/1050] R1[236/300], Temp: 0.1082, Energy: -55.478288-0.000357j
[2025-08-07 17:44:00] [Iter 388/1050] R1[237/300], Temp: 0.1049, Energy: -55.393344+0.006339j
[2025-08-07 17:44:05] [Iter 389/1050] R1[238/300], Temp: 0.1017, Energy: -55.433906+0.003865j
[2025-08-07 17:44:09] [Iter 390/1050] R1[239/300], Temp: 0.0986, Energy: -55.275597+0.004805j
[2025-08-07 17:44:13] [Iter 391/1050] R1[240/300], Temp: 0.0955, Energy: -55.413814-0.000900j
[2025-08-07 17:44:17] [Iter 392/1050] R1[241/300], Temp: 0.0924, Energy: -55.395131+0.001377j
[2025-08-07 17:44:21] [Iter 393/1050] R1[242/300], Temp: 0.0894, Energy: -55.369818+0.001842j
[2025-08-07 17:44:25] [Iter 394/1050] R1[243/300], Temp: 0.0865, Energy: -55.377742+0.004781j
[2025-08-07 17:44:29] [Iter 395/1050] R1[244/300], Temp: 0.0835, Energy: -55.490155+0.001604j
[2025-08-07 17:44:34] [Iter 396/1050] R1[245/300], Temp: 0.0807, Energy: -55.440386-0.001420j
[2025-08-07 17:44:38] [Iter 397/1050] R1[246/300], Temp: 0.0778, Energy: -55.507098-0.004496j
[2025-08-07 17:44:42] [Iter 398/1050] R1[247/300], Temp: 0.0751, Energy: -55.412887-0.003319j
[2025-08-07 17:44:46] [Iter 399/1050] R1[248/300], Temp: 0.0723, Energy: -55.410533-0.004463j
[2025-08-07 17:44:50] [Iter 400/1050] R1[249/300], Temp: 0.0696, Energy: -55.425175-0.005152j
[2025-08-07 17:44:50] ✓ Checkpoint saved: checkpoint_iter_000400.pkl
[2025-08-07 17:44:54] [Iter 401/1050] R1[250/300], Temp: 0.0670, Energy: -55.422064-0.000834j
[2025-08-07 17:44:59] [Iter 402/1050] R1[251/300], Temp: 0.0644, Energy: -55.463178+0.003978j
[2025-08-07 17:45:03] [Iter 403/1050] R1[252/300], Temp: 0.0618, Energy: -55.413107-0.002210j
[2025-08-07 17:45:07] [Iter 404/1050] R1[253/300], Temp: 0.0593, Energy: -55.357622+0.003865j
[2025-08-07 17:45:11] [Iter 405/1050] R1[254/300], Temp: 0.0569, Energy: -55.486333-0.002559j
[2025-08-07 17:45:15] [Iter 406/1050] R1[255/300], Temp: 0.0545, Energy: -55.415150+0.001436j
[2025-08-07 17:45:19] [Iter 407/1050] R1[256/300], Temp: 0.0521, Energy: -55.477975-0.000342j
[2025-08-07 17:45:23] [Iter 408/1050] R1[257/300], Temp: 0.0498, Energy: -55.405789-0.000586j
[2025-08-07 17:45:28] [Iter 409/1050] R1[258/300], Temp: 0.0476, Energy: -55.479364+0.002986j
[2025-08-07 17:45:32] [Iter 410/1050] R1[259/300], Temp: 0.0454, Energy: -55.440853+0.001588j
[2025-08-07 17:45:36] [Iter 411/1050] R1[260/300], Temp: 0.0432, Energy: -55.352418+0.000779j
[2025-08-07 17:45:40] [Iter 412/1050] R1[261/300], Temp: 0.0411, Energy: -55.493613-0.001468j
[2025-08-07 17:45:44] [Iter 413/1050] R1[262/300], Temp: 0.0391, Energy: -55.553879+0.001210j
[2025-08-07 17:45:48] [Iter 414/1050] R1[263/300], Temp: 0.0371, Energy: -55.505303-0.005257j
[2025-08-07 17:45:52] [Iter 415/1050] R1[264/300], Temp: 0.0351, Energy: -55.491326-0.000189j
[2025-08-07 17:45:57] [Iter 416/1050] R1[265/300], Temp: 0.0332, Energy: -55.572439-0.001145j
[2025-08-07 17:46:01] [Iter 417/1050] R1[266/300], Temp: 0.0314, Energy: -55.538383+0.003318j
[2025-08-07 17:46:05] [Iter 418/1050] R1[267/300], Temp: 0.0296, Energy: -55.572591+0.005425j
[2025-08-07 17:46:09] [Iter 419/1050] R1[268/300], Temp: 0.0278, Energy: -55.602546+0.000184j
[2025-08-07 17:46:13] [Iter 420/1050] R1[269/300], Temp: 0.0261, Energy: -55.580269+0.000625j
[2025-08-07 17:46:17] [Iter 421/1050] R1[270/300], Temp: 0.0245, Energy: -55.388632-0.000839j
[2025-08-07 17:46:21] [Iter 422/1050] R1[271/300], Temp: 0.0229, Energy: -55.413643-0.001646j
[2025-08-07 17:46:26] [Iter 423/1050] R1[272/300], Temp: 0.0213, Energy: -55.487690+0.001689j
[2025-08-07 17:46:30] [Iter 424/1050] R1[273/300], Temp: 0.0199, Energy: -55.458791+0.001424j
[2025-08-07 17:46:34] [Iter 425/1050] R1[274/300], Temp: 0.0184, Energy: -55.431013-0.001162j
[2025-08-07 17:46:38] [Iter 426/1050] R1[275/300], Temp: 0.0170, Energy: -55.295143-0.000943j
[2025-08-07 17:46:42] [Iter 427/1050] R1[276/300], Temp: 0.0157, Energy: -55.392395-0.002088j
[2025-08-07 17:46:46] [Iter 428/1050] R1[277/300], Temp: 0.0144, Energy: -55.360253+0.000138j
[2025-08-07 17:46:51] [Iter 429/1050] R1[278/300], Temp: 0.0132, Energy: -55.346523-0.002742j
[2025-08-07 17:46:55] [Iter 430/1050] R1[279/300], Temp: 0.0120, Energy: -55.361809-0.003375j
[2025-08-07 17:46:59] [Iter 431/1050] R1[280/300], Temp: 0.0109, Energy: -55.403212-0.007290j
[2025-08-07 17:47:03] [Iter 432/1050] R1[281/300], Temp: 0.0099, Energy: -55.290873+0.002862j
[2025-08-07 17:47:07] [Iter 433/1050] R1[282/300], Temp: 0.0089, Energy: -55.361324+0.000671j
[2025-08-07 17:47:11] [Iter 434/1050] R1[283/300], Temp: 0.0079, Energy: -55.401463-0.001611j
[2025-08-07 17:47:15] [Iter 435/1050] R1[284/300], Temp: 0.0070, Energy: -55.410866-0.005281j
[2025-08-07 17:47:20] [Iter 436/1050] R1[285/300], Temp: 0.0062, Energy: -55.420671-0.003753j
[2025-08-07 17:47:24] [Iter 437/1050] R1[286/300], Temp: 0.0054, Energy: -55.377751-0.002923j
[2025-08-07 17:47:28] [Iter 438/1050] R1[287/300], Temp: 0.0046, Energy: -55.432978-0.004063j
[2025-08-07 17:47:32] [Iter 439/1050] R1[288/300], Temp: 0.0039, Energy: -55.378071+0.008448j
[2025-08-07 17:47:36] [Iter 440/1050] R1[289/300], Temp: 0.0033, Energy: -55.345188+0.000006j
[2025-08-07 17:47:40] [Iter 441/1050] R1[290/300], Temp: 0.0027, Energy: -55.221270+0.003894j
[2025-08-07 17:47:44] [Iter 442/1050] R1[291/300], Temp: 0.0022, Energy: -55.292146+0.006187j
[2025-08-07 17:47:49] [Iter 443/1050] R1[292/300], Temp: 0.0018, Energy: -55.213336+0.002562j
[2025-08-07 17:47:53] [Iter 444/1050] R1[293/300], Temp: 0.0013, Energy: -55.342021-0.003758j
[2025-08-07 17:47:57] [Iter 445/1050] R1[294/300], Temp: 0.0010, Energy: -55.310148-0.004752j
[2025-08-07 17:48:01] [Iter 446/1050] R1[295/300], Temp: 0.0007, Energy: -55.238396-0.002692j
[2025-08-07 17:48:05] [Iter 447/1050] R1[296/300], Temp: 0.0004, Energy: -55.266615+0.002268j
[2025-08-07 17:48:09] [Iter 448/1050] R1[297/300], Temp: 0.0002, Energy: -55.290234+0.000939j
[2025-08-07 17:48:14] [Iter 449/1050] R1[298/300], Temp: 0.0001, Energy: -55.410833-0.003381j
[2025-08-07 17:48:18] [Iter 450/1050] R1[299/300], Temp: 0.0000, Energy: -55.452761-0.003849j
[2025-08-07 17:48:18] RESTART #2 | Period: 600
[2025-08-07 17:48:22] [Iter 451/1050] R2[0/600], Temp: 1.0000, Energy: -55.454080+0.000526j
[2025-08-07 17:48:26] [Iter 452/1050] R2[1/600], Temp: 1.0000, Energy: -55.545573-0.003709j
[2025-08-07 17:48:30] [Iter 453/1050] R2[2/600], Temp: 1.0000, Energy: -55.482279+0.001026j
[2025-08-07 17:48:34] [Iter 454/1050] R2[3/600], Temp: 0.9999, Energy: -55.458566+0.000313j
[2025-08-07 17:48:39] [Iter 455/1050] R2[4/600], Temp: 0.9999, Energy: -55.352042-0.001976j
[2025-08-07 17:48:43] [Iter 456/1050] R2[5/600], Temp: 0.9998, Energy: -55.375114+0.002696j
[2025-08-07 17:48:47] [Iter 457/1050] R2[6/600], Temp: 0.9998, Energy: -55.234401-0.000215j
[2025-08-07 17:48:51] [Iter 458/1050] R2[7/600], Temp: 0.9997, Energy: -55.304041-0.005038j
[2025-08-07 17:48:55] [Iter 459/1050] R2[8/600], Temp: 0.9996, Energy: -55.510856-0.001542j
[2025-08-07 17:48:59] [Iter 460/1050] R2[9/600], Temp: 0.9994, Energy: -55.433448-0.000927j
[2025-08-07 17:49:03] [Iter 461/1050] R2[10/600], Temp: 0.9993, Energy: -55.431943-0.003105j
[2025-08-07 17:49:08] [Iter 462/1050] R2[11/600], Temp: 0.9992, Energy: -55.343883-0.001309j
[2025-08-07 17:49:12] [Iter 463/1050] R2[12/600], Temp: 0.9990, Energy: -55.311134+0.000984j
[2025-08-07 17:49:16] [Iter 464/1050] R2[13/600], Temp: 0.9988, Energy: -55.334931-0.004701j
[2025-08-07 17:49:20] [Iter 465/1050] R2[14/600], Temp: 0.9987, Energy: -55.336848-0.002480j
[2025-08-07 17:49:24] [Iter 466/1050] R2[15/600], Temp: 0.9985, Energy: -55.306317+0.000468j
[2025-08-07 17:49:28] [Iter 467/1050] R2[16/600], Temp: 0.9982, Energy: -55.396790+0.000463j
[2025-08-07 17:49:33] [Iter 468/1050] R2[17/600], Temp: 0.9980, Energy: -55.180517+0.002317j
[2025-08-07 17:49:37] [Iter 469/1050] R2[18/600], Temp: 0.9978, Energy: -55.370207+0.001764j
[2025-08-07 17:49:41] [Iter 470/1050] R2[19/600], Temp: 0.9975, Energy: -55.317096-0.001546j
[2025-08-07 17:49:45] [Iter 471/1050] R2[20/600], Temp: 0.9973, Energy: -55.374411+0.004181j
[2025-08-07 17:49:49] [Iter 472/1050] R2[21/600], Temp: 0.9970, Energy: -55.328609-0.004428j
[2025-08-07 17:49:53] [Iter 473/1050] R2[22/600], Temp: 0.9967, Energy: -55.353699+0.002942j
[2025-08-07 17:49:57] [Iter 474/1050] R2[23/600], Temp: 0.9964, Energy: -55.344409-0.001400j
[2025-08-07 17:50:02] [Iter 475/1050] R2[24/600], Temp: 0.9961, Energy: -55.278167+0.003054j
[2025-08-07 17:50:06] [Iter 476/1050] R2[25/600], Temp: 0.9957, Energy: -55.181867+0.006703j
[2025-08-07 17:50:10] [Iter 477/1050] R2[26/600], Temp: 0.9954, Energy: -55.301998+0.002460j
[2025-08-07 17:50:14] [Iter 478/1050] R2[27/600], Temp: 0.9950, Energy: -55.286282-0.004069j
[2025-08-07 17:50:18] [Iter 479/1050] R2[28/600], Temp: 0.9946, Energy: -55.260078-0.002924j
[2025-08-07 17:50:22] [Iter 480/1050] R2[29/600], Temp: 0.9942, Energy: -55.325321+0.002280j
[2025-08-07 17:50:27] [Iter 481/1050] R2[30/600], Temp: 0.9938, Energy: -55.525629+0.004510j
[2025-08-07 17:50:31] [Iter 482/1050] R2[31/600], Temp: 0.9934, Energy: -55.366641-0.003162j
[2025-08-07 17:50:35] [Iter 483/1050] R2[32/600], Temp: 0.9930, Energy: -55.438283-0.006376j
[2025-08-07 17:50:39] [Iter 484/1050] R2[33/600], Temp: 0.9926, Energy: -55.471390+0.000132j
[2025-08-07 17:50:43] [Iter 485/1050] R2[34/600], Temp: 0.9921, Energy: -55.403121+0.005748j
[2025-08-07 17:50:47] [Iter 486/1050] R2[35/600], Temp: 0.9916, Energy: -55.472785+0.000659j
[2025-08-07 17:50:51] [Iter 487/1050] R2[36/600], Temp: 0.9911, Energy: -55.461850+0.002462j
[2025-08-07 17:50:56] [Iter 488/1050] R2[37/600], Temp: 0.9906, Energy: -55.607753-0.006104j
[2025-08-07 17:51:00] [Iter 489/1050] R2[38/600], Temp: 0.9901, Energy: -55.579937-0.003832j
[2025-08-07 17:51:04] [Iter 490/1050] R2[39/600], Temp: 0.9896, Energy: -55.565453-0.003578j
[2025-08-07 17:51:08] [Iter 491/1050] R2[40/600], Temp: 0.9891, Energy: -55.490435+0.002914j
[2025-08-07 17:51:12] [Iter 492/1050] R2[41/600], Temp: 0.9885, Energy: -55.330044+0.000777j
[2025-08-07 17:51:16] [Iter 493/1050] R2[42/600], Temp: 0.9880, Energy: -55.412465+0.001056j
[2025-08-07 17:51:21] [Iter 494/1050] R2[43/600], Temp: 0.9874, Energy: -55.517620+0.001484j
[2025-08-07 17:51:25] [Iter 495/1050] R2[44/600], Temp: 0.9868, Energy: -55.480021+0.000382j
[2025-08-07 17:51:29] [Iter 496/1050] R2[45/600], Temp: 0.9862, Energy: -55.472110+0.003340j
[2025-08-07 17:51:33] [Iter 497/1050] R2[46/600], Temp: 0.9856, Energy: -55.436031-0.001684j
[2025-08-07 17:51:37] [Iter 498/1050] R2[47/600], Temp: 0.9849, Energy: -55.402535-0.001513j
[2025-08-07 17:51:41] [Iter 499/1050] R2[48/600], Temp: 0.9843, Energy: -55.476540-0.003518j
[2025-08-07 17:51:45] [Iter 500/1050] R2[49/600], Temp: 0.9836, Energy: -55.389257+0.003220j
[2025-08-07 17:51:45] ✓ Checkpoint saved: checkpoint_iter_000500.pkl
[2025-08-07 17:51:50] [Iter 501/1050] R2[50/600], Temp: 0.9830, Energy: -55.365700+0.001920j
[2025-08-07 17:51:54] [Iter 502/1050] R2[51/600], Temp: 0.9823, Energy: -55.391156-0.003361j
[2025-08-07 17:51:58] [Iter 503/1050] R2[52/600], Temp: 0.9816, Energy: -55.392677+0.003114j
[2025-08-07 17:52:02] [Iter 504/1050] R2[53/600], Temp: 0.9809, Energy: -55.318488+0.006255j
[2025-08-07 17:52:06] [Iter 505/1050] R2[54/600], Temp: 0.9801, Energy: -55.332086+0.001204j
[2025-08-07 17:52:10] [Iter 506/1050] R2[55/600], Temp: 0.9794, Energy: -55.458314+0.002177j
[2025-08-07 17:52:15] [Iter 507/1050] R2[56/600], Temp: 0.9787, Energy: -55.455350-0.001293j
[2025-08-07 17:52:19] [Iter 508/1050] R2[57/600], Temp: 0.9779, Energy: -55.516861-0.001754j
[2025-08-07 17:52:23] [Iter 509/1050] R2[58/600], Temp: 0.9771, Energy: -55.472008-0.002682j
[2025-08-07 17:52:27] [Iter 510/1050] R2[59/600], Temp: 0.9763, Energy: -55.405651+0.000858j
[2025-08-07 17:52:31] [Iter 511/1050] R2[60/600], Temp: 0.9755, Energy: -55.548263+0.000441j
[2025-08-07 17:52:35] [Iter 512/1050] R2[61/600], Temp: 0.9747, Energy: -55.470810-0.003209j
[2025-08-07 17:52:39] [Iter 513/1050] R2[62/600], Temp: 0.9739, Energy: -55.413313+0.006791j
[2025-08-07 17:52:44] [Iter 514/1050] R2[63/600], Temp: 0.9730, Energy: -55.355525-0.000495j
[2025-08-07 17:52:48] [Iter 515/1050] R2[64/600], Temp: 0.9722, Energy: -55.459922-0.003985j
[2025-08-07 17:52:52] [Iter 516/1050] R2[65/600], Temp: 0.9713, Energy: -55.418187-0.002365j
[2025-08-07 17:52:56] [Iter 517/1050] R2[66/600], Temp: 0.9704, Energy: -55.629704-0.004447j
[2025-08-07 17:53:00] [Iter 518/1050] R2[67/600], Temp: 0.9695, Energy: -55.499429-0.006512j
[2025-08-07 17:53:04] [Iter 519/1050] R2[68/600], Temp: 0.9686, Energy: -55.566377-0.001718j
[2025-08-07 17:53:09] [Iter 520/1050] R2[69/600], Temp: 0.9677, Energy: -55.548167+0.001695j
[2025-08-07 17:53:13] [Iter 521/1050] R2[70/600], Temp: 0.9668, Energy: -55.535970+0.003960j
[2025-08-07 17:53:17] [Iter 522/1050] R2[71/600], Temp: 0.9658, Energy: -55.394614+0.002117j
[2025-08-07 17:53:21] [Iter 523/1050] R2[72/600], Temp: 0.9649, Energy: -55.516487-0.001507j
[2025-08-07 17:53:25] [Iter 524/1050] R2[73/600], Temp: 0.9639, Energy: -55.462893-0.002110j
[2025-08-07 17:53:29] [Iter 525/1050] R2[74/600], Temp: 0.9629, Energy: -55.331438+0.004768j
[2025-08-07 17:53:33] [Iter 526/1050] R2[75/600], Temp: 0.9619, Energy: -55.460869-0.003542j
[2025-08-07 17:53:38] [Iter 527/1050] R2[76/600], Temp: 0.9609, Energy: -55.389718-0.003531j
[2025-08-07 17:53:42] [Iter 528/1050] R2[77/600], Temp: 0.9599, Energy: -55.346993+0.001312j
[2025-08-07 17:53:46] [Iter 529/1050] R2[78/600], Temp: 0.9589, Energy: -55.428156+0.001156j
[2025-08-07 17:53:50] [Iter 530/1050] R2[79/600], Temp: 0.9578, Energy: -55.396979+0.002319j
[2025-08-07 17:53:54] [Iter 531/1050] R2[80/600], Temp: 0.9568, Energy: -55.423044-0.004761j
[2025-08-07 17:53:58] [Iter 532/1050] R2[81/600], Temp: 0.9557, Energy: -55.431378+0.006994j
[2025-08-07 17:54:02] [Iter 533/1050] R2[82/600], Temp: 0.9546, Energy: -55.450679-0.000436j
[2025-08-07 17:54:07] [Iter 534/1050] R2[83/600], Temp: 0.9535, Energy: -55.503144+0.000590j
[2025-08-07 17:54:11] [Iter 535/1050] R2[84/600], Temp: 0.9524, Energy: -55.443966+0.002568j
[2025-08-07 17:54:15] [Iter 536/1050] R2[85/600], Temp: 0.9513, Energy: -55.370386-0.000794j
[2025-08-07 17:54:19] [Iter 537/1050] R2[86/600], Temp: 0.9502, Energy: -55.335166+0.000417j
[2025-08-07 17:54:23] [Iter 538/1050] R2[87/600], Temp: 0.9490, Energy: -55.329418+0.001193j
[2025-08-07 17:54:27] [Iter 539/1050] R2[88/600], Temp: 0.9479, Energy: -55.508174-0.003671j
[2025-08-07 17:54:32] [Iter 540/1050] R2[89/600], Temp: 0.9467, Energy: -55.348476+0.000192j
[2025-08-07 17:54:36] [Iter 541/1050] R2[90/600], Temp: 0.9455, Energy: -55.371443-0.001927j
[2025-08-07 17:54:40] [Iter 542/1050] R2[91/600], Temp: 0.9443, Energy: -55.327148+0.004398j
[2025-08-07 17:54:44] [Iter 543/1050] R2[92/600], Temp: 0.9431, Energy: -55.329991+0.008575j
[2025-08-07 17:54:48] [Iter 544/1050] R2[93/600], Temp: 0.9419, Energy: -55.372373-0.006594j
[2025-08-07 17:54:52] [Iter 545/1050] R2[94/600], Temp: 0.9407, Energy: -55.254588+0.000281j
[2025-08-07 17:54:56] [Iter 546/1050] R2[95/600], Temp: 0.9394, Energy: -55.325953-0.001748j
[2025-08-07 17:55:01] [Iter 547/1050] R2[96/600], Temp: 0.9382, Energy: -55.436164-0.005957j
[2025-08-07 17:55:05] [Iter 548/1050] R2[97/600], Temp: 0.9369, Energy: -55.376014+0.007629j
[2025-08-07 17:55:09] [Iter 549/1050] R2[98/600], Temp: 0.9356, Energy: -55.374720-0.001803j
[2025-08-07 17:55:13] [Iter 550/1050] R2[99/600], Temp: 0.9343, Energy: -55.347594-0.003414j
[2025-08-07 17:55:17] [Iter 551/1050] R2[100/600], Temp: 0.9330, Energy: -55.372083-0.001327j
[2025-08-07 17:55:21] [Iter 552/1050] R2[101/600], Temp: 0.9317, Energy: -55.448765-0.004526j
[2025-08-07 17:55:25] [Iter 553/1050] R2[102/600], Temp: 0.9304, Energy: -55.387228-0.000940j
[2025-08-07 17:55:30] [Iter 554/1050] R2[103/600], Temp: 0.9290, Energy: -55.484974-0.003837j
[2025-08-07 17:55:34] [Iter 555/1050] R2[104/600], Temp: 0.9277, Energy: -55.319872+0.004690j
[2025-08-07 17:55:38] [Iter 556/1050] R2[105/600], Temp: 0.9263, Energy: -55.412655+0.003843j
[2025-08-07 17:55:42] [Iter 557/1050] R2[106/600], Temp: 0.9249, Energy: -55.329327-0.000786j
[2025-08-07 17:55:46] [Iter 558/1050] R2[107/600], Temp: 0.9236, Energy: -55.391290+0.001812j
[2025-08-07 17:55:50] [Iter 559/1050] R2[108/600], Temp: 0.9222, Energy: -55.374021+0.000626j
[2025-08-07 17:55:55] [Iter 560/1050] R2[109/600], Temp: 0.9208, Energy: -55.384827+0.001295j
[2025-08-07 17:55:59] [Iter 561/1050] R2[110/600], Temp: 0.9193, Energy: -55.285907+0.001408j
[2025-08-07 17:56:03] [Iter 562/1050] R2[111/600], Temp: 0.9179, Energy: -55.457132+0.003224j
[2025-08-07 17:56:07] [Iter 563/1050] R2[112/600], Temp: 0.9165, Energy: -55.442880-0.001515j
[2025-08-07 17:56:11] [Iter 564/1050] R2[113/600], Temp: 0.9150, Energy: -55.552778+0.003337j
[2025-08-07 17:56:15] [Iter 565/1050] R2[114/600], Temp: 0.9135, Energy: -55.430702+0.002820j
[2025-08-07 17:56:19] [Iter 566/1050] R2[115/600], Temp: 0.9121, Energy: -55.393965+0.000462j
[2025-08-07 17:56:24] [Iter 567/1050] R2[116/600], Temp: 0.9106, Energy: -55.496377+0.002283j
[2025-08-07 17:56:28] [Iter 568/1050] R2[117/600], Temp: 0.9091, Energy: -55.387709-0.002474j
[2025-08-07 17:56:32] [Iter 569/1050] R2[118/600], Temp: 0.9076, Energy: -55.367276-0.000517j
[2025-08-07 17:56:36] [Iter 570/1050] R2[119/600], Temp: 0.9060, Energy: -55.452174+0.000523j
[2025-08-07 17:56:40] [Iter 571/1050] R2[120/600], Temp: 0.9045, Energy: -55.386936+0.002634j
[2025-08-07 17:56:44] [Iter 572/1050] R2[121/600], Temp: 0.9030, Energy: -55.508041-0.008801j
[2025-08-07 17:56:48] [Iter 573/1050] R2[122/600], Temp: 0.9014, Energy: -55.442822+0.003531j
[2025-08-07 17:56:53] [Iter 574/1050] R2[123/600], Temp: 0.8998, Energy: -55.568678+0.007659j
[2025-08-07 17:56:57] [Iter 575/1050] R2[124/600], Temp: 0.8983, Energy: -55.526127+0.000542j
[2025-08-07 17:57:01] [Iter 576/1050] R2[125/600], Temp: 0.8967, Energy: -55.488328+0.003542j
[2025-08-07 17:57:05] [Iter 577/1050] R2[126/600], Temp: 0.8951, Energy: -55.469333-0.008309j
[2025-08-07 17:57:09] [Iter 578/1050] R2[127/600], Temp: 0.8935, Energy: -55.461474-0.000662j
[2025-08-07 17:57:13] [Iter 579/1050] R2[128/600], Temp: 0.8918, Energy: -55.453813+0.004881j
[2025-08-07 17:57:18] [Iter 580/1050] R2[129/600], Temp: 0.8902, Energy: -55.455119-0.000891j
[2025-08-07 17:57:22] [Iter 581/1050] R2[130/600], Temp: 0.8886, Energy: -55.444750-0.000356j
[2025-08-07 17:57:26] [Iter 582/1050] R2[131/600], Temp: 0.8869, Energy: -55.329217+0.001036j
[2025-08-07 17:57:30] [Iter 583/1050] R2[132/600], Temp: 0.8853, Energy: -55.406538-0.002238j
[2025-08-07 17:57:34] [Iter 584/1050] R2[133/600], Temp: 0.8836, Energy: -55.318476+0.004460j
[2025-08-07 17:57:38] [Iter 585/1050] R2[134/600], Temp: 0.8819, Energy: -55.321919+0.004083j
[2025-08-07 17:57:42] [Iter 586/1050] R2[135/600], Temp: 0.8802, Energy: -55.238942+0.006674j
[2025-08-07 17:57:47] [Iter 587/1050] R2[136/600], Temp: 0.8785, Energy: -55.245552+0.005118j
[2025-08-07 17:57:51] [Iter 588/1050] R2[137/600], Temp: 0.8768, Energy: -55.391087+0.000071j
[2025-08-07 17:57:55] [Iter 589/1050] R2[138/600], Temp: 0.8751, Energy: -55.390473+0.001609j
[2025-08-07 17:57:59] [Iter 590/1050] R2[139/600], Temp: 0.8733, Energy: -55.455235+0.000416j
[2025-08-07 17:58:03] [Iter 591/1050] R2[140/600], Temp: 0.8716, Energy: -55.338029+0.006270j
[2025-08-07 17:58:07] [Iter 592/1050] R2[141/600], Temp: 0.8698, Energy: -55.383379-0.001991j
[2025-08-07 17:58:12] [Iter 593/1050] R2[142/600], Temp: 0.8680, Energy: -55.409134+0.000599j
[2025-08-07 17:58:16] [Iter 594/1050] R2[143/600], Temp: 0.8663, Energy: -55.391942+0.002745j
[2025-08-07 17:58:20] [Iter 595/1050] R2[144/600], Temp: 0.8645, Energy: -55.481941-0.004285j
[2025-08-07 17:58:24] [Iter 596/1050] R2[145/600], Temp: 0.8627, Energy: -55.420072+0.002114j
[2025-08-07 17:58:28] [Iter 597/1050] R2[146/600], Temp: 0.8609, Energy: -55.416496+0.003449j
[2025-08-07 17:58:32] [Iter 598/1050] R2[147/600], Temp: 0.8591, Energy: -55.467151+0.001260j
[2025-08-07 17:58:36] [Iter 599/1050] R2[148/600], Temp: 0.8572, Energy: -55.456256-0.001997j
[2025-08-07 17:58:41] [Iter 600/1050] R2[149/600], Temp: 0.8554, Energy: -55.509159-0.000403j
[2025-08-07 17:58:41] ✓ Checkpoint saved: checkpoint_iter_000600.pkl
[2025-08-07 17:58:45] [Iter 601/1050] R2[150/600], Temp: 0.8536, Energy: -55.364066-0.001977j
[2025-08-07 17:58:49] [Iter 602/1050] R2[151/600], Temp: 0.8517, Energy: -55.313722+0.001467j
[2025-08-07 17:58:53] [Iter 603/1050] R2[152/600], Temp: 0.8498, Energy: -55.477899-0.000474j
[2025-08-07 17:58:57] [Iter 604/1050] R2[153/600], Temp: 0.8480, Energy: -55.429399+0.001811j
[2025-08-07 17:59:01] [Iter 605/1050] R2[154/600], Temp: 0.8461, Energy: -55.458743+0.006065j
[2025-08-07 17:59:05] [Iter 606/1050] R2[155/600], Temp: 0.8442, Energy: -55.473328-0.002348j
[2025-08-07 17:59:10] [Iter 607/1050] R2[156/600], Temp: 0.8423, Energy: -55.367707+0.003139j
[2025-08-07 17:59:14] [Iter 608/1050] R2[157/600], Temp: 0.8404, Energy: -55.436789+0.002278j
[2025-08-07 17:59:18] [Iter 609/1050] R2[158/600], Temp: 0.8384, Energy: -55.373412+0.000789j
[2025-08-07 17:59:22] [Iter 610/1050] R2[159/600], Temp: 0.8365, Energy: -55.325100-0.005904j
[2025-08-07 17:59:26] [Iter 611/1050] R2[160/600], Temp: 0.8346, Energy: -55.486969+0.004175j
[2025-08-07 17:59:30] [Iter 612/1050] R2[161/600], Temp: 0.8326, Energy: -55.586885+0.001658j
[2025-08-07 17:59:35] [Iter 613/1050] R2[162/600], Temp: 0.8307, Energy: -55.568795-0.001580j
[2025-08-07 17:59:39] [Iter 614/1050] R2[163/600], Temp: 0.8287, Energy: -55.445066-0.003020j
[2025-08-07 17:59:43] [Iter 615/1050] R2[164/600], Temp: 0.8267, Energy: -55.417102+0.003192j
[2025-08-07 17:59:47] [Iter 616/1050] R2[165/600], Temp: 0.8247, Energy: -55.252224+0.001625j
[2025-08-07 17:59:51] [Iter 617/1050] R2[166/600], Temp: 0.8227, Energy: -55.237004-0.001110j
[2025-08-07 17:59:55] [Iter 618/1050] R2[167/600], Temp: 0.8207, Energy: -55.229259+0.004016j
[2025-08-07 17:59:59] [Iter 619/1050] R2[168/600], Temp: 0.8187, Energy: -55.198347-0.000502j
[2025-08-07 18:00:04] [Iter 620/1050] R2[169/600], Temp: 0.8167, Energy: -55.289102-0.002986j
[2025-08-07 18:00:08] [Iter 621/1050] R2[170/600], Temp: 0.8147, Energy: -55.295865-0.007855j
[2025-08-07 18:00:12] [Iter 622/1050] R2[171/600], Temp: 0.8126, Energy: -55.345529-0.000384j
[2025-08-07 18:00:16] [Iter 623/1050] R2[172/600], Temp: 0.8106, Energy: -55.360945+0.000121j
[2025-08-07 18:00:20] [Iter 624/1050] R2[173/600], Temp: 0.8085, Energy: -55.305090+0.000228j
[2025-08-07 18:00:24] [Iter 625/1050] R2[174/600], Temp: 0.8065, Energy: -55.391785+0.001455j
[2025-08-07 18:00:29] [Iter 626/1050] R2[175/600], Temp: 0.8044, Energy: -55.492444-0.006015j
[2025-08-07 18:00:33] [Iter 627/1050] R2[176/600], Temp: 0.8023, Energy: -55.466099+0.002113j
[2025-08-07 18:00:37] [Iter 628/1050] R2[177/600], Temp: 0.8002, Energy: -55.349967-0.004426j
[2025-08-07 18:00:41] [Iter 629/1050] R2[178/600], Temp: 0.7981, Energy: -55.479527-0.001614j
[2025-08-07 18:00:45] [Iter 630/1050] R2[179/600], Temp: 0.7960, Energy: -55.417796+0.002335j
[2025-08-07 18:00:49] [Iter 631/1050] R2[180/600], Temp: 0.7939, Energy: -55.404371-0.003173j
[2025-08-07 18:00:53] [Iter 632/1050] R2[181/600], Temp: 0.7918, Energy: -55.444643+0.001461j
[2025-08-07 18:00:58] [Iter 633/1050] R2[182/600], Temp: 0.7896, Energy: -55.489420-0.000445j
[2025-08-07 18:01:02] [Iter 634/1050] R2[183/600], Temp: 0.7875, Energy: -55.387314-0.008322j
[2025-08-07 18:01:06] [Iter 635/1050] R2[184/600], Temp: 0.7854, Energy: -55.433649-0.003165j
[2025-08-07 18:01:10] [Iter 636/1050] R2[185/600], Temp: 0.7832, Energy: -55.360493-0.002111j
[2025-08-07 18:01:14] [Iter 637/1050] R2[186/600], Temp: 0.7810, Energy: -55.416553-0.004381j
[2025-08-07 18:01:18] [Iter 638/1050] R2[187/600], Temp: 0.7789, Energy: -55.487763+0.002780j
[2025-08-07 18:01:22] [Iter 639/1050] R2[188/600], Temp: 0.7767, Energy: -55.523916-0.001396j
[2025-08-07 18:01:27] [Iter 640/1050] R2[189/600], Temp: 0.7745, Energy: -55.424863-0.004588j
[2025-08-07 18:01:31] [Iter 641/1050] R2[190/600], Temp: 0.7723, Energy: -55.389086+0.002646j
[2025-08-07 18:01:35] [Iter 642/1050] R2[191/600], Temp: 0.7701, Energy: -55.398876+0.002073j
[2025-08-07 18:01:39] [Iter 643/1050] R2[192/600], Temp: 0.7679, Energy: -55.348301-0.005149j
[2025-08-07 18:01:43] [Iter 644/1050] R2[193/600], Temp: 0.7657, Energy: -55.398143+0.004536j
[2025-08-07 18:01:47] [Iter 645/1050] R2[194/600], Temp: 0.7635, Energy: -55.341177-0.005762j
[2025-08-07 18:01:51] [Iter 646/1050] R2[195/600], Temp: 0.7612, Energy: -55.413690+0.007549j
[2025-08-07 18:01:56] [Iter 647/1050] R2[196/600], Temp: 0.7590, Energy: -55.357892+0.001934j
[2025-08-07 18:02:00] [Iter 648/1050] R2[197/600], Temp: 0.7568, Energy: -55.405562-0.001696j
[2025-08-07 18:02:04] [Iter 649/1050] R2[198/600], Temp: 0.7545, Energy: -55.446307+0.002079j
[2025-08-07 18:02:08] [Iter 650/1050] R2[199/600], Temp: 0.7523, Energy: -55.396967-0.000502j
[2025-08-07 18:02:12] [Iter 651/1050] R2[200/600], Temp: 0.7500, Energy: -55.371046-0.002291j
[2025-08-07 18:02:16] [Iter 652/1050] R2[201/600], Temp: 0.7477, Energy: -55.351208+0.004951j
[2025-08-07 18:02:21] [Iter 653/1050] R2[202/600], Temp: 0.7455, Energy: -55.348195-0.002386j
[2025-08-07 18:02:25] [Iter 654/1050] R2[203/600], Temp: 0.7432, Energy: -55.200587-0.000927j
[2025-08-07 18:02:29] [Iter 655/1050] R2[204/600], Temp: 0.7409, Energy: -55.272780+0.000837j
[2025-08-07 18:02:33] [Iter 656/1050] R2[205/600], Temp: 0.7386, Energy: -55.171922+0.003101j
[2025-08-07 18:02:37] [Iter 657/1050] R2[206/600], Temp: 0.7363, Energy: -55.282491+0.003309j
[2025-08-07 18:02:41] [Iter 658/1050] R2[207/600], Temp: 0.7340, Energy: -55.294991+0.005234j
[2025-08-07 18:02:45] [Iter 659/1050] R2[208/600], Temp: 0.7316, Energy: -55.338005-0.001012j
[2025-08-07 18:02:50] [Iter 660/1050] R2[209/600], Temp: 0.7293, Energy: -55.260254-0.002360j
[2025-08-07 18:02:54] [Iter 661/1050] R2[210/600], Temp: 0.7270, Energy: -55.182979+0.003588j
[2025-08-07 18:02:58] [Iter 662/1050] R2[211/600], Temp: 0.7247, Energy: -55.311113-0.000680j
[2025-08-07 18:03:02] [Iter 663/1050] R2[212/600], Temp: 0.7223, Energy: -55.332884-0.002016j
[2025-08-07 18:03:06] [Iter 664/1050] R2[213/600], Temp: 0.7200, Energy: -55.308820+0.005078j
[2025-08-07 18:03:10] [Iter 665/1050] R2[214/600], Temp: 0.7176, Energy: -55.221972+0.003321j
[2025-08-07 18:03:15] [Iter 666/1050] R2[215/600], Temp: 0.7153, Energy: -55.372175-0.001723j
[2025-08-07 18:03:19] [Iter 667/1050] R2[216/600], Temp: 0.7129, Energy: -55.450607-0.001162j
[2025-08-07 18:03:23] [Iter 668/1050] R2[217/600], Temp: 0.7105, Energy: -55.457383-0.000719j
[2025-08-07 18:03:27] [Iter 669/1050] R2[218/600], Temp: 0.7081, Energy: -55.359836+0.004305j
[2025-08-07 18:03:31] [Iter 670/1050] R2[219/600], Temp: 0.7058, Energy: -55.560814+0.001573j
[2025-08-07 18:03:35] [Iter 671/1050] R2[220/600], Temp: 0.7034, Energy: -55.538662-0.001029j
[2025-08-07 18:03:39] [Iter 672/1050] R2[221/600], Temp: 0.7010, Energy: -55.491748+0.000301j
[2025-08-07 18:03:44] [Iter 673/1050] R2[222/600], Temp: 0.6986, Energy: -55.514232+0.004113j
[2025-08-07 18:03:48] [Iter 674/1050] R2[223/600], Temp: 0.6962, Energy: -55.403192+0.000180j
[2025-08-07 18:03:52] [Iter 675/1050] R2[224/600], Temp: 0.6938, Energy: -55.359484+0.003008j
[2025-08-07 18:03:56] [Iter 676/1050] R2[225/600], Temp: 0.6913, Energy: -55.396548-0.002394j
[2025-08-07 18:04:00] [Iter 677/1050] R2[226/600], Temp: 0.6889, Energy: -55.364819-0.002240j
[2025-08-07 18:04:04] [Iter 678/1050] R2[227/600], Temp: 0.6865, Energy: -55.457257+0.000687j
[2025-08-07 18:04:09] [Iter 679/1050] R2[228/600], Temp: 0.6841, Energy: -55.380222+0.004073j
[2025-08-07 18:04:13] [Iter 680/1050] R2[229/600], Temp: 0.6816, Energy: -55.355536+0.000452j
[2025-08-07 18:04:17] [Iter 681/1050] R2[230/600], Temp: 0.6792, Energy: -55.442888-0.001584j
[2025-08-07 18:04:21] [Iter 682/1050] R2[231/600], Temp: 0.6767, Energy: -55.485770-0.001507j
[2025-08-07 18:04:25] [Iter 683/1050] R2[232/600], Temp: 0.6743, Energy: -55.375872+0.001333j
[2025-08-07 18:04:29] [Iter 684/1050] R2[233/600], Temp: 0.6718, Energy: -55.471904+0.001478j
[2025-08-07 18:04:33] [Iter 685/1050] R2[234/600], Temp: 0.6694, Energy: -55.460781-0.002582j
[2025-08-07 18:04:38] [Iter 686/1050] R2[235/600], Temp: 0.6669, Energy: -55.412847+0.004057j
[2025-08-07 18:04:42] [Iter 687/1050] R2[236/600], Temp: 0.6644, Energy: -55.429877-0.002863j
[2025-08-07 18:04:46] [Iter 688/1050] R2[237/600], Temp: 0.6620, Energy: -55.411642+0.001299j
[2025-08-07 18:04:50] [Iter 689/1050] R2[238/600], Temp: 0.6595, Energy: -55.372505+0.000652j
[2025-08-07 18:04:54] [Iter 690/1050] R2[239/600], Temp: 0.6570, Energy: -55.370746-0.004562j
[2025-08-07 18:04:58] [Iter 691/1050] R2[240/600], Temp: 0.6545, Energy: -55.426827-0.002202j
[2025-08-07 18:05:03] [Iter 692/1050] R2[241/600], Temp: 0.6520, Energy: -55.464565-0.004803j
[2025-08-07 18:05:07] [Iter 693/1050] R2[242/600], Temp: 0.6495, Energy: -55.392521-0.004228j
[2025-08-07 18:05:11] [Iter 694/1050] R2[243/600], Temp: 0.6470, Energy: -55.458002-0.000853j
[2025-08-07 18:05:15] [Iter 695/1050] R2[244/600], Temp: 0.6445, Energy: -55.477034-0.000330j
[2025-08-07 18:05:19] [Iter 696/1050] R2[245/600], Temp: 0.6420, Energy: -55.461957+0.001776j
[2025-08-07 18:05:23] [Iter 697/1050] R2[246/600], Temp: 0.6395, Energy: -55.538580+0.000347j
[2025-08-07 18:05:27] [Iter 698/1050] R2[247/600], Temp: 0.6370, Energy: -55.514361+0.001820j
[2025-08-07 18:05:32] [Iter 699/1050] R2[248/600], Temp: 0.6345, Energy: -55.534847+0.002023j
[2025-08-07 18:05:36] [Iter 700/1050] R2[249/600], Temp: 0.6319, Energy: -55.565005-0.002266j
[2025-08-07 18:05:36] ✓ Checkpoint saved: checkpoint_iter_000700.pkl
[2025-08-07 18:05:40] [Iter 701/1050] R2[250/600], Temp: 0.6294, Energy: -55.413168+0.000551j
[2025-08-07 18:05:44] [Iter 702/1050] R2[251/600], Temp: 0.6269, Energy: -55.381330-0.002964j
[2025-08-07 18:05:48] [Iter 703/1050] R2[252/600], Temp: 0.6243, Energy: -55.319447-0.004972j
[2025-08-07 18:05:52] [Iter 704/1050] R2[253/600], Temp: 0.6218, Energy: -55.335340+0.002045j
[2025-08-07 18:05:57] [Iter 705/1050] R2[254/600], Temp: 0.6193, Energy: -55.391154-0.003420j
[2025-08-07 18:06:01] [Iter 706/1050] R2[255/600], Temp: 0.6167, Energy: -55.423128-0.002443j
[2025-08-07 18:06:05] [Iter 707/1050] R2[256/600], Temp: 0.6142, Energy: -55.435036-0.001718j
[2025-08-07 18:06:09] [Iter 708/1050] R2[257/600], Temp: 0.6116, Energy: -55.357398+0.004730j
[2025-08-07 18:06:13] [Iter 709/1050] R2[258/600], Temp: 0.6091, Energy: -55.398573+0.000682j
[2025-08-07 18:06:17] [Iter 710/1050] R2[259/600], Temp: 0.6065, Energy: -55.482758+0.000346j
[2025-08-07 18:06:21] [Iter 711/1050] R2[260/600], Temp: 0.6040, Energy: -55.359443-0.000466j
[2025-08-07 18:06:26] [Iter 712/1050] R2[261/600], Temp: 0.6014, Energy: -55.491795+0.000757j
[2025-08-07 18:06:30] [Iter 713/1050] R2[262/600], Temp: 0.5988, Energy: -55.406938+0.002722j
[2025-08-07 18:06:34] [Iter 714/1050] R2[263/600], Temp: 0.5963, Energy: -55.260939-0.002640j
[2025-08-07 18:06:38] [Iter 715/1050] R2[264/600], Temp: 0.5937, Energy: -55.495351-0.000890j
[2025-08-07 18:06:42] [Iter 716/1050] R2[265/600], Temp: 0.5911, Energy: -55.633315-0.000276j
[2025-08-07 18:06:46] [Iter 717/1050] R2[266/600], Temp: 0.5885, Energy: -55.720998-0.003298j
[2025-08-07 18:06:51] [Iter 718/1050] R2[267/600], Temp: 0.5860, Energy: -55.617868+0.000239j
[2025-08-07 18:06:55] [Iter 719/1050] R2[268/600], Temp: 0.5834, Energy: -55.541715+0.004769j
[2025-08-07 18:06:59] [Iter 720/1050] R2[269/600], Temp: 0.5808, Energy: -55.431337+0.000788j
[2025-08-07 18:07:03] [Iter 721/1050] R2[270/600], Temp: 0.5782, Energy: -55.418603+0.002649j
[2025-08-07 18:07:07] [Iter 722/1050] R2[271/600], Temp: 0.5756, Energy: -55.415807-0.004357j
[2025-08-07 18:07:11] [Iter 723/1050] R2[272/600], Temp: 0.5730, Energy: -55.468914-0.002135j
[2025-08-07 18:07:16] [Iter 724/1050] R2[273/600], Temp: 0.5705, Energy: -55.487267-0.000795j
[2025-08-07 18:07:20] [Iter 725/1050] R2[274/600], Temp: 0.5679, Energy: -55.431500+0.000096j
[2025-08-07 18:07:24] [Iter 726/1050] R2[275/600], Temp: 0.5653, Energy: -55.478145+0.003082j
[2025-08-07 18:07:28] [Iter 727/1050] R2[276/600], Temp: 0.5627, Energy: -55.514564-0.001043j
[2025-08-07 18:07:32] [Iter 728/1050] R2[277/600], Temp: 0.5601, Energy: -55.517306-0.005027j
[2025-08-07 18:07:36] [Iter 729/1050] R2[278/600], Temp: 0.5575, Energy: -55.423453+0.003767j
[2025-08-07 18:07:40] [Iter 730/1050] R2[279/600], Temp: 0.5549, Energy: -55.432463+0.000488j
[2025-08-07 18:07:45] [Iter 731/1050] R2[280/600], Temp: 0.5523, Energy: -55.480340-0.000589j
[2025-08-07 18:07:49] [Iter 732/1050] R2[281/600], Temp: 0.5497, Energy: -55.540405-0.003579j
[2025-08-07 18:07:53] [Iter 733/1050] R2[282/600], Temp: 0.5471, Energy: -55.589323+0.000738j
[2025-08-07 18:07:57] [Iter 734/1050] R2[283/600], Temp: 0.5444, Energy: -55.483248+0.005875j
[2025-08-07 18:08:01] [Iter 735/1050] R2[284/600], Temp: 0.5418, Energy: -55.443377+0.005781j
[2025-08-07 18:08:05] [Iter 736/1050] R2[285/600], Temp: 0.5392, Energy: -55.364729+0.002230j
[2025-08-07 18:08:09] [Iter 737/1050] R2[286/600], Temp: 0.5366, Energy: -55.469061+0.006765j
[2025-08-07 18:08:14] [Iter 738/1050] R2[287/600], Temp: 0.5340, Energy: -55.457337+0.002066j
[2025-08-07 18:08:18] [Iter 739/1050] R2[288/600], Temp: 0.5314, Energy: -55.403815+0.000217j
[2025-08-07 18:08:22] [Iter 740/1050] R2[289/600], Temp: 0.5288, Energy: -55.192401-0.000902j
[2025-08-07 18:08:26] [Iter 741/1050] R2[290/600], Temp: 0.5262, Energy: -55.392352+0.002565j
[2025-08-07 18:08:30] [Iter 742/1050] R2[291/600], Temp: 0.5236, Energy: -55.373604-0.002959j
[2025-08-07 18:08:34] [Iter 743/1050] R2[292/600], Temp: 0.5209, Energy: -55.295278-0.003689j
[2025-08-07 18:08:39] [Iter 744/1050] R2[293/600], Temp: 0.5183, Energy: -55.315301-0.001333j
[2025-08-07 18:08:43] [Iter 745/1050] R2[294/600], Temp: 0.5157, Energy: -55.362170-0.003329j
[2025-08-07 18:08:47] [Iter 746/1050] R2[295/600], Temp: 0.5131, Energy: -55.342802-0.002097j
[2025-08-07 18:08:51] [Iter 747/1050] R2[296/600], Temp: 0.5105, Energy: -55.421658-0.005597j
[2025-08-07 18:08:55] [Iter 748/1050] R2[297/600], Temp: 0.5079, Energy: -55.490374-0.004146j
[2025-08-07 18:08:59] [Iter 749/1050] R2[298/600], Temp: 0.5052, Energy: -55.444887-0.001097j
[2025-08-07 18:09:04] [Iter 750/1050] R2[299/600], Temp: 0.5026, Energy: -55.348881-0.002907j
[2025-08-07 18:09:08] [Iter 751/1050] R2[300/600], Temp: 0.5000, Energy: -55.322763-0.002769j
[2025-08-07 18:09:12] [Iter 752/1050] R2[301/600], Temp: 0.4974, Energy: -55.398001+0.002835j
[2025-08-07 18:09:16] [Iter 753/1050] R2[302/600], Temp: 0.4948, Energy: -55.373781+0.001705j
[2025-08-07 18:09:20] [Iter 754/1050] R2[303/600], Temp: 0.4921, Energy: -55.430494+0.002525j
[2025-08-07 18:09:24] [Iter 755/1050] R2[304/600], Temp: 0.4895, Energy: -55.397156-0.002048j
[2025-08-07 18:09:28] [Iter 756/1050] R2[305/600], Temp: 0.4869, Energy: -55.400468+0.000897j
[2025-08-07 18:09:33] [Iter 757/1050] R2[306/600], Temp: 0.4843, Energy: -55.377164+0.000460j
[2025-08-07 18:09:37] [Iter 758/1050] R2[307/600], Temp: 0.4817, Energy: -55.379886+0.007551j
[2025-08-07 18:09:41] [Iter 759/1050] R2[308/600], Temp: 0.4791, Energy: -55.331536+0.003369j
[2025-08-07 18:09:45] [Iter 760/1050] R2[309/600], Temp: 0.4764, Energy: -55.368466+0.005206j
[2025-08-07 18:09:49] [Iter 761/1050] R2[310/600], Temp: 0.4738, Energy: -55.259671+0.000767j
[2025-08-07 18:09:53] [Iter 762/1050] R2[311/600], Temp: 0.4712, Energy: -55.326429-0.003807j
[2025-08-07 18:09:57] [Iter 763/1050] R2[312/600], Temp: 0.4686, Energy: -55.487892-0.002980j
[2025-08-07 18:10:02] [Iter 764/1050] R2[313/600], Temp: 0.4660, Energy: -55.534814-0.004679j
[2025-08-07 18:10:06] [Iter 765/1050] R2[314/600], Temp: 0.4634, Energy: -55.424945-0.000093j
[2025-08-07 18:10:10] [Iter 766/1050] R2[315/600], Temp: 0.4608, Energy: -55.354503-0.001764j
[2025-08-07 18:10:14] [Iter 767/1050] R2[316/600], Temp: 0.4582, Energy: -55.383554+0.001362j
[2025-08-07 18:10:18] [Iter 768/1050] R2[317/600], Temp: 0.4556, Energy: -55.363221-0.000740j
[2025-08-07 18:10:22] [Iter 769/1050] R2[318/600], Temp: 0.4529, Energy: -55.436120-0.001671j
[2025-08-07 18:10:27] [Iter 770/1050] R2[319/600], Temp: 0.4503, Energy: -55.428631+0.004036j
[2025-08-07 18:10:31] [Iter 771/1050] R2[320/600], Temp: 0.4477, Energy: -55.443620-0.001946j
[2025-08-07 18:10:35] [Iter 772/1050] R2[321/600], Temp: 0.4451, Energy: -55.432658-0.005676j
[2025-08-07 18:10:39] [Iter 773/1050] R2[322/600], Temp: 0.4425, Energy: -55.435441-0.000789j
[2025-08-07 18:10:43] [Iter 774/1050] R2[323/600], Temp: 0.4399, Energy: -55.476203+0.000896j
[2025-08-07 18:10:47] [Iter 775/1050] R2[324/600], Temp: 0.4373, Energy: -55.413914-0.000560j
[2025-08-07 18:10:51] [Iter 776/1050] R2[325/600], Temp: 0.4347, Energy: -55.517799-0.001770j
[2025-08-07 18:10:56] [Iter 777/1050] R2[326/600], Temp: 0.4321, Energy: -55.583334-0.002692j
[2025-08-07 18:11:00] [Iter 778/1050] R2[327/600], Temp: 0.4295, Energy: -55.454728-0.003762j
[2025-08-07 18:11:04] [Iter 779/1050] R2[328/600], Temp: 0.4270, Energy: -55.562646-0.001181j
[2025-08-07 18:11:08] [Iter 780/1050] R2[329/600], Temp: 0.4244, Energy: -55.487518-0.001283j
[2025-08-07 18:11:12] [Iter 781/1050] R2[330/600], Temp: 0.4218, Energy: -55.477349-0.002427j
[2025-08-07 18:11:16] [Iter 782/1050] R2[331/600], Temp: 0.4192, Energy: -55.495252+0.001017j
[2025-08-07 18:11:21] [Iter 783/1050] R2[332/600], Temp: 0.4166, Energy: -55.447026-0.001272j
[2025-08-07 18:11:25] [Iter 784/1050] R2[333/600], Temp: 0.4140, Energy: -55.495822-0.002201j
[2025-08-07 18:11:29] [Iter 785/1050] R2[334/600], Temp: 0.4115, Energy: -55.513231-0.003188j
[2025-08-07 18:11:33] [Iter 786/1050] R2[335/600], Temp: 0.4089, Energy: -55.447640-0.002772j
[2025-08-07 18:11:37] [Iter 787/1050] R2[336/600], Temp: 0.4063, Energy: -55.516485+0.011087j
[2025-08-07 18:11:41] [Iter 788/1050] R2[337/600], Temp: 0.4037, Energy: -55.346880+0.005776j
[2025-08-07 18:11:46] [Iter 789/1050] R2[338/600], Temp: 0.4012, Energy: -55.465972+0.003262j
[2025-08-07 18:11:50] [Iter 790/1050] R2[339/600], Temp: 0.3986, Energy: -55.462380-0.002619j
[2025-08-07 18:11:54] [Iter 791/1050] R2[340/600], Temp: 0.3960, Energy: -55.457632+0.004644j
[2025-08-07 18:11:58] [Iter 792/1050] R2[341/600], Temp: 0.3935, Energy: -55.243316-0.001413j
[2025-08-07 18:12:02] [Iter 793/1050] R2[342/600], Temp: 0.3909, Energy: -55.235978-0.000410j
[2025-08-07 18:12:06] [Iter 794/1050] R2[343/600], Temp: 0.3884, Energy: -55.384569+0.000483j
[2025-08-07 18:12:10] [Iter 795/1050] R2[344/600], Temp: 0.3858, Energy: -55.321514-0.000909j
[2025-08-07 18:12:15] [Iter 796/1050] R2[345/600], Temp: 0.3833, Energy: -55.266338-0.001953j
[2025-08-07 18:12:19] [Iter 797/1050] R2[346/600], Temp: 0.3807, Energy: -55.284445-0.000532j
[2025-08-07 18:12:23] [Iter 798/1050] R2[347/600], Temp: 0.3782, Energy: -55.256277-0.001156j
[2025-08-07 18:12:27] [Iter 799/1050] R2[348/600], Temp: 0.3757, Energy: -55.377556-0.000294j
[2025-08-07 18:12:31] [Iter 800/1050] R2[349/600], Temp: 0.3731, Energy: -55.303390+0.006887j
[2025-08-07 18:12:31] ✓ Checkpoint saved: checkpoint_iter_000800.pkl
[2025-08-07 18:12:35] [Iter 801/1050] R2[350/600], Temp: 0.3706, Energy: -55.363259+0.003057j
[2025-08-07 18:12:40] [Iter 802/1050] R2[351/600], Temp: 0.3681, Energy: -55.392410-0.003238j
[2025-08-07 18:12:44] [Iter 803/1050] R2[352/600], Temp: 0.3655, Energy: -55.339556-0.002176j
[2025-08-07 18:12:48] [Iter 804/1050] R2[353/600], Temp: 0.3630, Energy: -55.508786-0.001761j
[2025-08-07 18:12:52] [Iter 805/1050] R2[354/600], Temp: 0.3605, Energy: -55.342322-0.002979j
[2025-08-07 18:12:56] [Iter 806/1050] R2[355/600], Temp: 0.3580, Energy: -55.339426+0.001274j
[2025-08-07 18:13:00] [Iter 807/1050] R2[356/600], Temp: 0.3555, Energy: -55.398159-0.000994j
[2025-08-07 18:13:05] [Iter 808/1050] R2[357/600], Temp: 0.3530, Energy: -55.391912-0.001908j
[2025-08-07 18:13:09] [Iter 809/1050] R2[358/600], Temp: 0.3505, Energy: -55.412254-0.000486j
[2025-08-07 18:13:13] [Iter 810/1050] R2[359/600], Temp: 0.3480, Energy: -55.403444-0.000095j
[2025-08-07 18:13:17] [Iter 811/1050] R2[360/600], Temp: 0.3455, Energy: -55.425807+0.002870j
[2025-08-07 18:13:21] [Iter 812/1050] R2[361/600], Temp: 0.3430, Energy: -55.407784+0.005240j
[2025-08-07 18:13:25] [Iter 813/1050] R2[362/600], Temp: 0.3405, Energy: -55.391887+0.005079j
[2025-08-07 18:13:29] [Iter 814/1050] R2[363/600], Temp: 0.3380, Energy: -55.413780+0.000894j
[2025-08-07 18:13:34] [Iter 815/1050] R2[364/600], Temp: 0.3356, Energy: -55.381131+0.001352j
[2025-08-07 18:13:38] [Iter 816/1050] R2[365/600], Temp: 0.3331, Energy: -55.440310+0.000647j
[2025-08-07 18:13:42] [Iter 817/1050] R2[366/600], Temp: 0.3306, Energy: -55.431944+0.005523j
[2025-08-07 18:13:46] [Iter 818/1050] R2[367/600], Temp: 0.3282, Energy: -55.464639+0.003110j
[2025-08-07 18:13:50] [Iter 819/1050] R2[368/600], Temp: 0.3257, Energy: -55.414753-0.002689j
[2025-08-07 18:13:54] [Iter 820/1050] R2[369/600], Temp: 0.3233, Energy: -55.345031+0.002722j
[2025-08-07 18:13:59] [Iter 821/1050] R2[370/600], Temp: 0.3208, Energy: -55.262799+0.000639j
[2025-08-07 18:14:03] [Iter 822/1050] R2[371/600], Temp: 0.3184, Energy: -55.195849+0.000664j
[2025-08-07 18:14:07] [Iter 823/1050] R2[372/600], Temp: 0.3159, Energy: -55.259492-0.002164j
[2025-08-07 18:14:11] [Iter 824/1050] R2[373/600], Temp: 0.3135, Energy: -55.382276+0.001716j
[2025-08-07 18:14:15] [Iter 825/1050] R2[374/600], Temp: 0.3111, Energy: -55.364421+0.001344j
[2025-08-07 18:14:19] [Iter 826/1050] R2[375/600], Temp: 0.3087, Energy: -55.316197-0.002046j
[2025-08-07 18:14:23] [Iter 827/1050] R2[376/600], Temp: 0.3062, Energy: -55.380732+0.004097j
[2025-08-07 18:14:28] [Iter 828/1050] R2[377/600], Temp: 0.3038, Energy: -55.456618-0.000881j
[2025-08-07 18:14:32] [Iter 829/1050] R2[378/600], Temp: 0.3014, Energy: -55.404354-0.002916j
[2025-08-07 18:14:36] [Iter 830/1050] R2[379/600], Temp: 0.2990, Energy: -55.375340+0.000057j
[2025-08-07 18:14:40] [Iter 831/1050] R2[380/600], Temp: 0.2966, Energy: -55.362480-0.002699j
[2025-08-07 18:14:44] [Iter 832/1050] R2[381/600], Temp: 0.2942, Energy: -55.472665-0.004057j
[2025-08-07 18:14:48] [Iter 833/1050] R2[382/600], Temp: 0.2919, Energy: -55.475841-0.004597j
[2025-08-07 18:14:52] [Iter 834/1050] R2[383/600], Temp: 0.2895, Energy: -55.456414+0.005257j
[2025-08-07 18:14:57] [Iter 835/1050] R2[384/600], Temp: 0.2871, Energy: -55.466641-0.001453j
[2025-08-07 18:15:01] [Iter 836/1050] R2[385/600], Temp: 0.2847, Energy: -55.433786-0.000202j
[2025-08-07 18:15:05] [Iter 837/1050] R2[386/600], Temp: 0.2824, Energy: -55.496396-0.000224j
[2025-08-07 18:15:09] [Iter 838/1050] R2[387/600], Temp: 0.2800, Energy: -55.483335+0.005587j
[2025-08-07 18:15:13] [Iter 839/1050] R2[388/600], Temp: 0.2777, Energy: -55.483707-0.000010j
[2025-08-07 18:15:17] [Iter 840/1050] R2[389/600], Temp: 0.2753, Energy: -55.413881-0.002029j
[2025-08-07 18:15:22] [Iter 841/1050] R2[390/600], Temp: 0.2730, Energy: -55.337942+0.000374j
[2025-08-07 18:15:26] [Iter 842/1050] R2[391/600], Temp: 0.2707, Energy: -55.382923+0.005529j
[2025-08-07 18:15:30] [Iter 843/1050] R2[392/600], Temp: 0.2684, Energy: -55.382111-0.000230j
[2025-08-07 18:15:34] [Iter 844/1050] R2[393/600], Temp: 0.2660, Energy: -55.363699+0.006215j
[2025-08-07 18:15:38] [Iter 845/1050] R2[394/600], Temp: 0.2637, Energy: -55.321026-0.000973j
[2025-08-07 18:15:42] [Iter 846/1050] R2[395/600], Temp: 0.2614, Energy: -55.453978+0.001573j
[2025-08-07 18:15:47] [Iter 847/1050] R2[396/600], Temp: 0.2591, Energy: -55.510735+0.003802j
[2025-08-07 18:15:51] [Iter 848/1050] R2[397/600], Temp: 0.2568, Energy: -55.505250-0.000289j
[2025-08-07 18:15:55] [Iter 849/1050] R2[398/600], Temp: 0.2545, Energy: -55.493242+0.006322j
[2025-08-07 18:15:59] [Iter 850/1050] R2[399/600], Temp: 0.2523, Energy: -55.419497+0.002481j
[2025-08-07 18:16:03] [Iter 851/1050] R2[400/600], Temp: 0.2500, Energy: -55.506309+0.003518j
[2025-08-07 18:16:07] [Iter 852/1050] R2[401/600], Temp: 0.2477, Energy: -55.430760+0.005494j
[2025-08-07 18:16:12] [Iter 853/1050] R2[402/600], Temp: 0.2455, Energy: -55.465076+0.004670j
[2025-08-07 18:16:16] [Iter 854/1050] R2[403/600], Temp: 0.2432, Energy: -55.405870-0.000205j
[2025-08-07 18:16:20] [Iter 855/1050] R2[404/600], Temp: 0.2410, Energy: -55.392154-0.001182j
[2025-08-07 18:16:24] [Iter 856/1050] R2[405/600], Temp: 0.2388, Energy: -55.413500+0.003408j
[2025-08-07 18:16:28] [Iter 857/1050] R2[406/600], Temp: 0.2365, Energy: -55.269578-0.000140j
[2025-08-07 18:16:32] [Iter 858/1050] R2[407/600], Temp: 0.2343, Energy: -55.300425+0.001927j
[2025-08-07 18:16:36] [Iter 859/1050] R2[408/600], Temp: 0.2321, Energy: -55.370655+0.003034j
[2025-08-07 18:16:41] [Iter 860/1050] R2[409/600], Temp: 0.2299, Energy: -55.330764-0.004444j
[2025-08-07 18:16:45] [Iter 861/1050] R2[410/600], Temp: 0.2277, Energy: -55.251504+0.003200j
[2025-08-07 18:16:49] [Iter 862/1050] R2[411/600], Temp: 0.2255, Energy: -55.351930-0.001808j
[2025-08-07 18:16:53] [Iter 863/1050] R2[412/600], Temp: 0.2233, Energy: -55.389822-0.001607j
[2025-08-07 18:16:57] [Iter 864/1050] R2[413/600], Temp: 0.2211, Energy: -55.318510+0.001423j
[2025-08-07 18:17:01] [Iter 865/1050] R2[414/600], Temp: 0.2190, Energy: -55.358322-0.003214j
[2025-08-07 18:17:05] [Iter 866/1050] R2[415/600], Temp: 0.2168, Energy: -55.386212-0.005598j
[2025-08-07 18:17:10] [Iter 867/1050] R2[416/600], Temp: 0.2146, Energy: -55.521817+0.002436j
[2025-08-07 18:17:14] [Iter 868/1050] R2[417/600], Temp: 0.2125, Energy: -55.477993-0.001089j
[2025-08-07 18:17:18] [Iter 869/1050] R2[418/600], Temp: 0.2104, Energy: -55.407791+0.003234j
[2025-08-07 18:17:22] [Iter 870/1050] R2[419/600], Temp: 0.2082, Energy: -55.402299+0.002468j
[2025-08-07 18:17:26] [Iter 871/1050] R2[420/600], Temp: 0.2061, Energy: -55.452023+0.001294j
[2025-08-07 18:17:30] [Iter 872/1050] R2[421/600], Temp: 0.2040, Energy: -55.661639-0.002554j
[2025-08-07 18:17:35] [Iter 873/1050] R2[422/600], Temp: 0.2019, Energy: -55.538645+0.001579j
[2025-08-07 18:17:39] [Iter 874/1050] R2[423/600], Temp: 0.1998, Energy: -55.410832+0.004706j
[2025-08-07 18:17:43] [Iter 875/1050] R2[424/600], Temp: 0.1977, Energy: -55.450076+0.001236j
[2025-08-07 18:17:47] [Iter 876/1050] R2[425/600], Temp: 0.1956, Energy: -55.544401-0.000135j
[2025-08-07 18:17:51] [Iter 877/1050] R2[426/600], Temp: 0.1935, Energy: -55.433312-0.005011j
[2025-08-07 18:17:55] [Iter 878/1050] R2[427/600], Temp: 0.1915, Energy: -55.487222-0.003148j
[2025-08-07 18:18:00] [Iter 879/1050] R2[428/600], Temp: 0.1894, Energy: -55.363523-0.004059j
[2025-08-07 18:18:04] [Iter 880/1050] R2[429/600], Temp: 0.1874, Energy: -55.419292+0.001155j
[2025-08-07 18:18:08] [Iter 881/1050] R2[430/600], Temp: 0.1853, Energy: -55.501101+0.001397j
[2025-08-07 18:18:12] [Iter 882/1050] R2[431/600], Temp: 0.1833, Energy: -55.477847+0.000429j
[2025-08-07 18:18:16] [Iter 883/1050] R2[432/600], Temp: 0.1813, Energy: -55.393125-0.005510j
[2025-08-07 18:18:20] [Iter 884/1050] R2[433/600], Temp: 0.1793, Energy: -55.368247-0.003679j
[2025-08-07 18:18:24] [Iter 885/1050] R2[434/600], Temp: 0.1773, Energy: -55.247782+0.002286j
[2025-08-07 18:18:29] [Iter 886/1050] R2[435/600], Temp: 0.1753, Energy: -55.320713-0.001903j
[2025-08-07 18:18:33] [Iter 887/1050] R2[436/600], Temp: 0.1733, Energy: -55.330029+0.004246j
[2025-08-07 18:18:37] [Iter 888/1050] R2[437/600], Temp: 0.1713, Energy: -55.458415-0.000548j
[2025-08-07 18:18:41] [Iter 889/1050] R2[438/600], Temp: 0.1693, Energy: -55.385993-0.001386j
[2025-08-07 18:18:45] [Iter 890/1050] R2[439/600], Temp: 0.1674, Energy: -55.410299-0.001129j
[2025-08-07 18:18:49] [Iter 891/1050] R2[440/600], Temp: 0.1654, Energy: -55.449195-0.000532j
[2025-08-07 18:18:53] [Iter 892/1050] R2[441/600], Temp: 0.1635, Energy: -55.550761-0.000519j
[2025-08-07 18:18:58] [Iter 893/1050] R2[442/600], Temp: 0.1616, Energy: -55.374809-0.001646j
[2025-08-07 18:19:02] [Iter 894/1050] R2[443/600], Temp: 0.1596, Energy: -55.552372+0.000457j
[2025-08-07 18:19:06] [Iter 895/1050] R2[444/600], Temp: 0.1577, Energy: -55.456086+0.002785j
[2025-08-07 18:19:10] [Iter 896/1050] R2[445/600], Temp: 0.1558, Energy: -55.584848-0.003666j
[2025-08-07 18:19:14] [Iter 897/1050] R2[446/600], Temp: 0.1539, Energy: -55.560382+0.000056j
[2025-08-07 18:19:18] [Iter 898/1050] R2[447/600], Temp: 0.1520, Energy: -55.488700-0.002842j
[2025-08-07 18:19:23] [Iter 899/1050] R2[448/600], Temp: 0.1502, Energy: -55.500944+0.000299j
[2025-08-07 18:19:27] [Iter 900/1050] R2[449/600], Temp: 0.1483, Energy: -55.401931+0.000017j
[2025-08-07 18:19:27] ✓ Checkpoint saved: checkpoint_iter_000900.pkl
[2025-08-07 18:19:31] [Iter 901/1050] R2[450/600], Temp: 0.1464, Energy: -55.478907-0.002510j
[2025-08-07 18:19:35] [Iter 902/1050] R2[451/600], Temp: 0.1446, Energy: -55.366351-0.002371j
[2025-08-07 18:19:39] [Iter 903/1050] R2[452/600], Temp: 0.1428, Energy: -55.502885-0.001731j
[2025-08-07 18:19:43] [Iter 904/1050] R2[453/600], Temp: 0.1409, Energy: -55.440686-0.005548j
[2025-08-07 18:19:47] [Iter 905/1050] R2[454/600], Temp: 0.1391, Energy: -55.546144+0.000697j
[2025-08-07 18:19:52] [Iter 906/1050] R2[455/600], Temp: 0.1373, Energy: -55.604293-0.005949j
[2025-08-07 18:19:56] [Iter 907/1050] R2[456/600], Temp: 0.1355, Energy: -55.509161-0.003488j
[2025-08-07 18:20:00] [Iter 908/1050] R2[457/600], Temp: 0.1337, Energy: -55.448438+0.002189j
[2025-08-07 18:20:04] [Iter 909/1050] R2[458/600], Temp: 0.1320, Energy: -55.499979+0.000086j
[2025-08-07 18:20:08] [Iter 910/1050] R2[459/600], Temp: 0.1302, Energy: -55.514643-0.000596j
[2025-08-07 18:20:12] [Iter 911/1050] R2[460/600], Temp: 0.1284, Energy: -55.560707-0.003726j
[2025-08-07 18:20:17] [Iter 912/1050] R2[461/600], Temp: 0.1267, Energy: -55.480863+0.004375j
[2025-08-07 18:20:21] [Iter 913/1050] R2[462/600], Temp: 0.1249, Energy: -55.540371+0.001518j
[2025-08-07 18:20:25] [Iter 914/1050] R2[463/600], Temp: 0.1232, Energy: -55.512589-0.001741j
[2025-08-07 18:20:29] [Iter 915/1050] R2[464/600], Temp: 0.1215, Energy: -55.551327-0.000544j
[2025-08-07 18:20:33] [Iter 916/1050] R2[465/600], Temp: 0.1198, Energy: -55.439157+0.002042j
[2025-08-07 18:20:37] [Iter 917/1050] R2[466/600], Temp: 0.1181, Energy: -55.372928+0.002005j
[2025-08-07 18:20:42] [Iter 918/1050] R2[467/600], Temp: 0.1164, Energy: -55.425540+0.002044j
[2025-08-07 18:20:46] [Iter 919/1050] R2[468/600], Temp: 0.1147, Energy: -55.324903-0.000939j
[2025-08-07 18:20:50] [Iter 920/1050] R2[469/600], Temp: 0.1131, Energy: -55.367118+0.001476j
[2025-08-07 18:20:54] [Iter 921/1050] R2[470/600], Temp: 0.1114, Energy: -55.402894+0.004696j
[2025-08-07 18:20:58] [Iter 922/1050] R2[471/600], Temp: 0.1098, Energy: -55.410450+0.000884j
[2025-08-07 18:21:02] [Iter 923/1050] R2[472/600], Temp: 0.1082, Energy: -55.324869+0.003779j
[2025-08-07 18:21:06] [Iter 924/1050] R2[473/600], Temp: 0.1065, Energy: -55.361890-0.001789j
[2025-08-07 18:21:11] [Iter 925/1050] R2[474/600], Temp: 0.1049, Energy: -55.383050+0.000274j
[2025-08-07 18:21:15] [Iter 926/1050] R2[475/600], Temp: 0.1033, Energy: -55.352492+0.002607j
[2025-08-07 18:21:19] [Iter 927/1050] R2[476/600], Temp: 0.1017, Energy: -55.411951+0.006099j
[2025-08-07 18:21:23] [Iter 928/1050] R2[477/600], Temp: 0.1002, Energy: -55.406369+0.003225j
[2025-08-07 18:21:27] [Iter 929/1050] R2[478/600], Temp: 0.0986, Energy: -55.408511+0.002259j
[2025-08-07 18:21:31] [Iter 930/1050] R2[479/600], Temp: 0.0970, Energy: -55.424954+0.001292j
[2025-08-07 18:21:35] [Iter 931/1050] R2[480/600], Temp: 0.0955, Energy: -55.398769-0.000648j
[2025-08-07 18:21:40] [Iter 932/1050] R2[481/600], Temp: 0.0940, Energy: -55.319196+0.003494j
[2025-08-07 18:21:44] [Iter 933/1050] R2[482/600], Temp: 0.0924, Energy: -55.414339-0.004084j
[2025-08-07 18:21:48] [Iter 934/1050] R2[483/600], Temp: 0.0909, Energy: -55.549138+0.003590j
[2025-08-07 18:21:52] [Iter 935/1050] R2[484/600], Temp: 0.0894, Energy: -55.512812-0.003115j
[2025-08-07 18:21:56] [Iter 936/1050] R2[485/600], Temp: 0.0879, Energy: -55.390054+0.001753j
[2025-08-07 18:22:00] [Iter 937/1050] R2[486/600], Temp: 0.0865, Energy: -55.454996-0.000492j
[2025-08-07 18:22:05] [Iter 938/1050] R2[487/600], Temp: 0.0850, Energy: -55.488502+0.000791j
[2025-08-07 18:22:09] [Iter 939/1050] R2[488/600], Temp: 0.0835, Energy: -55.489580-0.000588j
[2025-08-07 18:22:13] [Iter 940/1050] R2[489/600], Temp: 0.0821, Energy: -55.460189+0.004666j
[2025-08-07 18:22:17] [Iter 941/1050] R2[490/600], Temp: 0.0807, Energy: -55.430705+0.004904j
[2025-08-07 18:22:21] [Iter 942/1050] R2[491/600], Temp: 0.0792, Energy: -55.354087+0.004196j
[2025-08-07 18:22:25] [Iter 943/1050] R2[492/600], Temp: 0.0778, Energy: -55.433825-0.001115j
[2025-08-07 18:22:29] [Iter 944/1050] R2[493/600], Temp: 0.0764, Energy: -55.462430+0.001572j
[2025-08-07 18:22:34] [Iter 945/1050] R2[494/600], Temp: 0.0751, Energy: -55.512866+0.002144j
[2025-08-07 18:22:38] [Iter 946/1050] R2[495/600], Temp: 0.0737, Energy: -55.473186+0.003253j
[2025-08-07 18:22:42] [Iter 947/1050] R2[496/600], Temp: 0.0723, Energy: -55.610068+0.004622j
[2025-08-07 18:22:46] [Iter 948/1050] R2[497/600], Temp: 0.0710, Energy: -55.570358-0.000894j
[2025-08-07 18:22:50] [Iter 949/1050] R2[498/600], Temp: 0.0696, Energy: -55.562968+0.002458j
[2025-08-07 18:22:54] [Iter 950/1050] R2[499/600], Temp: 0.0683, Energy: -55.508092+0.000254j
[2025-08-07 18:22:59] [Iter 951/1050] R2[500/600], Temp: 0.0670, Energy: -55.489916-0.002684j
[2025-08-07 18:23:03] [Iter 952/1050] R2[501/600], Temp: 0.0657, Energy: -55.395676+0.006987j
[2025-08-07 18:23:07] [Iter 953/1050] R2[502/600], Temp: 0.0644, Energy: -55.380225+0.005194j
[2025-08-07 18:23:11] [Iter 954/1050] R2[503/600], Temp: 0.0631, Energy: -55.353286-0.004400j
[2025-08-07 18:23:15] [Iter 955/1050] R2[504/600], Temp: 0.0618, Energy: -55.444538+0.002974j
[2025-08-07 18:23:19] [Iter 956/1050] R2[505/600], Temp: 0.0606, Energy: -55.402656-0.002815j
[2025-08-07 18:23:23] [Iter 957/1050] R2[506/600], Temp: 0.0593, Energy: -55.398178-0.001129j
[2025-08-07 18:23:28] [Iter 958/1050] R2[507/600], Temp: 0.0581, Energy: -55.409659+0.003723j
[2025-08-07 18:23:32] [Iter 959/1050] R2[508/600], Temp: 0.0569, Energy: -55.414906+0.000232j
[2025-08-07 18:23:36] [Iter 960/1050] R2[509/600], Temp: 0.0557, Energy: -55.396208+0.004487j
[2025-08-07 18:23:40] [Iter 961/1050] R2[510/600], Temp: 0.0545, Energy: -55.361516+0.000612j
[2025-08-07 18:23:44] [Iter 962/1050] R2[511/600], Temp: 0.0533, Energy: -55.363056+0.002837j
[2025-08-07 18:23:48] [Iter 963/1050] R2[512/600], Temp: 0.0521, Energy: -55.333281+0.003669j
[2025-08-07 18:23:52] [Iter 964/1050] R2[513/600], Temp: 0.0510, Energy: -55.270421-0.000793j
[2025-08-07 18:23:57] [Iter 965/1050] R2[514/600], Temp: 0.0498, Energy: -55.391459+0.004074j
[2025-08-07 18:24:01] [Iter 966/1050] R2[515/600], Temp: 0.0487, Energy: -55.449344-0.002830j
[2025-08-07 18:24:05] [Iter 967/1050] R2[516/600], Temp: 0.0476, Energy: -55.478731+0.000933j
[2025-08-07 18:24:09] [Iter 968/1050] R2[517/600], Temp: 0.0465, Energy: -55.378567+0.003216j
[2025-08-07 18:24:13] [Iter 969/1050] R2[518/600], Temp: 0.0454, Energy: -55.329201+0.002131j
[2025-08-07 18:24:17] [Iter 970/1050] R2[519/600], Temp: 0.0443, Energy: -55.467190+0.000229j
[2025-08-07 18:24:22] [Iter 971/1050] R2[520/600], Temp: 0.0432, Energy: -55.558728-0.004411j
[2025-08-07 18:24:26] [Iter 972/1050] R2[521/600], Temp: 0.0422, Energy: -55.579574+0.003705j
[2025-08-07 18:24:30] [Iter 973/1050] R2[522/600], Temp: 0.0411, Energy: -55.574003+0.000646j
[2025-08-07 18:24:34] [Iter 974/1050] R2[523/600], Temp: 0.0401, Energy: -55.415648-0.005987j
[2025-08-07 18:24:38] [Iter 975/1050] R2[524/600], Temp: 0.0391, Energy: -55.483331-0.002393j
[2025-08-07 18:24:42] [Iter 976/1050] R2[525/600], Temp: 0.0381, Energy: -55.285617+0.001854j
[2025-08-07 18:24:47] [Iter 977/1050] R2[526/600], Temp: 0.0371, Energy: -55.329338+0.009125j
[2025-08-07 18:24:51] [Iter 978/1050] R2[527/600], Temp: 0.0361, Energy: -55.353402-0.000149j
[2025-08-07 18:24:55] [Iter 979/1050] R2[528/600], Temp: 0.0351, Energy: -55.350187+0.001489j
[2025-08-07 18:24:59] [Iter 980/1050] R2[529/600], Temp: 0.0342, Energy: -55.340417-0.000317j
[2025-08-07 18:25:03] [Iter 981/1050] R2[530/600], Temp: 0.0332, Energy: -55.343243+0.001078j
[2025-08-07 18:25:07] [Iter 982/1050] R2[531/600], Temp: 0.0323, Energy: -55.519663+0.000761j
[2025-08-07 18:25:12] [Iter 983/1050] R2[532/600], Temp: 0.0314, Energy: -55.415103+0.002226j
[2025-08-07 18:25:16] [Iter 984/1050] R2[533/600], Temp: 0.0305, Energy: -55.346433+0.003563j
[2025-08-07 18:25:20] [Iter 985/1050] R2[534/600], Temp: 0.0296, Energy: -55.396307-0.000307j
[2025-08-07 18:25:24] [Iter 986/1050] R2[535/600], Temp: 0.0287, Energy: -55.395188-0.003550j
[2025-08-07 18:25:28] [Iter 987/1050] R2[536/600], Temp: 0.0278, Energy: -55.376949+0.003168j
[2025-08-07 18:25:32] [Iter 988/1050] R2[537/600], Temp: 0.0270, Energy: -55.434105+0.001454j
[2025-08-07 18:25:36] [Iter 989/1050] R2[538/600], Temp: 0.0261, Energy: -55.403278-0.001960j
[2025-08-07 18:25:41] [Iter 990/1050] R2[539/600], Temp: 0.0253, Energy: -55.431489-0.005053j
[2025-08-07 18:25:45] [Iter 991/1050] R2[540/600], Temp: 0.0245, Energy: -55.409176+0.000186j
[2025-08-07 18:25:49] [Iter 992/1050] R2[541/600], Temp: 0.0237, Energy: -55.322691-0.000600j
[2025-08-07 18:25:53] [Iter 993/1050] R2[542/600], Temp: 0.0229, Energy: -55.336387-0.000598j
[2025-08-07 18:25:57] [Iter 994/1050] R2[543/600], Temp: 0.0221, Energy: -55.375091+0.001928j
[2025-08-07 18:26:01] [Iter 995/1050] R2[544/600], Temp: 0.0213, Energy: -55.323951-0.004759j
[2025-08-07 18:26:05] [Iter 996/1050] R2[545/600], Temp: 0.0206, Energy: -55.392973+0.004358j
[2025-08-07 18:26:10] [Iter 997/1050] R2[546/600], Temp: 0.0199, Energy: -55.346947-0.000819j
[2025-08-07 18:26:14] [Iter 998/1050] R2[547/600], Temp: 0.0191, Energy: -55.375382+0.000587j
[2025-08-07 18:26:18] [Iter 999/1050] R2[548/600], Temp: 0.0184, Energy: -55.368972-0.000466j
[2025-08-07 18:26:22] [Iter 1000/1050] R2[549/600], Temp: 0.0177, Energy: -55.436387-0.004694j
[2025-08-07 18:26:22] ✓ Checkpoint saved: checkpoint_iter_001000.pkl
[2025-08-07 18:26:26] [Iter 1001/1050] R2[550/600], Temp: 0.0170, Energy: -55.494974-0.008362j
[2025-08-07 18:26:30] [Iter 1002/1050] R2[551/600], Temp: 0.0164, Energy: -55.331352-0.001338j
[2025-08-07 18:26:35] [Iter 1003/1050] R2[552/600], Temp: 0.0157, Energy: -55.452459-0.000761j
[2025-08-07 18:26:39] [Iter 1004/1050] R2[553/600], Temp: 0.0151, Energy: -55.334700+0.001760j
[2025-08-07 18:26:43] [Iter 1005/1050] R2[554/600], Temp: 0.0144, Energy: -55.286515+0.001741j
[2025-08-07 18:26:47] [Iter 1006/1050] R2[555/600], Temp: 0.0138, Energy: -55.341843+0.001892j
[2025-08-07 18:26:51] [Iter 1007/1050] R2[556/600], Temp: 0.0132, Energy: -55.446419+0.004544j
[2025-08-07 18:26:55] [Iter 1008/1050] R2[557/600], Temp: 0.0126, Energy: -55.542117-0.000971j
[2025-08-07 18:27:00] [Iter 1009/1050] R2[558/600], Temp: 0.0120, Energy: -55.496391+0.000701j
[2025-08-07 18:27:04] [Iter 1010/1050] R2[559/600], Temp: 0.0115, Energy: -55.488166+0.003097j
[2025-08-07 18:27:08] [Iter 1011/1050] R2[560/600], Temp: 0.0109, Energy: -55.325599-0.001659j
[2025-08-07 18:27:12] [Iter 1012/1050] R2[561/600], Temp: 0.0104, Energy: -55.325060-0.001791j
[2025-08-07 18:27:16] [Iter 1013/1050] R2[562/600], Temp: 0.0099, Energy: -55.352411-0.001812j
[2025-08-07 18:27:20] [Iter 1014/1050] R2[563/600], Temp: 0.0094, Energy: -55.397422+0.002799j
[2025-08-07 18:27:24] [Iter 1015/1050] R2[564/600], Temp: 0.0089, Energy: -55.328141-0.002154j
[2025-08-07 18:27:29] [Iter 1016/1050] R2[565/600], Temp: 0.0084, Energy: -55.434037-0.004073j
[2025-08-07 18:27:33] [Iter 1017/1050] R2[566/600], Temp: 0.0079, Energy: -55.433854-0.004731j
[2025-08-07 18:27:37] [Iter 1018/1050] R2[567/600], Temp: 0.0074, Energy: -55.555259+0.000195j
[2025-08-07 18:27:41] [Iter 1019/1050] R2[568/600], Temp: 0.0070, Energy: -55.402947-0.001734j
[2025-08-07 18:27:45] [Iter 1020/1050] R2[569/600], Temp: 0.0066, Energy: -55.517771+0.002372j
[2025-08-07 18:27:49] [Iter 1021/1050] R2[570/600], Temp: 0.0062, Energy: -55.508104-0.000896j
[2025-08-07 18:27:53] [Iter 1022/1050] R2[571/600], Temp: 0.0058, Energy: -55.558290-0.003954j
[2025-08-07 18:27:58] [Iter 1023/1050] R2[572/600], Temp: 0.0054, Energy: -55.608240-0.000269j
[2025-08-07 18:28:02] [Iter 1024/1050] R2[573/600], Temp: 0.0050, Energy: -55.559193-0.002519j
[2025-08-07 18:28:06] [Iter 1025/1050] R2[574/600], Temp: 0.0046, Energy: -55.553413-0.001425j
[2025-08-07 18:28:10] [Iter 1026/1050] R2[575/600], Temp: 0.0043, Energy: -55.514136+0.000043j
[2025-08-07 18:28:14] [Iter 1027/1050] R2[576/600], Temp: 0.0039, Energy: -55.317701+0.001017j
[2025-08-07 18:28:18] [Iter 1028/1050] R2[577/600], Temp: 0.0036, Energy: -55.400715-0.002941j
[2025-08-07 18:28:23] [Iter 1029/1050] R2[578/600], Temp: 0.0033, Energy: -55.582733-0.003703j
[2025-08-07 18:28:27] [Iter 1030/1050] R2[579/600], Temp: 0.0030, Energy: -55.503022-0.000527j
[2025-08-07 18:28:31] [Iter 1031/1050] R2[580/600], Temp: 0.0027, Energy: -55.460131-0.004254j
[2025-08-07 18:28:35] [Iter 1032/1050] R2[581/600], Temp: 0.0025, Energy: -55.436627-0.005756j
[2025-08-07 18:28:39] [Iter 1033/1050] R2[582/600], Temp: 0.0022, Energy: -55.455287+0.000825j
[2025-08-07 18:28:43] [Iter 1034/1050] R2[583/600], Temp: 0.0020, Energy: -55.457751-0.001844j
[2025-08-07 18:28:47] [Iter 1035/1050] R2[584/600], Temp: 0.0018, Energy: -55.435298+0.000928j
[2025-08-07 18:28:52] [Iter 1036/1050] R2[585/600], Temp: 0.0015, Energy: -55.506318+0.006688j
[2025-08-07 18:28:56] [Iter 1037/1050] R2[586/600], Temp: 0.0013, Energy: -55.573977-0.004714j
[2025-08-07 18:29:00] [Iter 1038/1050] R2[587/600], Temp: 0.0012, Energy: -55.583944-0.000498j
[2025-08-07 18:29:04] [Iter 1039/1050] R2[588/600], Temp: 0.0010, Energy: -55.476932+0.001662j
[2025-08-07 18:29:08] [Iter 1040/1050] R2[589/600], Temp: 0.0008, Energy: -55.467672-0.005473j
[2025-08-07 18:29:12] [Iter 1041/1050] R2[590/600], Temp: 0.0007, Energy: -55.458030-0.003736j
[2025-08-07 18:29:17] [Iter 1042/1050] R2[591/600], Temp: 0.0006, Energy: -55.501019+0.000414j
[2025-08-07 18:29:21] [Iter 1043/1050] R2[592/600], Temp: 0.0004, Energy: -55.445303+0.004580j
[2025-08-07 18:29:25] [Iter 1044/1050] R2[593/600], Temp: 0.0003, Energy: -55.509801+0.003864j
[2025-08-07 18:29:29] [Iter 1045/1050] R2[594/600], Temp: 0.0002, Energy: -55.457202+0.000778j
[2025-08-07 18:29:33] [Iter 1046/1050] R2[595/600], Temp: 0.0002, Energy: -55.428177-0.001429j
[2025-08-07 18:29:37] [Iter 1047/1050] R2[596/600], Temp: 0.0001, Energy: -55.492274-0.000218j
[2025-08-07 18:29:42] [Iter 1048/1050] R2[597/600], Temp: 0.0001, Energy: -55.417944+0.003321j
[2025-08-07 18:29:46] [Iter 1049/1050] R2[598/600], Temp: 0.0000, Energy: -55.467179+0.001995j
[2025-08-07 18:29:50] [Iter 1050/1050] R2[599/600], Temp: 0.0000, Energy: -55.631521+0.003207j
[2025-08-07 18:29:50] ✅ Training completed | Restarts: 2
[2025-08-07 18:29:50] ============================================================
[2025-08-07 18:29:50] Training completed | Runtime: 4397.0s
[2025-08-07 18:30:04] ✓ Final state saved: checkpoints/final_GCNN.pkl
[2025-08-07 18:30:04] ============================================================
