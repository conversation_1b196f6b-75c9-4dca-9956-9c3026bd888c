[2025-08-22 22:53:38] ✓ 从checkpoint恢复: results/L=4/J2=0.00/J1=0.08/training/checkpoints/final_GCNN.pkl
[2025-08-22 22:53:38]   - 迭代次数: final
[2025-08-22 22:53:39]   - 能量: -56.292282+0.001767j ± 0.084626
[2025-08-22 22:53:39]   - 时间戳: 2025-08-22T19:03:02.977980+08:00
[2025-08-22 22:53:48] ✓ 变分状态参数已从checkpoint恢复
[2025-08-22 22:53:48] ✓ 从final状态恢复, 重置迭代计数为0
[2025-08-22 22:53:48] ==================================================
[2025-08-22 22:53:48] GCNN for Shastry-Sutherland Model
[2025-08-22 22:53:48] ==================================================
[2025-08-22 22:53:48] System parameters:
[2025-08-22 22:53:48]   - System size: L=4, N=64
[2025-08-22 22:53:48]   - System parameters: J1=0.09, J2=0.0, Q=1.0
[2025-08-22 22:53:48] --------------------------------------------------
[2025-08-22 22:53:48] Model parameters:
[2025-08-22 22:53:48]   - Number of layers = 4
[2025-08-22 22:53:48]   - Number of features = 4
[2025-08-22 22:53:48]   - Total parameters = 12572
[2025-08-22 22:53:48] --------------------------------------------------
[2025-08-22 22:53:48] Training parameters:
[2025-08-22 22:53:48]   - Learning rate: 0.015
[2025-08-22 22:53:48]   - Total iterations: 1050
[2025-08-22 22:53:48]   - Annealing cycles: 3
[2025-08-22 22:53:48]   - Initial period: 150
[2025-08-22 22:53:48]   - Period multiplier: 2.0
[2025-08-22 22:53:48]   - Temperature range: 0.0-1.0
[2025-08-22 22:53:48]   - Samples: 4096
[2025-08-22 22:53:48]   - Discarded samples: 0
[2025-08-22 22:53:48]   - Chunk size: 2048
[2025-08-22 22:53:48]   - Diagonal shift: 0.2
[2025-08-22 22:53:48]   - Gradient clipping: 1.0
[2025-08-22 22:53:48]   - Checkpoint enabled: interval=100
[2025-08-22 22:53:48]   - Checkpoint directory: results/L=4/J2=0.00/J1=0.09/training/checkpoints
[2025-08-22 22:53:48] --------------------------------------------------
[2025-08-22 22:53:48] Device status:
[2025-08-22 22:53:48]   - Devices model: NVIDIA H200 NVL
[2025-08-22 22:53:48]   - Number of devices: 1
[2025-08-22 22:53:48]   - Sharding: True
[2025-08-22 22:53:48] ============================================================
[2025-08-22 22:54:30] [Iter 1/1050] R0[0/150], Temp: 1.0000, Energy: -56.688408-0.006805j
[2025-08-22 22:54:54] [Iter 2/1050] R0[1/150], Temp: 0.9999, Energy: -56.527166+0.000726j
[2025-08-22 22:54:58] [Iter 3/1050] R0[2/150], Temp: 0.9996, Energy: -56.618119-0.006009j
[2025-08-22 22:55:03] [Iter 4/1050] R0[3/150], Temp: 0.9990, Energy: -56.485778-0.002871j
[2025-08-22 22:55:08] [Iter 5/1050] R0[4/150], Temp: 0.9982, Energy: -56.566457-0.004832j
[2025-08-22 22:55:12] [Iter 6/1050] R0[5/150], Temp: 0.9973, Energy: -56.643871-0.003427j
[2025-08-22 22:55:16] [Iter 7/1050] R0[6/150], Temp: 0.9961, Energy: -56.642775+0.000468j
[2025-08-22 22:55:20] [Iter 8/1050] R0[7/150], Temp: 0.9946, Energy: -56.658708-0.000960j
[2025-08-22 22:55:25] [Iter 9/1050] R0[8/150], Temp: 0.9930, Energy: -56.699706-0.001218j
[2025-08-22 22:55:29] [Iter 10/1050] R0[9/150], Temp: 0.9911, Energy: -56.691898-0.003734j
[2025-08-22 22:55:33] [Iter 11/1050] R0[10/150], Temp: 0.9891, Energy: -56.819352-0.003307j
[2025-08-22 22:55:38] [Iter 12/1050] R0[11/150], Temp: 0.9868, Energy: -56.595347+0.001015j
[2025-08-22 22:55:42] [Iter 13/1050] R0[12/150], Temp: 0.9843, Energy: -56.571671-0.000708j
[2025-08-22 22:55:46] [Iter 14/1050] R0[13/150], Temp: 0.9816, Energy: -56.589854-0.003644j
[2025-08-22 22:55:50] [Iter 15/1050] R0[14/150], Temp: 0.9787, Energy: -56.599373-0.003488j
[2025-08-22 22:55:55] [Iter 16/1050] R0[15/150], Temp: 0.9755, Energy: -56.641967-0.003891j
[2025-08-22 22:55:59] [Iter 17/1050] R0[16/150], Temp: 0.9722, Energy: -56.635773-0.003409j
[2025-08-22 22:56:03] [Iter 18/1050] R0[17/150], Temp: 0.9686, Energy: -56.637359-0.004991j
[2025-08-22 22:56:07] [Iter 19/1050] R0[18/150], Temp: 0.9649, Energy: -56.615384-0.001583j
[2025-08-22 22:56:12] [Iter 20/1050] R0[19/150], Temp: 0.9609, Energy: -56.593682-0.002491j
[2025-08-22 22:56:16] [Iter 21/1050] R0[20/150], Temp: 0.9568, Energy: -56.576097-0.001314j
[2025-08-22 22:56:20] [Iter 22/1050] R0[21/150], Temp: 0.9524, Energy: -56.632954+0.001909j
[2025-08-22 22:56:25] [Iter 23/1050] R0[22/150], Temp: 0.9479, Energy: -56.701424-0.000505j
[2025-08-22 22:56:29] [Iter 24/1050] R0[23/150], Temp: 0.9431, Energy: -56.564523+0.005504j
[2025-08-22 22:56:33] [Iter 25/1050] R0[24/150], Temp: 0.9382, Energy: -56.609264-0.003414j
[2025-08-22 22:56:37] [Iter 26/1050] R0[25/150], Temp: 0.9330, Energy: -56.740439+0.005439j
[2025-08-22 22:56:42] [Iter 27/1050] R0[26/150], Temp: 0.9277, Energy: -56.630703-0.002546j
[2025-08-22 22:56:46] [Iter 28/1050] R0[27/150], Temp: 0.9222, Energy: -56.629634-0.006198j
[2025-08-22 22:56:50] [Iter 29/1050] R0[28/150], Temp: 0.9165, Energy: -56.762533-0.003731j
[2025-08-22 22:56:55] [Iter 30/1050] R0[29/150], Temp: 0.9106, Energy: -56.765266-0.004646j
[2025-08-22 22:56:59] [Iter 31/1050] R0[30/150], Temp: 0.9045, Energy: -56.745049+0.002544j
[2025-08-22 22:57:03] [Iter 32/1050] R0[31/150], Temp: 0.8983, Energy: -56.686938-0.007965j
[2025-08-22 22:57:07] [Iter 33/1050] R0[32/150], Temp: 0.8918, Energy: -56.589363-0.000757j
[2025-08-22 22:57:12] [Iter 34/1050] R0[33/150], Temp: 0.8853, Energy: -56.658354-0.000903j
[2025-08-22 22:57:16] [Iter 35/1050] R0[34/150], Temp: 0.8785, Energy: -56.661089+0.006533j
[2025-08-22 22:57:20] [Iter 36/1050] R0[35/150], Temp: 0.8716, Energy: -56.659972-0.000065j
[2025-08-22 22:57:25] [Iter 37/1050] R0[36/150], Temp: 0.8645, Energy: -56.515922-0.002334j
[2025-08-22 22:57:29] [Iter 38/1050] R0[37/150], Temp: 0.8572, Energy: -56.471311+0.001812j
[2025-08-22 22:57:33] [Iter 39/1050] R0[38/150], Temp: 0.8498, Energy: -56.471885+0.002120j
[2025-08-22 22:57:37] [Iter 40/1050] R0[39/150], Temp: 0.8423, Energy: -56.542558+0.003757j
[2025-08-22 22:57:42] [Iter 41/1050] R0[40/150], Temp: 0.8346, Energy: -56.653900+0.002649j
[2025-08-22 22:57:46] [Iter 42/1050] R0[41/150], Temp: 0.8267, Energy: -56.619397-0.000682j
[2025-08-22 22:57:50] [Iter 43/1050] R0[42/150], Temp: 0.8187, Energy: -56.546270-0.003565j
[2025-08-22 22:57:54] [Iter 44/1050] R0[43/150], Temp: 0.8106, Energy: -56.515335-0.002196j
[2025-08-22 22:57:59] [Iter 45/1050] R0[44/150], Temp: 0.8023, Energy: -56.616065+0.000607j
[2025-08-22 22:58:03] [Iter 46/1050] R0[45/150], Temp: 0.7939, Energy: -56.652870-0.003845j
[2025-08-22 22:58:07] [Iter 47/1050] R0[46/150], Temp: 0.7854, Energy: -56.720565-0.004217j
[2025-08-22 22:58:12] [Iter 48/1050] R0[47/150], Temp: 0.7767, Energy: -56.678169+0.003215j
[2025-08-22 22:58:16] [Iter 49/1050] R0[48/150], Temp: 0.7679, Energy: -56.731630+0.001310j
[2025-08-22 22:58:20] [Iter 50/1050] R0[49/150], Temp: 0.7590, Energy: -56.686314-0.000940j
[2025-08-22 22:58:24] [Iter 51/1050] R0[50/150], Temp: 0.7500, Energy: -56.624002+0.000294j
[2025-08-22 22:58:29] [Iter 52/1050] R0[51/150], Temp: 0.7409, Energy: -56.580148+0.002858j
[2025-08-22 22:58:33] [Iter 53/1050] R0[52/150], Temp: 0.7316, Energy: -56.526598+0.000288j
[2025-08-22 22:58:37] [Iter 54/1050] R0[53/150], Temp: 0.7223, Energy: -56.661073-0.004497j
[2025-08-22 22:58:42] [Iter 55/1050] R0[54/150], Temp: 0.7129, Energy: -56.564029-0.002232j
[2025-08-22 22:58:46] [Iter 56/1050] R0[55/150], Temp: 0.7034, Energy: -56.601668+0.002322j
[2025-08-22 22:58:50] [Iter 57/1050] R0[56/150], Temp: 0.6938, Energy: -56.558854+0.000507j
[2025-08-22 22:58:54] [Iter 58/1050] R0[57/150], Temp: 0.6841, Energy: -56.397594+0.004891j
[2025-08-22 22:58:59] [Iter 59/1050] R0[58/150], Temp: 0.6743, Energy: -56.526859+0.005715j
[2025-08-22 22:59:03] [Iter 60/1050] R0[59/150], Temp: 0.6644, Energy: -56.508800-0.003565j
[2025-08-22 22:59:07] [Iter 61/1050] R0[60/150], Temp: 0.6545, Energy: -56.608878-0.002418j
[2025-08-22 22:59:12] [Iter 62/1050] R0[61/150], Temp: 0.6445, Energy: -56.606266+0.000705j
[2025-08-22 22:59:16] [Iter 63/1050] R0[62/150], Temp: 0.6345, Energy: -56.622434+0.000563j
[2025-08-22 22:59:20] [Iter 64/1050] R0[63/150], Temp: 0.6243, Energy: -56.651530-0.002316j
[2025-08-22 22:59:24] [Iter 65/1050] R0[64/150], Temp: 0.6142, Energy: -56.695246+0.000697j
[2025-08-22 22:59:29] [Iter 66/1050] R0[65/150], Temp: 0.6040, Energy: -56.731230+0.001925j
[2025-08-22 22:59:33] [Iter 67/1050] R0[66/150], Temp: 0.5937, Energy: -56.744300+0.003519j
[2025-08-22 22:59:37] [Iter 68/1050] R0[67/150], Temp: 0.5834, Energy: -56.466275+0.000786j
[2025-08-22 22:59:41] [Iter 69/1050] R0[68/150], Temp: 0.5730, Energy: -56.555059-0.005602j
[2025-08-22 22:59:46] [Iter 70/1050] R0[69/150], Temp: 0.5627, Energy: -56.545819+0.000064j
[2025-08-22 22:59:50] [Iter 71/1050] R0[70/150], Temp: 0.5523, Energy: -56.543197+0.001649j
[2025-08-22 22:59:54] [Iter 72/1050] R0[71/150], Temp: 0.5418, Energy: -56.620474+0.007593j
[2025-08-22 22:59:59] [Iter 73/1050] R0[72/150], Temp: 0.5314, Energy: -56.568255+0.004027j
[2025-08-22 23:00:03] [Iter 74/1050] R0[73/150], Temp: 0.5209, Energy: -56.530685+0.006605j
[2025-08-22 23:00:07] [Iter 75/1050] R0[74/150], Temp: 0.5105, Energy: -56.557119+0.000019j
[2025-08-22 23:00:11] [Iter 76/1050] R0[75/150], Temp: 0.5000, Energy: -56.672415+0.000387j
[2025-08-22 23:00:16] [Iter 77/1050] R0[76/150], Temp: 0.4895, Energy: -56.452963+0.005324j
[2025-08-22 23:00:20] [Iter 78/1050] R0[77/150], Temp: 0.4791, Energy: -56.625814+0.002884j
[2025-08-22 23:00:24] [Iter 79/1050] R0[78/150], Temp: 0.4686, Energy: -56.590722+0.001600j
[2025-08-22 23:00:29] [Iter 80/1050] R0[79/150], Temp: 0.4582, Energy: -56.675262+0.001479j
[2025-08-22 23:00:33] [Iter 81/1050] R0[80/150], Temp: 0.4477, Energy: -56.729817-0.002891j
[2025-08-22 23:00:37] [Iter 82/1050] R0[81/150], Temp: 0.4373, Energy: -56.694315-0.000609j
[2025-08-22 23:00:41] [Iter 83/1050] R0[82/150], Temp: 0.4270, Energy: -56.613972+0.000961j
[2025-08-22 23:00:46] [Iter 84/1050] R0[83/150], Temp: 0.4166, Energy: -56.563414-0.004789j
[2025-08-22 23:00:50] [Iter 85/1050] R0[84/150], Temp: 0.4063, Energy: -56.679079-0.001666j
[2025-08-22 23:00:54] [Iter 86/1050] R0[85/150], Temp: 0.3960, Energy: -56.683029+0.004276j
[2025-08-22 23:00:59] [Iter 87/1050] R0[86/150], Temp: 0.3858, Energy: -56.666325-0.001716j
[2025-08-22 23:01:03] [Iter 88/1050] R0[87/150], Temp: 0.3757, Energy: -56.581798+0.000575j
[2025-08-22 23:01:07] [Iter 89/1050] R0[88/150], Temp: 0.3655, Energy: -56.606998+0.000946j
[2025-08-22 23:01:11] [Iter 90/1050] R0[89/150], Temp: 0.3555, Energy: -56.500541-0.004807j
[2025-08-22 23:01:16] [Iter 91/1050] R0[90/150], Temp: 0.3455, Energy: -56.667293-0.005944j
[2025-08-22 23:01:20] [Iter 92/1050] R0[91/150], Temp: 0.3356, Energy: -56.673095-0.002054j
[2025-08-22 23:01:24] [Iter 93/1050] R0[92/150], Temp: 0.3257, Energy: -56.773780-0.000548j
[2025-08-22 23:01:28] [Iter 94/1050] R0[93/150], Temp: 0.3159, Energy: -56.781127+0.000642j
[2025-08-22 23:01:33] [Iter 95/1050] R0[94/150], Temp: 0.3062, Energy: -56.687613+0.000455j
[2025-08-22 23:01:37] [Iter 96/1050] R0[95/150], Temp: 0.2966, Energy: -56.687434+0.003355j
[2025-08-22 23:01:41] [Iter 97/1050] R0[96/150], Temp: 0.2871, Energy: -56.619046+0.004654j
[2025-08-22 23:01:46] [Iter 98/1050] R0[97/150], Temp: 0.2777, Energy: -56.634441-0.004146j
[2025-08-22 23:01:50] [Iter 99/1050] R0[98/150], Temp: 0.2684, Energy: -56.707733+0.002901j
[2025-08-22 23:01:54] [Iter 100/1050] R0[99/150], Temp: 0.2591, Energy: -56.691134+0.002061j
[2025-08-22 23:01:54] ✓ Checkpoint saved: checkpoint_iter_000100.pkl
[2025-08-22 23:01:58] [Iter 101/1050] R0[100/150], Temp: 0.2500, Energy: -56.644317-0.000793j
[2025-08-22 23:02:03] [Iter 102/1050] R0[101/150], Temp: 0.2410, Energy: -56.674653-0.007554j
[2025-08-22 23:02:07] [Iter 103/1050] R0[102/150], Temp: 0.2321, Energy: -56.633068+0.000293j
[2025-08-22 23:02:11] [Iter 104/1050] R0[103/150], Temp: 0.2233, Energy: -56.660252-0.000087j
[2025-08-22 23:02:16] [Iter 105/1050] R0[104/150], Temp: 0.2146, Energy: -56.561983-0.001660j
[2025-08-22 23:02:20] [Iter 106/1050] R0[105/150], Temp: 0.2061, Energy: -56.716925-0.004543j
[2025-08-22 23:02:24] [Iter 107/1050] R0[106/150], Temp: 0.1977, Energy: -56.589064-0.000926j
[2025-08-22 23:02:28] [Iter 108/1050] R0[107/150], Temp: 0.1894, Energy: -56.647858+0.004437j
[2025-08-22 23:02:33] [Iter 109/1050] R0[108/150], Temp: 0.1813, Energy: -56.446376-0.004534j
[2025-08-22 23:02:37] [Iter 110/1050] R0[109/150], Temp: 0.1733, Energy: -56.511076+0.001140j
[2025-08-22 23:02:41] [Iter 111/1050] R0[110/150], Temp: 0.1654, Energy: -56.601921-0.002131j
[2025-08-22 23:02:46] [Iter 112/1050] R0[111/150], Temp: 0.1577, Energy: -56.550625-0.001944j
[2025-08-22 23:02:50] [Iter 113/1050] R0[112/150], Temp: 0.1502, Energy: -56.631678-0.000012j
[2025-08-22 23:02:54] [Iter 114/1050] R0[113/150], Temp: 0.1428, Energy: -56.666946+0.002218j
[2025-08-22 23:02:58] [Iter 115/1050] R0[114/150], Temp: 0.1355, Energy: -56.716603+0.002841j
[2025-08-22 23:03:03] [Iter 116/1050] R0[115/150], Temp: 0.1284, Energy: -56.658888+0.002809j
[2025-08-22 23:03:07] [Iter 117/1050] R0[116/150], Temp: 0.1215, Energy: -56.591440-0.002703j
[2025-08-22 23:03:11] [Iter 118/1050] R0[117/150], Temp: 0.1147, Energy: -56.592271+0.002093j
[2025-08-22 23:03:16] [Iter 119/1050] R0[118/150], Temp: 0.1082, Energy: -56.519074-0.002143j
[2025-08-22 23:03:20] [Iter 120/1050] R0[119/150], Temp: 0.1017, Energy: -56.531474+0.000183j
[2025-08-22 23:03:24] [Iter 121/1050] R0[120/150], Temp: 0.0955, Energy: -56.512971+0.000173j
[2025-08-22 23:03:28] [Iter 122/1050] R0[121/150], Temp: 0.0894, Energy: -56.442723-0.002282j
[2025-08-22 23:03:33] [Iter 123/1050] R0[122/150], Temp: 0.0835, Energy: -56.604978+0.006885j
[2025-08-22 23:03:37] [Iter 124/1050] R0[123/150], Temp: 0.0778, Energy: -56.660151+0.003365j
[2025-08-22 23:03:41] [Iter 125/1050] R0[124/150], Temp: 0.0723, Energy: -56.643187-0.002344j
[2025-08-22 23:03:45] [Iter 126/1050] R0[125/150], Temp: 0.0670, Energy: -56.593693+0.001582j
[2025-08-22 23:03:50] [Iter 127/1050] R0[126/150], Temp: 0.0618, Energy: -56.656089+0.004773j
[2025-08-22 23:03:54] [Iter 128/1050] R0[127/150], Temp: 0.0569, Energy: -56.608034-0.001289j
[2025-08-22 23:03:58] [Iter 129/1050] R0[128/150], Temp: 0.0521, Energy: -56.617920-0.001161j
[2025-08-22 23:04:03] [Iter 130/1050] R0[129/150], Temp: 0.0476, Energy: -56.684574-0.003021j
[2025-08-22 23:04:07] [Iter 131/1050] R0[130/150], Temp: 0.0432, Energy: -56.752644+0.001634j
[2025-08-22 23:04:11] [Iter 132/1050] R0[131/150], Temp: 0.0391, Energy: -56.637663+0.003672j
[2025-08-22 23:04:15] [Iter 133/1050] R0[132/150], Temp: 0.0351, Energy: -56.497779-0.000596j
[2025-08-22 23:04:20] [Iter 134/1050] R0[133/150], Temp: 0.0314, Energy: -56.680804-0.002127j
[2025-08-22 23:04:24] [Iter 135/1050] R0[134/150], Temp: 0.0278, Energy: -56.545031-0.001876j
[2025-08-22 23:04:28] [Iter 136/1050] R0[135/150], Temp: 0.0245, Energy: -56.574923+0.001680j
[2025-08-22 23:04:33] [Iter 137/1050] R0[136/150], Temp: 0.0213, Energy: -56.491207-0.000576j
[2025-08-22 23:04:37] [Iter 138/1050] R0[137/150], Temp: 0.0184, Energy: -56.331104+0.004085j
[2025-08-22 23:04:41] [Iter 139/1050] R0[138/150], Temp: 0.0157, Energy: -56.411887+0.002144j
[2025-08-22 23:04:45] [Iter 140/1050] R0[139/150], Temp: 0.0132, Energy: -56.449539-0.002495j
[2025-08-22 23:04:50] [Iter 141/1050] R0[140/150], Temp: 0.0109, Energy: -56.556900-0.000682j
[2025-08-22 23:04:54] [Iter 142/1050] R0[141/150], Temp: 0.0089, Energy: -56.734588-0.000121j
[2025-08-22 23:04:58] [Iter 143/1050] R0[142/150], Temp: 0.0070, Energy: -56.482846-0.000217j
[2025-08-22 23:05:03] [Iter 144/1050] R0[143/150], Temp: 0.0054, Energy: -56.580759+0.002564j
[2025-08-22 23:05:07] [Iter 145/1050] R0[144/150], Temp: 0.0039, Energy: -56.519269-0.000179j
[2025-08-22 23:05:11] [Iter 146/1050] R0[145/150], Temp: 0.0027, Energy: -56.627865+0.000298j
[2025-08-22 23:05:15] [Iter 147/1050] R0[146/150], Temp: 0.0018, Energy: -56.603576-0.002359j
[2025-08-22 23:05:20] [Iter 148/1050] R0[147/150], Temp: 0.0010, Energy: -56.550704-0.001161j
[2025-08-22 23:05:24] [Iter 149/1050] R0[148/150], Temp: 0.0004, Energy: -56.620202+0.001545j
[2025-08-22 23:05:28] [Iter 150/1050] R0[149/150], Temp: 0.0001, Energy: -56.466011+0.001746j
[2025-08-22 23:05:28] RESTART #1 | Period: 300
[2025-08-22 23:05:33] [Iter 151/1050] R1[0/300], Temp: 1.0000, Energy: -56.677458-0.002766j
[2025-08-22 23:05:37] [Iter 152/1050] R1[1/300], Temp: 1.0000, Energy: -56.566473+0.001368j
[2025-08-22 23:05:41] [Iter 153/1050] R1[2/300], Temp: 0.9999, Energy: -56.795288-0.003377j
[2025-08-22 23:05:45] [Iter 154/1050] R1[3/300], Temp: 0.9998, Energy: -56.664921-0.000650j
[2025-08-22 23:05:50] [Iter 155/1050] R1[4/300], Temp: 0.9996, Energy: -56.564394-0.003085j
[2025-08-22 23:05:54] [Iter 156/1050] R1[5/300], Temp: 0.9993, Energy: -56.535419+0.000205j
[2025-08-22 23:05:58] [Iter 157/1050] R1[6/300], Temp: 0.9990, Energy: -56.521549+0.002261j
[2025-08-22 23:06:02] [Iter 158/1050] R1[7/300], Temp: 0.9987, Energy: -56.581526+0.001235j
[2025-08-22 23:06:07] [Iter 159/1050] R1[8/300], Temp: 0.9982, Energy: -56.578445+0.000484j
[2025-08-22 23:06:11] [Iter 160/1050] R1[9/300], Temp: 0.9978, Energy: -56.586387-0.002118j
[2025-08-22 23:06:15] [Iter 161/1050] R1[10/300], Temp: 0.9973, Energy: -56.607510-0.003180j
[2025-08-22 23:06:20] [Iter 162/1050] R1[11/300], Temp: 0.9967, Energy: -56.633557+0.000937j
[2025-08-22 23:06:24] [Iter 163/1050] R1[12/300], Temp: 0.9961, Energy: -56.560522-0.001586j
[2025-08-22 23:06:28] [Iter 164/1050] R1[13/300], Temp: 0.9954, Energy: -56.623575+0.001614j
[2025-08-22 23:06:32] [Iter 165/1050] R1[14/300], Temp: 0.9946, Energy: -56.601051+0.004558j
[2025-08-22 23:06:37] [Iter 166/1050] R1[15/300], Temp: 0.9938, Energy: -56.701131-0.004615j
[2025-08-22 23:06:41] [Iter 167/1050] R1[16/300], Temp: 0.9930, Energy: -56.565002-0.004925j
[2025-08-22 23:06:45] [Iter 168/1050] R1[17/300], Temp: 0.9921, Energy: -56.636832-0.001520j
[2025-08-22 23:06:50] [Iter 169/1050] R1[18/300], Temp: 0.9911, Energy: -56.581584-0.000567j
[2025-08-22 23:06:54] [Iter 170/1050] R1[19/300], Temp: 0.9901, Energy: -56.542397+0.000879j
[2025-08-22 23:06:58] [Iter 171/1050] R1[20/300], Temp: 0.9891, Energy: -56.644552-0.002698j
[2025-08-22 23:07:02] [Iter 172/1050] R1[21/300], Temp: 0.9880, Energy: -56.556280+0.004335j
[2025-08-22 23:07:07] [Iter 173/1050] R1[22/300], Temp: 0.9868, Energy: -56.612632+0.001786j
[2025-08-22 23:07:11] [Iter 174/1050] R1[23/300], Temp: 0.9856, Energy: -56.482478+0.001276j
[2025-08-22 23:07:15] [Iter 175/1050] R1[24/300], Temp: 0.9843, Energy: -56.591859-0.000491j
[2025-08-22 23:07:20] [Iter 176/1050] R1[25/300], Temp: 0.9830, Energy: -56.520771-0.000537j
[2025-08-22 23:07:24] [Iter 177/1050] R1[26/300], Temp: 0.9816, Energy: -56.588245+0.000845j
[2025-08-22 23:07:28] [Iter 178/1050] R1[27/300], Temp: 0.9801, Energy: -56.545924+0.001406j
[2025-08-22 23:07:32] [Iter 179/1050] R1[28/300], Temp: 0.9787, Energy: -56.587087-0.001166j
[2025-08-22 23:07:37] [Iter 180/1050] R1[29/300], Temp: 0.9771, Energy: -56.577915+0.001854j
[2025-08-22 23:07:41] [Iter 181/1050] R1[30/300], Temp: 0.9755, Energy: -56.686029-0.000420j
[2025-08-22 23:07:45] [Iter 182/1050] R1[31/300], Temp: 0.9739, Energy: -56.623608-0.002055j
[2025-08-22 23:07:49] [Iter 183/1050] R1[32/300], Temp: 0.9722, Energy: -56.564031+0.000048j
[2025-08-22 23:07:54] [Iter 184/1050] R1[33/300], Temp: 0.9704, Energy: -56.575373+0.001725j
[2025-08-22 23:07:58] [Iter 185/1050] R1[34/300], Temp: 0.9686, Energy: -56.680917-0.000559j
[2025-08-22 23:08:02] [Iter 186/1050] R1[35/300], Temp: 0.9668, Energy: -56.660672+0.001194j
[2025-08-22 23:08:07] [Iter 187/1050] R1[36/300], Temp: 0.9649, Energy: -56.771153+0.005219j
[2025-08-22 23:08:11] [Iter 188/1050] R1[37/300], Temp: 0.9629, Energy: -56.617207+0.000019j
[2025-08-22 23:08:15] [Iter 189/1050] R1[38/300], Temp: 0.9609, Energy: -56.623468-0.000771j
[2025-08-22 23:08:19] [Iter 190/1050] R1[39/300], Temp: 0.9589, Energy: -56.615027+0.002656j
[2025-08-22 23:08:24] [Iter 191/1050] R1[40/300], Temp: 0.9568, Energy: -56.525686+0.003558j
[2025-08-22 23:08:28] [Iter 192/1050] R1[41/300], Temp: 0.9546, Energy: -56.427055+0.000884j
[2025-08-22 23:08:32] [Iter 193/1050] R1[42/300], Temp: 0.9524, Energy: -56.602046+0.001268j
[2025-08-22 23:08:37] [Iter 194/1050] R1[43/300], Temp: 0.9502, Energy: -56.652311-0.003330j
[2025-08-22 23:08:41] [Iter 195/1050] R1[44/300], Temp: 0.9479, Energy: -56.541614+0.004267j
[2025-08-22 23:08:45] [Iter 196/1050] R1[45/300], Temp: 0.9455, Energy: -56.525406-0.002505j
[2025-08-22 23:08:49] [Iter 197/1050] R1[46/300], Temp: 0.9431, Energy: -56.652494-0.003645j
[2025-08-22 23:08:54] [Iter 198/1050] R1[47/300], Temp: 0.9407, Energy: -56.687262-0.001446j
[2025-08-22 23:08:58] [Iter 199/1050] R1[48/300], Temp: 0.9382, Energy: -56.679105+0.002970j
[2025-08-22 23:09:02] [Iter 200/1050] R1[49/300], Temp: 0.9356, Energy: -56.597808-0.003096j
[2025-08-22 23:09:02] ✓ Checkpoint saved: checkpoint_iter_000200.pkl
[2025-08-22 23:09:07] [Iter 201/1050] R1[50/300], Temp: 0.9330, Energy: -56.695778+0.000139j
[2025-08-22 23:09:11] [Iter 202/1050] R1[51/300], Temp: 0.9304, Energy: -56.535481+0.001688j
[2025-08-22 23:09:15] [Iter 203/1050] R1[52/300], Temp: 0.9277, Energy: -56.372359+0.005545j
[2025-08-22 23:09:19] [Iter 204/1050] R1[53/300], Temp: 0.9249, Energy: -56.430025-0.001394j
[2025-08-22 23:09:24] [Iter 205/1050] R1[54/300], Temp: 0.9222, Energy: -56.549427+0.000950j
[2025-08-22 23:09:28] [Iter 206/1050] R1[55/300], Temp: 0.9193, Energy: -56.573714-0.006073j
[2025-08-22 23:09:32] [Iter 207/1050] R1[56/300], Temp: 0.9165, Energy: -56.530325-0.001709j
[2025-08-22 23:09:37] [Iter 208/1050] R1[57/300], Temp: 0.9135, Energy: -56.623900-0.001067j
[2025-08-22 23:09:41] [Iter 209/1050] R1[58/300], Temp: 0.9106, Energy: -56.612067-0.006403j
[2025-08-22 23:09:45] [Iter 210/1050] R1[59/300], Temp: 0.9076, Energy: -56.703581-0.000744j
[2025-08-22 23:09:49] [Iter 211/1050] R1[60/300], Temp: 0.9045, Energy: -56.567465+0.004085j
[2025-08-22 23:09:54] [Iter 212/1050] R1[61/300], Temp: 0.9014, Energy: -56.618931+0.002048j
[2025-08-22 23:09:58] [Iter 213/1050] R1[62/300], Temp: 0.8983, Energy: -56.506015+0.004157j
[2025-08-22 23:10:02] [Iter 214/1050] R1[63/300], Temp: 0.8951, Energy: -56.496025-0.000646j
[2025-08-22 23:10:07] [Iter 215/1050] R1[64/300], Temp: 0.8918, Energy: -56.538699+0.001288j
[2025-08-22 23:10:11] [Iter 216/1050] R1[65/300], Temp: 0.8886, Energy: -56.632333-0.000360j
[2025-08-22 23:10:15] [Iter 217/1050] R1[66/300], Temp: 0.8853, Energy: -56.665329-0.001856j
[2025-08-22 23:10:19] [Iter 218/1050] R1[67/300], Temp: 0.8819, Energy: -56.591080+0.000661j
[2025-08-22 23:10:24] [Iter 219/1050] R1[68/300], Temp: 0.8785, Energy: -56.527880+0.000443j
[2025-08-22 23:10:28] [Iter 220/1050] R1[69/300], Temp: 0.8751, Energy: -56.698411+0.000279j
[2025-08-22 23:10:32] [Iter 221/1050] R1[70/300], Temp: 0.8716, Energy: -56.613928+0.002212j
[2025-08-22 23:10:37] [Iter 222/1050] R1[71/300], Temp: 0.8680, Energy: -56.587735-0.002866j
[2025-08-22 23:10:41] [Iter 223/1050] R1[72/300], Temp: 0.8645, Energy: -56.531040-0.002421j
[2025-08-22 23:10:45] [Iter 224/1050] R1[73/300], Temp: 0.8609, Energy: -56.540129-0.000341j
[2025-08-22 23:10:49] [Iter 225/1050] R1[74/300], Temp: 0.8572, Energy: -56.495251+0.001196j
[2025-08-22 23:10:54] [Iter 226/1050] R1[75/300], Temp: 0.8536, Energy: -56.559565+0.003182j
[2025-08-22 23:10:58] [Iter 227/1050] R1[76/300], Temp: 0.8498, Energy: -56.640545-0.000156j
[2025-08-22 23:11:02] [Iter 228/1050] R1[77/300], Temp: 0.8461, Energy: -56.638143-0.000646j
[2025-08-22 23:11:06] [Iter 229/1050] R1[78/300], Temp: 0.8423, Energy: -56.621103+0.001169j
[2025-08-22 23:11:11] [Iter 230/1050] R1[79/300], Temp: 0.8384, Energy: -56.631575-0.001233j
[2025-08-22 23:11:15] [Iter 231/1050] R1[80/300], Temp: 0.8346, Energy: -56.500568+0.000277j
[2025-08-22 23:11:19] [Iter 232/1050] R1[81/300], Temp: 0.8307, Energy: -56.603818+0.000057j
[2025-08-22 23:11:24] [Iter 233/1050] R1[82/300], Temp: 0.8267, Energy: -56.471730+0.000730j
[2025-08-22 23:11:28] [Iter 234/1050] R1[83/300], Temp: 0.8227, Energy: -56.731530-0.001679j
[2025-08-22 23:11:32] [Iter 235/1050] R1[84/300], Temp: 0.8187, Energy: -56.664211-0.001342j
[2025-08-22 23:11:36] [Iter 236/1050] R1[85/300], Temp: 0.8147, Energy: -56.598464-0.004517j
[2025-08-22 23:11:41] [Iter 237/1050] R1[86/300], Temp: 0.8106, Energy: -56.599603-0.001320j
[2025-08-22 23:11:45] [Iter 238/1050] R1[87/300], Temp: 0.8065, Energy: -56.604241-0.000920j
[2025-08-22 23:11:49] [Iter 239/1050] R1[88/300], Temp: 0.8023, Energy: -56.724997+0.001676j
[2025-08-22 23:11:54] [Iter 240/1050] R1[89/300], Temp: 0.7981, Energy: -56.564298-0.003653j
[2025-08-22 23:11:58] [Iter 241/1050] R1[90/300], Temp: 0.7939, Energy: -56.590503-0.000484j
[2025-08-22 23:12:02] [Iter 242/1050] R1[91/300], Temp: 0.7896, Energy: -56.463750+0.000035j
[2025-08-22 23:12:06] [Iter 243/1050] R1[92/300], Temp: 0.7854, Energy: -56.601638-0.005623j
[2025-08-22 23:12:11] [Iter 244/1050] R1[93/300], Temp: 0.7810, Energy: -56.640195+0.001844j
[2025-08-22 23:12:15] [Iter 245/1050] R1[94/300], Temp: 0.7767, Energy: -56.559001-0.002153j
[2025-08-22 23:12:19] [Iter 246/1050] R1[95/300], Temp: 0.7723, Energy: -56.387145-0.003269j
[2025-08-22 23:12:24] [Iter 247/1050] R1[96/300], Temp: 0.7679, Energy: -56.456055-0.004408j
[2025-08-22 23:12:28] [Iter 248/1050] R1[97/300], Temp: 0.7635, Energy: -56.481160-0.001562j
[2025-08-22 23:12:32] [Iter 249/1050] R1[98/300], Temp: 0.7590, Energy: -56.542177+0.003174j
[2025-08-22 23:12:36] [Iter 250/1050] R1[99/300], Temp: 0.7545, Energy: -56.635301+0.001971j
[2025-08-22 23:12:41] [Iter 251/1050] R1[100/300], Temp: 0.7500, Energy: -56.372131+0.001616j
[2025-08-22 23:12:45] [Iter 252/1050] R1[101/300], Temp: 0.7455, Energy: -56.571070-0.000783j
[2025-08-22 23:12:49] [Iter 253/1050] R1[102/300], Temp: 0.7409, Energy: -56.547559-0.000322j
[2025-08-22 23:12:53] [Iter 254/1050] R1[103/300], Temp: 0.7363, Energy: -56.603318-0.003254j
[2025-08-22 23:12:58] [Iter 255/1050] R1[104/300], Temp: 0.7316, Energy: -56.594766-0.000659j
[2025-08-22 23:13:02] [Iter 256/1050] R1[105/300], Temp: 0.7270, Energy: -56.700723-0.000260j
[2025-08-22 23:13:06] [Iter 257/1050] R1[106/300], Temp: 0.7223, Energy: -56.624113+0.002340j
[2025-08-22 23:13:11] [Iter 258/1050] R1[107/300], Temp: 0.7176, Energy: -56.482936+0.000368j
[2025-08-22 23:13:15] [Iter 259/1050] R1[108/300], Temp: 0.7129, Energy: -56.531672-0.000298j
[2025-08-22 23:13:19] [Iter 260/1050] R1[109/300], Temp: 0.7081, Energy: -56.607812-0.000721j
[2025-08-22 23:13:23] [Iter 261/1050] R1[110/300], Temp: 0.7034, Energy: -56.734313+0.002245j
[2025-08-22 23:13:28] [Iter 262/1050] R1[111/300], Temp: 0.6986, Energy: -56.680621+0.000439j
[2025-08-22 23:13:32] [Iter 263/1050] R1[112/300], Temp: 0.6938, Energy: -56.597512+0.001081j
[2025-08-22 23:13:36] [Iter 264/1050] R1[113/300], Temp: 0.6889, Energy: -56.657737+0.001733j
[2025-08-22 23:13:41] [Iter 265/1050] R1[114/300], Temp: 0.6841, Energy: -56.565201-0.003340j
[2025-08-22 23:13:45] [Iter 266/1050] R1[115/300], Temp: 0.6792, Energy: -56.726558-0.001437j
[2025-08-22 23:13:49] [Iter 267/1050] R1[116/300], Temp: 0.6743, Energy: -56.653264+0.002066j
[2025-08-22 23:13:53] [Iter 268/1050] R1[117/300], Temp: 0.6694, Energy: -56.623389+0.002323j
[2025-08-22 23:13:58] [Iter 269/1050] R1[118/300], Temp: 0.6644, Energy: -56.550170+0.001366j
[2025-08-22 23:14:02] [Iter 270/1050] R1[119/300], Temp: 0.6595, Energy: -56.468259+0.003703j
[2025-08-22 23:14:06] [Iter 271/1050] R1[120/300], Temp: 0.6545, Energy: -56.488087+0.003128j
[2025-08-22 23:14:11] [Iter 272/1050] R1[121/300], Temp: 0.6495, Energy: -56.480933-0.001584j
[2025-08-22 23:14:15] [Iter 273/1050] R1[122/300], Temp: 0.6445, Energy: -56.456077-0.001211j
[2025-08-22 23:14:19] [Iter 274/1050] R1[123/300], Temp: 0.6395, Energy: -56.601589-0.001974j
[2025-08-22 23:14:23] [Iter 275/1050] R1[124/300], Temp: 0.6345, Energy: -56.800918-0.001144j
[2025-08-22 23:14:28] [Iter 276/1050] R1[125/300], Temp: 0.6294, Energy: -56.674189-0.002918j
[2025-08-22 23:14:32] [Iter 277/1050] R1[126/300], Temp: 0.6243, Energy: -56.655275-0.004638j
[2025-08-22 23:14:36] [Iter 278/1050] R1[127/300], Temp: 0.6193, Energy: -56.578276+0.000850j
[2025-08-22 23:14:41] [Iter 279/1050] R1[128/300], Temp: 0.6142, Energy: -56.490613-0.001492j
[2025-08-22 23:14:45] [Iter 280/1050] R1[129/300], Temp: 0.6091, Energy: -56.604473+0.002633j
[2025-08-22 23:14:49] [Iter 281/1050] R1[130/300], Temp: 0.6040, Energy: -56.608607+0.002050j
[2025-08-22 23:14:53] [Iter 282/1050] R1[131/300], Temp: 0.5988, Energy: -56.615568+0.006828j
[2025-08-22 23:14:58] [Iter 283/1050] R1[132/300], Temp: 0.5937, Energy: -56.670197+0.002585j
[2025-08-22 23:15:02] [Iter 284/1050] R1[133/300], Temp: 0.5885, Energy: -56.676986+0.000492j
[2025-08-22 23:15:06] [Iter 285/1050] R1[134/300], Temp: 0.5834, Energy: -56.618545-0.003364j
[2025-08-22 23:15:10] [Iter 286/1050] R1[135/300], Temp: 0.5782, Energy: -56.548361-0.001092j
[2025-08-22 23:15:15] [Iter 287/1050] R1[136/300], Temp: 0.5730, Energy: -56.571491+0.001797j
[2025-08-22 23:15:19] [Iter 288/1050] R1[137/300], Temp: 0.5679, Energy: -56.747227+0.003365j
[2025-08-22 23:15:23] [Iter 289/1050] R1[138/300], Temp: 0.5627, Energy: -56.642460-0.006407j
[2025-08-22 23:15:28] [Iter 290/1050] R1[139/300], Temp: 0.5575, Energy: -56.584476-0.007819j
[2025-08-22 23:15:32] [Iter 291/1050] R1[140/300], Temp: 0.5523, Energy: -56.529261-0.002731j
[2025-08-22 23:15:36] [Iter 292/1050] R1[141/300], Temp: 0.5471, Energy: -56.517621+0.000566j
[2025-08-22 23:15:40] [Iter 293/1050] R1[142/300], Temp: 0.5418, Energy: -56.575524-0.003841j
[2025-08-22 23:15:45] [Iter 294/1050] R1[143/300], Temp: 0.5366, Energy: -56.659833+0.002185j
[2025-08-22 23:15:49] [Iter 295/1050] R1[144/300], Temp: 0.5314, Energy: -56.628634+0.002621j
[2025-08-22 23:15:53] [Iter 296/1050] R1[145/300], Temp: 0.5262, Energy: -56.802556-0.000466j
[2025-08-22 23:15:58] [Iter 297/1050] R1[146/300], Temp: 0.5209, Energy: -56.714951+0.001605j
[2025-08-22 23:16:02] [Iter 298/1050] R1[147/300], Temp: 0.5157, Energy: -56.678990-0.001757j
[2025-08-22 23:16:06] [Iter 299/1050] R1[148/300], Temp: 0.5105, Energy: -56.595795-0.001233j
[2025-08-22 23:16:10] [Iter 300/1050] R1[149/300], Temp: 0.5052, Energy: -56.649916+0.003392j
[2025-08-22 23:16:10] ✓ Checkpoint saved: checkpoint_iter_000300.pkl
[2025-08-22 23:16:15] [Iter 301/1050] R1[150/300], Temp: 0.5000, Energy: -56.691343+0.002481j
[2025-08-22 23:16:19] [Iter 302/1050] R1[151/300], Temp: 0.4948, Energy: -56.668318+0.003115j
[2025-08-22 23:16:23] [Iter 303/1050] R1[152/300], Temp: 0.4895, Energy: -56.768355-0.002205j
[2025-08-22 23:16:28] [Iter 304/1050] R1[153/300], Temp: 0.4843, Energy: -56.640154+0.001504j
[2025-08-22 23:16:32] [Iter 305/1050] R1[154/300], Temp: 0.4791, Energy: -56.630353+0.002578j
[2025-08-22 23:16:36] [Iter 306/1050] R1[155/300], Temp: 0.4738, Energy: -56.429378-0.002503j
[2025-08-22 23:16:40] [Iter 307/1050] R1[156/300], Temp: 0.4686, Energy: -56.547855+0.000377j
[2025-08-22 23:16:45] [Iter 308/1050] R1[157/300], Temp: 0.4634, Energy: -56.586969+0.001125j
[2025-08-22 23:16:49] [Iter 309/1050] R1[158/300], Temp: 0.4582, Energy: -56.589626+0.001118j
[2025-08-22 23:16:53] [Iter 310/1050] R1[159/300], Temp: 0.4529, Energy: -56.533390-0.001571j
[2025-08-22 23:16:57] [Iter 311/1050] R1[160/300], Temp: 0.4477, Energy: -56.513086-0.001519j
[2025-08-22 23:17:02] [Iter 312/1050] R1[161/300], Temp: 0.4425, Energy: -56.490633-0.001547j
[2025-08-22 23:17:06] [Iter 313/1050] R1[162/300], Temp: 0.4373, Energy: -56.485244-0.001334j
[2025-08-22 23:17:10] [Iter 314/1050] R1[163/300], Temp: 0.4321, Energy: -56.519089+0.001553j
[2025-08-22 23:17:15] [Iter 315/1050] R1[164/300], Temp: 0.4270, Energy: -56.644463-0.003530j
[2025-08-22 23:17:19] [Iter 316/1050] R1[165/300], Temp: 0.4218, Energy: -56.500198+0.003638j
[2025-08-22 23:17:23] [Iter 317/1050] R1[166/300], Temp: 0.4166, Energy: -56.634169+0.004170j
[2025-08-22 23:17:27] [Iter 318/1050] R1[167/300], Temp: 0.4115, Energy: -56.474882+0.000123j
[2025-08-22 23:17:32] [Iter 319/1050] R1[168/300], Temp: 0.4063, Energy: -56.590562-0.003810j
[2025-08-22 23:17:36] [Iter 320/1050] R1[169/300], Temp: 0.4012, Energy: -56.603317-0.000368j
[2025-08-22 23:17:40] [Iter 321/1050] R1[170/300], Temp: 0.3960, Energy: -56.598192-0.001174j
[2025-08-22 23:17:45] [Iter 322/1050] R1[171/300], Temp: 0.3909, Energy: -56.723868-0.000038j
[2025-08-22 23:17:49] [Iter 323/1050] R1[172/300], Temp: 0.3858, Energy: -56.739299+0.001860j
[2025-08-22 23:17:53] [Iter 324/1050] R1[173/300], Temp: 0.3807, Energy: -56.726777-0.001703j
[2025-08-22 23:17:57] [Iter 325/1050] R1[174/300], Temp: 0.3757, Energy: -56.508661+0.001725j
[2025-08-22 23:18:02] [Iter 326/1050] R1[175/300], Temp: 0.3706, Energy: -56.696067+0.000222j
[2025-08-22 23:18:06] [Iter 327/1050] R1[176/300], Temp: 0.3655, Energy: -56.767393-0.000044j
[2025-08-22 23:18:10] [Iter 328/1050] R1[177/300], Temp: 0.3605, Energy: -56.618541+0.002043j
[2025-08-22 23:18:14] [Iter 329/1050] R1[178/300], Temp: 0.3555, Energy: -56.560025+0.003675j
[2025-08-22 23:18:19] [Iter 330/1050] R1[179/300], Temp: 0.3505, Energy: -56.542183-0.001121j
[2025-08-22 23:18:23] [Iter 331/1050] R1[180/300], Temp: 0.3455, Energy: -56.703531-0.000128j
[2025-08-22 23:18:27] [Iter 332/1050] R1[181/300], Temp: 0.3405, Energy: -56.718578-0.001902j
[2025-08-22 23:18:32] [Iter 333/1050] R1[182/300], Temp: 0.3356, Energy: -56.657510-0.000078j
[2025-08-22 23:18:36] [Iter 334/1050] R1[183/300], Temp: 0.3306, Energy: -56.692753+0.001839j
[2025-08-22 23:18:40] [Iter 335/1050] R1[184/300], Temp: 0.3257, Energy: -56.678813-0.002499j
[2025-08-22 23:18:44] [Iter 336/1050] R1[185/300], Temp: 0.3208, Energy: -56.612331-0.004085j
[2025-08-22 23:18:49] [Iter 337/1050] R1[186/300], Temp: 0.3159, Energy: -56.638909+0.001200j
[2025-08-22 23:18:53] [Iter 338/1050] R1[187/300], Temp: 0.3111, Energy: -56.682127+0.000689j
[2025-08-22 23:18:57] [Iter 339/1050] R1[188/300], Temp: 0.3062, Energy: -56.585259-0.004592j
[2025-08-22 23:19:02] [Iter 340/1050] R1[189/300], Temp: 0.3014, Energy: -56.577728+0.004051j
[2025-08-22 23:19:06] [Iter 341/1050] R1[190/300], Temp: 0.2966, Energy: -56.597034+0.002804j
[2025-08-22 23:19:10] [Iter 342/1050] R1[191/300], Temp: 0.2919, Energy: -56.558834-0.002077j
[2025-08-22 23:19:14] [Iter 343/1050] R1[192/300], Temp: 0.2871, Energy: -56.703366+0.003972j
[2025-08-22 23:19:19] [Iter 344/1050] R1[193/300], Temp: 0.2824, Energy: -56.746757+0.001263j
[2025-08-22 23:19:23] [Iter 345/1050] R1[194/300], Temp: 0.2777, Energy: -56.702115-0.000261j
[2025-08-22 23:19:27] [Iter 346/1050] R1[195/300], Temp: 0.2730, Energy: -56.723263-0.000081j
[2025-08-22 23:19:31] [Iter 347/1050] R1[196/300], Temp: 0.2684, Energy: -56.587890-0.001112j
[2025-08-22 23:19:36] [Iter 348/1050] R1[197/300], Temp: 0.2637, Energy: -56.665199+0.000824j
[2025-08-22 23:19:40] [Iter 349/1050] R1[198/300], Temp: 0.2591, Energy: -56.535244-0.004249j
[2025-08-22 23:19:44] [Iter 350/1050] R1[199/300], Temp: 0.2545, Energy: -56.571738+0.003400j
[2025-08-22 23:19:49] [Iter 351/1050] R1[200/300], Temp: 0.2500, Energy: -56.708465+0.000132j
[2025-08-22 23:19:53] [Iter 352/1050] R1[201/300], Temp: 0.2455, Energy: -56.680355-0.001101j
[2025-08-22 23:19:57] [Iter 353/1050] R1[202/300], Temp: 0.2410, Energy: -56.605868-0.001919j
[2025-08-22 23:20:01] [Iter 354/1050] R1[203/300], Temp: 0.2365, Energy: -56.582986+0.000355j
[2025-08-22 23:20:06] [Iter 355/1050] R1[204/300], Temp: 0.2321, Energy: -56.645416+0.003356j
[2025-08-22 23:20:10] [Iter 356/1050] R1[205/300], Temp: 0.2277, Energy: -56.602114-0.000253j
[2025-08-22 23:20:14] [Iter 357/1050] R1[206/300], Temp: 0.2233, Energy: -56.555794-0.001471j
[2025-08-22 23:20:19] [Iter 358/1050] R1[207/300], Temp: 0.2190, Energy: -56.462949+0.003695j
[2025-08-22 23:20:23] [Iter 359/1050] R1[208/300], Temp: 0.2146, Energy: -56.670120+0.000239j
[2025-08-22 23:20:27] [Iter 360/1050] R1[209/300], Temp: 0.2104, Energy: -56.707015-0.004998j
[2025-08-22 23:20:31] [Iter 361/1050] R1[210/300], Temp: 0.2061, Energy: -56.545983+0.003240j
[2025-08-22 23:20:36] [Iter 362/1050] R1[211/300], Temp: 0.2019, Energy: -56.650427-0.002013j
[2025-08-22 23:20:40] [Iter 363/1050] R1[212/300], Temp: 0.1977, Energy: -56.641464+0.000009j
[2025-08-22 23:20:44] [Iter 364/1050] R1[213/300], Temp: 0.1935, Energy: -56.673360-0.000757j
[2025-08-22 23:20:48] [Iter 365/1050] R1[214/300], Temp: 0.1894, Energy: -56.734399-0.001106j
[2025-08-22 23:20:53] [Iter 366/1050] R1[215/300], Temp: 0.1853, Energy: -56.699491-0.001732j
[2025-08-22 23:20:57] [Iter 367/1050] R1[216/300], Temp: 0.1813, Energy: -56.733362-0.000078j
[2025-08-22 23:21:01] [Iter 368/1050] R1[217/300], Temp: 0.1773, Energy: -56.608046-0.002112j
[2025-08-22 23:21:06] [Iter 369/1050] R1[218/300], Temp: 0.1733, Energy: -56.636898+0.000179j
[2025-08-22 23:21:10] [Iter 370/1050] R1[219/300], Temp: 0.1693, Energy: -56.545033-0.001350j
[2025-08-22 23:21:14] [Iter 371/1050] R1[220/300], Temp: 0.1654, Energy: -56.671225-0.002430j
[2025-08-22 23:21:18] [Iter 372/1050] R1[221/300], Temp: 0.1616, Energy: -56.582457-0.002548j
[2025-08-22 23:21:23] [Iter 373/1050] R1[222/300], Temp: 0.1577, Energy: -56.716433+0.002690j
[2025-08-22 23:21:27] [Iter 374/1050] R1[223/300], Temp: 0.1539, Energy: -56.694003-0.002587j
[2025-08-22 23:21:31] [Iter 375/1050] R1[224/300], Temp: 0.1502, Energy: -56.599313-0.000283j
[2025-08-22 23:21:36] [Iter 376/1050] R1[225/300], Temp: 0.1464, Energy: -56.588301+0.002672j
[2025-08-22 23:21:40] [Iter 377/1050] R1[226/300], Temp: 0.1428, Energy: -56.656404-0.002121j
[2025-08-22 23:21:44] [Iter 378/1050] R1[227/300], Temp: 0.1391, Energy: -56.565040-0.000029j
[2025-08-22 23:21:48] [Iter 379/1050] R1[228/300], Temp: 0.1355, Energy: -56.625232+0.002474j
[2025-08-22 23:21:53] [Iter 380/1050] R1[229/300], Temp: 0.1320, Energy: -56.705171+0.000269j
[2025-08-22 23:21:57] [Iter 381/1050] R1[230/300], Temp: 0.1284, Energy: -56.580117-0.001734j
[2025-08-22 23:22:01] [Iter 382/1050] R1[231/300], Temp: 0.1249, Energy: -56.660747+0.000367j
[2025-08-22 23:22:05] [Iter 383/1050] R1[232/300], Temp: 0.1215, Energy: -56.588071+0.001714j
[2025-08-22 23:22:10] [Iter 384/1050] R1[233/300], Temp: 0.1181, Energy: -56.534488+0.007066j
[2025-08-22 23:22:14] [Iter 385/1050] R1[234/300], Temp: 0.1147, Energy: -56.512221+0.001262j
[2025-08-22 23:22:18] [Iter 386/1050] R1[235/300], Temp: 0.1114, Energy: -56.620779+0.003399j
[2025-08-22 23:22:23] [Iter 387/1050] R1[236/300], Temp: 0.1082, Energy: -56.698473-0.000188j
[2025-08-22 23:22:27] [Iter 388/1050] R1[237/300], Temp: 0.1049, Energy: -56.646325-0.001296j
[2025-08-22 23:22:31] [Iter 389/1050] R1[238/300], Temp: 0.1017, Energy: -56.842220+0.001216j
[2025-08-22 23:22:35] [Iter 390/1050] R1[239/300], Temp: 0.0986, Energy: -56.622666-0.000298j
[2025-08-22 23:22:40] [Iter 391/1050] R1[240/300], Temp: 0.0955, Energy: -56.597249+0.002367j
[2025-08-22 23:22:44] [Iter 392/1050] R1[241/300], Temp: 0.0924, Energy: -56.542599+0.002463j
[2025-08-22 23:22:48] [Iter 393/1050] R1[242/300], Temp: 0.0894, Energy: -56.580972+0.001725j
[2025-08-22 23:22:53] [Iter 394/1050] R1[243/300], Temp: 0.0865, Energy: -56.452738-0.001858j
[2025-08-22 23:22:57] [Iter 395/1050] R1[244/300], Temp: 0.0835, Energy: -56.627980+0.003846j
[2025-08-22 23:23:01] [Iter 396/1050] R1[245/300], Temp: 0.0807, Energy: -56.546844+0.003270j
[2025-08-22 23:23:05] [Iter 397/1050] R1[246/300], Temp: 0.0778, Energy: -56.614206+0.002944j
[2025-08-22 23:23:10] [Iter 398/1050] R1[247/300], Temp: 0.0751, Energy: -56.658816+0.002318j
[2025-08-22 23:23:14] [Iter 399/1050] R1[248/300], Temp: 0.0723, Energy: -56.874345+0.006462j
[2025-08-22 23:23:18] [Iter 400/1050] R1[249/300], Temp: 0.0696, Energy: -56.703313+0.004427j
[2025-08-22 23:23:18] ✓ Checkpoint saved: checkpoint_iter_000400.pkl
[2025-08-22 23:23:23] [Iter 401/1050] R1[250/300], Temp: 0.0670, Energy: -56.684659-0.001049j
[2025-08-22 23:23:27] [Iter 402/1050] R1[251/300], Temp: 0.0644, Energy: -56.758988-0.000158j
[2025-08-22 23:23:31] [Iter 403/1050] R1[252/300], Temp: 0.0618, Energy: -56.633518+0.002026j
[2025-08-22 23:23:35] [Iter 404/1050] R1[253/300], Temp: 0.0593, Energy: -56.787496-0.001845j
[2025-08-22 23:23:40] [Iter 405/1050] R1[254/300], Temp: 0.0569, Energy: -56.705143+0.001386j
[2025-08-22 23:23:44] [Iter 406/1050] R1[255/300], Temp: 0.0545, Energy: -56.571040-0.001915j
[2025-08-22 23:23:48] [Iter 407/1050] R1[256/300], Temp: 0.0521, Energy: -56.579532-0.002108j
[2025-08-22 23:23:53] [Iter 408/1050] R1[257/300], Temp: 0.0498, Energy: -56.641377+0.003711j
[2025-08-22 23:23:57] [Iter 409/1050] R1[258/300], Temp: 0.0476, Energy: -56.568405+0.005564j
[2025-08-22 23:24:01] [Iter 410/1050] R1[259/300], Temp: 0.0454, Energy: -56.545885+0.000574j
[2025-08-22 23:24:05] [Iter 411/1050] R1[260/300], Temp: 0.0432, Energy: -56.654282-0.002599j
[2025-08-22 23:24:10] [Iter 412/1050] R1[261/300], Temp: 0.0411, Energy: -56.561181-0.000210j
[2025-08-22 23:24:14] [Iter 413/1050] R1[262/300], Temp: 0.0391, Energy: -56.600601+0.002999j
[2025-08-22 23:24:18] [Iter 414/1050] R1[263/300], Temp: 0.0371, Energy: -56.660905+0.000026j
[2025-08-22 23:24:22] [Iter 415/1050] R1[264/300], Temp: 0.0351, Energy: -56.683010-0.002319j
[2025-08-22 23:24:27] [Iter 416/1050] R1[265/300], Temp: 0.0332, Energy: -56.737020+0.003083j
[2025-08-22 23:24:31] [Iter 417/1050] R1[266/300], Temp: 0.0314, Energy: -56.715008+0.001168j
[2025-08-22 23:24:35] [Iter 418/1050] R1[267/300], Temp: 0.0296, Energy: -56.614806+0.000963j
[2025-08-22 23:24:40] [Iter 419/1050] R1[268/300], Temp: 0.0278, Energy: -56.668997+0.001536j
[2025-08-22 23:24:44] [Iter 420/1050] R1[269/300], Temp: 0.0261, Energy: -56.588890-0.003141j
[2025-08-22 23:24:48] [Iter 421/1050] R1[270/300], Temp: 0.0245, Energy: -56.535222-0.000334j
[2025-08-22 23:24:52] [Iter 422/1050] R1[271/300], Temp: 0.0229, Energy: -56.665913+0.002872j
[2025-08-22 23:24:57] [Iter 423/1050] R1[272/300], Temp: 0.0213, Energy: -56.594789-0.001114j
[2025-08-22 23:25:01] [Iter 424/1050] R1[273/300], Temp: 0.0199, Energy: -56.634906+0.002542j
[2025-08-22 23:25:05] [Iter 425/1050] R1[274/300], Temp: 0.0184, Energy: -56.583148+0.003146j
[2025-08-22 23:25:10] [Iter 426/1050] R1[275/300], Temp: 0.0170, Energy: -56.638599+0.001951j
[2025-08-22 23:25:14] [Iter 427/1050] R1[276/300], Temp: 0.0157, Energy: -56.495232+0.000268j
[2025-08-22 23:25:18] [Iter 428/1050] R1[277/300], Temp: 0.0144, Energy: -56.606684+0.000618j
[2025-08-22 23:25:22] [Iter 429/1050] R1[278/300], Temp: 0.0132, Energy: -56.602074-0.001687j
[2025-08-22 23:25:27] [Iter 430/1050] R1[279/300], Temp: 0.0120, Energy: -56.539845+0.003274j
[2025-08-22 23:25:31] [Iter 431/1050] R1[280/300], Temp: 0.0109, Energy: -56.669564+0.002507j
[2025-08-22 23:25:35] [Iter 432/1050] R1[281/300], Temp: 0.0099, Energy: -56.542653-0.001369j
[2025-08-22 23:25:39] [Iter 433/1050] R1[282/300], Temp: 0.0089, Energy: -56.544025+0.006347j
[2025-08-22 23:25:44] [Iter 434/1050] R1[283/300], Temp: 0.0079, Energy: -56.645313+0.000970j
[2025-08-22 23:25:48] [Iter 435/1050] R1[284/300], Temp: 0.0070, Energy: -56.648666+0.002586j
[2025-08-22 23:25:52] [Iter 436/1050] R1[285/300], Temp: 0.0062, Energy: -56.434037+0.000338j
[2025-08-22 23:25:57] [Iter 437/1050] R1[286/300], Temp: 0.0054, Energy: -56.560331-0.000178j
[2025-08-22 23:26:01] [Iter 438/1050] R1[287/300], Temp: 0.0046, Energy: -56.657227+0.001909j
[2025-08-22 23:26:05] [Iter 439/1050] R1[288/300], Temp: 0.0039, Energy: -56.588381+0.002215j
[2025-08-22 23:26:09] [Iter 440/1050] R1[289/300], Temp: 0.0033, Energy: -56.685598-0.000640j
[2025-08-22 23:26:14] [Iter 441/1050] R1[290/300], Temp: 0.0027, Energy: -56.612502+0.001490j
[2025-08-22 23:26:18] [Iter 442/1050] R1[291/300], Temp: 0.0022, Energy: -56.754346-0.002139j
[2025-08-22 23:26:22] [Iter 443/1050] R1[292/300], Temp: 0.0018, Energy: -56.644097-0.002990j
[2025-08-22 23:26:27] [Iter 444/1050] R1[293/300], Temp: 0.0013, Energy: -56.607607-0.001537j
[2025-08-22 23:26:31] [Iter 445/1050] R1[294/300], Temp: 0.0010, Energy: -56.605259-0.001450j
[2025-08-22 23:26:35] [Iter 446/1050] R1[295/300], Temp: 0.0007, Energy: -56.699263+0.001573j
[2025-08-22 23:26:39] [Iter 447/1050] R1[296/300], Temp: 0.0004, Energy: -56.673963+0.002396j
[2025-08-22 23:26:44] [Iter 448/1050] R1[297/300], Temp: 0.0002, Energy: -56.614232+0.002309j
[2025-08-22 23:26:48] [Iter 449/1050] R1[298/300], Temp: 0.0001, Energy: -56.586219-0.004637j
[2025-08-22 23:26:52] [Iter 450/1050] R1[299/300], Temp: 0.0000, Energy: -56.517083+0.000426j
[2025-08-22 23:26:52] RESTART #2 | Period: 600
[2025-08-22 23:26:56] [Iter 451/1050] R2[0/600], Temp: 1.0000, Energy: -56.581370+0.001070j
[2025-08-22 23:27:01] [Iter 452/1050] R2[1/600], Temp: 1.0000, Energy: -56.446107+0.003904j
[2025-08-22 23:27:05] [Iter 453/1050] R2[2/600], Temp: 1.0000, Energy: -56.524976+0.003593j
[2025-08-22 23:27:09] [Iter 454/1050] R2[3/600], Temp: 0.9999, Energy: -56.625312+0.001085j
[2025-08-22 23:27:14] [Iter 455/1050] R2[4/600], Temp: 0.9999, Energy: -56.559791-0.001438j
[2025-08-22 23:27:18] [Iter 456/1050] R2[5/600], Temp: 0.9998, Energy: -56.592171+0.000952j
[2025-08-22 23:27:22] [Iter 457/1050] R2[6/600], Temp: 0.9998, Energy: -56.630593+0.003442j
[2025-08-22 23:27:26] [Iter 458/1050] R2[7/600], Temp: 0.9997, Energy: -56.530151-0.004264j
[2025-08-22 23:27:31] [Iter 459/1050] R2[8/600], Temp: 0.9996, Energy: -56.627732-0.000208j
[2025-08-22 23:27:35] [Iter 460/1050] R2[9/600], Temp: 0.9994, Energy: -56.549836-0.002847j
[2025-08-22 23:27:39] [Iter 461/1050] R2[10/600], Temp: 0.9993, Energy: -56.593451+0.002477j
[2025-08-22 23:27:44] [Iter 462/1050] R2[11/600], Temp: 0.9992, Energy: -56.754617-0.002145j
[2025-08-22 23:27:48] [Iter 463/1050] R2[12/600], Temp: 0.9990, Energy: -56.736850-0.002005j
[2025-08-22 23:27:52] [Iter 464/1050] R2[13/600], Temp: 0.9988, Energy: -56.650032+0.004901j
[2025-08-22 23:27:56] [Iter 465/1050] R2[14/600], Temp: 0.9987, Energy: -56.518121+0.006060j
[2025-08-22 23:28:01] [Iter 466/1050] R2[15/600], Temp: 0.9985, Energy: -56.634678-0.000655j
[2025-08-22 23:28:05] [Iter 467/1050] R2[16/600], Temp: 0.9982, Energy: -56.762077-0.003918j
[2025-08-22 23:28:09] [Iter 468/1050] R2[17/600], Temp: 0.9980, Energy: -56.536547+0.000866j
[2025-08-22 23:28:13] [Iter 469/1050] R2[18/600], Temp: 0.9978, Energy: -56.543527+0.000340j
[2025-08-22 23:28:18] [Iter 470/1050] R2[19/600], Temp: 0.9975, Energy: -56.620335-0.003504j
[2025-08-22 23:28:22] [Iter 471/1050] R2[20/600], Temp: 0.9973, Energy: -56.610714-0.001222j
[2025-08-22 23:28:26] [Iter 472/1050] R2[21/600], Temp: 0.9970, Energy: -56.569831-0.005836j
[2025-08-22 23:28:31] [Iter 473/1050] R2[22/600], Temp: 0.9967, Energy: -56.595490+0.001567j
[2025-08-22 23:28:35] [Iter 474/1050] R2[23/600], Temp: 0.9964, Energy: -56.641814-0.002199j
[2025-08-22 23:28:39] [Iter 475/1050] R2[24/600], Temp: 0.9961, Energy: -56.611635-0.001587j
[2025-08-22 23:28:43] [Iter 476/1050] R2[25/600], Temp: 0.9957, Energy: -56.751231-0.000350j
[2025-08-22 23:28:48] [Iter 477/1050] R2[26/600], Temp: 0.9954, Energy: -56.679534+0.000272j
[2025-08-22 23:28:52] [Iter 478/1050] R2[27/600], Temp: 0.9950, Energy: -56.638447-0.002543j
[2025-08-22 23:28:56] [Iter 479/1050] R2[28/600], Temp: 0.9946, Energy: -56.554889+0.002991j
[2025-08-22 23:29:01] [Iter 480/1050] R2[29/600], Temp: 0.9942, Energy: -56.548739+0.003231j
[2025-08-22 23:29:05] [Iter 481/1050] R2[30/600], Temp: 0.9938, Energy: -56.626405+0.004833j
[2025-08-22 23:29:09] [Iter 482/1050] R2[31/600], Temp: 0.9934, Energy: -56.653470-0.000039j
[2025-08-22 23:29:13] [Iter 483/1050] R2[32/600], Temp: 0.9930, Energy: -56.569728-0.005421j
[2025-08-22 23:29:18] [Iter 484/1050] R2[33/600], Temp: 0.9926, Energy: -56.577931+0.000086j
[2025-08-22 23:29:22] [Iter 485/1050] R2[34/600], Temp: 0.9921, Energy: -56.463420+0.000756j
[2025-08-22 23:29:26] [Iter 486/1050] R2[35/600], Temp: 0.9916, Energy: -56.608267+0.000842j
[2025-08-22 23:29:31] [Iter 487/1050] R2[36/600], Temp: 0.9911, Energy: -56.653084-0.000467j
[2025-08-22 23:29:35] [Iter 488/1050] R2[37/600], Temp: 0.9906, Energy: -56.559371+0.003301j
[2025-08-22 23:29:39] [Iter 489/1050] R2[38/600], Temp: 0.9901, Energy: -56.575490+0.000138j
[2025-08-22 23:29:43] [Iter 490/1050] R2[39/600], Temp: 0.9896, Energy: -56.484716+0.001962j
[2025-08-22 23:29:48] [Iter 491/1050] R2[40/600], Temp: 0.9891, Energy: -56.372936-0.000549j
[2025-08-22 23:29:52] [Iter 492/1050] R2[41/600], Temp: 0.9885, Energy: -56.565168-0.003599j
[2025-08-22 23:29:56] [Iter 493/1050] R2[42/600], Temp: 0.9880, Energy: -56.425695-0.001340j
[2025-08-22 23:30:01] [Iter 494/1050] R2[43/600], Temp: 0.9874, Energy: -56.513797+0.000351j
[2025-08-22 23:30:05] [Iter 495/1050] R2[44/600], Temp: 0.9868, Energy: -56.467140-0.001600j
[2025-08-22 23:30:09] [Iter 496/1050] R2[45/600], Temp: 0.9862, Energy: -56.602837-0.001824j
[2025-08-22 23:30:13] [Iter 497/1050] R2[46/600], Temp: 0.9856, Energy: -56.626104-0.000824j
[2025-08-22 23:30:18] [Iter 498/1050] R2[47/600], Temp: 0.9849, Energy: -56.509069-0.003512j
[2025-08-22 23:30:22] [Iter 499/1050] R2[48/600], Temp: 0.9843, Energy: -56.576145+0.001898j
[2025-08-22 23:30:26] [Iter 500/1050] R2[49/600], Temp: 0.9836, Energy: -56.589797-0.000665j
[2025-08-22 23:30:26] ✓ Checkpoint saved: checkpoint_iter_000500.pkl
[2025-08-22 23:30:30] [Iter 501/1050] R2[50/600], Temp: 0.9830, Energy: -56.567779+0.004685j
[2025-08-22 23:30:35] [Iter 502/1050] R2[51/600], Temp: 0.9823, Energy: -56.601026-0.000153j
[2025-08-22 23:30:39] [Iter 503/1050] R2[52/600], Temp: 0.9816, Energy: -56.565300-0.002513j
[2025-08-22 23:30:43] [Iter 504/1050] R2[53/600], Temp: 0.9809, Energy: -56.794334+0.001639j
[2025-08-22 23:30:48] [Iter 505/1050] R2[54/600], Temp: 0.9801, Energy: -56.604228+0.001781j
[2025-08-22 23:30:52] [Iter 506/1050] R2[55/600], Temp: 0.9794, Energy: -56.664395-0.000352j
[2025-08-22 23:30:56] [Iter 507/1050] R2[56/600], Temp: 0.9787, Energy: -56.667958-0.000356j
[2025-08-22 23:31:01] [Iter 508/1050] R2[57/600], Temp: 0.9779, Energy: -56.638890-0.001308j
[2025-08-22 23:31:05] [Iter 509/1050] R2[58/600], Temp: 0.9771, Energy: -56.668669-0.002545j
[2025-08-22 23:31:09] [Iter 510/1050] R2[59/600], Temp: 0.9763, Energy: -56.587797-0.002732j
[2025-08-22 23:31:14] [Iter 511/1050] R2[60/600], Temp: 0.9755, Energy: -56.518881+0.003065j
[2025-08-22 23:31:18] [Iter 512/1050] R2[61/600], Temp: 0.9747, Energy: -56.510682+0.001615j
[2025-08-22 23:31:22] [Iter 513/1050] R2[62/600], Temp: 0.9739, Energy: -56.614642-0.000539j
[2025-08-22 23:31:26] [Iter 514/1050] R2[63/600], Temp: 0.9730, Energy: -56.640895-0.002847j
[2025-08-22 23:31:31] [Iter 515/1050] R2[64/600], Temp: 0.9722, Energy: -56.679305-0.005978j
[2025-08-22 23:31:35] [Iter 516/1050] R2[65/600], Temp: 0.9713, Energy: -56.641426+0.000287j
[2025-08-22 23:31:39] [Iter 517/1050] R2[66/600], Temp: 0.9704, Energy: -56.669551-0.000088j
[2025-08-22 23:31:43] [Iter 518/1050] R2[67/600], Temp: 0.9695, Energy: -56.555945-0.002572j
[2025-08-22 23:31:48] [Iter 519/1050] R2[68/600], Temp: 0.9686, Energy: -56.612046-0.001963j
[2025-08-22 23:31:52] [Iter 520/1050] R2[69/600], Temp: 0.9677, Energy: -56.554339-0.002214j
[2025-08-22 23:31:56] [Iter 521/1050] R2[70/600], Temp: 0.9668, Energy: -56.629637-0.003632j
[2025-08-22 23:32:01] [Iter 522/1050] R2[71/600], Temp: 0.9658, Energy: -56.571939+0.001094j
[2025-08-22 23:32:05] [Iter 523/1050] R2[72/600], Temp: 0.9649, Energy: -56.541803+0.002221j
[2025-08-22 23:32:09] [Iter 524/1050] R2[73/600], Temp: 0.9639, Energy: -56.539832-0.001159j
[2025-08-22 23:32:13] [Iter 525/1050] R2[74/600], Temp: 0.9629, Energy: -56.530316-0.000714j
[2025-08-22 23:32:18] [Iter 526/1050] R2[75/600], Temp: 0.9619, Energy: -56.563444+0.001489j
[2025-08-22 23:32:22] [Iter 527/1050] R2[76/600], Temp: 0.9609, Energy: -56.612066-0.003640j
[2025-08-22 23:32:26] [Iter 528/1050] R2[77/600], Temp: 0.9599, Energy: -56.649459-0.003717j
[2025-08-22 23:32:31] [Iter 529/1050] R2[78/600], Temp: 0.9589, Energy: -56.503994+0.002893j
[2025-08-22 23:32:35] [Iter 530/1050] R2[79/600], Temp: 0.9578, Energy: -56.575013+0.000967j
[2025-08-22 23:32:39] [Iter 531/1050] R2[80/600], Temp: 0.9568, Energy: -56.582940+0.004407j
[2025-08-22 23:32:43] [Iter 532/1050] R2[81/600], Temp: 0.9557, Energy: -56.595707+0.004059j
[2025-08-22 23:32:48] [Iter 533/1050] R2[82/600], Temp: 0.9546, Energy: -56.658898+0.000770j
[2025-08-22 23:32:52] [Iter 534/1050] R2[83/600], Temp: 0.9535, Energy: -56.476290-0.002421j
[2025-08-22 23:32:56] [Iter 535/1050] R2[84/600], Temp: 0.9524, Energy: -56.507204-0.005427j
[2025-08-22 23:33:00] [Iter 536/1050] R2[85/600], Temp: 0.9513, Energy: -56.518824+0.002859j
[2025-08-22 23:33:05] [Iter 537/1050] R2[86/600], Temp: 0.9502, Energy: -56.664491+0.002442j
[2025-08-22 23:33:09] [Iter 538/1050] R2[87/600], Temp: 0.9490, Energy: -56.545199+0.003096j
[2025-08-22 23:33:13] [Iter 539/1050] R2[88/600], Temp: 0.9479, Energy: -56.580145-0.003534j
[2025-08-22 23:33:18] [Iter 540/1050] R2[89/600], Temp: 0.9467, Energy: -56.552175+0.001768j
[2025-08-22 23:33:22] [Iter 541/1050] R2[90/600], Temp: 0.9455, Energy: -56.553103-0.001225j
[2025-08-22 23:33:26] [Iter 542/1050] R2[91/600], Temp: 0.9443, Energy: -56.546388+0.001158j
[2025-08-22 23:33:30] [Iter 543/1050] R2[92/600], Temp: 0.9431, Energy: -56.533252-0.000376j
[2025-08-22 23:33:35] [Iter 544/1050] R2[93/600], Temp: 0.9419, Energy: -56.507187-0.000143j
[2025-08-22 23:33:39] [Iter 545/1050] R2[94/600], Temp: 0.9407, Energy: -56.552740+0.000994j
[2025-08-22 23:33:43] [Iter 546/1050] R2[95/600], Temp: 0.9394, Energy: -56.565854+0.003302j
[2025-08-22 23:33:48] [Iter 547/1050] R2[96/600], Temp: 0.9382, Energy: -56.606760+0.003857j
[2025-08-22 23:33:52] [Iter 548/1050] R2[97/600], Temp: 0.9369, Energy: -56.576069-0.000830j
[2025-08-22 23:33:56] [Iter 549/1050] R2[98/600], Temp: 0.9356, Energy: -56.578068+0.002783j
[2025-08-22 23:34:00] [Iter 550/1050] R2[99/600], Temp: 0.9343, Energy: -56.560358-0.002037j
[2025-08-22 23:34:05] [Iter 551/1050] R2[100/600], Temp: 0.9330, Energy: -56.632539-0.003096j
[2025-08-22 23:34:09] [Iter 552/1050] R2[101/600], Temp: 0.9317, Energy: -56.586890-0.001335j
[2025-08-22 23:34:13] [Iter 553/1050] R2[102/600], Temp: 0.9304, Energy: -56.623090+0.000200j
[2025-08-22 23:34:18] [Iter 554/1050] R2[103/600], Temp: 0.9290, Energy: -56.651857+0.000289j
[2025-08-22 23:34:22] [Iter 555/1050] R2[104/600], Temp: 0.9277, Energy: -56.667893-0.002162j
[2025-08-22 23:34:27] [Iter 556/1050] R2[105/600], Temp: 0.9263, Energy: -56.609247-0.000264j
[2025-08-22 23:34:31] [Iter 557/1050] R2[106/600], Temp: 0.9249, Energy: -56.676708+0.001361j
[2025-08-22 23:34:35] [Iter 558/1050] R2[107/600], Temp: 0.9236, Energy: -56.759767+0.001837j
[2025-08-22 23:34:40] [Iter 559/1050] R2[108/600], Temp: 0.9222, Energy: -56.711188-0.000658j
[2025-08-22 23:34:44] [Iter 560/1050] R2[109/600], Temp: 0.9208, Energy: -56.649791+0.002668j
[2025-08-22 23:34:48] [Iter 561/1050] R2[110/600], Temp: 0.9193, Energy: -56.536580+0.000493j
[2025-08-22 23:34:52] [Iter 562/1050] R2[111/600], Temp: 0.9179, Energy: -56.552417-0.004157j
[2025-08-22 23:34:57] [Iter 563/1050] R2[112/600], Temp: 0.9165, Energy: -56.541046-0.001315j
[2025-08-22 23:35:01] [Iter 564/1050] R2[113/600], Temp: 0.9150, Energy: -56.581932+0.002698j
[2025-08-22 23:35:05] [Iter 565/1050] R2[114/600], Temp: 0.9135, Energy: -56.570704+0.000139j
[2025-08-22 23:35:10] [Iter 566/1050] R2[115/600], Temp: 0.9121, Energy: -56.666330-0.002412j
[2025-08-22 23:35:14] [Iter 567/1050] R2[116/600], Temp: 0.9106, Energy: -56.653103-0.000973j
[2025-08-22 23:35:18] [Iter 568/1050] R2[117/600], Temp: 0.9091, Energy: -56.683340+0.000354j
[2025-08-22 23:35:22] [Iter 569/1050] R2[118/600], Temp: 0.9076, Energy: -56.530597+0.004953j
[2025-08-22 23:35:27] [Iter 570/1050] R2[119/600], Temp: 0.9060, Energy: -56.543886+0.002233j
[2025-08-22 23:35:31] [Iter 571/1050] R2[120/600], Temp: 0.9045, Energy: -56.639159+0.001746j
[2025-08-22 23:35:35] [Iter 572/1050] R2[121/600], Temp: 0.9030, Energy: -56.591324+0.002077j
[2025-08-22 23:35:40] [Iter 573/1050] R2[122/600], Temp: 0.9014, Energy: -56.552988-0.003030j
[2025-08-22 23:35:44] [Iter 574/1050] R2[123/600], Temp: 0.8998, Energy: -56.628269-0.001477j
[2025-08-22 23:35:48] [Iter 575/1050] R2[124/600], Temp: 0.8983, Energy: -56.680607-0.002248j
[2025-08-22 23:35:52] [Iter 576/1050] R2[125/600], Temp: 0.8967, Energy: -56.605659-0.001012j
[2025-08-22 23:35:57] [Iter 577/1050] R2[126/600], Temp: 0.8951, Energy: -56.611428-0.001669j
[2025-08-22 23:36:01] [Iter 578/1050] R2[127/600], Temp: 0.8935, Energy: -56.518275-0.002897j
[2025-08-22 23:36:05] [Iter 579/1050] R2[128/600], Temp: 0.8918, Energy: -56.407610-0.001731j
[2025-08-22 23:36:10] [Iter 580/1050] R2[129/600], Temp: 0.8902, Energy: -56.471474+0.004244j
[2025-08-22 23:36:14] [Iter 581/1050] R2[130/600], Temp: 0.8886, Energy: -56.477393+0.001724j
[2025-08-22 23:36:18] [Iter 582/1050] R2[131/600], Temp: 0.8869, Energy: -56.490780+0.002131j
[2025-08-22 23:36:22] [Iter 583/1050] R2[132/600], Temp: 0.8853, Energy: -56.572209+0.000453j
[2025-08-22 23:36:27] [Iter 584/1050] R2[133/600], Temp: 0.8836, Energy: -56.666599-0.001351j
[2025-08-22 23:36:31] [Iter 585/1050] R2[134/600], Temp: 0.8819, Energy: -56.601055+0.001083j
[2025-08-22 23:36:35] [Iter 586/1050] R2[135/600], Temp: 0.8802, Energy: -56.610019+0.001655j
[2025-08-22 23:36:40] [Iter 587/1050] R2[136/600], Temp: 0.8785, Energy: -56.602231-0.003564j
[2025-08-22 23:36:44] [Iter 588/1050] R2[137/600], Temp: 0.8768, Energy: -56.725969+0.000602j
[2025-08-22 23:36:48] [Iter 589/1050] R2[138/600], Temp: 0.8751, Energy: -56.585794-0.003901j
[2025-08-22 23:36:52] [Iter 590/1050] R2[139/600], Temp: 0.8733, Energy: -56.686767+0.001432j
[2025-08-22 23:36:57] [Iter 591/1050] R2[140/600], Temp: 0.8716, Energy: -56.581563+0.000677j
[2025-08-22 23:37:01] [Iter 592/1050] R2[141/600], Temp: 0.8698, Energy: -56.560286-0.001799j
[2025-08-22 23:37:05] [Iter 593/1050] R2[142/600], Temp: 0.8680, Energy: -56.516763-0.000485j
[2025-08-22 23:37:10] [Iter 594/1050] R2[143/600], Temp: 0.8663, Energy: -56.488839-0.000526j
[2025-08-22 23:37:14] [Iter 595/1050] R2[144/600], Temp: 0.8645, Energy: -56.426264-0.000582j
[2025-08-22 23:37:18] [Iter 596/1050] R2[145/600], Temp: 0.8627, Energy: -56.565863+0.004599j
[2025-08-22 23:37:22] [Iter 597/1050] R2[146/600], Temp: 0.8609, Energy: -56.634884-0.001109j
[2025-08-22 23:37:27] [Iter 598/1050] R2[147/600], Temp: 0.8591, Energy: -56.607367-0.000279j
[2025-08-22 23:37:31] [Iter 599/1050] R2[148/600], Temp: 0.8572, Energy: -56.575364+0.002636j
[2025-08-22 23:37:35] [Iter 600/1050] R2[149/600], Temp: 0.8554, Energy: -56.480402-0.004162j
[2025-08-22 23:37:35] ✓ Checkpoint saved: checkpoint_iter_000600.pkl
[2025-08-22 23:37:39] [Iter 601/1050] R2[150/600], Temp: 0.8536, Energy: -56.738825-0.001617j
[2025-08-22 23:37:44] [Iter 602/1050] R2[151/600], Temp: 0.8517, Energy: -56.588735+0.001903j
[2025-08-22 23:37:48] [Iter 603/1050] R2[152/600], Temp: 0.8498, Energy: -56.639022+0.001198j
[2025-08-22 23:37:52] [Iter 604/1050] R2[153/600], Temp: 0.8480, Energy: -56.728608+0.004821j
[2025-08-22 23:37:57] [Iter 605/1050] R2[154/600], Temp: 0.8461, Energy: -56.613820-0.002123j
[2025-08-22 23:38:01] [Iter 606/1050] R2[155/600], Temp: 0.8442, Energy: -56.572229+0.001910j
[2025-08-22 23:38:05] [Iter 607/1050] R2[156/600], Temp: 0.8423, Energy: -56.683701+0.000976j
[2025-08-22 23:38:09] [Iter 608/1050] R2[157/600], Temp: 0.8404, Energy: -56.657232+0.000479j
[2025-08-22 23:38:14] [Iter 609/1050] R2[158/600], Temp: 0.8384, Energy: -56.581662+0.004501j
[2025-08-22 23:38:18] [Iter 610/1050] R2[159/600], Temp: 0.8365, Energy: -56.628905-0.001812j
[2025-08-22 23:38:22] [Iter 611/1050] R2[160/600], Temp: 0.8346, Energy: -56.571162+0.002014j
[2025-08-22 23:38:27] [Iter 612/1050] R2[161/600], Temp: 0.8326, Energy: -56.600818+0.003514j
[2025-08-22 23:38:31] [Iter 613/1050] R2[162/600], Temp: 0.8307, Energy: -56.497794+0.002805j
[2025-08-22 23:38:35] [Iter 614/1050] R2[163/600], Temp: 0.8287, Energy: -56.680130+0.004288j
[2025-08-22 23:38:39] [Iter 615/1050] R2[164/600], Temp: 0.8267, Energy: -56.472048+0.004981j
[2025-08-22 23:38:44] [Iter 616/1050] R2[165/600], Temp: 0.8247, Energy: -56.548381+0.003365j
[2025-08-22 23:38:48] [Iter 617/1050] R2[166/600], Temp: 0.8227, Energy: -56.555351-0.000927j
[2025-08-22 23:38:52] [Iter 618/1050] R2[167/600], Temp: 0.8207, Energy: -56.543549+0.000475j
[2025-08-22 23:38:56] [Iter 619/1050] R2[168/600], Temp: 0.8187, Energy: -56.597782-0.001867j
[2025-08-22 23:39:01] [Iter 620/1050] R2[169/600], Temp: 0.8167, Energy: -56.545026+0.000474j
[2025-08-22 23:39:05] [Iter 621/1050] R2[170/600], Temp: 0.8147, Energy: -56.539880+0.000019j
[2025-08-22 23:39:09] [Iter 622/1050] R2[171/600], Temp: 0.8126, Energy: -56.564280-0.000903j
[2025-08-22 23:39:14] [Iter 623/1050] R2[172/600], Temp: 0.8106, Energy: -56.582356-0.001588j
[2025-08-22 23:39:18] [Iter 624/1050] R2[173/600], Temp: 0.8085, Energy: -56.552050-0.001911j
[2025-08-22 23:39:22] [Iter 625/1050] R2[174/600], Temp: 0.8065, Energy: -56.522405+0.002791j
[2025-08-22 23:39:26] [Iter 626/1050] R2[175/600], Temp: 0.8044, Energy: -56.681936+0.000842j
[2025-08-22 23:39:31] [Iter 627/1050] R2[176/600], Temp: 0.8023, Energy: -56.575168+0.002569j
[2025-08-22 23:39:35] [Iter 628/1050] R2[177/600], Temp: 0.8002, Energy: -56.517828-0.002658j
[2025-08-22 23:39:39] [Iter 629/1050] R2[178/600], Temp: 0.7981, Energy: -56.690090+0.005219j
[2025-08-22 23:39:44] [Iter 630/1050] R2[179/600], Temp: 0.7960, Energy: -56.593635+0.002082j
[2025-08-22 23:39:48] [Iter 631/1050] R2[180/600], Temp: 0.7939, Energy: -56.444687-0.002066j
[2025-08-22 23:39:52] [Iter 632/1050] R2[181/600], Temp: 0.7918, Energy: -56.565580+0.000100j
[2025-08-22 23:39:56] [Iter 633/1050] R2[182/600], Temp: 0.7896, Energy: -56.515365-0.004585j
[2025-08-22 23:40:01] [Iter 634/1050] R2[183/600], Temp: 0.7875, Energy: -56.577494-0.002316j
[2025-08-22 23:40:05] [Iter 635/1050] R2[184/600], Temp: 0.7854, Energy: -56.434688-0.004457j
[2025-08-22 23:40:09] [Iter 636/1050] R2[185/600], Temp: 0.7832, Energy: -56.455071+0.001980j
[2025-08-22 23:40:13] [Iter 637/1050] R2[186/600], Temp: 0.7810, Energy: -56.606746+0.001054j
[2025-08-22 23:40:18] [Iter 638/1050] R2[187/600], Temp: 0.7789, Energy: -56.618995+0.000610j
[2025-08-22 23:40:22] [Iter 639/1050] R2[188/600], Temp: 0.7767, Energy: -56.518770-0.002568j
[2025-08-22 23:40:26] [Iter 640/1050] R2[189/600], Temp: 0.7745, Energy: -56.645327+0.001946j
[2025-08-22 23:40:31] [Iter 641/1050] R2[190/600], Temp: 0.7723, Energy: -56.439352+0.000099j
[2025-08-22 23:40:35] [Iter 642/1050] R2[191/600], Temp: 0.7701, Energy: -56.633551+0.005356j
[2025-08-22 23:40:39] [Iter 643/1050] R2[192/600], Temp: 0.7679, Energy: -56.630842+0.002307j
[2025-08-22 23:40:43] [Iter 644/1050] R2[193/600], Temp: 0.7657, Energy: -56.650489-0.000188j
[2025-08-22 23:40:48] [Iter 645/1050] R2[194/600], Temp: 0.7635, Energy: -56.677058-0.004624j
[2025-08-22 23:40:52] [Iter 646/1050] R2[195/600], Temp: 0.7612, Energy: -56.611080-0.000089j
[2025-08-22 23:40:56] [Iter 647/1050] R2[196/600], Temp: 0.7590, Energy: -56.363766+0.002144j
[2025-08-22 23:41:01] [Iter 648/1050] R2[197/600], Temp: 0.7568, Energy: -56.320643+0.001397j
[2025-08-22 23:41:05] [Iter 649/1050] R2[198/600], Temp: 0.7545, Energy: -56.535931-0.001328j
[2025-08-22 23:41:09] [Iter 650/1050] R2[199/600], Temp: 0.7523, Energy: -56.451299+0.000227j
[2025-08-22 23:41:13] [Iter 651/1050] R2[200/600], Temp: 0.7500, Energy: -56.541606+0.000582j
[2025-08-22 23:41:18] [Iter 652/1050] R2[201/600], Temp: 0.7477, Energy: -56.517405+0.000815j
[2025-08-22 23:41:22] [Iter 653/1050] R2[202/600], Temp: 0.7455, Energy: -56.659160+0.001048j
[2025-08-22 23:41:26] [Iter 654/1050] R2[203/600], Temp: 0.7432, Energy: -56.573695-0.002077j
[2025-08-22 23:41:31] [Iter 655/1050] R2[204/600], Temp: 0.7409, Energy: -56.571051-0.002074j
[2025-08-22 23:41:35] [Iter 656/1050] R2[205/600], Temp: 0.7386, Energy: -56.526162-0.002449j
[2025-08-22 23:41:39] [Iter 657/1050] R2[206/600], Temp: 0.7363, Energy: -56.490126+0.000529j
[2025-08-22 23:41:43] [Iter 658/1050] R2[207/600], Temp: 0.7340, Energy: -56.519817-0.001901j
[2025-08-22 23:41:48] [Iter 659/1050] R2[208/600], Temp: 0.7316, Energy: -56.548578-0.001192j
[2025-08-22 23:41:52] [Iter 660/1050] R2[209/600], Temp: 0.7293, Energy: -56.655463-0.004754j
[2025-08-22 23:41:56] [Iter 661/1050] R2[210/600], Temp: 0.7270, Energy: -56.732587-0.000746j
[2025-08-22 23:42:00] [Iter 662/1050] R2[211/600], Temp: 0.7247, Energy: -56.704033+0.002498j
[2025-08-22 23:42:05] [Iter 663/1050] R2[212/600], Temp: 0.7223, Energy: -56.634506+0.001247j
[2025-08-22 23:42:09] [Iter 664/1050] R2[213/600], Temp: 0.7200, Energy: -56.690704-0.002397j
[2025-08-22 23:42:13] [Iter 665/1050] R2[214/600], Temp: 0.7176, Energy: -56.665427-0.000407j
[2025-08-22 23:42:18] [Iter 666/1050] R2[215/600], Temp: 0.7153, Energy: -56.601985-0.003580j
[2025-08-22 23:42:22] [Iter 667/1050] R2[216/600], Temp: 0.7129, Energy: -56.603568+0.000407j
[2025-08-22 23:42:26] [Iter 668/1050] R2[217/600], Temp: 0.7105, Energy: -56.553956+0.001423j
[2025-08-22 23:42:30] [Iter 669/1050] R2[218/600], Temp: 0.7081, Energy: -56.563597+0.002937j
[2025-08-22 23:42:35] [Iter 670/1050] R2[219/600], Temp: 0.7058, Energy: -56.542412-0.001970j
[2025-08-22 23:42:40] [Iter 671/1050] R2[220/600], Temp: 0.7034, Energy: -56.526526+0.002791j
[2025-08-22 23:42:44] [Iter 672/1050] R2[221/600], Temp: 0.7010, Energy: -56.576556-0.008287j
[2025-08-22 23:42:48] [Iter 673/1050] R2[222/600], Temp: 0.6986, Energy: -56.547823+0.000314j
[2025-08-22 23:42:52] [Iter 674/1050] R2[223/600], Temp: 0.6962, Energy: -56.664330+0.003643j
[2025-08-22 23:42:57] [Iter 675/1050] R2[224/600], Temp: 0.6938, Energy: -56.642050-0.001417j
[2025-08-22 23:43:01] [Iter 676/1050] R2[225/600], Temp: 0.6913, Energy: -56.566413+0.001299j
[2025-08-22 23:43:05] [Iter 677/1050] R2[226/600], Temp: 0.6889, Energy: -56.533904-0.001553j
[2025-08-22 23:43:09] [Iter 678/1050] R2[227/600], Temp: 0.6865, Energy: -56.461339-0.000564j
[2025-08-22 23:43:14] [Iter 679/1050] R2[228/600], Temp: 0.6841, Energy: -56.561547+0.006241j
[2025-08-22 23:43:18] [Iter 680/1050] R2[229/600], Temp: 0.6816, Energy: -56.505031-0.000820j
[2025-08-22 23:43:22] [Iter 681/1050] R2[230/600], Temp: 0.6792, Energy: -56.575183+0.002439j
[2025-08-22 23:43:27] [Iter 682/1050] R2[231/600], Temp: 0.6767, Energy: -56.693218-0.002998j
[2025-08-22 23:43:31] [Iter 683/1050] R2[232/600], Temp: 0.6743, Energy: -56.650332-0.000718j
[2025-08-22 23:43:35] [Iter 684/1050] R2[233/600], Temp: 0.6718, Energy: -56.630148+0.002175j
[2025-08-22 23:43:39] [Iter 685/1050] R2[234/600], Temp: 0.6694, Energy: -56.764424+0.000434j
[2025-08-22 23:43:44] [Iter 686/1050] R2[235/600], Temp: 0.6669, Energy: -56.675669-0.004222j
[2025-08-22 23:43:48] [Iter 687/1050] R2[236/600], Temp: 0.6644, Energy: -56.608396-0.001598j
[2025-08-22 23:43:52] [Iter 688/1050] R2[237/600], Temp: 0.6620, Energy: -56.551958+0.002186j
[2025-08-22 23:43:57] [Iter 689/1050] R2[238/600], Temp: 0.6595, Energy: -56.618048+0.000535j
[2025-08-22 23:44:01] [Iter 690/1050] R2[239/600], Temp: 0.6570, Energy: -56.653836+0.008488j
[2025-08-22 23:44:05] [Iter 691/1050] R2[240/600], Temp: 0.6545, Energy: -56.609030+0.002081j
[2025-08-22 23:44:09] [Iter 692/1050] R2[241/600], Temp: 0.6520, Energy: -56.595924+0.000812j
[2025-08-22 23:44:14] [Iter 693/1050] R2[242/600], Temp: 0.6495, Energy: -56.544983-0.003632j
[2025-08-22 23:44:18] [Iter 694/1050] R2[243/600], Temp: 0.6470, Energy: -56.528079+0.000620j
[2025-08-22 23:44:22] [Iter 695/1050] R2[244/600], Temp: 0.6445, Energy: -56.544532+0.003721j
[2025-08-22 23:44:27] [Iter 696/1050] R2[245/600], Temp: 0.6420, Energy: -56.521521+0.001316j
[2025-08-22 23:44:31] [Iter 697/1050] R2[246/600], Temp: 0.6395, Energy: -56.463423-0.004343j
[2025-08-22 23:44:35] [Iter 698/1050] R2[247/600], Temp: 0.6370, Energy: -56.511542-0.000560j
[2025-08-22 23:44:39] [Iter 699/1050] R2[248/600], Temp: 0.6345, Energy: -56.471674+0.000791j
[2025-08-22 23:44:44] [Iter 700/1050] R2[249/600], Temp: 0.6319, Energy: -56.554071+0.000047j
[2025-08-22 23:44:44] ✓ Checkpoint saved: checkpoint_iter_000700.pkl
[2025-08-22 23:44:48] [Iter 701/1050] R2[250/600], Temp: 0.6294, Energy: -56.545121+0.000093j
[2025-08-22 23:44:52] [Iter 702/1050] R2[251/600], Temp: 0.6269, Energy: -56.570305-0.001993j
[2025-08-22 23:44:57] [Iter 703/1050] R2[252/600], Temp: 0.6243, Energy: -56.597329+0.002650j
[2025-08-22 23:45:01] [Iter 704/1050] R2[253/600], Temp: 0.6218, Energy: -56.688020+0.001807j
[2025-08-22 23:45:05] [Iter 705/1050] R2[254/600], Temp: 0.6193, Energy: -56.669441-0.002845j
[2025-08-22 23:45:09] [Iter 706/1050] R2[255/600], Temp: 0.6167, Energy: -56.673832-0.000368j
[2025-08-22 23:45:14] [Iter 707/1050] R2[256/600], Temp: 0.6142, Energy: -56.748128+0.000679j
[2025-08-22 23:45:18] [Iter 708/1050] R2[257/600], Temp: 0.6116, Energy: -56.604406+0.000018j
[2025-08-22 23:45:22] [Iter 709/1050] R2[258/600], Temp: 0.6091, Energy: -56.562168+0.000038j
[2025-08-22 23:45:26] [Iter 710/1050] R2[259/600], Temp: 0.6065, Energy: -56.490671-0.000580j
[2025-08-22 23:45:31] [Iter 711/1050] R2[260/600], Temp: 0.6040, Energy: -56.521527+0.003428j
[2025-08-22 23:45:35] [Iter 712/1050] R2[261/600], Temp: 0.6014, Energy: -56.524846-0.001391j
[2025-08-22 23:45:39] [Iter 713/1050] R2[262/600], Temp: 0.5988, Energy: -56.522976+0.002449j
[2025-08-22 23:45:44] [Iter 714/1050] R2[263/600], Temp: 0.5963, Energy: -56.543650+0.000889j
[2025-08-22 23:45:48] [Iter 715/1050] R2[264/600], Temp: 0.5937, Energy: -56.622039+0.003813j
[2025-08-22 23:45:52] [Iter 716/1050] R2[265/600], Temp: 0.5911, Energy: -56.632726+0.003752j
[2025-08-22 23:45:56] [Iter 717/1050] R2[266/600], Temp: 0.5885, Energy: -56.613506+0.001908j
[2025-08-22 23:46:01] [Iter 718/1050] R2[267/600], Temp: 0.5860, Energy: -56.586601-0.003102j
[2025-08-22 23:46:05] [Iter 719/1050] R2[268/600], Temp: 0.5834, Energy: -56.665516-0.002850j
[2025-08-22 23:46:09] [Iter 720/1050] R2[269/600], Temp: 0.5808, Energy: -56.783552+0.001547j
[2025-08-22 23:46:14] [Iter 721/1050] R2[270/600], Temp: 0.5782, Energy: -56.749760+0.001976j
[2025-08-22 23:46:18] [Iter 722/1050] R2[271/600], Temp: 0.5756, Energy: -56.619499-0.002635j
[2025-08-22 23:46:22] [Iter 723/1050] R2[272/600], Temp: 0.5730, Energy: -56.635346+0.000691j
[2025-08-22 23:46:26] [Iter 724/1050] R2[273/600], Temp: 0.5705, Energy: -56.704405+0.000954j
[2025-08-22 23:46:31] [Iter 725/1050] R2[274/600], Temp: 0.5679, Energy: -56.543455-0.000233j
[2025-08-22 23:46:35] [Iter 726/1050] R2[275/600], Temp: 0.5653, Energy: -56.535683+0.000950j
[2025-08-22 23:46:39] [Iter 727/1050] R2[276/600], Temp: 0.5627, Energy: -56.669923+0.004383j
[2025-08-22 23:46:43] [Iter 728/1050] R2[277/600], Temp: 0.5601, Energy: -56.764592+0.000599j
[2025-08-22 23:46:48] [Iter 729/1050] R2[278/600], Temp: 0.5575, Energy: -56.669205+0.003582j
[2025-08-22 23:46:52] [Iter 730/1050] R2[279/600], Temp: 0.5549, Energy: -56.681281+0.001342j
[2025-08-22 23:46:56] [Iter 731/1050] R2[280/600], Temp: 0.5523, Energy: -56.792943+0.003894j
[2025-08-22 23:47:01] [Iter 732/1050] R2[281/600], Temp: 0.5497, Energy: -56.586300-0.001757j
[2025-08-22 23:47:05] [Iter 733/1050] R2[282/600], Temp: 0.5471, Energy: -56.603392+0.004806j
[2025-08-22 23:47:09] [Iter 734/1050] R2[283/600], Temp: 0.5444, Energy: -56.611029-0.003993j
[2025-08-22 23:47:13] [Iter 735/1050] R2[284/600], Temp: 0.5418, Energy: -56.679561-0.001220j
[2025-08-22 23:47:18] [Iter 736/1050] R2[285/600], Temp: 0.5392, Energy: -56.621249+0.002287j
[2025-08-22 23:47:22] [Iter 737/1050] R2[286/600], Temp: 0.5366, Energy: -56.545357+0.001654j
[2025-08-22 23:47:26] [Iter 738/1050] R2[287/600], Temp: 0.5340, Energy: -56.676960+0.006231j
[2025-08-22 23:47:31] [Iter 739/1050] R2[288/600], Temp: 0.5314, Energy: -56.561580+0.004215j
[2025-08-22 23:47:35] [Iter 740/1050] R2[289/600], Temp: 0.5288, Energy: -56.577779-0.000834j
[2025-08-22 23:47:39] [Iter 741/1050] R2[290/600], Temp: 0.5262, Energy: -56.594169+0.004529j
[2025-08-22 23:47:43] [Iter 742/1050] R2[291/600], Temp: 0.5236, Energy: -56.755239-0.000022j
[2025-08-22 23:47:48] [Iter 743/1050] R2[292/600], Temp: 0.5209, Energy: -56.692954-0.001639j
[2025-08-22 23:47:52] [Iter 744/1050] R2[293/600], Temp: 0.5183, Energy: -56.614965+0.002433j
[2025-08-22 23:47:56] [Iter 745/1050] R2[294/600], Temp: 0.5157, Energy: -56.664277-0.001004j
[2025-08-22 23:48:00] [Iter 746/1050] R2[295/600], Temp: 0.5131, Energy: -56.547605+0.002032j
[2025-08-22 23:48:05] [Iter 747/1050] R2[296/600], Temp: 0.5105, Energy: -56.512710-0.003177j
[2025-08-22 23:48:09] [Iter 748/1050] R2[297/600], Temp: 0.5079, Energy: -56.458188-0.007562j
[2025-08-22 23:48:13] [Iter 749/1050] R2[298/600], Temp: 0.5052, Energy: -56.697969+0.002399j
[2025-08-22 23:48:18] [Iter 750/1050] R2[299/600], Temp: 0.5026, Energy: -56.736555+0.000012j
[2025-08-22 23:48:22] [Iter 751/1050] R2[300/600], Temp: 0.5000, Energy: -56.652079+0.000603j
[2025-08-22 23:48:26] [Iter 752/1050] R2[301/600], Temp: 0.4974, Energy: -56.618660-0.002677j
[2025-08-22 23:48:30] [Iter 753/1050] R2[302/600], Temp: 0.4948, Energy: -56.663611+0.001742j
[2025-08-22 23:48:35] [Iter 754/1050] R2[303/600], Temp: 0.4921, Energy: -56.516748+0.007265j
[2025-08-22 23:48:39] [Iter 755/1050] R2[304/600], Temp: 0.4895, Energy: -56.441396-0.003384j
[2025-08-22 23:48:43] [Iter 756/1050] R2[305/600], Temp: 0.4869, Energy: -56.563642+0.002735j
[2025-08-22 23:48:48] [Iter 757/1050] R2[306/600], Temp: 0.4843, Energy: -56.582866-0.001077j
[2025-08-22 23:48:52] [Iter 758/1050] R2[307/600], Temp: 0.4817, Energy: -56.585935-0.001581j
[2025-08-22 23:48:56] [Iter 759/1050] R2[308/600], Temp: 0.4791, Energy: -56.542090+0.005858j
[2025-08-22 23:49:00] [Iter 760/1050] R2[309/600], Temp: 0.4764, Energy: -56.570760+0.000879j
[2025-08-22 23:49:05] [Iter 761/1050] R2[310/600], Temp: 0.4738, Energy: -56.622929-0.000063j
[2025-08-22 23:49:09] [Iter 762/1050] R2[311/600], Temp: 0.4712, Energy: -56.635614-0.001566j
[2025-08-22 23:49:13] [Iter 763/1050] R2[312/600], Temp: 0.4686, Energy: -56.536661-0.002537j
[2025-08-22 23:49:18] [Iter 764/1050] R2[313/600], Temp: 0.4660, Energy: -56.565738-0.000994j
[2025-08-22 23:49:22] [Iter 765/1050] R2[314/600], Temp: 0.4634, Energy: -56.428267-0.000082j
[2025-08-22 23:49:26] [Iter 766/1050] R2[315/600], Temp: 0.4608, Energy: -56.503730+0.000155j
[2025-08-22 23:49:30] [Iter 767/1050] R2[316/600], Temp: 0.4582, Energy: -56.534939-0.000152j
[2025-08-22 23:49:35] [Iter 768/1050] R2[317/600], Temp: 0.4556, Energy: -56.498741+0.001529j
[2025-08-22 23:49:39] [Iter 769/1050] R2[318/600], Temp: 0.4529, Energy: -56.548000-0.005995j
[2025-08-22 23:49:43] [Iter 770/1050] R2[319/600], Temp: 0.4503, Energy: -56.384218+0.000834j
[2025-08-22 23:49:48] [Iter 771/1050] R2[320/600], Temp: 0.4477, Energy: -56.499816+0.000907j
[2025-08-22 23:49:52] [Iter 772/1050] R2[321/600], Temp: 0.4451, Energy: -56.514529-0.002421j
[2025-08-22 23:49:56] [Iter 773/1050] R2[322/600], Temp: 0.4425, Energy: -56.423456+0.003066j
[2025-08-22 23:50:00] [Iter 774/1050] R2[323/600], Temp: 0.4399, Energy: -56.553645-0.004194j
[2025-08-22 23:50:05] [Iter 775/1050] R2[324/600], Temp: 0.4373, Energy: -56.657274-0.000336j
[2025-08-22 23:50:09] [Iter 776/1050] R2[325/600], Temp: 0.4347, Energy: -56.572447+0.001257j
[2025-08-22 23:50:13] [Iter 777/1050] R2[326/600], Temp: 0.4321, Energy: -56.725850-0.005448j
[2025-08-22 23:50:18] [Iter 778/1050] R2[327/600], Temp: 0.4295, Energy: -56.593127+0.003655j
[2025-08-22 23:50:22] [Iter 779/1050] R2[328/600], Temp: 0.4270, Energy: -56.570837+0.006428j
[2025-08-22 23:50:26] [Iter 780/1050] R2[329/600], Temp: 0.4244, Energy: -56.573560-0.001601j
[2025-08-22 23:50:30] [Iter 781/1050] R2[330/600], Temp: 0.4218, Energy: -56.616655-0.001385j
[2025-08-22 23:50:35] [Iter 782/1050] R2[331/600], Temp: 0.4192, Energy: -56.716341+0.003697j
[2025-08-22 23:50:39] [Iter 783/1050] R2[332/600], Temp: 0.4166, Energy: -56.729198+0.001700j
[2025-08-22 23:50:43] [Iter 784/1050] R2[333/600], Temp: 0.4140, Energy: -56.708471-0.001920j
[2025-08-22 23:50:47] [Iter 785/1050] R2[334/600], Temp: 0.4115, Energy: -56.539974+0.001272j
[2025-08-22 23:50:52] [Iter 786/1050] R2[335/600], Temp: 0.4089, Energy: -56.556082+0.003066j
[2025-08-22 23:50:56] [Iter 787/1050] R2[336/600], Temp: 0.4063, Energy: -56.659040-0.002065j
[2025-08-22 23:51:00] [Iter 788/1050] R2[337/600], Temp: 0.4037, Energy: -56.528976-0.004895j
[2025-08-22 23:51:05] [Iter 789/1050] R2[338/600], Temp: 0.4012, Energy: -56.571364+0.002353j
[2025-08-22 23:51:09] [Iter 790/1050] R2[339/600], Temp: 0.3986, Energy: -56.482431-0.001237j
[2025-08-22 23:51:13] [Iter 791/1050] R2[340/600], Temp: 0.3960, Energy: -56.505898+0.003677j
[2025-08-22 23:51:17] [Iter 792/1050] R2[341/600], Temp: 0.3935, Energy: -56.666303-0.000192j
[2025-08-22 23:51:22] [Iter 793/1050] R2[342/600], Temp: 0.3909, Energy: -56.663631-0.001272j
[2025-08-22 23:51:26] [Iter 794/1050] R2[343/600], Temp: 0.3884, Energy: -56.697135-0.001562j
[2025-08-22 23:51:30] [Iter 795/1050] R2[344/600], Temp: 0.3858, Energy: -56.685691+0.000878j
[2025-08-22 23:51:35] [Iter 796/1050] R2[345/600], Temp: 0.3833, Energy: -56.651743+0.002200j
[2025-08-22 23:51:39] [Iter 797/1050] R2[346/600], Temp: 0.3807, Energy: -56.611742-0.000703j
[2025-08-22 23:51:43] [Iter 798/1050] R2[347/600], Temp: 0.3782, Energy: -56.538565+0.000752j
[2025-08-22 23:51:47] [Iter 799/1050] R2[348/600], Temp: 0.3757, Energy: -56.520306+0.001404j
[2025-08-22 23:51:52] [Iter 800/1050] R2[349/600], Temp: 0.3731, Energy: -56.524798+0.004332j
[2025-08-22 23:51:52] ✓ Checkpoint saved: checkpoint_iter_000800.pkl
[2025-08-22 23:51:56] [Iter 801/1050] R2[350/600], Temp: 0.3706, Energy: -56.551319-0.003236j
[2025-08-22 23:52:00] [Iter 802/1050] R2[351/600], Temp: 0.3681, Energy: -56.568284-0.002214j
[2025-08-22 23:52:05] [Iter 803/1050] R2[352/600], Temp: 0.3655, Energy: -56.544519+0.000978j
[2025-08-22 23:52:09] [Iter 804/1050] R2[353/600], Temp: 0.3630, Energy: -56.524406-0.000718j
[2025-08-22 23:52:13] [Iter 805/1050] R2[354/600], Temp: 0.3605, Energy: -56.610862+0.003783j
[2025-08-22 23:52:17] [Iter 806/1050] R2[355/600], Temp: 0.3580, Energy: -56.564118-0.002883j
[2025-08-22 23:52:22] [Iter 807/1050] R2[356/600], Temp: 0.3555, Energy: -56.498415-0.002897j
[2025-08-22 23:52:26] [Iter 808/1050] R2[357/600], Temp: 0.3530, Energy: -56.493686-0.001325j
[2025-08-22 23:52:30] [Iter 809/1050] R2[358/600], Temp: 0.3505, Energy: -56.459435-0.000050j
[2025-08-22 23:52:34] [Iter 810/1050] R2[359/600], Temp: 0.3480, Energy: -56.432362+0.002193j
[2025-08-22 23:52:39] [Iter 811/1050] R2[360/600], Temp: 0.3455, Energy: -56.721141-0.000750j
[2025-08-22 23:52:43] [Iter 812/1050] R2[361/600], Temp: 0.3430, Energy: -56.727626+0.004769j
[2025-08-22 23:52:47] [Iter 813/1050] R2[362/600], Temp: 0.3405, Energy: -56.722767+0.002846j
[2025-08-22 23:52:52] [Iter 814/1050] R2[363/600], Temp: 0.3380, Energy: -56.734624-0.002649j
[2025-08-22 23:52:56] [Iter 815/1050] R2[364/600], Temp: 0.3356, Energy: -56.801916+0.003689j
[2025-08-22 23:53:00] [Iter 816/1050] R2[365/600], Temp: 0.3331, Energy: -56.615380-0.000196j
[2025-08-22 23:53:04] [Iter 817/1050] R2[366/600], Temp: 0.3306, Energy: -56.758765+0.001800j
[2025-08-22 23:53:09] [Iter 818/1050] R2[367/600], Temp: 0.3282, Energy: -56.785699+0.001983j
[2025-08-22 23:53:13] [Iter 819/1050] R2[368/600], Temp: 0.3257, Energy: -56.614681+0.001304j
[2025-08-22 23:53:17] [Iter 820/1050] R2[369/600], Temp: 0.3233, Energy: -56.466458-0.001235j
[2025-08-22 23:53:22] [Iter 821/1050] R2[370/600], Temp: 0.3208, Energy: -56.551951+0.006269j
[2025-08-22 23:53:26] [Iter 822/1050] R2[371/600], Temp: 0.3184, Energy: -56.699826+0.000929j
[2025-08-22 23:53:30] [Iter 823/1050] R2[372/600], Temp: 0.3159, Energy: -56.721198+0.003909j
[2025-08-22 23:53:34] [Iter 824/1050] R2[373/600], Temp: 0.3135, Energy: -56.715354+0.001473j
[2025-08-22 23:53:39] [Iter 825/1050] R2[374/600], Temp: 0.3111, Energy: -56.617933-0.000010j
[2025-08-22 23:53:43] [Iter 826/1050] R2[375/600], Temp: 0.3087, Energy: -56.626807-0.001302j
[2025-08-22 23:53:47] [Iter 827/1050] R2[376/600], Temp: 0.3062, Energy: -56.705696+0.003560j
[2025-08-22 23:53:51] [Iter 828/1050] R2[377/600], Temp: 0.3038, Energy: -56.626415-0.002370j
[2025-08-22 23:53:56] [Iter 829/1050] R2[378/600], Temp: 0.3014, Energy: -56.518419+0.001491j
[2025-08-22 23:54:00] [Iter 830/1050] R2[379/600], Temp: 0.2990, Energy: -56.589651+0.003781j
[2025-08-22 23:54:04] [Iter 831/1050] R2[380/600], Temp: 0.2966, Energy: -56.588397+0.000282j
[2025-08-22 23:54:09] [Iter 832/1050] R2[381/600], Temp: 0.2942, Energy: -56.604953-0.000764j
[2025-08-22 23:54:13] [Iter 833/1050] R2[382/600], Temp: 0.2919, Energy: -56.662164+0.000097j
[2025-08-22 23:54:17] [Iter 834/1050] R2[383/600], Temp: 0.2895, Energy: -56.632280-0.003081j
[2025-08-22 23:54:21] [Iter 835/1050] R2[384/600], Temp: 0.2871, Energy: -56.617867-0.006573j
[2025-08-22 23:54:26] [Iter 836/1050] R2[385/600], Temp: 0.2847, Energy: -56.723595+0.008366j
[2025-08-22 23:54:30] [Iter 837/1050] R2[386/600], Temp: 0.2824, Energy: -56.801991+0.000718j
[2025-08-22 23:54:34] [Iter 838/1050] R2[387/600], Temp: 0.2800, Energy: -56.666650+0.003075j
[2025-08-22 23:54:39] [Iter 839/1050] R2[388/600], Temp: 0.2777, Energy: -56.762294+0.000059j
[2025-08-22 23:54:43] [Iter 840/1050] R2[389/600], Temp: 0.2753, Energy: -56.647004+0.000163j
[2025-08-22 23:54:47] [Iter 841/1050] R2[390/600], Temp: 0.2730, Energy: -56.804386+0.000798j
[2025-08-22 23:54:51] [Iter 842/1050] R2[391/600], Temp: 0.2707, Energy: -56.694361-0.006136j
[2025-08-22 23:54:56] [Iter 843/1050] R2[392/600], Temp: 0.2684, Energy: -56.583769-0.005009j
[2025-08-22 23:55:00] [Iter 844/1050] R2[393/600], Temp: 0.2660, Energy: -56.788397+0.000342j
[2025-08-22 23:55:04] [Iter 845/1050] R2[394/600], Temp: 0.2637, Energy: -56.609711+0.002269j
[2025-08-22 23:55:09] [Iter 846/1050] R2[395/600], Temp: 0.2614, Energy: -56.612341-0.003216j
[2025-08-22 23:55:13] [Iter 847/1050] R2[396/600], Temp: 0.2591, Energy: -56.666867-0.000605j
[2025-08-22 23:55:17] [Iter 848/1050] R2[397/600], Temp: 0.2568, Energy: -56.683251-0.003948j
[2025-08-22 23:55:21] [Iter 849/1050] R2[398/600], Temp: 0.2545, Energy: -56.566390+0.001910j
[2025-08-22 23:55:26] [Iter 850/1050] R2[399/600], Temp: 0.2523, Energy: -56.530479-0.000676j
[2025-08-22 23:55:30] [Iter 851/1050] R2[400/600], Temp: 0.2500, Energy: -56.585487+0.000218j
[2025-08-22 23:55:34] [Iter 852/1050] R2[401/600], Temp: 0.2477, Energy: -56.538388-0.001933j
[2025-08-22 23:55:38] [Iter 853/1050] R2[402/600], Temp: 0.2455, Energy: -56.634477+0.002498j
[2025-08-22 23:55:43] [Iter 854/1050] R2[403/600], Temp: 0.2432, Energy: -56.629220+0.002116j
[2025-08-22 23:55:47] [Iter 855/1050] R2[404/600], Temp: 0.2410, Energy: -56.588405-0.000336j
[2025-08-22 23:55:51] [Iter 856/1050] R2[405/600], Temp: 0.2388, Energy: -56.647950+0.000110j
[2025-08-22 23:55:56] [Iter 857/1050] R2[406/600], Temp: 0.2365, Energy: -56.695619-0.000547j
[2025-08-22 23:56:00] [Iter 858/1050] R2[407/600], Temp: 0.2343, Energy: -56.749880-0.000097j
[2025-08-22 23:56:04] [Iter 859/1050] R2[408/600], Temp: 0.2321, Energy: -56.676274-0.000641j
[2025-08-22 23:56:08] [Iter 860/1050] R2[409/600], Temp: 0.2299, Energy: -56.508335+0.005799j
[2025-08-22 23:56:13] [Iter 861/1050] R2[410/600], Temp: 0.2277, Energy: -56.606967-0.003139j
[2025-08-22 23:56:17] [Iter 862/1050] R2[411/600], Temp: 0.2255, Energy: -56.577967-0.002903j
[2025-08-22 23:56:21] [Iter 863/1050] R2[412/600], Temp: 0.2233, Energy: -56.549605+0.005476j
[2025-08-22 23:56:26] [Iter 864/1050] R2[413/600], Temp: 0.2211, Energy: -56.493416-0.000521j
[2025-08-22 23:56:30] [Iter 865/1050] R2[414/600], Temp: 0.2190, Energy: -56.512199-0.000089j
[2025-08-22 23:56:34] [Iter 866/1050] R2[415/600], Temp: 0.2168, Energy: -56.639724+0.001243j
[2025-08-22 23:56:38] [Iter 867/1050] R2[416/600], Temp: 0.2146, Energy: -56.668302+0.001345j
[2025-08-22 23:56:43] [Iter 868/1050] R2[417/600], Temp: 0.2125, Energy: -56.565503+0.001699j
[2025-08-22 23:56:47] [Iter 869/1050] R2[418/600], Temp: 0.2104, Energy: -56.679829+0.000781j
[2025-08-22 23:56:51] [Iter 870/1050] R2[419/600], Temp: 0.2082, Energy: -56.559825+0.004001j
[2025-08-22 23:56:56] [Iter 871/1050] R2[420/600], Temp: 0.2061, Energy: -56.478281-0.001761j
[2025-08-22 23:57:00] [Iter 872/1050] R2[421/600], Temp: 0.2040, Energy: -56.626643+0.000790j
[2025-08-22 23:57:04] [Iter 873/1050] R2[422/600], Temp: 0.2019, Energy: -56.696794+0.003193j
[2025-08-22 23:57:08] [Iter 874/1050] R2[423/600], Temp: 0.1998, Energy: -56.629417+0.000424j
[2025-08-22 23:57:13] [Iter 875/1050] R2[424/600], Temp: 0.1977, Energy: -56.616164+0.003115j
[2025-08-22 23:57:17] [Iter 876/1050] R2[425/600], Temp: 0.1956, Energy: -56.620918+0.002607j
[2025-08-22 23:57:21] [Iter 877/1050] R2[426/600], Temp: 0.1935, Energy: -56.611079-0.000220j
[2025-08-22 23:57:25] [Iter 878/1050] R2[427/600], Temp: 0.1915, Energy: -56.620226+0.002930j
[2025-08-22 23:57:30] [Iter 879/1050] R2[428/600], Temp: 0.1894, Energy: -56.586217-0.002269j
[2025-08-22 23:57:34] [Iter 880/1050] R2[429/600], Temp: 0.1874, Energy: -56.574638+0.000631j
[2025-08-22 23:57:38] [Iter 881/1050] R2[430/600], Temp: 0.1853, Energy: -56.706767+0.004114j
[2025-08-22 23:57:43] [Iter 882/1050] R2[431/600], Temp: 0.1833, Energy: -56.648008-0.000043j
[2025-08-22 23:57:47] [Iter 883/1050] R2[432/600], Temp: 0.1813, Energy: -56.534799-0.002812j
[2025-08-22 23:57:51] [Iter 884/1050] R2[433/600], Temp: 0.1793, Energy: -56.531598+0.002026j
[2025-08-22 23:57:55] [Iter 885/1050] R2[434/600], Temp: 0.1773, Energy: -56.551599-0.000512j
[2025-08-22 23:58:00] [Iter 886/1050] R2[435/600], Temp: 0.1753, Energy: -56.639374+0.001148j
[2025-08-22 23:58:04] [Iter 887/1050] R2[436/600], Temp: 0.1733, Energy: -56.735095-0.001560j
[2025-08-22 23:58:08] [Iter 888/1050] R2[437/600], Temp: 0.1713, Energy: -56.648141-0.001005j
[2025-08-22 23:58:13] [Iter 889/1050] R2[438/600], Temp: 0.1693, Energy: -56.754552-0.002257j
[2025-08-22 23:58:17] [Iter 890/1050] R2[439/600], Temp: 0.1674, Energy: -56.666694-0.000507j
[2025-08-22 23:58:21] [Iter 891/1050] R2[440/600], Temp: 0.1654, Energy: -56.788378-0.000790j
[2025-08-22 23:58:25] [Iter 892/1050] R2[441/600], Temp: 0.1635, Energy: -56.567991-0.002401j
[2025-08-22 23:58:30] [Iter 893/1050] R2[442/600], Temp: 0.1616, Energy: -56.648455-0.000817j
[2025-08-22 23:58:34] [Iter 894/1050] R2[443/600], Temp: 0.1596, Energy: -56.672627-0.001299j
[2025-08-22 23:58:38] [Iter 895/1050] R2[444/600], Temp: 0.1577, Energy: -56.577472-0.002465j
[2025-08-22 23:58:42] [Iter 896/1050] R2[445/600], Temp: 0.1558, Energy: -56.623254-0.005021j
[2025-08-22 23:58:47] [Iter 897/1050] R2[446/600], Temp: 0.1539, Energy: -56.735383-0.003732j
[2025-08-22 23:58:51] [Iter 898/1050] R2[447/600], Temp: 0.1520, Energy: -56.699630-0.005627j
[2025-08-22 23:58:55] [Iter 899/1050] R2[448/600], Temp: 0.1502, Energy: -56.678054+0.001987j
[2025-08-22 23:59:00] [Iter 900/1050] R2[449/600], Temp: 0.1483, Energy: -56.561587-0.001229j
[2025-08-22 23:59:00] ✓ Checkpoint saved: checkpoint_iter_000900.pkl
[2025-08-22 23:59:04] [Iter 901/1050] R2[450/600], Temp: 0.1464, Energy: -56.616695-0.002481j
[2025-08-22 23:59:08] [Iter 902/1050] R2[451/600], Temp: 0.1446, Energy: -56.589853-0.000279j
[2025-08-22 23:59:12] [Iter 903/1050] R2[452/600], Temp: 0.1428, Energy: -56.535764-0.006375j
[2025-08-22 23:59:17] [Iter 904/1050] R2[453/600], Temp: 0.1409, Energy: -56.499448+0.003821j
[2025-08-22 23:59:21] [Iter 905/1050] R2[454/600], Temp: 0.1391, Energy: -56.573198+0.003131j
[2025-08-22 23:59:25] [Iter 906/1050] R2[455/600], Temp: 0.1373, Energy: -56.561347-0.000291j
[2025-08-22 23:59:30] [Iter 907/1050] R2[456/600], Temp: 0.1355, Energy: -56.616525-0.002557j
[2025-08-22 23:59:34] [Iter 908/1050] R2[457/600], Temp: 0.1337, Energy: -56.502796+0.002240j
[2025-08-22 23:59:38] [Iter 909/1050] R2[458/600], Temp: 0.1320, Energy: -56.526307-0.001768j
[2025-08-22 23:59:42] [Iter 910/1050] R2[459/600], Temp: 0.1302, Energy: -56.552760-0.001087j
[2025-08-22 23:59:47] [Iter 911/1050] R2[460/600], Temp: 0.1284, Energy: -56.629179+0.002322j
[2025-08-22 23:59:51] [Iter 912/1050] R2[461/600], Temp: 0.1267, Energy: -56.708124-0.000021j
[2025-08-22 23:59:55] [Iter 913/1050] R2[462/600], Temp: 0.1249, Energy: -56.706702+0.001301j
[2025-08-23 00:00:00] [Iter 914/1050] R2[463/600], Temp: 0.1232, Energy: -56.600609-0.000503j
[2025-08-23 00:00:04] [Iter 915/1050] R2[464/600], Temp: 0.1215, Energy: -56.532605-0.001268j
[2025-08-23 00:00:08] [Iter 916/1050] R2[465/600], Temp: 0.1198, Energy: -56.666927+0.001305j
[2025-08-23 00:00:12] [Iter 917/1050] R2[466/600], Temp: 0.1181, Energy: -56.768136+0.000198j
[2025-08-23 00:00:17] [Iter 918/1050] R2[467/600], Temp: 0.1164, Energy: -56.686513+0.002454j
[2025-08-23 00:00:21] [Iter 919/1050] R2[468/600], Temp: 0.1147, Energy: -56.606454-0.001809j
[2025-08-23 00:00:25] [Iter 920/1050] R2[469/600], Temp: 0.1131, Energy: -56.570426+0.001464j
[2025-08-23 00:00:29] [Iter 921/1050] R2[470/600], Temp: 0.1114, Energy: -56.507578-0.002265j
[2025-08-23 00:00:34] [Iter 922/1050] R2[471/600], Temp: 0.1098, Energy: -56.342532-0.003962j
[2025-08-23 00:00:38] [Iter 923/1050] R2[472/600], Temp: 0.1082, Energy: -56.549897-0.004453j
[2025-08-23 00:00:42] [Iter 924/1050] R2[473/600], Temp: 0.1065, Energy: -56.570221+0.001313j
[2025-08-23 00:00:47] [Iter 925/1050] R2[474/600], Temp: 0.1049, Energy: -56.640226-0.000935j
[2025-08-23 00:00:51] [Iter 926/1050] R2[475/600], Temp: 0.1033, Energy: -56.591957-0.004541j
[2025-08-23 00:00:55] [Iter 927/1050] R2[476/600], Temp: 0.1017, Energy: -56.596370+0.001015j
[2025-08-23 00:00:59] [Iter 928/1050] R2[477/600], Temp: 0.1002, Energy: -56.772154+0.001998j
[2025-08-23 00:01:04] [Iter 929/1050] R2[478/600], Temp: 0.0986, Energy: -56.621331+0.003813j
[2025-08-23 00:01:08] [Iter 930/1050] R2[479/600], Temp: 0.0970, Energy: -56.636516+0.002243j
[2025-08-23 00:01:12] [Iter 931/1050] R2[480/600], Temp: 0.0955, Energy: -56.664704+0.002070j
[2025-08-23 00:01:17] [Iter 932/1050] R2[481/600], Temp: 0.0940, Energy: -56.658940-0.002456j
[2025-08-23 00:01:21] [Iter 933/1050] R2[482/600], Temp: 0.0924, Energy: -56.617966+0.007498j
[2025-08-23 00:01:25] [Iter 934/1050] R2[483/600], Temp: 0.0909, Energy: -56.686550-0.002914j
[2025-08-23 00:01:29] [Iter 935/1050] R2[484/600], Temp: 0.0894, Energy: -56.571902+0.000586j
[2025-08-23 00:01:34] [Iter 936/1050] R2[485/600], Temp: 0.0879, Energy: -56.639801-0.000820j
[2025-08-23 00:01:38] [Iter 937/1050] R2[486/600], Temp: 0.0865, Energy: -56.544913-0.000814j
[2025-08-23 00:01:42] [Iter 938/1050] R2[487/600], Temp: 0.0850, Energy: -56.538774-0.002449j
[2025-08-23 00:01:46] [Iter 939/1050] R2[488/600], Temp: 0.0835, Energy: -56.602752+0.006017j
[2025-08-23 00:01:51] [Iter 940/1050] R2[489/600], Temp: 0.0821, Energy: -56.665546+0.000126j
[2025-08-23 00:01:55] [Iter 941/1050] R2[490/600], Temp: 0.0807, Energy: -56.635059+0.000531j
[2025-08-23 00:01:59] [Iter 942/1050] R2[491/600], Temp: 0.0792, Energy: -56.592458-0.002944j
[2025-08-23 00:02:04] [Iter 943/1050] R2[492/600], Temp: 0.0778, Energy: -56.653355-0.002661j
[2025-08-23 00:02:08] [Iter 944/1050] R2[493/600], Temp: 0.0764, Energy: -56.590886+0.003274j
[2025-08-23 00:02:12] [Iter 945/1050] R2[494/600], Temp: 0.0751, Energy: -56.509984-0.007518j
[2025-08-23 00:02:16] [Iter 946/1050] R2[495/600], Temp: 0.0737, Energy: -56.648035-0.002549j
[2025-08-23 00:02:21] [Iter 947/1050] R2[496/600], Temp: 0.0723, Energy: -56.596555+0.001028j
[2025-08-23 00:02:25] [Iter 948/1050] R2[497/600], Temp: 0.0710, Energy: -56.552712-0.002315j
[2025-08-23 00:02:29] [Iter 949/1050] R2[498/600], Temp: 0.0696, Energy: -56.542924-0.002961j
[2025-08-23 00:02:34] [Iter 950/1050] R2[499/600], Temp: 0.0683, Energy: -56.701901-0.001313j
[2025-08-23 00:02:38] [Iter 951/1050] R2[500/600], Temp: 0.0670, Energy: -56.713364-0.000087j
[2025-08-23 00:02:42] [Iter 952/1050] R2[501/600], Temp: 0.0657, Energy: -56.704571+0.004454j
[2025-08-23 00:02:46] [Iter 953/1050] R2[502/600], Temp: 0.0644, Energy: -56.695007-0.001772j
[2025-08-23 00:02:51] [Iter 954/1050] R2[503/600], Temp: 0.0631, Energy: -56.717985+0.000694j
[2025-08-23 00:02:55] [Iter 955/1050] R2[504/600], Temp: 0.0618, Energy: -56.646870-0.006706j
[2025-08-23 00:02:59] [Iter 956/1050] R2[505/600], Temp: 0.0606, Energy: -56.648747-0.001849j
[2025-08-23 00:03:04] [Iter 957/1050] R2[506/600], Temp: 0.0593, Energy: -56.573559-0.000741j
[2025-08-23 00:03:08] [Iter 958/1050] R2[507/600], Temp: 0.0581, Energy: -56.588227+0.000579j
[2025-08-23 00:03:12] [Iter 959/1050] R2[508/600], Temp: 0.0569, Energy: -56.529157-0.001158j
[2025-08-23 00:03:16] [Iter 960/1050] R2[509/600], Temp: 0.0557, Energy: -56.631398+0.002574j
[2025-08-23 00:03:21] [Iter 961/1050] R2[510/600], Temp: 0.0545, Energy: -56.670570+0.005093j
[2025-08-23 00:03:25] [Iter 962/1050] R2[511/600], Temp: 0.0533, Energy: -56.825311-0.002423j
[2025-08-23 00:03:29] [Iter 963/1050] R2[512/600], Temp: 0.0521, Energy: -56.722555+0.000581j
[2025-08-23 00:03:33] [Iter 964/1050] R2[513/600], Temp: 0.0510, Energy: -56.629741+0.000168j
[2025-08-23 00:03:38] [Iter 965/1050] R2[514/600], Temp: 0.0498, Energy: -56.589355-0.002991j
[2025-08-23 00:03:42] [Iter 966/1050] R2[515/600], Temp: 0.0487, Energy: -56.655487-0.002187j
[2025-08-23 00:03:46] [Iter 967/1050] R2[516/600], Temp: 0.0476, Energy: -56.580491+0.001453j
[2025-08-23 00:03:51] [Iter 968/1050] R2[517/600], Temp: 0.0465, Energy: -56.691356+0.000670j
[2025-08-23 00:03:55] [Iter 969/1050] R2[518/600], Temp: 0.0454, Energy: -56.719138+0.004084j
[2025-08-23 00:03:59] [Iter 970/1050] R2[519/600], Temp: 0.0443, Energy: -56.777634-0.003835j
[2025-08-23 00:04:03] [Iter 971/1050] R2[520/600], Temp: 0.0432, Energy: -56.794317-0.001569j
[2025-08-23 00:04:08] [Iter 972/1050] R2[521/600], Temp: 0.0422, Energy: -56.756738-0.004388j
[2025-08-23 00:04:12] [Iter 973/1050] R2[522/600], Temp: 0.0411, Energy: -56.631927-0.003936j
[2025-08-23 00:04:16] [Iter 974/1050] R2[523/600], Temp: 0.0401, Energy: -56.688446-0.002308j
[2025-08-23 00:04:21] [Iter 975/1050] R2[524/600], Temp: 0.0391, Energy: -56.589405-0.003374j
[2025-08-23 00:04:25] [Iter 976/1050] R2[525/600], Temp: 0.0381, Energy: -56.604866-0.000665j
[2025-08-23 00:04:29] [Iter 977/1050] R2[526/600], Temp: 0.0371, Energy: -56.569722-0.002566j
[2025-08-23 00:04:33] [Iter 978/1050] R2[527/600], Temp: 0.0361, Energy: -56.469774+0.000737j
[2025-08-23 00:04:38] [Iter 979/1050] R2[528/600], Temp: 0.0351, Energy: -56.682254-0.003458j
[2025-08-23 00:04:42] [Iter 980/1050] R2[529/600], Temp: 0.0342, Energy: -56.695332-0.003662j
[2025-08-23 00:04:46] [Iter 981/1050] R2[530/600], Temp: 0.0332, Energy: -56.587471-0.000438j
[2025-08-23 00:04:51] [Iter 982/1050] R2[531/600], Temp: 0.0323, Energy: -56.662745-0.001133j
[2025-08-23 00:04:55] [Iter 983/1050] R2[532/600], Temp: 0.0314, Energy: -56.711915-0.000199j
[2025-08-23 00:04:59] [Iter 984/1050] R2[533/600], Temp: 0.0305, Energy: -56.573507+0.000404j
[2025-08-23 00:05:03] [Iter 985/1050] R2[534/600], Temp: 0.0296, Energy: -56.624272+0.003478j
[2025-08-23 00:05:08] [Iter 986/1050] R2[535/600], Temp: 0.0287, Energy: -56.678155-0.002013j
[2025-08-23 00:05:12] [Iter 987/1050] R2[536/600], Temp: 0.0278, Energy: -56.753644-0.001495j
[2025-08-23 00:05:16] [Iter 988/1050] R2[537/600], Temp: 0.0270, Energy: -56.643093+0.000697j
[2025-08-23 00:05:21] [Iter 989/1050] R2[538/600], Temp: 0.0261, Energy: -56.637682-0.001983j
[2025-08-23 00:05:25] [Iter 990/1050] R2[539/600], Temp: 0.0253, Energy: -56.648794+0.001539j
[2025-08-23 00:05:29] [Iter 991/1050] R2[540/600], Temp: 0.0245, Energy: -56.580213-0.001819j
[2025-08-23 00:05:33] [Iter 992/1050] R2[541/600], Temp: 0.0237, Energy: -56.593583-0.001211j
[2025-08-23 00:05:38] [Iter 993/1050] R2[542/600], Temp: 0.0229, Energy: -56.640009+0.001511j
[2025-08-23 00:05:42] [Iter 994/1050] R2[543/600], Temp: 0.0221, Energy: -56.634133+0.000898j
[2025-08-23 00:05:46] [Iter 995/1050] R2[544/600], Temp: 0.0213, Energy: -56.651561-0.001209j
[2025-08-23 00:05:50] [Iter 996/1050] R2[545/600], Temp: 0.0206, Energy: -56.757958-0.003662j
[2025-08-23 00:05:55] [Iter 997/1050] R2[546/600], Temp: 0.0199, Energy: -56.595915+0.000559j
[2025-08-23 00:05:59] [Iter 998/1050] R2[547/600], Temp: 0.0191, Energy: -56.669112-0.004331j
[2025-08-23 00:06:03] [Iter 999/1050] R2[548/600], Temp: 0.0184, Energy: -56.820136+0.000965j
[2025-08-23 00:06:08] [Iter 1000/1050] R2[549/600], Temp: 0.0177, Energy: -56.747609+0.001926j
[2025-08-23 00:06:08] ✓ Checkpoint saved: checkpoint_iter_001000.pkl
[2025-08-23 00:06:12] [Iter 1001/1050] R2[550/600], Temp: 0.0170, Energy: -56.572234-0.002252j
[2025-08-23 00:06:16] [Iter 1002/1050] R2[551/600], Temp: 0.0164, Energy: -56.642775-0.001822j
[2025-08-23 00:06:20] [Iter 1003/1050] R2[552/600], Temp: 0.0157, Energy: -56.677833+0.002955j
[2025-08-23 00:06:25] [Iter 1004/1050] R2[553/600], Temp: 0.0151, Energy: -56.663055+0.003068j
[2025-08-23 00:06:29] [Iter 1005/1050] R2[554/600], Temp: 0.0144, Energy: -56.681109-0.003782j
[2025-08-23 00:06:33] [Iter 1006/1050] R2[555/600], Temp: 0.0138, Energy: -56.881781-0.003085j
[2025-08-23 00:06:38] [Iter 1007/1050] R2[556/600], Temp: 0.0132, Energy: -56.803677+0.000886j
[2025-08-23 00:06:42] [Iter 1008/1050] R2[557/600], Temp: 0.0126, Energy: -56.750313-0.000353j
[2025-08-23 00:06:46] [Iter 1009/1050] R2[558/600], Temp: 0.0120, Energy: -56.709016-0.001059j
[2025-08-23 00:06:50] [Iter 1010/1050] R2[559/600], Temp: 0.0115, Energy: -56.710919+0.000022j
[2025-08-23 00:06:55] [Iter 1011/1050] R2[560/600], Temp: 0.0109, Energy: -56.628826-0.002190j
[2025-08-23 00:06:59] [Iter 1012/1050] R2[561/600], Temp: 0.0104, Energy: -56.587660-0.001465j
[2025-08-23 00:07:03] [Iter 1013/1050] R2[562/600], Temp: 0.0099, Energy: -56.526728+0.001497j
[2025-08-23 00:07:07] [Iter 1014/1050] R2[563/600], Temp: 0.0094, Energy: -56.608417+0.000806j
[2025-08-23 00:07:12] [Iter 1015/1050] R2[564/600], Temp: 0.0089, Energy: -56.640246+0.000888j
[2025-08-23 00:07:16] [Iter 1016/1050] R2[565/600], Temp: 0.0084, Energy: -56.608946-0.000069j
[2025-08-23 00:07:20] [Iter 1017/1050] R2[566/600], Temp: 0.0079, Energy: -56.586786-0.000133j
[2025-08-23 00:07:25] [Iter 1018/1050] R2[567/600], Temp: 0.0074, Energy: -56.543384+0.004129j
[2025-08-23 00:07:29] [Iter 1019/1050] R2[568/600], Temp: 0.0070, Energy: -56.537213+0.000965j
[2025-08-23 00:07:33] [Iter 1020/1050] R2[569/600], Temp: 0.0066, Energy: -56.565797+0.000431j
[2025-08-23 00:07:37] [Iter 1021/1050] R2[570/600], Temp: 0.0062, Energy: -56.487373+0.001323j
[2025-08-23 00:07:42] [Iter 1022/1050] R2[571/600], Temp: 0.0058, Energy: -56.630609+0.000910j
[2025-08-23 00:07:46] [Iter 1023/1050] R2[572/600], Temp: 0.0054, Energy: -56.473017-0.000337j
[2025-08-23 00:07:50] [Iter 1024/1050] R2[573/600], Temp: 0.0050, Energy: -56.513719+0.001602j
[2025-08-23 00:07:55] [Iter 1025/1050] R2[574/600], Temp: 0.0046, Energy: -56.666365+0.001098j
[2025-08-23 00:07:59] [Iter 1026/1050] R2[575/600], Temp: 0.0043, Energy: -56.587930+0.004949j
[2025-08-23 00:08:03] [Iter 1027/1050] R2[576/600], Temp: 0.0039, Energy: -56.595685+0.003165j
[2025-08-23 00:08:07] [Iter 1028/1050] R2[577/600], Temp: 0.0036, Energy: -56.753105-0.004818j
[2025-08-23 00:08:12] [Iter 1029/1050] R2[578/600], Temp: 0.0033, Energy: -56.609822+0.002247j
[2025-08-23 00:08:16] [Iter 1030/1050] R2[579/600], Temp: 0.0030, Energy: -56.579170-0.002480j
[2025-08-23 00:08:20] [Iter 1031/1050] R2[580/600], Temp: 0.0027, Energy: -56.560550-0.002784j
[2025-08-23 00:08:25] [Iter 1032/1050] R2[581/600], Temp: 0.0025, Energy: -56.557615-0.000195j
[2025-08-23 00:08:29] [Iter 1033/1050] R2[582/600], Temp: 0.0022, Energy: -56.630397-0.000270j
[2025-08-23 00:08:33] [Iter 1034/1050] R2[583/600], Temp: 0.0020, Energy: -56.548181+0.018649j
[2025-08-23 00:08:37] [Iter 1035/1050] R2[584/600], Temp: 0.0018, Energy: -56.701348-0.002765j
[2025-08-23 00:08:42] [Iter 1036/1050] R2[585/600], Temp: 0.0015, Energy: -56.684586-0.002312j
[2025-08-23 00:08:46] [Iter 1037/1050] R2[586/600], Temp: 0.0013, Energy: -56.660001-0.003336j
[2025-08-23 00:08:50] [Iter 1038/1050] R2[587/600], Temp: 0.0012, Energy: -56.629912-0.002323j
[2025-08-23 00:08:54] [Iter 1039/1050] R2[588/600], Temp: 0.0010, Energy: -56.611273-0.000093j
[2025-08-23 00:08:59] [Iter 1040/1050] R2[589/600], Temp: 0.0008, Energy: -56.556893-0.001106j
[2025-08-23 00:09:03] [Iter 1041/1050] R2[590/600], Temp: 0.0007, Energy: -56.610387-0.002682j
[2025-08-23 00:09:07] [Iter 1042/1050] R2[591/600], Temp: 0.0006, Energy: -56.579856+0.007208j
[2025-08-23 00:09:12] [Iter 1043/1050] R2[592/600], Temp: 0.0004, Energy: -56.586846-0.001322j
[2025-08-23 00:09:16] [Iter 1044/1050] R2[593/600], Temp: 0.0003, Energy: -56.587207-0.001641j
[2025-08-23 00:09:20] [Iter 1045/1050] R2[594/600], Temp: 0.0002, Energy: -56.612132+0.001142j
[2025-08-23 00:09:24] [Iter 1046/1050] R2[595/600], Temp: 0.0002, Energy: -56.606349-0.002711j
[2025-08-23 00:09:29] [Iter 1047/1050] R2[596/600], Temp: 0.0001, Energy: -56.552842+0.000129j
[2025-08-23 00:09:33] [Iter 1048/1050] R2[597/600], Temp: 0.0001, Energy: -56.613638-0.005384j
[2025-08-23 00:09:37] [Iter 1049/1050] R2[598/600], Temp: 0.0000, Energy: -56.596022+0.000509j
[2025-08-23 00:09:42] [Iter 1050/1050] R2[599/600], Temp: 0.0000, Energy: -56.535833-0.002627j
[2025-08-23 00:09:42] ✅ Training completed | Restarts: 2
[2025-08-23 00:09:42] ============================================================
[2025-08-23 00:09:42] Training completed | Runtime: 4553.3s
[2025-08-23 00:09:43] ✓ Final state saved: checkpoints/final_GCNN.pkl
[2025-08-23 00:09:43] ============================================================
