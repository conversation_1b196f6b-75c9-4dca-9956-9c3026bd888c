[2025-08-08 11:25:18] 使用checkpoint文件: results/L=4/J2=0.00/J1=0.01/training/checkpoints/checkpoint_iter_000900.pkl
[2025-08-08 11:25:26] ✓ 从checkpoint加载参数: 900
[2025-08-08 11:25:26]   - 能量: -53.537925+0.000631j ± 0.090796
[2025-08-08 11:25:26] ================================================================================
[2025-08-08 11:25:26] 加载量子态: L=4, J2=0.00, J1=0.01, checkpoint=checkpoint_iter_000900
[2025-08-08 11:25:26] 设置样本数为: 1048576
[2025-08-08 11:25:26] 开始生成共享样本集...
[2025-08-08 11:27:58] 样本生成完成,耗时: 151.740 秒
[2025-08-08 11:27:58] ================================================================================
[2025-08-08 11:27:58] 开始计算自旋结构因子...
[2025-08-08 11:27:58] 初始化操作符缓存...
[2025-08-08 11:27:58] 预构建所有自旋相关操作符...
[2025-08-08 11:27:58] 开始计算自旋相关函数...
[2025-08-08 11:28:09] 自旋算符进度: 1/64, 当前算符: S_0 · S_0, 耗时: 10.735s
[2025-08-08 11:28:23] 自旋算符进度: 2/64, 当前算符: S_0 · S_1, 耗时: 13.912s
[2025-08-08 11:28:31] 自旋算符进度: 3/64, 当前算符: S_0 · S_2, 耗时: 8.136s
[2025-08-08 11:28:39] 自旋算符进度: 4/64, 当前算符: S_0 · S_3, 耗时: 8.133s
[2025-08-08 11:28:47] 自旋算符进度: 5/64, 当前算符: S_0 · S_4, 耗时: 8.141s
[2025-08-08 11:28:55] 自旋算符进度: 6/64, 当前算符: S_0 · S_5, 耗时: 8.137s
[2025-08-08 11:29:04] 自旋算符进度: 7/64, 当前算符: S_0 · S_6, 耗时: 8.123s
[2025-08-08 11:29:12] 自旋算符进度: 8/64, 当前算符: S_0 · S_7, 耗时: 8.137s
[2025-08-08 11:29:20] 自旋算符进度: 9/64, 当前算符: S_0 · S_8, 耗时: 8.131s
[2025-08-08 11:29:28] 自旋算符进度: 10/64, 当前算符: S_0 · S_9, 耗时: 8.134s
[2025-08-08 11:29:36] 自旋算符进度: 11/64, 当前算符: S_0 · S_10, 耗时: 8.138s
[2025-08-08 11:29:44] 自旋算符进度: 12/64, 当前算符: S_0 · S_11, 耗时: 8.133s
[2025-08-08 11:29:52] 自旋算符进度: 13/64, 当前算符: S_0 · S_12, 耗时: 8.161s
[2025-08-08 11:30:00] 自旋算符进度: 14/64, 当前算符: S_0 · S_13, 耗时: 8.131s
[2025-08-08 11:30:09] 自旋算符进度: 15/64, 当前算符: S_0 · S_14, 耗时: 8.140s
[2025-08-08 11:30:17] 自旋算符进度: 16/64, 当前算符: S_0 · S_15, 耗时: 8.129s
[2025-08-08 11:30:25] 自旋算符进度: 17/64, 当前算符: S_0 · S_16, 耗时: 8.140s
[2025-08-08 11:30:33] 自旋算符进度: 18/64, 当前算符: S_0 · S_17, 耗时: 8.137s
[2025-08-08 11:30:41] 自旋算符进度: 19/64, 当前算符: S_0 · S_18, 耗时: 8.123s
[2025-08-08 11:30:49] 自旋算符进度: 20/64, 当前算符: S_0 · S_19, 耗时: 8.137s
[2025-08-08 11:30:57] 自旋算符进度: 21/64, 当前算符: S_0 · S_20, 耗时: 8.134s
[2025-08-08 11:31:06] 自旋算符进度: 22/64, 当前算符: S_0 · S_21, 耗时: 8.130s
[2025-08-08 11:31:14] 自旋算符进度: 23/64, 当前算符: S_0 · S_22, 耗时: 8.140s
[2025-08-08 11:31:22] 自旋算符进度: 24/64, 当前算符: S_0 · S_23, 耗时: 8.126s
[2025-08-08 11:31:30] 自旋算符进度: 25/64, 当前算符: S_0 · S_24, 耗时: 8.140s
[2025-08-08 11:31:38] 自旋算符进度: 26/64, 当前算符: S_0 · S_25, 耗时: 8.140s
[2025-08-08 11:31:46] 自旋算符进度: 27/64, 当前算符: S_0 · S_26, 耗时: 8.126s
[2025-08-08 11:31:54] 自旋算符进度: 28/64, 当前算符: S_0 · S_27, 耗时: 8.139s
[2025-08-08 11:32:03] 自旋算符进度: 29/64, 当前算符: S_0 · S_28, 耗时: 8.131s
[2025-08-08 11:32:11] 自旋算符进度: 30/64, 当前算符: S_0 · S_29, 耗时: 8.135s
[2025-08-08 11:32:19] 自旋算符进度: 31/64, 当前算符: S_0 · S_30, 耗时: 8.144s
[2025-08-08 11:32:27] 自旋算符进度: 32/64, 当前算符: S_0 · S_31, 耗时: 8.140s
[2025-08-08 11:32:35] 自旋算符进度: 33/64, 当前算符: S_0 · S_32, 耗时: 8.134s
[2025-08-08 11:32:43] 自旋算符进度: 34/64, 当前算符: S_0 · S_33, 耗时: 8.130s
[2025-08-08 11:32:51] 自旋算符进度: 35/64, 当前算符: S_0 · S_34, 耗时: 8.138s
[2025-08-08 11:32:59] 自旋算符进度: 36/64, 当前算符: S_0 · S_35, 耗时: 8.131s
[2025-08-08 11:33:08] 自旋算符进度: 37/64, 当前算符: S_0 · S_36, 耗时: 8.138s
[2025-08-08 11:33:16] 自旋算符进度: 38/64, 当前算符: S_0 · S_37, 耗时: 8.142s
[2025-08-08 11:33:24] 自旋算符进度: 39/64, 当前算符: S_0 · S_38, 耗时: 8.136s
[2025-08-08 11:33:32] 自旋算符进度: 40/64, 当前算符: S_0 · S_39, 耗时: 8.145s
[2025-08-08 11:33:40] 自旋算符进度: 41/64, 当前算符: S_0 · S_40, 耗时: 8.136s
[2025-08-08 11:33:48] 自旋算符进度: 42/64, 当前算符: S_0 · S_41, 耗时: 8.125s
[2025-08-08 11:33:56] 自旋算符进度: 43/64, 当前算符: S_0 · S_42, 耗时: 8.141s
[2025-08-08 11:34:05] 自旋算符进度: 44/64, 当前算符: S_0 · S_43, 耗时: 8.132s
[2025-08-08 11:34:13] 自旋算符进度: 45/64, 当前算符: S_0 · S_44, 耗时: 8.143s
[2025-08-08 11:34:21] 自旋算符进度: 46/64, 当前算符: S_0 · S_45, 耗时: 8.144s
[2025-08-08 11:34:29] 自旋算符进度: 47/64, 当前算符: S_0 · S_46, 耗时: 8.137s
[2025-08-08 11:34:37] 自旋算符进度: 48/64, 当前算符: S_0 · S_47, 耗时: 8.153s
[2025-08-08 11:34:45] 自旋算符进度: 49/64, 当前算符: S_0 · S_48, 耗时: 8.137s
[2025-08-08 11:34:53] 自旋算符进度: 50/64, 当前算符: S_0 · S_49, 耗时: 8.125s
[2025-08-08 11:35:02] 自旋算符进度: 51/64, 当前算符: S_0 · S_50, 耗时: 8.139s
[2025-08-08 11:35:10] 自旋算符进度: 52/64, 当前算符: S_0 · S_51, 耗时: 8.128s
[2025-08-08 11:35:18] 自旋算符进度: 53/64, 当前算符: S_0 · S_52, 耗时: 8.122s
[2025-08-08 11:35:26] 自旋算符进度: 54/64, 当前算符: S_0 · S_53, 耗时: 8.132s
[2025-08-08 11:35:34] 自旋算符进度: 55/64, 当前算符: S_0 · S_54, 耗时: 8.138s
[2025-08-08 11:35:42] 自旋算符进度: 56/64, 当前算符: S_0 · S_55, 耗时: 8.133s
[2025-08-08 11:35:50] 自旋算符进度: 57/64, 当前算符: S_0 · S_56, 耗时: 8.150s
[2025-08-08 11:35:59] 自旋算符进度: 58/64, 当前算符: S_0 · S_57, 耗时: 8.136s
[2025-08-08 11:36:07] 自旋算符进度: 59/64, 当前算符: S_0 · S_58, 耗时: 8.124s
[2025-08-08 11:36:15] 自旋算符进度: 60/64, 当前算符: S_0 · S_59, 耗时: 8.138s
[2025-08-08 11:36:23] 自旋算符进度: 61/64, 当前算符: S_0 · S_60, 耗时: 8.131s
[2025-08-08 11:36:31] 自旋算符进度: 62/64, 当前算符: S_0 · S_61, 耗时: 8.133s
[2025-08-08 11:36:39] 自旋算符进度: 63/64, 当前算符: S_0 · S_62, 耗时: 8.136s
[2025-08-08 11:36:47] 自旋算符进度: 64/64, 当前算符: S_0 · S_63, 耗时: 8.134s
[2025-08-08 11:36:47] 自旋相关函数计算完成,总耗时 529.15 秒
[2025-08-08 11:36:47] 计算傅里叶变换...
[2025-08-08 11:36:48] 自旋结构因子计算完成
[2025-08-08 11:36:49] 自旋相关函数平均误差: 0.000678
