[2025-08-08 11:13:41] 使用checkpoint文件: results/L=4/J2=0.00/J1=0.01/training/checkpoints/checkpoint_iter_000800.pkl
[2025-08-08 11:13:49] ✓ 从checkpoint加载参数: 800
[2025-08-08 11:13:49]   - 能量: -53.485522+0.003304j ± 0.086294
[2025-08-08 11:13:49] ================================================================================
[2025-08-08 11:13:49] 加载量子态: L=4, J2=0.00, J1=0.01, checkpoint=checkpoint_iter_000800
[2025-08-08 11:13:49] 设置样本数为: 1048576
[2025-08-08 11:13:49] 开始生成共享样本集...
[2025-08-08 11:16:21] 样本生成完成,耗时: 152.107 秒
[2025-08-08 11:16:21] ================================================================================
[2025-08-08 11:16:21] 开始计算自旋结构因子...
[2025-08-08 11:16:21] 初始化操作符缓存...
[2025-08-08 11:16:21] 预构建所有自旋相关操作符...
[2025-08-08 11:16:21] 开始计算自旋相关函数...
[2025-08-08 11:16:32] 自旋算符进度: 1/64, 当前算符: S_0 · S_0, 耗时: 10.730s
[2025-08-08 11:16:46] 自旋算符进度: 2/64, 当前算符: S_0 · S_1, 耗时: 13.964s
[2025-08-08 11:16:54] 自旋算符进度: 3/64, 当前算符: S_0 · S_2, 耗时: 8.192s
[2025-08-08 11:17:02] 自旋算符进度: 4/64, 当前算符: S_0 · S_3, 耗时: 8.153s
[2025-08-08 11:17:11] 自旋算符进度: 5/64, 当前算符: S_0 · S_4, 耗时: 8.194s
[2025-08-08 11:17:19] 自旋算符进度: 6/64, 当前算符: S_0 · S_5, 耗时: 8.191s
[2025-08-08 11:17:27] 自旋算符进度: 7/64, 当前算符: S_0 · S_6, 耗时: 8.155s
[2025-08-08 11:17:35] 自旋算符进度: 8/64, 当前算符: S_0 · S_7, 耗时: 8.192s
[2025-08-08 11:17:43] 自旋算符进度: 9/64, 当前算符: S_0 · S_8, 耗时: 8.153s
[2025-08-08 11:17:51] 自旋算符进度: 10/64, 当前算符: S_0 · S_9, 耗时: 8.153s
[2025-08-08 11:18:00] 自旋算符进度: 11/64, 当前算符: S_0 · S_10, 耗时: 8.192s
[2025-08-08 11:18:08] 自旋算符进度: 12/64, 当前算符: S_0 · S_11, 耗时: 8.153s
[2025-08-08 11:18:16] 自旋算符进度: 13/64, 当前算符: S_0 · S_12, 耗时: 8.194s
[2025-08-08 11:18:24] 自旋算符进度: 14/64, 当前算符: S_0 · S_13, 耗时: 8.152s
[2025-08-08 11:18:32] 自旋算符进度: 15/64, 当前算符: S_0 · S_14, 耗时: 8.193s
[2025-08-08 11:18:40] 自旋算符进度: 16/64, 当前算符: S_0 · S_15, 耗时: 8.153s
[2025-08-08 11:18:49] 自旋算符进度: 17/64, 当前算符: S_0 · S_16, 耗时: 8.212s
[2025-08-08 11:18:57] 自旋算符进度: 18/64, 当前算符: S_0 · S_17, 耗时: 8.191s
[2025-08-08 11:19:05] 自旋算符进度: 19/64, 当前算符: S_0 · S_18, 耗时: 8.155s
[2025-08-08 11:19:13] 自旋算符进度: 20/64, 当前算符: S_0 · S_19, 耗时: 8.193s
[2025-08-08 11:19:21] 自旋算符进度: 21/64, 当前算符: S_0 · S_20, 耗时: 8.154s
[2025-08-08 11:19:29] 自旋算符进度: 22/64, 当前算符: S_0 · S_21, 耗时: 8.153s
[2025-08-08 11:19:38] 自旋算符进度: 23/64, 当前算符: S_0 · S_22, 耗时: 8.192s
[2025-08-08 11:19:46] 自旋算符进度: 24/64, 当前算符: S_0 · S_23, 耗时: 8.155s
[2025-08-08 11:19:54] 自旋算符进度: 25/64, 当前算符: S_0 · S_24, 耗时: 8.191s
[2025-08-08 11:20:02] 自旋算符进度: 26/64, 当前算符: S_0 · S_25, 耗时: 8.193s
[2025-08-08 11:20:10] 自旋算符进度: 27/64, 当前算符: S_0 · S_26, 耗时: 8.155s
[2025-08-08 11:20:19] 自旋算符进度: 28/64, 当前算符: S_0 · S_27, 耗时: 8.192s
[2025-08-08 11:20:27] 自旋算符进度: 29/64, 当前算符: S_0 · S_28, 耗时: 8.153s
[2025-08-08 11:20:35] 自旋算符进度: 30/64, 当前算符: S_0 · S_29, 耗时: 8.153s
[2025-08-08 11:20:43] 自旋算符进度: 31/64, 当前算符: S_0 · S_30, 耗时: 8.195s
[2025-08-08 11:20:51] 自旋算符进度: 32/64, 当前算符: S_0 · S_31, 耗时: 8.192s
[2025-08-08 11:20:59] 自旋算符进度: 33/64, 当前算符: S_0 · S_32, 耗时: 8.152s
[2025-08-08 11:21:08] 自旋算符进度: 34/64, 当前算符: S_0 · S_33, 耗时: 8.153s
[2025-08-08 11:21:16] 自旋算符进度: 35/64, 当前算符: S_0 · S_34, 耗时: 8.192s
[2025-08-08 11:21:24] 自旋算符进度: 36/64, 当前算符: S_0 · S_35, 耗时: 8.152s
[2025-08-08 11:21:32] 自旋算符进度: 37/64, 当前算符: S_0 · S_36, 耗时: 8.193s
[2025-08-08 11:21:40] 自旋算符进度: 38/64, 当前算符: S_0 · S_37, 耗时: 8.196s
[2025-08-08 11:21:48] 自旋算符进度: 39/64, 当前算符: S_0 · S_38, 耗时: 8.191s
[2025-08-08 11:21:57] 自旋算符进度: 40/64, 当前算符: S_0 · S_39, 耗时: 8.195s
[2025-08-08 11:22:05] 自旋算符进度: 41/64, 当前算符: S_0 · S_40, 耗时: 8.153s
[2025-08-08 11:22:13] 自旋算符进度: 42/64, 当前算符: S_0 · S_41, 耗时: 8.155s
[2025-08-08 11:22:21] 自旋算符进度: 43/64, 当前算符: S_0 · S_42, 耗时: 8.193s
[2025-08-08 11:22:29] 自旋算符进度: 44/64, 当前算符: S_0 · S_43, 耗时: 8.153s
[2025-08-08 11:22:38] 自旋算符进度: 45/64, 当前算符: S_0 · S_44, 耗时: 8.195s
[2025-08-08 11:22:46] 自旋算符进度: 46/64, 当前算符: S_0 · S_45, 耗时: 8.195s
[2025-08-08 11:22:54] 自旋算符进度: 47/64, 当前算符: S_0 · S_46, 耗时: 8.154s
[2025-08-08 11:23:02] 自旋算符进度: 48/64, 当前算符: S_0 · S_47, 耗时: 8.197s
[2025-08-08 11:23:10] 自旋算符进度: 49/64, 当前算符: S_0 · S_48, 耗时: 8.152s
[2025-08-08 11:23:19] 自旋算符进度: 50/64, 当前算符: S_0 · S_49, 耗时: 8.247s
[2025-08-08 11:23:27] 自旋算符进度: 51/64, 当前算符: S_0 · S_50, 耗时: 8.192s
[2025-08-08 11:23:35] 自旋算符进度: 52/64, 当前算符: S_0 · S_51, 耗时: 8.153s
[2025-08-08 11:23:43] 自旋算符进度: 53/64, 当前算符: S_0 · S_52, 耗时: 8.154s
[2025-08-08 11:23:51] 自旋算符进度: 54/64, 当前算符: S_0 · S_53, 耗时: 8.152s
[2025-08-08 11:23:59] 自旋算符进度: 55/64, 当前算符: S_0 · S_54, 耗时: 8.193s
[2025-08-08 11:24:08] 自旋算符进度: 56/64, 当前算符: S_0 · S_55, 耗时: 8.153s
[2025-08-08 11:24:16] 自旋算符进度: 57/64, 当前算符: S_0 · S_56, 耗时: 8.196s
[2025-08-08 11:24:24] 自旋算符进度: 58/64, 当前算符: S_0 · S_57, 耗时: 8.193s
[2025-08-08 11:24:32] 自旋算符进度: 59/64, 当前算符: S_0 · S_58, 耗时: 8.154s
[2025-08-08 11:24:40] 自旋算符进度: 60/64, 当前算符: S_0 · S_59, 耗时: 8.193s
[2025-08-08 11:24:49] 自旋算符进度: 61/64, 当前算符: S_0 · S_60, 耗时: 8.254s
[2025-08-08 11:24:57] 自旋算符进度: 62/64, 当前算符: S_0 · S_61, 耗时: 8.154s
[2025-08-08 11:25:05] 自旋算符进度: 63/64, 当前算符: S_0 · S_62, 耗时: 8.193s
[2025-08-08 11:25:13] 自旋算符进度: 64/64, 当前算符: S_0 · S_63, 耗时: 8.153s
[2025-08-08 11:25:13] 自旋相关函数计算完成,总耗时 531.73 秒
[2025-08-08 11:25:13] 计算傅里叶变换...
[2025-08-08 11:25:14] 自旋结构因子计算完成
[2025-08-08 11:25:14] 自旋相关函数平均误差: 0.000667
