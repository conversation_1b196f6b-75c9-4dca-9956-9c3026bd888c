[2025-08-08 09:51:52] 使用checkpoint文件: results/L=4/J2=0.00/J1=0.01/training/checkpoints/checkpoint_iter_000100.pkl
[2025-08-08 09:52:04] ✓ 从checkpoint加载参数: 100
[2025-08-08 09:52:04]   - 能量: -53.618516+0.000605j ± 0.084825
[2025-08-08 09:52:04] ================================================================================
[2025-08-08 09:52:04] 加载量子态: L=4, J2=0.00, J1=0.01, checkpoint=checkpoint_iter_000100
[2025-08-08 09:52:04] 设置样本数为: 1048576
[2025-08-08 09:52:04] 开始生成共享样本集...
[2025-08-08 09:54:37] 样本生成完成,耗时: 152.949 秒
[2025-08-08 09:54:37] ================================================================================
[2025-08-08 09:54:37] 开始计算自旋结构因子...
[2025-08-08 09:54:37] 初始化操作符缓存...
[2025-08-08 09:54:37] 预构建所有自旋相关操作符...
[2025-08-08 09:54:37] 开始计算自旋相关函数...
[2025-08-08 09:54:49] 自旋算符进度: 1/64, 当前算符: S_0 · S_0, 耗时: 11.553s
[2025-08-08 09:55:02] 自旋算符进度: 2/64, 当前算符: S_0 · S_1, 耗时: 13.958s
[2025-08-08 09:55:11] 自旋算符进度: 3/64, 当前算符: S_0 · S_2, 耗时: 8.141s
[2025-08-08 09:55:19] 自旋算符进度: 4/64, 当前算符: S_0 · S_3, 耗时: 8.138s
[2025-08-08 09:55:27] 自旋算符进度: 5/64, 当前算符: S_0 · S_4, 耗时: 8.142s
[2025-08-08 09:55:35] 自旋算符进度: 6/64, 当前算符: S_0 · S_5, 耗时: 8.140s
[2025-08-08 09:55:43] 自旋算符进度: 7/64, 当前算符: S_0 · S_6, 耗时: 8.126s
[2025-08-08 09:55:51] 自旋算符进度: 8/64, 当前算符: S_0 · S_7, 耗时: 8.142s
[2025-08-08 09:55:59] 自旋算符进度: 9/64, 当前算符: S_0 · S_8, 耗时: 8.128s
[2025-08-08 09:56:08] 自旋算符进度: 10/64, 当前算符: S_0 · S_9, 耗时: 8.137s
[2025-08-08 09:56:16] 自旋算符进度: 11/64, 当前算符: S_0 · S_10, 耗时: 8.141s
[2025-08-08 09:56:24] 自旋算符进度: 12/64, 当前算符: S_0 · S_11, 耗时: 8.138s
[2025-08-08 09:56:32] 自旋算符进度: 13/64, 当前算符: S_0 · S_12, 耗时: 8.141s
[2025-08-08 09:56:40] 自旋算符进度: 14/64, 当前算符: S_0 · S_13, 耗时: 8.132s
[2025-08-08 09:56:48] 自旋算符进度: 15/64, 当前算符: S_0 · S_14, 耗时: 8.141s
[2025-08-08 09:56:56] 自旋算符进度: 16/64, 当前算符: S_0 · S_15, 耗时: 8.128s
[2025-08-08 09:57:05] 自旋算符进度: 17/64, 当前算符: S_0 · S_16, 耗时: 8.142s
[2025-08-08 09:57:13] 自旋算符进度: 18/64, 当前算符: S_0 · S_17, 耗时: 8.140s
[2025-08-08 09:57:21] 自旋算符进度: 19/64, 当前算符: S_0 · S_18, 耗时: 8.126s
[2025-08-08 09:57:29] 自旋算符进度: 20/64, 当前算符: S_0 · S_19, 耗时: 8.141s
[2025-08-08 09:57:37] 自旋算符进度: 21/64, 当前算符: S_0 · S_20, 耗时: 8.138s
[2025-08-08 09:57:45] 自旋算符进度: 22/64, 当前算符: S_0 · S_21, 耗时: 8.130s
[2025-08-08 09:57:53] 自旋算符进度: 23/64, 当前算符: S_0 · S_22, 耗时: 8.140s
[2025-08-08 09:58:01] 自旋算符进度: 24/64, 当前算符: S_0 · S_23, 耗时: 8.125s
[2025-08-08 09:58:10] 自旋算符进度: 25/64, 当前算符: S_0 · S_24, 耗时: 8.140s
[2025-08-08 09:58:18] 自旋算符进度: 26/64, 当前算符: S_0 · S_25, 耗时: 8.142s
[2025-08-08 09:58:26] 自旋算符进度: 27/64, 当前算符: S_0 · S_26, 耗时: 8.127s
[2025-08-08 09:58:34] 自旋算符进度: 28/64, 当前算符: S_0 · S_27, 耗时: 8.142s
[2025-08-08 09:58:42] 自旋算符进度: 29/64, 当前算符: S_0 · S_28, 耗时: 8.128s
[2025-08-08 09:58:50] 自旋算符进度: 30/64, 当前算符: S_0 · S_29, 耗时: 8.135s
[2025-08-08 09:58:58] 自旋算符进度: 31/64, 当前算符: S_0 · S_30, 耗时: 8.143s
[2025-08-08 09:59:07] 自旋算符进度: 32/64, 当前算符: S_0 · S_31, 耗时: 8.141s
[2025-08-08 09:59:15] 自旋算符进度: 33/64, 当前算符: S_0 · S_32, 耗时: 8.132s
[2025-08-08 09:59:23] 自旋算符进度: 34/64, 当前算符: S_0 · S_33, 耗时: 8.129s
[2025-08-08 09:59:31] 自旋算符进度: 35/64, 当前算符: S_0 · S_34, 耗时: 8.140s
[2025-08-08 09:59:39] 自旋算符进度: 36/64, 当前算符: S_0 · S_35, 耗时: 8.131s
[2025-08-08 09:59:47] 自旋算符进度: 37/64, 当前算符: S_0 · S_36, 耗时: 8.141s
[2025-08-08 09:59:55] 自旋算符进度: 38/64, 当前算符: S_0 · S_37, 耗时: 8.142s
[2025-08-08 10:00:04] 自旋算符进度: 39/64, 当前算符: S_0 · S_38, 耗时: 8.135s
[2025-08-08 10:00:12] 自旋算符进度: 40/64, 当前算符: S_0 · S_39, 耗时: 8.142s
[2025-08-08 10:00:20] 自旋算符进度: 41/64, 当前算符: S_0 · S_40, 耗时: 8.136s
[2025-08-08 10:00:28] 自旋算符进度: 42/64, 当前算符: S_0 · S_41, 耗时: 8.126s
[2025-08-08 10:00:36] 自旋算符进度: 43/64, 当前算符: S_0 · S_42, 耗时: 8.141s
[2025-08-08 10:00:44] 自旋算符进度: 44/64, 当前算符: S_0 · S_43, 耗时: 8.128s
[2025-08-08 10:00:52] 自旋算符进度: 45/64, 当前算符: S_0 · S_44, 耗时: 8.141s
[2025-08-08 10:01:01] 自旋算符进度: 46/64, 当前算符: S_0 · S_45, 耗时: 8.142s
[2025-08-08 10:01:09] 自旋算符进度: 47/64, 当前算符: S_0 · S_46, 耗时: 8.138s
[2025-08-08 10:01:17] 自旋算符进度: 48/64, 当前算符: S_0 · S_47, 耗时: 8.151s
[2025-08-08 10:01:25] 自旋算符进度: 49/64, 当前算符: S_0 · S_48, 耗时: 8.137s
[2025-08-08 10:01:33] 自旋算符进度: 50/64, 当前算符: S_0 · S_49, 耗时: 8.126s
[2025-08-08 10:01:41] 自旋算符进度: 51/64, 当前算符: S_0 · S_50, 耗时: 8.141s
[2025-08-08 10:01:49] 自旋算符进度: 52/64, 当前算符: S_0 · S_51, 耗时: 8.127s
[2025-08-08 10:01:57] 自旋算符进度: 53/64, 当前算符: S_0 · S_52, 耗时: 8.126s
[2025-08-08 10:02:06] 自旋算符进度: 54/64, 当前算符: S_0 · S_53, 耗时: 8.133s
[2025-08-08 10:02:14] 自旋算符进度: 55/64, 当前算符: S_0 · S_54, 耗时: 8.141s
[2025-08-08 10:02:22] 自旋算符进度: 56/64, 当前算符: S_0 · S_55, 耗时: 8.136s
[2025-08-08 10:02:30] 自旋算符进度: 57/64, 当前算符: S_0 · S_56, 耗时: 8.150s
[2025-08-08 10:02:38] 自旋算符进度: 58/64, 当前算符: S_0 · S_57, 耗时: 8.141s
[2025-08-08 10:02:46] 自旋算符进度: 59/64, 当前算符: S_0 · S_58, 耗时: 8.125s
[2025-08-08 10:02:54] 自旋算符进度: 60/64, 当前算符: S_0 · S_59, 耗时: 8.141s
[2025-08-08 10:03:03] 自旋算符进度: 61/64, 当前算符: S_0 · S_60, 耗时: 8.132s
[2025-08-08 10:03:11] 自旋算符进度: 62/64, 当前算符: S_0 · S_61, 耗时: 8.138s
[2025-08-08 10:03:19] 自旋算符进度: 63/64, 当前算符: S_0 · S_62, 耗时: 8.141s
[2025-08-08 10:03:27] 自旋算符进度: 64/64, 当前算符: S_0 · S_63, 耗时: 8.137s
[2025-08-08 10:03:27] 自旋相关函数计算完成,总耗时 530.05 秒
[2025-08-08 10:03:27] 计算傅里叶变换...
[2025-08-08 10:03:28] 自旋结构因子计算完成
[2025-08-08 10:03:29] 自旋相关函数平均误差: 0.000672
