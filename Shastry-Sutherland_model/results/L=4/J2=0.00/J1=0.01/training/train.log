[2025-08-07 23:14:40] ✓ 从checkpoint恢复: results/L=4/J2=0.00/J1=0.02/training/checkpoints/final_GCNN.pkl
[2025-08-07 23:14:40]   - 迭代次数: final
[2025-08-07 23:14:40]   - 能量: -53.866643+0.002011j ± 0.085710
[2025-08-07 23:14:40]   - 时间戳: 2025-08-07T18:27:08.542571+08:00
[2025-08-07 23:14:48] ✓ 变分状态参数已从checkpoint恢复
[2025-08-07 23:14:48] ✓ 从final状态恢复, 重置迭代计数为0
[2025-08-07 23:14:48] ==================================================
[2025-08-07 23:14:48] GCNN for Shastry-Sutherland Model
[2025-08-07 23:14:48] ==================================================
[2025-08-07 23:14:48] System parameters:
[2025-08-07 23:14:48]   - System size: L=4, N=64
[2025-08-07 23:14:48]   - System parameters: J1=0.01, J2=0.0, Q=1.0
[2025-08-07 23:14:48] --------------------------------------------------
[2025-08-07 23:14:48] Model parameters:
[2025-08-07 23:14:48]   - Number of layers = 4
[2025-08-07 23:14:48]   - Number of features = 4
[2025-08-07 23:14:48]   - Total parameters = 12572
[2025-08-07 23:14:48] --------------------------------------------------
[2025-08-07 23:14:48] Training parameters:
[2025-08-07 23:14:48]   - Learning rate: 0.015
[2025-08-07 23:14:48]   - Total iterations: 1050
[2025-08-07 23:14:48]   - Annealing cycles: 3
[2025-08-07 23:14:48]   - Initial period: 150
[2025-08-07 23:14:48]   - Period multiplier: 2.0
[2025-08-07 23:14:48]   - Temperature range: 0.0-1.0
[2025-08-07 23:14:48]   - Samples: 4096
[2025-08-07 23:14:48]   - Discarded samples: 0
[2025-08-07 23:14:48]   - Chunk size: 2048
[2025-08-07 23:14:48]   - Diagonal shift: 0.2
[2025-08-07 23:14:48]   - Gradient clipping: 1.0
[2025-08-07 23:14:48]   - Checkpoint enabled: interval=100
[2025-08-07 23:14:48]   - Checkpoint directory: results/L=4/J2=0.00/J1=0.01/training/checkpoints
[2025-08-07 23:14:48] --------------------------------------------------
[2025-08-07 23:14:48] Device status:
[2025-08-07 23:14:48]   - Devices model: A100
[2025-08-07 23:14:48]   - Number of devices: 1
[2025-08-07 23:14:48]   - Sharding: True
[2025-08-07 23:14:48] ============================================================
[2025-08-07 23:15:18] [Iter 1/1050] R0[0/150], Temp: 1.0000, Energy: -53.178899-0.009601j
[2025-08-07 23:15:37] [Iter 2/1050] R0[1/150], Temp: 0.9999, Energy: -53.272192-0.005834j
[2025-08-07 23:15:41] [Iter 3/1050] R0[2/150], Temp: 0.9996, Energy: -53.276187-0.004398j
[2025-08-07 23:15:45] [Iter 4/1050] R0[3/150], Temp: 0.9990, Energy: -53.340040-0.009601j
[2025-08-07 23:15:49] [Iter 5/1050] R0[4/150], Temp: 0.9982, Energy: -53.316384-0.001634j
[2025-08-07 23:15:53] [Iter 6/1050] R0[5/150], Temp: 0.9973, Energy: -53.274728-0.010012j
[2025-08-07 23:15:57] [Iter 7/1050] R0[6/150], Temp: 0.9961, Energy: -53.346098-0.005518j
[2025-08-07 23:16:01] [Iter 8/1050] R0[7/150], Temp: 0.9946, Energy: -53.454528+0.001706j
[2025-08-07 23:16:06] [Iter 9/1050] R0[8/150], Temp: 0.9930, Energy: -53.425327-0.001252j
[2025-08-07 23:16:10] [Iter 10/1050] R0[9/150], Temp: 0.9911, Energy: -53.425638-0.015750j
[2025-08-07 23:16:14] [Iter 11/1050] R0[10/150], Temp: 0.9891, Energy: -53.529730-0.003878j
[2025-08-07 23:16:18] [Iter 12/1050] R0[11/150], Temp: 0.9868, Energy: -53.537597-0.007473j
[2025-08-07 23:16:22] [Iter 13/1050] R0[12/150], Temp: 0.9843, Energy: -53.580591-0.002269j
[2025-08-07 23:16:26] [Iter 14/1050] R0[13/150], Temp: 0.9816, Energy: -53.560688+0.002506j
[2025-08-07 23:16:30] [Iter 15/1050] R0[14/150], Temp: 0.9787, Energy: -53.511794-0.000080j
[2025-08-07 23:16:34] [Iter 16/1050] R0[15/150], Temp: 0.9755, Energy: -53.465395+0.002293j
[2025-08-07 23:16:39] [Iter 17/1050] R0[16/150], Temp: 0.9722, Energy: -53.457293+0.000727j
[2025-08-07 23:16:43] [Iter 18/1050] R0[17/150], Temp: 0.9686, Energy: -53.350065+0.003179j
[2025-08-07 23:16:47] [Iter 19/1050] R0[18/150], Temp: 0.9649, Energy: -53.369921-0.004581j
[2025-08-07 23:16:51] [Iter 20/1050] R0[19/150], Temp: 0.9609, Energy: -53.450977-0.004391j
[2025-08-07 23:16:55] [Iter 21/1050] R0[20/150], Temp: 0.9568, Energy: -53.565653+0.000738j
[2025-08-07 23:16:59] [Iter 22/1050] R0[21/150], Temp: 0.9524, Energy: -53.548303-0.003360j
[2025-08-07 23:17:03] [Iter 23/1050] R0[22/150], Temp: 0.9479, Energy: -53.467513-0.003313j
[2025-08-07 23:17:07] [Iter 24/1050] R0[23/150], Temp: 0.9431, Energy: -53.513162+0.006105j
[2025-08-07 23:17:12] [Iter 25/1050] R0[24/150], Temp: 0.9382, Energy: -53.580945+0.003111j
[2025-08-07 23:17:16] [Iter 26/1050] R0[25/150], Temp: 0.9330, Energy: -53.456478-0.002521j
[2025-08-07 23:17:20] [Iter 27/1050] R0[26/150], Temp: 0.9277, Energy: -53.495832+0.005207j
[2025-08-07 23:17:24] [Iter 28/1050] R0[27/150], Temp: 0.9222, Energy: -53.504489-0.001227j
[2025-08-07 23:17:28] [Iter 29/1050] R0[28/150], Temp: 0.9165, Energy: -53.471727-0.001994j
[2025-08-07 23:17:32] [Iter 30/1050] R0[29/150], Temp: 0.9106, Energy: -53.429485+0.004987j
[2025-08-07 23:17:36] [Iter 31/1050] R0[30/150], Temp: 0.9045, Energy: -53.431158+0.001851j
[2025-08-07 23:17:40] [Iter 32/1050] R0[31/150], Temp: 0.8983, Energy: -53.396773-0.000107j
[2025-08-07 23:17:45] [Iter 33/1050] R0[32/150], Temp: 0.8918, Energy: -53.420013+0.006635j
[2025-08-07 23:17:49] [Iter 34/1050] R0[33/150], Temp: 0.8853, Energy: -53.463089-0.002189j
[2025-08-07 23:17:53] [Iter 35/1050] R0[34/150], Temp: 0.8785, Energy: -53.435614+0.001134j
[2025-08-07 23:17:57] [Iter 36/1050] R0[35/150], Temp: 0.8716, Energy: -53.410897-0.005300j
[2025-08-07 23:18:01] [Iter 37/1050] R0[36/150], Temp: 0.8645, Energy: -53.377711+0.002468j
[2025-08-07 23:18:05] [Iter 38/1050] R0[37/150], Temp: 0.8572, Energy: -53.452494+0.001507j
[2025-08-07 23:18:09] [Iter 39/1050] R0[38/150], Temp: 0.8498, Energy: -53.403518-0.003795j
[2025-08-07 23:18:13] [Iter 40/1050] R0[39/150], Temp: 0.8423, Energy: -53.397632+0.010704j
[2025-08-07 23:18:17] [Iter 41/1050] R0[40/150], Temp: 0.8346, Energy: -53.489088-0.002961j
[2025-08-07 23:18:22] [Iter 42/1050] R0[41/150], Temp: 0.8267, Energy: -53.491716+0.003192j
[2025-08-07 23:18:26] [Iter 43/1050] R0[42/150], Temp: 0.8187, Energy: -53.464515+0.000742j
[2025-08-07 23:18:30] [Iter 44/1050] R0[43/150], Temp: 0.8106, Energy: -53.428212+0.000826j
[2025-08-07 23:18:34] [Iter 45/1050] R0[44/150], Temp: 0.8023, Energy: -53.350615-0.004373j
[2025-08-07 23:18:38] [Iter 46/1050] R0[45/150], Temp: 0.7939, Energy: -53.375347-0.001186j
[2025-08-07 23:18:42] [Iter 47/1050] R0[46/150], Temp: 0.7854, Energy: -53.343898-0.001951j
[2025-08-07 23:18:47] [Iter 48/1050] R0[47/150], Temp: 0.7767, Energy: -53.348729-0.001439j
[2025-08-07 23:18:51] [Iter 49/1050] R0[48/150], Temp: 0.7679, Energy: -53.360196+0.004597j
[2025-08-07 23:18:55] [Iter 50/1050] R0[49/150], Temp: 0.7590, Energy: -53.291117-0.001687j
[2025-08-07 23:18:59] [Iter 51/1050] R0[50/150], Temp: 0.7500, Energy: -53.308129-0.001133j
[2025-08-07 23:19:03] [Iter 52/1050] R0[51/150], Temp: 0.7409, Energy: -53.232108+0.000308j
[2025-08-07 23:19:07] [Iter 53/1050] R0[52/150], Temp: 0.7316, Energy: -53.347437-0.003130j
[2025-08-07 23:19:11] [Iter 54/1050] R0[53/150], Temp: 0.7223, Energy: -53.421017+0.000059j
[2025-08-07 23:19:15] [Iter 55/1050] R0[54/150], Temp: 0.7129, Energy: -53.477748-0.005951j
[2025-08-07 23:19:20] [Iter 56/1050] R0[55/150], Temp: 0.7034, Energy: -53.278294+0.002882j
[2025-08-07 23:19:24] [Iter 57/1050] R0[56/150], Temp: 0.6938, Energy: -53.289238+0.000203j
[2025-08-07 23:19:28] [Iter 58/1050] R0[57/150], Temp: 0.6841, Energy: -53.343069-0.002412j
[2025-08-07 23:19:32] [Iter 59/1050] R0[58/150], Temp: 0.6743, Energy: -53.378446+0.003413j
[2025-08-07 23:19:36] [Iter 60/1050] R0[59/150], Temp: 0.6644, Energy: -53.380065-0.001601j
[2025-08-07 23:19:40] [Iter 61/1050] R0[60/150], Temp: 0.6545, Energy: -53.447652-0.001495j
[2025-08-07 23:19:44] [Iter 62/1050] R0[61/150], Temp: 0.6445, Energy: -53.414561-0.000476j
[2025-08-07 23:19:48] [Iter 63/1050] R0[62/150], Temp: 0.6345, Energy: -53.497737+0.001773j
[2025-08-07 23:19:53] [Iter 64/1050] R0[63/150], Temp: 0.6243, Energy: -53.465759+0.002340j
[2025-08-07 23:19:57] [Iter 65/1050] R0[64/150], Temp: 0.6142, Energy: -53.461150+0.001545j
[2025-08-07 23:20:01] [Iter 66/1050] R0[65/150], Temp: 0.6040, Energy: -53.532670-0.001776j
[2025-08-07 23:20:05] [Iter 67/1050] R0[66/150], Temp: 0.5937, Energy: -53.547576+0.003794j
[2025-08-07 23:20:09] [Iter 68/1050] R0[67/150], Temp: 0.5834, Energy: -53.407786+0.007450j
[2025-08-07 23:20:13] [Iter 69/1050] R0[68/150], Temp: 0.5730, Energy: -53.298373+0.000129j
[2025-08-07 23:20:17] [Iter 70/1050] R0[69/150], Temp: 0.5627, Energy: -53.342586-0.000938j
[2025-08-07 23:20:21] [Iter 71/1050] R0[70/150], Temp: 0.5523, Energy: -53.183927+0.000875j
[2025-08-07 23:20:26] [Iter 72/1050] R0[71/150], Temp: 0.5418, Energy: -53.259333+0.001761j
[2025-08-07 23:20:30] [Iter 73/1050] R0[72/150], Temp: 0.5314, Energy: -53.285975+0.003207j
[2025-08-07 23:20:34] [Iter 74/1050] R0[73/150], Temp: 0.5209, Energy: -53.287897-0.001921j
[2025-08-07 23:20:38] [Iter 75/1050] R0[74/150], Temp: 0.5105, Energy: -53.407627+0.001353j
[2025-08-07 23:20:42] [Iter 76/1050] R0[75/150], Temp: 0.5000, Energy: -53.373584-0.005108j
[2025-08-07 23:20:46] [Iter 77/1050] R0[76/150], Temp: 0.4895, Energy: -53.443903-0.002417j
[2025-08-07 23:20:50] [Iter 78/1050] R0[77/150], Temp: 0.4791, Energy: -53.479448-0.000042j
[2025-08-07 23:20:54] [Iter 79/1050] R0[78/150], Temp: 0.4686, Energy: -53.507817-0.002075j
[2025-08-07 23:20:58] [Iter 80/1050] R0[79/150], Temp: 0.4582, Energy: -53.494282+0.001451j
[2025-08-07 23:21:03] [Iter 81/1050] R0[80/150], Temp: 0.4477, Energy: -53.448980+0.000226j
[2025-08-07 23:21:07] [Iter 82/1050] R0[81/150], Temp: 0.4373, Energy: -53.416616+0.001030j
[2025-08-07 23:21:11] [Iter 83/1050] R0[82/150], Temp: 0.4270, Energy: -53.364499+0.006089j
[2025-08-07 23:21:15] [Iter 84/1050] R0[83/150], Temp: 0.4166, Energy: -53.401221-0.003081j
[2025-08-07 23:21:19] [Iter 85/1050] R0[84/150], Temp: 0.4063, Energy: -53.410922+0.000977j
[2025-08-07 23:21:23] [Iter 86/1050] R0[85/150], Temp: 0.3960, Energy: -53.348532+0.001125j
[2025-08-07 23:21:27] [Iter 87/1050] R0[86/150], Temp: 0.3858, Energy: -53.383245-0.000670j
[2025-08-07 23:21:31] [Iter 88/1050] R0[87/150], Temp: 0.3757, Energy: -53.420547+0.003112j
[2025-08-07 23:21:36] [Iter 89/1050] R0[88/150], Temp: 0.3655, Energy: -53.358397+0.002357j
[2025-08-07 23:21:40] [Iter 90/1050] R0[89/150], Temp: 0.3555, Energy: -53.340420+0.000692j
[2025-08-07 23:21:44] [Iter 91/1050] R0[90/150], Temp: 0.3455, Energy: -53.352602-0.003436j
[2025-08-07 23:21:48] [Iter 92/1050] R0[91/150], Temp: 0.3356, Energy: -53.238717+0.006054j
[2025-08-07 23:21:52] [Iter 93/1050] R0[92/150], Temp: 0.3257, Energy: -53.269222-0.006030j
[2025-08-07 23:21:56] [Iter 94/1050] R0[93/150], Temp: 0.3159, Energy: -53.314322-0.005360j
[2025-08-07 23:22:00] [Iter 95/1050] R0[94/150], Temp: 0.3062, Energy: -53.439816-0.003950j
[2025-08-07 23:22:05] [Iter 96/1050] R0[95/150], Temp: 0.2966, Energy: -53.455552-0.001253j
[2025-08-07 23:22:09] [Iter 97/1050] R0[96/150], Temp: 0.2871, Energy: -53.472693-0.005209j
[2025-08-07 23:22:13] [Iter 98/1050] R0[97/150], Temp: 0.2777, Energy: -53.590448-0.002426j
[2025-08-07 23:22:17] [Iter 99/1050] R0[98/150], Temp: 0.2684, Energy: -53.502360+0.001797j
[2025-08-07 23:22:21] [Iter 100/1050] R0[99/150], Temp: 0.2591, Energy: -53.618516+0.000605j
[2025-08-07 23:22:21] ✓ Checkpoint saved: checkpoint_iter_000100.pkl
[2025-08-07 23:22:25] [Iter 101/1050] R0[100/150], Temp: 0.2500, Energy: -53.606582-0.001175j
[2025-08-07 23:22:29] [Iter 102/1050] R0[101/150], Temp: 0.2410, Energy: -53.628728-0.003559j
[2025-08-07 23:22:33] [Iter 103/1050] R0[102/150], Temp: 0.2321, Energy: -53.491906-0.003846j
[2025-08-07 23:22:38] [Iter 104/1050] R0[103/150], Temp: 0.2233, Energy: -53.632356-0.001946j
[2025-08-07 23:22:42] [Iter 105/1050] R0[104/150], Temp: 0.2146, Energy: -53.540907-0.000788j
[2025-08-07 23:22:46] [Iter 106/1050] R0[105/150], Temp: 0.2061, Energy: -53.570622-0.001973j
[2025-08-07 23:22:50] [Iter 107/1050] R0[106/150], Temp: 0.1977, Energy: -53.416288-0.007385j
[2025-08-07 23:22:54] [Iter 108/1050] R0[107/150], Temp: 0.1894, Energy: -53.531546+0.004640j
[2025-08-07 23:22:58] [Iter 109/1050] R0[108/150], Temp: 0.1813, Energy: -53.489618+0.001213j
[2025-08-07 23:23:02] [Iter 110/1050] R0[109/150], Temp: 0.1733, Energy: -53.540898-0.005538j
[2025-08-07 23:23:06] [Iter 111/1050] R0[110/150], Temp: 0.1654, Energy: -53.332708+0.000413j
[2025-08-07 23:23:11] [Iter 112/1050] R0[111/150], Temp: 0.1577, Energy: -53.430366+0.001175j
[2025-08-07 23:23:15] [Iter 113/1050] R0[112/150], Temp: 0.1502, Energy: -53.390940+0.003342j
[2025-08-07 23:23:19] [Iter 114/1050] R0[113/150], Temp: 0.1428, Energy: -53.472348+0.000152j
[2025-08-07 23:23:23] [Iter 115/1050] R0[114/150], Temp: 0.1355, Energy: -53.435189+0.001399j
[2025-08-07 23:23:27] [Iter 116/1050] R0[115/150], Temp: 0.1284, Energy: -53.530574+0.004678j
[2025-08-07 23:23:31] [Iter 117/1050] R0[116/150], Temp: 0.1215, Energy: -53.538165-0.001831j
[2025-08-07 23:23:35] [Iter 118/1050] R0[117/150], Temp: 0.1147, Energy: -53.522603-0.002921j
[2025-08-07 23:23:39] [Iter 119/1050] R0[118/150], Temp: 0.1082, Energy: -53.573818-0.000684j
[2025-08-07 23:23:44] [Iter 120/1050] R0[119/150], Temp: 0.1017, Energy: -53.547905+0.004153j
[2025-08-07 23:23:48] [Iter 121/1050] R0[120/150], Temp: 0.0955, Energy: -53.620203+0.000764j
[2025-08-07 23:23:52] [Iter 122/1050] R0[121/150], Temp: 0.0894, Energy: -53.603396-0.001206j
[2025-08-07 23:23:56] [Iter 123/1050] R0[122/150], Temp: 0.0835, Energy: -53.570824+0.000686j
[2025-08-07 23:24:00] [Iter 124/1050] R0[123/150], Temp: 0.0778, Energy: -53.523639-0.002571j
[2025-08-07 23:24:04] [Iter 125/1050] R0[124/150], Temp: 0.0723, Energy: -53.524745-0.007150j
[2025-08-07 23:24:08] [Iter 126/1050] R0[125/150], Temp: 0.0670, Energy: -53.510426+0.001750j
[2025-08-07 23:24:12] [Iter 127/1050] R0[126/150], Temp: 0.0618, Energy: -53.516900-0.009454j
[2025-08-07 23:24:17] [Iter 128/1050] R0[127/150], Temp: 0.0569, Energy: -53.583418+0.003124j
[2025-08-07 23:24:21] [Iter 129/1050] R0[128/150], Temp: 0.0521, Energy: -53.602129+0.006921j
[2025-08-07 23:24:25] [Iter 130/1050] R0[129/150], Temp: 0.0476, Energy: -53.461308+0.001490j
[2025-08-07 23:24:29] [Iter 131/1050] R0[130/150], Temp: 0.0432, Energy: -53.387599+0.005247j
[2025-08-07 23:24:33] [Iter 132/1050] R0[131/150], Temp: 0.0391, Energy: -53.515818+0.002135j
[2025-08-07 23:24:37] [Iter 133/1050] R0[132/150], Temp: 0.0351, Energy: -53.590208-0.004114j
[2025-08-07 23:24:41] [Iter 134/1050] R0[133/150], Temp: 0.0314, Energy: -53.582428-0.000957j
[2025-08-07 23:24:45] [Iter 135/1050] R0[134/150], Temp: 0.0278, Energy: -53.594973+0.002831j
[2025-08-07 23:24:50] [Iter 136/1050] R0[135/150], Temp: 0.0245, Energy: -53.534727-0.000315j
[2025-08-07 23:24:54] [Iter 137/1050] R0[136/150], Temp: 0.0213, Energy: -53.564566-0.002287j
[2025-08-07 23:24:58] [Iter 138/1050] R0[137/150], Temp: 0.0184, Energy: -53.546544-0.000285j
[2025-08-07 23:25:02] [Iter 139/1050] R0[138/150], Temp: 0.0157, Energy: -53.565173+0.000717j
[2025-08-07 23:25:06] [Iter 140/1050] R0[139/150], Temp: 0.0132, Energy: -53.623206+0.000357j
[2025-08-07 23:25:10] [Iter 141/1050] R0[140/150], Temp: 0.0109, Energy: -53.572872+0.006171j
[2025-08-07 23:25:14] [Iter 142/1050] R0[141/150], Temp: 0.0089, Energy: -53.448056+0.003596j
[2025-08-07 23:25:18] [Iter 143/1050] R0[142/150], Temp: 0.0070, Energy: -53.463442-0.008585j
[2025-08-07 23:25:23] [Iter 144/1050] R0[143/150], Temp: 0.0054, Energy: -53.466207+0.000584j
[2025-08-07 23:25:27] [Iter 145/1050] R0[144/150], Temp: 0.0039, Energy: -53.599356+0.007041j
[2025-08-07 23:25:31] [Iter 146/1050] R0[145/150], Temp: 0.0027, Energy: -53.507000+0.002336j
[2025-08-07 23:25:35] [Iter 147/1050] R0[146/150], Temp: 0.0018, Energy: -53.570509-0.001520j
[2025-08-07 23:25:39] [Iter 148/1050] R0[147/150], Temp: 0.0010, Energy: -53.515005+0.004493j
[2025-08-07 23:25:43] [Iter 149/1050] R0[148/150], Temp: 0.0004, Energy: -53.538427+0.000133j
[2025-08-07 23:25:47] [Iter 150/1050] R0[149/150], Temp: 0.0001, Energy: -53.488552-0.003567j
[2025-08-07 23:25:47] RESTART #1 | Period: 300
[2025-08-07 23:25:51] [Iter 151/1050] R1[0/300], Temp: 1.0000, Energy: -53.514733-0.000423j
[2025-08-07 23:25:56] [Iter 152/1050] R1[1/300], Temp: 1.0000, Energy: -53.507515-0.001572j
[2025-08-07 23:26:00] [Iter 153/1050] R1[2/300], Temp: 0.9999, Energy: -53.371112+0.002977j
[2025-08-07 23:26:04] [Iter 154/1050] R1[3/300], Temp: 0.9998, Energy: -53.392702-0.000216j
[2025-08-07 23:26:08] [Iter 155/1050] R1[4/300], Temp: 0.9996, Energy: -53.450533-0.004457j
[2025-08-07 23:26:12] [Iter 156/1050] R1[5/300], Temp: 0.9993, Energy: -53.453091-0.007396j
[2025-08-07 23:26:16] [Iter 157/1050] R1[6/300], Temp: 0.9990, Energy: -53.491169-0.005298j
[2025-08-07 23:26:20] [Iter 158/1050] R1[7/300], Temp: 0.9987, Energy: -53.324959-0.002044j
[2025-08-07 23:26:24] [Iter 159/1050] R1[8/300], Temp: 0.9982, Energy: -53.232805-0.001572j
[2025-08-07 23:26:28] [Iter 160/1050] R1[9/300], Temp: 0.9978, Energy: -53.359672+0.001335j
[2025-08-07 23:26:33] [Iter 161/1050] R1[10/300], Temp: 0.9973, Energy: -53.226293-0.001313j
[2025-08-07 23:26:37] [Iter 162/1050] R1[11/300], Temp: 0.9967, Energy: -53.225886-0.000777j
[2025-08-07 23:26:41] [Iter 163/1050] R1[12/300], Temp: 0.9961, Energy: -53.235720+0.001493j
[2025-08-07 23:26:45] [Iter 164/1050] R1[13/300], Temp: 0.9954, Energy: -53.319212-0.001861j
[2025-08-07 23:26:49] [Iter 165/1050] R1[14/300], Temp: 0.9946, Energy: -53.219780-0.002140j
[2025-08-07 23:26:53] [Iter 166/1050] R1[15/300], Temp: 0.9938, Energy: -53.326911-0.000887j
[2025-08-07 23:26:57] [Iter 167/1050] R1[16/300], Temp: 0.9930, Energy: -53.461697-0.000633j
[2025-08-07 23:27:01] [Iter 168/1050] R1[17/300], Temp: 0.9921, Energy: -53.400434+0.000276j
[2025-08-07 23:27:06] [Iter 169/1050] R1[18/300], Temp: 0.9911, Energy: -53.441838-0.000428j
[2025-08-07 23:27:10] [Iter 170/1050] R1[19/300], Temp: 0.9901, Energy: -53.450125-0.004682j
[2025-08-07 23:27:14] [Iter 171/1050] R1[20/300], Temp: 0.9891, Energy: -53.506900+0.000673j
[2025-08-07 23:27:18] [Iter 172/1050] R1[21/300], Temp: 0.9880, Energy: -53.540655+0.004861j
[2025-08-07 23:27:22] [Iter 173/1050] R1[22/300], Temp: 0.9868, Energy: -53.577843-0.000395j
[2025-08-07 23:27:26] [Iter 174/1050] R1[23/300], Temp: 0.9856, Energy: -53.495637+0.001894j
[2025-08-07 23:27:30] [Iter 175/1050] R1[24/300], Temp: 0.9843, Energy: -53.557708+0.000080j
[2025-08-07 23:27:35] [Iter 176/1050] R1[25/300], Temp: 0.9830, Energy: -53.599184-0.007409j
[2025-08-07 23:27:39] [Iter 177/1050] R1[26/300], Temp: 0.9816, Energy: -53.638458+0.002318j
[2025-08-07 23:27:43] [Iter 178/1050] R1[27/300], Temp: 0.9801, Energy: -53.568814-0.000217j
[2025-08-07 23:27:47] [Iter 179/1050] R1[28/300], Temp: 0.9787, Energy: -53.545499-0.007411j
[2025-08-07 23:27:51] [Iter 180/1050] R1[29/300], Temp: 0.9771, Energy: -53.426124-0.002802j
[2025-08-07 23:27:55] [Iter 181/1050] R1[30/300], Temp: 0.9755, Energy: -53.425788+0.000029j
[2025-08-07 23:27:59] [Iter 182/1050] R1[31/300], Temp: 0.9739, Energy: -53.342972-0.001209j
[2025-08-07 23:28:03] [Iter 183/1050] R1[32/300], Temp: 0.9722, Energy: -53.255464+0.006949j
[2025-08-07 23:28:08] [Iter 184/1050] R1[33/300], Temp: 0.9704, Energy: -53.343845+0.001830j
[2025-08-07 23:28:12] [Iter 185/1050] R1[34/300], Temp: 0.9686, Energy: -53.525733+0.000112j
[2025-08-07 23:28:16] [Iter 186/1050] R1[35/300], Temp: 0.9668, Energy: -53.399673+0.004627j
[2025-08-07 23:28:20] [Iter 187/1050] R1[36/300], Temp: 0.9649, Energy: -53.441462+0.002326j
[2025-08-07 23:28:24] [Iter 188/1050] R1[37/300], Temp: 0.9629, Energy: -53.344639+0.001617j
[2025-08-07 23:28:28] [Iter 189/1050] R1[38/300], Temp: 0.9609, Energy: -53.250637-0.002481j
[2025-08-07 23:28:32] [Iter 190/1050] R1[39/300], Temp: 0.9589, Energy: -53.240493-0.003868j
[2025-08-07 23:28:36] [Iter 191/1050] R1[40/300], Temp: 0.9568, Energy: -53.244184+0.002233j
[2025-08-07 23:28:41] [Iter 192/1050] R1[41/300], Temp: 0.9546, Energy: -53.282139-0.003192j
[2025-08-07 23:28:45] [Iter 193/1050] R1[42/300], Temp: 0.9524, Energy: -53.337696-0.003117j
[2025-08-07 23:28:49] [Iter 194/1050] R1[43/300], Temp: 0.9502, Energy: -53.381234+0.000822j
[2025-08-07 23:28:53] [Iter 195/1050] R1[44/300], Temp: 0.9479, Energy: -53.399992-0.002057j
[2025-08-07 23:28:57] [Iter 196/1050] R1[45/300], Temp: 0.9455, Energy: -53.420739+0.002297j
[2025-08-07 23:29:01] [Iter 197/1050] R1[46/300], Temp: 0.9431, Energy: -53.382035-0.001239j
[2025-08-07 23:29:05] [Iter 198/1050] R1[47/300], Temp: 0.9407, Energy: -53.434381+0.005370j
[2025-08-07 23:29:09] [Iter 199/1050] R1[48/300], Temp: 0.9382, Energy: -53.538753-0.002964j
[2025-08-07 23:29:14] [Iter 200/1050] R1[49/300], Temp: 0.9356, Energy: -53.522617-0.002032j
[2025-08-07 23:29:14] ✓ Checkpoint saved: checkpoint_iter_000200.pkl
[2025-08-07 23:29:18] [Iter 201/1050] R1[50/300], Temp: 0.9330, Energy: -53.496190-0.001765j
[2025-08-07 23:29:22] [Iter 202/1050] R1[51/300], Temp: 0.9304, Energy: -53.463586+0.003654j
[2025-08-07 23:29:26] [Iter 203/1050] R1[52/300], Temp: 0.9277, Energy: -53.417958+0.010187j
[2025-08-07 23:29:30] [Iter 204/1050] R1[53/300], Temp: 0.9249, Energy: -53.500020+0.003178j
[2025-08-07 23:29:34] [Iter 205/1050] R1[54/300], Temp: 0.9222, Energy: -53.488271+0.003232j
[2025-08-07 23:29:38] [Iter 206/1050] R1[55/300], Temp: 0.9193, Energy: -53.429030+0.003218j
[2025-08-07 23:29:42] [Iter 207/1050] R1[56/300], Temp: 0.9165, Energy: -53.481128-0.002465j
[2025-08-07 23:29:47] [Iter 208/1050] R1[57/300], Temp: 0.9135, Energy: -53.416806+0.002434j
[2025-08-07 23:29:51] [Iter 209/1050] R1[58/300], Temp: 0.9106, Energy: -53.580251+0.002171j
[2025-08-07 23:29:55] [Iter 210/1050] R1[59/300], Temp: 0.9076, Energy: -53.503319+0.003072j
[2025-08-07 23:29:59] [Iter 211/1050] R1[60/300], Temp: 0.9045, Energy: -53.432470-0.002069j
[2025-08-07 23:30:03] [Iter 212/1050] R1[61/300], Temp: 0.9014, Energy: -53.423768-0.001263j
[2025-08-07 23:30:07] [Iter 213/1050] R1[62/300], Temp: 0.8983, Energy: -53.472553-0.003512j
[2025-08-07 23:30:11] [Iter 214/1050] R1[63/300], Temp: 0.8951, Energy: -53.553646-0.000544j
[2025-08-07 23:30:15] [Iter 215/1050] R1[64/300], Temp: 0.8918, Energy: -53.538965+0.001622j
[2025-08-07 23:30:20] [Iter 216/1050] R1[65/300], Temp: 0.8886, Energy: -53.387778+0.006857j
[2025-08-07 23:30:24] [Iter 217/1050] R1[66/300], Temp: 0.8853, Energy: -53.420530-0.000616j
[2025-08-07 23:30:28] [Iter 218/1050] R1[67/300], Temp: 0.8819, Energy: -53.491236-0.000539j
[2025-08-07 23:30:32] [Iter 219/1050] R1[68/300], Temp: 0.8785, Energy: -53.509826+0.001937j
[2025-08-07 23:30:36] [Iter 220/1050] R1[69/300], Temp: 0.8751, Energy: -53.418002-0.003048j
[2025-08-07 23:30:40] [Iter 221/1050] R1[70/300], Temp: 0.8716, Energy: -53.445578+0.003897j
[2025-08-07 23:30:44] [Iter 222/1050] R1[71/300], Temp: 0.8680, Energy: -53.458648-0.001381j
[2025-08-07 23:30:48] [Iter 223/1050] R1[72/300], Temp: 0.8645, Energy: -53.474144-0.002100j
[2025-08-07 23:30:53] [Iter 224/1050] R1[73/300], Temp: 0.8609, Energy: -53.488121+0.002702j
[2025-08-07 23:30:57] [Iter 225/1050] R1[74/300], Temp: 0.8572, Energy: -53.580598+0.001563j
[2025-08-07 23:31:01] [Iter 226/1050] R1[75/300], Temp: 0.8536, Energy: -53.422046+0.001456j
[2025-08-07 23:31:05] [Iter 227/1050] R1[76/300], Temp: 0.8498, Energy: -53.432407+0.001199j
[2025-08-07 23:31:09] [Iter 228/1050] R1[77/300], Temp: 0.8461, Energy: -53.429609+0.005096j
[2025-08-07 23:31:13] [Iter 229/1050] R1[78/300], Temp: 0.8423, Energy: -53.401850-0.001221j
[2025-08-07 23:31:17] [Iter 230/1050] R1[79/300], Temp: 0.8384, Energy: -53.450073-0.000035j
[2025-08-07 23:31:21] [Iter 231/1050] R1[80/300], Temp: 0.8346, Energy: -53.428299+0.004874j
[2025-08-07 23:31:25] [Iter 232/1050] R1[81/300], Temp: 0.8307, Energy: -53.413944+0.007437j
[2025-08-07 23:31:30] [Iter 233/1050] R1[82/300], Temp: 0.8267, Energy: -53.370337-0.001807j
[2025-08-07 23:31:34] [Iter 234/1050] R1[83/300], Temp: 0.8227, Energy: -53.361278-0.002305j
[2025-08-07 23:31:38] [Iter 235/1050] R1[84/300], Temp: 0.8187, Energy: -53.420849+0.001637j
[2025-08-07 23:31:42] [Iter 236/1050] R1[85/300], Temp: 0.8147, Energy: -53.370566-0.006286j
[2025-08-07 23:31:46] [Iter 237/1050] R1[86/300], Temp: 0.8106, Energy: -53.468903-0.002233j
[2025-08-07 23:31:50] [Iter 238/1050] R1[87/300], Temp: 0.8065, Energy: -53.404504-0.000570j
[2025-08-07 23:31:54] [Iter 239/1050] R1[88/300], Temp: 0.8023, Energy: -53.441364+0.000614j
[2025-08-07 23:31:58] [Iter 240/1050] R1[89/300], Temp: 0.7981, Energy: -53.353333-0.000296j
[2025-08-07 23:32:03] [Iter 241/1050] R1[90/300], Temp: 0.7939, Energy: -53.283773-0.003382j
[2025-08-07 23:32:07] [Iter 242/1050] R1[91/300], Temp: 0.7896, Energy: -53.353027-0.005461j
[2025-08-07 23:32:11] [Iter 243/1050] R1[92/300], Temp: 0.7854, Energy: -53.337605+0.004429j
[2025-08-07 23:32:15] [Iter 244/1050] R1[93/300], Temp: 0.7810, Energy: -53.292411+0.001076j
[2025-08-07 23:32:19] [Iter 245/1050] R1[94/300], Temp: 0.7767, Energy: -53.364200+0.000729j
[2025-08-07 23:32:23] [Iter 246/1050] R1[95/300], Temp: 0.7723, Energy: -53.389163+0.006406j
[2025-08-07 23:32:27] [Iter 247/1050] R1[96/300], Temp: 0.7679, Energy: -53.347715+0.001634j
[2025-08-07 23:32:31] [Iter 248/1050] R1[97/300], Temp: 0.7635, Energy: -53.347065+0.004830j
[2025-08-07 23:32:36] [Iter 249/1050] R1[98/300], Temp: 0.7590, Energy: -53.433570-0.002781j
[2025-08-07 23:32:40] [Iter 250/1050] R1[99/300], Temp: 0.7545, Energy: -53.421897-0.000788j
[2025-08-07 23:32:44] [Iter 251/1050] R1[100/300], Temp: 0.7500, Energy: -53.389339+0.011016j
[2025-08-07 23:32:48] [Iter 252/1050] R1[101/300], Temp: 0.7455, Energy: -53.501358+0.004365j
[2025-08-07 23:32:52] [Iter 253/1050] R1[102/300], Temp: 0.7409, Energy: -53.571005+0.002993j
[2025-08-07 23:32:56] [Iter 254/1050] R1[103/300], Temp: 0.7363, Energy: -53.504787+0.000146j
[2025-08-07 23:33:00] [Iter 255/1050] R1[104/300], Temp: 0.7316, Energy: -53.436115-0.004640j
[2025-08-07 23:33:05] [Iter 256/1050] R1[105/300], Temp: 0.7270, Energy: -53.486350-0.003973j
[2025-08-07 23:33:09] [Iter 257/1050] R1[106/300], Temp: 0.7223, Energy: -53.442089+0.002384j
[2025-08-07 23:33:13] [Iter 258/1050] R1[107/300], Temp: 0.7176, Energy: -53.456105+0.002769j
[2025-08-07 23:33:17] [Iter 259/1050] R1[108/300], Temp: 0.7129, Energy: -53.413548-0.001837j
[2025-08-07 23:33:21] [Iter 260/1050] R1[109/300], Temp: 0.7081, Energy: -53.421375+0.000392j
[2025-08-07 23:33:25] [Iter 261/1050] R1[110/300], Temp: 0.7034, Energy: -53.289582+0.001239j
[2025-08-07 23:33:29] [Iter 262/1050] R1[111/300], Temp: 0.6986, Energy: -53.349297+0.002827j
[2025-08-07 23:33:33] [Iter 263/1050] R1[112/300], Temp: 0.6938, Energy: -53.391544+0.001735j
[2025-08-07 23:33:38] [Iter 264/1050] R1[113/300], Temp: 0.6889, Energy: -53.391052+0.000003j
[2025-08-07 23:33:42] [Iter 265/1050] R1[114/300], Temp: 0.6841, Energy: -53.422613+0.000461j
[2025-08-07 23:33:46] [Iter 266/1050] R1[115/300], Temp: 0.6792, Energy: -53.366608+0.000554j
[2025-08-07 23:33:50] [Iter 267/1050] R1[116/300], Temp: 0.6743, Energy: -53.288352-0.004484j
[2025-08-07 23:33:54] [Iter 268/1050] R1[117/300], Temp: 0.6694, Energy: -53.388564-0.002799j
[2025-08-07 23:33:58] [Iter 269/1050] R1[118/300], Temp: 0.6644, Energy: -53.349138+0.002397j
[2025-08-07 23:34:02] [Iter 270/1050] R1[119/300], Temp: 0.6595, Energy: -53.472963-0.004041j
[2025-08-07 23:34:06] [Iter 271/1050] R1[120/300], Temp: 0.6545, Energy: -53.427134-0.000435j
[2025-08-07 23:34:11] [Iter 272/1050] R1[121/300], Temp: 0.6495, Energy: -53.414254-0.003686j
[2025-08-07 23:34:15] [Iter 273/1050] R1[122/300], Temp: 0.6445, Energy: -53.420636-0.004033j
[2025-08-07 23:34:19] [Iter 274/1050] R1[123/300], Temp: 0.6395, Energy: -53.473824-0.006279j
[2025-08-07 23:34:23] [Iter 275/1050] R1[124/300], Temp: 0.6345, Energy: -53.539199+0.001722j
[2025-08-07 23:34:27] [Iter 276/1050] R1[125/300], Temp: 0.6294, Energy: -53.453131-0.002689j
[2025-08-07 23:34:31] [Iter 277/1050] R1[126/300], Temp: 0.6243, Energy: -53.468877+0.000236j
[2025-08-07 23:34:35] [Iter 278/1050] R1[127/300], Temp: 0.6193, Energy: -53.609474-0.003534j
[2025-08-07 23:34:39] [Iter 279/1050] R1[128/300], Temp: 0.6142, Energy: -53.549990+0.001749j
[2025-08-07 23:34:44] [Iter 280/1050] R1[129/300], Temp: 0.6091, Energy: -53.521726+0.002405j
[2025-08-07 23:34:48] [Iter 281/1050] R1[130/300], Temp: 0.6040, Energy: -53.503947+0.002512j
[2025-08-07 23:34:52] [Iter 282/1050] R1[131/300], Temp: 0.5988, Energy: -53.661307-0.002145j
[2025-08-07 23:34:56] [Iter 283/1050] R1[132/300], Temp: 0.5937, Energy: -53.589014-0.001241j
[2025-08-07 23:35:00] [Iter 284/1050] R1[133/300], Temp: 0.5885, Energy: -53.631601-0.001308j
[2025-08-07 23:35:04] [Iter 285/1050] R1[134/300], Temp: 0.5834, Energy: -53.551254+0.001370j
[2025-08-07 23:35:08] [Iter 286/1050] R1[135/300], Temp: 0.5782, Energy: -53.495744+0.001013j
[2025-08-07 23:35:12] [Iter 287/1050] R1[136/300], Temp: 0.5730, Energy: -53.460602-0.002307j
[2025-08-07 23:35:17] [Iter 288/1050] R1[137/300], Temp: 0.5679, Energy: -53.495402-0.001774j
[2025-08-07 23:35:21] [Iter 289/1050] R1[138/300], Temp: 0.5627, Energy: -53.598302-0.001191j
[2025-08-07 23:35:25] [Iter 290/1050] R1[139/300], Temp: 0.5575, Energy: -53.584698-0.003773j
[2025-08-07 23:35:29] [Iter 291/1050] R1[140/300], Temp: 0.5523, Energy: -53.510959+0.000837j
[2025-08-07 23:35:33] [Iter 292/1050] R1[141/300], Temp: 0.5471, Energy: -53.577263-0.002889j
[2025-08-07 23:35:37] [Iter 293/1050] R1[142/300], Temp: 0.5418, Energy: -53.426903+0.007503j
[2025-08-07 23:35:41] [Iter 294/1050] R1[143/300], Temp: 0.5366, Energy: -53.498988+0.000060j
[2025-08-07 23:35:45] [Iter 295/1050] R1[144/300], Temp: 0.5314, Energy: -53.538016+0.002305j
[2025-08-07 23:35:50] [Iter 296/1050] R1[145/300], Temp: 0.5262, Energy: -53.508256+0.006390j
[2025-08-07 23:35:54] [Iter 297/1050] R1[146/300], Temp: 0.5209, Energy: -53.456658-0.002416j
[2025-08-07 23:35:58] [Iter 298/1050] R1[147/300], Temp: 0.5157, Energy: -53.441530-0.000753j
[2025-08-07 23:36:02] [Iter 299/1050] R1[148/300], Temp: 0.5105, Energy: -53.408639+0.001480j
[2025-08-07 23:36:06] [Iter 300/1050] R1[149/300], Temp: 0.5052, Energy: -53.383844+0.000623j
[2025-08-07 23:36:06] ✓ Checkpoint saved: checkpoint_iter_000300.pkl
[2025-08-07 23:36:10] [Iter 301/1050] R1[150/300], Temp: 0.5000, Energy: -53.282849+0.006762j
[2025-08-07 23:36:14] [Iter 302/1050] R1[151/300], Temp: 0.4948, Energy: -53.285736+0.000373j
[2025-08-07 23:36:18] [Iter 303/1050] R1[152/300], Temp: 0.4895, Energy: -53.280273+0.002551j
[2025-08-07 23:36:23] [Iter 304/1050] R1[153/300], Temp: 0.4843, Energy: -53.312515+0.001304j
[2025-08-07 23:36:27] [Iter 305/1050] R1[154/300], Temp: 0.4791, Energy: -53.333121+0.000579j
[2025-08-07 23:36:31] [Iter 306/1050] R1[155/300], Temp: 0.4738, Energy: -53.327346+0.002114j
[2025-08-07 23:36:35] [Iter 307/1050] R1[156/300], Temp: 0.4686, Energy: -53.401264+0.003377j
[2025-08-07 23:36:39] [Iter 308/1050] R1[157/300], Temp: 0.4634, Energy: -53.417703+0.007277j
[2025-08-07 23:36:43] [Iter 309/1050] R1[158/300], Temp: 0.4582, Energy: -53.358215+0.001437j
[2025-08-07 23:36:47] [Iter 310/1050] R1[159/300], Temp: 0.4529, Energy: -53.332927-0.005783j
[2025-08-07 23:36:51] [Iter 311/1050] R1[160/300], Temp: 0.4477, Energy: -53.475819-0.003315j
[2025-08-07 23:36:55] [Iter 312/1050] R1[161/300], Temp: 0.4425, Energy: -53.403486+0.001459j
[2025-08-07 23:37:00] [Iter 313/1050] R1[162/300], Temp: 0.4373, Energy: -53.492122-0.004578j
[2025-08-07 23:37:04] [Iter 314/1050] R1[163/300], Temp: 0.4321, Energy: -53.376908-0.003073j
[2025-08-07 23:37:08] [Iter 315/1050] R1[164/300], Temp: 0.4270, Energy: -53.340866+0.000710j
[2025-08-07 23:37:12] [Iter 316/1050] R1[165/300], Temp: 0.4218, Energy: -53.348659-0.002978j
[2025-08-07 23:37:16] [Iter 317/1050] R1[166/300], Temp: 0.4166, Energy: -53.407262+0.001784j
[2025-08-07 23:37:20] [Iter 318/1050] R1[167/300], Temp: 0.4115, Energy: -53.319570-0.001220j
[2025-08-07 23:37:24] [Iter 319/1050] R1[168/300], Temp: 0.4063, Energy: -53.364002-0.002594j
[2025-08-07 23:37:28] [Iter 320/1050] R1[169/300], Temp: 0.4012, Energy: -53.471569+0.000485j
[2025-08-07 23:37:33] [Iter 321/1050] R1[170/300], Temp: 0.3960, Energy: -53.326922+0.004722j
[2025-08-07 23:37:37] [Iter 322/1050] R1[171/300], Temp: 0.3909, Energy: -53.394377+0.000246j
[2025-08-07 23:37:41] [Iter 323/1050] R1[172/300], Temp: 0.3858, Energy: -53.456667-0.002962j
[2025-08-07 23:37:45] [Iter 324/1050] R1[173/300], Temp: 0.3807, Energy: -53.379665-0.001241j
[2025-08-07 23:37:49] [Iter 325/1050] R1[174/300], Temp: 0.3757, Energy: -53.284857-0.000020j
[2025-08-07 23:37:53] [Iter 326/1050] R1[175/300], Temp: 0.3706, Energy: -53.438077-0.003363j
[2025-08-07 23:37:57] [Iter 327/1050] R1[176/300], Temp: 0.3655, Energy: -53.412923-0.003017j
[2025-08-07 23:38:01] [Iter 328/1050] R1[177/300], Temp: 0.3605, Energy: -53.431258-0.008204j
[2025-08-07 23:38:06] [Iter 329/1050] R1[178/300], Temp: 0.3555, Energy: -53.371998-0.003945j
[2025-08-07 23:38:10] [Iter 330/1050] R1[179/300], Temp: 0.3505, Energy: -53.417409-0.003174j
[2025-08-07 23:38:14] [Iter 331/1050] R1[180/300], Temp: 0.3455, Energy: -53.393886+0.004561j
[2025-08-07 23:38:18] [Iter 332/1050] R1[181/300], Temp: 0.3405, Energy: -53.403890-0.004816j
[2025-08-07 23:38:22] [Iter 333/1050] R1[182/300], Temp: 0.3356, Energy: -53.510715-0.001478j
[2025-08-07 23:38:26] [Iter 334/1050] R1[183/300], Temp: 0.3306, Energy: -53.478322-0.005314j
[2025-08-07 23:38:30] [Iter 335/1050] R1[184/300], Temp: 0.3257, Energy: -53.494733+0.002156j
[2025-08-07 23:38:35] [Iter 336/1050] R1[185/300], Temp: 0.3208, Energy: -53.451690+0.001894j
[2025-08-07 23:38:39] [Iter 337/1050] R1[186/300], Temp: 0.3159, Energy: -53.531640-0.001984j
[2025-08-07 23:38:43] [Iter 338/1050] R1[187/300], Temp: 0.3111, Energy: -53.378320-0.005154j
[2025-08-07 23:38:47] [Iter 339/1050] R1[188/300], Temp: 0.3062, Energy: -53.346096+0.000046j
[2025-08-07 23:38:51] [Iter 340/1050] R1[189/300], Temp: 0.3014, Energy: -53.410573+0.006377j
[2025-08-07 23:38:55] [Iter 341/1050] R1[190/300], Temp: 0.2966, Energy: -53.319736-0.007098j
[2025-08-07 23:38:59] [Iter 342/1050] R1[191/300], Temp: 0.2919, Energy: -53.381832-0.000026j
[2025-08-07 23:39:03] [Iter 343/1050] R1[192/300], Temp: 0.2871, Energy: -53.360698+0.000321j
[2025-08-07 23:39:08] [Iter 344/1050] R1[193/300], Temp: 0.2824, Energy: -53.321845+0.003565j
[2025-08-07 23:39:12] [Iter 345/1050] R1[194/300], Temp: 0.2777, Energy: -53.282334-0.002261j
[2025-08-07 23:39:16] [Iter 346/1050] R1[195/300], Temp: 0.2730, Energy: -53.363763-0.000673j
[2025-08-07 23:39:20] [Iter 347/1050] R1[196/300], Temp: 0.2684, Energy: -53.407825-0.004879j
[2025-08-07 23:39:24] [Iter 348/1050] R1[197/300], Temp: 0.2637, Energy: -53.540655-0.003353j
[2025-08-07 23:39:28] [Iter 349/1050] R1[198/300], Temp: 0.2591, Energy: -53.454492-0.005705j
[2025-08-07 23:39:32] [Iter 350/1050] R1[199/300], Temp: 0.2545, Energy: -53.368072-0.002621j
[2025-08-07 23:39:36] [Iter 351/1050] R1[200/300], Temp: 0.2500, Energy: -53.494056-0.002362j
[2025-08-07 23:39:41] [Iter 352/1050] R1[201/300], Temp: 0.2455, Energy: -53.388025-0.004300j
[2025-08-07 23:39:45] [Iter 353/1050] R1[202/300], Temp: 0.2410, Energy: -53.481144+0.000119j
[2025-08-07 23:39:49] [Iter 354/1050] R1[203/300], Temp: 0.2365, Energy: -53.515289+0.000878j
[2025-08-07 23:39:53] [Iter 355/1050] R1[204/300], Temp: 0.2321, Energy: -53.426763+0.001055j
[2025-08-07 23:39:57] [Iter 356/1050] R1[205/300], Temp: 0.2277, Energy: -53.436455+0.000363j
[2025-08-07 23:40:01] [Iter 357/1050] R1[206/300], Temp: 0.2233, Energy: -53.604256-0.001679j
[2025-08-07 23:40:05] [Iter 358/1050] R1[207/300], Temp: 0.2190, Energy: -53.560105-0.002155j
[2025-08-07 23:40:09] [Iter 359/1050] R1[208/300], Temp: 0.2146, Energy: -53.567521+0.000301j
[2025-08-07 23:40:14] [Iter 360/1050] R1[209/300], Temp: 0.2104, Energy: -53.554014+0.003242j
[2025-08-07 23:40:18] [Iter 361/1050] R1[210/300], Temp: 0.2061, Energy: -53.508512+0.000916j
[2025-08-07 23:40:22] [Iter 362/1050] R1[211/300], Temp: 0.2019, Energy: -53.584697-0.000573j
[2025-08-07 23:40:26] [Iter 363/1050] R1[212/300], Temp: 0.1977, Energy: -53.448174+0.001139j
[2025-08-07 23:40:30] [Iter 364/1050] R1[213/300], Temp: 0.1935, Energy: -53.460489-0.002353j
[2025-08-07 23:40:34] [Iter 365/1050] R1[214/300], Temp: 0.1894, Energy: -53.412720-0.003935j
[2025-08-07 23:40:38] [Iter 366/1050] R1[215/300], Temp: 0.1853, Energy: -53.488604-0.002551j
[2025-08-07 23:40:43] [Iter 367/1050] R1[216/300], Temp: 0.1813, Energy: -53.484460-0.000158j
[2025-08-07 23:40:47] [Iter 368/1050] R1[217/300], Temp: 0.1773, Energy: -53.509549+0.001512j
[2025-08-07 23:40:51] [Iter 369/1050] R1[218/300], Temp: 0.1733, Energy: -53.425690+0.002844j
[2025-08-07 23:40:55] [Iter 370/1050] R1[219/300], Temp: 0.1693, Energy: -53.457234+0.000171j
[2025-08-07 23:40:59] [Iter 371/1050] R1[220/300], Temp: 0.1654, Energy: -53.343654-0.004672j
[2025-08-07 23:41:03] [Iter 372/1050] R1[221/300], Temp: 0.1616, Energy: -53.414598+0.003987j
[2025-08-07 23:41:07] [Iter 373/1050] R1[222/300], Temp: 0.1577, Energy: -53.515091+0.001833j
[2025-08-07 23:41:11] [Iter 374/1050] R1[223/300], Temp: 0.1539, Energy: -53.602893+0.000763j
[2025-08-07 23:41:16] [Iter 375/1050] R1[224/300], Temp: 0.1502, Energy: -53.536937+0.002189j
[2025-08-07 23:41:20] [Iter 376/1050] R1[225/300], Temp: 0.1464, Energy: -53.484955+0.002266j
[2025-08-07 23:41:24] [Iter 377/1050] R1[226/300], Temp: 0.1428, Energy: -53.404865-0.003083j
[2025-08-07 23:41:28] [Iter 378/1050] R1[227/300], Temp: 0.1391, Energy: -53.404267-0.001779j
[2025-08-07 23:41:32] [Iter 379/1050] R1[228/300], Temp: 0.1355, Energy: -53.393395-0.001496j
[2025-08-07 23:41:36] [Iter 380/1050] R1[229/300], Temp: 0.1320, Energy: -53.504604-0.001309j
[2025-08-07 23:41:40] [Iter 381/1050] R1[230/300], Temp: 0.1284, Energy: -53.490849-0.000527j
[2025-08-07 23:41:44] [Iter 382/1050] R1[231/300], Temp: 0.1249, Energy: -53.430666-0.004669j
[2025-08-07 23:41:48] [Iter 383/1050] R1[232/300], Temp: 0.1215, Energy: -53.478266+0.001603j
[2025-08-07 23:41:53] [Iter 384/1050] R1[233/300], Temp: 0.1181, Energy: -53.524052-0.003134j
[2025-08-07 23:41:57] [Iter 385/1050] R1[234/300], Temp: 0.1147, Energy: -53.441946+0.001217j
[2025-08-07 23:42:01] [Iter 386/1050] R1[235/300], Temp: 0.1114, Energy: -53.402791-0.009538j
[2025-08-07 23:42:05] [Iter 387/1050] R1[236/300], Temp: 0.1082, Energy: -53.591034+0.002731j
[2025-08-07 23:42:09] [Iter 388/1050] R1[237/300], Temp: 0.1049, Energy: -53.521455+0.002173j
[2025-08-07 23:42:13] [Iter 389/1050] R1[238/300], Temp: 0.1017, Energy: -53.537384-0.005951j
[2025-08-07 23:42:17] [Iter 390/1050] R1[239/300], Temp: 0.0986, Energy: -53.396341+0.002409j
[2025-08-07 23:42:21] [Iter 391/1050] R1[240/300], Temp: 0.0955, Energy: -53.338110+0.001917j
[2025-08-07 23:42:26] [Iter 392/1050] R1[241/300], Temp: 0.0924, Energy: -53.531524-0.001012j
[2025-08-07 23:42:30] [Iter 393/1050] R1[242/300], Temp: 0.0894, Energy: -53.488016+0.001396j
[2025-08-07 23:42:34] [Iter 394/1050] R1[243/300], Temp: 0.0865, Energy: -53.438997-0.001624j
[2025-08-07 23:42:38] [Iter 395/1050] R1[244/300], Temp: 0.0835, Energy: -53.340605-0.003721j
[2025-08-07 23:42:42] [Iter 396/1050] R1[245/300], Temp: 0.0807, Energy: -53.345465+0.000405j
[2025-08-07 23:42:46] [Iter 397/1050] R1[246/300], Temp: 0.0778, Energy: -53.275559+0.000601j
[2025-08-07 23:42:50] [Iter 398/1050] R1[247/300], Temp: 0.0751, Energy: -53.408709-0.000595j
[2025-08-07 23:42:54] [Iter 399/1050] R1[248/300], Temp: 0.0723, Energy: -53.378841+0.004140j
[2025-08-07 23:42:58] [Iter 400/1050] R1[249/300], Temp: 0.0696, Energy: -53.463375+0.000774j
[2025-08-07 23:42:59] ✓ Checkpoint saved: checkpoint_iter_000400.pkl
[2025-08-07 23:43:03] [Iter 401/1050] R1[250/300], Temp: 0.0670, Energy: -53.412324-0.005568j
[2025-08-07 23:43:07] [Iter 402/1050] R1[251/300], Temp: 0.0644, Energy: -53.402015+0.000870j
[2025-08-07 23:43:11] [Iter 403/1050] R1[252/300], Temp: 0.0618, Energy: -53.384472-0.002446j
[2025-08-07 23:43:15] [Iter 404/1050] R1[253/300], Temp: 0.0593, Energy: -53.498001+0.007164j
[2025-08-07 23:43:19] [Iter 405/1050] R1[254/300], Temp: 0.0569, Energy: -53.490869-0.006695j
[2025-08-07 23:43:23] [Iter 406/1050] R1[255/300], Temp: 0.0545, Energy: -53.377352-0.001218j
[2025-08-07 23:43:27] [Iter 407/1050] R1[256/300], Temp: 0.0521, Energy: -53.430024+0.002558j
[2025-08-07 23:43:31] [Iter 408/1050] R1[257/300], Temp: 0.0498, Energy: -53.341379+0.003636j
[2025-08-07 23:43:36] [Iter 409/1050] R1[258/300], Temp: 0.0476, Energy: -53.470110+0.003152j
[2025-08-07 23:43:40] [Iter 410/1050] R1[259/300], Temp: 0.0454, Energy: -53.490920+0.003161j
[2025-08-07 23:43:44] [Iter 411/1050] R1[260/300], Temp: 0.0432, Energy: -53.482946+0.001315j
[2025-08-07 23:43:48] [Iter 412/1050] R1[261/300], Temp: 0.0411, Energy: -53.430461+0.000994j
[2025-08-07 23:43:52] [Iter 413/1050] R1[262/300], Temp: 0.0391, Energy: -53.526619+0.000255j
[2025-08-07 23:43:56] [Iter 414/1050] R1[263/300], Temp: 0.0371, Energy: -53.446832-0.001696j
[2025-08-07 23:44:00] [Iter 415/1050] R1[264/300], Temp: 0.0351, Energy: -53.343373+0.004796j
[2025-08-07 23:44:05] [Iter 416/1050] R1[265/300], Temp: 0.0332, Energy: -53.336627-0.002001j
[2025-08-07 23:44:09] [Iter 417/1050] R1[266/300], Temp: 0.0314, Energy: -53.443606+0.007996j
[2025-08-07 23:44:13] [Iter 418/1050] R1[267/300], Temp: 0.0296, Energy: -53.566174+0.005783j
[2025-08-07 23:44:17] [Iter 419/1050] R1[268/300], Temp: 0.0278, Energy: -53.440642+0.007161j
[2025-08-07 23:44:21] [Iter 420/1050] R1[269/300], Temp: 0.0261, Energy: -53.487009-0.002108j
[2025-08-07 23:44:25] [Iter 421/1050] R1[270/300], Temp: 0.0245, Energy: -53.415103-0.001981j
[2025-08-07 23:44:29] [Iter 422/1050] R1[271/300], Temp: 0.0229, Energy: -53.488314+0.000863j
[2025-08-07 23:44:33] [Iter 423/1050] R1[272/300], Temp: 0.0213, Energy: -53.401636+0.002114j
[2025-08-07 23:44:37] [Iter 424/1050] R1[273/300], Temp: 0.0199, Energy: -53.267333+0.003208j
[2025-08-07 23:44:42] [Iter 425/1050] R1[274/300], Temp: 0.0184, Energy: -53.306568+0.001564j
[2025-08-07 23:44:46] [Iter 426/1050] R1[275/300], Temp: 0.0170, Energy: -53.369722-0.000787j
[2025-08-07 23:44:50] [Iter 427/1050] R1[276/300], Temp: 0.0157, Energy: -53.251089-0.003847j
[2025-08-07 23:44:54] [Iter 428/1050] R1[277/300], Temp: 0.0144, Energy: -53.204636-0.000038j
[2025-08-07 23:44:58] [Iter 429/1050] R1[278/300], Temp: 0.0132, Energy: -53.347837-0.000774j
[2025-08-07 23:45:02] [Iter 430/1050] R1[279/300], Temp: 0.0120, Energy: -53.392312+0.000858j
[2025-08-07 23:45:06] [Iter 431/1050] R1[280/300], Temp: 0.0109, Energy: -53.308894-0.002497j
[2025-08-07 23:45:10] [Iter 432/1050] R1[281/300], Temp: 0.0099, Energy: -53.348066-0.002472j
[2025-08-07 23:45:15] [Iter 433/1050] R1[282/300], Temp: 0.0089, Energy: -53.411824+0.000095j
[2025-08-07 23:45:19] [Iter 434/1050] R1[283/300], Temp: 0.0079, Energy: -53.352681-0.001884j
[2025-08-07 23:45:23] [Iter 435/1050] R1[284/300], Temp: 0.0070, Energy: -53.416758+0.000485j
[2025-08-07 23:45:27] [Iter 436/1050] R1[285/300], Temp: 0.0062, Energy: -53.251026-0.005892j
[2025-08-07 23:45:31] [Iter 437/1050] R1[286/300], Temp: 0.0054, Energy: -53.363211-0.000003j
[2025-08-07 23:45:35] [Iter 438/1050] R1[287/300], Temp: 0.0046, Energy: -53.374828-0.000126j
[2025-08-07 23:45:39] [Iter 439/1050] R1[288/300], Temp: 0.0039, Energy: -53.387718+0.006951j
[2025-08-07 23:45:43] [Iter 440/1050] R1[289/300], Temp: 0.0033, Energy: -53.481055+0.000346j
[2025-08-07 23:45:48] [Iter 441/1050] R1[290/300], Temp: 0.0027, Energy: -53.419351+0.004849j
[2025-08-07 23:45:52] [Iter 442/1050] R1[291/300], Temp: 0.0022, Energy: -53.439465+0.000614j
[2025-08-07 23:45:56] [Iter 443/1050] R1[292/300], Temp: 0.0018, Energy: -53.386843+0.006756j
[2025-08-07 23:46:00] [Iter 444/1050] R1[293/300], Temp: 0.0013, Energy: -53.478078+0.000052j
[2025-08-07 23:46:04] [Iter 445/1050] R1[294/300], Temp: 0.0010, Energy: -53.419146+0.001150j
[2025-08-07 23:46:08] [Iter 446/1050] R1[295/300], Temp: 0.0007, Energy: -53.329446-0.001079j
[2025-08-07 23:46:12] [Iter 447/1050] R1[296/300], Temp: 0.0004, Energy: -53.380385-0.001246j
[2025-08-07 23:46:17] [Iter 448/1050] R1[297/300], Temp: 0.0002, Energy: -53.336582+0.002946j
[2025-08-07 23:46:21] [Iter 449/1050] R1[298/300], Temp: 0.0001, Energy: -53.368651+0.001627j
[2025-08-07 23:46:25] [Iter 450/1050] R1[299/300], Temp: 0.0000, Energy: -53.433637+0.001169j
[2025-08-07 23:46:25] RESTART #2 | Period: 600
[2025-08-07 23:46:29] [Iter 451/1050] R2[0/600], Temp: 1.0000, Energy: -53.358075-0.008540j
[2025-08-07 23:46:33] [Iter 452/1050] R2[1/600], Temp: 1.0000, Energy: -53.470392-0.006015j
[2025-08-07 23:46:37] [Iter 453/1050] R2[2/600], Temp: 1.0000, Energy: -53.506855-0.001350j
[2025-08-07 23:46:41] [Iter 454/1050] R2[3/600], Temp: 0.9999, Energy: -53.497166-0.001565j
[2025-08-07 23:46:45] [Iter 455/1050] R2[4/600], Temp: 0.9999, Energy: -53.538911+0.002006j
[2025-08-07 23:46:50] [Iter 456/1050] R2[5/600], Temp: 0.9998, Energy: -53.569724+0.002889j
[2025-08-07 23:46:54] [Iter 457/1050] R2[6/600], Temp: 0.9998, Energy: -53.385674-0.005261j
[2025-08-07 23:46:58] [Iter 458/1050] R2[7/600], Temp: 0.9997, Energy: -53.472688-0.004289j
[2025-08-07 23:47:02] [Iter 459/1050] R2[8/600], Temp: 0.9996, Energy: -53.391263-0.001813j
[2025-08-07 23:47:06] [Iter 460/1050] R2[9/600], Temp: 0.9994, Energy: -53.432905-0.002749j
[2025-08-07 23:47:10] [Iter 461/1050] R2[10/600], Temp: 0.9993, Energy: -53.522568+0.000837j
[2025-08-07 23:47:14] [Iter 462/1050] R2[11/600], Temp: 0.9992, Energy: -53.633129-0.000000j
[2025-08-07 23:47:18] [Iter 463/1050] R2[12/600], Temp: 0.9990, Energy: -53.547913+0.003462j
[2025-08-07 23:47:23] [Iter 464/1050] R2[13/600], Temp: 0.9988, Energy: -53.565194+0.003784j
[2025-08-07 23:47:27] [Iter 465/1050] R2[14/600], Temp: 0.9987, Energy: -53.627905+0.000843j
[2025-08-07 23:47:31] [Iter 466/1050] R2[15/600], Temp: 0.9985, Energy: -53.518433-0.003397j
[2025-08-07 23:47:35] [Iter 467/1050] R2[16/600], Temp: 0.9982, Energy: -53.578799-0.001834j
[2025-08-07 23:47:39] [Iter 468/1050] R2[17/600], Temp: 0.9980, Energy: -53.495139+0.007285j
[2025-08-07 23:47:43] [Iter 469/1050] R2[18/600], Temp: 0.9978, Energy: -53.408472+0.002306j
[2025-08-07 23:47:47] [Iter 470/1050] R2[19/600], Temp: 0.9975, Energy: -53.552834+0.003171j
[2025-08-07 23:47:51] [Iter 471/1050] R2[20/600], Temp: 0.9973, Energy: -53.523325+0.001330j
[2025-08-07 23:47:56] [Iter 472/1050] R2[21/600], Temp: 0.9970, Energy: -53.449585+0.002896j
[2025-08-07 23:48:00] [Iter 473/1050] R2[22/600], Temp: 0.9967, Energy: -53.451045+0.003281j
[2025-08-07 23:48:04] [Iter 474/1050] R2[23/600], Temp: 0.9964, Energy: -53.455780+0.001634j
[2025-08-07 23:48:08] [Iter 475/1050] R2[24/600], Temp: 0.9961, Energy: -53.461562+0.001565j
[2025-08-07 23:48:12] [Iter 476/1050] R2[25/600], Temp: 0.9957, Energy: -53.371571+0.003127j
[2025-08-07 23:48:16] [Iter 477/1050] R2[26/600], Temp: 0.9954, Energy: -53.426901+0.001988j
[2025-08-07 23:48:20] [Iter 478/1050] R2[27/600], Temp: 0.9950, Energy: -53.353690+0.003068j
[2025-08-07 23:48:24] [Iter 479/1050] R2[28/600], Temp: 0.9946, Energy: -53.454966+0.002432j
[2025-08-07 23:48:28] [Iter 480/1050] R2[29/600], Temp: 0.9942, Energy: -53.472501+0.003554j
[2025-08-07 23:48:33] [Iter 481/1050] R2[30/600], Temp: 0.9938, Energy: -53.522231-0.000820j
[2025-08-07 23:48:37] [Iter 482/1050] R2[31/600], Temp: 0.9934, Energy: -53.638730+0.000278j
[2025-08-07 23:48:41] [Iter 483/1050] R2[32/600], Temp: 0.9930, Energy: -53.581524+0.001325j
[2025-08-07 23:48:45] [Iter 484/1050] R2[33/600], Temp: 0.9926, Energy: -53.428557+0.000962j
[2025-08-07 23:48:49] [Iter 485/1050] R2[34/600], Temp: 0.9921, Energy: -53.443988-0.003505j
[2025-08-07 23:48:53] [Iter 486/1050] R2[35/600], Temp: 0.9916, Energy: -53.537946+0.001287j
[2025-08-07 23:48:57] [Iter 487/1050] R2[36/600], Temp: 0.9911, Energy: -53.405015-0.004299j
[2025-08-07 23:49:01] [Iter 488/1050] R2[37/600], Temp: 0.9906, Energy: -53.403779+0.006237j
[2025-08-07 23:49:06] [Iter 489/1050] R2[38/600], Temp: 0.9901, Energy: -53.386062+0.004335j
[2025-08-07 23:49:10] [Iter 490/1050] R2[39/600], Temp: 0.9896, Energy: -53.439817+0.004262j
[2025-08-07 23:49:14] [Iter 491/1050] R2[40/600], Temp: 0.9891, Energy: -53.388062+0.004768j
[2025-08-07 23:49:18] [Iter 492/1050] R2[41/600], Temp: 0.9885, Energy: -53.466837+0.003107j
[2025-08-07 23:49:22] [Iter 493/1050] R2[42/600], Temp: 0.9880, Energy: -53.462884-0.001642j
[2025-08-07 23:49:26] [Iter 494/1050] R2[43/600], Temp: 0.9874, Energy: -53.559118-0.001516j
[2025-08-07 23:49:30] [Iter 495/1050] R2[44/600], Temp: 0.9868, Energy: -53.548703-0.007616j
[2025-08-07 23:49:35] [Iter 496/1050] R2[45/600], Temp: 0.9862, Energy: -53.482790+0.000116j
[2025-08-07 23:49:39] [Iter 497/1050] R2[46/600], Temp: 0.9856, Energy: -53.386842+0.001999j
[2025-08-07 23:49:43] [Iter 498/1050] R2[47/600], Temp: 0.9849, Energy: -53.380782+0.002858j
[2025-08-07 23:49:47] [Iter 499/1050] R2[48/600], Temp: 0.9843, Energy: -53.299812-0.000300j
[2025-08-07 23:49:51] [Iter 500/1050] R2[49/600], Temp: 0.9836, Energy: -53.268230+0.003628j
[2025-08-07 23:49:51] ✓ Checkpoint saved: checkpoint_iter_000500.pkl
[2025-08-07 23:49:55] [Iter 501/1050] R2[50/600], Temp: 0.9830, Energy: -53.345489-0.001075j
[2025-08-07 23:49:59] [Iter 502/1050] R2[51/600], Temp: 0.9823, Energy: -53.381962+0.000696j
[2025-08-07 23:50:03] [Iter 503/1050] R2[52/600], Temp: 0.9816, Energy: -53.401815-0.007973j
[2025-08-07 23:50:08] [Iter 504/1050] R2[53/600], Temp: 0.9809, Energy: -53.395674-0.003769j
[2025-08-07 23:50:12] [Iter 505/1050] R2[54/600], Temp: 0.9801, Energy: -53.491130-0.000938j
[2025-08-07 23:50:16] [Iter 506/1050] R2[55/600], Temp: 0.9794, Energy: -53.553035-0.001167j
[2025-08-07 23:50:20] [Iter 507/1050] R2[56/600], Temp: 0.9787, Energy: -53.516549-0.001529j
[2025-08-07 23:50:24] [Iter 508/1050] R2[57/600], Temp: 0.9779, Energy: -53.450154-0.003281j
[2025-08-07 23:50:28] [Iter 509/1050] R2[58/600], Temp: 0.9771, Energy: -53.484368+0.004429j
[2025-08-07 23:50:32] [Iter 510/1050] R2[59/600], Temp: 0.9763, Energy: -53.432981+0.001846j
[2025-08-07 23:50:36] [Iter 511/1050] R2[60/600], Temp: 0.9755, Energy: -53.508585+0.003503j
[2025-08-07 23:50:41] [Iter 512/1050] R2[61/600], Temp: 0.9747, Energy: -53.472052+0.000563j
[2025-08-07 23:50:45] [Iter 513/1050] R2[62/600], Temp: 0.9739, Energy: -53.513910+0.002068j
[2025-08-07 23:50:49] [Iter 514/1050] R2[63/600], Temp: 0.9730, Energy: -53.429381-0.008164j
[2025-08-07 23:50:53] [Iter 515/1050] R2[64/600], Temp: 0.9722, Energy: -53.513187+0.003606j
[2025-08-07 23:50:57] [Iter 516/1050] R2[65/600], Temp: 0.9713, Energy: -53.543000+0.002196j
[2025-08-07 23:51:01] [Iter 517/1050] R2[66/600], Temp: 0.9704, Energy: -53.393190+0.005475j
[2025-08-07 23:51:05] [Iter 518/1050] R2[67/600], Temp: 0.9695, Energy: -53.430506-0.001453j
[2025-08-07 23:51:09] [Iter 519/1050] R2[68/600], Temp: 0.9686, Energy: -53.392727-0.000221j
[2025-08-07 23:51:13] [Iter 520/1050] R2[69/600], Temp: 0.9677, Energy: -53.424201-0.002027j
[2025-08-07 23:51:18] [Iter 521/1050] R2[70/600], Temp: 0.9668, Energy: -53.502856-0.001233j
[2025-08-07 23:51:22] [Iter 522/1050] R2[71/600], Temp: 0.9658, Energy: -53.450651-0.001002j
[2025-08-07 23:51:26] [Iter 523/1050] R2[72/600], Temp: 0.9649, Energy: -53.380334+0.001163j
[2025-08-07 23:51:30] [Iter 524/1050] R2[73/600], Temp: 0.9639, Energy: -53.350114-0.002840j
[2025-08-07 23:51:34] [Iter 525/1050] R2[74/600], Temp: 0.9629, Energy: -53.324659+0.003469j
[2025-08-07 23:51:38] [Iter 526/1050] R2[75/600], Temp: 0.9619, Energy: -53.301351-0.000618j
[2025-08-07 23:51:42] [Iter 527/1050] R2[76/600], Temp: 0.9609, Energy: -53.384171+0.005072j
[2025-08-07 23:51:47] [Iter 528/1050] R2[77/600], Temp: 0.9599, Energy: -53.417413+0.008736j
[2025-08-07 23:51:51] [Iter 529/1050] R2[78/600], Temp: 0.9589, Energy: -53.476993-0.000063j
[2025-08-07 23:51:55] [Iter 530/1050] R2[79/600], Temp: 0.9578, Energy: -53.416020-0.001655j
[2025-08-07 23:51:59] [Iter 531/1050] R2[80/600], Temp: 0.9568, Energy: -53.469469+0.002904j
[2025-08-07 23:52:03] [Iter 532/1050] R2[81/600], Temp: 0.9557, Energy: -53.448293+0.001981j
[2025-08-07 23:52:07] [Iter 533/1050] R2[82/600], Temp: 0.9546, Energy: -53.376794+0.000026j
[2025-08-07 23:52:11] [Iter 534/1050] R2[83/600], Temp: 0.9535, Energy: -53.389362+0.005312j
[2025-08-07 23:52:15] [Iter 535/1050] R2[84/600], Temp: 0.9524, Energy: -53.350422+0.004241j
[2025-08-07 23:52:20] [Iter 536/1050] R2[85/600], Temp: 0.9513, Energy: -53.458298+0.005788j
[2025-08-07 23:52:24] [Iter 537/1050] R2[86/600], Temp: 0.9502, Energy: -53.421941+0.000262j
[2025-08-07 23:52:28] [Iter 538/1050] R2[87/600], Temp: 0.9490, Energy: -53.460329-0.002875j
[2025-08-07 23:52:32] [Iter 539/1050] R2[88/600], Temp: 0.9479, Energy: -53.344389+0.002707j
[2025-08-07 23:52:36] [Iter 540/1050] R2[89/600], Temp: 0.9467, Energy: -53.462055-0.000495j
[2025-08-07 23:52:40] [Iter 541/1050] R2[90/600], Temp: 0.9455, Energy: -53.313557+0.001860j
[2025-08-07 23:52:44] [Iter 542/1050] R2[91/600], Temp: 0.9443, Energy: -53.466631+0.000221j
[2025-08-07 23:52:48] [Iter 543/1050] R2[92/600], Temp: 0.9431, Energy: -53.506429-0.002164j
[2025-08-07 23:52:53] [Iter 544/1050] R2[93/600], Temp: 0.9419, Energy: -53.339443+0.002688j
[2025-08-07 23:52:57] [Iter 545/1050] R2[94/600], Temp: 0.9407, Energy: -53.365759-0.001938j
[2025-08-07 23:53:01] [Iter 546/1050] R2[95/600], Temp: 0.9394, Energy: -53.241909+0.000471j
[2025-08-07 23:53:05] [Iter 547/1050] R2[96/600], Temp: 0.9382, Energy: -53.292970+0.004095j
[2025-08-07 23:53:09] [Iter 548/1050] R2[97/600], Temp: 0.9369, Energy: -53.380646+0.000404j
[2025-08-07 23:53:13] [Iter 549/1050] R2[98/600], Temp: 0.9356, Energy: -53.398018+0.000464j
[2025-08-07 23:53:17] [Iter 550/1050] R2[99/600], Temp: 0.9343, Energy: -53.420132-0.003790j
[2025-08-07 23:53:21] [Iter 551/1050] R2[100/600], Temp: 0.9330, Energy: -53.468726+0.001544j
[2025-08-07 23:53:25] [Iter 552/1050] R2[101/600], Temp: 0.9317, Energy: -53.548397+0.008092j
[2025-08-07 23:53:30] [Iter 553/1050] R2[102/600], Temp: 0.9304, Energy: -53.339652+0.003285j
[2025-08-07 23:53:34] [Iter 554/1050] R2[103/600], Temp: 0.9290, Energy: -53.404791-0.003978j
[2025-08-07 23:53:38] [Iter 555/1050] R2[104/600], Temp: 0.9277, Energy: -53.354653+0.001305j
[2025-08-07 23:53:42] [Iter 556/1050] R2[105/600], Temp: 0.9263, Energy: -53.345478+0.001059j
[2025-08-07 23:53:46] [Iter 557/1050] R2[106/600], Temp: 0.9249, Energy: -53.408699+0.005077j
[2025-08-07 23:53:50] [Iter 558/1050] R2[107/600], Temp: 0.9236, Energy: -53.464296+0.001441j
[2025-08-07 23:53:54] [Iter 559/1050] R2[108/600], Temp: 0.9222, Energy: -53.510346+0.000876j
[2025-08-07 23:53:58] [Iter 560/1050] R2[109/600], Temp: 0.9208, Energy: -53.464506+0.002937j
[2025-08-07 23:54:03] [Iter 561/1050] R2[110/600], Temp: 0.9193, Energy: -53.346725-0.000477j
[2025-08-07 23:54:07] [Iter 562/1050] R2[111/600], Temp: 0.9179, Energy: -53.382430-0.004055j
[2025-08-07 23:54:11] [Iter 563/1050] R2[112/600], Temp: 0.9165, Energy: -53.482160-0.001433j
[2025-08-07 23:54:15] [Iter 564/1050] R2[113/600], Temp: 0.9150, Energy: -53.346879-0.004491j
[2025-08-07 23:54:19] [Iter 565/1050] R2[114/600], Temp: 0.9135, Energy: -53.303747+0.003893j
[2025-08-07 23:54:23] [Iter 566/1050] R2[115/600], Temp: 0.9121, Energy: -53.309283+0.000032j
[2025-08-07 23:54:27] [Iter 567/1050] R2[116/600], Temp: 0.9106, Energy: -53.369767-0.000232j
[2025-08-07 23:54:31] [Iter 568/1050] R2[117/600], Temp: 0.9091, Energy: -53.363011-0.001611j
[2025-08-07 23:54:36] [Iter 569/1050] R2[118/600], Temp: 0.9076, Energy: -53.346391+0.002959j
[2025-08-07 23:54:40] [Iter 570/1050] R2[119/600], Temp: 0.9060, Energy: -53.376402-0.000259j
[2025-08-07 23:54:44] [Iter 571/1050] R2[120/600], Temp: 0.9045, Energy: -53.337513+0.003347j
[2025-08-07 23:54:48] [Iter 572/1050] R2[121/600], Temp: 0.9030, Energy: -53.329503+0.008596j
[2025-08-07 23:54:52] [Iter 573/1050] R2[122/600], Temp: 0.9014, Energy: -53.329062-0.001329j
[2025-08-07 23:54:56] [Iter 574/1050] R2[123/600], Temp: 0.8998, Energy: -53.383852+0.002331j
[2025-08-07 23:55:00] [Iter 575/1050] R2[124/600], Temp: 0.8983, Energy: -53.344509+0.006690j
[2025-08-07 23:55:04] [Iter 576/1050] R2[125/600], Temp: 0.8967, Energy: -53.307408+0.003076j
[2025-08-07 23:55:09] [Iter 577/1050] R2[126/600], Temp: 0.8951, Energy: -53.332469+0.000959j
[2025-08-07 23:55:13] [Iter 578/1050] R2[127/600], Temp: 0.8935, Energy: -53.240081-0.001185j
[2025-08-07 23:55:17] [Iter 579/1050] R2[128/600], Temp: 0.8918, Energy: -53.206930+0.002760j
[2025-08-07 23:55:21] [Iter 580/1050] R2[129/600], Temp: 0.8902, Energy: -53.278004+0.003557j
[2025-08-07 23:55:25] [Iter 581/1050] R2[130/600], Temp: 0.8886, Energy: -53.417307+0.004907j
[2025-08-07 23:55:29] [Iter 582/1050] R2[131/600], Temp: 0.8869, Energy: -53.436386-0.002927j
[2025-08-07 23:55:33] [Iter 583/1050] R2[132/600], Temp: 0.8853, Energy: -53.427263-0.002224j
[2025-08-07 23:55:37] [Iter 584/1050] R2[133/600], Temp: 0.8836, Energy: -53.348156-0.001292j
[2025-08-07 23:55:42] [Iter 585/1050] R2[134/600], Temp: 0.8819, Energy: -53.324255-0.002979j
[2025-08-07 23:55:46] [Iter 586/1050] R2[135/600], Temp: 0.8802, Energy: -53.507674+0.006505j
[2025-08-07 23:55:50] [Iter 587/1050] R2[136/600], Temp: 0.8785, Energy: -53.423581-0.001478j
[2025-08-07 23:55:54] [Iter 588/1050] R2[137/600], Temp: 0.8768, Energy: -53.443445+0.003666j
[2025-08-07 23:55:58] [Iter 589/1050] R2[138/600], Temp: 0.8751, Energy: -53.423653-0.000996j
[2025-08-07 23:56:02] [Iter 590/1050] R2[139/600], Temp: 0.8733, Energy: -53.377663-0.001903j
[2025-08-07 23:56:06] [Iter 591/1050] R2[140/600], Temp: 0.8716, Energy: -53.448851+0.006390j
[2025-08-07 23:56:10] [Iter 592/1050] R2[141/600], Temp: 0.8698, Energy: -53.401127+0.009470j
[2025-08-07 23:56:14] [Iter 593/1050] R2[142/600], Temp: 0.8680, Energy: -53.453060+0.000809j
[2025-08-07 23:56:19] [Iter 594/1050] R2[143/600], Temp: 0.8663, Energy: -53.499380-0.000874j
[2025-08-07 23:56:23] [Iter 595/1050] R2[144/600], Temp: 0.8645, Energy: -53.532519-0.003627j
[2025-08-07 23:56:27] [Iter 596/1050] R2[145/600], Temp: 0.8627, Energy: -53.557475-0.003477j
[2025-08-07 23:56:31] [Iter 597/1050] R2[146/600], Temp: 0.8609, Energy: -53.524915+0.001255j
[2025-08-07 23:56:35] [Iter 598/1050] R2[147/600], Temp: 0.8591, Energy: -53.573401-0.003867j
[2025-08-07 23:56:39] [Iter 599/1050] R2[148/600], Temp: 0.8572, Energy: -53.510490+0.007658j
[2025-08-07 23:56:43] [Iter 600/1050] R2[149/600], Temp: 0.8554, Energy: -53.447248-0.001769j
[2025-08-07 23:56:43] ✓ Checkpoint saved: checkpoint_iter_000600.pkl
[2025-08-07 23:56:47] [Iter 601/1050] R2[150/600], Temp: 0.8536, Energy: -53.489092+0.001472j
[2025-08-07 23:56:52] [Iter 602/1050] R2[151/600], Temp: 0.8517, Energy: -53.412462+0.002523j
[2025-08-07 23:56:56] [Iter 603/1050] R2[152/600], Temp: 0.8498, Energy: -53.515423+0.002781j
[2025-08-07 23:57:00] [Iter 604/1050] R2[153/600], Temp: 0.8480, Energy: -53.528759+0.001139j
[2025-08-07 23:57:04] [Iter 605/1050] R2[154/600], Temp: 0.8461, Energy: -53.489449-0.002239j
[2025-08-07 23:57:08] [Iter 606/1050] R2[155/600], Temp: 0.8442, Energy: -53.446737-0.000476j
[2025-08-07 23:57:12] [Iter 607/1050] R2[156/600], Temp: 0.8423, Energy: -53.570361-0.007037j
[2025-08-07 23:57:16] [Iter 608/1050] R2[157/600], Temp: 0.8404, Energy: -53.452662+0.005510j
[2025-08-07 23:57:20] [Iter 609/1050] R2[158/600], Temp: 0.8384, Energy: -53.418691+0.006130j
[2025-08-07 23:57:25] [Iter 610/1050] R2[159/600], Temp: 0.8365, Energy: -53.434003-0.001190j
[2025-08-07 23:57:29] [Iter 611/1050] R2[160/600], Temp: 0.8346, Energy: -53.442092-0.000527j
[2025-08-07 23:57:33] [Iter 612/1050] R2[161/600], Temp: 0.8326, Energy: -53.472010+0.000395j
[2025-08-07 23:57:37] [Iter 613/1050] R2[162/600], Temp: 0.8307, Energy: -53.542002-0.001982j
[2025-08-07 23:57:41] [Iter 614/1050] R2[163/600], Temp: 0.8287, Energy: -53.502284-0.002308j
[2025-08-07 23:57:45] [Iter 615/1050] R2[164/600], Temp: 0.8267, Energy: -53.476087-0.001096j
[2025-08-07 23:57:49] [Iter 616/1050] R2[165/600], Temp: 0.8247, Energy: -53.452435-0.002976j
[2025-08-07 23:57:53] [Iter 617/1050] R2[166/600], Temp: 0.8227, Energy: -53.457720-0.003778j
[2025-08-07 23:57:57] [Iter 618/1050] R2[167/600], Temp: 0.8207, Energy: -53.323253+0.001311j
[2025-08-07 23:58:02] [Iter 619/1050] R2[168/600], Temp: 0.8187, Energy: -53.333643+0.002016j
[2025-08-07 23:58:06] [Iter 620/1050] R2[169/600], Temp: 0.8167, Energy: -53.371587-0.001289j
[2025-08-07 23:58:10] [Iter 621/1050] R2[170/600], Temp: 0.8147, Energy: -53.273507-0.006181j
[2025-08-07 23:58:14] [Iter 622/1050] R2[171/600], Temp: 0.8126, Energy: -53.247867+0.001986j
[2025-08-07 23:58:18] [Iter 623/1050] R2[172/600], Temp: 0.8106, Energy: -53.309887+0.003873j
[2025-08-07 23:58:22] [Iter 624/1050] R2[173/600], Temp: 0.8085, Energy: -53.248070+0.003019j
[2025-08-07 23:58:26] [Iter 625/1050] R2[174/600], Temp: 0.8065, Energy: -53.418931+0.005035j
[2025-08-07 23:58:30] [Iter 626/1050] R2[175/600], Temp: 0.8044, Energy: -53.373806+0.000148j
[2025-08-07 23:58:35] [Iter 627/1050] R2[176/600], Temp: 0.8023, Energy: -53.395429-0.002264j
[2025-08-07 23:58:39] [Iter 628/1050] R2[177/600], Temp: 0.8002, Energy: -53.337197+0.001201j
[2025-08-07 23:58:43] [Iter 629/1050] R2[178/600], Temp: 0.7981, Energy: -53.444211+0.001360j
[2025-08-07 23:58:47] [Iter 630/1050] R2[179/600], Temp: 0.7960, Energy: -53.494097-0.001191j
[2025-08-07 23:58:51] [Iter 631/1050] R2[180/600], Temp: 0.7939, Energy: -53.393770+0.001120j
[2025-08-07 23:58:55] [Iter 632/1050] R2[181/600], Temp: 0.7918, Energy: -53.368052-0.001809j
[2025-08-07 23:58:59] [Iter 633/1050] R2[182/600], Temp: 0.7896, Energy: -53.379136-0.001525j
[2025-08-07 23:59:03] [Iter 634/1050] R2[183/600], Temp: 0.7875, Energy: -53.450242+0.004406j
[2025-08-07 23:59:08] [Iter 635/1050] R2[184/600], Temp: 0.7854, Energy: -53.395746+0.000840j
[2025-08-07 23:59:12] [Iter 636/1050] R2[185/600], Temp: 0.7832, Energy: -53.360746+0.003675j
[2025-08-07 23:59:16] [Iter 637/1050] R2[186/600], Temp: 0.7810, Energy: -53.382491-0.003691j
[2025-08-07 23:59:20] [Iter 638/1050] R2[187/600], Temp: 0.7789, Energy: -53.387001-0.004899j
[2025-08-07 23:59:24] [Iter 639/1050] R2[188/600], Temp: 0.7767, Energy: -53.259195-0.003605j
[2025-08-07 23:59:28] [Iter 640/1050] R2[189/600], Temp: 0.7745, Energy: -53.410319+0.000368j
[2025-08-07 23:59:32] [Iter 641/1050] R2[190/600], Temp: 0.7723, Energy: -53.423661+0.004216j
[2025-08-07 23:59:36] [Iter 642/1050] R2[191/600], Temp: 0.7701, Energy: -53.438379+0.000693j
[2025-08-07 23:59:41] [Iter 643/1050] R2[192/600], Temp: 0.7679, Energy: -53.293310+0.001239j
[2025-08-07 23:59:45] [Iter 644/1050] R2[193/600], Temp: 0.7657, Energy: -53.247559+0.004517j
[2025-08-07 23:59:49] [Iter 645/1050] R2[194/600], Temp: 0.7635, Energy: -53.150990+0.002261j
[2025-08-07 23:59:53] [Iter 646/1050] R2[195/600], Temp: 0.7612, Energy: -53.337513+0.005988j
[2025-08-07 23:59:57] [Iter 647/1050] R2[196/600], Temp: 0.7590, Energy: -53.323541-0.002025j
[2025-08-08 00:00:01] [Iter 648/1050] R2[197/600], Temp: 0.7568, Energy: -53.378968+0.000453j
[2025-08-08 00:00:05] [Iter 649/1050] R2[198/600], Temp: 0.7545, Energy: -53.352689-0.000868j
[2025-08-08 00:00:09] [Iter 650/1050] R2[199/600], Temp: 0.7523, Energy: -53.324782-0.001320j
[2025-08-08 00:00:14] [Iter 651/1050] R2[200/600], Temp: 0.7500, Energy: -53.430183-0.000770j
[2025-08-08 00:00:18] [Iter 652/1050] R2[201/600], Temp: 0.7477, Energy: -53.526960+0.001032j
[2025-08-08 00:00:22] [Iter 653/1050] R2[202/600], Temp: 0.7455, Energy: -53.508773+0.000869j
[2025-08-08 00:00:26] [Iter 654/1050] R2[203/600], Temp: 0.7432, Energy: -53.530308+0.000370j
[2025-08-08 00:00:30] [Iter 655/1050] R2[204/600], Temp: 0.7409, Energy: -53.420493-0.001275j
[2025-08-08 00:00:34] [Iter 656/1050] R2[205/600], Temp: 0.7386, Energy: -53.451555+0.002109j
[2025-08-08 00:00:38] [Iter 657/1050] R2[206/600], Temp: 0.7363, Energy: -53.420043+0.002838j
[2025-08-08 00:00:42] [Iter 658/1050] R2[207/600], Temp: 0.7340, Energy: -53.286545-0.000416j
[2025-08-08 00:00:47] [Iter 659/1050] R2[208/600], Temp: 0.7316, Energy: -53.359104+0.004973j
[2025-08-08 00:00:51] [Iter 660/1050] R2[209/600], Temp: 0.7293, Energy: -53.316068+0.003424j
[2025-08-08 00:00:55] [Iter 661/1050] R2[210/600], Temp: 0.7270, Energy: -53.380826-0.002233j
[2025-08-08 00:00:59] [Iter 662/1050] R2[211/600], Temp: 0.7247, Energy: -53.405574+0.000343j
[2025-08-08 00:01:03] [Iter 663/1050] R2[212/600], Temp: 0.7223, Energy: -53.396090+0.002093j
[2025-08-08 00:01:07] [Iter 664/1050] R2[213/600], Temp: 0.7200, Energy: -53.390720+0.000215j
[2025-08-08 00:01:11] [Iter 665/1050] R2[214/600], Temp: 0.7176, Energy: -53.428371+0.001770j
[2025-08-08 00:01:15] [Iter 666/1050] R2[215/600], Temp: 0.7153, Energy: -53.313227-0.003072j
[2025-08-08 00:01:20] [Iter 667/1050] R2[216/600], Temp: 0.7129, Energy: -53.393553+0.001785j
[2025-08-08 00:01:24] [Iter 668/1050] R2[217/600], Temp: 0.7105, Energy: -53.369295-0.000972j
[2025-08-08 00:01:28] [Iter 669/1050] R2[218/600], Temp: 0.7081, Energy: -53.407495-0.003218j
[2025-08-08 00:01:32] [Iter 670/1050] R2[219/600], Temp: 0.7058, Energy: -53.429174+0.000439j
[2025-08-08 00:01:36] [Iter 671/1050] R2[220/600], Temp: 0.7034, Energy: -53.370519+0.002304j
[2025-08-08 00:01:40] [Iter 672/1050] R2[221/600], Temp: 0.7010, Energy: -53.443970+0.005990j
[2025-08-08 00:01:44] [Iter 673/1050] R2[222/600], Temp: 0.6986, Energy: -53.382368-0.002463j
[2025-08-08 00:01:48] [Iter 674/1050] R2[223/600], Temp: 0.6962, Energy: -53.494664-0.002840j
[2025-08-08 00:01:53] [Iter 675/1050] R2[224/600], Temp: 0.6938, Energy: -53.401203-0.004387j
[2025-08-08 00:01:57] [Iter 676/1050] R2[225/600], Temp: 0.6913, Energy: -53.247548+0.000436j
[2025-08-08 00:02:01] [Iter 677/1050] R2[226/600], Temp: 0.6889, Energy: -53.276445-0.000368j
[2025-08-08 00:02:05] [Iter 678/1050] R2[227/600], Temp: 0.6865, Energy: -53.344339-0.002218j
[2025-08-08 00:02:09] [Iter 679/1050] R2[228/600], Temp: 0.6841, Energy: -53.357070-0.000934j
[2025-08-08 00:02:13] [Iter 680/1050] R2[229/600], Temp: 0.6816, Energy: -53.382543-0.008290j
[2025-08-08 00:02:17] [Iter 681/1050] R2[230/600], Temp: 0.6792, Energy: -53.388458+0.001793j
[2025-08-08 00:02:21] [Iter 682/1050] R2[231/600], Temp: 0.6767, Energy: -53.484116-0.001315j
[2025-08-08 00:02:25] [Iter 683/1050] R2[232/600], Temp: 0.6743, Energy: -53.395801+0.000230j
[2025-08-08 00:02:30] [Iter 684/1050] R2[233/600], Temp: 0.6718, Energy: -53.393317+0.002033j
[2025-08-08 00:02:34] [Iter 685/1050] R2[234/600], Temp: 0.6694, Energy: -53.347187-0.002809j
[2025-08-08 00:02:38] [Iter 686/1050] R2[235/600], Temp: 0.6669, Energy: -53.313363-0.002714j
[2025-08-08 00:02:42] [Iter 687/1050] R2[236/600], Temp: 0.6644, Energy: -53.392542+0.001552j
[2025-08-08 00:02:46] [Iter 688/1050] R2[237/600], Temp: 0.6620, Energy: -53.263415-0.007859j
[2025-08-08 00:02:50] [Iter 689/1050] R2[238/600], Temp: 0.6595, Energy: -53.430955+0.004256j
[2025-08-08 00:02:54] [Iter 690/1050] R2[239/600], Temp: 0.6570, Energy: -53.411079+0.001415j
[2025-08-08 00:02:58] [Iter 691/1050] R2[240/600], Temp: 0.6545, Energy: -53.301646-0.000140j
[2025-08-08 00:03:03] [Iter 692/1050] R2[241/600], Temp: 0.6520, Energy: -53.313649-0.004180j
[2025-08-08 00:03:07] [Iter 693/1050] R2[242/600], Temp: 0.6495, Energy: -53.343207+0.004064j
[2025-08-08 00:03:11] [Iter 694/1050] R2[243/600], Temp: 0.6470, Energy: -53.348750-0.002795j
[2025-08-08 00:03:15] [Iter 695/1050] R2[244/600], Temp: 0.6445, Energy: -53.236063+0.001816j
[2025-08-08 00:03:19] [Iter 696/1050] R2[245/600], Temp: 0.6420, Energy: -53.290906+0.001491j
[2025-08-08 00:03:23] [Iter 697/1050] R2[246/600], Temp: 0.6395, Energy: -53.399082-0.001366j
[2025-08-08 00:03:27] [Iter 698/1050] R2[247/600], Temp: 0.6370, Energy: -53.441698+0.004004j
[2025-08-08 00:03:31] [Iter 699/1050] R2[248/600], Temp: 0.6345, Energy: -53.389713+0.002990j
[2025-08-08 00:03:36] [Iter 700/1050] R2[249/600], Temp: 0.6319, Energy: -53.506568-0.004776j
[2025-08-08 00:03:36] ✓ Checkpoint saved: checkpoint_iter_000700.pkl
[2025-08-08 00:03:40] [Iter 701/1050] R2[250/600], Temp: 0.6294, Energy: -53.370905-0.003933j
[2025-08-08 00:03:44] [Iter 702/1050] R2[251/600], Temp: 0.6269, Energy: -53.310861-0.001402j
[2025-08-08 00:03:48] [Iter 703/1050] R2[252/600], Temp: 0.6243, Energy: -53.339974+0.000534j
[2025-08-08 00:03:52] [Iter 704/1050] R2[253/600], Temp: 0.6218, Energy: -53.441240-0.000769j
[2025-08-08 00:03:56] [Iter 705/1050] R2[254/600], Temp: 0.6193, Energy: -53.432097+0.002521j
[2025-08-08 00:04:00] [Iter 706/1050] R2[255/600], Temp: 0.6167, Energy: -53.637424+0.000822j
[2025-08-08 00:04:05] [Iter 707/1050] R2[256/600], Temp: 0.6142, Energy: -53.634645+0.002064j
[2025-08-08 00:04:09] [Iter 708/1050] R2[257/600], Temp: 0.6116, Energy: -53.489773+0.000567j
[2025-08-08 00:04:13] [Iter 709/1050] R2[258/600], Temp: 0.6091, Energy: -53.515998-0.001603j
[2025-08-08 00:04:17] [Iter 710/1050] R2[259/600], Temp: 0.6065, Energy: -53.440693+0.004153j
[2025-08-08 00:04:21] [Iter 711/1050] R2[260/600], Temp: 0.6040, Energy: -53.484101-0.004115j
[2025-08-08 00:04:25] [Iter 712/1050] R2[261/600], Temp: 0.6014, Energy: -53.409503+0.000220j
[2025-08-08 00:04:29] [Iter 713/1050] R2[262/600], Temp: 0.5988, Energy: -53.497512+0.000067j
[2025-08-08 00:04:33] [Iter 714/1050] R2[263/600], Temp: 0.5963, Energy: -53.536194+0.003798j
[2025-08-08 00:04:38] [Iter 715/1050] R2[264/600], Temp: 0.5937, Energy: -53.601283-0.002360j
[2025-08-08 00:04:42] [Iter 716/1050] R2[265/600], Temp: 0.5911, Energy: -53.510353+0.001204j
[2025-08-08 00:04:46] [Iter 717/1050] R2[266/600], Temp: 0.5885, Energy: -53.533619-0.000636j
[2025-08-08 00:04:50] [Iter 718/1050] R2[267/600], Temp: 0.5860, Energy: -53.519501-0.000736j
[2025-08-08 00:04:54] [Iter 719/1050] R2[268/600], Temp: 0.5834, Energy: -53.472765-0.002914j
[2025-08-08 00:04:58] [Iter 720/1050] R2[269/600], Temp: 0.5808, Energy: -53.540925+0.002669j
[2025-08-08 00:05:02] [Iter 721/1050] R2[270/600], Temp: 0.5782, Energy: -53.536829+0.001568j
[2025-08-08 00:05:06] [Iter 722/1050] R2[271/600], Temp: 0.5756, Energy: -53.532030-0.000835j
[2025-08-08 00:05:11] [Iter 723/1050] R2[272/600], Temp: 0.5730, Energy: -53.478788-0.005665j
[2025-08-08 00:05:15] [Iter 724/1050] R2[273/600], Temp: 0.5705, Energy: -53.491395-0.001007j
[2025-08-08 00:05:19] [Iter 725/1050] R2[274/600], Temp: 0.5679, Energy: -53.424109-0.002264j
[2025-08-08 00:05:23] [Iter 726/1050] R2[275/600], Temp: 0.5653, Energy: -53.528449-0.006255j
[2025-08-08 00:05:27] [Iter 727/1050] R2[276/600], Temp: 0.5627, Energy: -53.459319+0.002854j
[2025-08-08 00:05:31] [Iter 728/1050] R2[277/600], Temp: 0.5601, Energy: -53.393309-0.001613j
[2025-08-08 00:05:35] [Iter 729/1050] R2[278/600], Temp: 0.5575, Energy: -53.484370+0.002578j
[2025-08-08 00:05:39] [Iter 730/1050] R2[279/600], Temp: 0.5549, Energy: -53.474719+0.001984j
[2025-08-08 00:05:43] [Iter 731/1050] R2[280/600], Temp: 0.5523, Energy: -53.431984-0.001034j
[2025-08-08 00:05:48] [Iter 732/1050] R2[281/600], Temp: 0.5497, Energy: -53.521335-0.005250j
[2025-08-08 00:05:52] [Iter 733/1050] R2[282/600], Temp: 0.5471, Energy: -53.481875-0.004696j
[2025-08-08 00:05:56] [Iter 734/1050] R2[283/600], Temp: 0.5444, Energy: -53.516904+0.001215j
[2025-08-08 00:06:00] [Iter 735/1050] R2[284/600], Temp: 0.5418, Energy: -53.524431+0.000564j
[2025-08-08 00:06:04] [Iter 736/1050] R2[285/600], Temp: 0.5392, Energy: -53.357146+0.000770j
[2025-08-08 00:06:08] [Iter 737/1050] R2[286/600], Temp: 0.5366, Energy: -53.477478+0.004706j
[2025-08-08 00:06:12] [Iter 738/1050] R2[287/600], Temp: 0.5340, Energy: -53.447450+0.004409j
[2025-08-08 00:06:17] [Iter 739/1050] R2[288/600], Temp: 0.5314, Energy: -53.437352+0.005507j
[2025-08-08 00:06:21] [Iter 740/1050] R2[289/600], Temp: 0.5288, Energy: -53.431146+0.001152j
[2025-08-08 00:06:25] [Iter 741/1050] R2[290/600], Temp: 0.5262, Energy: -53.484180+0.000821j
[2025-08-08 00:06:29] [Iter 742/1050] R2[291/600], Temp: 0.5236, Energy: -53.489453+0.002261j
[2025-08-08 00:06:33] [Iter 743/1050] R2[292/600], Temp: 0.5209, Energy: -53.526217+0.001744j
[2025-08-08 00:06:37] [Iter 744/1050] R2[293/600], Temp: 0.5183, Energy: -53.560758+0.002301j
[2025-08-08 00:06:41] [Iter 745/1050] R2[294/600], Temp: 0.5157, Energy: -53.630035-0.004599j
[2025-08-08 00:06:45] [Iter 746/1050] R2[295/600], Temp: 0.5131, Energy: -53.505906-0.002302j
[2025-08-08 00:06:50] [Iter 747/1050] R2[296/600], Temp: 0.5105, Energy: -53.558671+0.004360j
[2025-08-08 00:06:54] [Iter 748/1050] R2[297/600], Temp: 0.5079, Energy: -53.465387-0.000751j
[2025-08-08 00:06:58] [Iter 749/1050] R2[298/600], Temp: 0.5052, Energy: -53.402329-0.006933j
[2025-08-08 00:07:02] [Iter 750/1050] R2[299/600], Temp: 0.5026, Energy: -53.381270+0.003086j
[2025-08-08 00:07:06] [Iter 751/1050] R2[300/600], Temp: 0.5000, Energy: -53.403295+0.000777j
[2025-08-08 00:07:10] [Iter 752/1050] R2[301/600], Temp: 0.4974, Energy: -53.483658+0.002578j
[2025-08-08 00:07:14] [Iter 753/1050] R2[302/600], Temp: 0.4948, Energy: -53.597135-0.001119j
[2025-08-08 00:07:18] [Iter 754/1050] R2[303/600], Temp: 0.4921, Energy: -53.567919+0.001489j
[2025-08-08 00:07:23] [Iter 755/1050] R2[304/600], Temp: 0.4895, Energy: -53.481613-0.003118j
[2025-08-08 00:07:27] [Iter 756/1050] R2[305/600], Temp: 0.4869, Energy: -53.455469+0.003881j
[2025-08-08 00:07:31] [Iter 757/1050] R2[306/600], Temp: 0.4843, Energy: -53.486286-0.001278j
[2025-08-08 00:07:35] [Iter 758/1050] R2[307/600], Temp: 0.4817, Energy: -53.482173-0.000986j
[2025-08-08 00:07:39] [Iter 759/1050] R2[308/600], Temp: 0.4791, Energy: -53.491239+0.002589j
[2025-08-08 00:07:43] [Iter 760/1050] R2[309/600], Temp: 0.4764, Energy: -53.498276-0.001330j
[2025-08-08 00:07:47] [Iter 761/1050] R2[310/600], Temp: 0.4738, Energy: -53.484173-0.001521j
[2025-08-08 00:07:51] [Iter 762/1050] R2[311/600], Temp: 0.4712, Energy: -53.471875-0.002416j
[2025-08-08 00:07:55] [Iter 763/1050] R2[312/600], Temp: 0.4686, Energy: -53.506642-0.001999j
[2025-08-08 00:08:00] [Iter 764/1050] R2[313/600], Temp: 0.4660, Energy: -53.386246-0.002760j
[2025-08-08 00:08:04] [Iter 765/1050] R2[314/600], Temp: 0.4634, Energy: -53.434994-0.000464j
[2025-08-08 00:08:08] [Iter 766/1050] R2[315/600], Temp: 0.4608, Energy: -53.428393+0.001725j
[2025-08-08 00:08:12] [Iter 767/1050] R2[316/600], Temp: 0.4582, Energy: -53.386292-0.003695j
[2025-08-08 00:08:16] [Iter 768/1050] R2[317/600], Temp: 0.4556, Energy: -53.434410-0.004570j
[2025-08-08 00:08:20] [Iter 769/1050] R2[318/600], Temp: 0.4529, Energy: -53.356570-0.002093j
[2025-08-08 00:08:24] [Iter 770/1050] R2[319/600], Temp: 0.4503, Energy: -53.376243+0.001578j
[2025-08-08 00:08:28] [Iter 771/1050] R2[320/600], Temp: 0.4477, Energy: -53.393238+0.000366j
[2025-08-08 00:08:33] [Iter 772/1050] R2[321/600], Temp: 0.4451, Energy: -53.528330-0.001829j
[2025-08-08 00:08:37] [Iter 773/1050] R2[322/600], Temp: 0.4425, Energy: -53.401553-0.000994j
[2025-08-08 00:08:41] [Iter 774/1050] R2[323/600], Temp: 0.4399, Energy: -53.424246+0.006073j
[2025-08-08 00:08:45] [Iter 775/1050] R2[324/600], Temp: 0.4373, Energy: -53.431738-0.002035j
[2025-08-08 00:08:49] [Iter 776/1050] R2[325/600], Temp: 0.4347, Energy: -53.626166+0.002265j
[2025-08-08 00:08:53] [Iter 777/1050] R2[326/600], Temp: 0.4321, Energy: -53.492010+0.002060j
[2025-08-08 00:08:57] [Iter 778/1050] R2[327/600], Temp: 0.4295, Energy: -53.552257-0.004286j
[2025-08-08 00:09:01] [Iter 779/1050] R2[328/600], Temp: 0.4270, Energy: -53.372154-0.004547j
[2025-08-08 00:09:06] [Iter 780/1050] R2[329/600], Temp: 0.4244, Energy: -53.289231+0.000514j
[2025-08-08 00:09:10] [Iter 781/1050] R2[330/600], Temp: 0.4218, Energy: -53.345787-0.003178j
[2025-08-08 00:09:14] [Iter 782/1050] R2[331/600], Temp: 0.4192, Energy: -53.284340+0.000096j
[2025-08-08 00:09:18] [Iter 783/1050] R2[332/600], Temp: 0.4166, Energy: -53.246208+0.001872j
[2025-08-08 00:09:22] [Iter 784/1050] R2[333/600], Temp: 0.4140, Energy: -53.320975-0.002274j
[2025-08-08 00:09:26] [Iter 785/1050] R2[334/600], Temp: 0.4115, Energy: -53.460849-0.000783j
[2025-08-08 00:09:30] [Iter 786/1050] R2[335/600], Temp: 0.4089, Energy: -53.394681-0.001980j
[2025-08-08 00:09:35] [Iter 787/1050] R2[336/600], Temp: 0.4063, Energy: -53.441269-0.005431j
[2025-08-08 00:09:39] [Iter 788/1050] R2[337/600], Temp: 0.4037, Energy: -53.470603-0.000536j
[2025-08-08 00:09:43] [Iter 789/1050] R2[338/600], Temp: 0.4012, Energy: -53.406190+0.002838j
[2025-08-08 00:09:47] [Iter 790/1050] R2[339/600], Temp: 0.3986, Energy: -53.338224+0.000585j
[2025-08-08 00:09:51] [Iter 791/1050] R2[340/600], Temp: 0.3960, Energy: -53.398719+0.002597j
[2025-08-08 00:09:55] [Iter 792/1050] R2[341/600], Temp: 0.3935, Energy: -53.386857+0.000835j
[2025-08-08 00:09:59] [Iter 793/1050] R2[342/600], Temp: 0.3909, Energy: -53.350116+0.000380j
[2025-08-08 00:10:03] [Iter 794/1050] R2[343/600], Temp: 0.3884, Energy: -53.346258-0.005213j
[2025-08-08 00:10:08] [Iter 795/1050] R2[344/600], Temp: 0.3858, Energy: -53.345368-0.001282j
[2025-08-08 00:10:12] [Iter 796/1050] R2[345/600], Temp: 0.3833, Energy: -53.353837-0.000920j
[2025-08-08 00:10:16] [Iter 797/1050] R2[346/600], Temp: 0.3807, Energy: -53.496488+0.004488j
[2025-08-08 00:10:20] [Iter 798/1050] R2[347/600], Temp: 0.3782, Energy: -53.460008+0.003108j
[2025-08-08 00:10:24] [Iter 799/1050] R2[348/600], Temp: 0.3757, Energy: -53.506622+0.001966j
[2025-08-08 00:10:28] [Iter 800/1050] R2[349/600], Temp: 0.3731, Energy: -53.485522+0.003304j
[2025-08-08 00:10:28] ✓ Checkpoint saved: checkpoint_iter_000800.pkl
[2025-08-08 00:10:32] [Iter 801/1050] R2[350/600], Temp: 0.3706, Energy: -53.485287+0.001218j
[2025-08-08 00:10:36] [Iter 802/1050] R2[351/600], Temp: 0.3681, Energy: -53.430688-0.003657j
[2025-08-08 00:10:41] [Iter 803/1050] R2[352/600], Temp: 0.3655, Energy: -53.509999+0.000878j
[2025-08-08 00:10:45] [Iter 804/1050] R2[353/600], Temp: 0.3630, Energy: -53.409501+0.000174j
[2025-08-08 00:10:49] [Iter 805/1050] R2[354/600], Temp: 0.3605, Energy: -53.402224+0.001674j
[2025-08-08 00:10:53] [Iter 806/1050] R2[355/600], Temp: 0.3580, Energy: -53.456451-0.004084j
[2025-08-08 00:10:57] [Iter 807/1050] R2[356/600], Temp: 0.3555, Energy: -53.612675-0.002914j
[2025-08-08 00:11:01] [Iter 808/1050] R2[357/600], Temp: 0.3530, Energy: -53.623248-0.002260j
[2025-08-08 00:11:05] [Iter 809/1050] R2[358/600], Temp: 0.3505, Energy: -53.421348-0.001366j
[2025-08-08 00:11:09] [Iter 810/1050] R2[359/600], Temp: 0.3480, Energy: -53.291992-0.002028j
[2025-08-08 00:11:14] [Iter 811/1050] R2[360/600], Temp: 0.3455, Energy: -53.324457+0.001301j
[2025-08-08 00:11:18] [Iter 812/1050] R2[361/600], Temp: 0.3430, Energy: -53.449914+0.006879j
[2025-08-08 00:11:22] [Iter 813/1050] R2[362/600], Temp: 0.3405, Energy: -53.451522+0.002901j
[2025-08-08 00:11:26] [Iter 814/1050] R2[363/600], Temp: 0.3380, Energy: -53.417072-0.002374j
[2025-08-08 00:11:30] [Iter 815/1050] R2[364/600], Temp: 0.3356, Energy: -53.319251+0.006467j
[2025-08-08 00:11:34] [Iter 816/1050] R2[365/600], Temp: 0.3331, Energy: -53.196906-0.002972j
[2025-08-08 00:11:38] [Iter 817/1050] R2[366/600], Temp: 0.3306, Energy: -53.323730+0.000498j
[2025-08-08 00:11:42] [Iter 818/1050] R2[367/600], Temp: 0.3282, Energy: -53.329856+0.008088j
[2025-08-08 00:11:47] [Iter 819/1050] R2[368/600], Temp: 0.3257, Energy: -53.454755-0.006186j
[2025-08-08 00:11:51] [Iter 820/1050] R2[369/600], Temp: 0.3233, Energy: -53.412660-0.003488j
[2025-08-08 00:11:55] [Iter 821/1050] R2[370/600], Temp: 0.3208, Energy: -53.351642+0.003482j
[2025-08-08 00:11:59] [Iter 822/1050] R2[371/600], Temp: 0.3184, Energy: -53.399172+0.004247j
[2025-08-08 00:12:03] [Iter 823/1050] R2[372/600], Temp: 0.3159, Energy: -53.450230-0.000079j
[2025-08-08 00:12:07] [Iter 824/1050] R2[373/600], Temp: 0.3135, Energy: -53.492103-0.001396j
[2025-08-08 00:12:11] [Iter 825/1050] R2[374/600], Temp: 0.3111, Energy: -53.458222+0.005520j
[2025-08-08 00:12:15] [Iter 826/1050] R2[375/600], Temp: 0.3087, Energy: -53.464507-0.002677j
[2025-08-08 00:12:20] [Iter 827/1050] R2[376/600], Temp: 0.3062, Energy: -53.534133+0.002959j
[2025-08-08 00:12:24] [Iter 828/1050] R2[377/600], Temp: 0.3038, Energy: -53.516992-0.002141j
[2025-08-08 00:12:28] [Iter 829/1050] R2[378/600], Temp: 0.3014, Energy: -53.395480-0.001077j
[2025-08-08 00:12:32] [Iter 830/1050] R2[379/600], Temp: 0.2990, Energy: -53.408596-0.000906j
[2025-08-08 00:12:36] [Iter 831/1050] R2[380/600], Temp: 0.2966, Energy: -53.355250-0.000217j
[2025-08-08 00:12:40] [Iter 832/1050] R2[381/600], Temp: 0.2942, Energy: -53.416067-0.001187j
[2025-08-08 00:12:44] [Iter 833/1050] R2[382/600], Temp: 0.2919, Energy: -53.392732+0.000327j
[2025-08-08 00:12:48] [Iter 834/1050] R2[383/600], Temp: 0.2895, Energy: -53.366583-0.002542j
[2025-08-08 00:12:53] [Iter 835/1050] R2[384/600], Temp: 0.2871, Energy: -53.393143-0.001145j
[2025-08-08 00:12:57] [Iter 836/1050] R2[385/600], Temp: 0.2847, Energy: -53.404072+0.002261j
[2025-08-08 00:13:01] [Iter 837/1050] R2[386/600], Temp: 0.2824, Energy: -53.397230-0.001923j
[2025-08-08 00:13:05] [Iter 838/1050] R2[387/600], Temp: 0.2800, Energy: -53.324609-0.003513j
[2025-08-08 00:13:09] [Iter 839/1050] R2[388/600], Temp: 0.2777, Energy: -53.406537-0.002795j
[2025-08-08 00:13:13] [Iter 840/1050] R2[389/600], Temp: 0.2753, Energy: -53.443484-0.003272j
[2025-08-08 00:13:17] [Iter 841/1050] R2[390/600], Temp: 0.2730, Energy: -53.369866-0.000450j
[2025-08-08 00:13:21] [Iter 842/1050] R2[391/600], Temp: 0.2707, Energy: -53.406612-0.003203j
[2025-08-08 00:13:25] [Iter 843/1050] R2[392/600], Temp: 0.2684, Energy: -53.341666+0.004588j
[2025-08-08 00:13:30] [Iter 844/1050] R2[393/600], Temp: 0.2660, Energy: -53.340219+0.002264j
[2025-08-08 00:13:34] [Iter 845/1050] R2[394/600], Temp: 0.2637, Energy: -53.262844+0.000143j
[2025-08-08 00:13:38] [Iter 846/1050] R2[395/600], Temp: 0.2614, Energy: -53.226282+0.002591j
[2025-08-08 00:13:42] [Iter 847/1050] R2[396/600], Temp: 0.2591, Energy: -53.334311-0.005797j
[2025-08-08 00:13:46] [Iter 848/1050] R2[397/600], Temp: 0.2568, Energy: -53.340789-0.000658j
[2025-08-08 00:13:50] [Iter 849/1050] R2[398/600], Temp: 0.2545, Energy: -53.369668+0.000028j
[2025-08-08 00:13:54] [Iter 850/1050] R2[399/600], Temp: 0.2523, Energy: -53.353569-0.003229j
[2025-08-08 00:13:58] [Iter 851/1050] R2[400/600], Temp: 0.2500, Energy: -53.402885+0.000434j
[2025-08-08 00:14:03] [Iter 852/1050] R2[401/600], Temp: 0.2477, Energy: -53.380811-0.001070j
[2025-08-08 00:14:07] [Iter 853/1050] R2[402/600], Temp: 0.2455, Energy: -53.366650+0.005110j
[2025-08-08 00:14:11] [Iter 854/1050] R2[403/600], Temp: 0.2432, Energy: -53.376818+0.003968j
[2025-08-08 00:14:15] [Iter 855/1050] R2[404/600], Temp: 0.2410, Energy: -53.498074+0.000418j
[2025-08-08 00:14:19] [Iter 856/1050] R2[405/600], Temp: 0.2388, Energy: -53.463672+0.000248j
[2025-08-08 00:14:23] [Iter 857/1050] R2[406/600], Temp: 0.2365, Energy: -53.442763-0.002077j
[2025-08-08 00:14:27] [Iter 858/1050] R2[407/600], Temp: 0.2343, Energy: -53.433279-0.002323j
[2025-08-08 00:14:31] [Iter 859/1050] R2[408/600], Temp: 0.2321, Energy: -53.535874+0.001590j
[2025-08-08 00:14:36] [Iter 860/1050] R2[409/600], Temp: 0.2299, Energy: -53.578254-0.001040j
[2025-08-08 00:14:40] [Iter 861/1050] R2[410/600], Temp: 0.2277, Energy: -53.532671-0.003017j
[2025-08-08 00:14:44] [Iter 862/1050] R2[411/600], Temp: 0.2255, Energy: -53.482962+0.002246j
[2025-08-08 00:14:48] [Iter 863/1050] R2[412/600], Temp: 0.2233, Energy: -53.446284-0.000113j
[2025-08-08 00:14:52] [Iter 864/1050] R2[413/600], Temp: 0.2211, Energy: -53.387805+0.003870j
[2025-08-08 00:14:56] [Iter 865/1050] R2[414/600], Temp: 0.2190, Energy: -53.412130-0.004475j
[2025-08-08 00:15:00] [Iter 866/1050] R2[415/600], Temp: 0.2168, Energy: -53.412720-0.005024j
[2025-08-08 00:15:05] [Iter 867/1050] R2[416/600], Temp: 0.2146, Energy: -53.530009+0.000003j
[2025-08-08 00:15:09] [Iter 868/1050] R2[417/600], Temp: 0.2125, Energy: -53.412054+0.000975j
[2025-08-08 00:15:13] [Iter 869/1050] R2[418/600], Temp: 0.2104, Energy: -53.426169-0.002400j
[2025-08-08 00:15:17] [Iter 870/1050] R2[419/600], Temp: 0.2082, Energy: -53.401027-0.000496j
[2025-08-08 00:15:21] [Iter 871/1050] R2[420/600], Temp: 0.2061, Energy: -53.360313+0.006985j
[2025-08-08 00:15:25] [Iter 872/1050] R2[421/600], Temp: 0.2040, Energy: -53.448449+0.004374j
[2025-08-08 00:15:29] [Iter 873/1050] R2[422/600], Temp: 0.2019, Energy: -53.452629-0.000155j
[2025-08-08 00:15:33] [Iter 874/1050] R2[423/600], Temp: 0.1998, Energy: -53.501732+0.001726j
[2025-08-08 00:15:38] [Iter 875/1050] R2[424/600], Temp: 0.1977, Energy: -53.389560-0.000584j
[2025-08-08 00:15:42] [Iter 876/1050] R2[425/600], Temp: 0.1956, Energy: -53.398456-0.000704j
[2025-08-08 00:15:46] [Iter 877/1050] R2[426/600], Temp: 0.1935, Energy: -53.480771+0.002940j
[2025-08-08 00:15:50] [Iter 878/1050] R2[427/600], Temp: 0.1915, Energy: -53.504507+0.002443j
[2025-08-08 00:15:54] [Iter 879/1050] R2[428/600], Temp: 0.1894, Energy: -53.567145+0.007117j
[2025-08-08 00:15:58] [Iter 880/1050] R2[429/600], Temp: 0.1874, Energy: -53.598516-0.001828j
[2025-08-08 00:16:02] [Iter 881/1050] R2[430/600], Temp: 0.1853, Energy: -53.542082+0.001610j
[2025-08-08 00:16:06] [Iter 882/1050] R2[431/600], Temp: 0.1833, Energy: -53.499511-0.003135j
[2025-08-08 00:16:11] [Iter 883/1050] R2[432/600], Temp: 0.1813, Energy: -53.632664-0.000043j
[2025-08-08 00:16:15] [Iter 884/1050] R2[433/600], Temp: 0.1793, Energy: -53.453907+0.002998j
[2025-08-08 00:16:19] [Iter 885/1050] R2[434/600], Temp: 0.1773, Energy: -53.532972+0.000120j
[2025-08-08 00:16:23] [Iter 886/1050] R2[435/600], Temp: 0.1753, Energy: -53.471495+0.005276j
[2025-08-08 00:16:27] [Iter 887/1050] R2[436/600], Temp: 0.1733, Energy: -53.477228+0.005451j
[2025-08-08 00:16:31] [Iter 888/1050] R2[437/600], Temp: 0.1713, Energy: -53.508327+0.003410j
[2025-08-08 00:16:35] [Iter 889/1050] R2[438/600], Temp: 0.1693, Energy: -53.495321-0.000038j
[2025-08-08 00:16:39] [Iter 890/1050] R2[439/600], Temp: 0.1674, Energy: -53.494073-0.001704j
[2025-08-08 00:16:44] [Iter 891/1050] R2[440/600], Temp: 0.1654, Energy: -53.589862+0.002789j
[2025-08-08 00:16:48] [Iter 892/1050] R2[441/600], Temp: 0.1635, Energy: -53.546864+0.002617j
[2025-08-08 00:16:52] [Iter 893/1050] R2[442/600], Temp: 0.1616, Energy: -53.615366+0.006571j
[2025-08-08 00:16:56] [Iter 894/1050] R2[443/600], Temp: 0.1596, Energy: -53.613667+0.001514j
[2025-08-08 00:17:00] [Iter 895/1050] R2[444/600], Temp: 0.1577, Energy: -53.590997-0.000620j
[2025-08-08 00:17:04] [Iter 896/1050] R2[445/600], Temp: 0.1558, Energy: -53.568556+0.003832j
[2025-08-08 00:17:08] [Iter 897/1050] R2[446/600], Temp: 0.1539, Energy: -53.563002+0.008458j
[2025-08-08 00:17:12] [Iter 898/1050] R2[447/600], Temp: 0.1520, Energy: -53.493587+0.000443j
[2025-08-08 00:17:17] [Iter 899/1050] R2[448/600], Temp: 0.1502, Energy: -53.527141+0.006782j
[2025-08-08 00:17:21] [Iter 900/1050] R2[449/600], Temp: 0.1483, Energy: -53.537925+0.000631j
[2025-08-08 00:17:21] ✓ Checkpoint saved: checkpoint_iter_000900.pkl
[2025-08-08 00:17:25] [Iter 901/1050] R2[450/600], Temp: 0.1464, Energy: -53.439852-0.004183j
[2025-08-08 00:17:29] [Iter 902/1050] R2[451/600], Temp: 0.1446, Energy: -53.536490-0.000084j
[2025-08-08 00:17:33] [Iter 903/1050] R2[452/600], Temp: 0.1428, Energy: -53.436487-0.001121j
[2025-08-08 00:17:37] [Iter 904/1050] R2[453/600], Temp: 0.1409, Energy: -53.382814-0.003240j
[2025-08-08 00:17:41] [Iter 905/1050] R2[454/600], Temp: 0.1391, Energy: -53.492160-0.007510j
[2025-08-08 00:17:45] [Iter 906/1050] R2[455/600], Temp: 0.1373, Energy: -53.345439-0.002534j
[2025-08-08 00:17:50] [Iter 907/1050] R2[456/600], Temp: 0.1355, Energy: -53.269395-0.001679j
[2025-08-08 00:17:54] [Iter 908/1050] R2[457/600], Temp: 0.1337, Energy: -53.308009-0.000772j
[2025-08-08 00:17:58] [Iter 909/1050] R2[458/600], Temp: 0.1320, Energy: -53.345059-0.001004j
[2025-08-08 00:18:02] [Iter 910/1050] R2[459/600], Temp: 0.1302, Energy: -53.362790+0.000476j
[2025-08-08 00:18:06] [Iter 911/1050] R2[460/600], Temp: 0.1284, Energy: -53.470334+0.001402j
[2025-08-08 00:18:10] [Iter 912/1050] R2[461/600], Temp: 0.1267, Energy: -53.365347+0.002684j
[2025-08-08 00:18:14] [Iter 913/1050] R2[462/600], Temp: 0.1249, Energy: -53.360948+0.001430j
[2025-08-08 00:18:18] [Iter 914/1050] R2[463/600], Temp: 0.1232, Energy: -53.387635+0.006319j
[2025-08-08 00:18:23] [Iter 915/1050] R2[464/600], Temp: 0.1215, Energy: -53.274182+0.002731j
[2025-08-08 00:18:27] [Iter 916/1050] R2[465/600], Temp: 0.1198, Energy: -53.328617-0.001774j
[2025-08-08 00:18:31] [Iter 917/1050] R2[466/600], Temp: 0.1181, Energy: -53.386062-0.000050j
[2025-08-08 00:18:35] [Iter 918/1050] R2[467/600], Temp: 0.1164, Energy: -53.447951-0.000676j
[2025-08-08 00:18:39] [Iter 919/1050] R2[468/600], Temp: 0.1147, Energy: -53.325707+0.007505j
[2025-08-08 00:18:43] [Iter 920/1050] R2[469/600], Temp: 0.1131, Energy: -53.491005-0.001256j
[2025-08-08 00:18:47] [Iter 921/1050] R2[470/600], Temp: 0.1114, Energy: -53.469473+0.001029j
[2025-08-08 00:18:51] [Iter 922/1050] R2[471/600], Temp: 0.1098, Energy: -53.493265+0.003177j
[2025-08-08 00:18:55] [Iter 923/1050] R2[472/600], Temp: 0.1082, Energy: -53.527512-0.002756j
[2025-08-08 00:19:00] [Iter 924/1050] R2[473/600], Temp: 0.1065, Energy: -53.457639-0.002110j
[2025-08-08 00:19:04] [Iter 925/1050] R2[474/600], Temp: 0.1049, Energy: -53.408934-0.000391j
[2025-08-08 00:19:08] [Iter 926/1050] R2[475/600], Temp: 0.1033, Energy: -53.405457-0.004221j
[2025-08-08 00:19:12] [Iter 927/1050] R2[476/600], Temp: 0.1017, Energy: -53.301239+0.003007j
[2025-08-08 00:19:16] [Iter 928/1050] R2[477/600], Temp: 0.1002, Energy: -53.233495-0.001374j
[2025-08-08 00:19:20] [Iter 929/1050] R2[478/600], Temp: 0.0986, Energy: -53.392881+0.002805j
[2025-08-08 00:19:24] [Iter 930/1050] R2[479/600], Temp: 0.0970, Energy: -53.380166+0.001216j
[2025-08-08 00:19:29] [Iter 931/1050] R2[480/600], Temp: 0.0955, Energy: -53.359120+0.003010j
[2025-08-08 00:19:33] [Iter 932/1050] R2[481/600], Temp: 0.0940, Energy: -53.362199+0.004922j
[2025-08-08 00:19:37] [Iter 933/1050] R2[482/600], Temp: 0.0924, Energy: -53.345414-0.007333j
[2025-08-08 00:19:41] [Iter 934/1050] R2[483/600], Temp: 0.0909, Energy: -53.335014-0.001493j
[2025-08-08 00:19:45] [Iter 935/1050] R2[484/600], Temp: 0.0894, Energy: -53.391225-0.000370j
[2025-08-08 00:19:49] [Iter 936/1050] R2[485/600], Temp: 0.0879, Energy: -53.542595-0.003729j
[2025-08-08 00:19:53] [Iter 937/1050] R2[486/600], Temp: 0.0865, Energy: -53.434570-0.000877j
[2025-08-08 00:19:57] [Iter 938/1050] R2[487/600], Temp: 0.0850, Energy: -53.456941+0.000334j
[2025-08-08 00:20:01] [Iter 939/1050] R2[488/600], Temp: 0.0835, Energy: -53.437064+0.005393j
[2025-08-08 00:20:06] [Iter 940/1050] R2[489/600], Temp: 0.0821, Energy: -53.410096+0.006855j
[2025-08-08 00:20:10] [Iter 941/1050] R2[490/600], Temp: 0.0807, Energy: -53.436708+0.006827j
[2025-08-08 00:20:14] [Iter 942/1050] R2[491/600], Temp: 0.0792, Energy: -53.362328+0.005413j
[2025-08-08 00:20:18] [Iter 943/1050] R2[492/600], Temp: 0.0778, Energy: -53.304055+0.003666j
[2025-08-08 00:20:22] [Iter 944/1050] R2[493/600], Temp: 0.0764, Energy: -53.431700+0.002734j
[2025-08-08 00:20:26] [Iter 945/1050] R2[494/600], Temp: 0.0751, Energy: -53.386379-0.000660j
[2025-08-08 00:20:30] [Iter 946/1050] R2[495/600], Temp: 0.0737, Energy: -53.484724+0.007108j
[2025-08-08 00:20:35] [Iter 947/1050] R2[496/600], Temp: 0.0723, Energy: -53.605997+0.001205j
[2025-08-08 00:20:39] [Iter 948/1050] R2[497/600], Temp: 0.0710, Energy: -53.704632-0.004633j
[2025-08-08 00:20:43] [Iter 949/1050] R2[498/600], Temp: 0.0696, Energy: -53.630353+0.005225j
[2025-08-08 00:20:47] [Iter 950/1050] R2[499/600], Temp: 0.0683, Energy: -53.572712-0.005617j
[2025-08-08 00:20:51] [Iter 951/1050] R2[500/600], Temp: 0.0670, Energy: -53.607667+0.001959j
[2025-08-08 00:20:55] [Iter 952/1050] R2[501/600], Temp: 0.0657, Energy: -53.572324+0.000477j
[2025-08-08 00:20:59] [Iter 953/1050] R2[502/600], Temp: 0.0644, Energy: -53.498923+0.000728j
[2025-08-08 00:21:03] [Iter 954/1050] R2[503/600], Temp: 0.0631, Energy: -53.560323-0.002350j
[2025-08-08 00:21:08] [Iter 955/1050] R2[504/600], Temp: 0.0618, Energy: -53.495013+0.007907j
[2025-08-08 00:21:12] [Iter 956/1050] R2[505/600], Temp: 0.0606, Energy: -53.555809-0.000526j
[2025-08-08 00:21:16] [Iter 957/1050] R2[506/600], Temp: 0.0593, Energy: -53.543099+0.004309j
[2025-08-08 00:21:20] [Iter 958/1050] R2[507/600], Temp: 0.0581, Energy: -53.525214+0.000280j
[2025-08-08 00:21:24] [Iter 959/1050] R2[508/600], Temp: 0.0569, Energy: -53.417399+0.001977j
[2025-08-08 00:21:28] [Iter 960/1050] R2[509/600], Temp: 0.0557, Energy: -53.622870-0.006730j
[2025-08-08 00:21:32] [Iter 961/1050] R2[510/600], Temp: 0.0545, Energy: -53.653595-0.002473j
[2025-08-08 00:21:36] [Iter 962/1050] R2[511/600], Temp: 0.0533, Energy: -53.556123-0.003041j
[2025-08-08 00:21:41] [Iter 963/1050] R2[512/600], Temp: 0.0521, Energy: -53.516361+0.005149j
[2025-08-08 00:21:45] [Iter 964/1050] R2[513/600], Temp: 0.0510, Energy: -53.469947+0.001279j
[2025-08-08 00:21:49] [Iter 965/1050] R2[514/600], Temp: 0.0498, Energy: -53.366593-0.000461j
[2025-08-08 00:21:53] [Iter 966/1050] R2[515/600], Temp: 0.0487, Energy: -53.451379+0.001083j
[2025-08-08 00:21:57] [Iter 967/1050] R2[516/600], Temp: 0.0476, Energy: -53.382978+0.004104j
[2025-08-08 00:22:01] [Iter 968/1050] R2[517/600], Temp: 0.0465, Energy: -53.431969-0.000023j
[2025-08-08 00:22:05] [Iter 969/1050] R2[518/600], Temp: 0.0454, Energy: -53.440707+0.004681j
[2025-08-08 00:22:09] [Iter 970/1050] R2[519/600], Temp: 0.0443, Energy: -53.437452+0.001387j
[2025-08-08 00:22:14] [Iter 971/1050] R2[520/600], Temp: 0.0432, Energy: -53.583969+0.000270j
[2025-08-08 00:22:18] [Iter 972/1050] R2[521/600], Temp: 0.0422, Energy: -53.623163+0.009470j
[2025-08-08 00:22:22] [Iter 973/1050] R2[522/600], Temp: 0.0411, Energy: -53.584252+0.005072j
[2025-08-08 00:22:26] [Iter 974/1050] R2[523/600], Temp: 0.0401, Energy: -53.577073+0.000660j
[2025-08-08 00:22:30] [Iter 975/1050] R2[524/600], Temp: 0.0391, Energy: -53.500181-0.001696j
[2025-08-08 00:22:34] [Iter 976/1050] R2[525/600], Temp: 0.0381, Energy: -53.544887-0.001769j
[2025-08-08 00:22:38] [Iter 977/1050] R2[526/600], Temp: 0.0371, Energy: -53.532336-0.001263j
[2025-08-08 00:22:42] [Iter 978/1050] R2[527/600], Temp: 0.0361, Energy: -53.357228+0.002491j
[2025-08-08 00:22:47] [Iter 979/1050] R2[528/600], Temp: 0.0351, Energy: -53.412436-0.002200j
[2025-08-08 00:22:51] [Iter 980/1050] R2[529/600], Temp: 0.0342, Energy: -53.481046-0.000300j
[2025-08-08 00:22:55] [Iter 981/1050] R2[530/600], Temp: 0.0332, Energy: -53.480803-0.001686j
[2025-08-08 00:22:59] [Iter 982/1050] R2[531/600], Temp: 0.0323, Energy: -53.414545-0.003936j
[2025-08-08 00:23:03] [Iter 983/1050] R2[532/600], Temp: 0.0314, Energy: -53.403890-0.003114j
[2025-08-08 00:23:07] [Iter 984/1050] R2[533/600], Temp: 0.0305, Energy: -53.468949-0.002064j
[2025-08-08 00:23:11] [Iter 985/1050] R2[534/600], Temp: 0.0296, Energy: -53.504503-0.001250j
[2025-08-08 00:23:15] [Iter 986/1050] R2[535/600], Temp: 0.0287, Energy: -53.550648+0.000759j
[2025-08-08 00:23:20] [Iter 987/1050] R2[536/600], Temp: 0.0278, Energy: -53.501915-0.003531j
[2025-08-08 00:23:24] [Iter 988/1050] R2[537/600], Temp: 0.0270, Energy: -53.367435+0.000485j
[2025-08-08 00:23:28] [Iter 989/1050] R2[538/600], Temp: 0.0261, Energy: -53.595221-0.000750j
[2025-08-08 00:23:32] [Iter 990/1050] R2[539/600], Temp: 0.0253, Energy: -53.417913-0.004148j
[2025-08-08 00:23:36] [Iter 991/1050] R2[540/600], Temp: 0.0245, Energy: -53.417479+0.000851j
[2025-08-08 00:23:40] [Iter 992/1050] R2[541/600], Temp: 0.0237, Energy: -53.341995+0.007326j
[2025-08-08 00:23:44] [Iter 993/1050] R2[542/600], Temp: 0.0229, Energy: -53.456865+0.005402j
[2025-08-08 00:23:48] [Iter 994/1050] R2[543/600], Temp: 0.0221, Energy: -53.381886+0.001331j
[2025-08-08 00:23:53] [Iter 995/1050] R2[544/600], Temp: 0.0213, Energy: -53.448585-0.005346j
[2025-08-08 00:23:57] [Iter 996/1050] R2[545/600], Temp: 0.0206, Energy: -53.445344+0.006352j
[2025-08-08 00:24:01] [Iter 997/1050] R2[546/600], Temp: 0.0199, Energy: -53.492398-0.001927j
[2025-08-08 00:24:05] [Iter 998/1050] R2[547/600], Temp: 0.0191, Energy: -53.492287+0.000744j
[2025-08-08 00:24:09] [Iter 999/1050] R2[548/600], Temp: 0.0184, Energy: -53.448802+0.000152j
[2025-08-08 00:24:13] [Iter 1000/1050] R2[549/600], Temp: 0.0177, Energy: -53.620750+0.002103j
[2025-08-08 00:24:13] ✓ Checkpoint saved: checkpoint_iter_001000.pkl
[2025-08-08 00:24:17] [Iter 1001/1050] R2[550/600], Temp: 0.0170, Energy: -53.554084-0.001078j
[2025-08-08 00:24:21] [Iter 1002/1050] R2[551/600], Temp: 0.0164, Energy: -53.538630+0.002167j
[2025-08-08 00:24:25] [Iter 1003/1050] R2[552/600], Temp: 0.0157, Energy: -53.596543+0.002685j
[2025-08-08 00:24:30] [Iter 1004/1050] R2[553/600], Temp: 0.0151, Energy: -53.540837-0.001847j
[2025-08-08 00:24:34] [Iter 1005/1050] R2[554/600], Temp: 0.0144, Energy: -53.504675-0.005135j
[2025-08-08 00:24:38] [Iter 1006/1050] R2[555/600], Temp: 0.0138, Energy: -53.581054-0.000789j
[2025-08-08 00:24:42] [Iter 1007/1050] R2[556/600], Temp: 0.0132, Energy: -53.587993-0.000646j
[2025-08-08 00:24:46] [Iter 1008/1050] R2[557/600], Temp: 0.0126, Energy: -53.580833+0.000470j
[2025-08-08 00:24:50] [Iter 1009/1050] R2[558/600], Temp: 0.0120, Energy: -53.496336-0.001786j
[2025-08-08 00:24:54] [Iter 1010/1050] R2[559/600], Temp: 0.0115, Energy: -53.385031-0.004012j
[2025-08-08 00:24:58] [Iter 1011/1050] R2[560/600], Temp: 0.0109, Energy: -53.506783-0.003541j
[2025-08-08 00:25:03] [Iter 1012/1050] R2[561/600], Temp: 0.0104, Energy: -53.430016-0.004925j
[2025-08-08 00:25:07] [Iter 1013/1050] R2[562/600], Temp: 0.0099, Energy: -53.431513-0.003251j
[2025-08-08 00:25:11] [Iter 1014/1050] R2[563/600], Temp: 0.0094, Energy: -53.477220+0.000098j
[2025-08-08 00:25:15] [Iter 1015/1050] R2[564/600], Temp: 0.0089, Energy: -53.391658-0.002333j
[2025-08-08 00:25:19] [Iter 1016/1050] R2[565/600], Temp: 0.0084, Energy: -53.504780-0.002163j
[2025-08-08 00:25:23] [Iter 1017/1050] R2[566/600], Temp: 0.0079, Energy: -53.501754+0.000567j
[2025-08-08 00:25:27] [Iter 1018/1050] R2[567/600], Temp: 0.0074, Energy: -53.600603-0.000522j
[2025-08-08 00:25:31] [Iter 1019/1050] R2[568/600], Temp: 0.0070, Energy: -53.498328-0.004432j
[2025-08-08 00:25:36] [Iter 1020/1050] R2[569/600], Temp: 0.0066, Energy: -53.385460-0.002289j
[2025-08-08 00:25:40] [Iter 1021/1050] R2[570/600], Temp: 0.0062, Energy: -53.415889+0.000495j
[2025-08-08 00:25:44] [Iter 1022/1050] R2[571/600], Temp: 0.0058, Energy: -53.498187-0.000098j
[2025-08-08 00:25:48] [Iter 1023/1050] R2[572/600], Temp: 0.0054, Energy: -53.463846+0.000985j
[2025-08-08 00:25:52] [Iter 1024/1050] R2[573/600], Temp: 0.0050, Energy: -53.380559-0.004028j
[2025-08-08 00:25:56] [Iter 1025/1050] R2[574/600], Temp: 0.0046, Energy: -53.483542-0.001979j
[2025-08-08 00:26:00] [Iter 1026/1050] R2[575/600], Temp: 0.0043, Energy: -53.354208+0.003004j
[2025-08-08 00:26:05] [Iter 1027/1050] R2[576/600], Temp: 0.0039, Energy: -53.380557-0.002007j
[2025-08-08 00:26:09] [Iter 1028/1050] R2[577/600], Temp: 0.0036, Energy: -53.391456+0.001480j
[2025-08-08 00:26:13] [Iter 1029/1050] R2[578/600], Temp: 0.0033, Energy: -53.349136-0.000583j
[2025-08-08 00:26:17] [Iter 1030/1050] R2[579/600], Temp: 0.0030, Energy: -53.434034-0.001214j
[2025-08-08 00:26:21] [Iter 1031/1050] R2[580/600], Temp: 0.0027, Energy: -53.364964-0.006018j
[2025-08-08 00:26:25] [Iter 1032/1050] R2[581/600], Temp: 0.0025, Energy: -53.390900-0.006917j
[2025-08-08 00:26:29] [Iter 1033/1050] R2[582/600], Temp: 0.0022, Energy: -53.559210-0.002128j
[2025-08-08 00:26:33] [Iter 1034/1050] R2[583/600], Temp: 0.0020, Energy: -53.369768+0.001602j
[2025-08-08 00:26:38] [Iter 1035/1050] R2[584/600], Temp: 0.0018, Energy: -53.448875+0.002167j
[2025-08-08 00:26:42] [Iter 1036/1050] R2[585/600], Temp: 0.0015, Energy: -53.365034+0.004328j
[2025-08-08 00:26:46] [Iter 1037/1050] R2[586/600], Temp: 0.0013, Energy: -53.386941+0.001162j
[2025-08-08 00:26:50] [Iter 1038/1050] R2[587/600], Temp: 0.0012, Energy: -53.431978+0.000618j
[2025-08-08 00:26:54] [Iter 1039/1050] R2[588/600], Temp: 0.0010, Energy: -53.379420-0.002424j
[2025-08-08 00:26:58] [Iter 1040/1050] R2[589/600], Temp: 0.0008, Energy: -53.298467-0.000818j
[2025-08-08 00:27:02] [Iter 1041/1050] R2[590/600], Temp: 0.0007, Energy: -53.385897-0.003325j
[2025-08-08 00:27:06] [Iter 1042/1050] R2[591/600], Temp: 0.0006, Energy: -53.408324-0.003982j
[2025-08-08 00:27:11] [Iter 1043/1050] R2[592/600], Temp: 0.0004, Energy: -53.371487+0.001051j
[2025-08-08 00:27:15] [Iter 1044/1050] R2[593/600], Temp: 0.0003, Energy: -53.459576-0.003313j
[2025-08-08 00:27:19] [Iter 1045/1050] R2[594/600], Temp: 0.0002, Energy: -53.434415-0.006191j
[2025-08-08 00:27:23] [Iter 1046/1050] R2[595/600], Temp: 0.0002, Energy: -53.397569-0.000476j
[2025-08-08 00:27:27] [Iter 1047/1050] R2[596/600], Temp: 0.0001, Energy: -53.443212-0.000887j
[2025-08-08 00:27:31] [Iter 1048/1050] R2[597/600], Temp: 0.0001, Energy: -53.440705+0.001653j
[2025-08-08 00:27:35] [Iter 1049/1050] R2[598/600], Temp: 0.0000, Energy: -53.477787+0.003793j
[2025-08-08 00:27:39] [Iter 1050/1050] R2[599/600], Temp: 0.0000, Energy: -53.416914-0.000981j
[2025-08-08 00:27:39] ✅ Training completed | Restarts: 2
[2025-08-08 00:27:39] ============================================================
[2025-08-08 00:27:39] Training completed | Runtime: 4371.4s
[2025-08-08 00:27:52] ✓ Final state saved: checkpoints/final_GCNN.pkl
[2025-08-08 00:27:52] ============================================================
