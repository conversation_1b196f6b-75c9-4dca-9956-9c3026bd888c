[2025-08-23 00:31:30] ✓ 从checkpoint恢复: results/L=4/J2=0.00/J1=0.09/training/checkpoints/final_GCNN.pkl
[2025-08-23 00:31:30]   - 迭代次数: final
[2025-08-23 00:31:30]   - 能量: -56.535833-0.002627j ± 0.083461
[2025-08-23 00:31:30]   - 时间戳: 2025-08-23T00:09:43.760033+08:00
[2025-08-23 00:31:39] ✓ 变分状态参数已从checkpoint恢复
[2025-08-23 00:31:39] ✓ 从final状态恢复, 重置迭代计数为0
[2025-08-23 00:31:39] ==================================================
[2025-08-23 00:31:39] GCNN for Shastry-Sutherland Model
[2025-08-23 00:31:39] ==================================================
[2025-08-23 00:31:39] System parameters:
[2025-08-23 00:31:39]   - System size: L=4, N=64
[2025-08-23 00:31:39]   - System parameters: J1=0.1, J2=0.0, Q=1.0
[2025-08-23 00:31:39] --------------------------------------------------
[2025-08-23 00:31:39] Model parameters:
[2025-08-23 00:31:39]   - Number of layers = 4
[2025-08-23 00:31:39]   - Number of features = 4
[2025-08-23 00:31:39]   - Total parameters = 12572
[2025-08-23 00:31:39] --------------------------------------------------
[2025-08-23 00:31:39] Training parameters:
[2025-08-23 00:31:39]   - Learning rate: 0.015
[2025-08-23 00:31:39]   - Total iterations: 1050
[2025-08-23 00:31:39]   - Annealing cycles: 3
[2025-08-23 00:31:39]   - Initial period: 150
[2025-08-23 00:31:39]   - Period multiplier: 2.0
[2025-08-23 00:31:39]   - Temperature range: 0.0-1.0
[2025-08-23 00:31:39]   - Samples: 4096
[2025-08-23 00:31:39]   - Discarded samples: 0
[2025-08-23 00:31:39]   - Chunk size: 2048
[2025-08-23 00:31:39]   - Diagonal shift: 0.2
[2025-08-23 00:31:40]   - Gradient clipping: 1.0
[2025-08-23 00:31:40]   - Checkpoint enabled: interval=100
[2025-08-23 00:31:40]   - Checkpoint directory: results/L=4/J2=0.00/J1=0.10/training/checkpoints
[2025-08-23 00:31:40] --------------------------------------------------
[2025-08-23 00:31:40] Device status:
[2025-08-23 00:31:40]   - Devices model: NVIDIA H200 NVL
[2025-08-23 00:31:40]   - Number of devices: 1
[2025-08-23 00:31:40]   - Sharding: True
[2025-08-23 00:31:40] ============================================================
[2025-08-23 00:32:21] [Iter 1/1050] R0[0/150], Temp: 1.0000, Energy: -56.852409-0.008194j
[2025-08-23 00:32:46] [Iter 2/1050] R0[1/150], Temp: 0.9999, Energy: -57.035469+0.001243j
[2025-08-23 00:32:50] [Iter 3/1050] R0[2/150], Temp: 0.9996, Energy: -56.933103+0.002110j
[2025-08-23 00:32:54] [Iter 4/1050] R0[3/150], Temp: 0.9990, Energy: -56.919660+0.002389j
[2025-08-23 00:32:58] [Iter 5/1050] R0[4/150], Temp: 0.9982, Energy: -56.961637+0.003264j
[2025-08-23 00:33:03] [Iter 6/1050] R0[5/150], Temp: 0.9973, Energy: -57.041539+0.002054j
[2025-08-23 00:33:07] [Iter 7/1050] R0[6/150], Temp: 0.9961, Energy: -57.064823+0.004075j
[2025-08-23 00:33:11] [Iter 8/1050] R0[7/150], Temp: 0.9946, Energy: -56.797153-0.002753j
[2025-08-23 00:33:16] [Iter 9/1050] R0[8/150], Temp: 0.9930, Energy: -57.021934+0.000009j
[2025-08-23 00:33:20] [Iter 10/1050] R0[9/150], Temp: 0.9911, Energy: -57.042794-0.002391j
[2025-08-23 00:33:24] [Iter 11/1050] R0[10/150], Temp: 0.9891, Energy: -56.989934-0.001324j
[2025-08-23 00:33:29] [Iter 12/1050] R0[11/150], Temp: 0.9868, Energy: -57.035473+0.002297j
[2025-08-23 00:33:33] [Iter 13/1050] R0[12/150], Temp: 0.9843, Energy: -57.003141-0.001803j
[2025-08-23 00:33:37] [Iter 14/1050] R0[13/150], Temp: 0.9816, Energy: -56.918120-0.000628j
[2025-08-23 00:33:42] [Iter 15/1050] R0[14/150], Temp: 0.9787, Energy: -56.852874-0.007272j
[2025-08-23 00:33:46] [Iter 16/1050] R0[15/150], Temp: 0.9755, Energy: -56.937515-0.000483j
[2025-08-23 00:33:50] [Iter 17/1050] R0[16/150], Temp: 0.9722, Energy: -57.045447-0.003860j
[2025-08-23 00:33:55] [Iter 18/1050] R0[17/150], Temp: 0.9686, Energy: -57.087044+0.002408j
[2025-08-23 00:33:59] [Iter 19/1050] R0[18/150], Temp: 0.9649, Energy: -57.023081+0.003174j
[2025-08-23 00:34:03] [Iter 20/1050] R0[19/150], Temp: 0.9609, Energy: -57.002221-0.005251j
[2025-08-23 00:34:08] [Iter 21/1050] R0[20/150], Temp: 0.9568, Energy: -57.012075-0.001044j
[2025-08-23 00:34:12] [Iter 22/1050] R0[21/150], Temp: 0.9524, Energy: -56.896258-0.001458j
[2025-08-23 00:34:16] [Iter 23/1050] R0[22/150], Temp: 0.9479, Energy: -56.934536-0.006017j
[2025-08-23 00:34:20] [Iter 24/1050] R0[23/150], Temp: 0.9431, Energy: -56.958307+0.001432j
[2025-08-23 00:34:25] [Iter 25/1050] R0[24/150], Temp: 0.9382, Energy: -57.006631-0.001473j
[2025-08-23 00:34:29] [Iter 26/1050] R0[25/150], Temp: 0.9330, Energy: -57.009327-0.002614j
[2025-08-23 00:34:33] [Iter 27/1050] R0[26/150], Temp: 0.9277, Energy: -57.013106-0.002830j
[2025-08-23 00:34:38] [Iter 28/1050] R0[27/150], Temp: 0.9222, Energy: -57.014456-0.003712j
[2025-08-23 00:34:42] [Iter 29/1050] R0[28/150], Temp: 0.9165, Energy: -57.106201+0.000638j
[2025-08-23 00:34:46] [Iter 30/1050] R0[29/150], Temp: 0.9106, Energy: -57.267907+0.001963j
[2025-08-23 00:34:51] [Iter 31/1050] R0[30/150], Temp: 0.9045, Energy: -57.089880-0.003584j
[2025-08-23 00:34:55] [Iter 32/1050] R0[31/150], Temp: 0.8983, Energy: -56.954445+0.000906j
[2025-08-23 00:34:59] [Iter 33/1050] R0[32/150], Temp: 0.8918, Energy: -56.984926+0.002348j
[2025-08-23 00:35:04] [Iter 34/1050] R0[33/150], Temp: 0.8853, Energy: -56.995397-0.000136j
[2025-08-23 00:35:08] [Iter 35/1050] R0[34/150], Temp: 0.8785, Energy: -56.843411+0.002299j
[2025-08-23 00:35:12] [Iter 36/1050] R0[35/150], Temp: 0.8716, Energy: -56.970887+0.000314j
[2025-08-23 00:35:17] [Iter 37/1050] R0[36/150], Temp: 0.8645, Energy: -56.882867+0.002029j
[2025-08-23 00:35:21] [Iter 38/1050] R0[37/150], Temp: 0.8572, Energy: -56.979867+0.004126j
[2025-08-23 00:35:25] [Iter 39/1050] R0[38/150], Temp: 0.8498, Energy: -56.864441-0.002567j
[2025-08-23 00:35:29] [Iter 40/1050] R0[39/150], Temp: 0.8423, Energy: -56.799229-0.001453j
[2025-08-23 00:35:34] [Iter 41/1050] R0[40/150], Temp: 0.8346, Energy: -56.934092-0.001314j
[2025-08-23 00:35:38] [Iter 42/1050] R0[41/150], Temp: 0.8267, Energy: -57.084795-0.002773j
[2025-08-23 00:35:42] [Iter 43/1050] R0[42/150], Temp: 0.8187, Energy: -57.084443+0.002039j
[2025-08-23 00:35:47] [Iter 44/1050] R0[43/150], Temp: 0.8106, Energy: -57.158056+0.000234j
[2025-08-23 00:35:51] [Iter 45/1050] R0[44/150], Temp: 0.8023, Energy: -57.153539+0.001155j
[2025-08-23 00:35:55] [Iter 46/1050] R0[45/150], Temp: 0.7939, Energy: -56.996377+0.002411j
[2025-08-23 00:36:00] [Iter 47/1050] R0[46/150], Temp: 0.7854, Energy: -57.164978-0.006093j
[2025-08-23 00:36:04] [Iter 48/1050] R0[47/150], Temp: 0.7767, Energy: -57.088495+0.001376j
[2025-08-23 00:36:08] [Iter 49/1050] R0[48/150], Temp: 0.7679, Energy: -56.950111-0.003013j
[2025-08-23 00:36:13] [Iter 50/1050] R0[49/150], Temp: 0.7590, Energy: -56.926682-0.001790j
[2025-08-23 00:36:17] [Iter 51/1050] R0[50/150], Temp: 0.7500, Energy: -57.029708-0.002976j
[2025-08-23 00:36:21] [Iter 52/1050] R0[51/150], Temp: 0.7409, Energy: -57.092735-0.000990j
[2025-08-23 00:36:26] [Iter 53/1050] R0[52/150], Temp: 0.7316, Energy: -56.990187+0.001603j
[2025-08-23 00:36:30] [Iter 54/1050] R0[53/150], Temp: 0.7223, Energy: -56.953109+0.001795j
[2025-08-23 00:36:34] [Iter 55/1050] R0[54/150], Temp: 0.7129, Energy: -56.857653+0.003631j
[2025-08-23 00:36:38] [Iter 56/1050] R0[55/150], Temp: 0.7034, Energy: -56.959890-0.002143j
[2025-08-23 00:36:43] [Iter 57/1050] R0[56/150], Temp: 0.6938, Energy: -56.945902+0.000584j
[2025-08-23 00:36:47] [Iter 58/1050] R0[57/150], Temp: 0.6841, Energy: -56.887614+0.000307j
[2025-08-23 00:36:51] [Iter 59/1050] R0[58/150], Temp: 0.6743, Energy: -56.945493-0.000451j
[2025-08-23 00:36:56] [Iter 60/1050] R0[59/150], Temp: 0.6644, Energy: -56.994778+0.000346j
[2025-08-23 00:37:00] [Iter 61/1050] R0[60/150], Temp: 0.6545, Energy: -56.906348-0.003140j
[2025-08-23 00:37:04] [Iter 62/1050] R0[61/150], Temp: 0.6445, Energy: -56.981023-0.004983j
[2025-08-23 00:37:09] [Iter 63/1050] R0[62/150], Temp: 0.6345, Energy: -57.004626+0.000860j
[2025-08-23 00:37:13] [Iter 64/1050] R0[63/150], Temp: 0.6243, Energy: -56.994136+0.001598j
[2025-08-23 00:37:17] [Iter 65/1050] R0[64/150], Temp: 0.6142, Energy: -56.904233+0.001091j
[2025-08-23 00:37:22] [Iter 66/1050] R0[65/150], Temp: 0.6040, Energy: -57.025568+0.001727j
[2025-08-23 00:37:26] [Iter 67/1050] R0[66/150], Temp: 0.5937, Energy: -57.103510+0.000018j
[2025-08-23 00:37:30] [Iter 68/1050] R0[67/150], Temp: 0.5834, Energy: -57.087207+0.005950j
[2025-08-23 00:37:35] [Iter 69/1050] R0[68/150], Temp: 0.5730, Energy: -57.047817-0.000719j
[2025-08-23 00:37:39] [Iter 70/1050] R0[69/150], Temp: 0.5627, Energy: -57.030037-0.001136j
[2025-08-23 00:37:43] [Iter 71/1050] R0[70/150], Temp: 0.5523, Energy: -56.940204+0.001838j
[2025-08-23 00:37:47] [Iter 72/1050] R0[71/150], Temp: 0.5418, Energy: -56.974030-0.004194j
[2025-08-23 00:37:52] [Iter 73/1050] R0[72/150], Temp: 0.5314, Energy: -56.945254+0.000512j
[2025-08-23 00:37:56] [Iter 74/1050] R0[73/150], Temp: 0.5209, Energy: -57.045225+0.001457j
[2025-08-23 00:38:00] [Iter 75/1050] R0[74/150], Temp: 0.5105, Energy: -57.049538-0.003337j
[2025-08-23 00:38:05] [Iter 76/1050] R0[75/150], Temp: 0.5000, Energy: -57.206712-0.006921j
[2025-08-23 00:38:09] [Iter 77/1050] R0[76/150], Temp: 0.4895, Energy: -57.026921-0.001130j
[2025-08-23 00:38:13] [Iter 78/1050] R0[77/150], Temp: 0.4791, Energy: -57.022632-0.004020j
[2025-08-23 00:38:18] [Iter 79/1050] R0[78/150], Temp: 0.4686, Energy: -57.014966+0.001237j
[2025-08-23 00:38:22] [Iter 80/1050] R0[79/150], Temp: 0.4582, Energy: -57.037440-0.004353j
[2025-08-23 00:38:26] [Iter 81/1050] R0[80/150], Temp: 0.4477, Energy: -57.120688-0.001552j
[2025-08-23 00:38:31] [Iter 82/1050] R0[81/150], Temp: 0.4373, Energy: -57.140428-0.004459j
[2025-08-23 00:38:35] [Iter 83/1050] R0[82/150], Temp: 0.4270, Energy: -57.114667+0.000828j
[2025-08-23 00:38:39] [Iter 84/1050] R0[83/150], Temp: 0.4166, Energy: -57.038457+0.001234j
[2025-08-23 00:38:44] [Iter 85/1050] R0[84/150], Temp: 0.4063, Energy: -56.937946+0.000016j
[2025-08-23 00:38:48] [Iter 86/1050] R0[85/150], Temp: 0.3960, Energy: -56.926887+0.002214j
[2025-08-23 00:38:52] [Iter 87/1050] R0[86/150], Temp: 0.3858, Energy: -56.867015+0.003495j
[2025-08-23 00:38:57] [Iter 88/1050] R0[87/150], Temp: 0.3757, Energy: -56.973806-0.005504j
[2025-08-23 00:39:01] [Iter 89/1050] R0[88/150], Temp: 0.3655, Energy: -56.997306-0.003149j
[2025-08-23 00:39:05] [Iter 90/1050] R0[89/150], Temp: 0.3555, Energy: -57.087836-0.000228j
[2025-08-23 00:39:09] [Iter 91/1050] R0[90/150], Temp: 0.3455, Energy: -56.912310+0.001965j
[2025-08-23 00:39:14] [Iter 92/1050] R0[91/150], Temp: 0.3356, Energy: -56.954119+0.000230j
[2025-08-23 00:39:18] [Iter 93/1050] R0[92/150], Temp: 0.3257, Energy: -57.002570-0.004967j
[2025-08-23 00:39:22] [Iter 94/1050] R0[93/150], Temp: 0.3159, Energy: -57.016073-0.000709j
[2025-08-23 00:39:27] [Iter 95/1050] R0[94/150], Temp: 0.3062, Energy: -57.070200-0.000442j
[2025-08-23 00:39:31] [Iter 96/1050] R0[95/150], Temp: 0.2966, Energy: -57.023569+0.001711j
[2025-08-23 00:39:35] [Iter 97/1050] R0[96/150], Temp: 0.2871, Energy: -56.888251+0.001219j
[2025-08-23 00:39:40] [Iter 98/1050] R0[97/150], Temp: 0.2777, Energy: -57.133021+0.002048j
[2025-08-23 00:39:44] [Iter 99/1050] R0[98/150], Temp: 0.2684, Energy: -56.976140+0.000046j
[2025-08-23 00:39:48] [Iter 100/1050] R0[99/150], Temp: 0.2591, Energy: -56.946141-0.000406j
[2025-08-23 00:39:48] ✓ Checkpoint saved: checkpoint_iter_000100.pkl
[2025-08-23 00:39:53] [Iter 101/1050] R0[100/150], Temp: 0.2500, Energy: -57.013134-0.002365j
[2025-08-23 00:39:57] [Iter 102/1050] R0[101/150], Temp: 0.2410, Energy: -56.997448+0.000097j
[2025-08-23 00:40:01] [Iter 103/1050] R0[102/150], Temp: 0.2321, Energy: -56.993980-0.000268j
[2025-08-23 00:40:06] [Iter 104/1050] R0[103/150], Temp: 0.2233, Energy: -57.055849+0.000680j
[2025-08-23 00:40:10] [Iter 105/1050] R0[104/150], Temp: 0.2146, Energy: -57.032877+0.000634j
[2025-08-23 00:40:14] [Iter 106/1050] R0[105/150], Temp: 0.2061, Energy: -57.073351-0.004212j
[2025-08-23 00:40:18] [Iter 107/1050] R0[106/150], Temp: 0.1977, Energy: -56.982343+0.001616j
[2025-08-23 00:40:23] [Iter 108/1050] R0[107/150], Temp: 0.1894, Energy: -57.091236-0.000574j
[2025-08-23 00:40:27] [Iter 109/1050] R0[108/150], Temp: 0.1813, Energy: -56.967043+0.006946j
[2025-08-23 00:40:31] [Iter 110/1050] R0[109/150], Temp: 0.1733, Energy: -57.002177-0.004389j
[2025-08-23 00:40:36] [Iter 111/1050] R0[110/150], Temp: 0.1654, Energy: -56.859488+0.003215j
[2025-08-23 00:40:40] [Iter 112/1050] R0[111/150], Temp: 0.1577, Energy: -56.967160-0.001166j
[2025-08-23 00:40:44] [Iter 113/1050] R0[112/150], Temp: 0.1502, Energy: -57.063545-0.000216j
[2025-08-23 00:40:49] [Iter 114/1050] R0[113/150], Temp: 0.1428, Energy: -57.017784-0.000532j
[2025-08-23 00:40:53] [Iter 115/1050] R0[114/150], Temp: 0.1355, Energy: -57.150832-0.001147j
[2025-08-23 00:40:57] [Iter 116/1050] R0[115/150], Temp: 0.1284, Energy: -57.095471-0.001676j
[2025-08-23 00:41:02] [Iter 117/1050] R0[116/150], Temp: 0.1215, Energy: -57.047293+0.000787j
[2025-08-23 00:41:06] [Iter 118/1050] R0[117/150], Temp: 0.1147, Energy: -56.996020+0.003914j
[2025-08-23 00:41:10] [Iter 119/1050] R0[118/150], Temp: 0.1082, Energy: -56.954104-0.001049j
[2025-08-23 00:41:15] [Iter 120/1050] R0[119/150], Temp: 0.1017, Energy: -56.926723-0.001036j
[2025-08-23 00:41:19] [Iter 121/1050] R0[120/150], Temp: 0.0955, Energy: -56.841368-0.003293j
[2025-08-23 00:41:23] [Iter 122/1050] R0[121/150], Temp: 0.0894, Energy: -56.993845+0.000501j
[2025-08-23 00:41:27] [Iter 123/1050] R0[122/150], Temp: 0.0835, Energy: -56.984471+0.000794j
[2025-08-23 00:41:32] [Iter 124/1050] R0[123/150], Temp: 0.0778, Energy: -57.033790+0.000122j
[2025-08-23 00:41:36] [Iter 125/1050] R0[124/150], Temp: 0.0723, Energy: -57.128209+0.002215j
[2025-08-23 00:41:40] [Iter 126/1050] R0[125/150], Temp: 0.0670, Energy: -56.845084+0.001955j
[2025-08-23 00:41:45] [Iter 127/1050] R0[126/150], Temp: 0.0618, Energy: -56.899095+0.003148j
[2025-08-23 00:41:49] [Iter 128/1050] R0[127/150], Temp: 0.0569, Energy: -56.947729-0.004505j
[2025-08-23 00:41:53] [Iter 129/1050] R0[128/150], Temp: 0.0521, Energy: -57.048978+0.000115j
[2025-08-23 00:41:58] [Iter 130/1050] R0[129/150], Temp: 0.0476, Energy: -56.987513+0.002807j
[2025-08-23 00:42:02] [Iter 131/1050] R0[130/150], Temp: 0.0432, Energy: -56.920697+0.001631j
[2025-08-23 00:42:06] [Iter 132/1050] R0[131/150], Temp: 0.0391, Energy: -57.022795+0.003878j
[2025-08-23 00:42:11] [Iter 133/1050] R0[132/150], Temp: 0.0351, Energy: -56.948143+0.001006j
[2025-08-23 00:42:15] [Iter 134/1050] R0[133/150], Temp: 0.0314, Energy: -57.065083-0.000395j
[2025-08-23 00:42:19] [Iter 135/1050] R0[134/150], Temp: 0.0278, Energy: -57.086436+0.002819j
[2025-08-23 00:42:24] [Iter 136/1050] R0[135/150], Temp: 0.0245, Energy: -57.052676+0.003403j
[2025-08-23 00:42:28] [Iter 137/1050] R0[136/150], Temp: 0.0213, Energy: -57.118447+0.000096j
[2025-08-23 00:42:32] [Iter 138/1050] R0[137/150], Temp: 0.0184, Energy: -57.073420+0.003198j
[2025-08-23 00:42:37] [Iter 139/1050] R0[138/150], Temp: 0.0157, Energy: -57.064786+0.002292j
[2025-08-23 00:42:41] [Iter 140/1050] R0[139/150], Temp: 0.0132, Energy: -56.957403-0.000566j
[2025-08-23 00:42:45] [Iter 141/1050] R0[140/150], Temp: 0.0109, Energy: -57.005711+0.000039j
[2025-08-23 00:42:49] [Iter 142/1050] R0[141/150], Temp: 0.0089, Energy: -57.037417+0.003873j
[2025-08-23 00:42:54] [Iter 143/1050] R0[142/150], Temp: 0.0070, Energy: -57.055529+0.001670j
[2025-08-23 00:42:58] [Iter 144/1050] R0[143/150], Temp: 0.0054, Energy: -57.027898-0.000201j
[2025-08-23 00:43:02] [Iter 145/1050] R0[144/150], Temp: 0.0039, Energy: -57.168312+0.002153j
[2025-08-23 00:43:07] [Iter 146/1050] R0[145/150], Temp: 0.0027, Energy: -57.100797+0.003113j
[2025-08-23 00:43:11] [Iter 147/1050] R0[146/150], Temp: 0.0018, Energy: -57.086034+0.003791j
[2025-08-23 00:43:15] [Iter 148/1050] R0[147/150], Temp: 0.0010, Energy: -57.131983-0.001157j
[2025-08-23 00:43:20] [Iter 149/1050] R0[148/150], Temp: 0.0004, Energy: -57.029420-0.001921j
[2025-08-23 00:43:24] [Iter 150/1050] R0[149/150], Temp: 0.0001, Energy: -56.938273+0.001337j
[2025-08-23 00:43:24] RESTART #1 | Period: 300
[2025-08-23 00:43:28] [Iter 151/1050] R1[0/300], Temp: 1.0000, Energy: -56.927293-0.000398j
[2025-08-23 00:43:33] [Iter 152/1050] R1[1/300], Temp: 1.0000, Energy: -56.868784-0.002893j
[2025-08-23 00:43:37] [Iter 153/1050] R1[2/300], Temp: 0.9999, Energy: -56.760522+0.000283j
[2025-08-23 00:43:41] [Iter 154/1050] R1[3/300], Temp: 0.9998, Energy: -56.901864-0.003285j
[2025-08-23 00:43:45] [Iter 155/1050] R1[4/300], Temp: 0.9996, Energy: -57.099583+0.000070j
[2025-08-23 00:43:50] [Iter 156/1050] R1[5/300], Temp: 0.9993, Energy: -56.991941+0.001144j
[2025-08-23 00:43:54] [Iter 157/1050] R1[6/300], Temp: 0.9990, Energy: -56.992069+0.000802j
[2025-08-23 00:43:58] [Iter 158/1050] R1[7/300], Temp: 0.9987, Energy: -56.960527+0.001263j
[2025-08-23 00:44:03] [Iter 159/1050] R1[8/300], Temp: 0.9982, Energy: -56.876184+0.003326j
[2025-08-23 00:44:07] [Iter 160/1050] R1[9/300], Temp: 0.9978, Energy: -56.982369+0.002100j
[2025-08-23 00:44:11] [Iter 161/1050] R1[10/300], Temp: 0.9973, Energy: -57.093116+0.001069j
[2025-08-23 00:44:16] [Iter 162/1050] R1[11/300], Temp: 0.9967, Energy: -57.042876-0.001993j
[2025-08-23 00:44:20] [Iter 163/1050] R1[12/300], Temp: 0.9961, Energy: -56.955224-0.000471j
[2025-08-23 00:44:24] [Iter 164/1050] R1[13/300], Temp: 0.9954, Energy: -57.028727-0.005906j
[2025-08-23 00:44:29] [Iter 165/1050] R1[14/300], Temp: 0.9946, Energy: -57.109693-0.001486j
[2025-08-23 00:44:33] [Iter 166/1050] R1[15/300], Temp: 0.9938, Energy: -57.048304+0.001327j
[2025-08-23 00:44:37] [Iter 167/1050] R1[16/300], Temp: 0.9930, Energy: -57.074456+0.001844j
[2025-08-23 00:44:42] [Iter 168/1050] R1[17/300], Temp: 0.9921, Energy: -57.192363+0.003337j
[2025-08-23 00:44:46] [Iter 169/1050] R1[18/300], Temp: 0.9911, Energy: -57.057556+0.002994j
[2025-08-23 00:44:50] [Iter 170/1050] R1[19/300], Temp: 0.9901, Energy: -56.971712+0.003829j
[2025-08-23 00:44:55] [Iter 171/1050] R1[20/300], Temp: 0.9891, Energy: -56.930073+0.004878j
[2025-08-23 00:44:59] [Iter 172/1050] R1[21/300], Temp: 0.9880, Energy: -56.856523+0.001785j
[2025-08-23 00:45:03] [Iter 173/1050] R1[22/300], Temp: 0.9868, Energy: -56.856476-0.001779j
[2025-08-23 00:45:07] [Iter 174/1050] R1[23/300], Temp: 0.9856, Energy: -56.947925-0.002088j
[2025-08-23 00:45:12] [Iter 175/1050] R1[24/300], Temp: 0.9843, Energy: -56.915131-0.002401j
[2025-08-23 00:45:16] [Iter 176/1050] R1[25/300], Temp: 0.9830, Energy: -56.880942-0.000790j
[2025-08-23 00:45:20] [Iter 177/1050] R1[26/300], Temp: 0.9816, Energy: -56.865610-0.002224j
[2025-08-23 00:45:25] [Iter 178/1050] R1[27/300], Temp: 0.9801, Energy: -56.931872-0.001851j
[2025-08-23 00:45:29] [Iter 179/1050] R1[28/300], Temp: 0.9787, Energy: -57.104465-0.001130j
[2025-08-23 00:45:33] [Iter 180/1050] R1[29/300], Temp: 0.9771, Energy: -57.031340-0.000278j
[2025-08-23 00:45:38] [Iter 181/1050] R1[30/300], Temp: 0.9755, Energy: -57.061402+0.020141j
[2025-08-23 00:45:42] [Iter 182/1050] R1[31/300], Temp: 0.9739, Energy: -56.998104+0.001606j
[2025-08-23 00:45:46] [Iter 183/1050] R1[32/300], Temp: 0.9722, Energy: -56.991445-0.000050j
[2025-08-23 00:45:51] [Iter 184/1050] R1[33/300], Temp: 0.9704, Energy: -57.091278+0.002897j
[2025-08-23 00:45:55] [Iter 185/1050] R1[34/300], Temp: 0.9686, Energy: -56.932832-0.000862j
[2025-08-23 00:45:59] [Iter 186/1050] R1[35/300], Temp: 0.9668, Energy: -56.907502-0.000711j
[2025-08-23 00:46:04] [Iter 187/1050] R1[36/300], Temp: 0.9649, Energy: -56.905210+0.000952j
[2025-08-23 00:46:08] [Iter 188/1050] R1[37/300], Temp: 0.9629, Energy: -56.883321-0.000372j
[2025-08-23 00:46:12] [Iter 189/1050] R1[38/300], Temp: 0.9609, Energy: -56.997815+0.001494j
[2025-08-23 00:46:16] [Iter 190/1050] R1[39/300], Temp: 0.9589, Energy: -56.985069-0.002151j
[2025-08-23 00:46:21] [Iter 191/1050] R1[40/300], Temp: 0.9568, Energy: -57.057327-0.001479j
[2025-08-23 00:46:25] [Iter 192/1050] R1[41/300], Temp: 0.9546, Energy: -57.029607-0.002664j
[2025-08-23 00:46:29] [Iter 193/1050] R1[42/300], Temp: 0.9524, Energy: -57.070690-0.001317j
[2025-08-23 00:46:34] [Iter 194/1050] R1[43/300], Temp: 0.9502, Energy: -57.007064+0.000522j
[2025-08-23 00:46:38] [Iter 195/1050] R1[44/300], Temp: 0.9479, Energy: -57.059069+0.000236j
[2025-08-23 00:46:42] [Iter 196/1050] R1[45/300], Temp: 0.9455, Energy: -57.159764+0.002394j
[2025-08-23 00:46:47] [Iter 197/1050] R1[46/300], Temp: 0.9431, Energy: -57.130334-0.000386j
[2025-08-23 00:46:51] [Iter 198/1050] R1[47/300], Temp: 0.9407, Energy: -57.015533+0.001477j
[2025-08-23 00:46:55] [Iter 199/1050] R1[48/300], Temp: 0.9382, Energy: -57.148469-0.002176j
[2025-08-23 00:47:00] [Iter 200/1050] R1[49/300], Temp: 0.9356, Energy: -57.118870-0.000992j
[2025-08-23 00:47:00] ✓ Checkpoint saved: checkpoint_iter_000200.pkl
[2025-08-23 00:47:04] [Iter 201/1050] R1[50/300], Temp: 0.9330, Energy: -57.055777-0.000126j
[2025-08-23 00:47:08] [Iter 202/1050] R1[51/300], Temp: 0.9304, Energy: -56.976792+0.002574j
[2025-08-23 00:47:13] [Iter 203/1050] R1[52/300], Temp: 0.9277, Energy: -57.066377-0.000167j
[2025-08-23 00:47:17] [Iter 204/1050] R1[53/300], Temp: 0.9249, Energy: -57.010645-0.000680j
[2025-08-23 00:47:21] [Iter 205/1050] R1[54/300], Temp: 0.9222, Energy: -57.044947-0.000045j
[2025-08-23 00:47:25] [Iter 206/1050] R1[55/300], Temp: 0.9193, Energy: -57.033515+0.003328j
[2025-08-23 00:47:30] [Iter 207/1050] R1[56/300], Temp: 0.9165, Energy: -57.034550+0.000628j
[2025-08-23 00:47:34] [Iter 208/1050] R1[57/300], Temp: 0.9135, Energy: -56.925461+0.001007j
[2025-08-23 00:47:38] [Iter 209/1050] R1[58/300], Temp: 0.9106, Energy: -56.967543+0.003504j
[2025-08-23 00:47:43] [Iter 210/1050] R1[59/300], Temp: 0.9076, Energy: -57.060485+0.000734j
[2025-08-23 00:47:47] [Iter 211/1050] R1[60/300], Temp: 0.9045, Energy: -57.109883+0.001097j
[2025-08-23 00:47:51] [Iter 212/1050] R1[61/300], Temp: 0.9014, Energy: -56.946698+0.001114j
[2025-08-23 00:47:56] [Iter 213/1050] R1[62/300], Temp: 0.8983, Energy: -57.131234+0.001111j
[2025-08-23 00:48:00] [Iter 214/1050] R1[63/300], Temp: 0.8951, Energy: -56.911951-0.003349j
[2025-08-23 00:48:04] [Iter 215/1050] R1[64/300], Temp: 0.8918, Energy: -56.828539-0.000098j
[2025-08-23 00:48:09] [Iter 216/1050] R1[65/300], Temp: 0.8886, Energy: -56.948823-0.002154j
[2025-08-23 00:48:13] [Iter 217/1050] R1[66/300], Temp: 0.8853, Energy: -57.060393-0.003570j
[2025-08-23 00:48:17] [Iter 218/1050] R1[67/300], Temp: 0.8819, Energy: -57.096996+0.001294j
[2025-08-23 00:48:22] [Iter 219/1050] R1[68/300], Temp: 0.8785, Energy: -56.976147-0.002928j
[2025-08-23 00:48:26] [Iter 220/1050] R1[69/300], Temp: 0.8751, Energy: -57.012713-0.003660j
[2025-08-23 00:48:30] [Iter 221/1050] R1[70/300], Temp: 0.8716, Energy: -57.113104-0.002066j
[2025-08-23 00:48:35] [Iter 222/1050] R1[71/300], Temp: 0.8680, Energy: -57.133848-0.000516j
[2025-08-23 00:48:39] [Iter 223/1050] R1[72/300], Temp: 0.8645, Energy: -57.083241-0.004030j
[2025-08-23 00:48:43] [Iter 224/1050] R1[73/300], Temp: 0.8609, Energy: -57.040366-0.001413j
[2025-08-23 00:48:47] [Iter 225/1050] R1[74/300], Temp: 0.8572, Energy: -56.947565+0.003270j
[2025-08-23 00:48:52] [Iter 226/1050] R1[75/300], Temp: 0.8536, Energy: -57.009610+0.000584j
[2025-08-23 00:48:56] [Iter 227/1050] R1[76/300], Temp: 0.8498, Energy: -57.004324-0.003386j
[2025-08-23 00:49:00] [Iter 228/1050] R1[77/300], Temp: 0.8461, Energy: -56.962586-0.000325j
[2025-08-23 00:49:05] [Iter 229/1050] R1[78/300], Temp: 0.8423, Energy: -57.084757+0.003131j
[2025-08-23 00:49:09] [Iter 230/1050] R1[79/300], Temp: 0.8384, Energy: -57.125357+0.003149j
[2025-08-23 00:49:13] [Iter 231/1050] R1[80/300], Temp: 0.8346, Energy: -57.030059+0.001619j
[2025-08-23 00:49:18] [Iter 232/1050] R1[81/300], Temp: 0.8307, Energy: -57.109074+0.003141j
[2025-08-23 00:49:22] [Iter 233/1050] R1[82/300], Temp: 0.8267, Energy: -56.964788+0.001799j
[2025-08-23 00:49:26] [Iter 234/1050] R1[83/300], Temp: 0.8227, Energy: -57.008565+0.000851j
[2025-08-23 00:49:31] [Iter 235/1050] R1[84/300], Temp: 0.8187, Energy: -56.996384+0.002235j
[2025-08-23 00:49:35] [Iter 236/1050] R1[85/300], Temp: 0.8147, Energy: -57.052049-0.001386j
[2025-08-23 00:49:39] [Iter 237/1050] R1[86/300], Temp: 0.8106, Energy: -57.026256-0.003582j
[2025-08-23 00:49:44] [Iter 238/1050] R1[87/300], Temp: 0.8065, Energy: -57.019978+0.000107j
[2025-08-23 00:49:48] [Iter 239/1050] R1[88/300], Temp: 0.8023, Energy: -57.067015-0.000410j
[2025-08-23 00:49:52] [Iter 240/1050] R1[89/300], Temp: 0.7981, Energy: -57.156364+0.004340j
[2025-08-23 00:49:56] [Iter 241/1050] R1[90/300], Temp: 0.7939, Energy: -57.079760+0.003304j
[2025-08-23 00:50:01] [Iter 242/1050] R1[91/300], Temp: 0.7896, Energy: -56.951076-0.004026j
[2025-08-23 00:50:05] [Iter 243/1050] R1[92/300], Temp: 0.7854, Energy: -57.052834-0.003107j
[2025-08-23 00:50:09] [Iter 244/1050] R1[93/300], Temp: 0.7810, Energy: -57.079380+0.001216j
[2025-08-23 00:50:14] [Iter 245/1050] R1[94/300], Temp: 0.7767, Energy: -57.024288+0.002839j
[2025-08-23 00:50:18] [Iter 246/1050] R1[95/300], Temp: 0.7723, Energy: -57.019108+0.002096j
[2025-08-23 00:50:22] [Iter 247/1050] R1[96/300], Temp: 0.7679, Energy: -56.994655-0.001884j
[2025-08-23 00:50:27] [Iter 248/1050] R1[97/300], Temp: 0.7635, Energy: -57.030924+0.000489j
[2025-08-23 00:50:31] [Iter 249/1050] R1[98/300], Temp: 0.7590, Energy: -57.013110+0.003277j
[2025-08-23 00:50:35] [Iter 250/1050] R1[99/300], Temp: 0.7545, Energy: -57.029275+0.004860j
[2025-08-23 00:50:40] [Iter 251/1050] R1[100/300], Temp: 0.7500, Energy: -57.021046-0.003125j
[2025-08-23 00:50:44] [Iter 252/1050] R1[101/300], Temp: 0.7455, Energy: -56.976781-0.000268j
[2025-08-23 00:50:48] [Iter 253/1050] R1[102/300], Temp: 0.7409, Energy: -57.107455-0.002171j
[2025-08-23 00:50:53] [Iter 254/1050] R1[103/300], Temp: 0.7363, Energy: -57.023834-0.000169j
[2025-08-23 00:50:57] [Iter 255/1050] R1[104/300], Temp: 0.7316, Energy: -56.962552-0.000581j
[2025-08-23 00:51:01] [Iter 256/1050] R1[105/300], Temp: 0.7270, Energy: -57.080305+0.001354j
[2025-08-23 00:51:06] [Iter 257/1050] R1[106/300], Temp: 0.7223, Energy: -56.942248+0.005129j
[2025-08-23 00:51:10] [Iter 258/1050] R1[107/300], Temp: 0.7176, Energy: -56.910022-0.001634j
[2025-08-23 00:51:14] [Iter 259/1050] R1[108/300], Temp: 0.7129, Energy: -56.936109-0.004703j
[2025-08-23 00:51:18] [Iter 260/1050] R1[109/300], Temp: 0.7081, Energy: -56.903059-0.004323j
[2025-08-23 00:51:23] [Iter 261/1050] R1[110/300], Temp: 0.7034, Energy: -57.096043-0.001395j
[2025-08-23 00:51:27] [Iter 262/1050] R1[111/300], Temp: 0.6986, Energy: -57.158543+0.000674j
[2025-08-23 00:51:31] [Iter 263/1050] R1[112/300], Temp: 0.6938, Energy: -57.243012-0.002023j
[2025-08-23 00:51:36] [Iter 264/1050] R1[113/300], Temp: 0.6889, Energy: -57.029080-0.000943j
[2025-08-23 00:51:40] [Iter 265/1050] R1[114/300], Temp: 0.6841, Energy: -57.071256+0.002299j
[2025-08-23 00:51:44] [Iter 266/1050] R1[115/300], Temp: 0.6792, Energy: -57.069845-0.001203j
[2025-08-23 00:51:49] [Iter 267/1050] R1[116/300], Temp: 0.6743, Energy: -57.085563+0.001540j
[2025-08-23 00:51:53] [Iter 268/1050] R1[117/300], Temp: 0.6694, Energy: -57.094310-0.001890j
[2025-08-23 00:51:57] [Iter 269/1050] R1[118/300], Temp: 0.6644, Energy: -57.049913+0.002658j
[2025-08-23 00:52:02] [Iter 270/1050] R1[119/300], Temp: 0.6595, Energy: -57.038242+0.001875j
[2025-08-23 00:52:06] [Iter 271/1050] R1[120/300], Temp: 0.6545, Energy: -57.010563+0.003049j
[2025-08-23 00:52:10] [Iter 272/1050] R1[121/300], Temp: 0.6495, Energy: -57.153311-0.000040j
[2025-08-23 00:52:15] [Iter 273/1050] R1[122/300], Temp: 0.6445, Energy: -57.221736+0.003860j
[2025-08-23 00:52:19] [Iter 274/1050] R1[123/300], Temp: 0.6395, Energy: -57.036743+0.004500j
[2025-08-23 00:52:23] [Iter 275/1050] R1[124/300], Temp: 0.6345, Energy: -57.248059-0.003333j
[2025-08-23 00:52:28] [Iter 276/1050] R1[125/300], Temp: 0.6294, Energy: -57.067172+0.000384j
[2025-08-23 00:52:32] [Iter 277/1050] R1[126/300], Temp: 0.6243, Energy: -57.126929+0.002629j
[2025-08-23 00:52:36] [Iter 278/1050] R1[127/300], Temp: 0.6193, Energy: -57.104278+0.006625j
[2025-08-23 00:52:40] [Iter 279/1050] R1[128/300], Temp: 0.6142, Energy: -57.080145+0.006231j
[2025-08-23 00:52:45] [Iter 280/1050] R1[129/300], Temp: 0.6091, Energy: -56.875411-0.001423j
[2025-08-23 00:52:49] [Iter 281/1050] R1[130/300], Temp: 0.6040, Energy: -56.898225+0.001286j
[2025-08-23 00:52:53] [Iter 282/1050] R1[131/300], Temp: 0.5988, Energy: -56.964987+0.001154j
[2025-08-23 00:52:58] [Iter 283/1050] R1[132/300], Temp: 0.5937, Energy: -57.005085+0.003676j
[2025-08-23 00:53:02] [Iter 284/1050] R1[133/300], Temp: 0.5885, Energy: -56.957609+0.000884j
[2025-08-23 00:53:06] [Iter 285/1050] R1[134/300], Temp: 0.5834, Energy: -56.834590-0.000506j
[2025-08-23 00:53:11] [Iter 286/1050] R1[135/300], Temp: 0.5782, Energy: -56.976858+0.000858j
[2025-08-23 00:53:15] [Iter 287/1050] R1[136/300], Temp: 0.5730, Energy: -57.049543-0.001433j
[2025-08-23 00:53:19] [Iter 288/1050] R1[137/300], Temp: 0.5679, Energy: -57.085048-0.001770j
[2025-08-23 00:53:24] [Iter 289/1050] R1[138/300], Temp: 0.5627, Energy: -57.103861+0.000374j
[2025-08-23 00:53:28] [Iter 290/1050] R1[139/300], Temp: 0.5575, Energy: -57.060444+0.000785j
[2025-08-23 00:53:32] [Iter 291/1050] R1[140/300], Temp: 0.5523, Energy: -56.985600+0.002577j
[2025-08-23 00:53:37] [Iter 292/1050] R1[141/300], Temp: 0.5471, Energy: -56.887996-0.001910j
[2025-08-23 00:53:41] [Iter 293/1050] R1[142/300], Temp: 0.5418, Energy: -56.949567+0.002904j
[2025-08-23 00:53:45] [Iter 294/1050] R1[143/300], Temp: 0.5366, Energy: -56.941681-0.001560j
[2025-08-23 00:53:49] [Iter 295/1050] R1[144/300], Temp: 0.5314, Energy: -56.889451+0.004554j
[2025-08-23 00:53:54] [Iter 296/1050] R1[145/300], Temp: 0.5262, Energy: -56.986131+0.002249j
[2025-08-23 00:53:58] [Iter 297/1050] R1[146/300], Temp: 0.5209, Energy: -57.087259+0.000495j
[2025-08-23 00:54:02] [Iter 298/1050] R1[147/300], Temp: 0.5157, Energy: -56.987790+0.000676j
[2025-08-23 00:54:07] [Iter 299/1050] R1[148/300], Temp: 0.5105, Energy: -57.116839+0.000877j
[2025-08-23 00:54:11] [Iter 300/1050] R1[149/300], Temp: 0.5052, Energy: -57.081067-0.000706j
[2025-08-23 00:54:11] ✓ Checkpoint saved: checkpoint_iter_000300.pkl
[2025-08-23 00:54:15] [Iter 301/1050] R1[150/300], Temp: 0.5000, Energy: -57.054927+0.001446j
[2025-08-23 00:54:20] [Iter 302/1050] R1[151/300], Temp: 0.4948, Energy: -57.071644-0.001349j
[2025-08-23 00:54:24] [Iter 303/1050] R1[152/300], Temp: 0.4895, Energy: -57.028830-0.000301j
[2025-08-23 00:54:28] [Iter 304/1050] R1[153/300], Temp: 0.4843, Energy: -57.074126+0.001478j
[2025-08-23 00:54:33] [Iter 305/1050] R1[154/300], Temp: 0.4791, Energy: -57.135606-0.001274j
[2025-08-23 00:54:37] [Iter 306/1050] R1[155/300], Temp: 0.4738, Energy: -57.129160+0.001844j
[2025-08-23 00:54:41] [Iter 307/1050] R1[156/300], Temp: 0.4686, Energy: -56.873396-0.000245j
[2025-08-23 00:54:46] [Iter 308/1050] R1[157/300], Temp: 0.4634, Energy: -56.957296-0.000906j
[2025-08-23 00:54:50] [Iter 309/1050] R1[158/300], Temp: 0.4582, Energy: -56.985798-0.002658j
[2025-08-23 00:54:54] [Iter 310/1050] R1[159/300], Temp: 0.4529, Energy: -57.054205-0.002087j
[2025-08-23 00:54:58] [Iter 311/1050] R1[160/300], Temp: 0.4477, Energy: -56.939548+0.000366j
[2025-08-23 00:55:03] [Iter 312/1050] R1[161/300], Temp: 0.4425, Energy: -56.837888+0.000180j
[2025-08-23 00:55:07] [Iter 313/1050] R1[162/300], Temp: 0.4373, Energy: -56.895803-0.003736j
[2025-08-23 00:55:11] [Iter 314/1050] R1[163/300], Temp: 0.4321, Energy: -56.970370-0.001528j
[2025-08-23 00:55:16] [Iter 315/1050] R1[164/300], Temp: 0.4270, Energy: -56.890868+0.000411j
[2025-08-23 00:55:20] [Iter 316/1050] R1[165/300], Temp: 0.4218, Energy: -56.984510+0.003695j
[2025-08-23 00:55:24] [Iter 317/1050] R1[166/300], Temp: 0.4166, Energy: -56.977801+0.000113j
[2025-08-23 00:55:29] [Iter 318/1050] R1[167/300], Temp: 0.4115, Energy: -56.932445+0.000505j
[2025-08-23 00:55:33] [Iter 319/1050] R1[168/300], Temp: 0.4063, Energy: -57.028284+0.000593j
[2025-08-23 00:55:37] [Iter 320/1050] R1[169/300], Temp: 0.4012, Energy: -57.020481-0.000267j
[2025-08-23 00:55:42] [Iter 321/1050] R1[170/300], Temp: 0.3960, Energy: -56.943538+0.004876j
[2025-08-23 00:55:46] [Iter 322/1050] R1[171/300], Temp: 0.3909, Energy: -56.942630-0.000973j
[2025-08-23 00:55:50] [Iter 323/1050] R1[172/300], Temp: 0.3858, Energy: -57.055179+0.000743j
[2025-08-23 00:55:55] [Iter 324/1050] R1[173/300], Temp: 0.3807, Energy: -57.164127+0.000301j
[2025-08-23 00:55:59] [Iter 325/1050] R1[174/300], Temp: 0.3757, Energy: -56.990445-0.002011j
[2025-08-23 00:56:03] [Iter 326/1050] R1[175/300], Temp: 0.3706, Energy: -57.066255+0.000821j
[2025-08-23 00:56:07] [Iter 327/1050] R1[176/300], Temp: 0.3655, Energy: -57.018022-0.000553j
[2025-08-23 00:56:12] [Iter 328/1050] R1[177/300], Temp: 0.3605, Energy: -57.122636-0.000608j
[2025-08-23 00:56:16] [Iter 329/1050] R1[178/300], Temp: 0.3555, Energy: -57.017223-0.005288j
[2025-08-23 00:56:20] [Iter 330/1050] R1[179/300], Temp: 0.3505, Energy: -56.870756-0.003453j
[2025-08-23 00:56:25] [Iter 331/1050] R1[180/300], Temp: 0.3455, Energy: -57.053020+0.001896j
[2025-08-23 00:56:29] [Iter 332/1050] R1[181/300], Temp: 0.3405, Energy: -57.129729+0.001019j
[2025-08-23 00:56:33] [Iter 333/1050] R1[182/300], Temp: 0.3356, Energy: -57.021253+0.000988j
[2025-08-23 00:56:38] [Iter 334/1050] R1[183/300], Temp: 0.3306, Energy: -57.067812-0.000637j
[2025-08-23 00:56:42] [Iter 335/1050] R1[184/300], Temp: 0.3257, Energy: -57.059805-0.002172j
[2025-08-23 00:56:46] [Iter 336/1050] R1[185/300], Temp: 0.3208, Energy: -57.080790+0.002521j
[2025-08-23 00:56:51] [Iter 337/1050] R1[186/300], Temp: 0.3159, Energy: -56.926756+0.001244j
[2025-08-23 00:56:55] [Iter 338/1050] R1[187/300], Temp: 0.3111, Energy: -57.011447-0.004174j
[2025-08-23 00:56:59] [Iter 339/1050] R1[188/300], Temp: 0.3062, Energy: -56.927917-0.001954j
[2025-08-23 00:57:03] [Iter 340/1050] R1[189/300], Temp: 0.3014, Energy: -56.955794-0.000787j
[2025-08-23 00:57:08] [Iter 341/1050] R1[190/300], Temp: 0.2966, Energy: -57.007583+0.004396j
[2025-08-23 00:57:12] [Iter 342/1050] R1[191/300], Temp: 0.2919, Energy: -56.969541-0.002081j
[2025-08-23 00:57:16] [Iter 343/1050] R1[192/300], Temp: 0.2871, Energy: -56.905783+0.003605j
[2025-08-23 00:57:21] [Iter 344/1050] R1[193/300], Temp: 0.2824, Energy: -57.025287+0.000040j
[2025-08-23 00:57:25] [Iter 345/1050] R1[194/300], Temp: 0.2777, Energy: -56.927100+0.001280j
[2025-08-23 00:57:29] [Iter 346/1050] R1[195/300], Temp: 0.2730, Energy: -56.921905-0.003453j
[2025-08-23 00:57:34] [Iter 347/1050] R1[196/300], Temp: 0.2684, Energy: -56.975234-0.000394j
[2025-08-23 00:57:38] [Iter 348/1050] R1[197/300], Temp: 0.2637, Energy: -57.031039-0.002762j
[2025-08-23 00:57:42] [Iter 349/1050] R1[198/300], Temp: 0.2591, Energy: -56.994385-0.000591j
[2025-08-23 00:57:47] [Iter 350/1050] R1[199/300], Temp: 0.2545, Energy: -57.111858+0.002184j
[2025-08-23 00:57:51] [Iter 351/1050] R1[200/300], Temp: 0.2500, Energy: -57.059061+0.001419j
[2025-08-23 00:57:55] [Iter 352/1050] R1[201/300], Temp: 0.2455, Energy: -57.039202+0.000113j
[2025-08-23 00:58:00] [Iter 353/1050] R1[202/300], Temp: 0.2410, Energy: -56.928816-0.006185j
[2025-08-23 00:58:04] [Iter 354/1050] R1[203/300], Temp: 0.2365, Energy: -56.943916+0.002120j
[2025-08-23 00:58:08] [Iter 355/1050] R1[204/300], Temp: 0.2321, Energy: -57.009335+0.004065j
[2025-08-23 00:58:12] [Iter 356/1050] R1[205/300], Temp: 0.2277, Energy: -57.021546+0.002214j
[2025-08-23 00:58:17] [Iter 357/1050] R1[206/300], Temp: 0.2233, Energy: -56.879548+0.002049j
[2025-08-23 00:58:21] [Iter 358/1050] R1[207/300], Temp: 0.2190, Energy: -56.883566-0.005828j
[2025-08-23 00:58:25] [Iter 359/1050] R1[208/300], Temp: 0.2146, Energy: -56.956855-0.000052j
[2025-08-23 00:58:30] [Iter 360/1050] R1[209/300], Temp: 0.2104, Energy: -56.997933-0.001242j
[2025-08-23 00:58:34] [Iter 361/1050] R1[210/300], Temp: 0.2061, Energy: -57.022837+0.001100j
[2025-08-23 00:58:38] [Iter 362/1050] R1[211/300], Temp: 0.2019, Energy: -57.014237-0.003835j
[2025-08-23 00:58:43] [Iter 363/1050] R1[212/300], Temp: 0.1977, Energy: -57.083879-0.000929j
[2025-08-23 00:58:47] [Iter 364/1050] R1[213/300], Temp: 0.1935, Energy: -57.057463+0.004011j
[2025-08-23 00:58:51] [Iter 365/1050] R1[214/300], Temp: 0.1894, Energy: -57.064340+0.004346j
[2025-08-23 00:58:56] [Iter 366/1050] R1[215/300], Temp: 0.1853, Energy: -56.980378-0.002119j
[2025-08-23 00:59:00] [Iter 367/1050] R1[216/300], Temp: 0.1813, Energy: -56.898594+0.001599j
[2025-08-23 00:59:04] [Iter 368/1050] R1[217/300], Temp: 0.1773, Energy: -57.020467+0.002506j
[2025-08-23 00:59:09] [Iter 369/1050] R1[218/300], Temp: 0.1733, Energy: -56.922641+0.000256j
[2025-08-23 00:59:13] [Iter 370/1050] R1[219/300], Temp: 0.1693, Energy: -56.945529-0.000307j
[2025-08-23 00:59:17] [Iter 371/1050] R1[220/300], Temp: 0.1654, Energy: -56.856079+0.001928j
[2025-08-23 00:59:21] [Iter 372/1050] R1[221/300], Temp: 0.1616, Energy: -56.934951+0.004396j
[2025-08-23 00:59:26] [Iter 373/1050] R1[222/300], Temp: 0.1577, Energy: -57.028239+0.002119j
[2025-08-23 00:59:30] [Iter 374/1050] R1[223/300], Temp: 0.1539, Energy: -57.118302-0.001770j
[2025-08-23 00:59:34] [Iter 375/1050] R1[224/300], Temp: 0.1502, Energy: -57.004091+0.003002j
[2025-08-23 00:59:39] [Iter 376/1050] R1[225/300], Temp: 0.1464, Energy: -56.917084+0.003027j
[2025-08-23 00:59:43] [Iter 377/1050] R1[226/300], Temp: 0.1428, Energy: -56.988271-0.001734j
[2025-08-23 00:59:47] [Iter 378/1050] R1[227/300], Temp: 0.1391, Energy: -56.976214+0.001652j
[2025-08-23 00:59:52] [Iter 379/1050] R1[228/300], Temp: 0.1355, Energy: -56.988191-0.003533j
[2025-08-23 00:59:56] [Iter 380/1050] R1[229/300], Temp: 0.1320, Energy: -57.030194-0.004364j
[2025-08-23 01:00:00] [Iter 381/1050] R1[230/300], Temp: 0.1284, Energy: -57.037551-0.001698j
[2025-08-23 01:00:05] [Iter 382/1050] R1[231/300], Temp: 0.1249, Energy: -56.988002+0.004752j
[2025-08-23 01:00:09] [Iter 383/1050] R1[232/300], Temp: 0.1215, Energy: -57.005667-0.001785j
[2025-08-23 01:00:13] [Iter 384/1050] R1[233/300], Temp: 0.1181, Energy: -57.084091-0.002499j
[2025-08-23 01:00:18] [Iter 385/1050] R1[234/300], Temp: 0.1147, Energy: -57.078697-0.001993j
[2025-08-23 01:00:22] [Iter 386/1050] R1[235/300], Temp: 0.1114, Energy: -57.081768+0.000474j
[2025-08-23 01:00:26] [Iter 387/1050] R1[236/300], Temp: 0.1082, Energy: -56.933990+0.002353j
[2025-08-23 01:00:30] [Iter 388/1050] R1[237/300], Temp: 0.1049, Energy: -57.040468+0.000611j
[2025-08-23 01:00:35] [Iter 389/1050] R1[238/300], Temp: 0.1017, Energy: -56.927308-0.001949j
[2025-08-23 01:00:39] [Iter 390/1050] R1[239/300], Temp: 0.0986, Energy: -57.205029+0.001666j
[2025-08-23 01:00:43] [Iter 391/1050] R1[240/300], Temp: 0.0955, Energy: -57.062097-0.001568j
[2025-08-23 01:00:48] [Iter 392/1050] R1[241/300], Temp: 0.0924, Energy: -57.087456+0.001663j
[2025-08-23 01:00:52] [Iter 393/1050] R1[242/300], Temp: 0.0894, Energy: -57.007204+0.001285j
[2025-08-23 01:00:56] [Iter 394/1050] R1[243/300], Temp: 0.0865, Energy: -56.907771-0.001683j
[2025-08-23 01:01:01] [Iter 395/1050] R1[244/300], Temp: 0.0835, Energy: -56.928078-0.000638j
[2025-08-23 01:01:05] [Iter 396/1050] R1[245/300], Temp: 0.0807, Energy: -56.926913-0.000091j
[2025-08-23 01:01:09] [Iter 397/1050] R1[246/300], Temp: 0.0778, Energy: -57.084946-0.000928j
[2025-08-23 01:01:14] [Iter 398/1050] R1[247/300], Temp: 0.0751, Energy: -57.007433+0.003884j
[2025-08-23 01:01:18] [Iter 399/1050] R1[248/300], Temp: 0.0723, Energy: -56.922732-0.000020j
[2025-08-23 01:01:22] [Iter 400/1050] R1[249/300], Temp: 0.0696, Energy: -56.870463+0.001550j
[2025-08-23 01:01:22] ✓ Checkpoint saved: checkpoint_iter_000400.pkl
[2025-08-23 01:01:26] [Iter 401/1050] R1[250/300], Temp: 0.0670, Energy: -56.974883+0.000318j
[2025-08-23 01:01:31] [Iter 402/1050] R1[251/300], Temp: 0.0644, Energy: -56.933488+0.003215j
[2025-08-23 01:01:35] [Iter 403/1050] R1[252/300], Temp: 0.0618, Energy: -56.929544-0.001438j
[2025-08-23 01:01:39] [Iter 404/1050] R1[253/300], Temp: 0.0593, Energy: -57.010201+0.002200j
[2025-08-23 01:01:44] [Iter 405/1050] R1[254/300], Temp: 0.0569, Energy: -56.954200-0.000523j
[2025-08-23 01:01:48] [Iter 406/1050] R1[255/300], Temp: 0.0545, Energy: -57.060133-0.000066j
[2025-08-23 01:01:52] [Iter 407/1050] R1[256/300], Temp: 0.0521, Energy: -56.890479+0.005723j
[2025-08-23 01:01:57] [Iter 408/1050] R1[257/300], Temp: 0.0498, Energy: -57.083241-0.001523j
[2025-08-23 01:02:01] [Iter 409/1050] R1[258/300], Temp: 0.0476, Energy: -56.935058+0.000385j
[2025-08-23 01:02:05] [Iter 410/1050] R1[259/300], Temp: 0.0454, Energy: -56.867297-0.000280j
[2025-08-23 01:02:10] [Iter 411/1050] R1[260/300], Temp: 0.0432, Energy: -57.015036+0.001755j
[2025-08-23 01:02:14] [Iter 412/1050] R1[261/300], Temp: 0.0411, Energy: -57.090145-0.002910j
[2025-08-23 01:02:18] [Iter 413/1050] R1[262/300], Temp: 0.0391, Energy: -57.039345-0.002875j
[2025-08-23 01:02:23] [Iter 414/1050] R1[263/300], Temp: 0.0371, Energy: -56.954639+0.001321j
[2025-08-23 01:02:27] [Iter 415/1050] R1[264/300], Temp: 0.0351, Energy: -56.988723+0.004482j
[2025-08-23 01:02:31] [Iter 416/1050] R1[265/300], Temp: 0.0332, Energy: -56.991703-0.003662j
[2025-08-23 01:02:35] [Iter 417/1050] R1[266/300], Temp: 0.0314, Energy: -57.099977-0.000514j
[2025-08-23 01:02:40] [Iter 418/1050] R1[267/300], Temp: 0.0296, Energy: -57.040021+0.001883j
[2025-08-23 01:02:44] [Iter 419/1050] R1[268/300], Temp: 0.0278, Energy: -57.026247-0.001184j
[2025-08-23 01:02:48] [Iter 420/1050] R1[269/300], Temp: 0.0261, Energy: -57.042764-0.000842j
[2025-08-23 01:02:53] [Iter 421/1050] R1[270/300], Temp: 0.0245, Energy: -57.016229+0.001554j
[2025-08-23 01:02:57] [Iter 422/1050] R1[271/300], Temp: 0.0229, Energy: -56.894618-0.004514j
[2025-08-23 01:03:01] [Iter 423/1050] R1[272/300], Temp: 0.0213, Energy: -56.906368-0.002394j
[2025-08-23 01:03:06] [Iter 424/1050] R1[273/300], Temp: 0.0199, Energy: -56.940372-0.001720j
[2025-08-23 01:03:10] [Iter 425/1050] R1[274/300], Temp: 0.0184, Energy: -57.144008+0.001537j
[2025-08-23 01:03:14] [Iter 426/1050] R1[275/300], Temp: 0.0170, Energy: -57.104650+0.001602j
[2025-08-23 01:03:19] [Iter 427/1050] R1[276/300], Temp: 0.0157, Energy: -56.972176+0.000547j
[2025-08-23 01:03:23] [Iter 428/1050] R1[277/300], Temp: 0.0144, Energy: -57.012727+0.003723j
[2025-08-23 01:03:27] [Iter 429/1050] R1[278/300], Temp: 0.0132, Energy: -57.061817-0.002526j
[2025-08-23 01:03:31] [Iter 430/1050] R1[279/300], Temp: 0.0120, Energy: -57.084206-0.001216j
[2025-08-23 01:03:36] [Iter 431/1050] R1[280/300], Temp: 0.0109, Energy: -57.048238-0.002107j
[2025-08-23 01:03:40] [Iter 432/1050] R1[281/300], Temp: 0.0099, Energy: -57.005649-0.005405j
[2025-08-23 01:03:44] [Iter 433/1050] R1[282/300], Temp: 0.0089, Energy: -57.009966+0.000975j
[2025-08-23 01:03:49] [Iter 434/1050] R1[283/300], Temp: 0.0079, Energy: -56.934009+0.001458j
[2025-08-23 01:03:53] [Iter 435/1050] R1[284/300], Temp: 0.0070, Energy: -57.012388-0.003289j
[2025-08-23 01:03:57] [Iter 436/1050] R1[285/300], Temp: 0.0062, Energy: -56.921783-0.002386j
[2025-08-23 01:04:02] [Iter 437/1050] R1[286/300], Temp: 0.0054, Energy: -57.099616-0.001560j
[2025-08-23 01:04:06] [Iter 438/1050] R1[287/300], Temp: 0.0046, Energy: -57.028577-0.002951j
[2025-08-23 01:04:10] [Iter 439/1050] R1[288/300], Temp: 0.0039, Energy: -56.969235-0.006670j
[2025-08-23 01:04:15] [Iter 440/1050] R1[289/300], Temp: 0.0033, Energy: -56.844149+0.003197j
[2025-08-23 01:04:19] [Iter 441/1050] R1[290/300], Temp: 0.0027, Energy: -56.895573-0.001121j
[2025-08-23 01:04:23] [Iter 442/1050] R1[291/300], Temp: 0.0022, Energy: -57.008318-0.000237j
[2025-08-23 01:04:28] [Iter 443/1050] R1[292/300], Temp: 0.0018, Energy: -57.053548+0.002301j
[2025-08-23 01:04:32] [Iter 444/1050] R1[293/300], Temp: 0.0013, Energy: -56.990191-0.000167j
[2025-08-23 01:04:36] [Iter 445/1050] R1[294/300], Temp: 0.0010, Energy: -56.927854-0.001572j
[2025-08-23 01:04:40] [Iter 446/1050] R1[295/300], Temp: 0.0007, Energy: -56.994314-0.002615j
[2025-08-23 01:04:45] [Iter 447/1050] R1[296/300], Temp: 0.0004, Energy: -57.032693-0.003619j
[2025-08-23 01:04:49] [Iter 448/1050] R1[297/300], Temp: 0.0002, Energy: -56.974566+0.002301j
[2025-08-23 01:04:53] [Iter 449/1050] R1[298/300], Temp: 0.0001, Energy: -56.988265-0.000044j
[2025-08-23 01:04:58] [Iter 450/1050] R1[299/300], Temp: 0.0000, Energy: -57.045115+0.001660j
[2025-08-23 01:04:58] RESTART #2 | Period: 600
[2025-08-23 01:05:02] [Iter 451/1050] R2[0/600], Temp: 1.0000, Energy: -57.045085+0.000390j
[2025-08-23 01:05:06] [Iter 452/1050] R2[1/600], Temp: 1.0000, Energy: -57.033563+0.004654j
[2025-08-23 01:05:11] [Iter 453/1050] R2[2/600], Temp: 1.0000, Energy: -57.065147+0.000506j
[2025-08-23 01:05:15] [Iter 454/1050] R2[3/600], Temp: 0.9999, Energy: -57.118231+0.003136j
[2025-08-23 01:05:19] [Iter 455/1050] R2[4/600], Temp: 0.9999, Energy: -57.061251+0.000901j
[2025-08-23 01:05:24] [Iter 456/1050] R2[5/600], Temp: 0.9998, Energy: -57.026542+0.002493j
[2025-08-23 01:05:28] [Iter 457/1050] R2[6/600], Temp: 0.9998, Energy: -56.943500+0.001908j
[2025-08-23 01:05:32] [Iter 458/1050] R2[7/600], Temp: 0.9997, Energy: -57.079287-0.001174j
[2025-08-23 01:05:37] [Iter 459/1050] R2[8/600], Temp: 0.9996, Energy: -57.032263-0.001792j
[2025-08-23 01:05:41] [Iter 460/1050] R2[9/600], Temp: 0.9994, Energy: -57.007297-0.001345j
[2025-08-23 01:05:45] [Iter 461/1050] R2[10/600], Temp: 0.9993, Energy: -57.115577+0.004933j
[2025-08-23 01:05:49] [Iter 462/1050] R2[11/600], Temp: 0.9992, Energy: -57.094079+0.006503j
[2025-08-23 01:05:54] [Iter 463/1050] R2[12/600], Temp: 0.9990, Energy: -57.011118+0.002522j
[2025-08-23 01:05:58] [Iter 464/1050] R2[13/600], Temp: 0.9988, Energy: -57.138027-0.001553j
[2025-08-23 01:06:02] [Iter 465/1050] R2[14/600], Temp: 0.9987, Energy: -57.014867-0.003601j
[2025-08-23 01:06:07] [Iter 466/1050] R2[15/600], Temp: 0.9985, Energy: -57.048722+0.002903j
[2025-08-23 01:06:11] [Iter 467/1050] R2[16/600], Temp: 0.9982, Energy: -57.090026-0.002523j
[2025-08-23 01:06:15] [Iter 468/1050] R2[17/600], Temp: 0.9980, Energy: -57.049304-0.002091j
[2025-08-23 01:06:20] [Iter 469/1050] R2[18/600], Temp: 0.9978, Energy: -56.957830-0.003054j
[2025-08-23 01:06:24] [Iter 470/1050] R2[19/600], Temp: 0.9975, Energy: -56.994533+0.001783j
[2025-08-23 01:06:28] [Iter 471/1050] R2[20/600], Temp: 0.9973, Energy: -56.925480-0.002928j
[2025-08-23 01:06:33] [Iter 472/1050] R2[21/600], Temp: 0.9970, Energy: -57.112618-0.001150j
[2025-08-23 01:06:37] [Iter 473/1050] R2[22/600], Temp: 0.9967, Energy: -57.105815-0.002976j
[2025-08-23 01:06:41] [Iter 474/1050] R2[23/600], Temp: 0.9964, Energy: -57.079021-0.001857j
[2025-08-23 01:06:46] [Iter 475/1050] R2[24/600], Temp: 0.9961, Energy: -56.989992+0.004222j
[2025-08-23 01:06:50] [Iter 476/1050] R2[25/600], Temp: 0.9957, Energy: -57.050045-0.002532j
[2025-08-23 01:06:54] [Iter 477/1050] R2[26/600], Temp: 0.9954, Energy: -56.943283-0.001293j
[2025-08-23 01:06:58] [Iter 478/1050] R2[27/600], Temp: 0.9950, Energy: -56.905002-0.003467j
[2025-08-23 01:07:03] [Iter 479/1050] R2[28/600], Temp: 0.9946, Energy: -56.986478+0.004033j
[2025-08-23 01:07:07] [Iter 480/1050] R2[29/600], Temp: 0.9942, Energy: -57.059078+0.001963j
[2025-08-23 01:07:11] [Iter 481/1050] R2[30/600], Temp: 0.9938, Energy: -57.014198-0.001095j
[2025-08-23 01:07:16] [Iter 482/1050] R2[31/600], Temp: 0.9934, Energy: -56.987478-0.000437j
[2025-08-23 01:07:20] [Iter 483/1050] R2[32/600], Temp: 0.9930, Energy: -57.019685-0.000905j
[2025-08-23 01:07:24] [Iter 484/1050] R2[33/600], Temp: 0.9926, Energy: -56.958617-0.001274j
[2025-08-23 01:07:29] [Iter 485/1050] R2[34/600], Temp: 0.9921, Energy: -57.124785-0.000643j
[2025-08-23 01:07:33] [Iter 486/1050] R2[35/600], Temp: 0.9916, Energy: -56.942232-0.000824j
[2025-08-23 01:07:37] [Iter 487/1050] R2[36/600], Temp: 0.9911, Energy: -57.012186+0.001326j
[2025-08-23 01:07:42] [Iter 488/1050] R2[37/600], Temp: 0.9906, Energy: -56.915160+0.002705j
[2025-08-23 01:07:46] [Iter 489/1050] R2[38/600], Temp: 0.9901, Energy: -56.975319-0.002482j
[2025-08-23 01:07:50] [Iter 490/1050] R2[39/600], Temp: 0.9896, Energy: -57.069482-0.001377j
[2025-08-23 01:07:55] [Iter 491/1050] R2[40/600], Temp: 0.9891, Energy: -57.053159+0.003176j
[2025-08-23 01:07:59] [Iter 492/1050] R2[41/600], Temp: 0.9885, Energy: -57.005782-0.000978j
[2025-08-23 01:08:03] [Iter 493/1050] R2[42/600], Temp: 0.9880, Energy: -57.125193-0.000693j
[2025-08-23 01:08:07] [Iter 494/1050] R2[43/600], Temp: 0.9874, Energy: -57.099808-0.002077j
[2025-08-23 01:08:12] [Iter 495/1050] R2[44/600], Temp: 0.9868, Energy: -56.977385-0.000248j
[2025-08-23 01:08:16] [Iter 496/1050] R2[45/600], Temp: 0.9862, Energy: -56.955973+0.002952j
[2025-08-23 01:08:20] [Iter 497/1050] R2[46/600], Temp: 0.9856, Energy: -56.862293+0.000475j
[2025-08-23 01:08:25] [Iter 498/1050] R2[47/600], Temp: 0.9849, Energy: -56.969035-0.000805j
[2025-08-23 01:08:29] [Iter 499/1050] R2[48/600], Temp: 0.9843, Energy: -56.959838+0.001729j
[2025-08-23 01:08:33] [Iter 500/1050] R2[49/600], Temp: 0.9836, Energy: -56.982300-0.000460j
[2025-08-23 01:08:33] ✓ Checkpoint saved: checkpoint_iter_000500.pkl
[2025-08-23 01:08:38] [Iter 501/1050] R2[50/600], Temp: 0.9830, Energy: -56.978443-0.000898j
[2025-08-23 01:08:42] [Iter 502/1050] R2[51/600], Temp: 0.9823, Energy: -56.931402+0.001716j
[2025-08-23 01:08:46] [Iter 503/1050] R2[52/600], Temp: 0.9816, Energy: -57.098325+0.001725j
[2025-08-23 01:08:51] [Iter 504/1050] R2[53/600], Temp: 0.9809, Energy: -57.008707-0.003544j
[2025-08-23 01:08:55] [Iter 505/1050] R2[54/600], Temp: 0.9801, Energy: -57.037343+0.000173j
[2025-08-23 01:08:59] [Iter 506/1050] R2[55/600], Temp: 0.9794, Energy: -57.051698+0.001732j
[2025-08-23 01:09:03] [Iter 507/1050] R2[56/600], Temp: 0.9787, Energy: -57.155151-0.001638j
[2025-08-23 01:09:08] [Iter 508/1050] R2[57/600], Temp: 0.9779, Energy: -57.175564-0.000953j
[2025-08-23 01:09:12] [Iter 509/1050] R2[58/600], Temp: 0.9771, Energy: -56.942729-0.003507j
[2025-08-23 01:09:16] [Iter 510/1050] R2[59/600], Temp: 0.9763, Energy: -56.913804-0.001556j
[2025-08-23 01:09:21] [Iter 511/1050] R2[60/600], Temp: 0.9755, Energy: -57.034458-0.005100j
[2025-08-23 01:09:25] [Iter 512/1050] R2[61/600], Temp: 0.9747, Energy: -57.043986+0.003734j
[2025-08-23 01:09:29] [Iter 513/1050] R2[62/600], Temp: 0.9739, Energy: -57.091411+0.000236j
[2025-08-23 01:09:34] [Iter 514/1050] R2[63/600], Temp: 0.9730, Energy: -57.095948-0.001037j
[2025-08-23 01:09:38] [Iter 515/1050] R2[64/600], Temp: 0.9722, Energy: -56.980794-0.000765j
[2025-08-23 01:09:42] [Iter 516/1050] R2[65/600], Temp: 0.9713, Energy: -57.030602-0.002496j
[2025-08-23 01:09:47] [Iter 517/1050] R2[66/600], Temp: 0.9704, Energy: -57.025906+0.000315j
[2025-08-23 01:09:51] [Iter 518/1050] R2[67/600], Temp: 0.9695, Energy: -56.997016+0.001327j
[2025-08-23 01:09:55] [Iter 519/1050] R2[68/600], Temp: 0.9686, Energy: -57.119456-0.002027j
[2025-08-23 01:10:00] [Iter 520/1050] R2[69/600], Temp: 0.9677, Energy: -56.933633+0.001813j
[2025-08-23 01:10:04] [Iter 521/1050] R2[70/600], Temp: 0.9668, Energy: -56.874763-0.000253j
[2025-08-23 01:10:08] [Iter 522/1050] R2[71/600], Temp: 0.9658, Energy: -57.104113-0.004005j
[2025-08-23 01:10:12] [Iter 523/1050] R2[72/600], Temp: 0.9649, Energy: -57.176855-0.000334j
[2025-08-23 01:10:17] [Iter 524/1050] R2[73/600], Temp: 0.9639, Energy: -57.055184+0.001376j
[2025-08-23 01:10:21] [Iter 525/1050] R2[74/600], Temp: 0.9629, Energy: -56.997580+0.003467j
[2025-08-23 01:10:25] [Iter 526/1050] R2[75/600], Temp: 0.9619, Energy: -57.040801-0.000725j
[2025-08-23 01:10:30] [Iter 527/1050] R2[76/600], Temp: 0.9609, Energy: -57.126864-0.004133j
[2025-08-23 01:10:34] [Iter 528/1050] R2[77/600], Temp: 0.9599, Energy: -57.081025-0.001912j
[2025-08-23 01:10:38] [Iter 529/1050] R2[78/600], Temp: 0.9589, Energy: -57.115991-0.004414j
[2025-08-23 01:10:43] [Iter 530/1050] R2[79/600], Temp: 0.9578, Energy: -56.925409+0.005846j
[2025-08-23 01:10:47] [Iter 531/1050] R2[80/600], Temp: 0.9568, Energy: -56.887869-0.001040j
[2025-08-23 01:10:51] [Iter 532/1050] R2[81/600], Temp: 0.9557, Energy: -57.034804-0.004067j
[2025-08-23 01:10:56] [Iter 533/1050] R2[82/600], Temp: 0.9546, Energy: -57.175416+0.000226j
[2025-08-23 01:11:00] [Iter 534/1050] R2[83/600], Temp: 0.9535, Energy: -57.095629-0.001218j
[2025-08-23 01:11:04] [Iter 535/1050] R2[84/600], Temp: 0.9524, Energy: -57.093823+0.000004j
[2025-08-23 01:11:08] [Iter 536/1050] R2[85/600], Temp: 0.9513, Energy: -56.992259+0.006021j
[2025-08-23 01:11:13] [Iter 537/1050] R2[86/600], Temp: 0.9502, Energy: -57.187466+0.001484j
[2025-08-23 01:11:17] [Iter 538/1050] R2[87/600], Temp: 0.9490, Energy: -57.249259+0.000066j
[2025-08-23 01:11:21] [Iter 539/1050] R2[88/600], Temp: 0.9479, Energy: -57.142893-0.001547j
[2025-08-23 01:11:26] [Iter 540/1050] R2[89/600], Temp: 0.9467, Energy: -56.998110-0.002364j
[2025-08-23 01:11:30] [Iter 541/1050] R2[90/600], Temp: 0.9455, Energy: -57.030770+0.002409j
[2025-08-23 01:11:34] [Iter 542/1050] R2[91/600], Temp: 0.9443, Energy: -57.207229-0.003293j
[2025-08-23 01:11:39] [Iter 543/1050] R2[92/600], Temp: 0.9431, Energy: -57.112909+0.000819j
[2025-08-23 01:11:43] [Iter 544/1050] R2[93/600], Temp: 0.9419, Energy: -57.091689-0.003623j
[2025-08-23 01:11:47] [Iter 545/1050] R2[94/600], Temp: 0.9407, Energy: -57.014886+0.000334j
[2025-08-23 01:11:52] [Iter 546/1050] R2[95/600], Temp: 0.9394, Energy: -57.034051-0.003590j
[2025-08-23 01:11:56] [Iter 547/1050] R2[96/600], Temp: 0.9382, Energy: -57.072190-0.001564j
[2025-08-23 01:12:00] [Iter 548/1050] R2[97/600], Temp: 0.9369, Energy: -57.091701-0.000481j
[2025-08-23 01:12:05] [Iter 549/1050] R2[98/600], Temp: 0.9356, Energy: -56.791507-0.004634j
[2025-08-23 01:12:09] [Iter 550/1050] R2[99/600], Temp: 0.9343, Energy: -56.916597-0.000757j
[2025-08-23 01:12:13] [Iter 551/1050] R2[100/600], Temp: 0.9330, Energy: -56.999566-0.000823j
[2025-08-23 01:12:18] [Iter 552/1050] R2[101/600], Temp: 0.9317, Energy: -56.975007-0.001830j
[2025-08-23 01:12:22] [Iter 553/1050] R2[102/600], Temp: 0.9304, Energy: -56.924763+0.002840j
[2025-08-23 01:12:26] [Iter 554/1050] R2[103/600], Temp: 0.9290, Energy: -56.842728+0.001617j
[2025-08-23 01:12:31] [Iter 555/1050] R2[104/600], Temp: 0.9277, Energy: -56.993115-0.001194j
[2025-08-23 01:12:35] [Iter 556/1050] R2[105/600], Temp: 0.9263, Energy: -56.991454-0.000725j
[2025-08-23 01:12:39] [Iter 557/1050] R2[106/600], Temp: 0.9249, Energy: -57.022251-0.000140j
[2025-08-23 01:12:43] [Iter 558/1050] R2[107/600], Temp: 0.9236, Energy: -57.011062-0.000270j
[2025-08-23 01:12:48] [Iter 559/1050] R2[108/600], Temp: 0.9222, Energy: -57.111185-0.000641j
[2025-08-23 01:12:52] [Iter 560/1050] R2[109/600], Temp: 0.9208, Energy: -57.117640+0.002453j
[2025-08-23 01:12:56] [Iter 561/1050] R2[110/600], Temp: 0.9193, Energy: -57.004148-0.002545j
[2025-08-23 01:13:01] [Iter 562/1050] R2[111/600], Temp: 0.9179, Energy: -57.001378+0.002930j
[2025-08-23 01:13:05] [Iter 563/1050] R2[112/600], Temp: 0.9165, Energy: -56.877543-0.002686j
[2025-08-23 01:13:09] [Iter 564/1050] R2[113/600], Temp: 0.9150, Energy: -57.037803-0.000025j
[2025-08-23 01:13:14] [Iter 565/1050] R2[114/600], Temp: 0.9135, Energy: -56.967197+0.000359j
[2025-08-23 01:13:18] [Iter 566/1050] R2[115/600], Temp: 0.9121, Energy: -57.088509-0.002693j
[2025-08-23 01:13:22] [Iter 567/1050] R2[116/600], Temp: 0.9106, Energy: -56.947812-0.002595j
[2025-08-23 01:13:27] [Iter 568/1050] R2[117/600], Temp: 0.9091, Energy: -56.943580+0.000283j
[2025-08-23 01:13:31] [Iter 569/1050] R2[118/600], Temp: 0.9076, Energy: -57.001929-0.003093j
[2025-08-23 01:13:35] [Iter 570/1050] R2[119/600], Temp: 0.9060, Energy: -56.965580-0.001746j
[2025-08-23 01:13:40] [Iter 571/1050] R2[120/600], Temp: 0.9045, Energy: -57.005685-0.001246j
[2025-08-23 01:13:44] [Iter 572/1050] R2[121/600], Temp: 0.9030, Energy: -56.996226+0.005101j
[2025-08-23 01:13:48] [Iter 573/1050] R2[122/600], Temp: 0.9014, Energy: -57.016150+0.000695j
[2025-08-23 01:13:52] [Iter 574/1050] R2[123/600], Temp: 0.8998, Energy: -56.991640+0.005114j
[2025-08-23 01:13:57] [Iter 575/1050] R2[124/600], Temp: 0.8983, Energy: -57.016160-0.001164j
[2025-08-23 01:14:01] [Iter 576/1050] R2[125/600], Temp: 0.8967, Energy: -56.973362-0.000519j
[2025-08-23 01:14:05] [Iter 577/1050] R2[126/600], Temp: 0.8951, Energy: -57.000308+0.000582j
[2025-08-23 01:14:10] [Iter 578/1050] R2[127/600], Temp: 0.8935, Energy: -57.001875+0.001261j
[2025-08-23 01:14:14] [Iter 579/1050] R2[128/600], Temp: 0.8918, Energy: -57.166721-0.004026j
[2025-08-23 01:14:18] [Iter 580/1050] R2[129/600], Temp: 0.8902, Energy: -57.093290+0.001538j
[2025-08-23 01:14:23] [Iter 581/1050] R2[130/600], Temp: 0.8886, Energy: -57.199504+0.004124j
[2025-08-23 01:14:27] [Iter 582/1050] R2[131/600], Temp: 0.8869, Energy: -57.099883-0.001069j
[2025-08-23 01:14:31] [Iter 583/1050] R2[132/600], Temp: 0.8853, Energy: -57.065235+0.001751j
[2025-08-23 01:14:36] [Iter 584/1050] R2[133/600], Temp: 0.8836, Energy: -57.073729-0.004417j
[2025-08-23 01:14:40] [Iter 585/1050] R2[134/600], Temp: 0.8819, Energy: -57.113608-0.000763j
[2025-08-23 01:14:44] [Iter 586/1050] R2[135/600], Temp: 0.8802, Energy: -57.077314+0.000795j
[2025-08-23 01:14:48] [Iter 587/1050] R2[136/600], Temp: 0.8785, Energy: -57.160863+0.001335j
[2025-08-23 01:14:53] [Iter 588/1050] R2[137/600], Temp: 0.8768, Energy: -57.072295+0.000608j
[2025-08-23 01:14:57] [Iter 589/1050] R2[138/600], Temp: 0.8751, Energy: -56.962389+0.000716j
[2025-08-23 01:15:01] [Iter 590/1050] R2[139/600], Temp: 0.8733, Energy: -57.109908+0.002322j
[2025-08-23 01:15:06] [Iter 591/1050] R2[140/600], Temp: 0.8716, Energy: -56.988039-0.001730j
[2025-08-23 01:15:10] [Iter 592/1050] R2[141/600], Temp: 0.8698, Energy: -56.899326-0.004706j
[2025-08-23 01:15:14] [Iter 593/1050] R2[142/600], Temp: 0.8680, Energy: -57.049726-0.000724j
[2025-08-23 01:15:19] [Iter 594/1050] R2[143/600], Temp: 0.8663, Energy: -56.995616+0.001340j
[2025-08-23 01:15:23] [Iter 595/1050] R2[144/600], Temp: 0.8645, Energy: -56.972991+0.001711j
[2025-08-23 01:15:27] [Iter 596/1050] R2[145/600], Temp: 0.8627, Energy: -57.025284+0.001743j
[2025-08-23 01:15:32] [Iter 597/1050] R2[146/600], Temp: 0.8609, Energy: -57.003100+0.002899j
[2025-08-23 01:15:36] [Iter 598/1050] R2[147/600], Temp: 0.8591, Energy: -57.020905-0.001031j
[2025-08-23 01:15:40] [Iter 599/1050] R2[148/600], Temp: 0.8572, Energy: -56.912396-0.002165j
[2025-08-23 01:15:45] [Iter 600/1050] R2[149/600], Temp: 0.8554, Energy: -56.804699+0.001315j
[2025-08-23 01:15:45] ✓ Checkpoint saved: checkpoint_iter_000600.pkl
[2025-08-23 01:15:49] [Iter 601/1050] R2[150/600], Temp: 0.8536, Energy: -56.984594+0.000431j
[2025-08-23 01:15:53] [Iter 602/1050] R2[151/600], Temp: 0.8517, Energy: -56.899305+0.003732j
[2025-08-23 01:15:57] [Iter 603/1050] R2[152/600], Temp: 0.8498, Energy: -56.882250+0.000797j
[2025-08-23 01:16:02] [Iter 604/1050] R2[153/600], Temp: 0.8480, Energy: -56.874896+0.001916j
[2025-08-23 01:16:06] [Iter 605/1050] R2[154/600], Temp: 0.8461, Energy: -57.060445+0.003648j
[2025-08-23 01:16:10] [Iter 606/1050] R2[155/600], Temp: 0.8442, Energy: -57.010813-0.001726j
[2025-08-23 01:16:15] [Iter 607/1050] R2[156/600], Temp: 0.8423, Energy: -56.841934-0.003578j
[2025-08-23 01:16:19] [Iter 608/1050] R2[157/600], Temp: 0.8404, Energy: -56.860472+0.000444j
[2025-08-23 01:16:23] [Iter 609/1050] R2[158/600], Temp: 0.8384, Energy: -57.023723-0.002242j
[2025-08-23 01:16:28] [Iter 610/1050] R2[159/600], Temp: 0.8365, Energy: -56.982718-0.000113j
[2025-08-23 01:16:32] [Iter 611/1050] R2[160/600], Temp: 0.8346, Energy: -56.945327+0.002564j
[2025-08-23 01:16:36] [Iter 612/1050] R2[161/600], Temp: 0.8326, Energy: -57.025964+0.000062j
[2025-08-23 01:16:41] [Iter 613/1050] R2[162/600], Temp: 0.8307, Energy: -57.001354-0.000585j
[2025-08-23 01:16:45] [Iter 614/1050] R2[163/600], Temp: 0.8287, Energy: -56.969751+0.001975j
[2025-08-23 01:16:49] [Iter 615/1050] R2[164/600], Temp: 0.8267, Energy: -56.974169-0.000872j
[2025-08-23 01:16:53] [Iter 616/1050] R2[165/600], Temp: 0.8247, Energy: -56.992122-0.001138j
[2025-08-23 01:16:58] [Iter 617/1050] R2[166/600], Temp: 0.8227, Energy: -56.963893-0.000199j
[2025-08-23 01:17:02] [Iter 618/1050] R2[167/600], Temp: 0.8207, Energy: -56.921675+0.003873j
[2025-08-23 01:17:06] [Iter 619/1050] R2[168/600], Temp: 0.8187, Energy: -56.880502-0.002691j
[2025-08-23 01:17:11] [Iter 620/1050] R2[169/600], Temp: 0.8167, Energy: -56.928163+0.002114j
[2025-08-23 01:17:15] [Iter 621/1050] R2[170/600], Temp: 0.8147, Energy: -56.815191-0.001436j
[2025-08-23 01:17:19] [Iter 622/1050] R2[171/600], Temp: 0.8126, Energy: -57.054941+0.003543j
[2025-08-23 01:17:24] [Iter 623/1050] R2[172/600], Temp: 0.8106, Energy: -56.988840+0.001164j
[2025-08-23 01:17:28] [Iter 624/1050] R2[173/600], Temp: 0.8085, Energy: -57.001183+0.001694j
[2025-08-23 01:17:32] [Iter 625/1050] R2[174/600], Temp: 0.8065, Energy: -57.048507+0.006145j
[2025-08-23 01:17:37] [Iter 626/1050] R2[175/600], Temp: 0.8044, Energy: -57.002593-0.001509j
[2025-08-23 01:17:41] [Iter 627/1050] R2[176/600], Temp: 0.8023, Energy: -57.158151-0.002754j
[2025-08-23 01:17:45] [Iter 628/1050] R2[177/600], Temp: 0.8002, Energy: -57.022052+0.003538j
[2025-08-23 01:17:50] [Iter 629/1050] R2[178/600], Temp: 0.7981, Energy: -56.935764+0.000523j
[2025-08-23 01:17:54] [Iter 630/1050] R2[179/600], Temp: 0.7960, Energy: -57.025568+0.002600j
[2025-08-23 01:17:58] [Iter 631/1050] R2[180/600], Temp: 0.7939, Energy: -57.003322+0.000631j
[2025-08-23 01:18:02] [Iter 632/1050] R2[181/600], Temp: 0.7918, Energy: -57.050472-0.002701j
[2025-08-23 01:18:07] [Iter 633/1050] R2[182/600], Temp: 0.7896, Energy: -56.820222-0.001221j
[2025-08-23 01:18:11] [Iter 634/1050] R2[183/600], Temp: 0.7875, Energy: -56.856348-0.000545j
[2025-08-23 01:18:15] [Iter 635/1050] R2[184/600], Temp: 0.7854, Energy: -56.809968-0.003304j
[2025-08-23 01:18:20] [Iter 636/1050] R2[185/600], Temp: 0.7832, Energy: -56.925530+0.000832j
[2025-08-23 01:18:24] [Iter 637/1050] R2[186/600], Temp: 0.7810, Energy: -56.986300+0.001700j
[2025-08-23 01:18:28] [Iter 638/1050] R2[187/600], Temp: 0.7789, Energy: -56.881744-0.001992j
[2025-08-23 01:18:33] [Iter 639/1050] R2[188/600], Temp: 0.7767, Energy: -56.871457+0.003053j
[2025-08-23 01:18:37] [Iter 640/1050] R2[189/600], Temp: 0.7745, Energy: -56.882028+0.000004j
[2025-08-23 01:18:41] [Iter 641/1050] R2[190/600], Temp: 0.7723, Energy: -56.838272-0.004551j
[2025-08-23 01:18:46] [Iter 642/1050] R2[191/600], Temp: 0.7701, Energy: -56.948865+0.000754j
[2025-08-23 01:18:50] [Iter 643/1050] R2[192/600], Temp: 0.7679, Energy: -56.951341+0.002383j
[2025-08-23 01:18:54] [Iter 644/1050] R2[193/600], Temp: 0.7657, Energy: -57.062440-0.000871j
[2025-08-23 01:18:59] [Iter 645/1050] R2[194/600], Temp: 0.7635, Energy: -56.917060+0.002154j
[2025-08-23 01:19:03] [Iter 646/1050] R2[195/600], Temp: 0.7612, Energy: -57.012991+0.001653j
[2025-08-23 01:19:07] [Iter 647/1050] R2[196/600], Temp: 0.7590, Energy: -57.009456-0.000234j
[2025-08-23 01:19:11] [Iter 648/1050] R2[197/600], Temp: 0.7568, Energy: -57.034652+0.002238j
[2025-08-23 01:19:16] [Iter 649/1050] R2[198/600], Temp: 0.7545, Energy: -56.967448+0.000964j
[2025-08-23 01:19:20] [Iter 650/1050] R2[199/600], Temp: 0.7523, Energy: -57.087722-0.004036j
[2025-08-23 01:19:24] [Iter 651/1050] R2[200/600], Temp: 0.7500, Energy: -57.047165-0.007317j
[2025-08-23 01:19:29] [Iter 652/1050] R2[201/600], Temp: 0.7477, Energy: -57.040800-0.001169j
[2025-08-23 01:19:33] [Iter 653/1050] R2[202/600], Temp: 0.7455, Energy: -56.830549+0.000379j
[2025-08-23 01:19:37] [Iter 654/1050] R2[203/600], Temp: 0.7432, Energy: -56.972917-0.002193j
[2025-08-23 01:19:42] [Iter 655/1050] R2[204/600], Temp: 0.7409, Energy: -57.023750+0.001899j
[2025-08-23 01:19:46] [Iter 656/1050] R2[205/600], Temp: 0.7386, Energy: -57.198152+0.001531j
[2025-08-23 01:19:50] [Iter 657/1050] R2[206/600], Temp: 0.7363, Energy: -57.052851-0.001461j
[2025-08-23 01:19:55] [Iter 658/1050] R2[207/600], Temp: 0.7340, Energy: -56.924277-0.002486j
[2025-08-23 01:19:59] [Iter 659/1050] R2[208/600], Temp: 0.7316, Energy: -57.079612+0.001808j
[2025-08-23 01:20:03] [Iter 660/1050] R2[209/600], Temp: 0.7293, Energy: -57.081298+0.000239j
[2025-08-23 01:20:08] [Iter 661/1050] R2[210/600], Temp: 0.7270, Energy: -57.106494+0.000343j
[2025-08-23 01:20:12] [Iter 662/1050] R2[211/600], Temp: 0.7247, Energy: -56.983559+0.002867j
[2025-08-23 01:20:16] [Iter 663/1050] R2[212/600], Temp: 0.7223, Energy: -56.951285+0.001555j
[2025-08-23 01:20:20] [Iter 664/1050] R2[213/600], Temp: 0.7200, Energy: -57.005652-0.002084j
[2025-08-23 01:20:25] [Iter 665/1050] R2[214/600], Temp: 0.7176, Energy: -57.013180-0.004238j
[2025-08-23 01:20:29] [Iter 666/1050] R2[215/600], Temp: 0.7153, Energy: -57.118478+0.004664j
[2025-08-23 01:20:33] [Iter 667/1050] R2[216/600], Temp: 0.7129, Energy: -57.135491+0.005275j
[2025-08-23 01:20:38] [Iter 668/1050] R2[217/600], Temp: 0.7105, Energy: -57.053359+0.001057j
[2025-08-23 01:20:42] [Iter 669/1050] R2[218/600], Temp: 0.7081, Energy: -57.018391+0.001584j
[2025-08-23 01:20:46] [Iter 670/1050] R2[219/600], Temp: 0.7058, Energy: -56.969537-0.002922j
[2025-08-23 01:20:51] [Iter 671/1050] R2[220/600], Temp: 0.7034, Energy: -56.995424+0.002386j
[2025-08-23 01:20:55] [Iter 672/1050] R2[221/600], Temp: 0.7010, Energy: -56.989483+0.004169j
[2025-08-23 01:20:59] [Iter 673/1050] R2[222/600], Temp: 0.6986, Energy: -56.828839-0.002431j
[2025-08-23 01:21:04] [Iter 674/1050] R2[223/600], Temp: 0.6962, Energy: -56.934217-0.000444j
[2025-08-23 01:21:08] [Iter 675/1050] R2[224/600], Temp: 0.6938, Energy: -56.822031+0.000487j
[2025-08-23 01:21:12] [Iter 676/1050] R2[225/600], Temp: 0.6913, Energy: -56.841148+0.000407j
[2025-08-23 01:21:17] [Iter 677/1050] R2[226/600], Temp: 0.6889, Energy: -56.931210-0.000066j
[2025-08-23 01:21:21] [Iter 678/1050] R2[227/600], Temp: 0.6865, Energy: -56.852910-0.000689j
[2025-08-23 01:21:25] [Iter 679/1050] R2[228/600], Temp: 0.6841, Energy: -57.038093+0.000383j
[2025-08-23 01:21:29] [Iter 680/1050] R2[229/600], Temp: 0.6816, Energy: -56.938009+0.005924j
[2025-08-23 01:21:34] [Iter 681/1050] R2[230/600], Temp: 0.6792, Energy: -56.988294-0.003431j
[2025-08-23 01:21:38] [Iter 682/1050] R2[231/600], Temp: 0.6767, Energy: -56.932745-0.001645j
[2025-08-23 01:21:42] [Iter 683/1050] R2[232/600], Temp: 0.6743, Energy: -57.049223-0.000286j
[2025-08-23 01:21:47] [Iter 684/1050] R2[233/600], Temp: 0.6718, Energy: -56.953535-0.000644j
[2025-08-23 01:21:51] [Iter 685/1050] R2[234/600], Temp: 0.6694, Energy: -56.957007+0.002154j
[2025-08-23 01:21:55] [Iter 686/1050] R2[235/600], Temp: 0.6669, Energy: -57.095559-0.001181j
[2025-08-23 01:22:00] [Iter 687/1050] R2[236/600], Temp: 0.6644, Energy: -57.064872+0.005150j
[2025-08-23 01:22:04] [Iter 688/1050] R2[237/600], Temp: 0.6620, Energy: -57.012475-0.000416j
[2025-08-23 01:22:08] [Iter 689/1050] R2[238/600], Temp: 0.6595, Energy: -56.948952-0.001273j
[2025-08-23 01:22:13] [Iter 690/1050] R2[239/600], Temp: 0.6570, Energy: -57.039328+0.000377j
[2025-08-23 01:22:17] [Iter 691/1050] R2[240/600], Temp: 0.6545, Energy: -57.197412-0.001680j
[2025-08-23 01:22:21] [Iter 692/1050] R2[241/600], Temp: 0.6520, Energy: -57.004150-0.004596j
[2025-08-23 01:22:26] [Iter 693/1050] R2[242/600], Temp: 0.6495, Energy: -57.004117+0.001412j
[2025-08-23 01:22:30] [Iter 694/1050] R2[243/600], Temp: 0.6470, Energy: -56.964800-0.001660j
[2025-08-23 01:22:34] [Iter 695/1050] R2[244/600], Temp: 0.6445, Energy: -56.915099-0.005854j
[2025-08-23 01:22:38] [Iter 696/1050] R2[245/600], Temp: 0.6420, Energy: -57.074159+0.004675j
[2025-08-23 01:22:43] [Iter 697/1050] R2[246/600], Temp: 0.6395, Energy: -56.987571+0.004781j
[2025-08-23 01:22:47] [Iter 698/1050] R2[247/600], Temp: 0.6370, Energy: -56.995178+0.002391j
[2025-08-23 01:22:51] [Iter 699/1050] R2[248/600], Temp: 0.6345, Energy: -57.062844-0.001582j
[2025-08-23 01:22:56] [Iter 700/1050] R2[249/600], Temp: 0.6319, Energy: -57.085625-0.001625j
[2025-08-23 01:22:56] ✓ Checkpoint saved: checkpoint_iter_000700.pkl
[2025-08-23 01:23:00] [Iter 701/1050] R2[250/600], Temp: 0.6294, Energy: -57.142073-0.002929j
[2025-08-23 01:23:04] [Iter 702/1050] R2[251/600], Temp: 0.6269, Energy: -56.927507+0.005923j
[2025-08-23 01:23:09] [Iter 703/1050] R2[252/600], Temp: 0.6243, Energy: -56.941670-0.001658j
[2025-08-23 01:23:13] [Iter 704/1050] R2[253/600], Temp: 0.6218, Energy: -57.072516-0.004562j
[2025-08-23 01:23:17] [Iter 705/1050] R2[254/600], Temp: 0.6193, Energy: -56.934820-0.002871j
[2025-08-23 01:23:22] [Iter 706/1050] R2[255/600], Temp: 0.6167, Energy: -56.910843+0.001079j
[2025-08-23 01:23:26] [Iter 707/1050] R2[256/600], Temp: 0.6142, Energy: -56.938613-0.001369j
[2025-08-23 01:23:30] [Iter 708/1050] R2[257/600], Temp: 0.6116, Energy: -57.015303+0.001713j
[2025-08-23 01:23:35] [Iter 709/1050] R2[258/600], Temp: 0.6091, Energy: -57.001118-0.000956j
[2025-08-23 01:23:39] [Iter 710/1050] R2[259/600], Temp: 0.6065, Energy: -56.937097-0.001457j
[2025-08-23 01:23:43] [Iter 711/1050] R2[260/600], Temp: 0.6040, Energy: -57.013421+0.000593j
[2025-08-23 01:23:47] [Iter 712/1050] R2[261/600], Temp: 0.6014, Energy: -56.923288-0.007890j
[2025-08-23 01:23:52] [Iter 713/1050] R2[262/600], Temp: 0.5988, Energy: -56.944966+0.002486j
[2025-08-23 01:23:56] [Iter 714/1050] R2[263/600], Temp: 0.5963, Energy: -56.899288-0.002066j
[2025-08-23 01:24:00] [Iter 715/1050] R2[264/600], Temp: 0.5937, Energy: -57.027285-0.001515j
[2025-08-23 01:24:05] [Iter 716/1050] R2[265/600], Temp: 0.5911, Energy: -57.002796-0.006805j
[2025-08-23 01:24:09] [Iter 717/1050] R2[266/600], Temp: 0.5885, Energy: -56.958764+0.002032j
[2025-08-23 01:24:13] [Iter 718/1050] R2[267/600], Temp: 0.5860, Energy: -57.096625+0.001949j
[2025-08-23 01:24:18] [Iter 719/1050] R2[268/600], Temp: 0.5834, Energy: -56.944616+0.002072j
[2025-08-23 01:24:22] [Iter 720/1050] R2[269/600], Temp: 0.5808, Energy: -56.939452-0.002177j
[2025-08-23 01:24:26] [Iter 721/1050] R2[270/600], Temp: 0.5782, Energy: -56.966392-0.001380j
[2025-08-23 01:24:31] [Iter 722/1050] R2[271/600], Temp: 0.5756, Energy: -56.938346-0.000074j
[2025-08-23 01:24:35] [Iter 723/1050] R2[272/600], Temp: 0.5730, Energy: -56.980742-0.001403j
[2025-08-23 01:24:39] [Iter 724/1050] R2[273/600], Temp: 0.5705, Energy: -56.977793-0.001738j
[2025-08-23 01:24:44] [Iter 725/1050] R2[274/600], Temp: 0.5679, Energy: -57.030273-0.001973j
[2025-08-23 01:24:48] [Iter 726/1050] R2[275/600], Temp: 0.5653, Energy: -57.107505-0.002217j
[2025-08-23 01:24:52] [Iter 727/1050] R2[276/600], Temp: 0.5627, Energy: -57.071881-0.006637j
[2025-08-23 01:24:56] [Iter 728/1050] R2[277/600], Temp: 0.5601, Energy: -57.012249-0.001609j
[2025-08-23 01:25:01] [Iter 729/1050] R2[278/600], Temp: 0.5575, Energy: -56.941948-0.001062j
[2025-08-23 01:25:05] [Iter 730/1050] R2[279/600], Temp: 0.5549, Energy: -56.968772+0.001175j
[2025-08-23 01:25:09] [Iter 731/1050] R2[280/600], Temp: 0.5523, Energy: -56.781364+0.003288j
[2025-08-23 01:25:14] [Iter 732/1050] R2[281/600], Temp: 0.5497, Energy: -56.736970-0.002494j
[2025-08-23 01:25:18] [Iter 733/1050] R2[282/600], Temp: 0.5471, Energy: -56.816912+0.001274j
[2025-08-23 01:25:22] [Iter 734/1050] R2[283/600], Temp: 0.5444, Energy: -56.937675-0.001757j
[2025-08-23 01:25:27] [Iter 735/1050] R2[284/600], Temp: 0.5418, Energy: -56.917595-0.000723j
[2025-08-23 01:25:31] [Iter 736/1050] R2[285/600], Temp: 0.5392, Energy: -57.029564-0.002480j
[2025-08-23 01:25:36] [Iter 737/1050] R2[286/600], Temp: 0.5366, Energy: -56.984036-0.002314j
[2025-08-23 01:25:40] [Iter 738/1050] R2[287/600], Temp: 0.5340, Energy: -57.033940-0.000203j
[2025-08-23 01:25:44] [Iter 739/1050] R2[288/600], Temp: 0.5314, Energy: -57.067808-0.003143j
[2025-08-23 01:25:49] [Iter 740/1050] R2[289/600], Temp: 0.5288, Energy: -56.837146+0.000762j
[2025-08-23 01:25:53] [Iter 741/1050] R2[290/600], Temp: 0.5262, Energy: -57.043509+0.002267j
[2025-08-23 01:25:57] [Iter 742/1050] R2[291/600], Temp: 0.5236, Energy: -56.961706+0.000560j
[2025-08-23 01:26:02] [Iter 743/1050] R2[292/600], Temp: 0.5209, Energy: -56.957838+0.001088j
[2025-08-23 01:26:06] [Iter 744/1050] R2[293/600], Temp: 0.5183, Energy: -57.067312-0.000045j
[2025-08-23 01:26:10] [Iter 745/1050] R2[294/600], Temp: 0.5157, Energy: -56.969434+0.006046j
[2025-08-23 01:26:15] [Iter 746/1050] R2[295/600], Temp: 0.5131, Energy: -56.988511-0.003011j
[2025-08-23 01:26:19] [Iter 747/1050] R2[296/600], Temp: 0.5105, Energy: -57.023552+0.002943j
[2025-08-23 01:26:23] [Iter 748/1050] R2[297/600], Temp: 0.5079, Energy: -56.934979-0.000626j
[2025-08-23 01:26:27] [Iter 749/1050] R2[298/600], Temp: 0.5052, Energy: -56.966080-0.001043j
[2025-08-23 01:26:32] [Iter 750/1050] R2[299/600], Temp: 0.5026, Energy: -56.994220-0.000032j
[2025-08-23 01:26:36] [Iter 751/1050] R2[300/600], Temp: 0.5000, Energy: -57.131935+0.003382j
[2025-08-23 01:26:40] [Iter 752/1050] R2[301/600], Temp: 0.4974, Energy: -57.083383-0.001900j
[2025-08-23 01:26:45] [Iter 753/1050] R2[302/600], Temp: 0.4948, Energy: -57.006829-0.003481j
[2025-08-23 01:26:49] [Iter 754/1050] R2[303/600], Temp: 0.4921, Energy: -57.041683-0.001768j
[2025-08-23 01:26:53] [Iter 755/1050] R2[304/600], Temp: 0.4895, Energy: -56.898816+0.000186j
[2025-08-23 01:26:58] [Iter 756/1050] R2[305/600], Temp: 0.4869, Energy: -57.010715+0.003846j
[2025-08-23 01:27:02] [Iter 757/1050] R2[306/600], Temp: 0.4843, Energy: -57.102529+0.001918j
[2025-08-23 01:27:06] [Iter 758/1050] R2[307/600], Temp: 0.4817, Energy: -57.075861+0.000880j
[2025-08-23 01:27:11] [Iter 759/1050] R2[308/600], Temp: 0.4791, Energy: -56.976889+0.001041j
[2025-08-23 01:27:15] [Iter 760/1050] R2[309/600], Temp: 0.4764, Energy: -57.039311+0.000330j
[2025-08-23 01:27:19] [Iter 761/1050] R2[310/600], Temp: 0.4738, Energy: -56.931172+0.003931j
[2025-08-23 01:27:24] [Iter 762/1050] R2[311/600], Temp: 0.4712, Energy: -56.993329+0.000296j
[2025-08-23 01:27:28] [Iter 763/1050] R2[312/600], Temp: 0.4686, Energy: -57.003439-0.000130j
[2025-08-23 01:27:32] [Iter 764/1050] R2[313/600], Temp: 0.4660, Energy: -56.981899+0.002069j
[2025-08-23 01:27:36] [Iter 765/1050] R2[314/600], Temp: 0.4634, Energy: -57.009816+0.001489j
[2025-08-23 01:27:41] [Iter 766/1050] R2[315/600], Temp: 0.4608, Energy: -56.899900+0.000746j
[2025-08-23 01:27:45] [Iter 767/1050] R2[316/600], Temp: 0.4582, Energy: -56.962796+0.001639j
[2025-08-23 01:27:49] [Iter 768/1050] R2[317/600], Temp: 0.4556, Energy: -56.977491-0.000915j
[2025-08-23 01:27:54] [Iter 769/1050] R2[318/600], Temp: 0.4529, Energy: -57.084207-0.000834j
[2025-08-23 01:27:58] [Iter 770/1050] R2[319/600], Temp: 0.4503, Energy: -56.970438-0.001514j
[2025-08-23 01:28:02] [Iter 771/1050] R2[320/600], Temp: 0.4477, Energy: -56.837184-0.003246j
[2025-08-23 01:28:07] [Iter 772/1050] R2[321/600], Temp: 0.4451, Energy: -56.867356-0.000887j
[2025-08-23 01:28:11] [Iter 773/1050] R2[322/600], Temp: 0.4425, Energy: -56.954283-0.002282j
[2025-08-23 01:28:15] [Iter 774/1050] R2[323/600], Temp: 0.4399, Energy: -56.997630+0.001111j
[2025-08-23 01:28:20] [Iter 775/1050] R2[324/600], Temp: 0.4373, Energy: -57.092039+0.004069j
[2025-08-23 01:28:24] [Iter 776/1050] R2[325/600], Temp: 0.4347, Energy: -57.070298+0.002806j
[2025-08-23 01:28:28] [Iter 777/1050] R2[326/600], Temp: 0.4321, Energy: -57.062039-0.003713j
[2025-08-23 01:28:33] [Iter 778/1050] R2[327/600], Temp: 0.4295, Energy: -56.935882+0.002147j
[2025-08-23 01:28:37] [Iter 779/1050] R2[328/600], Temp: 0.4270, Energy: -56.940618+0.002084j
[2025-08-23 01:28:41] [Iter 780/1050] R2[329/600], Temp: 0.4244, Energy: -56.994903-0.002929j
[2025-08-23 01:28:45] [Iter 781/1050] R2[330/600], Temp: 0.4218, Energy: -56.878213+0.001141j
[2025-08-23 01:28:50] [Iter 782/1050] R2[331/600], Temp: 0.4192, Energy: -57.046972-0.000819j
[2025-08-23 01:28:54] [Iter 783/1050] R2[332/600], Temp: 0.4166, Energy: -56.941234+0.001301j
[2025-08-23 01:28:58] [Iter 784/1050] R2[333/600], Temp: 0.4140, Energy: -56.938096-0.003448j
[2025-08-23 01:29:03] [Iter 785/1050] R2[334/600], Temp: 0.4115, Energy: -56.993054-0.005255j
[2025-08-23 01:29:07] [Iter 786/1050] R2[335/600], Temp: 0.4089, Energy: -56.963593-0.003212j
[2025-08-23 01:29:11] [Iter 787/1050] R2[336/600], Temp: 0.4063, Energy: -57.100085-0.004361j
[2025-08-23 01:29:16] [Iter 788/1050] R2[337/600], Temp: 0.4037, Energy: -57.092306+0.003905j
[2025-08-23 01:29:20] [Iter 789/1050] R2[338/600], Temp: 0.4012, Energy: -57.063918-0.003102j
[2025-08-23 01:29:24] [Iter 790/1050] R2[339/600], Temp: 0.3986, Energy: -56.842726+0.001939j
[2025-08-23 01:29:29] [Iter 791/1050] R2[340/600], Temp: 0.3960, Energy: -56.821969-0.003611j
[2025-08-23 01:29:33] [Iter 792/1050] R2[341/600], Temp: 0.3935, Energy: -56.953027-0.004854j
[2025-08-23 01:29:37] [Iter 793/1050] R2[342/600], Temp: 0.3909, Energy: -57.033828-0.000763j
[2025-08-23 01:29:42] [Iter 794/1050] R2[343/600], Temp: 0.3884, Energy: -57.052428+0.001949j
[2025-08-23 01:29:46] [Iter 795/1050] R2[344/600], Temp: 0.3858, Energy: -56.898111-0.003191j
[2025-08-23 01:29:50] [Iter 796/1050] R2[345/600], Temp: 0.3833, Energy: -56.907243+0.001101j
[2025-08-23 01:29:54] [Iter 797/1050] R2[346/600], Temp: 0.3807, Energy: -57.027491-0.001132j
[2025-08-23 01:29:59] [Iter 798/1050] R2[347/600], Temp: 0.3782, Energy: -56.963167+0.004658j
[2025-08-23 01:30:03] [Iter 799/1050] R2[348/600], Temp: 0.3757, Energy: -56.971682-0.000673j
[2025-08-23 01:30:07] [Iter 800/1050] R2[349/600], Temp: 0.3731, Energy: -56.934478+0.000349j
[2025-08-23 01:30:07] ✓ Checkpoint saved: checkpoint_iter_000800.pkl
[2025-08-23 01:30:12] [Iter 801/1050] R2[350/600], Temp: 0.3706, Energy: -56.888180+0.000761j
[2025-08-23 01:30:16] [Iter 802/1050] R2[351/600], Temp: 0.3681, Energy: -56.958467+0.000529j
[2025-08-23 01:30:20] [Iter 803/1050] R2[352/600], Temp: 0.3655, Energy: -57.067149+0.002172j
[2025-08-23 01:30:25] [Iter 804/1050] R2[353/600], Temp: 0.3630, Energy: -57.002999-0.002118j
[2025-08-23 01:30:29] [Iter 805/1050] R2[354/600], Temp: 0.3605, Energy: -57.060222+0.002406j
[2025-08-23 01:30:33] [Iter 806/1050] R2[355/600], Temp: 0.3580, Energy: -57.029435+0.005856j
[2025-08-23 01:30:38] [Iter 807/1050] R2[356/600], Temp: 0.3555, Energy: -57.142752+0.003197j
[2025-08-23 01:30:42] [Iter 808/1050] R2[357/600], Temp: 0.3530, Energy: -57.191519-0.003338j
[2025-08-23 01:30:46] [Iter 809/1050] R2[358/600], Temp: 0.3505, Energy: -57.064305-0.004830j
[2025-08-23 01:30:50] [Iter 810/1050] R2[359/600], Temp: 0.3480, Energy: -56.915610-0.003708j
[2025-08-23 01:30:55] [Iter 811/1050] R2[360/600], Temp: 0.3455, Energy: -57.128671-0.001266j
[2025-08-23 01:30:59] [Iter 812/1050] R2[361/600], Temp: 0.3430, Energy: -57.055706+0.000360j
[2025-08-23 01:31:03] [Iter 813/1050] R2[362/600], Temp: 0.3405, Energy: -56.967721+0.000385j
[2025-08-23 01:31:08] [Iter 814/1050] R2[363/600], Temp: 0.3380, Energy: -57.059136+0.006741j
[2025-08-23 01:31:12] [Iter 815/1050] R2[364/600], Temp: 0.3356, Energy: -56.953446-0.000871j
[2025-08-23 01:31:16] [Iter 816/1050] R2[365/600], Temp: 0.3331, Energy: -56.942674-0.003191j
[2025-08-23 01:31:21] [Iter 817/1050] R2[366/600], Temp: 0.3306, Energy: -56.991404-0.000133j
[2025-08-23 01:31:25] [Iter 818/1050] R2[367/600], Temp: 0.3282, Energy: -57.002391-0.001115j
[2025-08-23 01:31:29] [Iter 819/1050] R2[368/600], Temp: 0.3257, Energy: -57.013564+0.002874j
[2025-08-23 01:31:34] [Iter 820/1050] R2[369/600], Temp: 0.3233, Energy: -56.841764+0.000055j
[2025-08-23 01:31:38] [Iter 821/1050] R2[370/600], Temp: 0.3208, Energy: -56.985125+0.002515j
[2025-08-23 01:31:42] [Iter 822/1050] R2[371/600], Temp: 0.3184, Energy: -56.818093-0.004080j
[2025-08-23 01:31:47] [Iter 823/1050] R2[372/600], Temp: 0.3159, Energy: -57.005129+0.003792j
[2025-08-23 01:31:51] [Iter 824/1050] R2[373/600], Temp: 0.3135, Energy: -57.015368+0.002982j
[2025-08-23 01:31:55] [Iter 825/1050] R2[374/600], Temp: 0.3111, Energy: -57.075024+0.001287j
[2025-08-23 01:31:59] [Iter 826/1050] R2[375/600], Temp: 0.3087, Energy: -56.954792+0.000775j
[2025-08-23 01:32:04] [Iter 827/1050] R2[376/600], Temp: 0.3062, Energy: -57.041970-0.000480j
[2025-08-23 01:32:08] [Iter 828/1050] R2[377/600], Temp: 0.3038, Energy: -57.103221-0.001326j
[2025-08-23 01:32:12] [Iter 829/1050] R2[378/600], Temp: 0.3014, Energy: -57.107604-0.002301j
[2025-08-23 01:32:17] [Iter 830/1050] R2[379/600], Temp: 0.2990, Energy: -57.062369-0.002117j
[2025-08-23 01:32:21] [Iter 831/1050] R2[380/600], Temp: 0.2966, Energy: -57.022118-0.000888j
[2025-08-23 01:32:25] [Iter 832/1050] R2[381/600], Temp: 0.2942, Energy: -57.007207-0.000675j
[2025-08-23 01:32:30] [Iter 833/1050] R2[382/600], Temp: 0.2919, Energy: -57.083240-0.001830j
[2025-08-23 01:32:34] [Iter 834/1050] R2[383/600], Temp: 0.2895, Energy: -57.055797-0.003103j
[2025-08-23 01:32:38] [Iter 835/1050] R2[384/600], Temp: 0.2871, Energy: -57.104674+0.001838j
[2025-08-23 01:32:43] [Iter 836/1050] R2[385/600], Temp: 0.2847, Energy: -56.949340-0.000920j
[2025-08-23 01:32:47] [Iter 837/1050] R2[386/600], Temp: 0.2824, Energy: -56.857990+0.001796j
[2025-08-23 01:32:51] [Iter 838/1050] R2[387/600], Temp: 0.2800, Energy: -56.699530-0.005788j
[2025-08-23 01:32:56] [Iter 839/1050] R2[388/600], Temp: 0.2777, Energy: -56.925518+0.003528j
[2025-08-23 01:33:00] [Iter 840/1050] R2[389/600], Temp: 0.2753, Energy: -56.935366-0.001914j
[2025-08-23 01:33:04] [Iter 841/1050] R2[390/600], Temp: 0.2730, Energy: -56.920597+0.002559j
[2025-08-23 01:33:08] [Iter 842/1050] R2[391/600], Temp: 0.2707, Energy: -56.966352-0.002524j
[2025-08-23 01:33:13] [Iter 843/1050] R2[392/600], Temp: 0.2684, Energy: -57.030683-0.001787j
[2025-08-23 01:33:17] [Iter 844/1050] R2[393/600], Temp: 0.2660, Energy: -57.013579+0.001165j
[2025-08-23 01:33:21] [Iter 845/1050] R2[394/600], Temp: 0.2637, Energy: -56.965156+0.001981j
[2025-08-23 01:33:26] [Iter 846/1050] R2[395/600], Temp: 0.2614, Energy: -57.122455+0.003807j
[2025-08-23 01:33:30] [Iter 847/1050] R2[396/600], Temp: 0.2591, Energy: -57.009092+0.002487j
[2025-08-23 01:33:34] [Iter 848/1050] R2[397/600], Temp: 0.2568, Energy: -56.971074+0.005789j
[2025-08-23 01:33:39] [Iter 849/1050] R2[398/600], Temp: 0.2545, Energy: -56.951353-0.000702j
[2025-08-23 01:33:43] [Iter 850/1050] R2[399/600], Temp: 0.2523, Energy: -56.988140+0.000190j
[2025-08-23 01:33:47] [Iter 851/1050] R2[400/600], Temp: 0.2500, Energy: -57.011259+0.000230j
[2025-08-23 01:33:52] [Iter 852/1050] R2[401/600], Temp: 0.2477, Energy: -57.132822-0.001136j
[2025-08-23 01:33:56] [Iter 853/1050] R2[402/600], Temp: 0.2455, Energy: -57.070436-0.000756j
[2025-08-23 01:34:00] [Iter 854/1050] R2[403/600], Temp: 0.2432, Energy: -56.908577-0.004128j
[2025-08-23 01:34:05] [Iter 855/1050] R2[404/600], Temp: 0.2410, Energy: -56.885342-0.003792j
[2025-08-23 01:34:09] [Iter 856/1050] R2[405/600], Temp: 0.2388, Energy: -56.927520-0.000769j
[2025-08-23 01:34:13] [Iter 857/1050] R2[406/600], Temp: 0.2365, Energy: -56.968513+0.003384j
[2025-08-23 01:34:18] [Iter 858/1050] R2[407/600], Temp: 0.2343, Energy: -57.003652-0.002364j
[2025-08-23 01:34:22] [Iter 859/1050] R2[408/600], Temp: 0.2321, Energy: -56.955137+0.003246j
[2025-08-23 01:34:26] [Iter 860/1050] R2[409/600], Temp: 0.2299, Energy: -56.887590-0.001222j
[2025-08-23 01:34:30] [Iter 861/1050] R2[410/600], Temp: 0.2277, Energy: -56.885895-0.000846j
[2025-08-23 01:34:35] [Iter 862/1050] R2[411/600], Temp: 0.2255, Energy: -57.027224+0.000364j
[2025-08-23 01:34:39] [Iter 863/1050] R2[412/600], Temp: 0.2233, Energy: -56.926865+0.002315j
[2025-08-23 01:34:43] [Iter 864/1050] R2[413/600], Temp: 0.2211, Energy: -56.894211-0.001922j
[2025-08-23 01:34:48] [Iter 865/1050] R2[414/600], Temp: 0.2190, Energy: -56.913022-0.002865j
[2025-08-23 01:34:52] [Iter 866/1050] R2[415/600], Temp: 0.2168, Energy: -56.874328+0.001093j
[2025-08-23 01:34:56] [Iter 867/1050] R2[416/600], Temp: 0.2146, Energy: -56.861287-0.004118j
[2025-08-23 01:35:01] [Iter 868/1050] R2[417/600], Temp: 0.2125, Energy: -56.916013-0.001291j
[2025-08-23 01:35:05] [Iter 869/1050] R2[418/600], Temp: 0.2104, Energy: -56.975057-0.005081j
[2025-08-23 01:35:09] [Iter 870/1050] R2[419/600], Temp: 0.2082, Energy: -57.006469-0.001607j
[2025-08-23 01:35:14] [Iter 871/1050] R2[420/600], Temp: 0.2061, Energy: -56.999418-0.001953j
[2025-08-23 01:35:18] [Iter 872/1050] R2[421/600], Temp: 0.2040, Energy: -57.054413-0.006624j
[2025-08-23 01:35:22] [Iter 873/1050] R2[422/600], Temp: 0.2019, Energy: -56.960945+0.000693j
[2025-08-23 01:35:27] [Iter 874/1050] R2[423/600], Temp: 0.1998, Energy: -57.069881-0.001309j
[2025-08-23 01:35:31] [Iter 875/1050] R2[424/600], Temp: 0.1977, Energy: -57.045737-0.001760j
[2025-08-23 01:35:35] [Iter 876/1050] R2[425/600], Temp: 0.1956, Energy: -57.060207+0.001372j
[2025-08-23 01:35:39] [Iter 877/1050] R2[426/600], Temp: 0.1935, Energy: -57.060284-0.000791j
[2025-08-23 01:35:44] [Iter 878/1050] R2[427/600], Temp: 0.1915, Energy: -56.939962-0.003271j
[2025-08-23 01:35:48] [Iter 879/1050] R2[428/600], Temp: 0.1894, Energy: -56.996531-0.002186j
[2025-08-23 01:35:52] [Iter 880/1050] R2[429/600], Temp: 0.1874, Energy: -56.914626-0.000435j
[2025-08-23 01:35:57] [Iter 881/1050] R2[430/600], Temp: 0.1853, Energy: -57.040665-0.002024j
[2025-08-23 01:36:01] [Iter 882/1050] R2[431/600], Temp: 0.1833, Energy: -57.175943-0.004801j
[2025-08-23 01:36:05] [Iter 883/1050] R2[432/600], Temp: 0.1813, Energy: -57.006946+0.000101j
[2025-08-23 01:36:10] [Iter 884/1050] R2[433/600], Temp: 0.1793, Energy: -56.969679+0.000846j
[2025-08-23 01:36:14] [Iter 885/1050] R2[434/600], Temp: 0.1773, Energy: -56.934838-0.002328j
[2025-08-23 01:36:18] [Iter 886/1050] R2[435/600], Temp: 0.1753, Energy: -56.988852-0.004526j
[2025-08-23 01:36:23] [Iter 887/1050] R2[436/600], Temp: 0.1733, Energy: -56.868031-0.003941j
[2025-08-23 01:36:27] [Iter 888/1050] R2[437/600], Temp: 0.1713, Energy: -57.016391+0.000477j
[2025-08-23 01:36:31] [Iter 889/1050] R2[438/600], Temp: 0.1693, Energy: -57.055654+0.001996j
[2025-08-23 01:36:36] [Iter 890/1050] R2[439/600], Temp: 0.1674, Energy: -57.103182-0.001575j
[2025-08-23 01:36:40] [Iter 891/1050] R2[440/600], Temp: 0.1654, Energy: -57.077724-0.000398j
[2025-08-23 01:36:44] [Iter 892/1050] R2[441/600], Temp: 0.1635, Energy: -56.976602+0.001434j
[2025-08-23 01:36:49] [Iter 893/1050] R2[442/600], Temp: 0.1616, Energy: -56.787741-0.001539j
[2025-08-23 01:36:53] [Iter 894/1050] R2[443/600], Temp: 0.1596, Energy: -56.964892+0.002766j
[2025-08-23 01:36:58] [Iter 895/1050] R2[444/600], Temp: 0.1577, Energy: -56.979156-0.000499j
[2025-08-23 01:37:02] [Iter 896/1050] R2[445/600], Temp: 0.1558, Energy: -57.055453+0.000581j
[2025-08-23 01:37:06] [Iter 897/1050] R2[446/600], Temp: 0.1539, Energy: -57.050392+0.003193j
[2025-08-23 01:37:10] [Iter 898/1050] R2[447/600], Temp: 0.1520, Energy: -57.144474+0.005965j
[2025-08-23 01:37:15] [Iter 899/1050] R2[448/600], Temp: 0.1502, Energy: -57.014827-0.000581j
[2025-08-23 01:37:19] [Iter 900/1050] R2[449/600], Temp: 0.1483, Energy: -56.933319+0.000949j
[2025-08-23 01:37:19] ✓ Checkpoint saved: checkpoint_iter_000900.pkl
[2025-08-23 01:37:23] [Iter 901/1050] R2[450/600], Temp: 0.1464, Energy: -57.037157-0.000069j
[2025-08-23 01:37:28] [Iter 902/1050] R2[451/600], Temp: 0.1446, Energy: -56.997016-0.005716j
[2025-08-23 01:37:32] [Iter 903/1050] R2[452/600], Temp: 0.1428, Energy: -57.128683+0.000563j
[2025-08-23 01:37:36] [Iter 904/1050] R2[453/600], Temp: 0.1409, Energy: -57.005888+0.007342j
[2025-08-23 01:37:41] [Iter 905/1050] R2[454/600], Temp: 0.1391, Energy: -57.044650+0.002328j
[2025-08-23 01:37:45] [Iter 906/1050] R2[455/600], Temp: 0.1373, Energy: -56.988414-0.003218j
[2025-08-23 01:37:49] [Iter 907/1050] R2[456/600], Temp: 0.1355, Energy: -56.899732+0.001266j
[2025-08-23 01:37:54] [Iter 908/1050] R2[457/600], Temp: 0.1337, Energy: -56.874153-0.011186j
[2025-08-23 01:37:58] [Iter 909/1050] R2[458/600], Temp: 0.1320, Energy: -56.890387+0.003566j
[2025-08-23 01:38:02] [Iter 910/1050] R2[459/600], Temp: 0.1302, Energy: -56.913649+0.001756j
[2025-08-23 01:38:07] [Iter 911/1050] R2[460/600], Temp: 0.1284, Energy: -56.888779-0.000747j
[2025-08-23 01:38:11] [Iter 912/1050] R2[461/600], Temp: 0.1267, Energy: -57.005727+0.001929j
[2025-08-23 01:38:15] [Iter 913/1050] R2[462/600], Temp: 0.1249, Energy: -56.964758+0.002872j
[2025-08-23 01:38:19] [Iter 914/1050] R2[463/600], Temp: 0.1232, Energy: -56.966201-0.000480j
[2025-08-23 01:38:24] [Iter 915/1050] R2[464/600], Temp: 0.1215, Energy: -57.018445+0.002620j
[2025-08-23 01:38:28] [Iter 916/1050] R2[465/600], Temp: 0.1198, Energy: -56.934093+0.002324j
[2025-08-23 01:38:32] [Iter 917/1050] R2[466/600], Temp: 0.1181, Energy: -57.000258+0.001029j
[2025-08-23 01:38:37] [Iter 918/1050] R2[467/600], Temp: 0.1164, Energy: -56.942995-0.000094j
[2025-08-23 01:38:41] [Iter 919/1050] R2[468/600], Temp: 0.1147, Energy: -56.812612-0.001704j
[2025-08-23 01:38:45] [Iter 920/1050] R2[469/600], Temp: 0.1131, Energy: -56.942590+0.001954j
[2025-08-23 01:38:50] [Iter 921/1050] R2[470/600], Temp: 0.1114, Energy: -56.926147+0.001805j
[2025-08-23 01:38:54] [Iter 922/1050] R2[471/600], Temp: 0.1098, Energy: -56.975196-0.001043j
[2025-08-23 01:38:58] [Iter 923/1050] R2[472/600], Temp: 0.1082, Energy: -56.925772+0.001143j
[2025-08-23 01:39:03] [Iter 924/1050] R2[473/600], Temp: 0.1065, Energy: -57.124959+0.003292j
[2025-08-23 01:39:07] [Iter 925/1050] R2[474/600], Temp: 0.1049, Energy: -57.096693+0.000194j
[2025-08-23 01:39:12] [Iter 926/1050] R2[475/600], Temp: 0.1033, Energy: -57.197764+0.001564j
[2025-08-23 01:39:16] [Iter 927/1050] R2[476/600], Temp: 0.1017, Energy: -57.086798-0.003719j
[2025-08-23 01:39:20] [Iter 928/1050] R2[477/600], Temp: 0.1002, Energy: -57.126143+0.001228j
[2025-08-23 01:39:25] [Iter 929/1050] R2[478/600], Temp: 0.0986, Energy: -57.040380-0.001436j
[2025-08-23 01:39:29] [Iter 930/1050] R2[479/600], Temp: 0.0970, Energy: -57.082237+0.005194j
[2025-08-23 01:39:33] [Iter 931/1050] R2[480/600], Temp: 0.0955, Energy: -56.981170+0.002274j
[2025-08-23 01:39:38] [Iter 932/1050] R2[481/600], Temp: 0.0940, Energy: -56.930684+0.000398j
[2025-08-23 01:39:42] [Iter 933/1050] R2[482/600], Temp: 0.0924, Energy: -57.060999+0.004564j
[2025-08-23 01:39:46] [Iter 934/1050] R2[483/600], Temp: 0.0909, Energy: -57.012997+0.002369j
[2025-08-23 01:39:51] [Iter 935/1050] R2[484/600], Temp: 0.0894, Energy: -57.054850+0.000562j
[2025-08-23 01:39:55] [Iter 936/1050] R2[485/600], Temp: 0.0879, Energy: -57.033048+0.002086j
[2025-08-23 01:39:59] [Iter 937/1050] R2[486/600], Temp: 0.0865, Energy: -57.117200+0.002837j
[2025-08-23 01:40:03] [Iter 938/1050] R2[487/600], Temp: 0.0850, Energy: -57.050517-0.000023j
[2025-08-23 01:40:08] [Iter 939/1050] R2[488/600], Temp: 0.0835, Energy: -56.999872-0.001904j
[2025-08-23 01:40:12] [Iter 940/1050] R2[489/600], Temp: 0.0821, Energy: -57.129490+0.005026j
[2025-08-23 01:40:16] [Iter 941/1050] R2[490/600], Temp: 0.0807, Energy: -57.114335+0.001576j
[2025-08-23 01:40:21] [Iter 942/1050] R2[491/600], Temp: 0.0792, Energy: -57.158902+0.002248j
[2025-08-23 01:40:25] [Iter 943/1050] R2[492/600], Temp: 0.0778, Energy: -56.976649+0.002075j
[2025-08-23 01:40:29] [Iter 944/1050] R2[493/600], Temp: 0.0764, Energy: -57.092494+0.003319j
[2025-08-23 01:40:34] [Iter 945/1050] R2[494/600], Temp: 0.0751, Energy: -56.984738+0.002937j
[2025-08-23 01:40:38] [Iter 946/1050] R2[495/600], Temp: 0.0737, Energy: -56.899606-0.002887j
[2025-08-23 01:40:42] [Iter 947/1050] R2[496/600], Temp: 0.0723, Energy: -57.012283+0.001325j
[2025-08-23 01:40:47] [Iter 948/1050] R2[497/600], Temp: 0.0710, Energy: -56.930765-0.001576j
[2025-08-23 01:40:51] [Iter 949/1050] R2[498/600], Temp: 0.0696, Energy: -56.954541+0.000644j
[2025-08-23 01:40:55] [Iter 950/1050] R2[499/600], Temp: 0.0683, Energy: -56.833799-0.001131j
[2025-08-23 01:41:00] [Iter 951/1050] R2[500/600], Temp: 0.0670, Energy: -56.952447+0.001557j
[2025-08-23 01:41:04] [Iter 952/1050] R2[501/600], Temp: 0.0657, Energy: -56.933819-0.000406j
[2025-08-23 01:41:08] [Iter 953/1050] R2[502/600], Temp: 0.0644, Energy: -56.810440-0.002858j
[2025-08-23 01:41:12] [Iter 954/1050] R2[503/600], Temp: 0.0631, Energy: -56.979757-0.000608j
[2025-08-23 01:41:17] [Iter 955/1050] R2[504/600], Temp: 0.0618, Energy: -56.989646-0.000704j
[2025-08-23 01:41:21] [Iter 956/1050] R2[505/600], Temp: 0.0606, Energy: -56.985023+0.009391j
[2025-08-23 01:41:25] [Iter 957/1050] R2[506/600], Temp: 0.0593, Energy: -56.940013-0.000526j
[2025-08-23 01:41:30] [Iter 958/1050] R2[507/600], Temp: 0.0581, Energy: -57.005738-0.001008j
[2025-08-23 01:41:34] [Iter 959/1050] R2[508/600], Temp: 0.0569, Energy: -57.031689+0.002259j
[2025-08-23 01:41:38] [Iter 960/1050] R2[509/600], Temp: 0.0557, Energy: -57.056731-0.002142j
[2025-08-23 01:41:43] [Iter 961/1050] R2[510/600], Temp: 0.0545, Energy: -57.082962-0.001790j
[2025-08-23 01:41:47] [Iter 962/1050] R2[511/600], Temp: 0.0533, Energy: -56.937825-0.000577j
[2025-08-23 01:41:51] [Iter 963/1050] R2[512/600], Temp: 0.0521, Energy: -56.902540-0.001460j
[2025-08-23 01:41:56] [Iter 964/1050] R2[513/600], Temp: 0.0510, Energy: -56.988340-0.003179j
[2025-08-23 01:42:00] [Iter 965/1050] R2[514/600], Temp: 0.0498, Energy: -57.036699+0.000728j
[2025-08-23 01:42:04] [Iter 966/1050] R2[515/600], Temp: 0.0487, Energy: -56.994600+0.001029j
[2025-08-23 01:42:08] [Iter 967/1050] R2[516/600], Temp: 0.0476, Energy: -56.953893+0.002685j
[2025-08-23 01:42:13] [Iter 968/1050] R2[517/600], Temp: 0.0465, Energy: -57.089142-0.000309j
[2025-08-23 01:42:17] [Iter 969/1050] R2[518/600], Temp: 0.0454, Energy: -56.893057+0.001544j
[2025-08-23 01:42:21] [Iter 970/1050] R2[519/600], Temp: 0.0443, Energy: -56.922928+0.000151j
[2025-08-23 01:42:26] [Iter 971/1050] R2[520/600], Temp: 0.0432, Energy: -57.005754+0.002382j
[2025-08-23 01:42:30] [Iter 972/1050] R2[521/600], Temp: 0.0422, Energy: -57.012453-0.004133j
[2025-08-23 01:42:34] [Iter 973/1050] R2[522/600], Temp: 0.0411, Energy: -57.114245+0.002881j
[2025-08-23 01:42:39] [Iter 974/1050] R2[523/600], Temp: 0.0401, Energy: -57.020730-0.002467j
[2025-08-23 01:42:43] [Iter 975/1050] R2[524/600], Temp: 0.0391, Energy: -56.955674-0.004405j
[2025-08-23 01:42:47] [Iter 976/1050] R2[525/600], Temp: 0.0381, Energy: -57.071045+0.000691j
[2025-08-23 01:42:52] [Iter 977/1050] R2[526/600], Temp: 0.0371, Energy: -56.888326+0.004905j
[2025-08-23 01:42:56] [Iter 978/1050] R2[527/600], Temp: 0.0361, Energy: -57.049947-0.000698j
[2025-08-23 01:43:00] [Iter 979/1050] R2[528/600], Temp: 0.0351, Energy: -56.846332-0.001370j
[2025-08-23 01:43:05] [Iter 980/1050] R2[529/600], Temp: 0.0342, Energy: -56.865552+0.002749j
[2025-08-23 01:43:09] [Iter 981/1050] R2[530/600], Temp: 0.0332, Energy: -56.867099-0.002534j
[2025-08-23 01:43:13] [Iter 982/1050] R2[531/600], Temp: 0.0323, Energy: -56.954593-0.000557j
[2025-08-23 01:43:17] [Iter 983/1050] R2[532/600], Temp: 0.0314, Energy: -56.966911-0.001083j
[2025-08-23 01:43:22] [Iter 984/1050] R2[533/600], Temp: 0.0305, Energy: -57.049485-0.002624j
[2025-08-23 01:43:26] [Iter 985/1050] R2[534/600], Temp: 0.0296, Energy: -56.912156+0.000129j
[2025-08-23 01:43:30] [Iter 986/1050] R2[535/600], Temp: 0.0287, Energy: -57.023382-0.000394j
[2025-08-23 01:43:35] [Iter 987/1050] R2[536/600], Temp: 0.0278, Energy: -56.953971+0.003792j
[2025-08-23 01:43:39] [Iter 988/1050] R2[537/600], Temp: 0.0270, Energy: -56.924286-0.003462j
[2025-08-23 01:43:43] [Iter 989/1050] R2[538/600], Temp: 0.0261, Energy: -56.940673+0.001394j
[2025-08-23 01:43:48] [Iter 990/1050] R2[539/600], Temp: 0.0253, Energy: -56.892652-0.002677j
[2025-08-23 01:43:52] [Iter 991/1050] R2[540/600], Temp: 0.0245, Energy: -56.986902+0.002661j
[2025-08-23 01:43:56] [Iter 992/1050] R2[541/600], Temp: 0.0237, Energy: -57.021819-0.001369j
[2025-08-23 01:44:01] [Iter 993/1050] R2[542/600], Temp: 0.0229, Energy: -56.994694-0.000256j
[2025-08-23 01:44:05] [Iter 994/1050] R2[543/600], Temp: 0.0221, Energy: -56.957419+0.000418j
[2025-08-23 01:44:09] [Iter 995/1050] R2[544/600], Temp: 0.0213, Energy: -56.979981-0.002499j
[2025-08-23 01:44:13] [Iter 996/1050] R2[545/600], Temp: 0.0206, Energy: -57.035387+0.003686j
[2025-08-23 01:44:18] [Iter 997/1050] R2[546/600], Temp: 0.0199, Energy: -57.045018+0.001855j
[2025-08-23 01:44:22] [Iter 998/1050] R2[547/600], Temp: 0.0191, Energy: -56.983411-0.001034j
[2025-08-23 01:44:26] [Iter 999/1050] R2[548/600], Temp: 0.0184, Energy: -56.950523+0.004041j
[2025-08-23 01:44:31] [Iter 1000/1050] R2[549/600], Temp: 0.0177, Energy: -57.054952-0.002419j
[2025-08-23 01:44:31] ✓ Checkpoint saved: checkpoint_iter_001000.pkl
[2025-08-23 01:44:35] [Iter 1001/1050] R2[550/600], Temp: 0.0170, Energy: -57.058410+0.003507j
[2025-08-23 01:44:39] [Iter 1002/1050] R2[551/600], Temp: 0.0164, Energy: -56.947375+0.003286j
[2025-08-23 01:44:44] [Iter 1003/1050] R2[552/600], Temp: 0.0157, Energy: -57.037119+0.001237j
[2025-08-23 01:44:48] [Iter 1004/1050] R2[553/600], Temp: 0.0151, Energy: -57.041426-0.000171j
[2025-08-23 01:44:52] [Iter 1005/1050] R2[554/600], Temp: 0.0144, Energy: -56.979841+0.000464j
[2025-08-23 01:44:57] [Iter 1006/1050] R2[555/600], Temp: 0.0138, Energy: -56.957384-0.001171j
[2025-08-23 01:45:01] [Iter 1007/1050] R2[556/600], Temp: 0.0132, Energy: -56.868824-0.000790j
[2025-08-23 01:45:05] [Iter 1008/1050] R2[557/600], Temp: 0.0126, Energy: -56.852426-0.002889j
[2025-08-23 01:45:10] [Iter 1009/1050] R2[558/600], Temp: 0.0120, Energy: -56.943231+0.002830j
[2025-08-23 01:45:14] [Iter 1010/1050] R2[559/600], Temp: 0.0115, Energy: -56.985163-0.000020j
[2025-08-23 01:45:18] [Iter 1011/1050] R2[560/600], Temp: 0.0109, Energy: -57.083775-0.000454j
[2025-08-23 01:45:22] [Iter 1012/1050] R2[561/600], Temp: 0.0104, Energy: -57.139989+0.000709j
[2025-08-23 01:45:27] [Iter 1013/1050] R2[562/600], Temp: 0.0099, Energy: -57.113026+0.003893j
[2025-08-23 01:45:31] [Iter 1014/1050] R2[563/600], Temp: 0.0094, Energy: -57.108345-0.003824j
[2025-08-23 01:45:35] [Iter 1015/1050] R2[564/600], Temp: 0.0089, Energy: -56.963856-0.002189j
[2025-08-23 01:45:40] [Iter 1016/1050] R2[565/600], Temp: 0.0084, Energy: -57.042896+0.000979j
[2025-08-23 01:45:44] [Iter 1017/1050] R2[566/600], Temp: 0.0079, Energy: -57.139188+0.002068j
[2025-08-23 01:45:48] [Iter 1018/1050] R2[567/600], Temp: 0.0074, Energy: -57.098004-0.000457j
[2025-08-23 01:45:53] [Iter 1019/1050] R2[568/600], Temp: 0.0070, Energy: -57.122673+0.005931j
[2025-08-23 01:45:57] [Iter 1020/1050] R2[569/600], Temp: 0.0066, Energy: -56.934622-0.004841j
[2025-08-23 01:46:01] [Iter 1021/1050] R2[570/600], Temp: 0.0062, Energy: -57.100103-0.001840j
[2025-08-23 01:46:06] [Iter 1022/1050] R2[571/600], Temp: 0.0058, Energy: -56.914573+0.004044j
[2025-08-23 01:46:10] [Iter 1023/1050] R2[572/600], Temp: 0.0054, Energy: -56.992421+0.000060j
[2025-08-23 01:46:14] [Iter 1024/1050] R2[573/600], Temp: 0.0050, Energy: -57.168259-0.002062j
[2025-08-23 01:46:19] [Iter 1025/1050] R2[574/600], Temp: 0.0046, Energy: -57.012171+0.000314j
[2025-08-23 01:46:23] [Iter 1026/1050] R2[575/600], Temp: 0.0043, Energy: -57.075857-0.000820j
[2025-08-23 01:46:27] [Iter 1027/1050] R2[576/600], Temp: 0.0039, Energy: -57.066051+0.000057j
[2025-08-23 01:46:31] [Iter 1028/1050] R2[577/600], Temp: 0.0036, Energy: -57.174347-0.005339j
[2025-08-23 01:46:36] [Iter 1029/1050] R2[578/600], Temp: 0.0033, Energy: -57.006620+0.003340j
[2025-08-23 01:46:40] [Iter 1030/1050] R2[579/600], Temp: 0.0030, Energy: -56.905739-0.003068j
[2025-08-23 01:46:44] [Iter 1031/1050] R2[580/600], Temp: 0.0027, Energy: -57.079927-0.001304j
[2025-08-23 01:46:49] [Iter 1032/1050] R2[581/600], Temp: 0.0025, Energy: -57.064477+0.000981j
[2025-08-23 01:46:53] [Iter 1033/1050] R2[582/600], Temp: 0.0022, Energy: -57.164808+0.000742j
[2025-08-23 01:46:57] [Iter 1034/1050] R2[583/600], Temp: 0.0020, Energy: -57.064892+0.000013j
[2025-08-23 01:47:02] [Iter 1035/1050] R2[584/600], Temp: 0.0018, Energy: -56.977140+0.002773j
[2025-08-23 01:47:06] [Iter 1036/1050] R2[585/600], Temp: 0.0015, Energy: -56.922732+0.001337j
[2025-08-23 01:47:10] [Iter 1037/1050] R2[586/600], Temp: 0.0013, Energy: -56.991439-0.005505j
[2025-08-23 01:47:15] [Iter 1038/1050] R2[587/600], Temp: 0.0012, Energy: -57.020742-0.002490j
[2025-08-23 01:47:19] [Iter 1039/1050] R2[588/600], Temp: 0.0010, Energy: -57.162264+0.002664j
[2025-08-23 01:47:23] [Iter 1040/1050] R2[589/600], Temp: 0.0008, Energy: -57.136499-0.002757j
[2025-08-23 01:47:27] [Iter 1041/1050] R2[590/600], Temp: 0.0007, Energy: -57.012291-0.000228j
[2025-08-23 01:47:32] [Iter 1042/1050] R2[591/600], Temp: 0.0006, Energy: -57.010577-0.001159j
[2025-08-23 01:47:36] [Iter 1043/1050] R2[592/600], Temp: 0.0004, Energy: -56.981988+0.001265j
[2025-08-23 01:47:40] [Iter 1044/1050] R2[593/600], Temp: 0.0003, Energy: -57.053691-0.003365j
[2025-08-23 01:47:45] [Iter 1045/1050] R2[594/600], Temp: 0.0002, Energy: -57.036943+0.000021j
[2025-08-23 01:47:49] [Iter 1046/1050] R2[595/600], Temp: 0.0002, Energy: -57.079654+0.001539j
[2025-08-23 01:47:53] [Iter 1047/1050] R2[596/600], Temp: 0.0001, Energy: -57.071177+0.001654j
[2025-08-23 01:47:58] [Iter 1048/1050] R2[597/600], Temp: 0.0001, Energy: -56.959429+0.000792j
[2025-08-23 01:48:02] [Iter 1049/1050] R2[598/600], Temp: 0.0000, Energy: -56.911707-0.001319j
[2025-08-23 01:48:06] [Iter 1050/1050] R2[599/600], Temp: 0.0000, Energy: -56.823404+0.000366j
[2025-08-23 01:48:06] ✅ Training completed | Restarts: 2
[2025-08-23 01:48:06] ============================================================
[2025-08-23 01:48:06] Training completed | Runtime: 4586.8s
[2025-08-23 01:48:08] ✓ Final state saved: checkpoints/final_GCNN.pkl
[2025-08-23 01:48:08] ============================================================
