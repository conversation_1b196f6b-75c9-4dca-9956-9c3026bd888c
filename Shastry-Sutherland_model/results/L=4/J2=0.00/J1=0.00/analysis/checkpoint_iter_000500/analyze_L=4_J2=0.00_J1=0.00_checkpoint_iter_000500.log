[2025-08-28 10:22:46] 使用checkpoint文件: results/L=4/J2=0.00/J1=0.00/training/checkpoints/checkpoint_iter_000500.pkl
[2025-08-28 10:22:57] ✓ 从checkpoint加载参数: 500
[2025-08-28 10:22:57]   - 能量: -53.006710+0.003623j ± 0.086458
[2025-08-28 10:22:57] ================================================================================
[2025-08-28 10:22:57] 加载量子态: L=4, J2=0.00, J1=0.00, checkpoint=checkpoint_iter_000500
[2025-08-28 10:22:57] 设置样本数为: 1048576
[2025-08-28 10:22:57] 开始生成共享样本集...
[2025-08-28 10:24:19] 样本生成完成,耗时: 82.617 秒
[2025-08-28 10:24:19] ================================================================================
[2025-08-28 10:24:19] 开始计算自旋结构因子...
[2025-08-28 10:24:19] 初始化操作符缓存...
[2025-08-28 10:24:19] 预构建所有自旋相关操作符...
[2025-08-28 10:24:19] 开始计算自旋相关函数...
[2025-08-28 10:24:27] 自旋算符进度: 1/64, 当前算符: S_0 · S_0, 耗时: 7.552s
[2025-08-28 10:24:36] 自旋算符进度: 2/64, 当前算符: S_0 · S_1, 耗时: 8.983s
[2025-08-28 10:24:40] 自旋算符进度: 3/64, 当前算符: S_0 · S_2, 耗时: 4.240s
[2025-08-28 10:24:44] 自旋算符进度: 4/64, 当前算符: S_0 · S_3, 耗时: 4.263s
[2025-08-28 10:24:49] 自旋算符进度: 5/64, 当前算符: S_0 · S_4, 耗时: 4.264s
[2025-08-28 10:24:53] 自旋算符进度: 6/64, 当前算符: S_0 · S_5, 耗时: 4.258s
[2025-08-28 10:24:57] 自旋算符进度: 7/64, 当前算符: S_0 · S_6, 耗时: 4.236s
[2025-08-28 10:25:02] 自旋算符进度: 8/64, 当前算符: S_0 · S_7, 耗时: 4.277s
[2025-08-28 10:25:06] 自旋算符进度: 9/64, 当前算符: S_0 · S_8, 耗时: 4.237s
[2025-08-28 10:25:10] 自旋算符进度: 10/64, 当前算符: S_0 · S_9, 耗时: 4.286s
[2025-08-28 10:25:14] 自旋算符进度: 11/64, 当前算符: S_0 · S_10, 耗时: 4.248s
[2025-08-28 10:25:19] 自旋算符进度: 12/64, 当前算符: S_0 · S_11, 耗时: 4.264s
[2025-08-28 10:25:23] 自旋算符进度: 13/64, 当前算符: S_0 · S_12, 耗时: 4.238s
[2025-08-28 10:25:27] 自旋算符进度: 14/64, 当前算符: S_0 · S_13, 耗时: 4.259s
[2025-08-28 10:25:31] 自旋算符进度: 15/64, 当前算符: S_0 · S_14, 耗时: 4.263s
[2025-08-28 10:25:36] 自旋算符进度: 16/64, 当前算符: S_0 · S_15, 耗时: 4.244s
[2025-08-28 10:25:40] 自旋算符进度: 17/64, 当前算符: S_0 · S_16, 耗时: 4.257s
[2025-08-28 10:25:44] 自旋算符进度: 18/64, 当前算符: S_0 · S_17, 耗时: 4.276s
[2025-08-28 10:25:48] 自旋算符进度: 19/64, 当前算符: S_0 · S_18, 耗时: 4.239s
[2025-08-28 10:25:53] 自旋算符进度: 20/64, 当前算符: S_0 · S_19, 耗时: 4.279s
[2025-08-28 10:25:57] 自旋算符进度: 21/64, 当前算符: S_0 · S_20, 耗时: 4.260s
[2025-08-28 10:26:01] 自旋算符进度: 22/64, 当前算符: S_0 · S_21, 耗时: 4.263s
[2025-08-28 10:26:06] 自旋算符进度: 23/64, 当前算符: S_0 · S_22, 耗时: 4.238s
[2025-08-28 10:26:10] 自旋算符进度: 24/64, 当前算符: S_0 · S_23, 耗时: 4.259s
[2025-08-28 10:26:14] 自旋算符进度: 25/64, 当前算符: S_0 · S_24, 耗时: 4.246s
[2025-08-28 10:26:18] 自旋算符进度: 26/64, 当前算符: S_0 · S_25, 耗时: 4.259s
[2025-08-28 10:26:23] 自旋算符进度: 27/64, 当前算符: S_0 · S_26, 耗时: 4.240s
[2025-08-28 10:26:27] 自旋算符进度: 28/64, 当前算符: S_0 · S_27, 耗时: 4.281s
[2025-08-28 10:26:31] 自旋算符进度: 29/64, 当前算符: S_0 · S_28, 耗时: 4.248s
[2025-08-28 10:26:35] 自旋算符进度: 30/64, 当前算符: S_0 · S_29, 耗时: 4.236s
[2025-08-28 10:26:40] 自旋算符进度: 31/64, 当前算符: S_0 · S_30, 耗时: 4.236s
[2025-08-28 10:26:44] 自旋算符进度: 32/64, 当前算符: S_0 · S_31, 耗时: 4.257s
[2025-08-28 10:26:48] 自旋算符进度: 33/64, 当前算符: S_0 · S_32, 耗时: 4.252s
[2025-08-28 10:26:52] 自旋算符进度: 34/64, 当前算符: S_0 · S_33, 耗时: 4.258s
[2025-08-28 10:26:57] 自旋算符进度: 35/64, 当前算符: S_0 · S_34, 耗时: 4.247s
[2025-08-28 10:27:01] 自旋算符进度: 36/64, 当前算符: S_0 · S_35, 耗时: 4.258s
[2025-08-28 10:27:05] 自旋算符进度: 37/64, 当前算符: S_0 · S_36, 耗时: 4.240s
[2025-08-28 10:27:09] 自旋算符进度: 38/64, 当前算符: S_0 · S_37, 耗时: 4.260s
[2025-08-28 10:27:14] 自旋算符进度: 39/64, 当前算符: S_0 · S_38, 耗时: 4.238s
[2025-08-28 10:27:18] 自旋算符进度: 40/64, 当前算符: S_0 · S_39, 耗时: 4.264s
[2025-08-28 10:27:22] 自旋算符进度: 41/64, 当前算符: S_0 · S_40, 耗时: 4.238s
[2025-08-28 10:27:26] 自旋算符进度: 42/64, 当前算符: S_0 · S_41, 耗时: 4.246s
[2025-08-28 10:27:31] 自旋算符进度: 43/64, 当前算符: S_0 · S_42, 耗时: 4.238s
[2025-08-28 10:27:35] 自旋算符进度: 44/64, 当前算符: S_0 · S_43, 耗时: 4.262s
[2025-08-28 10:27:39] 自旋算符进度: 45/64, 当前算符: S_0 · S_44, 耗时: 4.240s
[2025-08-28 10:27:43] 自旋算符进度: 46/64, 当前算符: S_0 · S_45, 耗时: 4.239s
[2025-08-28 10:27:48] 自旋算符进度: 47/64, 当前算符: S_0 · S_46, 耗时: 4.270s
[2025-08-28 10:27:52] 自旋算符进度: 48/64, 当前算符: S_0 · S_47, 耗时: 4.240s
[2025-08-28 10:27:56] 自旋算符进度: 49/64, 当前算符: S_0 · S_48, 耗时: 4.266s
[2025-08-28 10:28:00] 自旋算符进度: 50/64, 当前算符: S_0 · S_49, 耗时: 4.275s
[2025-08-28 10:28:05] 自旋算符进度: 51/64, 当前算符: S_0 · S_50, 耗时: 4.238s
[2025-08-28 10:28:09] 自旋算符进度: 52/64, 当前算符: S_0 · S_51, 耗时: 4.282s
[2025-08-28 10:28:13] 自旋算符进度: 53/64, 当前算符: S_0 · S_52, 耗时: 4.249s
[2025-08-28 10:28:18] 自旋算符进度: 54/64, 当前算符: S_0 · S_53, 耗时: 4.262s
[2025-08-28 10:28:22] 自旋算符进度: 55/64, 当前算符: S_0 · S_54, 耗时: 4.240s
[2025-08-28 10:28:26] 自旋算符进度: 56/64, 当前算符: S_0 · S_55, 耗时: 4.284s
[2025-08-28 10:28:30] 自旋算符进度: 57/64, 当前算符: S_0 · S_56, 耗时: 4.240s
[2025-08-28 10:28:35] 自旋算符进度: 58/64, 当前算符: S_0 · S_57, 耗时: 4.241s
[2025-08-28 10:28:39] 自旋算符进度: 59/64, 当前算符: S_0 · S_58, 耗时: 4.240s
[2025-08-28 10:28:43] 自旋算符进度: 60/64, 当前算符: S_0 · S_59, 耗时: 4.279s
[2025-08-28 10:28:47] 自旋算符进度: 61/64, 当前算符: S_0 · S_60, 耗时: 4.248s
[2025-08-28 10:28:52] 自旋算符进度: 62/64, 当前算符: S_0 · S_61, 耗时: 4.238s
[2025-08-28 10:28:56] 自旋算符进度: 63/64, 当前算符: S_0 · S_62, 耗时: 4.272s
[2025-08-28 10:29:00] 自旋算符进度: 64/64, 当前算符: S_0 · S_63, 耗时: 4.246s
[2025-08-28 10:29:00] 自旋相关函数计算完成,总耗时 280.74 秒
[2025-08-28 10:29:00] 计算傅里叶变换...
[2025-08-28 10:29:01] 自旋结构因子计算完成
[2025-08-28 10:29:02] 自旋相关函数平均误差: 0.000674
