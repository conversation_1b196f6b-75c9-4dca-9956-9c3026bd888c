[2025-08-07 17:50:35] 使用checkpoint文件: results/L=4/J2=0.00/J1=0.03/training/checkpoints/checkpoint_iter_000400.pkl
[2025-08-07 17:50:43] ✓ 从checkpoint加载参数: 400
[2025-08-07 17:50:43]   - 能量: -54.230164-0.004154j ± 0.086879
[2025-08-07 17:50:43] ================================================================================
[2025-08-07 17:50:43] 加载量子态: L=4, J2=0.00, J1=0.03, checkpoint=checkpoint_iter_000400
[2025-08-07 17:50:43] 设置样本数为: 1048576
[2025-08-07 17:50:43] 开始生成共享样本集...
[2025-08-07 17:53:15] 样本生成完成,耗时: 151.821 秒
[2025-08-07 17:53:15] ================================================================================
[2025-08-07 17:53:15] 开始计算自旋结构因子...
[2025-08-07 17:53:15] 初始化操作符缓存...
[2025-08-07 17:53:15] 预构建所有自旋相关操作符...
[2025-08-07 17:53:15] 开始计算自旋相关函数...
[2025-08-07 17:53:25] 自旋算符进度: 1/64, 当前算符: S_0 · S_0, 耗时: 10.133s
[2025-08-07 17:53:38] 自旋算符进度: 2/64, 当前算符: S_0 · S_1, 耗时: 13.158s
[2025-08-07 17:53:46] 自旋算符进度: 3/64, 当前算符: S_0 · S_2, 耗时: 8.235s
[2025-08-07 17:53:54] 自旋算符进度: 4/64, 当前算符: S_0 · S_3, 耗时: 8.194s
[2025-08-07 17:54:03] 自旋算符进度: 5/64, 当前算符: S_0 · S_4, 耗时: 8.231s
[2025-08-07 17:54:11] 自旋算符进度: 6/64, 当前算符: S_0 · S_5, 耗时: 8.219s
[2025-08-07 17:54:19] 自旋算符进度: 7/64, 当前算符: S_0 · S_6, 耗时: 8.193s
[2025-08-07 17:54:27] 自旋算符进度: 8/64, 当前算符: S_0 · S_7, 耗时: 8.234s
[2025-08-07 17:54:35] 自旋算符进度: 9/64, 当前算符: S_0 · S_8, 耗时: 8.195s
[2025-08-07 17:54:44] 自旋算符进度: 10/64, 当前算符: S_0 · S_9, 耗时: 8.195s
[2025-08-07 17:54:52] 自旋算符进度: 11/64, 当前算符: S_0 · S_10, 耗时: 8.234s
[2025-08-07 17:55:00] 自旋算符进度: 12/64, 当前算符: S_0 · S_11, 耗时: 8.194s
[2025-08-07 17:55:08] 自旋算符进度: 13/64, 当前算符: S_0 · S_12, 耗时: 8.227s
[2025-08-07 17:55:16] 自旋算符进度: 14/64, 当前算符: S_0 · S_13, 耗时: 8.195s
[2025-08-07 17:55:25] 自旋算符进度: 15/64, 当前算符: S_0 · S_14, 耗时: 8.233s
[2025-08-07 17:55:33] 自旋算符进度: 16/64, 当前算符: S_0 · S_15, 耗时: 8.194s
[2025-08-07 17:55:41] 自旋算符进度: 17/64, 当前算符: S_0 · S_16, 耗时: 8.234s
[2025-08-07 17:55:49] 自旋算符进度: 18/64, 当前算符: S_0 · S_17, 耗时: 8.222s
[2025-08-07 17:55:58] 自旋算符进度: 19/64, 当前算符: S_0 · S_18, 耗时: 8.195s
[2025-08-07 17:56:06] 自旋算符进度: 20/64, 当前算符: S_0 · S_19, 耗时: 8.225s
[2025-08-07 17:56:14] 自旋算符进度: 21/64, 当前算符: S_0 · S_20, 耗时: 8.194s
[2025-08-07 17:56:22] 自旋算符进度: 22/64, 当前算符: S_0 · S_21, 耗时: 8.194s
[2025-08-07 17:56:30] 自旋算符进度: 23/64, 当前算符: S_0 · S_22, 耗时: 8.231s
[2025-08-07 17:56:39] 自旋算符进度: 24/64, 当前算符: S_0 · S_23, 耗时: 8.196s
[2025-08-07 17:56:47] 自旋算符进度: 25/64, 当前算符: S_0 · S_24, 耗时: 8.216s
[2025-08-07 17:56:55] 自旋算符进度: 26/64, 当前算符: S_0 · S_25, 耗时: 8.236s
[2025-08-07 17:57:03] 自旋算符进度: 27/64, 当前算符: S_0 · S_26, 耗时: 8.194s
[2025-08-07 17:57:12] 自旋算符进度: 28/64, 当前算符: S_0 · S_27, 耗时: 8.233s
[2025-08-07 17:57:20] 自旋算符进度: 29/64, 当前算符: S_0 · S_28, 耗时: 8.196s
[2025-08-07 17:57:28] 自旋算符进度: 30/64, 当前算符: S_0 · S_29, 耗时: 8.193s
[2025-08-07 17:57:36] 自旋算符进度: 31/64, 当前算符: S_0 · S_30, 耗时: 8.233s
[2025-08-07 17:57:44] 自旋算符进度: 32/64, 当前算符: S_0 · S_31, 耗时: 8.235s
[2025-08-07 17:57:53] 自旋算符进度: 33/64, 当前算符: S_0 · S_32, 耗时: 8.196s
[2025-08-07 17:58:01] 自旋算符进度: 34/64, 当前算符: S_0 · S_33, 耗时: 8.194s
[2025-08-07 17:58:09] 自旋算符进度: 35/64, 当前算符: S_0 · S_34, 耗时: 8.231s
[2025-08-07 17:58:17] 自旋算符进度: 36/64, 当前算符: S_0 · S_35, 耗时: 8.193s
[2025-08-07 17:58:25] 自旋算符进度: 37/64, 当前算符: S_0 · S_36, 耗时: 8.233s
[2025-08-07 17:58:34] 自旋算符进度: 38/64, 当前算符: S_0 · S_37, 耗时: 8.244s
[2025-08-07 17:58:42] 自旋算符进度: 39/64, 当前算符: S_0 · S_38, 耗时: 8.193s
[2025-08-07 17:58:50] 自旋算符进度: 40/64, 当前算符: S_0 · S_39, 耗时: 8.232s
[2025-08-07 17:58:58] 自旋算符进度: 41/64, 当前算符: S_0 · S_40, 耗时: 8.195s
[2025-08-07 17:59:06] 自旋算符进度: 42/64, 当前算符: S_0 · S_41, 耗时: 8.194s
[2025-08-07 17:59:15] 自旋算符进度: 43/64, 当前算符: S_0 · S_42, 耗时: 8.234s
[2025-08-07 17:59:23] 自旋算符进度: 44/64, 当前算符: S_0 · S_43, 耗时: 8.193s
[2025-08-07 17:59:31] 自旋算符进度: 45/64, 当前算符: S_0 · S_44, 耗时: 8.233s
[2025-08-07 17:59:39] 自旋算符进度: 46/64, 当前算符: S_0 · S_45, 耗时: 8.228s
[2025-08-07 17:59:48] 自旋算符进度: 47/64, 当前算符: S_0 · S_46, 耗时: 8.195s
[2025-08-07 17:59:56] 自旋算符进度: 48/64, 当前算符: S_0 · S_47, 耗时: 8.234s
[2025-08-07 18:00:04] 自旋算符进度: 49/64, 当前算符: S_0 · S_48, 耗时: 8.196s
[2025-08-07 18:00:12] 自旋算符进度: 50/64, 当前算符: S_0 · S_49, 耗时: 8.195s
[2025-08-07 18:00:20] 自旋算符进度: 51/64, 当前算符: S_0 · S_50, 耗时: 8.223s
[2025-08-07 18:00:29] 自旋算符进度: 52/64, 当前算符: S_0 · S_51, 耗时: 8.194s
[2025-08-07 18:00:37] 自旋算符进度: 53/64, 当前算符: S_0 · S_52, 耗时: 8.196s
[2025-08-07 18:00:45] 自旋算符进度: 54/64, 当前算符: S_0 · S_53, 耗时: 8.193s
[2025-08-07 18:00:53] 自旋算符进度: 55/64, 当前算符: S_0 · S_54, 耗时: 8.232s
[2025-08-07 18:01:01] 自旋算符进度: 56/64, 当前算符: S_0 · S_55, 耗时: 8.196s
[2025-08-07 18:01:10] 自旋算符进度: 57/64, 当前算符: S_0 · S_56, 耗时: 8.234s
[2025-08-07 18:01:18] 自旋算符进度: 58/64, 当前算符: S_0 · S_57, 耗时: 8.228s
[2025-08-07 18:01:26] 自旋算符进度: 59/64, 当前算符: S_0 · S_58, 耗时: 8.197s
[2025-08-07 18:01:34] 自旋算符进度: 60/64, 当前算符: S_0 · S_59, 耗时: 8.234s
[2025-08-07 18:01:43] 自旋算符进度: 61/64, 当前算符: S_0 · S_60, 耗时: 8.196s
[2025-08-07 18:01:51] 自旋算符进度: 62/64, 当前算符: S_0 · S_61, 耗时: 8.195s
[2025-08-07 18:01:59] 自旋算符进度: 63/64, 当前算符: S_0 · S_62, 耗时: 8.219s
[2025-08-07 18:02:07] 自旋算符进度: 64/64, 当前算符: S_0 · S_63, 耗时: 8.196s
[2025-08-07 18:02:07] 自旋相关函数计算完成,总耗时 532.52 秒
[2025-08-07 18:02:07] 计算傅里叶变换...
[2025-08-07 18:02:08] 自旋结构因子计算完成
[2025-08-07 18:02:08] 自旋相关函数平均误差: 0.000665
