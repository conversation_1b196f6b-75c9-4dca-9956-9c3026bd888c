[2025-07-30 04:39:58] ==================================================
[2025-07-30 04:39:58] GCNN for Shastry-Sutherland Model
[2025-07-30 04:39:58] ==================================================
[2025-07-30 04:39:58] System parameters:
[2025-07-30 04:39:58]   - System size: L=4, N=64
[2025-07-30 04:39:58]   - System parameters: J1=0.04, J2=0.0, Q=1.0
[2025-07-30 04:39:58] --------------------------------------------------
[2025-07-30 04:39:58] Model parameters:
[2025-07-30 04:39:58]   - Number of layers = 4
[2025-07-30 04:39:58]   - Number of features = 4
[2025-07-30 04:39:58]   - Total parameters = 12572
[2025-07-30 04:39:58] --------------------------------------------------
[2025-07-30 04:39:58] Training parameters:
[2025-07-30 04:39:58]   - Learning rate: 0.015
[2025-07-30 04:39:58]   - Total iterations: 4650
[2025-07-30 04:39:58]   - Annealing cycles: 5
[2025-07-30 04:39:58]   - Initial period: 150
[2025-07-30 04:39:58]   - Period multiplier: 2.0
[2025-07-30 04:39:58]   - Temperature range: 0.0-1.0
[2025-07-30 04:39:58]   - Samples: 16384
[2025-07-30 04:39:58]   - Discarded samples: 0
[2025-07-30 04:39:58]   - Chunk size: 2048
[2025-07-30 04:39:58]   - Diagonal shift: 0.2
[2025-07-30 04:39:58]   - Gradient clipping: 1.0
[2025-07-30 04:39:58]   - Checkpoint enabled: interval=500
[2025-07-30 04:39:58]   - Checkpoint directory: results/L=4/J2=0.00/J1=0.04/training/checkpoints
[2025-07-30 04:39:58] --------------------------------------------------
[2025-07-30 04:39:58] Device status:
[2025-07-30 04:39:58]   - Devices model: A100
[2025-07-30 04:39:58]   - Number of devices: 2
[2025-07-30 04:39:58]   - Sharding: True
[2025-07-30 04:39:58] ============================================================
[2025-07-30 04:40:38] [Iter 1/4650] R0[0/150], Temp: 1.0000, Energy: 1.279787+0.000062j
[2025-07-30 04:40:49] [Iter 2/4650] R0[1/150], Temp: 0.9999, Energy: 1.279698-0.000195j
[2025-07-30 04:40:59] [Iter 3/4650] R0[2/150], Temp: 0.9996, Energy: 1.279697+0.000016j
[2025-07-30 04:41:09] [Iter 4/4650] R0[3/150], Temp: 0.9990, Energy: 1.279378+0.000023j
[2025-07-30 04:41:19] [Iter 5/4650] R0[4/150], Temp: 0.9982, Energy: 1.279443+0.000019j
[2025-07-30 04:41:29] [Iter 6/4650] R0[5/150], Temp: 0.9973, Energy: 1.279757+0.000379j
[2025-07-30 04:41:39] [Iter 7/4650] R0[6/150], Temp: 0.9961, Energy: 1.279352+0.000042j
[2025-07-30 04:41:49] [Iter 8/4650] R0[7/150], Temp: 0.9946, Energy: 1.279756+0.000124j
[2025-07-30 04:41:59] [Iter 9/4650] R0[8/150], Temp: 0.9930, Energy: 1.279469-0.000305j
[2025-07-30 04:42:10] [Iter 10/4650] R0[9/150], Temp: 0.9911, Energy: 1.279568+0.000132j
[2025-07-30 04:42:20] [Iter 11/4650] R0[10/150], Temp: 0.9891, Energy: 1.279291-0.000126j
[2025-07-30 04:42:30] [Iter 12/4650] R0[11/150], Temp: 0.9868, Energy: 1.279454-0.000098j
[2025-07-30 04:42:40] [Iter 13/4650] R0[12/150], Temp: 0.9843, Energy: 1.279270-0.000386j
[2025-07-30 04:42:50] [Iter 14/4650] R0[13/150], Temp: 0.9816, Energy: 1.279633+0.000151j
[2025-07-30 04:43:00] [Iter 15/4650] R0[14/150], Temp: 0.9787, Energy: 1.278854+0.000009j
[2025-07-30 04:43:10] [Iter 16/4650] R0[15/150], Temp: 0.9755, Energy: 1.279219-0.000059j
[2025-07-30 04:43:20] [Iter 17/4650] R0[16/150], Temp: 0.9722, Energy: 1.279422+0.000012j
[2025-07-30 04:43:31] [Iter 18/4650] R0[17/150], Temp: 0.9686, Energy: 1.279501-0.000263j
[2025-07-30 04:43:41] [Iter 19/4650] R0[18/150], Temp: 0.9649, Energy: 1.279436-0.000093j
[2025-07-30 04:43:51] [Iter 20/4650] R0[19/150], Temp: 0.9609, Energy: 1.279609-0.000251j
[2025-07-30 04:44:01] [Iter 21/4650] R0[20/150], Temp: 0.9568, Energy: 1.279565-0.000324j
[2025-07-30 04:44:11] [Iter 22/4650] R0[21/150], Temp: 0.9524, Energy: 1.279354-0.000040j
[2025-07-30 04:44:21] [Iter 23/4650] R0[22/150], Temp: 0.9479, Energy: 1.279023+0.000110j
[2025-07-30 04:44:31] [Iter 24/4650] R0[23/150], Temp: 0.9431, Energy: 1.279526-0.000624j
[2025-07-30 04:44:41] [Iter 25/4650] R0[24/150], Temp: 0.9382, Energy: 1.279212-0.000225j
[2025-07-30 04:44:51] [Iter 26/4650] R0[25/150], Temp: 0.9330, Energy: 1.279488-0.000287j
[2025-07-30 04:45:02] [Iter 27/4650] R0[26/150], Temp: 0.9277, Energy: 1.279694-0.000226j
[2025-07-30 04:45:12] [Iter 28/4650] R0[27/150], Temp: 0.9222, Energy: 1.279578-0.000068j
[2025-07-30 04:45:22] [Iter 29/4650] R0[28/150], Temp: 0.9165, Energy: 1.278912+0.000112j
[2025-07-30 04:45:32] [Iter 30/4650] R0[29/150], Temp: 0.9106, Energy: 1.279113+0.000286j
[2025-07-30 04:45:42] [Iter 31/4650] R0[30/150], Temp: 0.9045, Energy: 1.279665-0.000799j
[2025-07-30 04:45:52] [Iter 32/4650] R0[31/150], Temp: 0.8983, Energy: 1.278920+0.000010j
[2025-07-30 04:46:02] [Iter 33/4650] R0[32/150], Temp: 0.8918, Energy: 1.279374-0.000197j
[2025-07-30 04:46:12] [Iter 34/4650] R0[33/150], Temp: 0.8853, Energy: 1.279452-0.000258j
[2025-07-30 04:46:23] [Iter 35/4650] R0[34/150], Temp: 0.8785, Energy: 1.279701-0.000148j
[2025-07-30 04:46:33] [Iter 36/4650] R0[35/150], Temp: 0.8716, Energy: 1.278521-0.000116j
[2025-07-30 04:46:43] [Iter 37/4650] R0[36/150], Temp: 0.8645, Energy: 1.278863+0.000072j
[2025-07-30 04:46:53] [Iter 38/4650] R0[37/150], Temp: 0.8572, Energy: 1.279176-0.000159j
[2025-07-30 04:47:03] [Iter 39/4650] R0[38/150], Temp: 0.8498, Energy: 1.278858+0.000027j
[2025-07-30 04:47:13] [Iter 40/4650] R0[39/150], Temp: 0.8423, Energy: 1.278454-0.000149j
[2025-07-30 04:47:23] [Iter 41/4650] R0[40/150], Temp: 0.8346, Energy: 1.278676+0.000557j
[2025-07-30 04:47:33] [Iter 42/4650] R0[41/150], Temp: 0.8267, Energy: 1.279102+0.000230j
[2025-07-30 04:47:44] [Iter 43/4650] R0[42/150], Temp: 0.8187, Energy: 1.278289+0.000316j
[2025-07-30 04:47:54] [Iter 44/4650] R0[43/150], Temp: 0.8106, Energy: 1.279039-0.000213j
[2025-07-30 04:48:04] [Iter 45/4650] R0[44/150], Temp: 0.8023, Energy: 1.278566-0.000118j
[2025-07-30 04:48:14] [Iter 46/4650] R0[45/150], Temp: 0.7939, Energy: 1.279368-0.000749j
[2025-07-30 04:48:24] [Iter 47/4650] R0[46/150], Temp: 0.7854, Energy: 1.278184+0.000061j
[2025-07-30 04:48:34] [Iter 48/4650] R0[47/150], Temp: 0.7767, Energy: 1.277888+0.000559j
[2025-07-30 04:48:44] [Iter 49/4650] R0[48/150], Temp: 0.7679, Energy: 1.278038+0.000608j
[2025-07-30 04:48:54] [Iter 50/4650] R0[49/150], Temp: 0.7590, Energy: 1.278402-0.000098j
[2025-07-30 04:49:05] [Iter 51/4650] R0[50/150], Temp: 0.7500, Energy: 1.278236-0.000241j
[2025-07-30 04:49:15] [Iter 52/4650] R0[51/150], Temp: 0.7409, Energy: 1.278000+0.000001j
[2025-07-30 04:49:25] [Iter 53/4650] R0[52/150], Temp: 0.7316, Energy: 1.277622+0.000271j
[2025-07-30 04:49:35] [Iter 54/4650] R0[53/150], Temp: 0.7223, Energy: 1.277783+0.000608j
[2025-07-30 04:49:45] [Iter 55/4650] R0[54/150], Temp: 0.7129, Energy: 1.277896+0.000328j
[2025-07-30 04:49:55] [Iter 56/4650] R0[55/150], Temp: 0.7034, Energy: 1.277248+0.000237j
[2025-07-30 04:50:05] [Iter 57/4650] R0[56/150], Temp: 0.6938, Energy: 1.278636-0.000178j
[2025-07-30 04:50:16] [Iter 58/4650] R0[57/150], Temp: 0.6841, Energy: 1.277860+0.000093j
[2025-07-30 04:50:26] [Iter 59/4650] R0[58/150], Temp: 0.6743, Energy: 1.277977+0.000197j
[2025-07-30 04:50:36] [Iter 60/4650] R0[59/150], Temp: 0.6644, Energy: 1.277807-0.000118j
[2025-07-30 04:50:46] [Iter 61/4650] R0[60/150], Temp: 0.6545, Energy: 1.277482+0.000105j
[2025-07-30 04:50:56] [Iter 62/4650] R0[61/150], Temp: 0.6445, Energy: 1.277019-0.000090j
[2025-07-30 04:51:06] [Iter 63/4650] R0[62/150], Temp: 0.6345, Energy: 1.277490-0.000349j
[2025-07-30 04:51:16] [Iter 64/4650] R0[63/150], Temp: 0.6243, Energy: 1.278134-0.000688j
[2025-07-30 04:51:27] [Iter 65/4650] R0[64/150], Temp: 0.6142, Energy: 1.276997+0.000130j
[2025-07-30 04:51:37] [Iter 66/4650] R0[65/150], Temp: 0.6040, Energy: 1.276736-0.000251j
[2025-07-30 04:51:47] [Iter 67/4650] R0[66/150], Temp: 0.5937, Energy: 1.276463-0.000374j
[2025-07-30 04:51:57] [Iter 68/4650] R0[67/150], Temp: 0.5834, Energy: 1.276144+0.000076j
[2025-07-30 04:52:07] [Iter 69/4650] R0[68/150], Temp: 0.5730, Energy: 1.277790-0.000958j
[2025-07-30 04:52:17] [Iter 70/4650] R0[69/150], Temp: 0.5627, Energy: 1.276784-0.000290j
[2025-07-30 04:52:27] [Iter 71/4650] R0[70/150], Temp: 0.5523, Energy: 1.275921-0.000233j
[2025-07-30 04:52:37] [Iter 72/4650] R0[71/150], Temp: 0.5418, Energy: 1.275340-0.000277j
[2025-07-30 04:52:48] [Iter 73/4650] R0[72/150], Temp: 0.5314, Energy: 1.276602-0.001168j
[2025-07-30 04:52:58] [Iter 74/4650] R0[73/150], Temp: 0.5209, Energy: 1.275710-0.000275j
[2025-07-30 04:53:08] [Iter 75/4650] R0[74/150], Temp: 0.5105, Energy: 1.275204-0.000533j
[2025-07-30 04:53:18] [Iter 76/4650] R0[75/150], Temp: 0.5000, Energy: 1.275320-0.000046j
[2025-07-30 04:53:28] [Iter 77/4650] R0[76/150], Temp: 0.4895, Energy: 1.275163-0.000890j
[2025-07-30 04:53:38] [Iter 78/4650] R0[77/150], Temp: 0.4791, Energy: 1.273367-0.000331j
[2025-07-30 04:53:48] [Iter 79/4650] R0[78/150], Temp: 0.4686, Energy: 1.272821-0.000002j
[2025-07-30 04:53:58] [Iter 80/4650] R0[79/150], Temp: 0.4582, Energy: 1.273008-0.000525j
[2025-07-30 04:54:09] [Iter 81/4650] R0[80/150], Temp: 0.4477, Energy: 1.272453-0.000122j
[2025-07-30 04:54:19] [Iter 82/4650] R0[81/150], Temp: 0.4373, Energy: 1.271151-0.000356j
[2025-07-30 04:54:29] [Iter 83/4650] R0[82/150], Temp: 0.4270, Energy: 1.273230-0.001167j
[2025-07-30 04:54:39] [Iter 84/4650] R0[83/150], Temp: 0.4166, Energy: 1.271917-0.000757j
[2025-07-30 04:54:49] [Iter 85/4650] R0[84/150], Temp: 0.4063, Energy: 1.269758-0.000434j
[2025-07-30 04:54:59] [Iter 86/4650] R0[85/150], Temp: 0.3960, Energy: 1.268901+0.000099j
[2025-07-30 04:55:10] [Iter 87/4650] R0[86/150], Temp: 0.3858, Energy: 1.267035+0.000370j
[2025-07-30 04:55:20] [Iter 88/4650] R0[87/150], Temp: 0.3757, Energy: 1.267391+0.000068j
[2025-07-30 04:55:30] [Iter 89/4650] R0[88/150], Temp: 0.3655, Energy: 1.266872+0.000294j
[2025-07-30 04:55:40] [Iter 90/4650] R0[89/150], Temp: 0.3555, Energy: 1.263713+0.000541j
[2025-07-30 04:55:50] [Iter 91/4650] R0[90/150], Temp: 0.3455, Energy: 1.264127-0.000346j
[2025-07-30 04:56:00] [Iter 92/4650] R0[91/150], Temp: 0.3356, Energy: 1.259395+0.001998j
[2025-07-30 04:56:10] [Iter 93/4650] R0[92/150], Temp: 0.3257, Energy: 1.261346-0.000522j
[2025-07-30 04:56:21] [Iter 94/4650] R0[93/150], Temp: 0.3159, Energy: 1.261035-0.000159j
[2025-07-30 04:56:31] [Iter 95/4650] R0[94/150], Temp: 0.3062, Energy: 1.256146+0.000678j
[2025-07-30 04:56:41] [Iter 96/4650] R0[95/150], Temp: 0.2966, Energy: 1.256716+0.000759j
[2025-07-30 04:56:51] [Iter 97/4650] R0[96/150], Temp: 0.2871, Energy: 1.255861-0.001358j
[2025-07-30 04:57:01] [Iter 98/4650] R0[97/150], Temp: 0.2777, Energy: 1.252324-0.000649j
[2025-07-30 04:57:11] [Iter 99/4650] R0[98/150], Temp: 0.2684, Energy: 1.248305+0.000766j
[2025-07-30 04:57:21] [Iter 100/4650] R0[99/150], Temp: 0.2591, Energy: 1.246415+0.000038j
[2025-07-30 04:57:31] [Iter 101/4650] R0[100/150], Temp: 0.2500, Energy: 1.244178-0.000257j
[2025-07-30 04:57:42] [Iter 102/4650] R0[101/150], Temp: 0.2410, Energy: 1.244168-0.002217j
[2025-07-30 04:57:52] [Iter 103/4650] R0[102/150], Temp: 0.2321, Energy: 1.237184-0.001572j
[2025-07-30 04:58:02] [Iter 104/4650] R0[103/150], Temp: 0.2233, Energy: 1.233532+0.000195j
[2025-07-30 04:58:12] [Iter 105/4650] R0[104/150], Temp: 0.2146, Energy: 1.228737-0.001245j
[2025-07-30 04:58:22] [Iter 106/4650] R0[105/150], Temp: 0.2061, Energy: 1.225318+0.000622j
[2025-07-30 04:58:32] [Iter 107/4650] R0[106/150], Temp: 0.1977, Energy: 1.224742-0.002621j
[2025-07-30 04:58:42] [Iter 108/4650] R0[107/150], Temp: 0.1894, Energy: 1.211062+0.000596j
[2025-07-30 04:58:52] [Iter 109/4650] R0[108/150], Temp: 0.1813, Energy: 1.209778-0.001977j
[2025-07-30 04:59:03] [Iter 110/4650] R0[109/150], Temp: 0.1733, Energy: 1.194349+0.002414j
[2025-07-30 04:59:13] [Iter 111/4650] R0[110/150], Temp: 0.1654, Energy: 1.192468-0.001309j
[2025-07-30 04:59:23] [Iter 112/4650] R0[111/150], Temp: 0.1577, Energy: 1.184040-0.001122j
[2025-07-30 04:59:33] [Iter 113/4650] R0[112/150], Temp: 0.1502, Energy: 1.168738+0.001145j
[2025-07-30 04:59:43] [Iter 114/4650] R0[113/150], Temp: 0.1428, Energy: 1.163948-0.002439j
[2025-07-30 04:59:53] [Iter 115/4650] R0[114/150], Temp: 0.1355, Energy: 1.144432+0.001790j
[2025-07-30 05:00:03] [Iter 116/4650] R0[115/150], Temp: 0.1284, Energy: 1.132062+0.001485j
[2025-07-30 05:00:14] [Iter 117/4650] R0[116/150], Temp: 0.1215, Energy: 1.116301+0.002284j
[2025-07-30 05:00:24] [Iter 118/4650] R0[117/150], Temp: 0.1147, Energy: 1.098790+0.001674j
[2025-07-30 05:00:34] [Iter 119/4650] R0[118/150], Temp: 0.1082, Energy: 1.066707+0.003738j
[2025-07-30 05:00:44] [Iter 120/4650] R0[119/150], Temp: 0.1017, Energy: 1.054637-0.000417j
[2025-07-30 05:00:54] [Iter 121/4650] R0[120/150], Temp: 0.0955, Energy: 1.029974-0.000263j
[2025-07-30 05:01:04] [Iter 122/4650] R0[121/150], Temp: 0.0894, Energy: 1.007405-0.002067j
[2025-07-30 05:01:14] [Iter 123/4650] R0[122/150], Temp: 0.0835, Energy: 0.970811+0.003460j
[2025-07-30 05:01:24] [Iter 124/4650] R0[123/150], Temp: 0.0778, Energy: 0.927691+0.003077j
[2025-07-30 05:01:35] [Iter 125/4650] R0[124/150], Temp: 0.0723, Energy: 0.894761+0.001026j
[2025-07-30 05:01:45] [Iter 126/4650] R0[125/150], Temp: 0.0670, Energy: 0.841340+0.004230j
[2025-07-30 05:01:55] [Iter 127/4650] R0[126/150], Temp: 0.0618, Energy: 0.799374+0.002396j
[2025-07-30 05:02:05] [Iter 128/4650] R0[127/150], Temp: 0.0569, Energy: 0.745737+0.002367j
[2025-07-30 05:02:15] [Iter 129/4650] R0[128/150], Temp: 0.0521, Energy: 0.683204+0.005326j
[2025-07-30 05:02:25] [Iter 130/4650] R0[129/150], Temp: 0.0476, Energy: 0.627763-0.003665j
[2025-07-30 05:02:35] [Iter 131/4650] R0[130/150], Temp: 0.0432, Energy: 0.560439-0.007627j
[2025-07-30 05:02:46] [Iter 132/4650] R0[131/150], Temp: 0.0391, Energy: 0.464485-0.002085j
[2025-07-30 05:02:56] [Iter 133/4650] R0[132/150], Temp: 0.0351, Energy: 0.351412+0.008244j
[2025-07-30 05:03:06] [Iter 134/4650] R0[133/150], Temp: 0.0314, Energy: 0.261436+0.007431j
[2025-07-30 05:03:16] [Iter 135/4650] R0[134/150], Temp: 0.0278, Energy: 0.156529+0.006331j
[2025-07-30 05:03:26] [Iter 136/4650] R0[135/150], Temp: 0.0245, Energy: 0.056045-0.005737j
[2025-07-30 05:03:36] [Iter 137/4650] R0[136/150], Temp: 0.0213, Energy: -0.096650+0.000704j
[2025-07-30 05:03:46] [Iter 138/4650] R0[137/150], Temp: 0.0184, Energy: -0.247640-0.005499j
[2025-07-30 05:03:56] [Iter 139/4650] R0[138/150], Temp: 0.0157, Energy: -0.396215-0.007263j
[2025-07-30 05:04:06] [Iter 140/4650] R0[139/150], Temp: 0.0132, Energy: -0.604706+0.007420j
[2025-07-30 05:04:17] [Iter 141/4650] R0[140/150], Temp: 0.0109, Energy: -0.764903-0.014571j
[2025-07-30 05:04:27] [Iter 142/4650] R0[141/150], Temp: 0.0089, Energy: -0.958194-0.001237j
[2025-07-30 05:04:37] [Iter 143/4650] R0[142/150], Temp: 0.0070, Energy: -1.223675-0.005036j
[2025-07-30 05:04:47] [Iter 144/4650] R0[143/150], Temp: 0.0054, Energy: -1.432755-0.010001j
[2025-07-30 05:04:57] [Iter 145/4650] R0[144/150], Temp: 0.0039, Energy: -1.723458-0.002357j
[2025-07-30 05:05:07] [Iter 146/4650] R0[145/150], Temp: 0.0027, Energy: -2.026828+0.007185j
[2025-07-30 05:05:17] [Iter 147/4650] R0[146/150], Temp: 0.0018, Energy: -2.332522+0.011288j
[2025-07-30 05:05:27] [Iter 148/4650] R0[147/150], Temp: 0.0010, Energy: -2.661564+0.002218j
[2025-07-30 05:05:38] [Iter 149/4650] R0[148/150], Temp: 0.0004, Energy: -3.019121-0.013669j
[2025-07-30 05:05:48] [Iter 150/4650] R0[149/150], Temp: 0.0001, Energy: -3.434671+0.001386j
[2025-07-30 05:05:48] RESTART #1 | Period: 300
[2025-07-30 05:05:58] [Iter 151/4650] R1[0/300], Temp: 1.0000, Energy: -3.861674-0.040470j
[2025-07-30 05:06:08] [Iter 152/4650] R1[1/300], Temp: 1.0000, Energy: -4.378853-0.015451j
[2025-07-30 05:06:18] [Iter 153/4650] R1[2/300], Temp: 0.9999, Energy: -4.810430-0.010085j
[2025-07-30 05:06:28] [Iter 154/4650] R1[3/300], Temp: 0.9998, Energy: -5.399706-0.030478j
[2025-07-30 05:06:38] [Iter 155/4650] R1[4/300], Temp: 0.9996, Energy: -6.078237-0.063024j
[2025-07-30 05:06:48] [Iter 156/4650] R1[5/300], Temp: 0.9993, Energy: -6.706835-0.057163j
[2025-07-30 05:06:59] [Iter 157/4650] R1[6/300], Temp: 0.9990, Energy: -7.351153-0.054883j
[2025-07-30 05:07:09] [Iter 158/4650] R1[7/300], Temp: 0.9987, Energy: -8.139898-0.085696j
[2025-07-30 05:07:19] [Iter 159/4650] R1[8/300], Temp: 0.9982, Energy: -8.924959-0.075848j
[2025-07-30 05:07:29] [Iter 160/4650] R1[9/300], Temp: 0.9978, Energy: -9.824364-0.124401j
[2025-07-30 05:07:39] [Iter 161/4650] R1[10/300], Temp: 0.9973, Energy: -10.745497-0.126733j
[2025-07-30 05:07:49] [Iter 162/4650] R1[11/300], Temp: 0.9967, Energy: -11.723692-0.160297j
[2025-07-30 05:07:59] [Iter 163/4650] R1[12/300], Temp: 0.9961, Energy: -12.726505-0.155573j
[2025-07-30 05:08:10] [Iter 164/4650] R1[13/300], Temp: 0.9954, Energy: -13.682736-0.161555j
[2025-07-30 05:08:20] [Iter 165/4650] R1[14/300], Temp: 0.9946, Energy: -14.803102-0.119040j
[2025-07-30 05:08:30] [Iter 166/4650] R1[15/300], Temp: 0.9938, Energy: -15.917023-0.206400j
[2025-07-30 05:08:40] [Iter 167/4650] R1[16/300], Temp: 0.9930, Energy: -16.951450-0.149981j
[2025-07-30 05:08:50] [Iter 168/4650] R1[17/300], Temp: 0.9921, Energy: -18.069689-0.235308j
[2025-07-30 05:09:00] [Iter 169/4650] R1[18/300], Temp: 0.9911, Energy: -19.237344-0.148513j
[2025-07-30 05:09:10] [Iter 170/4650] R1[19/300], Temp: 0.9901, Energy: -20.416244-0.144682j
[2025-07-30 05:09:20] [Iter 171/4650] R1[20/300], Temp: 0.9891, Energy: -21.568928-0.091459j
[2025-07-30 05:09:31] [Iter 172/4650] R1[21/300], Temp: 0.9880, Energy: -22.858568-0.087771j
[2025-07-30 05:09:41] [Iter 173/4650] R1[22/300], Temp: 0.9868, Energy: -24.178256-0.080027j
[2025-07-30 05:09:51] [Iter 174/4650] R1[23/300], Temp: 0.9856, Energy: -25.387784-0.076517j
[2025-07-30 05:10:01] [Iter 175/4650] R1[24/300], Temp: 0.9843, Energy: -26.860013-0.155208j
[2025-07-30 05:10:11] [Iter 176/4650] R1[25/300], Temp: 0.9830, Energy: -28.463173-0.125391j
[2025-07-30 05:10:21] [Iter 177/4650] R1[26/300], Temp: 0.9816, Energy: -29.993095-0.108357j
[2025-07-30 05:10:31] [Iter 178/4650] R1[27/300], Temp: 0.9801, Energy: -31.778656-0.169249j
[2025-07-30 05:10:41] [Iter 179/4650] R1[28/300], Temp: 0.9787, Energy: -33.534533-0.196871j
[2025-07-30 05:10:52] [Iter 180/4650] R1[29/300], Temp: 0.9771, Energy: -35.433874-0.349629j
[2025-07-30 05:11:02] [Iter 181/4650] R1[30/300], Temp: 0.9755, Energy: -37.140493-0.398588j
[2025-07-30 05:11:12] [Iter 182/4650] R1[31/300], Temp: 0.9739, Energy: -38.977240-0.447847j
[2025-07-30 05:11:22] [Iter 183/4650] R1[32/300], Temp: 0.9722, Energy: -40.660367-0.495843j
[2025-07-30 05:11:32] [Iter 184/4650] R1[33/300], Temp: 0.9704, Energy: -42.203624-0.488433j
[2025-07-30 05:11:42] [Iter 185/4650] R1[34/300], Temp: 0.9686, Energy: -43.554660-0.623241j
[2025-07-30 05:11:52] [Iter 186/4650] R1[35/300], Temp: 0.9668, Energy: -44.614067-0.672516j
[2025-07-30 05:12:02] [Iter 187/4650] R1[36/300], Temp: 0.9649, Energy: -45.660049-0.681331j
[2025-07-30 05:12:12] [Iter 188/4650] R1[37/300], Temp: 0.9629, Energy: -46.489863-0.730904j
[2025-07-30 05:12:23] [Iter 189/4650] R1[38/300], Temp: 0.9609, Energy: -47.134853-0.738801j
[2025-07-30 05:12:33] [Iter 190/4650] R1[39/300], Temp: 0.9589, Energy: -47.712381-0.707301j
[2025-07-30 05:12:43] [Iter 191/4650] R1[40/300], Temp: 0.9568, Energy: -48.205978-0.706490j
[2025-07-30 05:12:53] [Iter 192/4650] R1[41/300], Temp: 0.9546, Energy: -48.570407-0.651545j
[2025-07-30 05:13:03] [Iter 193/4650] R1[42/300], Temp: 0.9524, Energy: -49.004280-0.667288j
[2025-07-30 05:13:13] [Iter 194/4650] R1[43/300], Temp: 0.9502, Energy: -49.320990-0.563424j
[2025-07-30 05:13:23] [Iter 195/4650] R1[44/300], Temp: 0.9479, Energy: -49.707151-0.487292j
[2025-07-30 05:13:33] [Iter 196/4650] R1[45/300], Temp: 0.9455, Energy: -49.921168-0.429283j
[2025-07-30 05:13:44] [Iter 197/4650] R1[46/300], Temp: 0.9431, Energy: -50.161635-0.377931j
[2025-07-30 05:13:54] [Iter 198/4650] R1[47/300], Temp: 0.9407, Energy: -50.375696-0.266736j
[2025-07-30 05:14:04] [Iter 199/4650] R1[48/300], Temp: 0.9382, Energy: -50.573087-0.252199j
[2025-07-30 05:14:14] [Iter 200/4650] R1[49/300], Temp: 0.9356, Energy: -50.692275-0.210733j
[2025-07-30 05:14:24] [Iter 201/4650] R1[50/300], Temp: 0.9330, Energy: -50.897791-0.144647j
[2025-07-30 05:14:34] [Iter 202/4650] R1[51/300], Temp: 0.9304, Energy: -50.962567-0.097302j
[2025-07-30 05:14:44] [Iter 203/4650] R1[52/300], Temp: 0.9277, Energy: -51.137571-0.079450j
[2025-07-30 05:14:54] [Iter 204/4650] R1[53/300], Temp: 0.9249, Energy: -51.315124-0.053607j
[2025-07-30 05:15:05] [Iter 205/4650] R1[54/300], Temp: 0.9222, Energy: -51.498526-0.035628j
[2025-07-30 05:15:15] [Iter 206/4650] R1[55/300], Temp: 0.9193, Energy: -51.541293-0.014092j
[2025-07-30 05:15:25] [Iter 207/4650] R1[56/300], Temp: 0.9165, Energy: -51.666648+0.010079j
[2025-07-30 05:15:35] [Iter 208/4650] R1[57/300], Temp: 0.9135, Energy: -51.755495+0.005654j
[2025-07-30 05:15:45] [Iter 209/4650] R1[58/300], Temp: 0.9106, Energy: -51.799628+0.051349j
[2025-07-30 05:15:55] [Iter 210/4650] R1[59/300], Temp: 0.9076, Energy: -51.891799+0.046149j
[2025-07-30 05:16:05] [Iter 211/4650] R1[60/300], Temp: 0.9045, Energy: -52.018531+0.061605j
[2025-07-30 05:16:15] [Iter 212/4650] R1[61/300], Temp: 0.9014, Energy: -52.024907+0.072246j
[2025-07-30 05:16:26] [Iter 213/4650] R1[62/300], Temp: 0.8983, Energy: -52.144647+0.070398j
[2025-07-30 05:16:36] [Iter 214/4650] R1[63/300], Temp: 0.8951, Energy: -52.218830+0.090980j
[2025-07-30 05:16:46] [Iter 215/4650] R1[64/300], Temp: 0.8918, Energy: -52.359206+0.076320j
[2025-07-30 05:16:56] [Iter 216/4650] R1[65/300], Temp: 0.8886, Energy: -52.398538+0.098708j
[2025-07-30 05:17:06] [Iter 217/4650] R1[66/300], Temp: 0.8853, Energy: -52.565006+0.073599j
[2025-07-30 05:17:16] [Iter 218/4650] R1[67/300], Temp: 0.8819, Energy: -52.637584+0.107285j
[2025-07-30 05:17:26] [Iter 219/4650] R1[68/300], Temp: 0.8785, Energy: -52.648380+0.116047j
[2025-07-30 05:17:36] [Iter 220/4650] R1[69/300], Temp: 0.8751, Energy: -52.756993+0.095981j
[2025-07-30 05:17:47] [Iter 221/4650] R1[70/300], Temp: 0.8716, Energy: -52.865922+0.124436j
[2025-07-30 05:17:57] [Iter 222/4650] R1[71/300], Temp: 0.8680, Energy: -52.863636+0.105235j
[2025-07-30 05:18:07] [Iter 223/4650] R1[72/300], Temp: 0.8645, Energy: -52.997016+0.121020j
[2025-07-30 05:18:17] [Iter 224/4650] R1[73/300], Temp: 0.8609, Energy: -53.090092+0.096789j
[2025-07-30 05:18:27] [Iter 225/4650] R1[74/300], Temp: 0.8572, Energy: -53.030430+0.089241j
[2025-07-30 05:18:37] [Iter 226/4650] R1[75/300], Temp: 0.8536, Energy: -53.050924+0.098933j
[2025-07-30 05:18:47] [Iter 227/4650] R1[76/300], Temp: 0.8498, Energy: -53.154503+0.124936j
[2025-07-30 05:18:57] [Iter 228/4650] R1[77/300], Temp: 0.8461, Energy: -53.182058+0.103332j
[2025-07-30 05:19:08] [Iter 229/4650] R1[78/300], Temp: 0.8423, Energy: -53.284463+0.083561j
[2025-07-30 05:19:18] [Iter 230/4650] R1[79/300], Temp: 0.8384, Energy: -53.327832+0.092016j
[2025-07-30 05:19:28] [Iter 231/4650] R1[80/300], Temp: 0.8346, Energy: -53.338970+0.096299j
[2025-07-30 05:19:38] [Iter 232/4650] R1[81/300], Temp: 0.8307, Energy: -53.439244+0.085990j
[2025-07-30 05:19:48] [Iter 233/4650] R1[82/300], Temp: 0.8267, Energy: -53.439077+0.084707j
[2025-07-30 05:19:58] [Iter 234/4650] R1[83/300], Temp: 0.8227, Energy: -53.457460+0.087367j
[2025-07-30 05:20:08] [Iter 235/4650] R1[84/300], Temp: 0.8187, Energy: -53.484214+0.086908j
[2025-07-30 05:20:18] [Iter 236/4650] R1[85/300], Temp: 0.8147, Energy: -53.505723+0.058826j
[2025-07-30 05:20:29] [Iter 237/4650] R1[86/300], Temp: 0.8106, Energy: -53.559565+0.050196j
[2025-07-30 05:20:39] [Iter 238/4650] R1[87/300], Temp: 0.8065, Energy: -53.650627+0.073807j
[2025-07-30 05:20:49] [Iter 239/4650] R1[88/300], Temp: 0.8023, Energy: -53.658929+0.059020j
[2025-07-30 05:20:59] [Iter 240/4650] R1[89/300], Temp: 0.7981, Energy: -53.659078+0.047093j
[2025-07-30 05:21:09] [Iter 241/4650] R1[90/300], Temp: 0.7939, Energy: -53.741580+0.053175j
[2025-07-30 05:21:19] [Iter 242/4650] R1[91/300], Temp: 0.7896, Energy: -53.765945+0.038176j
[2025-07-30 05:21:29] [Iter 243/4650] R1[92/300], Temp: 0.7854, Energy: -53.806273+0.018171j
[2025-07-30 05:21:40] [Iter 244/4650] R1[93/300], Temp: 0.7810, Energy: -53.853328+0.022364j
[2025-07-30 05:21:50] [Iter 245/4650] R1[94/300], Temp: 0.7767, Energy: -53.797807+0.027048j
[2025-07-30 05:22:00] [Iter 246/4650] R1[95/300], Temp: 0.7723, Energy: -53.831267+0.037607j
[2025-07-30 05:22:10] [Iter 247/4650] R1[96/300], Temp: 0.7679, Energy: -53.883998+0.031452j
[2025-07-30 05:22:20] [Iter 248/4650] R1[97/300], Temp: 0.7635, Energy: -53.817619+0.040043j
[2025-07-30 05:22:30] [Iter 249/4650] R1[98/300], Temp: 0.7590, Energy: -53.941146+0.029058j
[2025-07-30 05:22:40] [Iter 250/4650] R1[99/300], Temp: 0.7545, Energy: -53.910347+0.034105j
[2025-07-30 05:22:50] [Iter 251/4650] R1[100/300], Temp: 0.7500, Energy: -53.992779+0.025265j
[2025-07-30 05:23:00] [Iter 252/4650] R1[101/300], Temp: 0.7455, Energy: -54.059454+0.025258j
[2025-07-30 05:23:11] [Iter 253/4650] R1[102/300], Temp: 0.7409, Energy: -54.084389+0.029013j
[2025-07-30 05:23:21] [Iter 254/4650] R1[103/300], Temp: 0.7363, Energy: -54.042697+0.028923j
[2025-07-30 05:23:31] [Iter 255/4650] R1[104/300], Temp: 0.7316, Energy: -54.061416+0.039683j
[2025-07-30 05:23:41] [Iter 256/4650] R1[105/300], Temp: 0.7270, Energy: -54.041215+0.021642j
[2025-07-30 05:23:51] [Iter 257/4650] R1[106/300], Temp: 0.7223, Energy: -54.051448+0.026249j
[2025-07-30 05:24:01] [Iter 258/4650] R1[107/300], Temp: 0.7176, Energy: -54.124434+0.038103j
[2025-07-30 05:24:11] [Iter 259/4650] R1[108/300], Temp: 0.7129, Energy: -54.126547+0.027046j
[2025-07-30 05:24:21] [Iter 260/4650] R1[109/300], Temp: 0.7081, Energy: -54.132564+0.017345j
[2025-07-30 05:24:32] [Iter 261/4650] R1[110/300], Temp: 0.7034, Energy: -54.133622+0.024787j
[2025-07-30 05:24:42] [Iter 262/4650] R1[111/300], Temp: 0.6986, Energy: -54.107166+0.013838j
[2025-07-30 05:24:52] [Iter 263/4650] R1[112/300], Temp: 0.6938, Energy: -54.150376-0.001447j
[2025-07-30 05:25:02] [Iter 264/4650] R1[113/300], Temp: 0.6889, Energy: -54.195517+0.025099j
[2025-07-30 05:25:12] [Iter 265/4650] R1[114/300], Temp: 0.6841, Energy: -54.176120+0.019146j
[2025-07-30 05:25:22] [Iter 266/4650] R1[115/300], Temp: 0.6792, Energy: -54.240112+0.002294j
[2025-07-30 05:25:32] [Iter 267/4650] R1[116/300], Temp: 0.6743, Energy: -54.181661+0.009760j
[2025-07-30 05:25:42] [Iter 268/4650] R1[117/300], Temp: 0.6694, Energy: -54.172013+0.000222j
[2025-07-30 05:25:53] [Iter 269/4650] R1[118/300], Temp: 0.6644, Energy: -54.214925+0.010589j
[2025-07-30 05:26:03] [Iter 270/4650] R1[119/300], Temp: 0.6595, Energy: -54.169418+0.003565j
[2025-07-30 05:26:13] [Iter 271/4650] R1[120/300], Temp: 0.6545, Energy: -54.230548-0.001679j
[2025-07-30 05:26:23] [Iter 272/4650] R1[121/300], Temp: 0.6495, Energy: -54.141901+0.007686j
[2025-07-30 05:26:33] [Iter 273/4650] R1[122/300], Temp: 0.6445, Energy: -54.248448+0.015797j
[2025-07-30 05:26:43] [Iter 274/4650] R1[123/300], Temp: 0.6395, Energy: -54.189945+0.010536j
[2025-07-30 05:26:53] [Iter 275/4650] R1[124/300], Temp: 0.6345, Energy: -54.205337-0.017328j
[2025-07-30 05:27:04] [Iter 276/4650] R1[125/300], Temp: 0.6294, Energy: -54.237993+0.002160j
[2025-07-30 05:27:14] [Iter 277/4650] R1[126/300], Temp: 0.6243, Energy: -54.257100-0.006905j
[2025-07-30 05:27:24] [Iter 278/4650] R1[127/300], Temp: 0.6193, Energy: -54.245248-0.006458j
[2025-07-30 05:27:34] [Iter 279/4650] R1[128/300], Temp: 0.6142, Energy: -54.260557+0.007182j
[2025-07-30 05:27:44] [Iter 280/4650] R1[129/300], Temp: 0.6091, Energy: -54.283219+0.018018j
[2025-07-30 05:27:54] [Iter 281/4650] R1[130/300], Temp: 0.6040, Energy: -54.238646+0.005199j
[2025-07-30 05:28:04] [Iter 282/4650] R1[131/300], Temp: 0.5988, Energy: -54.296977+0.002980j
[2025-07-30 05:28:15] [Iter 283/4650] R1[132/300], Temp: 0.5937, Energy: -54.278809+0.007315j
[2025-07-30 05:28:25] [Iter 284/4650] R1[133/300], Temp: 0.5885, Energy: -54.249156-0.003712j
[2025-07-30 05:28:35] [Iter 285/4650] R1[134/300], Temp: 0.5834, Energy: -54.232694-0.003090j
[2025-07-30 05:28:45] [Iter 286/4650] R1[135/300], Temp: 0.5782, Energy: -54.257276+0.006944j
[2025-07-30 05:28:55] [Iter 287/4650] R1[136/300], Temp: 0.5730, Energy: -54.280714+0.004496j
[2025-07-30 05:29:05] [Iter 288/4650] R1[137/300], Temp: 0.5679, Energy: -54.290602-0.012514j
[2025-07-30 05:29:15] [Iter 289/4650] R1[138/300], Temp: 0.5627, Energy: -54.236495-0.006014j
[2025-07-30 05:29:25] [Iter 290/4650] R1[139/300], Temp: 0.5575, Energy: -54.226365+0.008943j
[2025-07-30 05:29:36] [Iter 291/4650] R1[140/300], Temp: 0.5523, Energy: -54.270728-0.002571j
[2025-07-30 05:29:46] [Iter 292/4650] R1[141/300], Temp: 0.5471, Energy: -54.314797+0.004257j
[2025-07-30 05:29:56] [Iter 293/4650] R1[142/300], Temp: 0.5418, Energy: -54.261323-0.003789j
[2025-07-30 05:30:06] [Iter 294/4650] R1[143/300], Temp: 0.5366, Energy: -54.281526+0.003989j
[2025-07-30 05:30:16] [Iter 295/4650] R1[144/300], Temp: 0.5314, Energy: -54.284708-0.003481j
[2025-07-30 05:30:26] [Iter 296/4650] R1[145/300], Temp: 0.5262, Energy: -54.290912+0.012271j
[2025-07-30 05:30:36] [Iter 297/4650] R1[146/300], Temp: 0.5209, Energy: -54.226201-0.003061j
[2025-07-30 05:30:46] [Iter 298/4650] R1[147/300], Temp: 0.5157, Energy: -54.338951-0.013560j
[2025-07-30 05:30:57] [Iter 299/4650] R1[148/300], Temp: 0.5105, Energy: -54.291421+0.005214j
[2025-07-30 05:31:07] [Iter 300/4650] R1[149/300], Temp: 0.5052, Energy: -54.295186+0.006396j
[2025-07-30 05:31:17] [Iter 301/4650] R1[150/300], Temp: 0.5000, Energy: -54.315510+0.007760j
[2025-07-30 05:31:27] [Iter 302/4650] R1[151/300], Temp: 0.4948, Energy: -54.268226+0.007860j
[2025-07-30 05:31:37] [Iter 303/4650] R1[152/300], Temp: 0.4895, Energy: -54.308841-0.006648j
[2025-07-30 05:31:47] [Iter 304/4650] R1[153/300], Temp: 0.4843, Energy: -54.257080+0.002452j
[2025-07-30 05:31:57] [Iter 305/4650] R1[154/300], Temp: 0.4791, Energy: -54.239713+0.004301j
[2025-07-30 05:32:07] [Iter 306/4650] R1[155/300], Temp: 0.4738, Energy: -54.300130+0.004451j
[2025-07-30 05:32:18] [Iter 307/4650] R1[156/300], Temp: 0.4686, Energy: -54.345603+0.005498j
[2025-07-30 05:32:28] [Iter 308/4650] R1[157/300], Temp: 0.4634, Energy: -54.331723-0.000363j
[2025-07-30 05:32:38] [Iter 309/4650] R1[158/300], Temp: 0.4582, Energy: -54.325756+0.006125j
[2025-07-30 05:32:48] [Iter 310/4650] R1[159/300], Temp: 0.4529, Energy: -54.343410+0.007376j
[2025-07-30 05:32:58] [Iter 311/4650] R1[160/300], Temp: 0.4477, Energy: -54.356585-0.001166j
[2025-07-30 05:33:08] [Iter 312/4650] R1[161/300], Temp: 0.4425, Energy: -54.371951-0.005923j
[2025-07-30 05:33:18] [Iter 313/4650] R1[162/300], Temp: 0.4373, Energy: -54.387722-0.003241j
[2025-07-30 05:33:28] [Iter 314/4650] R1[163/300], Temp: 0.4321, Energy: -54.319809-0.004977j
[2025-07-30 05:33:39] [Iter 315/4650] R1[164/300], Temp: 0.4270, Energy: -54.346777-0.001623j
[2025-07-30 05:33:49] [Iter 316/4650] R1[165/300], Temp: 0.4218, Energy: -54.381220-0.000861j
[2025-07-30 05:33:59] [Iter 317/4650] R1[166/300], Temp: 0.4166, Energy: -54.329051-0.000774j
[2025-07-30 05:34:09] [Iter 318/4650] R1[167/300], Temp: 0.4115, Energy: -54.361062-0.005228j
[2025-07-30 05:34:19] [Iter 319/4650] R1[168/300], Temp: 0.4063, Energy: -54.298118+0.012360j
[2025-07-30 05:34:29] [Iter 320/4650] R1[169/300], Temp: 0.4012, Energy: -54.321235-0.001865j
[2025-07-30 05:34:39] [Iter 321/4650] R1[170/300], Temp: 0.3960, Energy: -54.461770-0.000725j
[2025-07-30 05:34:49] [Iter 322/4650] R1[171/300], Temp: 0.3909, Energy: -54.415917-0.001066j
[2025-07-30 05:35:00] [Iter 323/4650] R1[172/300], Temp: 0.3858, Energy: -54.384841+0.001780j
[2025-07-30 05:35:10] [Iter 324/4650] R1[173/300], Temp: 0.3807, Energy: -54.415099+0.004704j
[2025-07-30 05:35:20] [Iter 325/4650] R1[174/300], Temp: 0.3757, Energy: -54.438108+0.000830j
[2025-07-30 05:35:30] [Iter 326/4650] R1[175/300], Temp: 0.3706, Energy: -54.454112-0.000094j
[2025-07-30 05:35:40] [Iter 327/4650] R1[176/300], Temp: 0.3655, Energy: -54.407080+0.003600j
[2025-07-30 05:35:50] [Iter 328/4650] R1[177/300], Temp: 0.3605, Energy: -54.405106-0.000677j
[2025-07-30 05:36:00] [Iter 329/4650] R1[178/300], Temp: 0.3555, Energy: -54.358322+0.000209j
[2025-07-30 05:36:10] [Iter 330/4650] R1[179/300], Temp: 0.3505, Energy: -54.349097+0.004056j
[2025-07-30 05:36:21] [Iter 331/4650] R1[180/300], Temp: 0.3455, Energy: -54.339337+0.002047j
[2025-07-30 05:36:31] [Iter 332/4650] R1[181/300], Temp: 0.3405, Energy: -54.310365-0.000569j
[2025-07-30 05:36:41] [Iter 333/4650] R1[182/300], Temp: 0.3356, Energy: -54.278984+0.009765j
[2025-07-30 05:36:51] [Iter 334/4650] R1[183/300], Temp: 0.3306, Energy: -54.347184+0.010182j
[2025-07-30 05:37:01] [Iter 335/4650] R1[184/300], Temp: 0.3257, Energy: -54.377383+0.008301j
[2025-07-30 05:37:11] [Iter 336/4650] R1[185/300], Temp: 0.3208, Energy: -54.389187+0.003507j
[2025-07-30 05:37:21] [Iter 337/4650] R1[186/300], Temp: 0.3159, Energy: -54.451814-0.002198j
[2025-07-30 05:37:31] [Iter 338/4650] R1[187/300], Temp: 0.3111, Energy: -54.496210+0.003762j
[2025-07-30 05:37:42] [Iter 339/4650] R1[188/300], Temp: 0.3062, Energy: -54.455804+0.003898j
[2025-07-30 05:37:52] [Iter 340/4650] R1[189/300], Temp: 0.3014, Energy: -54.504302-0.012136j
[2025-07-30 05:38:02] [Iter 341/4650] R1[190/300], Temp: 0.2966, Energy: -54.442906+0.002006j
[2025-07-30 05:38:12] [Iter 342/4650] R1[191/300], Temp: 0.2919, Energy: -54.452066-0.000051j
[2025-07-30 05:38:22] [Iter 343/4650] R1[192/300], Temp: 0.2871, Energy: -54.405737-0.006181j
[2025-07-30 05:38:32] [Iter 344/4650] R1[193/300], Temp: 0.2824, Energy: -54.432434+0.003104j
[2025-07-30 05:38:42] [Iter 345/4650] R1[194/300], Temp: 0.2777, Energy: -54.418505+0.007881j
[2025-07-30 05:38:52] [Iter 346/4650] R1[195/300], Temp: 0.2730, Energy: -54.402804-0.001240j
[2025-07-30 05:39:03] [Iter 347/4650] R1[196/300], Temp: 0.2684, Energy: -54.414529+0.003232j
[2025-07-30 05:39:13] [Iter 348/4650] R1[197/300], Temp: 0.2637, Energy: -54.422733-0.000213j
[2025-07-30 05:39:23] [Iter 349/4650] R1[198/300], Temp: 0.2591, Energy: -54.434373+0.003007j
[2025-07-30 05:39:33] [Iter 350/4650] R1[199/300], Temp: 0.2545, Energy: -54.401099-0.009138j
[2025-07-30 05:39:43] [Iter 351/4650] R1[200/300], Temp: 0.2500, Energy: -54.395168-0.000013j
[2025-07-30 05:39:53] [Iter 352/4650] R1[201/300], Temp: 0.2455, Energy: -54.435780-0.007886j
[2025-07-30 05:40:03] [Iter 353/4650] R1[202/300], Temp: 0.2410, Energy: -54.415707-0.002737j
[2025-07-30 05:40:14] [Iter 354/4650] R1[203/300], Temp: 0.2365, Energy: -54.405047-0.009951j
[2025-07-30 05:40:24] [Iter 355/4650] R1[204/300], Temp: 0.2321, Energy: -54.402660+0.008156j
[2025-07-30 05:40:34] [Iter 356/4650] R1[205/300], Temp: 0.2277, Energy: -54.409816+0.001269j
[2025-07-30 05:40:44] [Iter 357/4650] R1[206/300], Temp: 0.2233, Energy: -54.457130-0.005260j
[2025-07-30 05:40:54] [Iter 358/4650] R1[207/300], Temp: 0.2190, Energy: -54.382796+0.003949j
[2025-07-30 05:41:04] [Iter 359/4650] R1[208/300], Temp: 0.2146, Energy: -54.463744-0.007860j
[2025-07-30 05:41:14] [Iter 360/4650] R1[209/300], Temp: 0.2104, Energy: -54.449758-0.001752j
[2025-07-30 05:41:25] [Iter 361/4650] R1[210/300], Temp: 0.2061, Energy: -54.449010+0.000961j
[2025-07-30 05:41:35] [Iter 362/4650] R1[211/300], Temp: 0.2019, Energy: -54.452518-0.002247j
[2025-07-30 05:41:45] [Iter 363/4650] R1[212/300], Temp: 0.1977, Energy: -54.438701+0.001460j
[2025-07-30 05:41:55] [Iter 364/4650] R1[213/300], Temp: 0.1935, Energy: -54.427801+0.000322j
[2025-07-30 05:42:05] [Iter 365/4650] R1[214/300], Temp: 0.1894, Energy: -54.494040-0.007275j
[2025-07-30 05:42:15] [Iter 366/4650] R1[215/300], Temp: 0.1853, Energy: -54.431102+0.000696j
[2025-07-30 05:42:25] [Iter 367/4650] R1[216/300], Temp: 0.1813, Energy: -54.414337+0.005070j
[2025-07-30 05:42:35] [Iter 368/4650] R1[217/300], Temp: 0.1773, Energy: -54.422644-0.000613j
[2025-07-30 05:42:46] [Iter 369/4650] R1[218/300], Temp: 0.1733, Energy: -54.462663-0.000266j
[2025-07-30 05:42:56] [Iter 370/4650] R1[219/300], Temp: 0.1693, Energy: -54.504769-0.004924j
[2025-07-30 05:43:06] [Iter 371/4650] R1[220/300], Temp: 0.1654, Energy: -54.450014-0.000296j
[2025-07-30 05:43:16] [Iter 372/4650] R1[221/300], Temp: 0.1616, Energy: -54.483921-0.002538j
[2025-07-30 05:43:26] [Iter 373/4650] R1[222/300], Temp: 0.1577, Energy: -54.495030+0.004416j
[2025-07-30 05:43:36] [Iter 374/4650] R1[223/300], Temp: 0.1539, Energy: -54.510314-0.011157j
[2025-07-30 05:43:46] [Iter 375/4650] R1[224/300], Temp: 0.1502, Energy: -54.436681-0.003391j
[2025-07-30 05:43:56] [Iter 376/4650] R1[225/300], Temp: 0.1464, Energy: -54.430447-0.004152j
[2025-07-30 05:44:07] [Iter 377/4650] R1[226/300], Temp: 0.1428, Energy: -54.456671-0.003960j
[2025-07-30 05:44:17] [Iter 378/4650] R1[227/300], Temp: 0.1391, Energy: -54.485684-0.002494j
[2025-07-30 05:44:27] [Iter 379/4650] R1[228/300], Temp: 0.1355, Energy: -54.520612+0.004102j
[2025-07-30 05:44:37] [Iter 380/4650] R1[229/300], Temp: 0.1320, Energy: -54.520067+0.001039j
[2025-07-30 05:44:47] [Iter 381/4650] R1[230/300], Temp: 0.1284, Energy: -54.469944+0.002022j
[2025-07-30 05:44:57] [Iter 382/4650] R1[231/300], Temp: 0.1249, Energy: -54.439532-0.006708j
[2025-07-30 05:45:07] [Iter 383/4650] R1[232/300], Temp: 0.1215, Energy: -54.449419+0.001064j
[2025-07-30 05:45:17] [Iter 384/4650] R1[233/300], Temp: 0.1181, Energy: -54.444250-0.001708j
[2025-07-30 05:45:28] [Iter 385/4650] R1[234/300], Temp: 0.1147, Energy: -54.428910+0.000064j
[2025-07-30 05:45:38] [Iter 386/4650] R1[235/300], Temp: 0.1114, Energy: -54.472227-0.003021j
[2025-07-30 05:45:48] [Iter 387/4650] R1[236/300], Temp: 0.1082, Energy: -54.473858+0.005427j
[2025-07-30 05:45:58] [Iter 388/4650] R1[237/300], Temp: 0.1049, Energy: -54.481495-0.000338j
[2025-07-30 05:46:08] [Iter 389/4650] R1[238/300], Temp: 0.1017, Energy: -54.414583-0.001512j
[2025-07-30 05:46:18] [Iter 390/4650] R1[239/300], Temp: 0.0986, Energy: -54.450883-0.000010j
[2025-07-30 05:46:28] [Iter 391/4650] R1[240/300], Temp: 0.0955, Energy: -54.412439-0.001401j
[2025-07-30 05:46:38] [Iter 392/4650] R1[241/300], Temp: 0.0924, Energy: -54.439189+0.007655j
[2025-07-30 05:46:49] [Iter 393/4650] R1[242/300], Temp: 0.0894, Energy: -54.423836+0.008269j
[2025-07-30 05:46:59] [Iter 394/4650] R1[243/300], Temp: 0.0865, Energy: -54.380496+0.005338j
[2025-07-30 05:47:09] [Iter 395/4650] R1[244/300], Temp: 0.0835, Energy: -54.346892-0.002421j
[2025-07-30 05:47:19] [Iter 396/4650] R1[245/300], Temp: 0.0807, Energy: -54.389344-0.000072j
[2025-07-30 05:47:29] [Iter 397/4650] R1[246/300], Temp: 0.0778, Energy: -54.394432-0.001067j
[2025-07-30 05:47:39] [Iter 398/4650] R1[247/300], Temp: 0.0751, Energy: -54.438881-0.001981j
[2025-07-30 05:47:49] [Iter 399/4650] R1[248/300], Temp: 0.0723, Energy: -54.536196-0.003408j
[2025-07-30 05:48:00] [Iter 400/4650] R1[249/300], Temp: 0.0696, Energy: -54.547793-0.004871j
[2025-07-30 05:48:10] [Iter 401/4650] R1[250/300], Temp: 0.0670, Energy: -54.593938+0.001694j
[2025-07-30 05:48:20] [Iter 402/4650] R1[251/300], Temp: 0.0644, Energy: -54.529342+0.003219j
[2025-07-30 05:48:30] [Iter 403/4650] R1[252/300], Temp: 0.0618, Energy: -54.500329-0.000388j
[2025-07-30 05:48:40] [Iter 404/4650] R1[253/300], Temp: 0.0593, Energy: -54.472478-0.003825j
[2025-07-30 05:48:50] [Iter 405/4650] R1[254/300], Temp: 0.0569, Energy: -54.517139-0.001146j
[2025-07-30 05:49:00] [Iter 406/4650] R1[255/300], Temp: 0.0545, Energy: -54.501120-0.001321j
[2025-07-30 05:49:11] [Iter 407/4650] R1[256/300], Temp: 0.0521, Energy: -54.436333-0.002696j
[2025-07-30 05:49:21] [Iter 408/4650] R1[257/300], Temp: 0.0498, Energy: -54.460589-0.007801j
[2025-07-30 05:49:31] [Iter 409/4650] R1[258/300], Temp: 0.0476, Energy: -54.446976+0.001004j
[2025-07-30 05:49:41] [Iter 410/4650] R1[259/300], Temp: 0.0454, Energy: -54.465768+0.005886j
[2025-07-30 05:49:51] [Iter 411/4650] R1[260/300], Temp: 0.0432, Energy: -54.441001+0.005081j
[2025-07-30 05:50:01] [Iter 412/4650] R1[261/300], Temp: 0.0411, Energy: -54.417140+0.001004j
[2025-07-30 05:50:11] [Iter 413/4650] R1[262/300], Temp: 0.0391, Energy: -54.374133+0.000829j
[2025-07-30 05:50:21] [Iter 414/4650] R1[263/300], Temp: 0.0371, Energy: -54.432670-0.001093j
[2025-07-30 05:50:31] [Iter 415/4650] R1[264/300], Temp: 0.0351, Energy: -54.445459+0.002231j
[2025-07-30 05:50:42] [Iter 416/4650] R1[265/300], Temp: 0.0332, Energy: -54.468919-0.000463j
[2025-07-30 05:50:52] [Iter 417/4650] R1[266/300], Temp: 0.0314, Energy: -54.378412+0.003501j
[2025-07-30 05:51:02] [Iter 418/4650] R1[267/300], Temp: 0.0296, Energy: -54.366423+0.001241j
[2025-07-30 05:51:12] [Iter 419/4650] R1[268/300], Temp: 0.0278, Energy: -54.439602-0.007791j
[2025-07-30 05:51:22] [Iter 420/4650] R1[269/300], Temp: 0.0261, Energy: -54.500082+0.001512j
[2025-07-30 05:51:32] [Iter 421/4650] R1[270/300], Temp: 0.0245, Energy: -54.445745+0.010683j
[2025-07-30 05:51:42] [Iter 422/4650] R1[271/300], Temp: 0.0229, Energy: -54.409236+0.003231j
[2025-07-30 05:51:52] [Iter 423/4650] R1[272/300], Temp: 0.0213, Energy: -54.454620+0.002264j
[2025-07-30 05:52:03] [Iter 424/4650] R1[273/300], Temp: 0.0199, Energy: -54.455547-0.003222j
[2025-07-30 05:52:13] [Iter 425/4650] R1[274/300], Temp: 0.0184, Energy: -54.451300-0.001687j
[2025-07-30 05:52:23] [Iter 426/4650] R1[275/300], Temp: 0.0170, Energy: -54.481393-0.001574j
[2025-07-30 05:52:33] [Iter 427/4650] R1[276/300], Temp: 0.0157, Energy: -54.501166+0.006551j
[2025-07-30 05:52:43] [Iter 428/4650] R1[277/300], Temp: 0.0144, Energy: -54.498131-0.001663j
[2025-07-30 05:52:53] [Iter 429/4650] R1[278/300], Temp: 0.0132, Energy: -54.464942-0.001467j
[2025-07-30 05:53:03] [Iter 430/4650] R1[279/300], Temp: 0.0120, Energy: -54.462841+0.003506j
[2025-07-30 05:53:14] [Iter 431/4650] R1[280/300], Temp: 0.0109, Energy: -54.456955-0.008896j
[2025-07-30 05:53:24] [Iter 432/4650] R1[281/300], Temp: 0.0099, Energy: -54.429735+0.002993j
[2025-07-30 05:53:34] [Iter 433/4650] R1[282/300], Temp: 0.0089, Energy: -54.442532-0.001412j
[2025-07-30 05:53:44] [Iter 434/4650] R1[283/300], Temp: 0.0079, Energy: -54.482912-0.005197j
[2025-07-30 05:53:54] [Iter 435/4650] R1[284/300], Temp: 0.0070, Energy: -54.461033-0.000387j
[2025-07-30 05:54:04] [Iter 436/4650] R1[285/300], Temp: 0.0062, Energy: -54.501215-0.006115j
[2025-07-30 05:54:14] [Iter 437/4650] R1[286/300], Temp: 0.0054, Energy: -54.451902-0.005594j
[2025-07-30 05:54:25] [Iter 438/4650] R1[287/300], Temp: 0.0046, Energy: -54.500316+0.004877j
[2025-07-30 05:54:35] [Iter 439/4650] R1[288/300], Temp: 0.0039, Energy: -54.511704-0.005833j
[2025-07-30 05:54:45] [Iter 440/4650] R1[289/300], Temp: 0.0033, Energy: -54.560570+0.003860j
[2025-07-30 05:54:55] [Iter 441/4650] R1[290/300], Temp: 0.0027, Energy: -54.539646-0.002332j
[2025-07-30 05:55:05] [Iter 442/4650] R1[291/300], Temp: 0.0022, Energy: -54.496206-0.004677j
[2025-07-30 05:55:15] [Iter 443/4650] R1[292/300], Temp: 0.0018, Energy: -54.444530+0.001213j
[2025-07-30 05:55:25] [Iter 444/4650] R1[293/300], Temp: 0.0013, Energy: -54.486272+0.002950j
[2025-07-30 05:55:35] [Iter 445/4650] R1[294/300], Temp: 0.0010, Energy: -54.487630-0.002010j
[2025-07-30 05:55:46] [Iter 446/4650] R1[295/300], Temp: 0.0007, Energy: -54.535319+0.005434j
[2025-07-30 05:55:56] [Iter 447/4650] R1[296/300], Temp: 0.0004, Energy: -54.556876-0.001023j
[2025-07-30 05:56:06] [Iter 448/4650] R1[297/300], Temp: 0.0002, Energy: -54.510662+0.000641j
[2025-07-30 05:56:16] [Iter 449/4650] R1[298/300], Temp: 0.0001, Energy: -54.570526+0.001680j
[2025-07-30 05:56:26] [Iter 450/4650] R1[299/300], Temp: 0.0000, Energy: -54.516885-0.010125j
[2025-07-30 05:56:26] RESTART #2 | Period: 600
[2025-07-30 05:56:36] [Iter 451/4650] R2[0/600], Temp: 1.0000, Energy: -54.460943-0.006150j
[2025-07-30 05:56:46] [Iter 452/4650] R2[1/600], Temp: 1.0000, Energy: -54.417688-0.002161j
[2025-07-30 05:56:56] [Iter 453/4650] R2[2/600], Temp: 1.0000, Energy: -54.431964-0.003328j
[2025-07-30 05:57:06] [Iter 454/4650] R2[3/600], Temp: 0.9999, Energy: -54.457457+0.000435j
[2025-07-30 05:57:17] [Iter 455/4650] R2[4/600], Temp: 0.9999, Energy: -54.461182+0.001664j
[2025-07-30 05:57:27] [Iter 456/4650] R2[5/600], Temp: 0.9998, Energy: -54.408576-0.004091j
[2025-07-30 05:57:37] [Iter 457/4650] R2[6/600], Temp: 0.9998, Energy: -54.429098+0.003467j
[2025-07-30 05:57:47] [Iter 458/4650] R2[7/600], Temp: 0.9997, Energy: -54.462798+0.004278j
[2025-07-30 05:57:57] [Iter 459/4650] R2[8/600], Temp: 0.9996, Energy: -54.444787-0.000573j
[2025-07-30 05:58:07] [Iter 460/4650] R2[9/600], Temp: 0.9994, Energy: -54.418947+0.001174j
[2025-07-30 05:58:17] [Iter 461/4650] R2[10/600], Temp: 0.9993, Energy: -54.469136-0.000216j
[2025-07-30 05:58:27] [Iter 462/4650] R2[11/600], Temp: 0.9992, Energy: -54.445317+0.000444j
[2025-07-30 05:58:38] [Iter 463/4650] R2[12/600], Temp: 0.9990, Energy: -54.519531-0.006546j
[2025-07-30 05:58:48] [Iter 464/4650] R2[13/600], Temp: 0.9988, Energy: -54.486811-0.005694j
[2025-07-30 05:58:58] [Iter 465/4650] R2[14/600], Temp: 0.9987, Energy: -54.510889+0.005249j
[2025-07-30 05:59:08] [Iter 466/4650] R2[15/600], Temp: 0.9985, Energy: -54.538783+0.001037j
[2025-07-30 05:59:18] [Iter 467/4650] R2[16/600], Temp: 0.9982, Energy: -54.467938-0.005393j
[2025-07-30 05:59:28] [Iter 468/4650] R2[17/600], Temp: 0.9980, Energy: -54.492620+0.002262j
[2025-07-30 05:59:38] [Iter 469/4650] R2[18/600], Temp: 0.9978, Energy: -54.482782-0.002332j
[2025-07-30 05:59:48] [Iter 470/4650] R2[19/600], Temp: 0.9975, Energy: -54.485032+0.001213j
[2025-07-30 05:59:59] [Iter 471/4650] R2[20/600], Temp: 0.9973, Energy: -54.448283-0.006743j
[2025-07-30 06:00:09] [Iter 472/4650] R2[21/600], Temp: 0.9970, Energy: -54.467260-0.004830j
[2025-07-30 06:00:19] [Iter 473/4650] R2[22/600], Temp: 0.9967, Energy: -54.462944+0.003711j
[2025-07-30 06:00:29] [Iter 474/4650] R2[23/600], Temp: 0.9964, Energy: -54.419463-0.000392j
[2025-07-30 06:00:39] [Iter 475/4650] R2[24/600], Temp: 0.9961, Energy: -54.424363-0.001327j
[2025-07-30 06:00:49] [Iter 476/4650] R2[25/600], Temp: 0.9957, Energy: -54.371442-0.004361j
[2025-07-30 06:00:59] [Iter 477/4650] R2[26/600], Temp: 0.9954, Energy: -54.519770-0.004199j
[2025-07-30 06:01:10] [Iter 478/4650] R2[27/600], Temp: 0.9950, Energy: -54.495729-0.006277j
[2025-07-30 06:01:20] [Iter 479/4650] R2[28/600], Temp: 0.9946, Energy: -54.481613+0.000453j
[2025-07-30 06:01:30] [Iter 480/4650] R2[29/600], Temp: 0.9942, Energy: -54.544732+0.000203j
[2025-07-30 06:01:40] [Iter 481/4650] R2[30/600], Temp: 0.9938, Energy: -54.579305+0.001231j
[2025-07-30 06:01:50] [Iter 482/4650] R2[31/600], Temp: 0.9934, Energy: -54.613013+0.001741j
[2025-07-30 06:02:00] [Iter 483/4650] R2[32/600], Temp: 0.9930, Energy: -54.590361+0.000436j
[2025-07-30 06:02:10] [Iter 484/4650] R2[33/600], Temp: 0.9926, Energy: -54.567977+0.003901j
[2025-07-30 06:02:20] [Iter 485/4650] R2[34/600], Temp: 0.9921, Energy: -54.548543-0.005045j
[2025-07-30 06:02:30] [Iter 486/4650] R2[35/600], Temp: 0.9916, Energy: -54.532393+0.002454j
[2025-07-30 06:02:41] [Iter 487/4650] R2[36/600], Temp: 0.9911, Energy: -54.542832-0.006937j
[2025-07-30 06:02:51] [Iter 488/4650] R2[37/600], Temp: 0.9906, Energy: -54.560741+0.001899j
[2025-07-30 06:03:01] [Iter 489/4650] R2[38/600], Temp: 0.9901, Energy: -54.556687+0.003503j
[2025-07-30 06:03:11] [Iter 490/4650] R2[39/600], Temp: 0.9896, Energy: -54.582151-0.001460j
[2025-07-30 06:03:21] [Iter 491/4650] R2[40/600], Temp: 0.9891, Energy: -54.551048-0.001427j
[2025-07-30 06:03:31] [Iter 492/4650] R2[41/600], Temp: 0.9885, Energy: -54.528880-0.004478j
[2025-07-30 06:03:41] [Iter 493/4650] R2[42/600], Temp: 0.9880, Energy: -54.523685+0.000220j
[2025-07-30 06:03:51] [Iter 494/4650] R2[43/600], Temp: 0.9874, Energy: -54.570758-0.003677j
[2025-07-30 06:04:02] [Iter 495/4650] R2[44/600], Temp: 0.9868, Energy: -54.526564+0.001008j
[2025-07-30 06:04:12] [Iter 496/4650] R2[45/600], Temp: 0.9862, Energy: -54.514627-0.003431j
[2025-07-30 06:04:22] [Iter 497/4650] R2[46/600], Temp: 0.9856, Energy: -54.479904+0.011864j
[2025-07-30 06:04:32] [Iter 498/4650] R2[47/600], Temp: 0.9849, Energy: -54.493146+0.004457j
[2025-07-30 06:04:42] [Iter 499/4650] R2[48/600], Temp: 0.9843, Energy: -54.548852+0.006077j
[2025-07-30 06:04:52] [Iter 500/4650] R2[49/600], Temp: 0.9836, Energy: -54.518183+0.005329j
[2025-07-30 06:04:52] ✓ Checkpoint saved: checkpoint_iter_000500.pkl
[2025-07-30 06:05:02] [Iter 501/4650] R2[50/600], Temp: 0.9830, Energy: -54.538009+0.000262j
[2025-07-30 06:05:12] [Iter 502/4650] R2[51/600], Temp: 0.9823, Energy: -54.604358+0.002050j
[2025-07-30 06:05:23] [Iter 503/4650] R2[52/600], Temp: 0.9816, Energy: -54.608370+0.003535j
[2025-07-30 06:05:33] [Iter 504/4650] R2[53/600], Temp: 0.9809, Energy: -54.609349+0.002637j
[2025-07-30 06:05:43] [Iter 505/4650] R2[54/600], Temp: 0.9801, Energy: -54.560779+0.007011j
[2025-07-30 06:05:53] [Iter 506/4650] R2[55/600], Temp: 0.9794, Energy: -54.547647+0.006318j
[2025-07-30 06:06:03] [Iter 507/4650] R2[56/600], Temp: 0.9787, Energy: -54.557784-0.003657j
[2025-07-30 06:06:13] [Iter 508/4650] R2[57/600], Temp: 0.9779, Energy: -54.519342-0.000093j
[2025-07-30 06:06:23] [Iter 509/4650] R2[58/600], Temp: 0.9771, Energy: -54.576310+0.003295j
[2025-07-30 06:06:33] [Iter 510/4650] R2[59/600], Temp: 0.9763, Energy: -54.542759+0.000946j
[2025-07-30 06:06:44] [Iter 511/4650] R2[60/600], Temp: 0.9755, Energy: -54.555968-0.003728j
[2025-07-30 06:06:54] [Iter 512/4650] R2[61/600], Temp: 0.9747, Energy: -54.545176+0.003002j
[2025-07-30 06:07:04] [Iter 513/4650] R2[62/600], Temp: 0.9739, Energy: -54.522275+0.005174j
[2025-07-30 06:07:14] [Iter 514/4650] R2[63/600], Temp: 0.9730, Energy: -54.538209+0.002050j
[2025-07-30 06:07:24] [Iter 515/4650] R2[64/600], Temp: 0.9722, Energy: -54.492329+0.002545j
[2025-07-30 06:07:34] [Iter 516/4650] R2[65/600], Temp: 0.9713, Energy: -54.511907-0.006563j
[2025-07-30 06:07:44] [Iter 517/4650] R2[66/600], Temp: 0.9704, Energy: -54.470158+0.003881j
[2025-07-30 06:07:54] [Iter 518/4650] R2[67/600], Temp: 0.9695, Energy: -54.501152+0.001495j
[2025-07-30 06:08:05] [Iter 519/4650] R2[68/600], Temp: 0.9686, Energy: -54.491578-0.000597j
[2025-07-30 06:08:15] [Iter 520/4650] R2[69/600], Temp: 0.9677, Energy: -54.492770+0.000883j
[2025-07-30 06:08:25] [Iter 521/4650] R2[70/600], Temp: 0.9668, Energy: -54.527705-0.002901j
[2025-07-30 06:08:35] [Iter 522/4650] R2[71/600], Temp: 0.9658, Energy: -54.491389-0.002057j
[2025-07-30 06:08:45] [Iter 523/4650] R2[72/600], Temp: 0.9649, Energy: -54.526195-0.002998j
[2025-07-30 06:08:55] [Iter 524/4650] R2[73/600], Temp: 0.9639, Energy: -54.496831+0.002416j
[2025-07-30 06:09:05] [Iter 525/4650] R2[74/600], Temp: 0.9629, Energy: -54.480745-0.003183j
[2025-07-30 06:09:16] [Iter 526/4650] R2[75/600], Temp: 0.9619, Energy: -54.497507-0.002103j
[2025-07-30 06:09:26] [Iter 527/4650] R2[76/600], Temp: 0.9609, Energy: -54.510615+0.002054j
[2025-07-30 06:09:36] [Iter 528/4650] R2[77/600], Temp: 0.9599, Energy: -54.517254-0.003754j
[2025-07-30 06:09:46] [Iter 529/4650] R2[78/600], Temp: 0.9589, Energy: -54.528544-0.002916j
[2025-07-30 06:09:56] [Iter 530/4650] R2[79/600], Temp: 0.9578, Energy: -54.598073+0.004074j
[2025-07-30 06:10:06] [Iter 531/4650] R2[80/600], Temp: 0.9568, Energy: -54.597284-0.000755j
[2025-07-30 06:10:16] [Iter 532/4650] R2[81/600], Temp: 0.9557, Energy: -54.557526-0.004570j
[2025-07-30 06:10:26] [Iter 533/4650] R2[82/600], Temp: 0.9546, Energy: -54.542331-0.003964j
[2025-07-30 06:10:36] [Iter 534/4650] R2[83/600], Temp: 0.9535, Energy: -54.569563+0.003743j
[2025-07-30 06:10:47] [Iter 535/4650] R2[84/600], Temp: 0.9524, Energy: -54.577996+0.003430j
[2025-07-30 06:10:57] [Iter 536/4650] R2[85/600], Temp: 0.9513, Energy: -54.594902+0.001340j
[2025-07-30 06:11:07] [Iter 537/4650] R2[86/600], Temp: 0.9502, Energy: -54.564610+0.004531j
[2025-07-30 06:11:17] [Iter 538/4650] R2[87/600], Temp: 0.9490, Energy: -54.601082-0.005094j
[2025-07-30 06:11:27] [Iter 539/4650] R2[88/600], Temp: 0.9479, Energy: -54.572909+0.002046j
[2025-07-30 06:11:37] [Iter 540/4650] R2[89/600], Temp: 0.9467, Energy: -54.497882-0.002433j
[2025-07-30 06:11:47] [Iter 541/4650] R2[90/600], Temp: 0.9455, Energy: -54.465070+0.003925j
[2025-07-30 06:11:57] [Iter 542/4650] R2[91/600], Temp: 0.9443, Energy: -54.456638-0.001704j
[2025-07-30 06:12:08] [Iter 543/4650] R2[92/600], Temp: 0.9431, Energy: -54.482533+0.006219j
[2025-07-30 06:12:18] [Iter 544/4650] R2[93/600], Temp: 0.9419, Energy: -54.480582+0.002153j
[2025-07-30 06:12:28] [Iter 545/4650] R2[94/600], Temp: 0.9407, Energy: -54.484470-0.001216j
[2025-07-30 06:12:38] [Iter 546/4650] R2[95/600], Temp: 0.9394, Energy: -54.463331+0.000779j
[2025-07-30 06:12:48] [Iter 547/4650] R2[96/600], Temp: 0.9382, Energy: -54.536525-0.000188j
[2025-07-30 06:12:58] [Iter 548/4650] R2[97/600], Temp: 0.9369, Energy: -54.567622-0.000871j
[2025-07-30 06:13:08] [Iter 549/4650] R2[98/600], Temp: 0.9356, Energy: -54.590715-0.002276j
[2025-07-30 06:13:18] [Iter 550/4650] R2[99/600], Temp: 0.9343, Energy: -54.577815+0.000592j
[2025-07-30 06:13:29] [Iter 551/4650] R2[100/600], Temp: 0.9330, Energy: -54.604385-0.002677j
[2025-07-30 06:13:39] [Iter 552/4650] R2[101/600], Temp: 0.9317, Energy: -54.568994-0.001494j
[2025-07-30 06:13:49] [Iter 553/4650] R2[102/600], Temp: 0.9304, Energy: -54.500065+0.004029j
[2025-07-30 06:13:59] [Iter 554/4650] R2[103/600], Temp: 0.9290, Energy: -54.485972-0.002060j
[2025-07-30 06:14:09] [Iter 555/4650] R2[104/600], Temp: 0.9277, Energy: -54.587539+0.000143j
[2025-07-30 06:14:19] [Iter 556/4650] R2[105/600], Temp: 0.9263, Energy: -54.583680-0.002289j
[2025-07-30 06:14:29] [Iter 557/4650] R2[106/600], Temp: 0.9249, Energy: -54.545581-0.007213j
[2025-07-30 06:14:40] [Iter 558/4650] R2[107/600], Temp: 0.9236, Energy: -54.531878-0.001096j
[2025-07-30 06:14:50] [Iter 559/4650] R2[108/600], Temp: 0.9222, Energy: -54.512010-0.003177j
[2025-07-30 06:15:00] [Iter 560/4650] R2[109/600], Temp: 0.9208, Energy: -54.499911+0.001937j
[2025-07-30 06:15:10] [Iter 561/4650] R2[110/600], Temp: 0.9193, Energy: -54.492018-0.001856j
[2025-07-30 06:15:20] [Iter 562/4650] R2[111/600], Temp: 0.9179, Energy: -54.556873+0.000251j
[2025-07-30 06:15:30] [Iter 563/4650] R2[112/600], Temp: 0.9165, Energy: -54.479720-0.000813j
[2025-07-30 06:15:40] [Iter 564/4650] R2[113/600], Temp: 0.9150, Energy: -54.489983+0.000325j
[2025-07-30 06:15:50] [Iter 565/4650] R2[114/600], Temp: 0.9135, Energy: -54.590914-0.002327j
[2025-07-30 06:16:00] [Iter 566/4650] R2[115/600], Temp: 0.9121, Energy: -54.596554+0.000458j
[2025-07-30 06:16:11] [Iter 567/4650] R2[116/600], Temp: 0.9106, Energy: -54.557639+0.003842j
[2025-07-30 06:16:21] [Iter 568/4650] R2[117/600], Temp: 0.9091, Energy: -54.555279+0.005741j
[2025-07-30 06:16:31] [Iter 569/4650] R2[118/600], Temp: 0.9076, Energy: -54.583979-0.002574j
[2025-07-30 06:16:41] [Iter 570/4650] R2[119/600], Temp: 0.9060, Energy: -54.542541+0.001708j
[2025-07-30 06:16:51] [Iter 571/4650] R2[120/600], Temp: 0.9045, Energy: -54.509746+0.004056j
[2025-07-30 06:17:01] [Iter 572/4650] R2[121/600], Temp: 0.9030, Energy: -54.556707-0.000449j
[2025-07-30 06:17:11] [Iter 573/4650] R2[122/600], Temp: 0.9014, Energy: -54.537038+0.005988j
[2025-07-30 06:17:21] [Iter 574/4650] R2[123/600], Temp: 0.8998, Energy: -54.556190-0.001102j
[2025-07-30 06:17:32] [Iter 575/4650] R2[124/600], Temp: 0.8983, Energy: -54.570378+0.000813j
[2025-07-30 06:17:42] [Iter 576/4650] R2[125/600], Temp: 0.8967, Energy: -54.535591-0.002341j
[2025-07-30 06:17:52] [Iter 577/4650] R2[126/600], Temp: 0.8951, Energy: -54.520142-0.001340j
[2025-07-30 06:18:02] [Iter 578/4650] R2[127/600], Temp: 0.8935, Energy: -54.570024-0.010811j
[2025-07-30 06:18:12] [Iter 579/4650] R2[128/600], Temp: 0.8918, Energy: -54.554203+0.001155j
[2025-07-30 06:18:22] [Iter 580/4650] R2[129/600], Temp: 0.8902, Energy: -54.546603-0.003505j
[2025-07-30 06:18:32] [Iter 581/4650] R2[130/600], Temp: 0.8886, Energy: -54.545989+0.002194j
[2025-07-30 06:18:42] [Iter 582/4650] R2[131/600], Temp: 0.8869, Energy: -54.517624+0.002273j
[2025-07-30 06:18:53] [Iter 583/4650] R2[132/600], Temp: 0.8853, Energy: -54.495285+0.004523j
[2025-07-30 06:19:03] [Iter 584/4650] R2[133/600], Temp: 0.8836, Energy: -54.513669-0.003323j
[2025-07-30 06:19:13] [Iter 585/4650] R2[134/600], Temp: 0.8819, Energy: -54.536474-0.001647j
[2025-07-30 06:19:23] [Iter 586/4650] R2[135/600], Temp: 0.8802, Energy: -54.499114-0.004497j
[2025-07-30 06:19:33] [Iter 587/4650] R2[136/600], Temp: 0.8785, Energy: -54.485059-0.004564j
[2025-07-30 06:19:43] [Iter 588/4650] R2[137/600], Temp: 0.8768, Energy: -54.539837-0.006120j
[2025-07-30 06:19:53] [Iter 589/4650] R2[138/600], Temp: 0.8751, Energy: -54.493749+0.001711j
[2025-07-30 06:20:04] [Iter 590/4650] R2[139/600], Temp: 0.8733, Energy: -54.536016+0.000166j
[2025-07-30 06:20:14] [Iter 591/4650] R2[140/600], Temp: 0.8716, Energy: -54.523896-0.002444j
[2025-07-30 06:20:24] [Iter 592/4650] R2[141/600], Temp: 0.8698, Energy: -54.550976+0.004771j
[2025-07-30 06:20:34] [Iter 593/4650] R2[142/600], Temp: 0.8680, Energy: -54.623068+0.000068j
[2025-07-30 06:20:44] [Iter 594/4650] R2[143/600], Temp: 0.8663, Energy: -54.604435-0.001247j
[2025-07-30 06:20:54] [Iter 595/4650] R2[144/600], Temp: 0.8645, Energy: -54.582264+0.005790j
[2025-07-30 06:21:04] [Iter 596/4650] R2[145/600], Temp: 0.8627, Energy: -54.621187+0.004995j
[2025-07-30 06:21:14] [Iter 597/4650] R2[146/600], Temp: 0.8609, Energy: -54.576798+0.000308j
[2025-07-30 06:21:25] [Iter 598/4650] R2[147/600], Temp: 0.8591, Energy: -54.567570-0.001412j
[2025-07-30 06:21:35] [Iter 599/4650] R2[148/600], Temp: 0.8572, Energy: -54.586106+0.001266j
[2025-07-30 06:21:45] [Iter 600/4650] R2[149/600], Temp: 0.8554, Energy: -54.591811+0.001205j
[2025-07-30 06:21:55] [Iter 601/4650] R2[150/600], Temp: 0.8536, Energy: -54.571531-0.005068j
[2025-07-30 06:22:05] [Iter 602/4650] R2[151/600], Temp: 0.8517, Energy: -54.593260+0.003687j
[2025-07-30 06:22:15] [Iter 603/4650] R2[152/600], Temp: 0.8498, Energy: -54.554440+0.001060j
[2025-07-30 06:22:25] [Iter 604/4650] R2[153/600], Temp: 0.8480, Energy: -54.582110+0.003304j
[2025-07-30 06:22:36] [Iter 605/4650] R2[154/600], Temp: 0.8461, Energy: -54.569382-0.001605j
[2025-07-30 06:22:46] [Iter 606/4650] R2[155/600], Temp: 0.8442, Energy: -54.587164+0.002135j
[2025-07-30 06:22:56] [Iter 607/4650] R2[156/600], Temp: 0.8423, Energy: -54.550324+0.007969j
[2025-07-30 06:23:06] [Iter 608/4650] R2[157/600], Temp: 0.8404, Energy: -54.542864-0.000369j
[2025-07-30 06:23:16] [Iter 609/4650] R2[158/600], Temp: 0.8384, Energy: -54.548479+0.004100j
[2025-07-30 06:23:26] [Iter 610/4650] R2[159/600], Temp: 0.8365, Energy: -54.510340+0.005418j
[2025-07-30 06:23:36] [Iter 611/4650] R2[160/600], Temp: 0.8346, Energy: -54.554833+0.002241j
[2025-07-30 06:23:46] [Iter 612/4650] R2[161/600], Temp: 0.8326, Energy: -54.484259-0.005849j
[2025-07-30 06:23:57] [Iter 613/4650] R2[162/600], Temp: 0.8307, Energy: -54.534509-0.004301j
[2025-07-30 06:24:07] [Iter 614/4650] R2[163/600], Temp: 0.8287, Energy: -54.536861-0.001663j
[2025-07-30 06:24:17] [Iter 615/4650] R2[164/600], Temp: 0.8267, Energy: -54.538388-0.003447j
[2025-07-30 06:24:27] [Iter 616/4650] R2[165/600], Temp: 0.8247, Energy: -54.520844-0.000440j
[2025-07-30 06:24:37] [Iter 617/4650] R2[166/600], Temp: 0.8227, Energy: -54.541625-0.002570j
[2025-07-30 06:24:47] [Iter 618/4650] R2[167/600], Temp: 0.8207, Energy: -54.543469+0.003942j
[2025-07-30 06:24:57] [Iter 619/4650] R2[168/600], Temp: 0.8187, Energy: -54.500776+0.003655j
[2025-07-30 06:25:07] [Iter 620/4650] R2[169/600], Temp: 0.8167, Energy: -54.519885-0.002697j
[2025-07-30 06:25:17] [Iter 621/4650] R2[170/600], Temp: 0.8147, Energy: -54.546482+0.001835j
[2025-07-30 06:25:28] [Iter 622/4650] R2[171/600], Temp: 0.8126, Energy: -54.608438+0.000433j
[2025-07-30 06:25:38] [Iter 623/4650] R2[172/600], Temp: 0.8106, Energy: -54.601614-0.001224j
[2025-07-30 06:25:48] [Iter 624/4650] R2[173/600], Temp: 0.8085, Energy: -54.527610+0.005479j
[2025-07-30 06:25:58] [Iter 625/4650] R2[174/600], Temp: 0.8065, Energy: -54.569024-0.003941j
[2025-07-30 06:26:08] [Iter 626/4650] R2[175/600], Temp: 0.8044, Energy: -54.550778+0.002106j
[2025-07-30 06:26:18] [Iter 627/4650] R2[176/600], Temp: 0.8023, Energy: -54.581340+0.000163j
[2025-07-30 06:26:28] [Iter 628/4650] R2[177/600], Temp: 0.8002, Energy: -54.566725+0.000802j
[2025-07-30 06:26:38] [Iter 629/4650] R2[178/600], Temp: 0.7981, Energy: -54.530649-0.000277j
[2025-07-30 06:26:49] [Iter 630/4650] R2[179/600], Temp: 0.7960, Energy: -54.487232+0.002481j
[2025-07-30 06:26:59] [Iter 631/4650] R2[180/600], Temp: 0.7939, Energy: -54.557349-0.003625j
[2025-07-30 06:27:09] [Iter 632/4650] R2[181/600], Temp: 0.7918, Energy: -54.481855-0.004321j
[2025-07-30 06:27:19] [Iter 633/4650] R2[182/600], Temp: 0.7896, Energy: -54.462312-0.003012j
[2025-07-30 06:27:29] [Iter 634/4650] R2[183/600], Temp: 0.7875, Energy: -54.467213+0.006728j
[2025-07-30 06:27:39] [Iter 635/4650] R2[184/600], Temp: 0.7854, Energy: -54.539711+0.004258j
[2025-07-30 06:27:49] [Iter 636/4650] R2[185/600], Temp: 0.7832, Energy: -54.494860-0.001694j
[2025-07-30 06:27:59] [Iter 637/4650] R2[186/600], Temp: 0.7810, Energy: -54.525871+0.000561j
[2025-07-30 06:28:10] [Iter 638/4650] R2[187/600], Temp: 0.7789, Energy: -54.596079+0.000740j
[2025-07-30 06:28:20] [Iter 639/4650] R2[188/600], Temp: 0.7767, Energy: -54.556762+0.004273j
[2025-07-30 06:28:30] [Iter 640/4650] R2[189/600], Temp: 0.7745, Energy: -54.566763-0.002225j
[2025-07-30 06:28:40] [Iter 641/4650] R2[190/600], Temp: 0.7723, Energy: -54.590945+0.000380j
[2025-07-30 06:28:50] [Iter 642/4650] R2[191/600], Temp: 0.7701, Energy: -54.612253-0.003333j
[2025-07-30 06:29:00] [Iter 643/4650] R2[192/600], Temp: 0.7679, Energy: -54.597388+0.001494j
[2025-07-30 06:29:11] [Iter 644/4650] R2[193/600], Temp: 0.7657, Energy: -54.588494-0.002273j
[2025-07-30 06:29:21] [Iter 645/4650] R2[194/600], Temp: 0.7635, Energy: -54.537537+0.001448j
[2025-07-30 06:29:31] [Iter 646/4650] R2[195/600], Temp: 0.7612, Energy: -54.517165-0.000676j
[2025-07-30 06:29:41] [Iter 647/4650] R2[196/600], Temp: 0.7590, Energy: -54.548998-0.001203j
[2025-07-30 06:29:51] [Iter 648/4650] R2[197/600], Temp: 0.7568, Energy: -54.534652-0.001840j
[2025-07-30 06:30:01] [Iter 649/4650] R2[198/600], Temp: 0.7545, Energy: -54.462831+0.001805j
[2025-07-30 06:30:11] [Iter 650/4650] R2[199/600], Temp: 0.7523, Energy: -54.532748+0.002177j
[2025-07-30 06:30:21] [Iter 651/4650] R2[200/600], Temp: 0.7500, Energy: -54.571205+0.002704j
[2025-07-30 06:30:31] [Iter 652/4650] R2[201/600], Temp: 0.7477, Energy: -54.562558+0.003489j
[2025-07-30 06:30:42] [Iter 653/4650] R2[202/600], Temp: 0.7455, Energy: -54.561565-0.002834j
[2025-07-30 06:30:52] [Iter 654/4650] R2[203/600], Temp: 0.7432, Energy: -54.525057+0.001889j
[2025-07-30 06:31:02] [Iter 655/4650] R2[204/600], Temp: 0.7409, Energy: -54.524720-0.004887j
[2025-07-30 06:31:12] [Iter 656/4650] R2[205/600], Temp: 0.7386, Energy: -54.579575+0.000661j
[2025-07-30 06:31:22] [Iter 657/4650] R2[206/600], Temp: 0.7363, Energy: -54.537786+0.003768j
[2025-07-30 06:31:32] [Iter 658/4650] R2[207/600], Temp: 0.7340, Energy: -54.548051+0.001122j
[2025-07-30 06:31:42] [Iter 659/4650] R2[208/600], Temp: 0.7316, Energy: -54.562484-0.001912j
[2025-07-30 06:31:52] [Iter 660/4650] R2[209/600], Temp: 0.7293, Energy: -54.620004-0.000940j
[2025-07-30 06:32:03] [Iter 661/4650] R2[210/600], Temp: 0.7270, Energy: -54.515750-0.001528j
[2025-07-30 06:32:13] [Iter 662/4650] R2[211/600], Temp: 0.7247, Energy: -54.540337-0.001637j
[2025-07-30 06:32:23] [Iter 663/4650] R2[212/600], Temp: 0.7223, Energy: -54.539709+0.004386j
[2025-07-30 06:32:33] [Iter 664/4650] R2[213/600], Temp: 0.7200, Energy: -54.495851-0.000887j
[2025-07-30 06:32:43] [Iter 665/4650] R2[214/600], Temp: 0.7176, Energy: -54.517622-0.001164j
[2025-07-30 06:32:53] [Iter 666/4650] R2[215/600], Temp: 0.7153, Energy: -54.564182-0.001252j
[2025-07-30 06:33:03] [Iter 667/4650] R2[216/600], Temp: 0.7129, Energy: -54.511050+0.003055j
[2025-07-30 06:33:13] [Iter 668/4650] R2[217/600], Temp: 0.7105, Energy: -54.497911+0.006970j
[2025-07-30 06:33:23] [Iter 669/4650] R2[218/600], Temp: 0.7081, Energy: -54.491696-0.001522j
[2025-07-30 06:33:34] [Iter 670/4650] R2[219/600], Temp: 0.7058, Energy: -54.492951+0.004221j
[2025-07-30 06:33:44] [Iter 671/4650] R2[220/600], Temp: 0.7034, Energy: -54.489663+0.001585j
[2025-07-30 06:33:54] [Iter 672/4650] R2[221/600], Temp: 0.7010, Energy: -54.503052+0.003004j
[2025-07-30 06:34:04] [Iter 673/4650] R2[222/600], Temp: 0.6986, Energy: -54.577061-0.000708j
[2025-07-30 06:34:14] [Iter 674/4650] R2[223/600], Temp: 0.6962, Energy: -54.583536-0.005276j
[2025-07-30 06:34:24] [Iter 675/4650] R2[224/600], Temp: 0.6938, Energy: -54.555808-0.003856j
[2025-07-30 06:34:34] [Iter 676/4650] R2[225/600], Temp: 0.6913, Energy: -54.571599-0.002509j
[2025-07-30 06:34:45] [Iter 677/4650] R2[226/600], Temp: 0.6889, Energy: -54.553864+0.007899j
[2025-07-30 06:34:55] [Iter 678/4650] R2[227/600], Temp: 0.6865, Energy: -54.554808+0.003743j
[2025-07-30 06:35:05] [Iter 679/4650] R2[228/600], Temp: 0.6841, Energy: -54.594314+0.000270j
[2025-07-30 06:35:15] [Iter 680/4650] R2[229/600], Temp: 0.6816, Energy: -54.558335+0.001979j
[2025-07-30 06:35:25] [Iter 681/4650] R2[230/600], Temp: 0.6792, Energy: -54.585484-0.000417j
[2025-07-30 06:35:35] [Iter 682/4650] R2[231/600], Temp: 0.6767, Energy: -54.501558-0.001476j
[2025-07-30 06:35:45] [Iter 683/4650] R2[232/600], Temp: 0.6743, Energy: -54.550628-0.000597j
[2025-07-30 06:35:55] [Iter 684/4650] R2[233/600], Temp: 0.6718, Energy: -54.580682+0.000093j
[2025-07-30 06:36:05] [Iter 685/4650] R2[234/600], Temp: 0.6694, Energy: -54.555291-0.002373j
[2025-07-30 06:36:16] [Iter 686/4650] R2[235/600], Temp: 0.6669, Energy: -54.563285+0.000623j
[2025-07-30 06:36:26] [Iter 687/4650] R2[236/600], Temp: 0.6644, Energy: -54.524297+0.005084j
[2025-07-30 06:36:36] [Iter 688/4650] R2[237/600], Temp: 0.6620, Energy: -54.522586+0.001327j
[2025-07-30 06:36:46] [Iter 689/4650] R2[238/600], Temp: 0.6595, Energy: -54.509888-0.005564j
[2025-07-30 06:36:56] [Iter 690/4650] R2[239/600], Temp: 0.6570, Energy: -54.530778+0.001128j
[2025-07-30 06:37:06] [Iter 691/4650] R2[240/600], Temp: 0.6545, Energy: -54.542448-0.000055j
[2025-07-30 06:37:16] [Iter 692/4650] R2[241/600], Temp: 0.6520, Energy: -54.523923-0.000763j
[2025-07-30 06:37:26] [Iter 693/4650] R2[242/600], Temp: 0.6495, Energy: -54.515602-0.003249j
[2025-07-30 06:37:37] [Iter 694/4650] R2[243/600], Temp: 0.6470, Energy: -54.551998-0.000306j
[2025-07-30 06:37:47] [Iter 695/4650] R2[244/600], Temp: 0.6445, Energy: -54.539928+0.001476j
[2025-07-30 06:37:57] [Iter 696/4650] R2[245/600], Temp: 0.6420, Energy: -54.553841+0.002643j
[2025-07-30 06:38:07] [Iter 697/4650] R2[246/600], Temp: 0.6395, Energy: -54.616157-0.003219j
[2025-07-30 06:38:17] [Iter 698/4650] R2[247/600], Temp: 0.6370, Energy: -54.591090-0.000284j
[2025-07-30 06:38:27] [Iter 699/4650] R2[248/600], Temp: 0.6345, Energy: -54.583478-0.002915j
[2025-07-30 06:38:37] [Iter 700/4650] R2[249/600], Temp: 0.6319, Energy: -54.549363-0.001198j
[2025-07-30 06:38:47] [Iter 701/4650] R2[250/600], Temp: 0.6294, Energy: -54.555480-0.005342j
[2025-07-30 06:38:58] [Iter 702/4650] R2[251/600], Temp: 0.6269, Energy: -54.600594+0.003633j
[2025-07-30 06:39:08] [Iter 703/4650] R2[252/600], Temp: 0.6243, Energy: -54.597240-0.005899j
[2025-07-30 06:39:18] [Iter 704/4650] R2[253/600], Temp: 0.6218, Energy: -54.569410-0.001221j
[2025-07-30 06:39:28] [Iter 705/4650] R2[254/600], Temp: 0.6193, Energy: -54.529753-0.001762j
[2025-07-30 06:39:38] [Iter 706/4650] R2[255/600], Temp: 0.6167, Energy: -54.554227+0.001229j
[2025-07-30 06:39:48] [Iter 707/4650] R2[256/600], Temp: 0.6142, Energy: -54.520622+0.000930j
[2025-07-30 06:39:58] [Iter 708/4650] R2[257/600], Temp: 0.6116, Energy: -54.565378-0.000221j
[2025-07-30 06:40:08] [Iter 709/4650] R2[258/600], Temp: 0.6091, Energy: -54.572361+0.002942j
[2025-07-30 06:40:19] [Iter 710/4650] R2[259/600], Temp: 0.6065, Energy: -54.521270-0.002681j
[2025-07-30 06:40:29] [Iter 711/4650] R2[260/600], Temp: 0.6040, Energy: -54.542972-0.006145j
[2025-07-30 06:40:39] [Iter 712/4650] R2[261/600], Temp: 0.6014, Energy: -54.570411-0.001395j
[2025-07-30 06:40:49] [Iter 713/4650] R2[262/600], Temp: 0.5988, Energy: -54.583249+0.001459j
[2025-07-30 06:40:59] [Iter 714/4650] R2[263/600], Temp: 0.5963, Energy: -54.554999+0.001926j
[2025-07-30 06:41:09] [Iter 715/4650] R2[264/600], Temp: 0.5937, Energy: -54.506629+0.002206j
[2025-07-30 06:41:19] [Iter 716/4650] R2[265/600], Temp: 0.5911, Energy: -54.512244-0.008139j
[2025-07-30 06:41:29] [Iter 717/4650] R2[266/600], Temp: 0.5885, Energy: -54.526543-0.000498j
[2025-07-30 06:41:40] [Iter 718/4650] R2[267/600], Temp: 0.5860, Energy: -54.602593+0.002314j
[2025-07-30 06:41:50] [Iter 719/4650] R2[268/600], Temp: 0.5834, Energy: -54.553615-0.000575j
[2025-07-30 06:42:00] [Iter 720/4650] R2[269/600], Temp: 0.5808, Energy: -54.550388-0.000001j
[2025-07-30 06:42:10] [Iter 721/4650] R2[270/600], Temp: 0.5782, Energy: -54.552927-0.000158j
[2025-07-30 06:42:20] [Iter 722/4650] R2[271/600], Temp: 0.5756, Energy: -54.520827-0.002963j
[2025-07-30 06:42:30] [Iter 723/4650] R2[272/600], Temp: 0.5730, Energy: -54.505088+0.000889j
[2025-07-30 06:42:41] [Iter 724/4650] R2[273/600], Temp: 0.5705, Energy: -54.526117+0.001184j
[2025-07-30 06:42:51] [Iter 725/4650] R2[274/600], Temp: 0.5679, Energy: -54.635363+0.000869j
[2025-07-30 06:43:01] [Iter 726/4650] R2[275/600], Temp: 0.5653, Energy: -54.622143-0.000637j
[2025-07-30 06:43:11] [Iter 727/4650] R2[276/600], Temp: 0.5627, Energy: -54.633152+0.004333j
[2025-07-30 06:43:21] [Iter 728/4650] R2[277/600], Temp: 0.5601, Energy: -54.586342-0.002756j
[2025-07-30 06:43:31] [Iter 729/4650] R2[278/600], Temp: 0.5575, Energy: -54.605311+0.002674j
[2025-07-30 06:43:41] [Iter 730/4650] R2[279/600], Temp: 0.5549, Energy: -54.594950+0.005351j
[2025-07-30 06:43:51] [Iter 731/4650] R2[280/600], Temp: 0.5523, Energy: -54.538199+0.003502j
[2025-07-30 06:44:02] [Iter 732/4650] R2[281/600], Temp: 0.5497, Energy: -54.518354-0.001725j
[2025-07-30 06:44:12] [Iter 733/4650] R2[282/600], Temp: 0.5471, Energy: -54.532838+0.002229j
[2025-07-30 06:44:22] [Iter 734/4650] R2[283/600], Temp: 0.5444, Energy: -54.529545-0.003098j
[2025-07-30 06:44:32] [Iter 735/4650] R2[284/600], Temp: 0.5418, Energy: -54.509015-0.002370j
[2025-07-30 06:44:42] [Iter 736/4650] R2[285/600], Temp: 0.5392, Energy: -54.540331-0.003312j
[2025-07-30 06:44:52] [Iter 737/4650] R2[286/600], Temp: 0.5366, Energy: -54.503499+0.001408j
[2025-07-30 06:45:02] [Iter 738/4650] R2[287/600], Temp: 0.5340, Energy: -54.580896-0.004002j
[2025-07-30 06:45:12] [Iter 739/4650] R2[288/600], Temp: 0.5314, Energy: -54.517991-0.000566j
[2025-07-30 06:45:22] [Iter 740/4650] R2[289/600], Temp: 0.5288, Energy: -54.539947+0.004260j
[2025-07-30 06:45:33] [Iter 741/4650] R2[290/600], Temp: 0.5262, Energy: -54.519492+0.004575j
[2025-07-30 06:45:43] [Iter 742/4650] R2[291/600], Temp: 0.5236, Energy: -54.541697+0.001836j
[2025-07-30 06:45:53] [Iter 743/4650] R2[292/600], Temp: 0.5209, Energy: -54.567406-0.000034j
[2025-07-30 06:46:03] [Iter 744/4650] R2[293/600], Temp: 0.5183, Energy: -54.515658-0.001128j
[2025-07-30 06:46:13] [Iter 745/4650] R2[294/600], Temp: 0.5157, Energy: -54.508435-0.003903j
[2025-07-30 06:46:23] [Iter 746/4650] R2[295/600], Temp: 0.5131, Energy: -54.536136+0.001827j
[2025-07-30 06:46:33] [Iter 747/4650] R2[296/600], Temp: 0.5105, Energy: -54.588389+0.000315j
[2025-07-30 06:46:44] [Iter 748/4650] R2[297/600], Temp: 0.5079, Energy: -54.578770+0.002876j
[2025-07-30 06:46:54] [Iter 749/4650] R2[298/600], Temp: 0.5052, Energy: -54.597801+0.002982j
[2025-07-30 06:47:04] [Iter 750/4650] R2[299/600], Temp: 0.5026, Energy: -54.548282-0.006388j
[2025-07-30 06:47:14] [Iter 751/4650] R2[300/600], Temp: 0.5000, Energy: -54.612905-0.004277j
[2025-07-30 06:47:24] [Iter 752/4650] R2[301/600], Temp: 0.4974, Energy: -54.601440+0.000268j
[2025-07-30 06:47:34] [Iter 753/4650] R2[302/600], Temp: 0.4948, Energy: -54.610504-0.003582j
[2025-07-30 06:47:44] [Iter 754/4650] R2[303/600], Temp: 0.4921, Energy: -54.630218-0.002798j
[2025-07-30 06:47:55] [Iter 755/4650] R2[304/600], Temp: 0.4895, Energy: -54.560258-0.002662j
[2025-07-30 06:48:05] [Iter 756/4650] R2[305/600], Temp: 0.4869, Energy: -54.562411-0.000797j
[2025-07-30 06:48:15] [Iter 757/4650] R2[306/600], Temp: 0.4843, Energy: -54.485416+0.003900j
[2025-07-30 06:48:25] [Iter 758/4650] R2[307/600], Temp: 0.4817, Energy: -54.543913+0.002590j
[2025-07-30 06:48:35] [Iter 759/4650] R2[308/600], Temp: 0.4791, Energy: -54.555708+0.003724j
[2025-07-30 06:48:45] [Iter 760/4650] R2[309/600], Temp: 0.4764, Energy: -54.570240+0.005943j
[2025-07-30 06:48:55] [Iter 761/4650] R2[310/600], Temp: 0.4738, Energy: -54.531525-0.003054j
[2025-07-30 06:49:05] [Iter 762/4650] R2[311/600], Temp: 0.4712, Energy: -54.514630+0.005264j
[2025-07-30 06:49:16] [Iter 763/4650] R2[312/600], Temp: 0.4686, Energy: -54.551206+0.001671j
[2025-07-30 06:49:26] [Iter 764/4650] R2[313/600], Temp: 0.4660, Energy: -54.535762-0.000628j
[2025-07-30 06:49:36] [Iter 765/4650] R2[314/600], Temp: 0.4634, Energy: -54.486601-0.000312j
[2025-07-30 06:49:46] [Iter 766/4650] R2[315/600], Temp: 0.4608, Energy: -54.479118+0.000490j
[2025-07-30 06:49:56] [Iter 767/4650] R2[316/600], Temp: 0.4582, Energy: -54.497292+0.002041j
[2025-07-30 06:50:06] [Iter 768/4650] R2[317/600], Temp: 0.4556, Energy: -54.443908+0.003880j
[2025-07-30 06:50:16] [Iter 769/4650] R2[318/600], Temp: 0.4529, Energy: -54.520709-0.000883j
[2025-07-30 06:50:26] [Iter 770/4650] R2[319/600], Temp: 0.4503, Energy: -54.536123-0.001676j
[2025-07-30 06:50:36] [Iter 771/4650] R2[320/600], Temp: 0.4477, Energy: -54.530138-0.000598j
[2025-07-30 06:50:47] [Iter 772/4650] R2[321/600], Temp: 0.4451, Energy: -54.595580-0.004108j
[2025-07-30 06:50:57] [Iter 773/4650] R2[322/600], Temp: 0.4425, Energy: -54.538957+0.001863j
[2025-07-30 06:51:07] [Iter 774/4650] R2[323/600], Temp: 0.4399, Energy: -54.558302+0.004033j
[2025-07-30 06:51:17] [Iter 775/4650] R2[324/600], Temp: 0.4373, Energy: -54.534213+0.001565j
[2025-07-30 06:51:27] [Iter 776/4650] R2[325/600], Temp: 0.4347, Energy: -54.535247-0.000711j
[2025-07-30 06:51:37] [Iter 777/4650] R2[326/600], Temp: 0.4321, Energy: -54.524926-0.005071j
[2025-07-30 06:51:47] [Iter 778/4650] R2[327/600], Temp: 0.4295, Energy: -54.563284-0.004757j
[2025-07-30 06:51:58] [Iter 779/4650] R2[328/600], Temp: 0.4270, Energy: -54.536165+0.000614j
[2025-07-30 06:52:08] [Iter 780/4650] R2[329/600], Temp: 0.4244, Energy: -54.543028+0.001209j
[2025-07-30 06:52:18] [Iter 781/4650] R2[330/600], Temp: 0.4218, Energy: -54.504649-0.000176j
[2025-07-30 06:52:28] [Iter 782/4650] R2[331/600], Temp: 0.4192, Energy: -54.552690+0.003073j
[2025-07-30 06:52:38] [Iter 783/4650] R2[332/600], Temp: 0.4166, Energy: -54.592057-0.001618j
[2025-07-30 06:52:48] [Iter 784/4650] R2[333/600], Temp: 0.4140, Energy: -54.592819-0.000181j
[2025-07-30 06:52:58] [Iter 785/4650] R2[334/600], Temp: 0.4115, Energy: -54.561200-0.002161j
[2025-07-30 06:53:08] [Iter 786/4650] R2[335/600], Temp: 0.4089, Energy: -54.571231-0.005795j
[2025-07-30 06:53:19] [Iter 787/4650] R2[336/600], Temp: 0.4063, Energy: -54.561308-0.003845j
[2025-07-30 06:53:29] [Iter 788/4650] R2[337/600], Temp: 0.4037, Energy: -54.553210-0.002138j
[2025-07-30 06:53:39] [Iter 789/4650] R2[338/600], Temp: 0.4012, Energy: -54.529417-0.002763j
[2025-07-30 06:53:49] [Iter 790/4650] R2[339/600], Temp: 0.3986, Energy: -54.575119-0.001565j
[2025-07-30 06:53:59] [Iter 791/4650] R2[340/600], Temp: 0.3960, Energy: -54.516283-0.003248j
[2025-07-30 06:54:09] [Iter 792/4650] R2[341/600], Temp: 0.3935, Energy: -54.552764+0.000682j
[2025-07-30 06:54:19] [Iter 793/4650] R2[342/600], Temp: 0.3909, Energy: -54.529429+0.000856j
[2025-07-30 06:54:30] [Iter 794/4650] R2[343/600], Temp: 0.3884, Energy: -54.578883+0.005410j
[2025-07-30 06:54:40] [Iter 795/4650] R2[344/600], Temp: 0.3858, Energy: -54.531629-0.006977j
[2025-07-30 06:54:50] [Iter 796/4650] R2[345/600], Temp: 0.3833, Energy: -54.511597+0.000657j
[2025-07-30 06:55:00] [Iter 797/4650] R2[346/600], Temp: 0.3807, Energy: -54.539979-0.000001j
[2025-07-30 06:55:10] [Iter 798/4650] R2[347/600], Temp: 0.3782, Energy: -54.539139+0.002623j
[2025-07-30 06:55:20] [Iter 799/4650] R2[348/600], Temp: 0.3757, Energy: -54.533908+0.000759j
[2025-07-30 06:55:30] [Iter 800/4650] R2[349/600], Temp: 0.3731, Energy: -54.572687+0.000525j
[2025-07-30 06:55:41] [Iter 801/4650] R2[350/600], Temp: 0.3706, Energy: -54.622274-0.000492j
[2025-07-30 06:55:51] [Iter 802/4650] R2[351/600], Temp: 0.3681, Energy: -54.624866+0.000858j
[2025-07-30 06:56:01] [Iter 803/4650] R2[352/600], Temp: 0.3655, Energy: -54.556206+0.003812j
[2025-07-30 06:56:11] [Iter 804/4650] R2[353/600], Temp: 0.3630, Energy: -54.551042+0.001899j
[2025-07-30 06:56:21] [Iter 805/4650] R2[354/600], Temp: 0.3605, Energy: -54.587601-0.002102j
[2025-07-30 06:56:31] [Iter 806/4650] R2[355/600], Temp: 0.3580, Energy: -54.582174+0.002155j
[2025-07-30 06:56:41] [Iter 807/4650] R2[356/600], Temp: 0.3555, Energy: -54.550248-0.002861j
[2025-07-30 06:56:51] [Iter 808/4650] R2[357/600], Temp: 0.3530, Energy: -54.571902-0.000730j
[2025-07-30 06:57:02] [Iter 809/4650] R2[358/600], Temp: 0.3505, Energy: -54.522729+0.001740j
[2025-07-30 06:57:12] [Iter 810/4650] R2[359/600], Temp: 0.3480, Energy: -54.534383-0.004052j
[2025-07-30 06:57:22] [Iter 811/4650] R2[360/600], Temp: 0.3455, Energy: -54.611241+0.005504j
[2025-07-30 06:57:32] [Iter 812/4650] R2[361/600], Temp: 0.3430, Energy: -54.531995+0.003188j
[2025-07-30 06:57:42] [Iter 813/4650] R2[362/600], Temp: 0.3405, Energy: -54.479578+0.000194j
[2025-07-30 06:57:52] [Iter 814/4650] R2[363/600], Temp: 0.3380, Energy: -54.495593-0.001107j
[2025-07-30 06:58:02] [Iter 815/4650] R2[364/600], Temp: 0.3356, Energy: -54.504165+0.004217j
[2025-07-30 06:58:12] [Iter 816/4650] R2[365/600], Temp: 0.3331, Energy: -54.520493+0.000021j
[2025-07-30 06:58:23] [Iter 817/4650] R2[366/600], Temp: 0.3306, Energy: -54.494186-0.001265j
[2025-07-30 06:58:33] [Iter 818/4650] R2[367/600], Temp: 0.3282, Energy: -54.567336-0.003771j
[2025-07-30 06:58:43] [Iter 819/4650] R2[368/600], Temp: 0.3257, Energy: -54.539903+0.001955j
[2025-07-30 06:58:53] [Iter 820/4650] R2[369/600], Temp: 0.3233, Energy: -54.499442+0.004582j
[2025-07-30 06:59:03] [Iter 821/4650] R2[370/600], Temp: 0.3208, Energy: -54.508064-0.002798j
[2025-07-30 06:59:13] [Iter 822/4650] R2[371/600], Temp: 0.3184, Energy: -54.567949+0.003454j
[2025-07-30 06:59:23] [Iter 823/4650] R2[372/600], Temp: 0.3159, Energy: -54.537219+0.002199j
[2025-07-30 06:59:33] [Iter 824/4650] R2[373/600], Temp: 0.3135, Energy: -54.522359+0.000923j
[2025-07-30 06:59:44] [Iter 825/4650] R2[374/600], Temp: 0.3111, Energy: -54.580789+0.000840j
[2025-07-30 06:59:54] [Iter 826/4650] R2[375/600], Temp: 0.3087, Energy: -54.544005+0.001156j
[2025-07-30 07:00:04] [Iter 827/4650] R2[376/600], Temp: 0.3062, Energy: -54.581331+0.001007j
[2025-07-30 07:00:14] [Iter 828/4650] R2[377/600], Temp: 0.3038, Energy: -54.580412+0.001397j
[2025-07-30 07:00:24] [Iter 829/4650] R2[378/600], Temp: 0.3014, Energy: -54.570541-0.003278j
[2025-07-30 07:00:34] [Iter 830/4650] R2[379/600], Temp: 0.2990, Energy: -54.544931+0.004211j
[2025-07-30 07:00:44] [Iter 831/4650] R2[380/600], Temp: 0.2966, Energy: -54.579259+0.000411j
[2025-07-30 07:00:55] [Iter 832/4650] R2[381/600], Temp: 0.2942, Energy: -54.524520-0.002828j
[2025-07-30 07:01:05] [Iter 833/4650] R2[382/600], Temp: 0.2919, Energy: -54.612606+0.000913j
[2025-07-30 07:01:15] [Iter 834/4650] R2[383/600], Temp: 0.2895, Energy: -54.601444-0.001065j
[2025-07-30 07:01:25] [Iter 835/4650] R2[384/600], Temp: 0.2871, Energy: -54.572506+0.002517j
[2025-07-30 07:01:35] [Iter 836/4650] R2[385/600], Temp: 0.2847, Energy: -54.585584+0.002902j
[2025-07-30 07:01:45] [Iter 837/4650] R2[386/600], Temp: 0.2824, Energy: -54.576686+0.001408j
[2025-07-30 07:01:55] [Iter 838/4650] R2[387/600], Temp: 0.2800, Energy: -54.631353+0.004854j
[2025-07-30 07:02:05] [Iter 839/4650] R2[388/600], Temp: 0.2777, Energy: -54.677540+0.003796j
[2025-07-30 07:02:15] [Iter 840/4650] R2[389/600], Temp: 0.2753, Energy: -54.653861+0.001730j
[2025-07-30 07:02:26] [Iter 841/4650] R2[390/600], Temp: 0.2730, Energy: -54.623012+0.000525j
[2025-07-30 07:02:36] [Iter 842/4650] R2[391/600], Temp: 0.2707, Energy: -54.606812+0.001588j
[2025-07-30 07:02:46] [Iter 843/4650] R2[392/600], Temp: 0.2684, Energy: -54.594546+0.001997j
[2025-07-30 07:02:56] [Iter 844/4650] R2[393/600], Temp: 0.2660, Energy: -54.629050-0.003547j
[2025-07-30 07:03:06] [Iter 845/4650] R2[394/600], Temp: 0.2637, Energy: -54.649293-0.002648j
[2025-07-30 07:03:16] [Iter 846/4650] R2[395/600], Temp: 0.2614, Energy: -54.652062+0.003106j
[2025-07-30 07:03:26] [Iter 847/4650] R2[396/600], Temp: 0.2591, Energy: -54.711787-0.000978j
[2025-07-30 07:03:36] [Iter 848/4650] R2[397/600], Temp: 0.2568, Energy: -54.654179-0.008652j
[2025-07-30 07:03:47] [Iter 849/4650] R2[398/600], Temp: 0.2545, Energy: -54.647138+0.002434j
[2025-07-30 07:03:57] [Iter 850/4650] R2[399/600], Temp: 0.2523, Energy: -54.641583-0.007322j
[2025-07-30 07:04:07] [Iter 851/4650] R2[400/600], Temp: 0.2500, Energy: -54.652196-0.003385j
[2025-07-30 07:04:17] [Iter 852/4650] R2[401/600], Temp: 0.2477, Energy: -54.645349-0.003506j
[2025-07-30 07:04:27] [Iter 853/4650] R2[402/600], Temp: 0.2455, Energy: -54.593359-0.005389j
[2025-07-30 07:04:37] [Iter 854/4650] R2[403/600], Temp: 0.2432, Energy: -54.658570-0.002420j
[2025-07-30 07:04:47] [Iter 855/4650] R2[404/600], Temp: 0.2410, Energy: -54.612120+0.001927j
[2025-07-30 07:04:57] [Iter 856/4650] R2[405/600], Temp: 0.2388, Energy: -54.564734+0.000426j
[2025-07-30 07:05:08] [Iter 857/4650] R2[406/600], Temp: 0.2365, Energy: -54.576399+0.001257j
[2025-07-30 07:05:18] [Iter 858/4650] R2[407/600], Temp: 0.2343, Energy: -54.587086+0.002467j
[2025-07-30 07:05:28] [Iter 859/4650] R2[408/600], Temp: 0.2321, Energy: -54.585448+0.000226j
[2025-07-30 07:05:38] [Iter 860/4650] R2[409/600], Temp: 0.2299, Energy: -54.536902+0.000732j
[2025-07-30 07:05:48] [Iter 861/4650] R2[410/600], Temp: 0.2277, Energy: -54.527064-0.002557j
[2025-07-30 07:05:58] [Iter 862/4650] R2[411/600], Temp: 0.2255, Energy: -54.495055+0.005449j
[2025-07-30 07:06:08] [Iter 863/4650] R2[412/600], Temp: 0.2233, Energy: -54.489704-0.005161j
[2025-07-30 07:06:18] [Iter 864/4650] R2[413/600], Temp: 0.2211, Energy: -54.547406+0.005472j
[2025-07-30 07:06:29] [Iter 865/4650] R2[414/600], Temp: 0.2190, Energy: -54.560559-0.002146j
[2025-07-30 07:06:39] [Iter 866/4650] R2[415/600], Temp: 0.2168, Energy: -54.592636-0.002289j
[2025-07-30 07:06:49] [Iter 867/4650] R2[416/600], Temp: 0.2146, Energy: -54.555192-0.000092j
[2025-07-30 07:06:59] [Iter 868/4650] R2[417/600], Temp: 0.2125, Energy: -54.555756+0.001263j
[2025-07-30 07:07:09] [Iter 869/4650] R2[418/600], Temp: 0.2104, Energy: -54.608990+0.000576j
[2025-07-30 07:07:19] [Iter 870/4650] R2[419/600], Temp: 0.2082, Energy: -54.624272+0.000023j
[2025-07-30 07:07:29] [Iter 871/4650] R2[420/600], Temp: 0.2061, Energy: -54.591856-0.002262j
[2025-07-30 07:07:40] [Iter 872/4650] R2[421/600], Temp: 0.2040, Energy: -54.614423-0.000759j
[2025-07-30 07:07:50] [Iter 873/4650] R2[422/600], Temp: 0.2019, Energy: -54.587379+0.003420j
[2025-07-30 07:08:00] [Iter 874/4650] R2[423/600], Temp: 0.1998, Energy: -54.583913-0.000890j
[2025-07-30 07:08:10] [Iter 875/4650] R2[424/600], Temp: 0.1977, Energy: -54.599190+0.003020j
[2025-07-30 07:08:20] [Iter 876/4650] R2[425/600], Temp: 0.1956, Energy: -54.621451-0.000931j
[2025-07-30 07:08:30] [Iter 877/4650] R2[426/600], Temp: 0.1935, Energy: -54.624902-0.001119j
[2025-07-30 07:08:40] [Iter 878/4650] R2[427/600], Temp: 0.1915, Energy: -54.596786+0.001512j
[2025-07-30 07:08:50] [Iter 879/4650] R2[428/600], Temp: 0.1894, Energy: -54.627113+0.000475j
[2025-07-30 07:09:01] [Iter 880/4650] R2[429/600], Temp: 0.1874, Energy: -54.641531+0.004283j
[2025-07-30 07:09:11] [Iter 881/4650] R2[430/600], Temp: 0.1853, Energy: -54.666903-0.001164j
[2025-07-30 07:09:21] [Iter 882/4650] R2[431/600], Temp: 0.1833, Energy: -54.608875+0.004170j
[2025-07-30 07:09:31] [Iter 883/4650] R2[432/600], Temp: 0.1813, Energy: -54.639896+0.000235j
[2025-07-30 07:09:41] [Iter 884/4650] R2[433/600], Temp: 0.1793, Energy: -54.637281-0.002638j
[2025-07-30 07:09:51] [Iter 885/4650] R2[434/600], Temp: 0.1773, Energy: -54.656443+0.001775j
[2025-07-30 07:10:01] [Iter 886/4650] R2[435/600], Temp: 0.1753, Energy: -54.633194-0.004593j
[2025-07-30 07:10:11] [Iter 887/4650] R2[436/600], Temp: 0.1733, Energy: -54.617226+0.002293j
[2025-07-30 07:10:21] [Iter 888/4650] R2[437/600], Temp: 0.1713, Energy: -54.644236+0.001232j
[2025-07-30 07:10:32] [Iter 889/4650] R2[438/600], Temp: 0.1693, Energy: -54.574449+0.000077j
[2025-07-30 07:10:42] [Iter 890/4650] R2[439/600], Temp: 0.1674, Energy: -54.580668+0.000044j
[2025-07-30 07:10:52] [Iter 891/4650] R2[440/600], Temp: 0.1654, Energy: -54.529949-0.002872j
[2025-07-30 07:11:02] [Iter 892/4650] R2[441/600], Temp: 0.1635, Energy: -54.513383+0.000266j
[2025-07-30 07:11:12] [Iter 893/4650] R2[442/600], Temp: 0.1616, Energy: -54.624599-0.000717j
[2025-07-30 07:11:22] [Iter 894/4650] R2[443/600], Temp: 0.1596, Energy: -54.571120-0.002470j
[2025-07-30 07:11:32] [Iter 895/4650] R2[444/600], Temp: 0.1577, Energy: -54.627967-0.000066j
[2025-07-30 07:11:42] [Iter 896/4650] R2[445/600], Temp: 0.1558, Energy: -54.618582+0.001873j
[2025-07-30 07:11:53] [Iter 897/4650] R2[446/600], Temp: 0.1539, Energy: -54.616895-0.002833j
[2025-07-30 07:12:03] [Iter 898/4650] R2[447/600], Temp: 0.1520, Energy: -54.601110-0.004152j
[2025-07-30 07:12:13] [Iter 899/4650] R2[448/600], Temp: 0.1502, Energy: -54.604137+0.000766j
[2025-07-30 07:12:23] [Iter 900/4650] R2[449/600], Temp: 0.1483, Energy: -54.554211+0.000643j
[2025-07-30 07:12:33] [Iter 901/4650] R2[450/600], Temp: 0.1464, Energy: -54.574431-0.003578j
[2025-07-30 07:12:43] [Iter 902/4650] R2[451/600], Temp: 0.1446, Energy: -54.491834-0.001149j
[2025-07-30 07:12:53] [Iter 903/4650] R2[452/600], Temp: 0.1428, Energy: -54.472393-0.001269j
[2025-07-30 07:13:03] [Iter 904/4650] R2[453/600], Temp: 0.1409, Energy: -54.506095+0.001811j
[2025-07-30 07:13:14] [Iter 905/4650] R2[454/600], Temp: 0.1391, Energy: -54.488935-0.001054j
[2025-07-30 07:13:24] [Iter 906/4650] R2[455/600], Temp: 0.1373, Energy: -54.549790-0.000584j
[2025-07-30 07:13:34] [Iter 907/4650] R2[456/600], Temp: 0.1355, Energy: -54.526805-0.002437j
[2025-07-30 07:13:44] [Iter 908/4650] R2[457/600], Temp: 0.1337, Energy: -54.595574+0.003374j
[2025-07-30 07:13:54] [Iter 909/4650] R2[458/600], Temp: 0.1320, Energy: -54.523688+0.002563j
[2025-07-30 07:14:04] [Iter 910/4650] R2[459/600], Temp: 0.1302, Energy: -54.563348-0.000944j
[2025-07-30 07:14:14] [Iter 911/4650] R2[460/600], Temp: 0.1284, Energy: -54.558061-0.002398j
[2025-07-30 07:14:25] [Iter 912/4650] R2[461/600], Temp: 0.1267, Energy: -54.549939-0.002637j
[2025-07-30 07:14:35] [Iter 913/4650] R2[462/600], Temp: 0.1249, Energy: -54.563773-0.001318j
[2025-07-30 07:14:45] [Iter 914/4650] R2[463/600], Temp: 0.1232, Energy: -54.508025+0.002255j
[2025-07-30 07:14:55] [Iter 915/4650] R2[464/600], Temp: 0.1215, Energy: -54.581660-0.003059j
[2025-07-30 07:15:05] [Iter 916/4650] R2[465/600], Temp: 0.1198, Energy: -54.574129-0.000111j
[2025-07-30 07:15:15] [Iter 917/4650] R2[466/600], Temp: 0.1181, Energy: -54.572997-0.000160j
[2025-07-30 07:15:25] [Iter 918/4650] R2[467/600], Temp: 0.1164, Energy: -54.600736-0.003697j
[2025-07-30 07:15:35] [Iter 919/4650] R2[468/600], Temp: 0.1147, Energy: -54.548577-0.005808j
[2025-07-30 07:15:46] [Iter 920/4650] R2[469/600], Temp: 0.1131, Energy: -54.587805+0.005165j
[2025-07-30 07:15:56] [Iter 921/4650] R2[470/600], Temp: 0.1114, Energy: -54.576098-0.005561j
[2025-07-30 07:16:06] [Iter 922/4650] R2[471/600], Temp: 0.1098, Energy: -54.598545-0.004381j
[2025-07-30 07:16:16] [Iter 923/4650] R2[472/600], Temp: 0.1082, Energy: -54.644931-0.001099j
[2025-07-30 07:16:26] [Iter 924/4650] R2[473/600], Temp: 0.1065, Energy: -54.623619-0.000155j
[2025-07-30 07:16:36] [Iter 925/4650] R2[474/600], Temp: 0.1049, Energy: -54.644117-0.002382j
[2025-07-30 07:16:46] [Iter 926/4650] R2[475/600], Temp: 0.1033, Energy: -54.597598+0.003401j
[2025-07-30 07:16:56] [Iter 927/4650] R2[476/600], Temp: 0.1017, Energy: -54.600560-0.001067j
[2025-07-30 07:17:06] [Iter 928/4650] R2[477/600], Temp: 0.1002, Energy: -54.607162-0.000521j
[2025-07-30 07:17:17] [Iter 929/4650] R2[478/600], Temp: 0.0986, Energy: -54.631262-0.000804j
[2025-07-30 07:17:27] [Iter 930/4650] R2[479/600], Temp: 0.0970, Energy: -54.640804+0.000074j
[2025-07-30 07:17:37] [Iter 931/4650] R2[480/600], Temp: 0.0955, Energy: -54.628017-0.002077j
[2025-07-30 07:17:47] [Iter 932/4650] R2[481/600], Temp: 0.0940, Energy: -54.602432+0.004031j
[2025-07-30 07:17:57] [Iter 933/4650] R2[482/600], Temp: 0.0924, Energy: -54.659505-0.003037j
[2025-07-30 07:18:07] [Iter 934/4650] R2[483/600], Temp: 0.0909, Energy: -54.526076+0.002942j
[2025-07-30 07:18:17] [Iter 935/4650] R2[484/600], Temp: 0.0894, Energy: -54.550290+0.000501j
[2025-07-30 07:18:27] [Iter 936/4650] R2[485/600], Temp: 0.0879, Energy: -54.591289+0.003022j
[2025-07-30 07:18:38] [Iter 937/4650] R2[486/600], Temp: 0.0865, Energy: -54.604340+0.000390j
[2025-07-30 07:18:48] [Iter 938/4650] R2[487/600], Temp: 0.0850, Energy: -54.636454-0.001793j
[2025-07-30 07:18:58] [Iter 939/4650] R2[488/600], Temp: 0.0835, Energy: -54.591882+0.001510j
[2025-07-30 07:19:08] [Iter 940/4650] R2[489/600], Temp: 0.0821, Energy: -54.616662-0.000076j
[2025-07-30 07:19:18] [Iter 941/4650] R2[490/600], Temp: 0.0807, Energy: -54.634876+0.000018j
[2025-07-30 07:19:28] [Iter 942/4650] R2[491/600], Temp: 0.0792, Energy: -54.654469-0.006154j
[2025-07-30 07:19:38] [Iter 943/4650] R2[492/600], Temp: 0.0778, Energy: -54.594339-0.004636j
[2025-07-30 07:19:48] [Iter 944/4650] R2[493/600], Temp: 0.0764, Energy: -54.578739+0.002399j
[2025-07-30 07:19:59] [Iter 945/4650] R2[494/600], Temp: 0.0751, Energy: -54.533410-0.001347j
[2025-07-30 07:20:09] [Iter 946/4650] R2[495/600], Temp: 0.0737, Energy: -54.485151-0.000081j
[2025-07-30 07:20:19] [Iter 947/4650] R2[496/600], Temp: 0.0723, Energy: -54.534221-0.000691j
[2025-07-30 07:20:29] [Iter 948/4650] R2[497/600], Temp: 0.0710, Energy: -54.489655-0.002854j
[2025-07-30 07:20:39] [Iter 949/4650] R2[498/600], Temp: 0.0696, Energy: -54.515229+0.002600j
[2025-07-30 07:20:49] [Iter 950/4650] R2[499/600], Temp: 0.0683, Energy: -54.581488+0.001522j
[2025-07-30 07:20:59] [Iter 951/4650] R2[500/600], Temp: 0.0670, Energy: -54.573900+0.000457j
[2025-07-30 07:21:10] [Iter 952/4650] R2[501/600], Temp: 0.0657, Energy: -54.598477+0.000172j
[2025-07-30 07:21:20] [Iter 953/4650] R2[502/600], Temp: 0.0644, Energy: -54.576940+0.002024j
[2025-07-30 07:21:30] [Iter 954/4650] R2[503/600], Temp: 0.0631, Energy: -54.561971+0.002492j
[2025-07-30 07:21:40] [Iter 955/4650] R2[504/600], Temp: 0.0618, Energy: -54.536665+0.003342j
[2025-07-30 07:21:50] [Iter 956/4650] R2[505/600], Temp: 0.0606, Energy: -54.511911-0.002144j
[2025-07-30 07:22:00] [Iter 957/4650] R2[506/600], Temp: 0.0593, Energy: -54.525437-0.000462j
[2025-07-30 07:22:10] [Iter 958/4650] R2[507/600], Temp: 0.0581, Energy: -54.588897-0.003112j
[2025-07-30 07:22:20] [Iter 959/4650] R2[508/600], Temp: 0.0569, Energy: -54.592178+0.000537j
[2025-07-30 07:22:30] [Iter 960/4650] R2[509/600], Temp: 0.0557, Energy: -54.544803-0.005705j
[2025-07-30 07:22:41] [Iter 961/4650] R2[510/600], Temp: 0.0545, Energy: -54.578309-0.001935j
[2025-07-30 07:22:51] [Iter 962/4650] R2[511/600], Temp: 0.0533, Energy: -54.608500-0.002428j
[2025-07-30 07:23:01] [Iter 963/4650] R2[512/600], Temp: 0.0521, Energy: -54.666898-0.001668j
[2025-07-30 07:23:11] [Iter 964/4650] R2[513/600], Temp: 0.0510, Energy: -54.685953+0.001418j
[2025-07-30 07:23:21] [Iter 965/4650] R2[514/600], Temp: 0.0498, Energy: -54.620523+0.001315j
[2025-07-30 07:23:31] [Iter 966/4650] R2[515/600], Temp: 0.0487, Energy: -54.586303+0.002169j
[2025-07-30 07:23:41] [Iter 967/4650] R2[516/600], Temp: 0.0476, Energy: -54.562563+0.002132j
[2025-07-30 07:23:51] [Iter 968/4650] R2[517/600], Temp: 0.0465, Energy: -54.608940-0.000318j
[2025-07-30 07:24:02] [Iter 969/4650] R2[518/600], Temp: 0.0454, Energy: -54.631398-0.000634j
[2025-07-30 07:24:12] [Iter 970/4650] R2[519/600], Temp: 0.0443, Energy: -54.553023-0.000751j
[2025-07-30 07:24:22] [Iter 971/4650] R2[520/600], Temp: 0.0432, Energy: -54.577550+0.004269j
[2025-07-30 07:24:32] [Iter 972/4650] R2[521/600], Temp: 0.0422, Energy: -54.581162+0.000147j
[2025-07-30 07:24:42] [Iter 973/4650] R2[522/600], Temp: 0.0411, Energy: -54.550241-0.005341j
[2025-07-30 07:24:52] [Iter 974/4650] R2[523/600], Temp: 0.0401, Energy: -54.544996-0.001690j
[2025-07-30 07:25:02] [Iter 975/4650] R2[524/600], Temp: 0.0391, Energy: -54.601557-0.001988j
[2025-07-30 07:25:12] [Iter 976/4650] R2[525/600], Temp: 0.0381, Energy: -54.578671+0.001854j
[2025-07-30 07:25:22] [Iter 977/4650] R2[526/600], Temp: 0.0371, Energy: -54.556100-0.003161j
[2025-07-30 07:25:33] [Iter 978/4650] R2[527/600], Temp: 0.0361, Energy: -54.617575-0.002642j
[2025-07-30 07:25:43] [Iter 979/4650] R2[528/600], Temp: 0.0351, Energy: -54.625525+0.001989j
[2025-07-30 07:25:53] [Iter 980/4650] R2[529/600], Temp: 0.0342, Energy: -54.604864+0.000598j
[2025-07-30 07:26:03] [Iter 981/4650] R2[530/600], Temp: 0.0332, Energy: -54.569921-0.001909j
[2025-07-30 07:26:13] [Iter 982/4650] R2[531/600], Temp: 0.0323, Energy: -54.557284+0.001873j
[2025-07-30 07:26:23] [Iter 983/4650] R2[532/600], Temp: 0.0314, Energy: -54.587670+0.000358j
[2025-07-30 07:26:33] [Iter 984/4650] R2[533/600], Temp: 0.0305, Energy: -54.550929+0.000110j
[2025-07-30 07:26:44] [Iter 985/4650] R2[534/600], Temp: 0.0296, Energy: -54.586100+0.000261j
[2025-07-30 07:26:54] [Iter 986/4650] R2[535/600], Temp: 0.0287, Energy: -54.564178+0.003925j
[2025-07-30 07:27:04] [Iter 987/4650] R2[536/600], Temp: 0.0278, Energy: -54.575415-0.006711j
[2025-07-30 07:27:14] [Iter 988/4650] R2[537/600], Temp: 0.0270, Energy: -54.636724-0.000430j
[2025-07-30 07:27:24] [Iter 989/4650] R2[538/600], Temp: 0.0261, Energy: -54.599068-0.005157j
[2025-07-30 07:27:34] [Iter 990/4650] R2[539/600], Temp: 0.0253, Energy: -54.609006-0.002238j
[2025-07-30 07:27:44] [Iter 991/4650] R2[540/600], Temp: 0.0245, Energy: -54.555501-0.003189j
[2025-07-30 07:27:55] [Iter 992/4650] R2[541/600], Temp: 0.0237, Energy: -54.584281+0.001688j
[2025-07-30 07:28:05] [Iter 993/4650] R2[542/600], Temp: 0.0229, Energy: -54.575141-0.000266j
[2025-07-30 07:28:15] [Iter 994/4650] R2[543/600], Temp: 0.0221, Energy: -54.520947+0.003546j
[2025-07-30 07:28:25] [Iter 995/4650] R2[544/600], Temp: 0.0213, Energy: -54.547158-0.004992j
[2025-07-30 07:28:35] [Iter 996/4650] R2[545/600], Temp: 0.0206, Energy: -54.573283-0.000116j
[2025-07-30 07:28:45] [Iter 997/4650] R2[546/600], Temp: 0.0199, Energy: -54.599075+0.003915j
[2025-07-30 07:28:55] [Iter 998/4650] R2[547/600], Temp: 0.0191, Energy: -54.581650+0.000772j
[2025-07-30 07:29:05] [Iter 999/4650] R2[548/600], Temp: 0.0184, Energy: -54.581970+0.000740j
[2025-07-30 07:29:15] [Iter 1000/4650] R2[549/600], Temp: 0.0177, Energy: -54.593446+0.002853j
[2025-07-30 07:29:15] ✓ Checkpoint saved: checkpoint_iter_001000.pkl
[2025-07-30 07:29:26] [Iter 1001/4650] R2[550/600], Temp: 0.0170, Energy: -54.579102+0.000485j
[2025-07-30 07:29:36] [Iter 1002/4650] R2[551/600], Temp: 0.0164, Energy: -54.606507+0.004564j
[2025-07-30 07:29:46] [Iter 1003/4650] R2[552/600], Temp: 0.0157, Energy: -54.598390+0.001241j
[2025-07-30 07:29:56] [Iter 1004/4650] R2[553/600], Temp: 0.0151, Energy: -54.545271-0.001162j
[2025-07-30 07:30:06] [Iter 1005/4650] R2[554/600], Temp: 0.0144, Energy: -54.547288-0.001067j
[2025-07-30 07:30:16] [Iter 1006/4650] R2[555/600], Temp: 0.0138, Energy: -54.603343-0.001574j
[2025-07-30 07:30:26] [Iter 1007/4650] R2[556/600], Temp: 0.0132, Energy: -54.634420-0.001241j
[2025-07-30 07:30:36] [Iter 1008/4650] R2[557/600], Temp: 0.0126, Energy: -54.619810-0.000764j
[2025-07-30 07:30:47] [Iter 1009/4650] R2[558/600], Temp: 0.0120, Energy: -54.562354+0.000309j
[2025-07-30 07:30:57] [Iter 1010/4650] R2[559/600], Temp: 0.0115, Energy: -54.535702-0.003123j
[2025-07-30 07:31:07] [Iter 1011/4650] R2[560/600], Temp: 0.0109, Energy: -54.595409-0.001970j
[2025-07-30 07:31:17] [Iter 1012/4650] R2[561/600], Temp: 0.0104, Energy: -54.632135+0.004061j
[2025-07-30 07:31:27] [Iter 1013/4650] R2[562/600], Temp: 0.0099, Energy: -54.535010+0.002164j
[2025-07-30 07:31:37] [Iter 1014/4650] R2[563/600], Temp: 0.0094, Energy: -54.548503-0.000905j
[2025-07-30 07:31:47] [Iter 1015/4650] R2[564/600], Temp: 0.0089, Energy: -54.529920-0.002219j
[2025-07-30 07:31:57] [Iter 1016/4650] R2[565/600], Temp: 0.0084, Energy: -54.564692+0.000514j
[2025-07-30 07:32:08] [Iter 1017/4650] R2[566/600], Temp: 0.0079, Energy: -54.622922-0.003011j
[2025-07-30 07:32:18] [Iter 1018/4650] R2[567/600], Temp: 0.0074, Energy: -54.618952-0.000319j
[2025-07-30 07:32:28] [Iter 1019/4650] R2[568/600], Temp: 0.0070, Energy: -54.626337-0.000456j
[2025-07-30 07:32:38] [Iter 1020/4650] R2[569/600], Temp: 0.0066, Energy: -54.629313+0.000821j
[2025-07-30 07:32:48] [Iter 1021/4650] R2[570/600], Temp: 0.0062, Energy: -54.636098+0.001531j
[2025-07-30 07:32:58] [Iter 1022/4650] R2[571/600], Temp: 0.0058, Energy: -54.618993-0.001734j
[2025-07-30 07:33:08] [Iter 1023/4650] R2[572/600], Temp: 0.0054, Energy: -54.659012-0.001848j
[2025-07-30 07:33:18] [Iter 1024/4650] R2[573/600], Temp: 0.0050, Energy: -54.590781-0.002236j
[2025-07-30 07:33:29] [Iter 1025/4650] R2[574/600], Temp: 0.0046, Energy: -54.595138-0.001349j
[2025-07-30 07:33:39] [Iter 1026/4650] R2[575/600], Temp: 0.0043, Energy: -54.571058-0.000864j
[2025-07-30 07:33:49] [Iter 1027/4650] R2[576/600], Temp: 0.0039, Energy: -54.572898-0.001170j
[2025-07-30 07:33:59] [Iter 1028/4650] R2[577/600], Temp: 0.0036, Energy: -54.571795-0.001619j
[2025-07-30 07:34:09] [Iter 1029/4650] R2[578/600], Temp: 0.0033, Energy: -54.567893-0.002130j
[2025-07-30 07:34:19] [Iter 1030/4650] R2[579/600], Temp: 0.0030, Energy: -54.560082+0.001238j
[2025-07-30 07:34:29] [Iter 1031/4650] R2[580/600], Temp: 0.0027, Energy: -54.608367+0.002030j
[2025-07-30 07:34:39] [Iter 1032/4650] R2[581/600], Temp: 0.0025, Energy: -54.657760-0.001012j
[2025-07-30 07:34:50] [Iter 1033/4650] R2[582/600], Temp: 0.0022, Energy: -54.610222-0.002719j
[2025-07-30 07:35:00] [Iter 1034/4650] R2[583/600], Temp: 0.0020, Energy: -54.592870+0.000994j
[2025-07-30 07:35:10] [Iter 1035/4650] R2[584/600], Temp: 0.0018, Energy: -54.589803+0.005954j
[2025-07-30 07:35:20] [Iter 1036/4650] R2[585/600], Temp: 0.0015, Energy: -54.586383+0.001445j
[2025-07-30 07:35:30] [Iter 1037/4650] R2[586/600], Temp: 0.0013, Energy: -54.625303+0.001611j
[2025-07-30 07:35:40] [Iter 1038/4650] R2[587/600], Temp: 0.0012, Energy: -54.618387-0.003603j
[2025-07-30 07:35:50] [Iter 1039/4650] R2[588/600], Temp: 0.0010, Energy: -54.605886-0.001204j
[2025-07-30 07:36:01] [Iter 1040/4650] R2[589/600], Temp: 0.0008, Energy: -54.597513+0.001503j
[2025-07-30 07:36:11] [Iter 1041/4650] R2[590/600], Temp: 0.0007, Energy: -54.609113+0.000372j
[2025-07-30 07:36:21] [Iter 1042/4650] R2[591/600], Temp: 0.0006, Energy: -54.637803-0.002084j
[2025-07-30 07:36:31] [Iter 1043/4650] R2[592/600], Temp: 0.0004, Energy: -54.612494-0.000632j
[2025-07-30 07:36:41] [Iter 1044/4650] R2[593/600], Temp: 0.0003, Energy: -54.555097+0.002336j
[2025-07-30 07:36:51] [Iter 1045/4650] R2[594/600], Temp: 0.0002, Energy: -54.537710-0.000297j
[2025-07-30 07:37:01] [Iter 1046/4650] R2[595/600], Temp: 0.0002, Energy: -54.556138+0.000154j
[2025-07-30 07:37:11] [Iter 1047/4650] R2[596/600], Temp: 0.0001, Energy: -54.592226+0.000284j
[2025-07-30 07:37:21] [Iter 1048/4650] R2[597/600], Temp: 0.0001, Energy: -54.612971+0.002166j
[2025-07-30 07:37:32] [Iter 1049/4650] R2[598/600], Temp: 0.0000, Energy: -54.616319-0.001803j
[2025-07-30 07:37:42] [Iter 1050/4650] R2[599/600], Temp: 0.0000, Energy: -54.579234-0.002137j
[2025-07-30 07:37:42] RESTART #3 | Period: 1200
[2025-07-30 07:37:52] [Iter 1051/4650] R3[0/1200], Temp: 1.0000, Energy: -54.612509-0.000973j
[2025-07-30 07:38:02] [Iter 1052/4650] R3[1/1200], Temp: 1.0000, Energy: -54.648454+0.000817j
[2025-07-30 07:38:12] [Iter 1053/4650] R3[2/1200], Temp: 1.0000, Energy: -54.638787-0.000671j
[2025-07-30 07:38:22] [Iter 1054/4650] R3[3/1200], Temp: 1.0000, Energy: -54.640479-0.003715j
[2025-07-30 07:38:32] [Iter 1055/4650] R3[4/1200], Temp: 1.0000, Energy: -54.638389+0.000333j
[2025-07-30 07:38:42] [Iter 1056/4650] R3[5/1200], Temp: 1.0000, Energy: -54.653645-0.000604j
[2025-07-30 07:38:53] [Iter 1057/4650] R3[6/1200], Temp: 0.9999, Energy: -54.614001+0.001304j
[2025-07-30 07:39:03] [Iter 1058/4650] R3[7/1200], Temp: 0.9999, Energy: -54.633541-0.000159j
[2025-07-30 07:39:13] [Iter 1059/4650] R3[8/1200], Temp: 0.9999, Energy: -54.598268-0.002782j
[2025-07-30 07:39:23] [Iter 1060/4650] R3[9/1200], Temp: 0.9999, Energy: -54.588454+0.000220j
[2025-07-30 07:39:33] [Iter 1061/4650] R3[10/1200], Temp: 0.9998, Energy: -54.578289+0.002600j
[2025-07-30 07:39:43] [Iter 1062/4650] R3[11/1200], Temp: 0.9998, Energy: -54.495341-0.001668j
[2025-07-30 07:39:53] [Iter 1063/4650] R3[12/1200], Temp: 0.9998, Energy: -54.550510-0.005249j
[2025-07-30 07:40:04] [Iter 1064/4650] R3[13/1200], Temp: 0.9997, Energy: -54.567053-0.001528j
[2025-07-30 07:40:14] [Iter 1065/4650] R3[14/1200], Temp: 0.9997, Energy: -54.516403+0.001117j
[2025-07-30 07:40:24] [Iter 1066/4650] R3[15/1200], Temp: 0.9996, Energy: -54.497796-0.000300j
[2025-07-30 07:40:34] [Iter 1067/4650] R3[16/1200], Temp: 0.9996, Energy: -54.464692-0.001555j
[2025-07-30 07:40:44] [Iter 1068/4650] R3[17/1200], Temp: 0.9995, Energy: -54.482521+0.005516j
[2025-07-30 07:40:54] [Iter 1069/4650] R3[18/1200], Temp: 0.9994, Energy: -54.526934-0.000237j
[2025-07-30 07:41:04] [Iter 1070/4650] R3[19/1200], Temp: 0.9994, Energy: -54.616840+0.000663j
[2025-07-30 07:41:15] [Iter 1071/4650] R3[20/1200], Temp: 0.9993, Energy: -54.610170+0.001271j
[2025-07-30 07:41:25] [Iter 1072/4650] R3[21/1200], Temp: 0.9992, Energy: -54.586772+0.000284j
[2025-07-30 07:41:35] [Iter 1073/4650] R3[22/1200], Temp: 0.9992, Energy: -54.594474-0.001183j
[2025-07-30 07:41:45] [Iter 1074/4650] R3[23/1200], Temp: 0.9991, Energy: -54.641548-0.002901j
[2025-07-30 07:41:55] [Iter 1075/4650] R3[24/1200], Temp: 0.9990, Energy: -54.612148-0.002054j
[2025-07-30 07:42:05] [Iter 1076/4650] R3[25/1200], Temp: 0.9989, Energy: -54.665605-0.001734j
[2025-07-30 07:42:15] [Iter 1077/4650] R3[26/1200], Temp: 0.9988, Energy: -54.586323-0.001377j
[2025-07-30 07:42:25] [Iter 1078/4650] R3[27/1200], Temp: 0.9988, Energy: -54.617486-0.001996j
[2025-07-30 07:42:36] [Iter 1079/4650] R3[28/1200], Temp: 0.9987, Energy: -54.581586+0.001765j
[2025-07-30 07:42:46] [Iter 1080/4650] R3[29/1200], Temp: 0.9986, Energy: -54.664249-0.001469j
[2025-07-30 07:42:56] [Iter 1081/4650] R3[30/1200], Temp: 0.9985, Energy: -54.638556+0.000575j
[2025-07-30 07:43:06] [Iter 1082/4650] R3[31/1200], Temp: 0.9984, Energy: -54.595907+0.002120j
[2025-07-30 07:43:16] [Iter 1083/4650] R3[32/1200], Temp: 0.9982, Energy: -54.663163+0.004308j
[2025-07-30 07:43:26] [Iter 1084/4650] R3[33/1200], Temp: 0.9981, Energy: -54.625929+0.000789j
[2025-07-30 07:43:36] [Iter 1085/4650] R3[34/1200], Temp: 0.9980, Energy: -54.682564-0.001051j
[2025-07-30 07:43:46] [Iter 1086/4650] R3[35/1200], Temp: 0.9979, Energy: -54.643011-0.001930j
[2025-07-30 07:43:57] [Iter 1087/4650] R3[36/1200], Temp: 0.9978, Energy: -54.628625-0.002855j
[2025-07-30 07:44:07] [Iter 1088/4650] R3[37/1200], Temp: 0.9977, Energy: -54.670883+0.000750j
[2025-07-30 07:44:17] [Iter 1089/4650] R3[38/1200], Temp: 0.9975, Energy: -54.655394+0.001543j
[2025-07-30 07:44:27] [Iter 1090/4650] R3[39/1200], Temp: 0.9974, Energy: -54.676444+0.000429j
[2025-07-30 07:44:37] [Iter 1091/4650] R3[40/1200], Temp: 0.9973, Energy: -54.660476-0.001756j
[2025-07-30 07:44:47] [Iter 1092/4650] R3[41/1200], Temp: 0.9971, Energy: -54.623107+0.000646j
[2025-07-30 07:44:57] [Iter 1093/4650] R3[42/1200], Temp: 0.9970, Energy: -54.571341-0.001571j
[2025-07-30 07:45:07] [Iter 1094/4650] R3[43/1200], Temp: 0.9968, Energy: -54.630296-0.001396j
[2025-07-30 07:45:17] [Iter 1095/4650] R3[44/1200], Temp: 0.9967, Energy: -54.600739-0.000583j
[2025-07-30 07:45:28] [Iter 1096/4650] R3[45/1200], Temp: 0.9965, Energy: -54.599880-0.002173j
[2025-07-30 07:45:38] [Iter 1097/4650] R3[46/1200], Temp: 0.9964, Energy: -54.626833+0.000877j
[2025-07-30 07:45:48] [Iter 1098/4650] R3[47/1200], Temp: 0.9962, Energy: -54.639337+0.000856j
[2025-07-30 07:45:58] [Iter 1099/4650] R3[48/1200], Temp: 0.9961, Energy: -54.613167-0.002014j
[2025-07-30 07:46:08] [Iter 1100/4650] R3[49/1200], Temp: 0.9959, Energy: -54.612703-0.000305j
[2025-07-30 07:46:18] [Iter 1101/4650] R3[50/1200], Temp: 0.9957, Energy: -54.591914+0.002090j
[2025-07-30 07:46:28] [Iter 1102/4650] R3[51/1200], Temp: 0.9955, Energy: -54.599368+0.000443j
[2025-07-30 07:46:39] [Iter 1103/4650] R3[52/1200], Temp: 0.9954, Energy: -54.602744-0.002810j
[2025-07-30 07:46:49] [Iter 1104/4650] R3[53/1200], Temp: 0.9952, Energy: -54.684292+0.005365j
[2025-07-30 07:46:59] [Iter 1105/4650] R3[54/1200], Temp: 0.9950, Energy: -54.579196-0.001058j
[2025-07-30 07:47:09] [Iter 1106/4650] R3[55/1200], Temp: 0.9948, Energy: -54.565488-0.003548j
[2025-07-30 07:47:19] [Iter 1107/4650] R3[56/1200], Temp: 0.9946, Energy: -54.628585-0.000234j
[2025-07-30 07:47:29] [Iter 1108/4650] R3[57/1200], Temp: 0.9944, Energy: -54.688633+0.000382j
[2025-07-30 07:47:39] [Iter 1109/4650] R3[58/1200], Temp: 0.9942, Energy: -54.688155+0.000342j
[2025-07-30 07:47:49] [Iter 1110/4650] R3[59/1200], Temp: 0.9940, Energy: -54.666666-0.001075j
[2025-07-30 07:48:00] [Iter 1111/4650] R3[60/1200], Temp: 0.9938, Energy: -54.659327-0.001083j
[2025-07-30 07:48:10] [Iter 1112/4650] R3[61/1200], Temp: 0.9936, Energy: -54.634888+0.001490j
[2025-07-30 07:48:20] [Iter 1113/4650] R3[62/1200], Temp: 0.9934, Energy: -54.590324-0.000302j
[2025-07-30 07:48:30] [Iter 1114/4650] R3[63/1200], Temp: 0.9932, Energy: -54.639196+0.007253j
[2025-07-30 07:48:40] [Iter 1115/4650] R3[64/1200], Temp: 0.9930, Energy: -54.613445+0.002626j
[2025-07-30 07:48:50] [Iter 1116/4650] R3[65/1200], Temp: 0.9928, Energy: -54.543431-0.000130j
[2025-07-30 07:49:00] [Iter 1117/4650] R3[66/1200], Temp: 0.9926, Energy: -54.604435-0.003838j
[2025-07-30 07:49:10] [Iter 1118/4650] R3[67/1200], Temp: 0.9923, Energy: -54.596330-0.001651j
[2025-07-30 07:49:21] [Iter 1119/4650] R3[68/1200], Temp: 0.9921, Energy: -54.555109-0.005819j
[2025-07-30 07:49:31] [Iter 1120/4650] R3[69/1200], Temp: 0.9919, Energy: -54.597435-0.001270j
[2025-07-30 07:49:41] [Iter 1121/4650] R3[70/1200], Temp: 0.9916, Energy: -54.608479-0.000219j
[2025-07-30 07:49:51] [Iter 1122/4650] R3[71/1200], Temp: 0.9914, Energy: -54.548431-0.001039j
[2025-07-30 07:50:01] [Iter 1123/4650] R3[72/1200], Temp: 0.9911, Energy: -54.583721+0.000158j
[2025-07-30 07:50:11] [Iter 1124/4650] R3[73/1200], Temp: 0.9909, Energy: -54.571325+0.000033j
[2025-07-30 07:50:21] [Iter 1125/4650] R3[74/1200], Temp: 0.9906, Energy: -54.552415+0.003176j
[2025-07-30 07:50:31] [Iter 1126/4650] R3[75/1200], Temp: 0.9904, Energy: -54.601174+0.002438j
[2025-07-30 07:50:42] [Iter 1127/4650] R3[76/1200], Temp: 0.9901, Energy: -54.576004-0.000282j
[2025-07-30 07:50:52] [Iter 1128/4650] R3[77/1200], Temp: 0.9899, Energy: -54.589972+0.000140j
[2025-07-30 07:51:02] [Iter 1129/4650] R3[78/1200], Temp: 0.9896, Energy: -54.539475+0.002302j
[2025-07-30 07:51:12] [Iter 1130/4650] R3[79/1200], Temp: 0.9893, Energy: -54.584731+0.001229j
[2025-07-30 07:51:22] [Iter 1131/4650] R3[80/1200], Temp: 0.9891, Energy: -54.626758+0.002752j
[2025-07-30 07:51:32] [Iter 1132/4650] R3[81/1200], Temp: 0.9888, Energy: -54.682449+0.001504j
[2025-07-30 07:51:42] [Iter 1133/4650] R3[82/1200], Temp: 0.9885, Energy: -54.624711+0.003962j
[2025-07-30 07:51:52] [Iter 1134/4650] R3[83/1200], Temp: 0.9882, Energy: -54.633036+0.001058j
[2025-07-30 07:52:03] [Iter 1135/4650] R3[84/1200], Temp: 0.9880, Energy: -54.523448-0.000852j
[2025-07-30 07:52:13] [Iter 1136/4650] R3[85/1200], Temp: 0.9877, Energy: -54.510497-0.000882j
[2025-07-30 07:52:23] [Iter 1137/4650] R3[86/1200], Temp: 0.9874, Energy: -54.578841-0.001757j
[2025-07-30 07:52:33] [Iter 1138/4650] R3[87/1200], Temp: 0.9871, Energy: -54.570074-0.003933j
[2025-07-30 07:52:43] [Iter 1139/4650] R3[88/1200], Temp: 0.9868, Energy: -54.611713+0.000058j
[2025-07-30 07:52:53] [Iter 1140/4650] R3[89/1200], Temp: 0.9865, Energy: -54.601431-0.002079j
[2025-07-30 07:53:03] [Iter 1141/4650] R3[90/1200], Temp: 0.9862, Energy: -54.571812-0.001250j
[2025-07-30 07:53:13] [Iter 1142/4650] R3[91/1200], Temp: 0.9859, Energy: -54.609059-0.000447j
[2025-07-30 07:53:23] [Iter 1143/4650] R3[92/1200], Temp: 0.9856, Energy: -54.644760+0.000277j
[2025-07-30 07:53:34] [Iter 1144/4650] R3[93/1200], Temp: 0.9853, Energy: -54.614924+0.000981j
[2025-07-30 07:53:44] [Iter 1145/4650] R3[94/1200], Temp: 0.9849, Energy: -54.628867-0.000904j
[2025-07-30 07:53:54] [Iter 1146/4650] R3[95/1200], Temp: 0.9846, Energy: -54.608095-0.003429j
[2025-07-30 07:54:04] [Iter 1147/4650] R3[96/1200], Temp: 0.9843, Energy: -54.614059-0.000459j
[2025-07-30 07:54:14] [Iter 1148/4650] R3[97/1200], Temp: 0.9840, Energy: -54.568150+0.004132j
[2025-07-30 07:54:24] [Iter 1149/4650] R3[98/1200], Temp: 0.9836, Energy: -54.548874-0.001918j
[2025-07-30 07:54:34] [Iter 1150/4650] R3[99/1200], Temp: 0.9833, Energy: -54.582762+0.000904j
[2025-07-30 07:54:45] [Iter 1151/4650] R3[100/1200], Temp: 0.9830, Energy: -54.563543+0.000326j
[2025-07-30 07:54:55] [Iter 1152/4650] R3[101/1200], Temp: 0.9826, Energy: -54.599684-0.000580j
[2025-07-30 07:55:05] [Iter 1153/4650] R3[102/1200], Temp: 0.9823, Energy: -54.606613+0.000603j
[2025-07-30 07:55:15] [Iter 1154/4650] R3[103/1200], Temp: 0.9819, Energy: -54.642050+0.001171j
[2025-07-30 07:55:25] [Iter 1155/4650] R3[104/1200], Temp: 0.9816, Energy: -54.640003-0.000489j
[2025-07-30 07:55:35] [Iter 1156/4650] R3[105/1200], Temp: 0.9812, Energy: -54.596801-0.002302j
[2025-07-30 07:55:45] [Iter 1157/4650] R3[106/1200], Temp: 0.9809, Energy: -54.623382+0.002076j
[2025-07-30 07:55:55] [Iter 1158/4650] R3[107/1200], Temp: 0.9805, Energy: -54.598593-0.002862j
[2025-07-30 07:56:06] [Iter 1159/4650] R3[108/1200], Temp: 0.9801, Energy: -54.623792+0.001635j
[2025-07-30 07:56:16] [Iter 1160/4650] R3[109/1200], Temp: 0.9798, Energy: -54.655727-0.000732j
[2025-07-30 07:56:26] [Iter 1161/4650] R3[110/1200], Temp: 0.9794, Energy: -54.628323-0.001548j
[2025-07-30 07:56:36] [Iter 1162/4650] R3[111/1200], Temp: 0.9790, Energy: -54.668405+0.000170j
[2025-07-30 07:56:46] [Iter 1163/4650] R3[112/1200], Temp: 0.9787, Energy: -54.687876+0.002400j
[2025-07-30 07:56:56] [Iter 1164/4650] R3[113/1200], Temp: 0.9783, Energy: -54.626484+0.000985j
[2025-07-30 07:57:06] [Iter 1165/4650] R3[114/1200], Temp: 0.9779, Energy: -54.568340+0.001384j
[2025-07-30 07:57:16] [Iter 1166/4650] R3[115/1200], Temp: 0.9775, Energy: -54.598982+0.001948j
[2025-07-30 07:57:26] [Iter 1167/4650] R3[116/1200], Temp: 0.9771, Energy: -54.589486+0.002851j
[2025-07-30 07:57:37] [Iter 1168/4650] R3[117/1200], Temp: 0.9767, Energy: -54.536425+0.000698j
[2025-07-30 07:57:47] [Iter 1169/4650] R3[118/1200], Temp: 0.9763, Energy: -54.541654+0.003365j
[2025-07-30 07:57:57] [Iter 1170/4650] R3[119/1200], Temp: 0.9759, Energy: -54.631666-0.002049j
[2025-07-30 07:58:07] [Iter 1171/4650] R3[120/1200], Temp: 0.9755, Energy: -54.640924-0.001480j
[2025-07-30 07:58:17] [Iter 1172/4650] R3[121/1200], Temp: 0.9751, Energy: -54.641762+0.001286j
[2025-07-30 07:58:27] [Iter 1173/4650] R3[122/1200], Temp: 0.9747, Energy: -54.583305+0.002063j
[2025-07-30 07:58:37] [Iter 1174/4650] R3[123/1200], Temp: 0.9743, Energy: -54.624727+0.001704j
[2025-07-30 07:58:47] [Iter 1175/4650] R3[124/1200], Temp: 0.9739, Energy: -54.636623+0.000353j
[2025-07-30 07:58:58] [Iter 1176/4650] R3[125/1200], Temp: 0.9735, Energy: -54.631436+0.000179j
[2025-07-30 07:59:08] [Iter 1177/4650] R3[126/1200], Temp: 0.9730, Energy: -54.617441-0.000897j
[2025-07-30 07:59:18] [Iter 1178/4650] R3[127/1200], Temp: 0.9726, Energy: -54.590691+0.002715j
[2025-07-30 07:59:28] [Iter 1179/4650] R3[128/1200], Temp: 0.9722, Energy: -54.548353-0.000142j
[2025-07-30 07:59:38] [Iter 1180/4650] R3[129/1200], Temp: 0.9718, Energy: -54.519685+0.000820j
[2025-07-30 07:59:48] [Iter 1181/4650] R3[130/1200], Temp: 0.9713, Energy: -54.537718+0.003155j
[2025-07-30 07:59:58] [Iter 1182/4650] R3[131/1200], Temp: 0.9709, Energy: -54.644509-0.000592j
[2025-07-30 08:00:08] [Iter 1183/4650] R3[132/1200], Temp: 0.9704, Energy: -54.613802-0.003287j
[2025-07-30 08:00:19] [Iter 1184/4650] R3[133/1200], Temp: 0.9700, Energy: -54.644351-0.002683j
[2025-07-30 08:00:29] [Iter 1185/4650] R3[134/1200], Temp: 0.9695, Energy: -54.656190+0.000322j
[2025-07-30 08:00:39] [Iter 1186/4650] R3[135/1200], Temp: 0.9691, Energy: -54.615154+0.000586j
[2025-07-30 08:00:49] [Iter 1187/4650] R3[136/1200], Temp: 0.9686, Energy: -54.573373-0.001403j
[2025-07-30 08:00:59] [Iter 1188/4650] R3[137/1200], Temp: 0.9682, Energy: -54.595433-0.000141j
[2025-07-30 08:01:09] [Iter 1189/4650] R3[138/1200], Temp: 0.9677, Energy: -54.552839+0.003476j
[2025-07-30 08:01:19] [Iter 1190/4650] R3[139/1200], Temp: 0.9673, Energy: -54.623132+0.000970j
[2025-07-30 08:01:29] [Iter 1191/4650] R3[140/1200], Temp: 0.9668, Energy: -54.629939+0.002632j
[2025-07-30 08:01:40] [Iter 1192/4650] R3[141/1200], Temp: 0.9663, Energy: -54.628916+0.002024j
[2025-07-30 08:01:50] [Iter 1193/4650] R3[142/1200], Temp: 0.9658, Energy: -54.598438-0.001231j
[2025-07-30 08:02:00] [Iter 1194/4650] R3[143/1200], Temp: 0.9654, Energy: -54.549283+0.003545j
[2025-07-30 08:02:10] [Iter 1195/4650] R3[144/1200], Temp: 0.9649, Energy: -54.565611-0.001106j
[2025-07-30 08:02:20] [Iter 1196/4650] R3[145/1200], Temp: 0.9644, Energy: -54.620336+0.002085j
[2025-07-30 08:02:30] [Iter 1197/4650] R3[146/1200], Temp: 0.9639, Energy: -54.594705-0.000550j
[2025-07-30 08:02:40] [Iter 1198/4650] R3[147/1200], Temp: 0.9634, Energy: -54.599526-0.002035j
[2025-07-30 08:02:50] [Iter 1199/4650] R3[148/1200], Temp: 0.9629, Energy: -54.557818+0.000405j
[2025-07-30 08:03:01] [Iter 1200/4650] R3[149/1200], Temp: 0.9624, Energy: -54.556916+0.000208j
[2025-07-30 08:03:11] [Iter 1201/4650] R3[150/1200], Temp: 0.9619, Energy: -54.634553+0.003561j
[2025-07-30 08:03:21] [Iter 1202/4650] R3[151/1200], Temp: 0.9614, Energy: -54.593067+0.001492j
[2025-07-30 08:03:31] [Iter 1203/4650] R3[152/1200], Temp: 0.9609, Energy: -54.602034+0.000770j
[2025-07-30 08:03:41] [Iter 1204/4650] R3[153/1200], Temp: 0.9604, Energy: -54.625141+0.003385j
[2025-07-30 08:03:51] [Iter 1205/4650] R3[154/1200], Temp: 0.9599, Energy: -54.567922+0.002558j
[2025-07-30 08:04:01] [Iter 1206/4650] R3[155/1200], Temp: 0.9594, Energy: -54.613766+0.000811j
[2025-07-30 08:04:11] [Iter 1207/4650] R3[156/1200], Temp: 0.9589, Energy: -54.682994-0.000687j
[2025-07-30 08:04:22] [Iter 1208/4650] R3[157/1200], Temp: 0.9584, Energy: -54.617871+0.002289j
[2025-07-30 08:04:32] [Iter 1209/4650] R3[158/1200], Temp: 0.9578, Energy: -54.686703-0.001720j
[2025-07-30 08:04:42] [Iter 1210/4650] R3[159/1200], Temp: 0.9573, Energy: -54.603881-0.000894j
[2025-07-30 08:04:52] [Iter 1211/4650] R3[160/1200], Temp: 0.9568, Energy: -54.566929+0.001373j
[2025-07-30 08:05:02] [Iter 1212/4650] R3[161/1200], Temp: 0.9562, Energy: -54.653357+0.000358j
[2025-07-30 08:05:12] [Iter 1213/4650] R3[162/1200], Temp: 0.9557, Energy: -54.617522-0.002026j
[2025-07-30 08:05:22] [Iter 1214/4650] R3[163/1200], Temp: 0.9552, Energy: -54.641906+0.001181j
[2025-07-30 08:05:32] [Iter 1215/4650] R3[164/1200], Temp: 0.9546, Energy: -54.666031-0.000344j
[2025-07-30 08:05:42] [Iter 1216/4650] R3[165/1200], Temp: 0.9541, Energy: -54.645514-0.000244j
[2025-07-30 08:05:53] [Iter 1217/4650] R3[166/1200], Temp: 0.9535, Energy: -54.684221-0.003395j
[2025-07-30 08:06:03] [Iter 1218/4650] R3[167/1200], Temp: 0.9530, Energy: -54.611702-0.002297j
[2025-07-30 08:06:13] [Iter 1219/4650] R3[168/1200], Temp: 0.9524, Energy: -54.677352-0.002428j
[2025-07-30 08:06:23] [Iter 1220/4650] R3[169/1200], Temp: 0.9519, Energy: -54.644243+0.000588j
[2025-07-30 08:06:33] [Iter 1221/4650] R3[170/1200], Temp: 0.9513, Energy: -54.615748-0.001466j
[2025-07-30 08:06:43] [Iter 1222/4650] R3[171/1200], Temp: 0.9507, Energy: -54.608370-0.000531j
[2025-07-30 08:06:53] [Iter 1223/4650] R3[172/1200], Temp: 0.9502, Energy: -54.605425+0.001588j
[2025-07-30 08:07:04] [Iter 1224/4650] R3[173/1200], Temp: 0.9496, Energy: -54.571927+0.001696j
[2025-07-30 08:07:14] [Iter 1225/4650] R3[174/1200], Temp: 0.9490, Energy: -54.595163-0.001913j
[2025-07-30 08:07:24] [Iter 1226/4650] R3[175/1200], Temp: 0.9484, Energy: -54.615170+0.000282j
[2025-07-30 08:07:34] [Iter 1227/4650] R3[176/1200], Temp: 0.9479, Energy: -54.584948+0.001036j
[2025-07-30 08:07:44] [Iter 1228/4650] R3[177/1200], Temp: 0.9473, Energy: -54.591563+0.001083j
[2025-07-30 08:07:54] [Iter 1229/4650] R3[178/1200], Temp: 0.9467, Energy: -54.583528+0.001161j
[2025-07-30 08:08:04] [Iter 1230/4650] R3[179/1200], Temp: 0.9461, Energy: -54.595522-0.003199j
[2025-07-30 08:08:15] [Iter 1231/4650] R3[180/1200], Temp: 0.9455, Energy: -54.570313-0.000836j
[2025-07-30 08:08:25] [Iter 1232/4650] R3[181/1200], Temp: 0.9449, Energy: -54.565934-0.003277j
[2025-07-30 08:08:35] [Iter 1233/4650] R3[182/1200], Temp: 0.9443, Energy: -54.582922+0.001774j
[2025-07-30 08:08:45] [Iter 1234/4650] R3[183/1200], Temp: 0.9437, Energy: -54.537190-0.004068j
[2025-07-30 08:08:55] [Iter 1235/4650] R3[184/1200], Temp: 0.9431, Energy: -54.590245-0.004615j
[2025-07-30 08:09:05] [Iter 1236/4650] R3[185/1200], Temp: 0.9425, Energy: -54.648965-0.001359j
[2025-07-30 08:09:15] [Iter 1237/4650] R3[186/1200], Temp: 0.9419, Energy: -54.647506+0.000330j
[2025-07-30 08:09:25] [Iter 1238/4650] R3[187/1200], Temp: 0.9413, Energy: -54.707255-0.002497j
[2025-07-30 08:09:36] [Iter 1239/4650] R3[188/1200], Temp: 0.9407, Energy: -54.681332+0.001761j
[2025-07-30 08:09:46] [Iter 1240/4650] R3[189/1200], Temp: 0.9400, Energy: -54.665658+0.001682j
[2025-07-30 08:09:56] [Iter 1241/4650] R3[190/1200], Temp: 0.9394, Energy: -54.625554+0.001669j
[2025-07-30 08:10:06] [Iter 1242/4650] R3[191/1200], Temp: 0.9388, Energy: -54.648748-0.003038j
[2025-07-30 08:10:16] [Iter 1243/4650] R3[192/1200], Temp: 0.9382, Energy: -54.636510-0.002818j
[2025-07-30 08:10:26] [Iter 1244/4650] R3[193/1200], Temp: 0.9375, Energy: -54.610947-0.002023j
[2025-07-30 08:10:36] [Iter 1245/4650] R3[194/1200], Temp: 0.9369, Energy: -54.599863-0.001690j
[2025-07-30 08:10:46] [Iter 1246/4650] R3[195/1200], Temp: 0.9362, Energy: -54.630973+0.003019j
[2025-07-30 08:10:56] [Iter 1247/4650] R3[196/1200], Temp: 0.9356, Energy: -54.666113-0.000144j
[2025-07-30 08:11:07] [Iter 1248/4650] R3[197/1200], Temp: 0.9350, Energy: -54.657214-0.003682j
[2025-07-30 08:11:17] [Iter 1249/4650] R3[198/1200], Temp: 0.9343, Energy: -54.648419-0.002837j
[2025-07-30 08:11:27] [Iter 1250/4650] R3[199/1200], Temp: 0.9337, Energy: -54.609034+0.002866j
[2025-07-30 08:11:37] [Iter 1251/4650] R3[200/1200], Temp: 0.9330, Energy: -54.608215-0.001315j
[2025-07-30 08:11:47] [Iter 1252/4650] R3[201/1200], Temp: 0.9324, Energy: -54.652437+0.004488j
[2025-07-30 08:11:57] [Iter 1253/4650] R3[202/1200], Temp: 0.9317, Energy: -54.657394+0.003489j
[2025-07-30 08:12:07] [Iter 1254/4650] R3[203/1200], Temp: 0.9310, Energy: -54.641716+0.000298j
[2025-07-30 08:12:17] [Iter 1255/4650] R3[204/1200], Temp: 0.9304, Energy: -54.628110+0.003611j
[2025-07-30 08:12:28] [Iter 1256/4650] R3[205/1200], Temp: 0.9297, Energy: -54.618967+0.002892j
[2025-07-30 08:12:38] [Iter 1257/4650] R3[206/1200], Temp: 0.9290, Energy: -54.600586+0.003559j
[2025-07-30 08:12:48] [Iter 1258/4650] R3[207/1200], Temp: 0.9284, Energy: -54.591859+0.001888j
[2025-07-30 08:12:58] [Iter 1259/4650] R3[208/1200], Temp: 0.9277, Energy: -54.621734-0.004122j
[2025-07-30 08:13:08] [Iter 1260/4650] R3[209/1200], Temp: 0.9270, Energy: -54.602116+0.001559j
[2025-07-30 08:13:18] [Iter 1261/4650] R3[210/1200], Temp: 0.9263, Energy: -54.605978-0.002327j
[2025-07-30 08:13:28] [Iter 1262/4650] R3[211/1200], Temp: 0.9256, Energy: -54.630604-0.000417j
[2025-07-30 08:13:38] [Iter 1263/4650] R3[212/1200], Temp: 0.9249, Energy: -54.671346-0.000211j
[2025-07-30 08:13:48] [Iter 1264/4650] R3[213/1200], Temp: 0.9243, Energy: -54.640426-0.000520j
[2025-07-30 08:13:59] [Iter 1265/4650] R3[214/1200], Temp: 0.9236, Energy: -54.595793+0.002107j
[2025-07-30 08:14:09] [Iter 1266/4650] R3[215/1200], Temp: 0.9229, Energy: -54.620702+0.000762j
[2025-07-30 08:14:19] [Iter 1267/4650] R3[216/1200], Temp: 0.9222, Energy: -54.648726-0.000584j
[2025-07-30 08:14:29] [Iter 1268/4650] R3[217/1200], Temp: 0.9215, Energy: -54.620086+0.003379j
[2025-07-30 08:14:39] [Iter 1269/4650] R3[218/1200], Temp: 0.9208, Energy: -54.538945-0.001303j
[2025-07-30 08:14:49] [Iter 1270/4650] R3[219/1200], Temp: 0.9200, Energy: -54.535125-0.002154j
[2025-07-30 08:14:59] [Iter 1271/4650] R3[220/1200], Temp: 0.9193, Energy: -54.592808-0.002625j
[2025-07-30 08:15:09] [Iter 1272/4650] R3[221/1200], Temp: 0.9186, Energy: -54.549253+0.003695j
[2025-07-30 08:15:20] [Iter 1273/4650] R3[222/1200], Temp: 0.9179, Energy: -54.563472-0.002613j
[2025-07-30 08:15:30] [Iter 1274/4650] R3[223/1200], Temp: 0.9172, Energy: -54.561327-0.003410j
[2025-07-30 08:15:40] [Iter 1275/4650] R3[224/1200], Temp: 0.9165, Energy: -54.560361-0.004656j
[2025-07-30 08:15:50] [Iter 1276/4650] R3[225/1200], Temp: 0.9157, Energy: -54.543758+0.000683j
[2025-07-30 08:16:00] [Iter 1277/4650] R3[226/1200], Temp: 0.9150, Energy: -54.620960-0.001721j
[2025-07-30 08:16:10] [Iter 1278/4650] R3[227/1200], Temp: 0.9143, Energy: -54.596099-0.003032j
[2025-07-30 08:16:20] [Iter 1279/4650] R3[228/1200], Temp: 0.9135, Energy: -54.615146+0.002224j
[2025-07-30 08:16:30] [Iter 1280/4650] R3[229/1200], Temp: 0.9128, Energy: -54.575831+0.002235j
[2025-07-30 08:16:41] [Iter 1281/4650] R3[230/1200], Temp: 0.9121, Energy: -54.598678+0.000875j
[2025-07-30 08:16:51] [Iter 1282/4650] R3[231/1200], Temp: 0.9113, Energy: -54.638507-0.001109j
[2025-07-30 08:17:01] [Iter 1283/4650] R3[232/1200], Temp: 0.9106, Energy: -54.635497-0.000877j
[2025-07-30 08:17:11] [Iter 1284/4650] R3[233/1200], Temp: 0.9098, Energy: -54.602980-0.000028j
[2025-07-30 08:17:21] [Iter 1285/4650] R3[234/1200], Temp: 0.9091, Energy: -54.675051-0.000486j
[2025-07-30 08:17:31] [Iter 1286/4650] R3[235/1200], Temp: 0.9083, Energy: -54.631477+0.001105j
[2025-07-30 08:17:41] [Iter 1287/4650] R3[236/1200], Temp: 0.9076, Energy: -54.587616-0.003816j
[2025-07-30 08:17:51] [Iter 1288/4650] R3[237/1200], Temp: 0.9068, Energy: -54.599448-0.002121j
[2025-07-30 08:18:02] [Iter 1289/4650] R3[238/1200], Temp: 0.9060, Energy: -54.611694-0.001964j
[2025-07-30 08:18:12] [Iter 1290/4650] R3[239/1200], Temp: 0.9053, Energy: -54.642383-0.001931j
[2025-07-30 08:18:22] [Iter 1291/4650] R3[240/1200], Temp: 0.9045, Energy: -54.624290+0.001377j
[2025-07-30 08:18:32] [Iter 1292/4650] R3[241/1200], Temp: 0.9037, Energy: -54.622654+0.000635j
[2025-07-30 08:18:42] [Iter 1293/4650] R3[242/1200], Temp: 0.9030, Energy: -54.612346+0.003693j
[2025-07-30 08:18:52] [Iter 1294/4650] R3[243/1200], Temp: 0.9022, Energy: -54.623806+0.001170j
[2025-07-30 08:19:02] [Iter 1295/4650] R3[244/1200], Temp: 0.9014, Energy: -54.585782-0.003506j
[2025-07-30 08:19:12] [Iter 1296/4650] R3[245/1200], Temp: 0.9006, Energy: -54.607760-0.004848j
[2025-07-30 08:19:22] [Iter 1297/4650] R3[246/1200], Temp: 0.8998, Energy: -54.593085+0.002319j
[2025-07-30 08:19:33] [Iter 1298/4650] R3[247/1200], Temp: 0.8991, Energy: -54.567183-0.001643j
[2025-07-30 08:19:43] [Iter 1299/4650] R3[248/1200], Temp: 0.8983, Energy: -54.580645-0.001529j
[2025-07-30 08:19:53] [Iter 1300/4650] R3[249/1200], Temp: 0.8975, Energy: -54.629400+0.000107j
[2025-07-30 08:20:03] [Iter 1301/4650] R3[250/1200], Temp: 0.8967, Energy: -54.584670+0.000370j
[2025-07-30 08:20:13] [Iter 1302/4650] R3[251/1200], Temp: 0.8959, Energy: -54.579764-0.001930j
[2025-07-30 08:20:23] [Iter 1303/4650] R3[252/1200], Temp: 0.8951, Energy: -54.604524-0.000356j
[2025-07-30 08:20:33] [Iter 1304/4650] R3[253/1200], Temp: 0.8943, Energy: -54.694418-0.000616j
[2025-07-30 08:20:43] [Iter 1305/4650] R3[254/1200], Temp: 0.8935, Energy: -54.684059-0.001024j
[2025-07-30 08:20:53] [Iter 1306/4650] R3[255/1200], Temp: 0.8927, Energy: -54.661086+0.000464j
[2025-07-30 08:21:04] [Iter 1307/4650] R3[256/1200], Temp: 0.8918, Energy: -54.657638-0.000870j
[2025-07-30 08:21:14] [Iter 1308/4650] R3[257/1200], Temp: 0.8910, Energy: -54.622652-0.002104j
[2025-07-30 08:21:24] [Iter 1309/4650] R3[258/1200], Temp: 0.8902, Energy: -54.591969-0.003915j
[2025-07-30 08:21:34] [Iter 1310/4650] R3[259/1200], Temp: 0.8894, Energy: -54.566232+0.004213j
[2025-07-30 08:21:44] [Iter 1311/4650] R3[260/1200], Temp: 0.8886, Energy: -54.572959+0.004306j
[2025-07-30 08:21:54] [Iter 1312/4650] R3[261/1200], Temp: 0.8877, Energy: -54.606774-0.001017j
[2025-07-30 08:22:04] [Iter 1313/4650] R3[262/1200], Temp: 0.8869, Energy: -54.628292-0.000627j
[2025-07-30 08:22:15] [Iter 1314/4650] R3[263/1200], Temp: 0.8861, Energy: -54.635873-0.000117j
[2025-07-30 08:22:25] [Iter 1315/4650] R3[264/1200], Temp: 0.8853, Energy: -54.667920-0.000822j
[2025-07-30 08:22:35] [Iter 1316/4650] R3[265/1200], Temp: 0.8844, Energy: -54.666335+0.000591j
[2025-07-30 08:22:45] [Iter 1317/4650] R3[266/1200], Temp: 0.8836, Energy: -54.643630-0.001074j
[2025-07-30 08:22:55] [Iter 1318/4650] R3[267/1200], Temp: 0.8827, Energy: -54.635349+0.000438j
[2025-07-30 08:23:05] [Iter 1319/4650] R3[268/1200], Temp: 0.8819, Energy: -54.641714+0.000125j
[2025-07-30 08:23:15] [Iter 1320/4650] R3[269/1200], Temp: 0.8811, Energy: -54.663348-0.000414j
[2025-07-30 08:23:25] [Iter 1321/4650] R3[270/1200], Temp: 0.8802, Energy: -54.619495-0.000353j
[2025-07-30 08:23:36] [Iter 1322/4650] R3[271/1200], Temp: 0.8794, Energy: -54.630466+0.000962j
[2025-07-30 08:23:46] [Iter 1323/4650] R3[272/1200], Temp: 0.8785, Energy: -54.603166+0.001111j
[2025-07-30 08:23:56] [Iter 1324/4650] R3[273/1200], Temp: 0.8776, Energy: -54.570185-0.000588j
[2025-07-30 08:24:06] [Iter 1325/4650] R3[274/1200], Temp: 0.8768, Energy: -54.614647-0.001934j
[2025-07-30 08:24:16] [Iter 1326/4650] R3[275/1200], Temp: 0.8759, Energy: -54.608304-0.000556j
[2025-07-30 08:24:26] [Iter 1327/4650] R3[276/1200], Temp: 0.8751, Energy: -54.620351-0.000927j
[2025-07-30 08:24:36] [Iter 1328/4650] R3[277/1200], Temp: 0.8742, Energy: -54.680763-0.002138j
[2025-07-30 08:24:46] [Iter 1329/4650] R3[278/1200], Temp: 0.8733, Energy: -54.637426-0.000975j
[2025-07-30 08:24:57] [Iter 1330/4650] R3[279/1200], Temp: 0.8724, Energy: -54.626530-0.000056j
[2025-07-30 08:25:07] [Iter 1331/4650] R3[280/1200], Temp: 0.8716, Energy: -54.667819+0.001459j
[2025-07-30 08:25:17] [Iter 1332/4650] R3[281/1200], Temp: 0.8707, Energy: -54.635967+0.000791j
[2025-07-30 08:25:27] [Iter 1333/4650] R3[282/1200], Temp: 0.8698, Energy: -54.659288+0.003059j
[2025-07-30 08:25:37] [Iter 1334/4650] R3[283/1200], Temp: 0.8689, Energy: -54.691198+0.001426j
[2025-07-30 08:25:47] [Iter 1335/4650] R3[284/1200], Temp: 0.8680, Energy: -54.703085+0.000210j
[2025-07-30 08:25:57] [Iter 1336/4650] R3[285/1200], Temp: 0.8672, Energy: -54.674569-0.003856j
[2025-07-30 08:26:07] [Iter 1337/4650] R3[286/1200], Temp: 0.8663, Energy: -54.687664+0.001683j
[2025-07-30 08:26:17] [Iter 1338/4650] R3[287/1200], Temp: 0.8654, Energy: -54.651652+0.000576j
[2025-07-30 08:26:28] [Iter 1339/4650] R3[288/1200], Temp: 0.8645, Energy: -54.549204+0.000538j
[2025-07-30 08:26:38] [Iter 1340/4650] R3[289/1200], Temp: 0.8636, Energy: -54.595177-0.002961j
[2025-07-30 08:26:48] [Iter 1341/4650] R3[290/1200], Temp: 0.8627, Energy: -54.612734+0.001658j
[2025-07-30 08:26:58] [Iter 1342/4650] R3[291/1200], Temp: 0.8618, Energy: -54.584775+0.001754j
[2025-07-30 08:27:08] [Iter 1343/4650] R3[292/1200], Temp: 0.8609, Energy: -54.569115-0.000439j
[2025-07-30 08:27:18] [Iter 1344/4650] R3[293/1200], Temp: 0.8600, Energy: -54.648937+0.001203j
[2025-07-30 08:27:28] [Iter 1345/4650] R3[294/1200], Temp: 0.8591, Energy: -54.621245+0.001878j
[2025-07-30 08:27:38] [Iter 1346/4650] R3[295/1200], Temp: 0.8582, Energy: -54.586790-0.003565j
[2025-07-30 08:27:49] [Iter 1347/4650] R3[296/1200], Temp: 0.8572, Energy: -54.626213+0.000176j
[2025-07-30 08:27:59] [Iter 1348/4650] R3[297/1200], Temp: 0.8563, Energy: -54.639636+0.001223j
[2025-07-30 08:28:09] [Iter 1349/4650] R3[298/1200], Temp: 0.8554, Energy: -54.606979-0.001286j
[2025-07-30 08:28:19] [Iter 1350/4650] R3[299/1200], Temp: 0.8545, Energy: -54.613567+0.001893j
[2025-07-30 08:28:29] [Iter 1351/4650] R3[300/1200], Temp: 0.8536, Energy: -54.614469+0.005366j
[2025-07-30 08:28:39] [Iter 1352/4650] R3[301/1200], Temp: 0.8526, Energy: -54.609287+0.001718j
[2025-07-30 08:28:49] [Iter 1353/4650] R3[302/1200], Temp: 0.8517, Energy: -54.576322-0.000114j
[2025-07-30 08:28:59] [Iter 1354/4650] R3[303/1200], Temp: 0.8508, Energy: -54.591859+0.000809j
[2025-07-30 08:29:10] [Iter 1355/4650] R3[304/1200], Temp: 0.8498, Energy: -54.593723+0.000293j
[2025-07-30 08:29:20] [Iter 1356/4650] R3[305/1200], Temp: 0.8489, Energy: -54.619475-0.000311j
[2025-07-30 08:29:30] [Iter 1357/4650] R3[306/1200], Temp: 0.8480, Energy: -54.599036-0.002913j
[2025-07-30 08:29:40] [Iter 1358/4650] R3[307/1200], Temp: 0.8470, Energy: -54.593923+0.007043j
[2025-07-30 08:29:50] [Iter 1359/4650] R3[308/1200], Temp: 0.8461, Energy: -54.574438+0.002506j
[2025-07-30 08:30:00] [Iter 1360/4650] R3[309/1200], Temp: 0.8451, Energy: -54.597029+0.002470j
[2025-07-30 08:30:10] [Iter 1361/4650] R3[310/1200], Temp: 0.8442, Energy: -54.632046+0.002321j
[2025-07-30 08:30:20] [Iter 1362/4650] R3[311/1200], Temp: 0.8432, Energy: -54.597813+0.003650j
[2025-07-30 08:30:30] [Iter 1363/4650] R3[312/1200], Temp: 0.8423, Energy: -54.667298+0.002094j
[2025-07-30 08:30:41] [Iter 1364/4650] R3[313/1200], Temp: 0.8413, Energy: -54.607408+0.002879j
[2025-07-30 08:30:51] [Iter 1365/4650] R3[314/1200], Temp: 0.8404, Energy: -54.592568-0.003215j
[2025-07-30 08:31:01] [Iter 1366/4650] R3[315/1200], Temp: 0.8394, Energy: -54.583287-0.000673j
[2025-07-30 08:31:11] [Iter 1367/4650] R3[316/1200], Temp: 0.8384, Energy: -54.572989+0.001199j
[2025-07-30 08:31:21] [Iter 1368/4650] R3[317/1200], Temp: 0.8375, Energy: -54.610535-0.000937j
[2025-07-30 08:31:31] [Iter 1369/4650] R3[318/1200], Temp: 0.8365, Energy: -54.614567-0.001301j
[2025-07-30 08:31:41] [Iter 1370/4650] R3[319/1200], Temp: 0.8355, Energy: -54.634485+0.000714j
[2025-07-30 08:31:51] [Iter 1371/4650] R3[320/1200], Temp: 0.8346, Energy: -54.678917+0.001633j
[2025-07-30 08:32:02] [Iter 1372/4650] R3[321/1200], Temp: 0.8336, Energy: -54.702043+0.001976j
[2025-07-30 08:32:12] [Iter 1373/4650] R3[322/1200], Temp: 0.8326, Energy: -54.688084+0.000766j
[2025-07-30 08:32:22] [Iter 1374/4650] R3[323/1200], Temp: 0.8316, Energy: -54.712558-0.000897j
[2025-07-30 08:32:32] [Iter 1375/4650] R3[324/1200], Temp: 0.8307, Energy: -54.673241+0.000635j
[2025-07-30 08:32:42] [Iter 1376/4650] R3[325/1200], Temp: 0.8297, Energy: -54.632504-0.002613j
[2025-07-30 08:32:52] [Iter 1377/4650] R3[326/1200], Temp: 0.8287, Energy: -54.590882-0.002533j
[2025-07-30 08:33:02] [Iter 1378/4650] R3[327/1200], Temp: 0.8277, Energy: -54.617719-0.000545j
[2025-07-30 08:33:12] [Iter 1379/4650] R3[328/1200], Temp: 0.8267, Energy: -54.610729-0.003639j
[2025-07-30 08:33:22] [Iter 1380/4650] R3[329/1200], Temp: 0.8257, Energy: -54.653106-0.000065j
[2025-07-30 08:33:33] [Iter 1381/4650] R3[330/1200], Temp: 0.8247, Energy: -54.603156-0.002171j
[2025-07-30 08:33:43] [Iter 1382/4650] R3[331/1200], Temp: 0.8237, Energy: -54.612447-0.000056j
[2025-07-30 08:33:53] [Iter 1383/4650] R3[332/1200], Temp: 0.8227, Energy: -54.654290-0.002605j
[2025-07-30 08:34:03] [Iter 1384/4650] R3[333/1200], Temp: 0.8217, Energy: -54.652945-0.002565j
[2025-07-30 08:34:13] [Iter 1385/4650] R3[334/1200], Temp: 0.8207, Energy: -54.588136-0.000381j
[2025-07-30 08:34:23] [Iter 1386/4650] R3[335/1200], Temp: 0.8197, Energy: -54.621339+0.002228j
[2025-07-30 08:34:33] [Iter 1387/4650] R3[336/1200], Temp: 0.8187, Energy: -54.629856+0.003193j
[2025-07-30 08:34:43] [Iter 1388/4650] R3[337/1200], Temp: 0.8177, Energy: -54.638291+0.000041j
[2025-07-30 08:34:54] [Iter 1389/4650] R3[338/1200], Temp: 0.8167, Energy: -54.574855-0.001101j
[2025-07-30 08:35:04] [Iter 1390/4650] R3[339/1200], Temp: 0.8157, Energy: -54.615258+0.000317j
[2025-07-30 08:35:14] [Iter 1391/4650] R3[340/1200], Temp: 0.8147, Energy: -54.545719-0.000506j
[2025-07-30 08:35:24] [Iter 1392/4650] R3[341/1200], Temp: 0.8136, Energy: -54.591769+0.000439j
[2025-07-30 08:35:34] [Iter 1393/4650] R3[342/1200], Temp: 0.8126, Energy: -54.589337+0.002929j
[2025-07-30 08:35:44] [Iter 1394/4650] R3[343/1200], Temp: 0.8116, Energy: -54.535900+0.000287j
[2025-07-30 08:35:54] [Iter 1395/4650] R3[344/1200], Temp: 0.8106, Energy: -54.576814+0.000834j
[2025-07-30 08:36:05] [Iter 1396/4650] R3[345/1200], Temp: 0.8095, Energy: -54.631338+0.003627j
[2025-07-30 08:36:15] [Iter 1397/4650] R3[346/1200], Temp: 0.8085, Energy: -54.562336+0.003502j
[2025-07-30 08:36:25] [Iter 1398/4650] R3[347/1200], Temp: 0.8075, Energy: -54.649249+0.005098j
[2025-07-30 08:36:35] [Iter 1399/4650] R3[348/1200], Temp: 0.8065, Energy: -54.618328-0.000255j
[2025-07-30 08:36:45] [Iter 1400/4650] R3[349/1200], Temp: 0.8054, Energy: -54.603549-0.001021j
[2025-07-30 08:36:55] [Iter 1401/4650] R3[350/1200], Temp: 0.8044, Energy: -54.598290+0.001116j
[2025-07-30 08:37:05] [Iter 1402/4650] R3[351/1200], Temp: 0.8033, Energy: -54.555372+0.000831j
[2025-07-30 08:37:15] [Iter 1403/4650] R3[352/1200], Temp: 0.8023, Energy: -54.563862+0.000276j
[2025-07-30 08:37:26] [Iter 1404/4650] R3[353/1200], Temp: 0.8013, Energy: -54.557483+0.001316j
[2025-07-30 08:37:36] [Iter 1405/4650] R3[354/1200], Temp: 0.8002, Energy: -54.593788+0.000540j
[2025-07-30 08:37:46] [Iter 1406/4650] R3[355/1200], Temp: 0.7992, Energy: -54.580391-0.001120j
[2025-07-30 08:37:56] [Iter 1407/4650] R3[356/1200], Temp: 0.7981, Energy: -54.601372-0.001058j
[2025-07-30 08:38:06] [Iter 1408/4650] R3[357/1200], Temp: 0.7971, Energy: -54.634433+0.000800j
[2025-07-30 08:38:16] [Iter 1409/4650] R3[358/1200], Temp: 0.7960, Energy: -54.596006+0.001236j
[2025-07-30 08:38:26] [Iter 1410/4650] R3[359/1200], Temp: 0.7950, Energy: -54.572104-0.004200j
[2025-07-30 08:38:36] [Iter 1411/4650] R3[360/1200], Temp: 0.7939, Energy: -54.568947+0.000388j
[2025-07-30 08:38:46] [Iter 1412/4650] R3[361/1200], Temp: 0.7928, Energy: -54.588750+0.000028j
[2025-07-30 08:38:57] [Iter 1413/4650] R3[362/1200], Temp: 0.7918, Energy: -54.633132-0.000984j
[2025-07-30 08:39:07] [Iter 1414/4650] R3[363/1200], Temp: 0.7907, Energy: -54.605864-0.001927j
[2025-07-30 08:39:17] [Iter 1415/4650] R3[364/1200], Temp: 0.7896, Energy: -54.620556+0.000772j
[2025-07-30 08:39:27] [Iter 1416/4650] R3[365/1200], Temp: 0.7886, Energy: -54.616213+0.000338j
[2025-07-30 08:39:37] [Iter 1417/4650] R3[366/1200], Temp: 0.7875, Energy: -54.583949-0.003104j
[2025-07-30 08:39:47] [Iter 1418/4650] R3[367/1200], Temp: 0.7864, Energy: -54.609616+0.000748j
[2025-07-30 08:39:57] [Iter 1419/4650] R3[368/1200], Temp: 0.7854, Energy: -54.679898+0.000277j
[2025-07-30 08:40:07] [Iter 1420/4650] R3[369/1200], Temp: 0.7843, Energy: -54.572354+0.001026j
[2025-07-30 08:40:18] [Iter 1421/4650] R3[370/1200], Temp: 0.7832, Energy: -54.615627+0.001981j
[2025-07-30 08:40:28] [Iter 1422/4650] R3[371/1200], Temp: 0.7821, Energy: -54.563199+0.001143j
[2025-07-30 08:40:38] [Iter 1423/4650] R3[372/1200], Temp: 0.7810, Energy: -54.598171+0.001389j
[2025-07-30 08:40:48] [Iter 1424/4650] R3[373/1200], Temp: 0.7800, Energy: -54.606950-0.003116j
[2025-07-30 08:40:58] [Iter 1425/4650] R3[374/1200], Temp: 0.7789, Energy: -54.588906-0.000029j
[2025-07-30 08:41:08] [Iter 1426/4650] R3[375/1200], Temp: 0.7778, Energy: -54.632101-0.002349j
[2025-07-30 08:41:18] [Iter 1427/4650] R3[376/1200], Temp: 0.7767, Energy: -54.609963-0.001707j
[2025-07-30 08:41:28] [Iter 1428/4650] R3[377/1200], Temp: 0.7756, Energy: -54.574017-0.001309j
[2025-07-30 08:41:38] [Iter 1429/4650] R3[378/1200], Temp: 0.7745, Energy: -54.552785-0.001100j
[2025-07-30 08:41:49] [Iter 1430/4650] R3[379/1200], Temp: 0.7734, Energy: -54.562276-0.001026j
[2025-07-30 08:41:59] [Iter 1431/4650] R3[380/1200], Temp: 0.7723, Energy: -54.568974-0.001448j
[2025-07-30 08:42:09] [Iter 1432/4650] R3[381/1200], Temp: 0.7712, Energy: -54.582418+0.001070j
[2025-07-30 08:42:19] [Iter 1433/4650] R3[382/1200], Temp: 0.7701, Energy: -54.588244+0.000655j
[2025-07-30 08:42:29] [Iter 1434/4650] R3[383/1200], Temp: 0.7690, Energy: -54.620069+0.000448j
[2025-07-30 08:42:39] [Iter 1435/4650] R3[384/1200], Temp: 0.7679, Energy: -54.586630-0.001004j
[2025-07-30 08:42:49] [Iter 1436/4650] R3[385/1200], Temp: 0.7668, Energy: -54.583259-0.003814j
[2025-07-30 08:42:59] [Iter 1437/4650] R3[386/1200], Temp: 0.7657, Energy: -54.585723+0.001223j
[2025-07-30 08:43:10] [Iter 1438/4650] R3[387/1200], Temp: 0.7646, Energy: -54.649231+0.001336j
[2025-07-30 08:43:20] [Iter 1439/4650] R3[388/1200], Temp: 0.7635, Energy: -54.537203+0.002542j
[2025-07-30 08:43:30] [Iter 1440/4650] R3[389/1200], Temp: 0.7624, Energy: -54.655302-0.002290j
[2025-07-30 08:43:40] [Iter 1441/4650] R3[390/1200], Temp: 0.7612, Energy: -54.656398-0.001919j
[2025-07-30 08:43:50] [Iter 1442/4650] R3[391/1200], Temp: 0.7601, Energy: -54.631440+0.002904j
[2025-07-30 08:44:00] [Iter 1443/4650] R3[392/1200], Temp: 0.7590, Energy: -54.598934-0.001563j
[2025-07-30 08:44:10] [Iter 1444/4650] R3[393/1200], Temp: 0.7579, Energy: -54.548294+0.000171j
[2025-07-30 08:44:20] [Iter 1445/4650] R3[394/1200], Temp: 0.7568, Energy: -54.550320+0.000455j
[2025-07-30 08:44:30] [Iter 1446/4650] R3[395/1200], Temp: 0.7556, Energy: -54.552658-0.001602j
[2025-07-30 08:44:41] [Iter 1447/4650] R3[396/1200], Temp: 0.7545, Energy: -54.564841+0.003360j
[2025-07-30 08:44:51] [Iter 1448/4650] R3[397/1200], Temp: 0.7534, Energy: -54.580666+0.000278j
[2025-07-30 08:45:01] [Iter 1449/4650] R3[398/1200], Temp: 0.7523, Energy: -54.581697+0.001455j
[2025-07-30 08:45:11] [Iter 1450/4650] R3[399/1200], Temp: 0.7511, Energy: -54.579641-0.000239j
[2025-07-30 08:45:21] [Iter 1451/4650] R3[400/1200], Temp: 0.7500, Energy: -54.552582+0.003285j
[2025-07-30 08:45:31] [Iter 1452/4650] R3[401/1200], Temp: 0.7489, Energy: -54.595379+0.000227j
[2025-07-30 08:45:41] [Iter 1453/4650] R3[402/1200], Temp: 0.7477, Energy: -54.573035+0.001141j
[2025-07-30 08:45:51] [Iter 1454/4650] R3[403/1200], Temp: 0.7466, Energy: -54.574346+0.000461j
[2025-07-30 08:46:01] [Iter 1455/4650] R3[404/1200], Temp: 0.7455, Energy: -54.564362+0.001397j
[2025-07-30 08:46:12] [Iter 1456/4650] R3[405/1200], Temp: 0.7443, Energy: -54.564503-0.002007j
[2025-07-30 08:46:22] [Iter 1457/4650] R3[406/1200], Temp: 0.7432, Energy: -54.571951-0.002596j
[2025-07-30 08:46:32] [Iter 1458/4650] R3[407/1200], Temp: 0.7420, Energy: -54.572254-0.000979j
[2025-07-30 08:46:42] [Iter 1459/4650] R3[408/1200], Temp: 0.7409, Energy: -54.571359+0.003046j
[2025-07-30 08:46:52] [Iter 1460/4650] R3[409/1200], Temp: 0.7397, Energy: -54.605509+0.000486j
[2025-07-30 08:47:02] [Iter 1461/4650] R3[410/1200], Temp: 0.7386, Energy: -54.573626+0.003203j
[2025-07-30 08:47:12] [Iter 1462/4650] R3[411/1200], Temp: 0.7374, Energy: -54.554109+0.001489j
[2025-07-30 08:47:22] [Iter 1463/4650] R3[412/1200], Temp: 0.7363, Energy: -54.584661-0.000562j
[2025-07-30 08:47:32] [Iter 1464/4650] R3[413/1200], Temp: 0.7351, Energy: -54.602245-0.002476j
[2025-07-30 08:47:43] [Iter 1465/4650] R3[414/1200], Temp: 0.7340, Energy: -54.675130+0.004383j
[2025-07-30 08:47:53] [Iter 1466/4650] R3[415/1200], Temp: 0.7328, Energy: -54.607167+0.000836j
[2025-07-30 08:48:03] [Iter 1467/4650] R3[416/1200], Temp: 0.7316, Energy: -54.617089-0.001221j
[2025-07-30 08:48:13] [Iter 1468/4650] R3[417/1200], Temp: 0.7305, Energy: -54.656154-0.000705j
[2025-07-30 08:48:23] [Iter 1469/4650] R3[418/1200], Temp: 0.7293, Energy: -54.643497+0.001009j
[2025-07-30 08:48:33] [Iter 1470/4650] R3[419/1200], Temp: 0.7282, Energy: -54.689585+0.000251j
[2025-07-30 08:48:43] [Iter 1471/4650] R3[420/1200], Temp: 0.7270, Energy: -54.658379+0.000255j
[2025-07-30 08:48:53] [Iter 1472/4650] R3[421/1200], Temp: 0.7258, Energy: -54.648481+0.001758j
[2025-07-30 08:49:04] [Iter 1473/4650] R3[422/1200], Temp: 0.7247, Energy: -54.620348-0.003576j
[2025-07-30 08:49:14] [Iter 1474/4650] R3[423/1200], Temp: 0.7235, Energy: -54.626075+0.002904j
[2025-07-30 08:49:24] [Iter 1475/4650] R3[424/1200], Temp: 0.7223, Energy: -54.581876-0.000551j
[2025-07-30 08:49:34] [Iter 1476/4650] R3[425/1200], Temp: 0.7211, Energy: -54.592801+0.000990j
[2025-07-30 08:49:44] [Iter 1477/4650] R3[426/1200], Temp: 0.7200, Energy: -54.605344-0.000456j
[2025-07-30 08:49:54] [Iter 1478/4650] R3[427/1200], Temp: 0.7188, Energy: -54.611387+0.000855j
[2025-07-30 08:50:04] [Iter 1479/4650] R3[428/1200], Temp: 0.7176, Energy: -54.550687-0.003408j
[2025-07-30 08:50:14] [Iter 1480/4650] R3[429/1200], Temp: 0.7164, Energy: -54.532430-0.001832j
[2025-07-30 08:50:24] [Iter 1481/4650] R3[430/1200], Temp: 0.7153, Energy: -54.484299-0.000601j
[2025-07-30 08:50:35] [Iter 1482/4650] R3[431/1200], Temp: 0.7141, Energy: -54.542805-0.003502j
[2025-07-30 08:50:45] [Iter 1483/4650] R3[432/1200], Temp: 0.7129, Energy: -54.553744+0.001834j
[2025-07-30 08:50:55] [Iter 1484/4650] R3[433/1200], Temp: 0.7117, Energy: -54.540029+0.002812j
[2025-07-30 08:51:05] [Iter 1485/4650] R3[434/1200], Temp: 0.7105, Energy: -54.555518-0.001784j
[2025-07-30 08:51:15] [Iter 1486/4650] R3[435/1200], Temp: 0.7093, Energy: -54.562059-0.001242j
[2025-07-30 08:51:25] [Iter 1487/4650] R3[436/1200], Temp: 0.7081, Energy: -54.557869+0.000326j
[2025-07-30 08:51:35] [Iter 1488/4650] R3[437/1200], Temp: 0.7069, Energy: -54.520524-0.000943j
[2025-07-30 08:51:45] [Iter 1489/4650] R3[438/1200], Temp: 0.7058, Energy: -54.486714+0.001895j
[2025-07-30 08:51:56] [Iter 1490/4650] R3[439/1200], Temp: 0.7046, Energy: -54.509174-0.005218j
[2025-07-30 08:52:06] [Iter 1491/4650] R3[440/1200], Temp: 0.7034, Energy: -54.468654+0.003334j
[2025-07-30 08:52:16] [Iter 1492/4650] R3[441/1200], Temp: 0.7022, Energy: -54.512112-0.001684j
[2025-07-30 08:52:26] [Iter 1493/4650] R3[442/1200], Temp: 0.7010, Energy: -54.559788-0.000949j
[2025-07-30 08:52:36] [Iter 1494/4650] R3[443/1200], Temp: 0.6998, Energy: -54.497084-0.003350j
[2025-07-30 08:52:46] [Iter 1495/4650] R3[444/1200], Temp: 0.6986, Energy: -54.579730-0.000908j
[2025-07-30 08:52:56] [Iter 1496/4650] R3[445/1200], Temp: 0.6974, Energy: -54.587743-0.004449j
[2025-07-30 08:53:06] [Iter 1497/4650] R3[446/1200], Temp: 0.6962, Energy: -54.563006-0.001771j
[2025-07-30 08:53:17] [Iter 1498/4650] R3[447/1200], Temp: 0.6950, Energy: -54.563607-0.000707j
[2025-07-30 08:53:27] [Iter 1499/4650] R3[448/1200], Temp: 0.6938, Energy: -54.589221-0.001787j
[2025-07-30 08:53:37] [Iter 1500/4650] R3[449/1200], Temp: 0.6926, Energy: -54.580775+0.000423j
[2025-07-30 08:53:37] ✓ Checkpoint saved: checkpoint_iter_001500.pkl
[2025-07-30 08:53:47] [Iter 1501/4650] R3[450/1200], Temp: 0.6913, Energy: -54.657799+0.004755j
[2025-07-30 08:53:57] [Iter 1502/4650] R3[451/1200], Temp: 0.6901, Energy: -54.649046-0.002792j
[2025-07-30 08:54:07] [Iter 1503/4650] R3[452/1200], Temp: 0.6889, Energy: -54.653578+0.002636j
[2025-07-30 08:54:17] [Iter 1504/4650] R3[453/1200], Temp: 0.6877, Energy: -54.621153+0.001390j
[2025-07-30 08:54:27] [Iter 1505/4650] R3[454/1200], Temp: 0.6865, Energy: -54.658540+0.001423j
[2025-07-30 08:54:37] [Iter 1506/4650] R3[455/1200], Temp: 0.6853, Energy: -54.606781-0.000606j
[2025-07-30 08:54:48] [Iter 1507/4650] R3[456/1200], Temp: 0.6841, Energy: -54.575585-0.001399j
[2025-07-30 08:54:58] [Iter 1508/4650] R3[457/1200], Temp: 0.6828, Energy: -54.615626+0.000408j
[2025-07-30 08:55:08] [Iter 1509/4650] R3[458/1200], Temp: 0.6816, Energy: -54.624281-0.001576j
[2025-07-30 08:55:18] [Iter 1510/4650] R3[459/1200], Temp: 0.6804, Energy: -54.602544+0.000661j
[2025-07-30 08:55:28] [Iter 1511/4650] R3[460/1200], Temp: 0.6792, Energy: -54.625482+0.001447j
[2025-07-30 08:55:38] [Iter 1512/4650] R3[461/1200], Temp: 0.6780, Energy: -54.588475+0.002184j
[2025-07-30 08:55:48] [Iter 1513/4650] R3[462/1200], Temp: 0.6767, Energy: -54.613708+0.003449j
[2025-07-30 08:55:58] [Iter 1514/4650] R3[463/1200], Temp: 0.6755, Energy: -54.584469-0.000770j
[2025-07-30 08:56:08] [Iter 1515/4650] R3[464/1200], Temp: 0.6743, Energy: -54.585433-0.001250j
[2025-07-30 08:56:19] [Iter 1516/4650] R3[465/1200], Temp: 0.6731, Energy: -54.560002+0.003377j
[2025-07-30 08:56:29] [Iter 1517/4650] R3[466/1200], Temp: 0.6718, Energy: -54.515256-0.001769j
[2025-07-30 08:56:39] [Iter 1518/4650] R3[467/1200], Temp: 0.6706, Energy: -54.500144+0.002203j
[2025-07-30 08:56:49] [Iter 1519/4650] R3[468/1200], Temp: 0.6694, Energy: -54.437557-0.001551j
[2025-07-30 08:56:59] [Iter 1520/4650] R3[469/1200], Temp: 0.6681, Energy: -54.494866+0.001583j
[2025-07-30 08:57:09] [Iter 1521/4650] R3[470/1200], Temp: 0.6669, Energy: -54.527581+0.000685j
[2025-07-30 08:57:19] [Iter 1522/4650] R3[471/1200], Temp: 0.6657, Energy: -54.477396-0.001580j
[2025-07-30 08:57:29] [Iter 1523/4650] R3[472/1200], Temp: 0.6644, Energy: -54.480598+0.000553j
[2025-07-30 08:57:40] [Iter 1524/4650] R3[473/1200], Temp: 0.6632, Energy: -54.529095+0.001326j
[2025-07-30 08:57:50] [Iter 1525/4650] R3[474/1200], Temp: 0.6620, Energy: -54.538799-0.000223j
[2025-07-30 08:58:00] [Iter 1526/4650] R3[475/1200], Temp: 0.6607, Energy: -54.553197-0.000257j
[2025-07-30 08:58:10] [Iter 1527/4650] R3[476/1200], Temp: 0.6595, Energy: -54.560412+0.000312j
[2025-07-30 08:58:20] [Iter 1528/4650] R3[477/1200], Temp: 0.6582, Energy: -54.571369+0.003332j
[2025-07-30 08:58:30] [Iter 1529/4650] R3[478/1200], Temp: 0.6570, Energy: -54.533904+0.000851j
[2025-07-30 08:58:40] [Iter 1530/4650] R3[479/1200], Temp: 0.6558, Energy: -54.509385+0.000426j
[2025-07-30 08:58:50] [Iter 1531/4650] R3[480/1200], Temp: 0.6545, Energy: -54.557596+0.002334j
[2025-07-30 08:59:01] [Iter 1532/4650] R3[481/1200], Temp: 0.6533, Energy: -54.558526+0.000683j
[2025-07-30 08:59:11] [Iter 1533/4650] R3[482/1200], Temp: 0.6520, Energy: -54.533440+0.001804j
[2025-07-30 08:59:21] [Iter 1534/4650] R3[483/1200], Temp: 0.6508, Energy: -54.490545+0.002233j
[2025-07-30 08:59:31] [Iter 1535/4650] R3[484/1200], Temp: 0.6495, Energy: -54.554821+0.003226j
[2025-07-30 08:59:41] [Iter 1536/4650] R3[485/1200], Temp: 0.6483, Energy: -54.522202+0.002344j
[2025-07-30 08:59:51] [Iter 1537/4650] R3[486/1200], Temp: 0.6470, Energy: -54.536970+0.001118j
[2025-07-30 09:00:01] [Iter 1538/4650] R3[487/1200], Temp: 0.6458, Energy: -54.571379-0.000378j
[2025-07-30 09:00:11] [Iter 1539/4650] R3[488/1200], Temp: 0.6445, Energy: -54.552741+0.001062j
[2025-07-30 09:00:21] [Iter 1540/4650] R3[489/1200], Temp: 0.6433, Energy: -54.587558-0.002011j
[2025-07-30 09:00:32] [Iter 1541/4650] R3[490/1200], Temp: 0.6420, Energy: -54.611724-0.000326j
[2025-07-30 09:00:42] [Iter 1542/4650] R3[491/1200], Temp: 0.6408, Energy: -54.664918+0.002409j
[2025-07-30 09:00:52] [Iter 1543/4650] R3[492/1200], Temp: 0.6395, Energy: -54.624323-0.000136j
[2025-07-30 09:01:02] [Iter 1544/4650] R3[493/1200], Temp: 0.6382, Energy: -54.633766+0.000875j
[2025-07-30 09:01:12] [Iter 1545/4650] R3[494/1200], Temp: 0.6370, Energy: -54.598052-0.002972j
[2025-07-30 09:01:22] [Iter 1546/4650] R3[495/1200], Temp: 0.6357, Energy: -54.633688+0.001474j
[2025-07-30 09:01:32] [Iter 1547/4650] R3[496/1200], Temp: 0.6345, Energy: -54.617971+0.000499j
[2025-07-30 09:01:42] [Iter 1548/4650] R3[497/1200], Temp: 0.6332, Energy: -54.606020+0.001812j
[2025-07-30 09:01:52] [Iter 1549/4650] R3[498/1200], Temp: 0.6319, Energy: -54.594471-0.003059j
[2025-07-30 09:02:03] [Iter 1550/4650] R3[499/1200], Temp: 0.6307, Energy: -54.580634-0.000246j
[2025-07-30 09:02:13] [Iter 1551/4650] R3[500/1200], Temp: 0.6294, Energy: -54.579480-0.000686j
[2025-07-30 09:02:23] [Iter 1552/4650] R3[501/1200], Temp: 0.6281, Energy: -54.589985+0.001622j
[2025-07-30 09:02:33] [Iter 1553/4650] R3[502/1200], Temp: 0.6269, Energy: -54.556232+0.001057j
[2025-07-30 09:02:43] [Iter 1554/4650] R3[503/1200], Temp: 0.6256, Energy: -54.569995+0.000054j
[2025-07-30 09:02:53] [Iter 1555/4650] R3[504/1200], Temp: 0.6243, Energy: -54.610524+0.000145j
[2025-07-30 09:03:03] [Iter 1556/4650] R3[505/1200], Temp: 0.6231, Energy: -54.593536-0.000543j
[2025-07-30 09:03:13] [Iter 1557/4650] R3[506/1200], Temp: 0.6218, Energy: -54.593017+0.001463j
[2025-07-30 09:03:23] [Iter 1558/4650] R3[507/1200], Temp: 0.6205, Energy: -54.644802-0.003056j
[2025-07-30 09:03:34] [Iter 1559/4650] R3[508/1200], Temp: 0.6193, Energy: -54.659250-0.000615j
[2025-07-30 09:03:44] [Iter 1560/4650] R3[509/1200], Temp: 0.6180, Energy: -54.621949-0.000378j
[2025-07-30 09:03:54] [Iter 1561/4650] R3[510/1200], Temp: 0.6167, Energy: -54.624765-0.004007j
[2025-07-30 09:04:04] [Iter 1562/4650] R3[511/1200], Temp: 0.6154, Energy: -54.550504-0.000054j
[2025-07-30 09:04:14] [Iter 1563/4650] R3[512/1200], Temp: 0.6142, Energy: -54.636246+0.000995j
[2025-07-30 09:04:24] [Iter 1564/4650] R3[513/1200], Temp: 0.6129, Energy: -54.641776+0.000542j
[2025-07-30 09:04:34] [Iter 1565/4650] R3[514/1200], Temp: 0.6116, Energy: -54.602076+0.000005j
[2025-07-30 09:04:45] [Iter 1566/4650] R3[515/1200], Temp: 0.6103, Energy: -54.595836-0.000079j
[2025-07-30 09:04:55] [Iter 1567/4650] R3[516/1200], Temp: 0.6091, Energy: -54.593614-0.000969j
[2025-07-30 09:05:05] [Iter 1568/4650] R3[517/1200], Temp: 0.6078, Energy: -54.572481+0.002015j
[2025-07-30 09:05:15] [Iter 1569/4650] R3[518/1200], Temp: 0.6065, Energy: -54.602086-0.001504j
[2025-07-30 09:05:25] [Iter 1570/4650] R3[519/1200], Temp: 0.6052, Energy: -54.609645+0.000666j
[2025-07-30 09:05:35] [Iter 1571/4650] R3[520/1200], Temp: 0.6040, Energy: -54.624731-0.000038j
[2025-07-30 09:05:45] [Iter 1572/4650] R3[521/1200], Temp: 0.6027, Energy: -54.579662+0.001934j
[2025-07-30 09:05:55] [Iter 1573/4650] R3[522/1200], Temp: 0.6014, Energy: -54.590850+0.001360j
[2025-07-30 09:06:05] [Iter 1574/4650] R3[523/1200], Temp: 0.6001, Energy: -54.569576+0.000408j
[2025-07-30 09:06:15] [Iter 1575/4650] R3[524/1200], Temp: 0.5988, Energy: -54.571176+0.000483j
[2025-07-30 09:06:26] [Iter 1576/4650] R3[525/1200], Temp: 0.5975, Energy: -54.597853+0.000951j
[2025-07-30 09:06:36] [Iter 1577/4650] R3[526/1200], Temp: 0.5963, Energy: -54.621404-0.001400j
[2025-07-30 09:06:46] [Iter 1578/4650] R3[527/1200], Temp: 0.5950, Energy: -54.576873+0.000431j
[2025-07-30 09:06:56] [Iter 1579/4650] R3[528/1200], Temp: 0.5937, Energy: -54.631380-0.000866j
[2025-07-30 09:07:06] [Iter 1580/4650] R3[529/1200], Temp: 0.5924, Energy: -54.552805-0.000890j
[2025-07-30 09:07:16] [Iter 1581/4650] R3[530/1200], Temp: 0.5911, Energy: -54.610130+0.000571j
[2025-07-30 09:07:26] [Iter 1582/4650] R3[531/1200], Temp: 0.5898, Energy: -54.559089-0.001326j
[2025-07-30 09:07:36] [Iter 1583/4650] R3[532/1200], Temp: 0.5885, Energy: -54.565454-0.001871j
[2025-07-30 09:07:47] [Iter 1584/4650] R3[533/1200], Temp: 0.5873, Energy: -54.601435+0.000691j
[2025-07-30 09:07:57] [Iter 1585/4650] R3[534/1200], Temp: 0.5860, Energy: -54.596221-0.002641j
[2025-07-30 09:08:07] [Iter 1586/4650] R3[535/1200], Temp: 0.5847, Energy: -54.611161-0.002108j
[2025-07-30 09:08:17] [Iter 1587/4650] R3[536/1200], Temp: 0.5834, Energy: -54.616526-0.002112j
[2025-07-30 09:08:27] [Iter 1588/4650] R3[537/1200], Temp: 0.5821, Energy: -54.624933-0.001267j
[2025-07-30 09:08:37] [Iter 1589/4650] R3[538/1200], Temp: 0.5808, Energy: -54.608739+0.003095j
[2025-07-30 09:08:47] [Iter 1590/4650] R3[539/1200], Temp: 0.5795, Energy: -54.556630+0.001820j
[2025-07-30 09:08:57] [Iter 1591/4650] R3[540/1200], Temp: 0.5782, Energy: -54.611852-0.000960j
[2025-07-30 09:09:07] [Iter 1592/4650] R3[541/1200], Temp: 0.5769, Energy: -54.588745+0.002445j
[2025-07-30 09:09:18] [Iter 1593/4650] R3[542/1200], Temp: 0.5756, Energy: -54.594200-0.001490j
[2025-07-30 09:09:28] [Iter 1594/4650] R3[543/1200], Temp: 0.5743, Energy: -54.618530-0.002191j
[2025-07-30 09:09:38] [Iter 1595/4650] R3[544/1200], Temp: 0.5730, Energy: -54.560049+0.000807j
[2025-07-30 09:09:48] [Iter 1596/4650] R3[545/1200], Temp: 0.5717, Energy: -54.559916+0.001097j
[2025-07-30 09:09:58] [Iter 1597/4650] R3[546/1200], Temp: 0.5705, Energy: -54.579283+0.001613j
[2025-07-30 09:10:08] [Iter 1598/4650] R3[547/1200], Temp: 0.5692, Energy: -54.541319+0.000415j
[2025-07-30 09:10:18] [Iter 1599/4650] R3[548/1200], Temp: 0.5679, Energy: -54.571901+0.001655j
[2025-07-30 09:10:28] [Iter 1600/4650] R3[549/1200], Temp: 0.5666, Energy: -54.601329-0.000161j
[2025-07-30 09:10:38] [Iter 1601/4650] R3[550/1200], Temp: 0.5653, Energy: -54.571159-0.002946j
[2025-07-30 09:10:49] [Iter 1602/4650] R3[551/1200], Temp: 0.5640, Energy: -54.535066+0.000889j
[2025-07-30 09:10:59] [Iter 1603/4650] R3[552/1200], Temp: 0.5627, Energy: -54.596796-0.000016j
[2025-07-30 09:11:09] [Iter 1604/4650] R3[553/1200], Temp: 0.5614, Energy: -54.648589+0.000650j
[2025-07-30 09:11:19] [Iter 1605/4650] R3[554/1200], Temp: 0.5601, Energy: -54.574635+0.000365j
[2025-07-30 09:11:29] [Iter 1606/4650] R3[555/1200], Temp: 0.5588, Energy: -54.632909+0.002106j
[2025-07-30 09:11:39] [Iter 1607/4650] R3[556/1200], Temp: 0.5575, Energy: -54.672724+0.003187j
[2025-07-30 09:11:49] [Iter 1608/4650] R3[557/1200], Temp: 0.5562, Energy: -54.646567+0.001532j
[2025-07-30 09:11:59] [Iter 1609/4650] R3[558/1200], Temp: 0.5549, Energy: -54.658290+0.001056j
[2025-07-30 09:12:10] [Iter 1610/4650] R3[559/1200], Temp: 0.5536, Energy: -54.624966+0.002036j
[2025-07-30 09:12:20] [Iter 1611/4650] R3[560/1200], Temp: 0.5523, Energy: -54.569601-0.005427j
[2025-07-30 09:12:30] [Iter 1612/4650] R3[561/1200], Temp: 0.5510, Energy: -54.580476+0.001748j
[2025-07-30 09:12:40] [Iter 1613/4650] R3[562/1200], Temp: 0.5497, Energy: -54.568729-0.000788j
[2025-07-30 09:12:50] [Iter 1614/4650] R3[563/1200], Temp: 0.5484, Energy: -54.606282-0.001999j
[2025-07-30 09:13:00] [Iter 1615/4650] R3[564/1200], Temp: 0.5471, Energy: -54.560902-0.000114j
[2025-07-30 09:13:10] [Iter 1616/4650] R3[565/1200], Temp: 0.5458, Energy: -54.609765+0.000204j
[2025-07-30 09:13:21] [Iter 1617/4650] R3[566/1200], Temp: 0.5444, Energy: -54.570659-0.000649j
[2025-07-30 09:13:31] [Iter 1618/4650] R3[567/1200], Temp: 0.5431, Energy: -54.594391-0.001513j
[2025-07-30 09:13:41] [Iter 1619/4650] R3[568/1200], Temp: 0.5418, Energy: -54.596013+0.003384j
[2025-07-30 09:13:51] [Iter 1620/4650] R3[569/1200], Temp: 0.5405, Energy: -54.576877+0.000179j
[2025-07-30 09:14:01] [Iter 1621/4650] R3[570/1200], Temp: 0.5392, Energy: -54.577782-0.001756j
[2025-07-30 09:14:11] [Iter 1622/4650] R3[571/1200], Temp: 0.5379, Energy: -54.552058+0.001308j
[2025-07-30 09:14:21] [Iter 1623/4650] R3[572/1200], Temp: 0.5366, Energy: -54.571083-0.001291j
[2025-07-30 09:14:31] [Iter 1624/4650] R3[573/1200], Temp: 0.5353, Energy: -54.591984-0.002817j
[2025-07-30 09:14:42] [Iter 1625/4650] R3[574/1200], Temp: 0.5340, Energy: -54.566231-0.000013j
[2025-07-30 09:14:52] [Iter 1626/4650] R3[575/1200], Temp: 0.5327, Energy: -54.576753+0.003264j
[2025-07-30 09:15:02] [Iter 1627/4650] R3[576/1200], Temp: 0.5314, Energy: -54.624923+0.001635j
[2025-07-30 09:15:12] [Iter 1628/4650] R3[577/1200], Temp: 0.5301, Energy: -54.589703+0.000388j
[2025-07-30 09:15:22] [Iter 1629/4650] R3[578/1200], Temp: 0.5288, Energy: -54.558123-0.003027j
[2025-07-30 09:15:32] [Iter 1630/4650] R3[579/1200], Temp: 0.5275, Energy: -54.565532+0.001500j
[2025-07-30 09:15:42] [Iter 1631/4650] R3[580/1200], Temp: 0.5262, Energy: -54.591544+0.002013j
[2025-07-30 09:15:52] [Iter 1632/4650] R3[581/1200], Temp: 0.5249, Energy: -54.564627-0.001298j
[2025-07-30 09:16:02] [Iter 1633/4650] R3[582/1200], Temp: 0.5236, Energy: -54.651026-0.000278j
[2025-07-30 09:16:13] [Iter 1634/4650] R3[583/1200], Temp: 0.5222, Energy: -54.606005+0.000445j
[2025-07-30 09:16:23] [Iter 1635/4650] R3[584/1200], Temp: 0.5209, Energy: -54.608939-0.000330j
[2025-07-30 09:16:33] [Iter 1636/4650] R3[585/1200], Temp: 0.5196, Energy: -54.582115-0.000225j
[2025-07-30 09:16:43] [Iter 1637/4650] R3[586/1200], Temp: 0.5183, Energy: -54.566583+0.000317j
[2025-07-30 09:16:53] [Iter 1638/4650] R3[587/1200], Temp: 0.5170, Energy: -54.554437+0.000801j
[2025-07-30 09:17:03] [Iter 1639/4650] R3[588/1200], Temp: 0.5157, Energy: -54.561761-0.001317j
[2025-07-30 09:17:13] [Iter 1640/4650] R3[589/1200], Temp: 0.5144, Energy: -54.580916+0.000139j
[2025-07-30 09:17:23] [Iter 1641/4650] R3[590/1200], Temp: 0.5131, Energy: -54.618387-0.000860j
[2025-07-30 09:17:33] [Iter 1642/4650] R3[591/1200], Temp: 0.5118, Energy: -54.557236+0.000675j
[2025-07-30 09:17:44] [Iter 1643/4650] R3[592/1200], Temp: 0.5105, Energy: -54.568008+0.001168j
[2025-07-30 09:17:54] [Iter 1644/4650] R3[593/1200], Temp: 0.5092, Energy: -54.627054+0.000837j
[2025-07-30 09:18:04] [Iter 1645/4650] R3[594/1200], Temp: 0.5079, Energy: -54.654898-0.001389j
[2025-07-30 09:18:14] [Iter 1646/4650] R3[595/1200], Temp: 0.5065, Energy: -54.619992+0.000825j
[2025-07-30 09:18:24] [Iter 1647/4650] R3[596/1200], Temp: 0.5052, Energy: -54.632104+0.004018j
[2025-07-30 09:18:34] [Iter 1648/4650] R3[597/1200], Temp: 0.5039, Energy: -54.641654+0.003663j
[2025-07-30 09:18:44] [Iter 1649/4650] R3[598/1200], Temp: 0.5026, Energy: -54.682336-0.000349j
[2025-07-30 09:18:54] [Iter 1650/4650] R3[599/1200], Temp: 0.5013, Energy: -54.660297-0.001167j
[2025-07-30 09:19:05] [Iter 1651/4650] R3[600/1200], Temp: 0.5000, Energy: -54.657716-0.002777j
[2025-07-30 09:19:15] [Iter 1652/4650] R3[601/1200], Temp: 0.4987, Energy: -54.615952+0.001398j
[2025-07-30 09:19:25] [Iter 1653/4650] R3[602/1200], Temp: 0.4974, Energy: -54.574204+0.000035j
[2025-07-30 09:19:35] [Iter 1654/4650] R3[603/1200], Temp: 0.4961, Energy: -54.538443+0.002190j
[2025-07-30 09:19:45] [Iter 1655/4650] R3[604/1200], Temp: 0.4948, Energy: -54.530722-0.002061j
[2025-07-30 09:19:55] [Iter 1656/4650] R3[605/1200], Temp: 0.4935, Energy: -54.562175-0.000379j
[2025-07-30 09:20:05] [Iter 1657/4650] R3[606/1200], Temp: 0.4921, Energy: -54.550377-0.000691j
[2025-07-30 09:20:15] [Iter 1658/4650] R3[607/1200], Temp: 0.4908, Energy: -54.541865-0.002130j
[2025-07-30 09:20:25] [Iter 1659/4650] R3[608/1200], Temp: 0.4895, Energy: -54.522877+0.000714j
[2025-07-30 09:20:36] [Iter 1660/4650] R3[609/1200], Temp: 0.4882, Energy: -54.522956+0.000068j
[2025-07-30 09:20:46] [Iter 1661/4650] R3[610/1200], Temp: 0.4869, Energy: -54.497149+0.002550j
[2025-07-30 09:20:56] [Iter 1662/4650] R3[611/1200], Temp: 0.4856, Energy: -54.524486+0.001973j
[2025-07-30 09:21:06] [Iter 1663/4650] R3[612/1200], Temp: 0.4843, Energy: -54.571317+0.001779j
[2025-07-30 09:21:16] [Iter 1664/4650] R3[613/1200], Temp: 0.4830, Energy: -54.636209-0.000008j
[2025-07-30 09:21:26] [Iter 1665/4650] R3[614/1200], Temp: 0.4817, Energy: -54.540219-0.000065j
[2025-07-30 09:21:36] [Iter 1666/4650] R3[615/1200], Temp: 0.4804, Energy: -54.525451-0.001177j
[2025-07-30 09:21:46] [Iter 1667/4650] R3[616/1200], Temp: 0.4791, Energy: -54.574200-0.000542j
[2025-07-30 09:21:56] [Iter 1668/4650] R3[617/1200], Temp: 0.4778, Energy: -54.593172+0.001795j
[2025-07-30 09:22:07] [Iter 1669/4650] R3[618/1200], Temp: 0.4764, Energy: -54.593849-0.000532j
[2025-07-30 09:22:17] [Iter 1670/4650] R3[619/1200], Temp: 0.4751, Energy: -54.595059+0.001359j
[2025-07-30 09:22:27] [Iter 1671/4650] R3[620/1200], Temp: 0.4738, Energy: -54.654951-0.000084j
[2025-07-30 09:22:37] [Iter 1672/4650] R3[621/1200], Temp: 0.4725, Energy: -54.707455+0.003717j
[2025-07-30 09:22:47] [Iter 1673/4650] R3[622/1200], Temp: 0.4712, Energy: -54.654345-0.002096j
[2025-07-30 09:22:57] [Iter 1674/4650] R3[623/1200], Temp: 0.4699, Energy: -54.643931-0.001817j
[2025-07-30 09:23:07] [Iter 1675/4650] R3[624/1200], Temp: 0.4686, Energy: -54.641444+0.000205j
[2025-07-30 09:23:17] [Iter 1676/4650] R3[625/1200], Temp: 0.4673, Energy: -54.604399-0.000432j
[2025-07-30 09:23:27] [Iter 1677/4650] R3[626/1200], Temp: 0.4660, Energy: -54.623994+0.003429j
[2025-07-30 09:23:37] [Iter 1678/4650] R3[627/1200], Temp: 0.4647, Energy: -54.611837-0.002243j
[2025-07-30 09:23:48] [Iter 1679/4650] R3[628/1200], Temp: 0.4634, Energy: -54.556267+0.002222j
[2025-07-30 09:23:58] [Iter 1680/4650] R3[629/1200], Temp: 0.4621, Energy: -54.623585+0.002579j
[2025-07-30 09:24:08] [Iter 1681/4650] R3[630/1200], Temp: 0.4608, Energy: -54.595779-0.000138j
[2025-07-30 09:24:18] [Iter 1682/4650] R3[631/1200], Temp: 0.4595, Energy: -54.529581-0.003247j
[2025-07-30 09:24:28] [Iter 1683/4650] R3[632/1200], Temp: 0.4582, Energy: -54.581133-0.002195j
[2025-07-30 09:24:38] [Iter 1684/4650] R3[633/1200], Temp: 0.4569, Energy: -54.578713+0.001932j
[2025-07-30 09:24:48] [Iter 1685/4650] R3[634/1200], Temp: 0.4556, Energy: -54.573089-0.000381j
[2025-07-30 09:24:58] [Iter 1686/4650] R3[635/1200], Temp: 0.4542, Energy: -54.638138+0.001808j
[2025-07-30 09:25:08] [Iter 1687/4650] R3[636/1200], Temp: 0.4529, Energy: -54.624856-0.003130j
[2025-07-30 09:25:19] [Iter 1688/4650] R3[637/1200], Temp: 0.4516, Energy: -54.623780-0.003725j
[2025-07-30 09:25:29] [Iter 1689/4650] R3[638/1200], Temp: 0.4503, Energy: -54.597727-0.001708j
[2025-07-30 09:25:39] [Iter 1690/4650] R3[639/1200], Temp: 0.4490, Energy: -54.571846-0.000018j
[2025-07-30 09:25:49] [Iter 1691/4650] R3[640/1200], Temp: 0.4477, Energy: -54.577284-0.003251j
[2025-07-30 09:25:59] [Iter 1692/4650] R3[641/1200], Temp: 0.4464, Energy: -54.574736+0.004916j
[2025-07-30 09:26:09] [Iter 1693/4650] R3[642/1200], Temp: 0.4451, Energy: -54.646090+0.001155j
[2025-07-30 09:26:19] [Iter 1694/4650] R3[643/1200], Temp: 0.4438, Energy: -54.662333+0.000819j
[2025-07-30 09:26:29] [Iter 1695/4650] R3[644/1200], Temp: 0.4425, Energy: -54.628112+0.001916j
[2025-07-30 09:26:40] [Iter 1696/4650] R3[645/1200], Temp: 0.4412, Energy: -54.674261-0.003377j
[2025-07-30 09:26:50] [Iter 1697/4650] R3[646/1200], Temp: 0.4399, Energy: -54.673617-0.000643j
[2025-07-30 09:27:00] [Iter 1698/4650] R3[647/1200], Temp: 0.4386, Energy: -54.657346+0.003303j
[2025-07-30 09:27:10] [Iter 1699/4650] R3[648/1200], Temp: 0.4373, Energy: -54.700028+0.000939j
[2025-07-30 09:27:20] [Iter 1700/4650] R3[649/1200], Temp: 0.4360, Energy: -54.708333+0.000667j
[2025-07-30 09:27:30] [Iter 1701/4650] R3[650/1200], Temp: 0.4347, Energy: -54.633893-0.000831j
[2025-07-30 09:27:40] [Iter 1702/4650] R3[651/1200], Temp: 0.4334, Energy: -54.560322+0.001729j
[2025-07-30 09:27:50] [Iter 1703/4650] R3[652/1200], Temp: 0.4321, Energy: -54.596781-0.001324j
[2025-07-30 09:28:01] [Iter 1704/4650] R3[653/1200], Temp: 0.4308, Energy: -54.667691-0.003168j
[2025-07-30 09:28:11] [Iter 1705/4650] R3[654/1200], Temp: 0.4295, Energy: -54.586685+0.003647j
[2025-07-30 09:28:21] [Iter 1706/4650] R3[655/1200], Temp: 0.4283, Energy: -54.570614+0.005723j
[2025-07-30 09:28:31] [Iter 1707/4650] R3[656/1200], Temp: 0.4270, Energy: -54.637368-0.002353j
[2025-07-30 09:28:41] [Iter 1708/4650] R3[657/1200], Temp: 0.4257, Energy: -54.615593-0.000715j
[2025-07-30 09:28:51] [Iter 1709/4650] R3[658/1200], Temp: 0.4244, Energy: -54.598134+0.002080j
[2025-07-30 09:29:01] [Iter 1710/4650] R3[659/1200], Temp: 0.4231, Energy: -54.563924-0.002044j
[2025-07-30 09:29:11] [Iter 1711/4650] R3[660/1200], Temp: 0.4218, Energy: -54.573376-0.001086j
[2025-07-30 09:29:21] [Iter 1712/4650] R3[661/1200], Temp: 0.4205, Energy: -54.520095-0.000755j
[2025-07-30 09:29:32] [Iter 1713/4650] R3[662/1200], Temp: 0.4192, Energy: -54.619916+0.001960j
[2025-07-30 09:29:42] [Iter 1714/4650] R3[663/1200], Temp: 0.4179, Energy: -54.613860+0.000123j
[2025-07-30 09:29:52] [Iter 1715/4650] R3[664/1200], Temp: 0.4166, Energy: -54.586537-0.001431j
[2025-07-30 09:30:02] [Iter 1716/4650] R3[665/1200], Temp: 0.4153, Energy: -54.611908-0.003377j
[2025-07-30 09:30:12] [Iter 1717/4650] R3[666/1200], Temp: 0.4140, Energy: -54.612264+0.000242j
[2025-07-30 09:30:22] [Iter 1718/4650] R3[667/1200], Temp: 0.4127, Energy: -54.657713+0.005249j
[2025-07-30 09:30:32] [Iter 1719/4650] R3[668/1200], Temp: 0.4115, Energy: -54.610287+0.001270j
[2025-07-30 09:30:42] [Iter 1720/4650] R3[669/1200], Temp: 0.4102, Energy: -54.596458-0.001658j
[2025-07-30 09:30:52] [Iter 1721/4650] R3[670/1200], Temp: 0.4089, Energy: -54.609126-0.000857j
[2025-07-30 09:31:03] [Iter 1722/4650] R3[671/1200], Temp: 0.4076, Energy: -54.589968+0.001797j
[2025-07-30 09:31:13] [Iter 1723/4650] R3[672/1200], Temp: 0.4063, Energy: -54.595898-0.001091j
[2025-07-30 09:31:23] [Iter 1724/4650] R3[673/1200], Temp: 0.4050, Energy: -54.597244-0.001309j
[2025-07-30 09:31:33] [Iter 1725/4650] R3[674/1200], Temp: 0.4037, Energy: -54.611085-0.000016j
[2025-07-30 09:31:43] [Iter 1726/4650] R3[675/1200], Temp: 0.4025, Energy: -54.630819+0.000663j
[2025-07-30 09:31:53] [Iter 1727/4650] R3[676/1200], Temp: 0.4012, Energy: -54.564763-0.002542j
[2025-07-30 09:32:03] [Iter 1728/4650] R3[677/1200], Temp: 0.3999, Energy: -54.634839+0.000761j
[2025-07-30 09:32:13] [Iter 1729/4650] R3[678/1200], Temp: 0.3986, Energy: -54.659028+0.001493j
[2025-07-30 09:32:23] [Iter 1730/4650] R3[679/1200], Temp: 0.3973, Energy: -54.596513-0.000253j
[2025-07-30 09:32:34] [Iter 1731/4650] R3[680/1200], Temp: 0.3960, Energy: -54.642810-0.001750j
[2025-07-30 09:32:44] [Iter 1732/4650] R3[681/1200], Temp: 0.3948, Energy: -54.595798+0.001023j
[2025-07-30 09:32:54] [Iter 1733/4650] R3[682/1200], Temp: 0.3935, Energy: -54.577336-0.002871j
[2025-07-30 09:33:04] [Iter 1734/4650] R3[683/1200], Temp: 0.3922, Energy: -54.613105+0.000906j
[2025-07-30 09:33:14] [Iter 1735/4650] R3[684/1200], Temp: 0.3909, Energy: -54.589127-0.003566j
[2025-07-30 09:33:24] [Iter 1736/4650] R3[685/1200], Temp: 0.3897, Energy: -54.588782+0.002598j
[2025-07-30 09:33:34] [Iter 1737/4650] R3[686/1200], Temp: 0.3884, Energy: -54.615756+0.001037j
[2025-07-30 09:33:45] [Iter 1738/4650] R3[687/1200], Temp: 0.3871, Energy: -54.670557+0.003814j
[2025-07-30 09:33:55] [Iter 1739/4650] R3[688/1200], Temp: 0.3858, Energy: -54.688491+0.001060j
[2025-07-30 09:34:05] [Iter 1740/4650] R3[689/1200], Temp: 0.3846, Energy: -54.654378+0.002062j
[2025-07-30 09:34:15] [Iter 1741/4650] R3[690/1200], Temp: 0.3833, Energy: -54.642471-0.001115j
[2025-07-30 09:34:25] [Iter 1742/4650] R3[691/1200], Temp: 0.3820, Energy: -54.649703+0.003104j
[2025-07-30 09:34:35] [Iter 1743/4650] R3[692/1200], Temp: 0.3807, Energy: -54.677155-0.001790j
[2025-07-30 09:34:45] [Iter 1744/4650] R3[693/1200], Temp: 0.3795, Energy: -54.640425-0.002155j
[2025-07-30 09:34:55] [Iter 1745/4650] R3[694/1200], Temp: 0.3782, Energy: -54.649517-0.002146j
[2025-07-30 09:35:05] [Iter 1746/4650] R3[695/1200], Temp: 0.3769, Energy: -54.631440-0.000893j
[2025-07-30 09:35:15] [Iter 1747/4650] R3[696/1200], Temp: 0.3757, Energy: -54.685421-0.003795j
[2025-07-30 09:35:26] [Iter 1748/4650] R3[697/1200], Temp: 0.3744, Energy: -54.644319+0.001033j
[2025-07-30 09:35:36] [Iter 1749/4650] R3[698/1200], Temp: 0.3731, Energy: -54.626202-0.000196j
[2025-07-30 09:35:46] [Iter 1750/4650] R3[699/1200], Temp: 0.3719, Energy: -54.641537+0.001300j
[2025-07-30 09:35:56] [Iter 1751/4650] R3[700/1200], Temp: 0.3706, Energy: -54.607304+0.002379j
[2025-07-30 09:36:06] [Iter 1752/4650] R3[701/1200], Temp: 0.3693, Energy: -54.553892+0.000381j
[2025-07-30 09:36:16] [Iter 1753/4650] R3[702/1200], Temp: 0.3681, Energy: -54.620742-0.000686j
[2025-07-30 09:36:26] [Iter 1754/4650] R3[703/1200], Temp: 0.3668, Energy: -54.624826+0.000239j
[2025-07-30 09:36:36] [Iter 1755/4650] R3[704/1200], Temp: 0.3655, Energy: -54.604062-0.000810j
[2025-07-30 09:36:47] [Iter 1756/4650] R3[705/1200], Temp: 0.3643, Energy: -54.658687-0.001691j
[2025-07-30 09:36:57] [Iter 1757/4650] R3[706/1200], Temp: 0.3630, Energy: -54.622308-0.001751j
[2025-07-30 09:37:07] [Iter 1758/4650] R3[707/1200], Temp: 0.3618, Energy: -54.651656+0.003983j
[2025-07-30 09:37:17] [Iter 1759/4650] R3[708/1200], Temp: 0.3605, Energy: -54.669159-0.001202j
[2025-07-30 09:37:27] [Iter 1760/4650] R3[709/1200], Temp: 0.3592, Energy: -54.636747+0.001944j
[2025-07-30 09:37:37] [Iter 1761/4650] R3[710/1200], Temp: 0.3580, Energy: -54.648691-0.003030j
[2025-07-30 09:37:47] [Iter 1762/4650] R3[711/1200], Temp: 0.3567, Energy: -54.680650-0.003771j
[2025-07-30 09:37:57] [Iter 1763/4650] R3[712/1200], Temp: 0.3555, Energy: -54.676668-0.001342j
[2025-07-30 09:38:07] [Iter 1764/4650] R3[713/1200], Temp: 0.3542, Energy: -54.688149-0.001337j
[2025-07-30 09:38:17] [Iter 1765/4650] R3[714/1200], Temp: 0.3530, Energy: -54.654798+0.000405j
[2025-07-30 09:38:28] [Iter 1766/4650] R3[715/1200], Temp: 0.3517, Energy: -54.686592-0.001113j
[2025-07-30 09:38:38] [Iter 1767/4650] R3[716/1200], Temp: 0.3505, Energy: -54.688988-0.000688j
[2025-07-30 09:38:48] [Iter 1768/4650] R3[717/1200], Temp: 0.3492, Energy: -54.711745-0.002072j
[2025-07-30 09:38:58] [Iter 1769/4650] R3[718/1200], Temp: 0.3480, Energy: -54.654477+0.002151j
[2025-07-30 09:39:08] [Iter 1770/4650] R3[719/1200], Temp: 0.3467, Energy: -54.697383-0.001409j
[2025-07-30 09:39:18] [Iter 1771/4650] R3[720/1200], Temp: 0.3455, Energy: -54.680728-0.001930j
[2025-07-30 09:39:28] [Iter 1772/4650] R3[721/1200], Temp: 0.3442, Energy: -54.717024-0.001485j
[2025-07-30 09:39:38] [Iter 1773/4650] R3[722/1200], Temp: 0.3430, Energy: -54.693284-0.000559j
[2025-07-30 09:39:49] [Iter 1774/4650] R3[723/1200], Temp: 0.3418, Energy: -54.637410+0.003336j
[2025-07-30 09:39:59] [Iter 1775/4650] R3[724/1200], Temp: 0.3405, Energy: -54.691094+0.001966j
[2025-07-30 09:40:09] [Iter 1776/4650] R3[725/1200], Temp: 0.3393, Energy: -54.686798+0.001066j
[2025-07-30 09:40:19] [Iter 1777/4650] R3[726/1200], Temp: 0.3380, Energy: -54.684863+0.001688j
[2025-07-30 09:40:29] [Iter 1778/4650] R3[727/1200], Temp: 0.3368, Energy: -54.660125+0.001980j
[2025-07-30 09:40:39] [Iter 1779/4650] R3[728/1200], Temp: 0.3356, Energy: -54.679497+0.000115j
[2025-07-30 09:40:49] [Iter 1780/4650] R3[729/1200], Temp: 0.3343, Energy: -54.680293+0.001975j
[2025-07-30 09:40:59] [Iter 1781/4650] R3[730/1200], Temp: 0.3331, Energy: -54.608827+0.001528j
[2025-07-30 09:41:10] [Iter 1782/4650] R3[731/1200], Temp: 0.3319, Energy: -54.655369+0.000761j
[2025-07-30 09:41:20] [Iter 1783/4650] R3[732/1200], Temp: 0.3306, Energy: -54.629475-0.000288j
[2025-07-30 09:41:30] [Iter 1784/4650] R3[733/1200], Temp: 0.3294, Energy: -54.602864+0.003450j
[2025-07-30 09:41:40] [Iter 1785/4650] R3[734/1200], Temp: 0.3282, Energy: -54.582758+0.005336j
[2025-07-30 09:41:50] [Iter 1786/4650] R3[735/1200], Temp: 0.3269, Energy: -54.623962-0.002177j
[2025-07-30 09:42:00] [Iter 1787/4650] R3[736/1200], Temp: 0.3257, Energy: -54.642554+0.004470j
[2025-07-30 09:42:10] [Iter 1788/4650] R3[737/1200], Temp: 0.3245, Energy: -54.670098+0.001054j
[2025-07-30 09:42:20] [Iter 1789/4650] R3[738/1200], Temp: 0.3233, Energy: -54.633693-0.001737j
[2025-07-30 09:42:31] [Iter 1790/4650] R3[739/1200], Temp: 0.3220, Energy: -54.631357-0.001566j
[2025-07-30 09:42:41] [Iter 1791/4650] R3[740/1200], Temp: 0.3208, Energy: -54.591934-0.000142j
[2025-07-30 09:42:51] [Iter 1792/4650] R3[741/1200], Temp: 0.3196, Energy: -54.605448+0.000521j
[2025-07-30 09:43:01] [Iter 1793/4650] R3[742/1200], Temp: 0.3184, Energy: -54.577474+0.000018j
[2025-07-30 09:43:11] [Iter 1794/4650] R3[743/1200], Temp: 0.3172, Energy: -54.677987-0.001500j
[2025-07-30 09:43:21] [Iter 1795/4650] R3[744/1200], Temp: 0.3159, Energy: -54.640995-0.000389j
[2025-07-30 09:43:31] [Iter 1796/4650] R3[745/1200], Temp: 0.3147, Energy: -54.664394-0.001339j
[2025-07-30 09:43:41] [Iter 1797/4650] R3[746/1200], Temp: 0.3135, Energy: -54.628177+0.000837j
[2025-07-30 09:43:51] [Iter 1798/4650] R3[747/1200], Temp: 0.3123, Energy: -54.650612+0.000243j
[2025-07-30 09:44:01] [Iter 1799/4650] R3[748/1200], Temp: 0.3111, Energy: -54.620679+0.000985j
[2025-07-30 09:44:12] [Iter 1800/4650] R3[749/1200], Temp: 0.3099, Energy: -54.611993+0.000163j
[2025-07-30 09:44:22] [Iter 1801/4650] R3[750/1200], Temp: 0.3087, Energy: -54.568612-0.000716j
[2025-07-30 09:44:32] [Iter 1802/4650] R3[751/1200], Temp: 0.3074, Energy: -54.585524-0.001425j
[2025-07-30 09:44:42] [Iter 1803/4650] R3[752/1200], Temp: 0.3062, Energy: -54.586888-0.002883j
[2025-07-30 09:44:52] [Iter 1804/4650] R3[753/1200], Temp: 0.3050, Energy: -54.600254+0.000403j
[2025-07-30 09:45:02] [Iter 1805/4650] R3[754/1200], Temp: 0.3038, Energy: -54.678033-0.002502j
[2025-07-30 09:45:12] [Iter 1806/4650] R3[755/1200], Temp: 0.3026, Energy: -54.600843-0.000613j
[2025-07-30 09:45:22] [Iter 1807/4650] R3[756/1200], Temp: 0.3014, Energy: -54.677573+0.000225j
[2025-07-30 09:45:32] [Iter 1808/4650] R3[757/1200], Temp: 0.3002, Energy: -54.652808+0.000467j
[2025-07-30 09:45:43] [Iter 1809/4650] R3[758/1200], Temp: 0.2990, Energy: -54.621199-0.001424j
[2025-07-30 09:45:53] [Iter 1810/4650] R3[759/1200], Temp: 0.2978, Energy: -54.570639+0.000894j
[2025-07-30 09:46:03] [Iter 1811/4650] R3[760/1200], Temp: 0.2966, Energy: -54.574057-0.000907j
[2025-07-30 09:46:13] [Iter 1812/4650] R3[761/1200], Temp: 0.2954, Energy: -54.608658-0.001496j
[2025-07-30 09:46:23] [Iter 1813/4650] R3[762/1200], Temp: 0.2942, Energy: -54.647409-0.001078j
[2025-07-30 09:46:33] [Iter 1814/4650] R3[763/1200], Temp: 0.2931, Energy: -54.640628-0.003939j
[2025-07-30 09:46:43] [Iter 1815/4650] R3[764/1200], Temp: 0.2919, Energy: -54.653057-0.002273j
[2025-07-30 09:46:53] [Iter 1816/4650] R3[765/1200], Temp: 0.2907, Energy: -54.685589+0.000802j
[2025-07-30 09:47:04] [Iter 1817/4650] R3[766/1200], Temp: 0.2895, Energy: -54.631298-0.002786j
[2025-07-30 09:47:14] [Iter 1818/4650] R3[767/1200], Temp: 0.2883, Energy: -54.611611-0.000201j
[2025-07-30 09:47:24] [Iter 1819/4650] R3[768/1200], Temp: 0.2871, Energy: -54.572915-0.002318j
[2025-07-30 09:47:34] [Iter 1820/4650] R3[769/1200], Temp: 0.2859, Energy: -54.603337-0.000969j
[2025-07-30 09:47:44] [Iter 1821/4650] R3[770/1200], Temp: 0.2847, Energy: -54.668833-0.000517j
[2025-07-30 09:47:54] [Iter 1822/4650] R3[771/1200], Temp: 0.2836, Energy: -54.727323-0.001499j
[2025-07-30 09:48:04] [Iter 1823/4650] R3[772/1200], Temp: 0.2824, Energy: -54.703218-0.000165j
[2025-07-30 09:48:14] [Iter 1824/4650] R3[773/1200], Temp: 0.2812, Energy: -54.643296+0.000544j
[2025-07-30 09:48:24] [Iter 1825/4650] R3[774/1200], Temp: 0.2800, Energy: -54.634263-0.000344j
[2025-07-30 09:48:35] [Iter 1826/4650] R3[775/1200], Temp: 0.2789, Energy: -54.591536+0.004492j
[2025-07-30 09:48:45] [Iter 1827/4650] R3[776/1200], Temp: 0.2777, Energy: -54.626694-0.001388j
[2025-07-30 09:48:55] [Iter 1828/4650] R3[777/1200], Temp: 0.2765, Energy: -54.626910+0.001741j
[2025-07-30 09:49:05] [Iter 1829/4650] R3[778/1200], Temp: 0.2753, Energy: -54.689932+0.003320j
[2025-07-30 09:49:15] [Iter 1830/4650] R3[779/1200], Temp: 0.2742, Energy: -54.665263-0.000079j
[2025-07-30 09:49:25] [Iter 1831/4650] R3[780/1200], Temp: 0.2730, Energy: -54.661544-0.000311j
[2025-07-30 09:49:35] [Iter 1832/4650] R3[781/1200], Temp: 0.2718, Energy: -54.642245+0.000190j
[2025-07-30 09:49:45] [Iter 1833/4650] R3[782/1200], Temp: 0.2707, Energy: -54.664143-0.004678j
[2025-07-30 09:49:55] [Iter 1834/4650] R3[783/1200], Temp: 0.2695, Energy: -54.655828-0.000214j
[2025-07-30 09:50:06] [Iter 1835/4650] R3[784/1200], Temp: 0.2684, Energy: -54.622947+0.002365j
[2025-07-30 09:50:16] [Iter 1836/4650] R3[785/1200], Temp: 0.2672, Energy: -54.624657-0.000676j
[2025-07-30 09:50:26] [Iter 1837/4650] R3[786/1200], Temp: 0.2660, Energy: -54.654006+0.000015j
[2025-07-30 09:50:36] [Iter 1838/4650] R3[787/1200], Temp: 0.2649, Energy: -54.641395+0.001394j
[2025-07-30 09:50:46] [Iter 1839/4650] R3[788/1200], Temp: 0.2637, Energy: -54.654993-0.000490j
[2025-07-30 09:50:56] [Iter 1840/4650] R3[789/1200], Temp: 0.2626, Energy: -54.704854+0.001607j
[2025-07-30 09:51:06] [Iter 1841/4650] R3[790/1200], Temp: 0.2614, Energy: -54.687494-0.001079j
[2025-07-30 09:51:16] [Iter 1842/4650] R3[791/1200], Temp: 0.2603, Energy: -54.706208-0.002703j
[2025-07-30 09:51:26] [Iter 1843/4650] R3[792/1200], Temp: 0.2591, Energy: -54.683380-0.000722j
[2025-07-30 09:51:37] [Iter 1844/4650] R3[793/1200], Temp: 0.2580, Energy: -54.724225-0.000320j
[2025-07-30 09:51:47] [Iter 1845/4650] R3[794/1200], Temp: 0.2568, Energy: -54.621001-0.000548j
[2025-07-30 09:51:57] [Iter 1846/4650] R3[795/1200], Temp: 0.2557, Energy: -54.725024+0.004781j
[2025-07-30 09:52:07] [Iter 1847/4650] R3[796/1200], Temp: 0.2545, Energy: -54.732865-0.000049j
[2025-07-30 09:52:17] [Iter 1848/4650] R3[797/1200], Temp: 0.2534, Energy: -54.696361-0.004033j
[2025-07-30 09:52:27] [Iter 1849/4650] R3[798/1200], Temp: 0.2523, Energy: -54.647476+0.000433j
[2025-07-30 09:52:37] [Iter 1850/4650] R3[799/1200], Temp: 0.2511, Energy: -54.687229-0.000899j
[2025-07-30 09:52:47] [Iter 1851/4650] R3[800/1200], Temp: 0.2500, Energy: -54.687997-0.004166j
[2025-07-30 09:52:57] [Iter 1852/4650] R3[801/1200], Temp: 0.2489, Energy: -54.693391-0.003013j
[2025-07-30 09:53:08] [Iter 1853/4650] R3[802/1200], Temp: 0.2477, Energy: -54.683534-0.002249j
[2025-07-30 09:53:18] [Iter 1854/4650] R3[803/1200], Temp: 0.2466, Energy: -54.670771+0.001451j
[2025-07-30 09:53:28] [Iter 1855/4650] R3[804/1200], Temp: 0.2455, Energy: -54.680393+0.000749j
[2025-07-30 09:53:38] [Iter 1856/4650] R3[805/1200], Temp: 0.2444, Energy: -54.608334-0.000215j
[2025-07-30 09:53:48] [Iter 1857/4650] R3[806/1200], Temp: 0.2432, Energy: -54.645894-0.001621j
[2025-07-30 09:53:58] [Iter 1858/4650] R3[807/1200], Temp: 0.2421, Energy: -54.601465+0.000774j
[2025-07-30 09:54:08] [Iter 1859/4650] R3[808/1200], Temp: 0.2410, Energy: -54.584590+0.000309j
[2025-07-30 09:54:18] [Iter 1860/4650] R3[809/1200], Temp: 0.2399, Energy: -54.595115+0.002236j
[2025-07-30 09:54:28] [Iter 1861/4650] R3[810/1200], Temp: 0.2388, Energy: -54.664665-0.001822j
[2025-07-30 09:54:39] [Iter 1862/4650] R3[811/1200], Temp: 0.2376, Energy: -54.627916-0.002034j
[2025-07-30 09:54:49] [Iter 1863/4650] R3[812/1200], Temp: 0.2365, Energy: -54.670346+0.000880j
[2025-07-30 09:54:59] [Iter 1864/4650] R3[813/1200], Temp: 0.2354, Energy: -54.635704+0.000649j
[2025-07-30 09:55:09] [Iter 1865/4650] R3[814/1200], Temp: 0.2343, Energy: -54.669759+0.001454j
[2025-07-30 09:55:19] [Iter 1866/4650] R3[815/1200], Temp: 0.2332, Energy: -54.594018-0.000213j
[2025-07-30 09:55:29] [Iter 1867/4650] R3[816/1200], Temp: 0.2321, Energy: -54.578737-0.000021j
[2025-07-30 09:55:39] [Iter 1868/4650] R3[817/1200], Temp: 0.2310, Energy: -54.576675+0.000951j
[2025-07-30 09:55:49] [Iter 1869/4650] R3[818/1200], Temp: 0.2299, Energy: -54.595082-0.001929j
[2025-07-30 09:55:59] [Iter 1870/4650] R3[819/1200], Temp: 0.2288, Energy: -54.621122+0.000948j
[2025-07-30 09:56:10] [Iter 1871/4650] R3[820/1200], Temp: 0.2277, Energy: -54.616084-0.001879j
[2025-07-30 09:56:20] [Iter 1872/4650] R3[821/1200], Temp: 0.2266, Energy: -54.631578+0.001689j
[2025-07-30 09:56:30] [Iter 1873/4650] R3[822/1200], Temp: 0.2255, Energy: -54.656097+0.002068j
[2025-07-30 09:56:40] [Iter 1874/4650] R3[823/1200], Temp: 0.2244, Energy: -54.640613-0.000017j
[2025-07-30 09:56:50] [Iter 1875/4650] R3[824/1200], Temp: 0.2233, Energy: -54.655461+0.002388j
[2025-07-30 09:57:00] [Iter 1876/4650] R3[825/1200], Temp: 0.2222, Energy: -54.651664+0.002926j
[2025-07-30 09:57:10] [Iter 1877/4650] R3[826/1200], Temp: 0.2211, Energy: -54.615354+0.000237j
[2025-07-30 09:57:20] [Iter 1878/4650] R3[827/1200], Temp: 0.2200, Energy: -54.655192+0.000427j
[2025-07-30 09:57:31] [Iter 1879/4650] R3[828/1200], Temp: 0.2190, Energy: -54.601750+0.001684j
[2025-07-30 09:57:41] [Iter 1880/4650] R3[829/1200], Temp: 0.2179, Energy: -54.614267-0.000739j
[2025-07-30 09:57:51] [Iter 1881/4650] R3[830/1200], Temp: 0.2168, Energy: -54.626037+0.001072j
[2025-07-30 09:58:01] [Iter 1882/4650] R3[831/1200], Temp: 0.2157, Energy: -54.639343+0.000204j
[2025-07-30 09:58:11] [Iter 1883/4650] R3[832/1200], Temp: 0.2146, Energy: -54.640730+0.001238j
[2025-07-30 09:58:21] [Iter 1884/4650] R3[833/1200], Temp: 0.2136, Energy: -54.692147+0.000229j
[2025-07-30 09:58:31] [Iter 1885/4650] R3[834/1200], Temp: 0.2125, Energy: -54.613306+0.002176j
[2025-07-30 09:58:41] [Iter 1886/4650] R3[835/1200], Temp: 0.2114, Energy: -54.624478-0.001346j
[2025-07-30 09:58:51] [Iter 1887/4650] R3[836/1200], Temp: 0.2104, Energy: -54.618501+0.001027j
[2025-07-30 09:59:02] [Iter 1888/4650] R3[837/1200], Temp: 0.2093, Energy: -54.648386+0.001362j
[2025-07-30 09:59:12] [Iter 1889/4650] R3[838/1200], Temp: 0.2082, Energy: -54.646714+0.002683j
[2025-07-30 09:59:22] [Iter 1890/4650] R3[839/1200], Temp: 0.2072, Energy: -54.550837-0.000134j
[2025-07-30 09:59:32] [Iter 1891/4650] R3[840/1200], Temp: 0.2061, Energy: -54.608834+0.000643j
[2025-07-30 09:59:42] [Iter 1892/4650] R3[841/1200], Temp: 0.2050, Energy: -54.661046-0.000249j
[2025-07-30 09:59:52] [Iter 1893/4650] R3[842/1200], Temp: 0.2040, Energy: -54.683404+0.001868j
[2025-07-30 10:00:02] [Iter 1894/4650] R3[843/1200], Temp: 0.2029, Energy: -54.618084+0.000790j
[2025-07-30 10:00:12] [Iter 1895/4650] R3[844/1200], Temp: 0.2019, Energy: -54.609058+0.001637j
[2025-07-30 10:00:22] [Iter 1896/4650] R3[845/1200], Temp: 0.2008, Energy: -54.637606-0.003155j
[2025-07-30 10:00:32] [Iter 1897/4650] R3[846/1200], Temp: 0.1998, Energy: -54.654682-0.001349j
[2025-07-30 10:00:43] [Iter 1898/4650] R3[847/1200], Temp: 0.1987, Energy: -54.619230-0.000189j
[2025-07-30 10:00:53] [Iter 1899/4650] R3[848/1200], Temp: 0.1977, Energy: -54.543091-0.001464j
[2025-07-30 10:01:03] [Iter 1900/4650] R3[849/1200], Temp: 0.1967, Energy: -54.583028-0.000808j
[2025-07-30 10:01:13] [Iter 1901/4650] R3[850/1200], Temp: 0.1956, Energy: -54.560600+0.000200j
[2025-07-30 10:01:23] [Iter 1902/4650] R3[851/1200], Temp: 0.1946, Energy: -54.579098+0.000445j
[2025-07-30 10:01:33] [Iter 1903/4650] R3[852/1200], Temp: 0.1935, Energy: -54.612462+0.000404j
[2025-07-30 10:01:43] [Iter 1904/4650] R3[853/1200], Temp: 0.1925, Energy: -54.560417-0.000767j
[2025-07-30 10:01:53] [Iter 1905/4650] R3[854/1200], Temp: 0.1915, Energy: -54.597028-0.000581j
[2025-07-30 10:02:04] [Iter 1906/4650] R3[855/1200], Temp: 0.1905, Energy: -54.562166-0.001240j
[2025-07-30 10:02:14] [Iter 1907/4650] R3[856/1200], Temp: 0.1894, Energy: -54.565573+0.001351j
[2025-07-30 10:02:24] [Iter 1908/4650] R3[857/1200], Temp: 0.1884, Energy: -54.558840-0.001394j
[2025-07-30 10:02:34] [Iter 1909/4650] R3[858/1200], Temp: 0.1874, Energy: -54.540177-0.001767j
[2025-07-30 10:02:44] [Iter 1910/4650] R3[859/1200], Temp: 0.1864, Energy: -54.543787-0.002030j
[2025-07-30 10:02:54] [Iter 1911/4650] R3[860/1200], Temp: 0.1853, Energy: -54.537715+0.002529j
[2025-07-30 10:03:04] [Iter 1912/4650] R3[861/1200], Temp: 0.1843, Energy: -54.506984-0.003765j
[2025-07-30 10:03:14] [Iter 1913/4650] R3[862/1200], Temp: 0.1833, Energy: -54.555538+0.001995j
[2025-07-30 10:03:24] [Iter 1914/4650] R3[863/1200], Temp: 0.1823, Energy: -54.579148+0.000503j
[2025-07-30 10:03:35] [Iter 1915/4650] R3[864/1200], Temp: 0.1813, Energy: -54.557789+0.001288j
[2025-07-30 10:03:45] [Iter 1916/4650] R3[865/1200], Temp: 0.1803, Energy: -54.547086+0.000274j
[2025-07-30 10:03:55] [Iter 1917/4650] R3[866/1200], Temp: 0.1793, Energy: -54.533199+0.000347j
[2025-07-30 10:04:05] [Iter 1918/4650] R3[867/1200], Temp: 0.1783, Energy: -54.558771-0.003615j
[2025-07-30 10:04:15] [Iter 1919/4650] R3[868/1200], Temp: 0.1773, Energy: -54.587548+0.002541j
[2025-07-30 10:04:25] [Iter 1920/4650] R3[869/1200], Temp: 0.1763, Energy: -54.539739+0.002076j
[2025-07-30 10:04:35] [Iter 1921/4650] R3[870/1200], Temp: 0.1753, Energy: -54.563559+0.000130j
[2025-07-30 10:04:45] [Iter 1922/4650] R3[871/1200], Temp: 0.1743, Energy: -54.560561-0.000134j
[2025-07-30 10:04:55] [Iter 1923/4650] R3[872/1200], Temp: 0.1733, Energy: -54.540189-0.000458j
[2025-07-30 10:05:06] [Iter 1924/4650] R3[873/1200], Temp: 0.1723, Energy: -54.590343+0.000992j
[2025-07-30 10:05:16] [Iter 1925/4650] R3[874/1200], Temp: 0.1713, Energy: -54.629981-0.001293j
[2025-07-30 10:05:26] [Iter 1926/4650] R3[875/1200], Temp: 0.1703, Energy: -54.600882+0.001170j
[2025-07-30 10:05:36] [Iter 1927/4650] R3[876/1200], Temp: 0.1693, Energy: -54.586199-0.000950j
[2025-07-30 10:05:46] [Iter 1928/4650] R3[877/1200], Temp: 0.1684, Energy: -54.625638-0.001145j
[2025-07-30 10:05:56] [Iter 1929/4650] R3[878/1200], Temp: 0.1674, Energy: -54.605862+0.003912j
[2025-07-30 10:06:06] [Iter 1930/4650] R3[879/1200], Temp: 0.1664, Energy: -54.638203+0.000947j
[2025-07-30 10:06:16] [Iter 1931/4650] R3[880/1200], Temp: 0.1654, Energy: -54.634162+0.000024j
[2025-07-30 10:06:26] [Iter 1932/4650] R3[881/1200], Temp: 0.1645, Energy: -54.567163-0.001752j
[2025-07-30 10:06:36] [Iter 1933/4650] R3[882/1200], Temp: 0.1635, Energy: -54.563549+0.001026j
[2025-07-30 10:06:47] [Iter 1934/4650] R3[883/1200], Temp: 0.1625, Energy: -54.630253-0.002777j
[2025-07-30 10:06:57] [Iter 1935/4650] R3[884/1200], Temp: 0.1616, Energy: -54.649524-0.000733j
[2025-07-30 10:07:07] [Iter 1936/4650] R3[885/1200], Temp: 0.1606, Energy: -54.608760-0.003904j
[2025-07-30 10:07:17] [Iter 1937/4650] R3[886/1200], Temp: 0.1596, Energy: -54.629593-0.001003j
[2025-07-30 10:07:27] [Iter 1938/4650] R3[887/1200], Temp: 0.1587, Energy: -54.622886+0.003128j
[2025-07-30 10:07:37] [Iter 1939/4650] R3[888/1200], Temp: 0.1577, Energy: -54.610366-0.001811j
[2025-07-30 10:07:47] [Iter 1940/4650] R3[889/1200], Temp: 0.1568, Energy: -54.618924-0.000536j
[2025-07-30 10:07:57] [Iter 1941/4650] R3[890/1200], Temp: 0.1558, Energy: -54.651238-0.001375j
[2025-07-30 10:08:07] [Iter 1942/4650] R3[891/1200], Temp: 0.1549, Energy: -54.628103+0.000999j
[2025-07-30 10:08:18] [Iter 1943/4650] R3[892/1200], Temp: 0.1539, Energy: -54.639938-0.000035j
[2025-07-30 10:08:28] [Iter 1944/4650] R3[893/1200], Temp: 0.1530, Energy: -54.613028+0.000055j
[2025-07-30 10:08:38] [Iter 1945/4650] R3[894/1200], Temp: 0.1520, Energy: -54.613645-0.002271j
[2025-07-30 10:08:48] [Iter 1946/4650] R3[895/1200], Temp: 0.1511, Energy: -54.636340+0.001772j
[2025-07-30 10:08:58] [Iter 1947/4650] R3[896/1200], Temp: 0.1502, Energy: -54.649592+0.002528j
[2025-07-30 10:09:08] [Iter 1948/4650] R3[897/1200], Temp: 0.1492, Energy: -54.606563+0.000267j
[2025-07-30 10:09:18] [Iter 1949/4650] R3[898/1200], Temp: 0.1483, Energy: -54.604351+0.001010j
[2025-07-30 10:09:28] [Iter 1950/4650] R3[899/1200], Temp: 0.1474, Energy: -54.620032+0.003665j
[2025-07-30 10:09:39] [Iter 1951/4650] R3[900/1200], Temp: 0.1464, Energy: -54.621312+0.001238j
[2025-07-30 10:09:49] [Iter 1952/4650] R3[901/1200], Temp: 0.1455, Energy: -54.635614+0.003569j
[2025-07-30 10:09:59] [Iter 1953/4650] R3[902/1200], Temp: 0.1446, Energy: -54.699858-0.003651j
[2025-07-30 10:10:09] [Iter 1954/4650] R3[903/1200], Temp: 0.1437, Energy: -54.629250-0.001427j
[2025-07-30 10:10:19] [Iter 1955/4650] R3[904/1200], Temp: 0.1428, Energy: -54.633222-0.000946j
[2025-07-30 10:10:29] [Iter 1956/4650] R3[905/1200], Temp: 0.1418, Energy: -54.607335+0.001143j
[2025-07-30 10:10:39] [Iter 1957/4650] R3[906/1200], Temp: 0.1409, Energy: -54.627299+0.002558j
[2025-07-30 10:10:49] [Iter 1958/4650] R3[907/1200], Temp: 0.1400, Energy: -54.598868-0.001753j
[2025-07-30 10:10:59] [Iter 1959/4650] R3[908/1200], Temp: 0.1391, Energy: -54.658861-0.001032j
[2025-07-30 10:11:10] [Iter 1960/4650] R3[909/1200], Temp: 0.1382, Energy: -54.666281-0.000707j
[2025-07-30 10:11:20] [Iter 1961/4650] R3[910/1200], Temp: 0.1373, Energy: -54.682327-0.001769j
[2025-07-30 10:11:30] [Iter 1962/4650] R3[911/1200], Temp: 0.1364, Energy: -54.617352+0.001754j
[2025-07-30 10:11:40] [Iter 1963/4650] R3[912/1200], Temp: 0.1355, Energy: -54.637406+0.000735j
[2025-07-30 10:11:50] [Iter 1964/4650] R3[913/1200], Temp: 0.1346, Energy: -54.570578+0.001956j
[2025-07-30 10:12:00] [Iter 1965/4650] R3[914/1200], Temp: 0.1337, Energy: -54.602286-0.000367j
[2025-07-30 10:12:10] [Iter 1966/4650] R3[915/1200], Temp: 0.1328, Energy: -54.585230+0.002041j
[2025-07-30 10:12:20] [Iter 1967/4650] R3[916/1200], Temp: 0.1320, Energy: -54.582625-0.000241j
[2025-07-30 10:12:31] [Iter 1968/4650] R3[917/1200], Temp: 0.1311, Energy: -54.554451-0.000851j
[2025-07-30 10:12:41] [Iter 1969/4650] R3[918/1200], Temp: 0.1302, Energy: -54.571611+0.003242j
[2025-07-30 10:12:51] [Iter 1970/4650] R3[919/1200], Temp: 0.1293, Energy: -54.612703-0.001213j
[2025-07-30 10:13:01] [Iter 1971/4650] R3[920/1200], Temp: 0.1284, Energy: -54.623198-0.000243j
[2025-07-30 10:13:11] [Iter 1972/4650] R3[921/1200], Temp: 0.1276, Energy: -54.577607-0.000277j
[2025-07-30 10:13:21] [Iter 1973/4650] R3[922/1200], Temp: 0.1267, Energy: -54.683570+0.000826j
[2025-07-30 10:13:31] [Iter 1974/4650] R3[923/1200], Temp: 0.1258, Energy: -54.603706-0.002492j
[2025-07-30 10:13:42] [Iter 1975/4650] R3[924/1200], Temp: 0.1249, Energy: -54.648422-0.002686j
[2025-07-30 10:13:52] [Iter 1976/4650] R3[925/1200], Temp: 0.1241, Energy: -54.630529+0.000480j
[2025-07-30 10:14:02] [Iter 1977/4650] R3[926/1200], Temp: 0.1232, Energy: -54.685262-0.000525j
[2025-07-30 10:14:12] [Iter 1978/4650] R3[927/1200], Temp: 0.1224, Energy: -54.708667-0.001204j
[2025-07-30 10:14:22] [Iter 1979/4650] R3[928/1200], Temp: 0.1215, Energy: -54.634025+0.001873j
[2025-07-30 10:14:32] [Iter 1980/4650] R3[929/1200], Temp: 0.1206, Energy: -54.632350+0.000448j
[2025-07-30 10:14:42] [Iter 1981/4650] R3[930/1200], Temp: 0.1198, Energy: -54.612448-0.000364j
[2025-07-30 10:14:52] [Iter 1982/4650] R3[931/1200], Temp: 0.1189, Energy: -54.595890+0.000916j
[2025-07-30 10:15:02] [Iter 1983/4650] R3[932/1200], Temp: 0.1181, Energy: -54.640442-0.000932j
[2025-07-30 10:15:13] [Iter 1984/4650] R3[933/1200], Temp: 0.1173, Energy: -54.592069-0.001961j
[2025-07-30 10:15:23] [Iter 1985/4650] R3[934/1200], Temp: 0.1164, Energy: -54.563240-0.002022j
[2025-07-30 10:15:33] [Iter 1986/4650] R3[935/1200], Temp: 0.1156, Energy: -54.604679-0.002157j
[2025-07-30 10:15:43] [Iter 1987/4650] R3[936/1200], Temp: 0.1147, Energy: -54.609580-0.000216j
[2025-07-30 10:15:53] [Iter 1988/4650] R3[937/1200], Temp: 0.1139, Energy: -54.580436-0.000680j
[2025-07-30 10:16:03] [Iter 1989/4650] R3[938/1200], Temp: 0.1131, Energy: -54.623116-0.004045j
[2025-07-30 10:16:13] [Iter 1990/4650] R3[939/1200], Temp: 0.1123, Energy: -54.588004+0.002024j
[2025-07-30 10:16:23] [Iter 1991/4650] R3[940/1200], Temp: 0.1114, Energy: -54.613490+0.002395j
[2025-07-30 10:16:33] [Iter 1992/4650] R3[941/1200], Temp: 0.1106, Energy: -54.642732-0.001685j
[2025-07-30 10:16:44] [Iter 1993/4650] R3[942/1200], Temp: 0.1098, Energy: -54.613955+0.003439j
[2025-07-30 10:16:54] [Iter 1994/4650] R3[943/1200], Temp: 0.1090, Energy: -54.665466+0.001087j
[2025-07-30 10:17:04] [Iter 1995/4650] R3[944/1200], Temp: 0.1082, Energy: -54.606752+0.002341j
[2025-07-30 10:17:14] [Iter 1996/4650] R3[945/1200], Temp: 0.1073, Energy: -54.641448+0.000478j
[2025-07-30 10:17:24] [Iter 1997/4650] R3[946/1200], Temp: 0.1065, Energy: -54.650354-0.002287j
[2025-07-30 10:17:34] [Iter 1998/4650] R3[947/1200], Temp: 0.1057, Energy: -54.613680+0.001433j
[2025-07-30 10:17:44] [Iter 1999/4650] R3[948/1200], Temp: 0.1049, Energy: -54.575630+0.000407j
[2025-07-30 10:17:54] [Iter 2000/4650] R3[949/1200], Temp: 0.1041, Energy: -54.580393-0.000268j
[2025-07-30 10:17:54] ✓ Checkpoint saved: checkpoint_iter_002000.pkl
[2025-07-30 10:18:05] [Iter 2001/4650] R3[950/1200], Temp: 0.1033, Energy: -54.593955+0.000917j
[2025-07-30 10:18:15] [Iter 2002/4650] R3[951/1200], Temp: 0.1025, Energy: -54.622686+0.001380j
[2025-07-30 10:18:25] [Iter 2003/4650] R3[952/1200], Temp: 0.1017, Energy: -54.638807-0.001289j
[2025-07-30 10:18:35] [Iter 2004/4650] R3[953/1200], Temp: 0.1009, Energy: -54.656074-0.000850j
[2025-07-30 10:18:45] [Iter 2005/4650] R3[954/1200], Temp: 0.1002, Energy: -54.639622+0.001493j
[2025-07-30 10:18:55] [Iter 2006/4650] R3[955/1200], Temp: 0.0994, Energy: -54.681148-0.000762j
[2025-07-30 10:19:05] [Iter 2007/4650] R3[956/1200], Temp: 0.0986, Energy: -54.581496-0.000862j
[2025-07-30 10:19:15] [Iter 2008/4650] R3[957/1200], Temp: 0.0978, Energy: -54.598005-0.000103j
[2025-07-30 10:19:25] [Iter 2009/4650] R3[958/1200], Temp: 0.0970, Energy: -54.653024+0.003141j
[2025-07-30 10:19:36] [Iter 2010/4650] R3[959/1200], Temp: 0.0963, Energy: -54.586575-0.000258j
[2025-07-30 10:19:46] [Iter 2011/4650] R3[960/1200], Temp: 0.0955, Energy: -54.621843-0.000617j
[2025-07-30 10:19:56] [Iter 2012/4650] R3[961/1200], Temp: 0.0947, Energy: -54.556419-0.002392j
[2025-07-30 10:20:06] [Iter 2013/4650] R3[962/1200], Temp: 0.0940, Energy: -54.603291+0.000565j
[2025-07-30 10:20:16] [Iter 2014/4650] R3[963/1200], Temp: 0.0932, Energy: -54.618256-0.001293j
[2025-07-30 10:20:26] [Iter 2015/4650] R3[964/1200], Temp: 0.0924, Energy: -54.564753+0.001068j
[2025-07-30 10:20:36] [Iter 2016/4650] R3[965/1200], Temp: 0.0917, Energy: -54.590611-0.000005j
[2025-07-30 10:20:46] [Iter 2017/4650] R3[966/1200], Temp: 0.0909, Energy: -54.635069+0.000305j
[2025-07-30 10:20:56] [Iter 2018/4650] R3[967/1200], Temp: 0.0902, Energy: -54.587483+0.001015j
[2025-07-30 10:21:07] [Iter 2019/4650] R3[968/1200], Temp: 0.0894, Energy: -54.598688+0.001557j
[2025-07-30 10:21:17] [Iter 2020/4650] R3[969/1200], Temp: 0.0887, Energy: -54.558716-0.001451j
[2025-07-30 10:21:27] [Iter 2021/4650] R3[970/1200], Temp: 0.0879, Energy: -54.651996-0.000284j
[2025-07-30 10:21:37] [Iter 2022/4650] R3[971/1200], Temp: 0.0872, Energy: -54.662094-0.000907j
[2025-07-30 10:21:47] [Iter 2023/4650] R3[972/1200], Temp: 0.0865, Energy: -54.578279-0.000041j
[2025-07-30 10:21:57] [Iter 2024/4650] R3[973/1200], Temp: 0.0857, Energy: -54.570188-0.000006j
[2025-07-30 10:22:07] [Iter 2025/4650] R3[974/1200], Temp: 0.0850, Energy: -54.568707+0.001191j
[2025-07-30 10:22:17] [Iter 2026/4650] R3[975/1200], Temp: 0.0843, Energy: -54.629639-0.002320j
[2025-07-30 10:22:27] [Iter 2027/4650] R3[976/1200], Temp: 0.0835, Energy: -54.652005-0.003263j
[2025-07-30 10:22:38] [Iter 2028/4650] R3[977/1200], Temp: 0.0828, Energy: -54.599167-0.000597j
[2025-07-30 10:22:48] [Iter 2029/4650] R3[978/1200], Temp: 0.0821, Energy: -54.600289-0.003732j
[2025-07-30 10:22:58] [Iter 2030/4650] R3[979/1200], Temp: 0.0814, Energy: -54.644476+0.002500j
[2025-07-30 10:23:08] [Iter 2031/4650] R3[980/1200], Temp: 0.0807, Energy: -54.655303+0.003459j
[2025-07-30 10:23:18] [Iter 2032/4650] R3[981/1200], Temp: 0.0800, Energy: -54.604178-0.000112j
[2025-07-30 10:23:28] [Iter 2033/4650] R3[982/1200], Temp: 0.0792, Energy: -54.661888+0.001028j
[2025-07-30 10:23:38] [Iter 2034/4650] R3[983/1200], Temp: 0.0785, Energy: -54.590133+0.002738j
[2025-07-30 10:23:48] [Iter 2035/4650] R3[984/1200], Temp: 0.0778, Energy: -54.599832+0.000866j
[2025-07-30 10:23:58] [Iter 2036/4650] R3[985/1200], Temp: 0.0771, Energy: -54.569282+0.001378j
[2025-07-30 10:24:09] [Iter 2037/4650] R3[986/1200], Temp: 0.0764, Energy: -54.555866-0.004175j
[2025-07-30 10:24:19] [Iter 2038/4650] R3[987/1200], Temp: 0.0757, Energy: -54.590565+0.000502j
[2025-07-30 10:24:29] [Iter 2039/4650] R3[988/1200], Temp: 0.0751, Energy: -54.588648+0.001702j
[2025-07-30 10:24:39] [Iter 2040/4650] R3[989/1200], Temp: 0.0744, Energy: -54.569541-0.000018j
[2025-07-30 10:24:49] [Iter 2041/4650] R3[990/1200], Temp: 0.0737, Energy: -54.665361-0.000473j
[2025-07-30 10:24:59] [Iter 2042/4650] R3[991/1200], Temp: 0.0730, Energy: -54.628196+0.002839j
[2025-07-30 10:25:09] [Iter 2043/4650] R3[992/1200], Temp: 0.0723, Energy: -54.625199-0.001025j
[2025-07-30 10:25:19] [Iter 2044/4650] R3[993/1200], Temp: 0.0716, Energy: -54.608611-0.002584j
[2025-07-30 10:25:29] [Iter 2045/4650] R3[994/1200], Temp: 0.0710, Energy: -54.673360+0.001740j
[2025-07-30 10:25:40] [Iter 2046/4650] R3[995/1200], Temp: 0.0703, Energy: -54.626228+0.000167j
[2025-07-30 10:25:50] [Iter 2047/4650] R3[996/1200], Temp: 0.0696, Energy: -54.556655+0.003488j
[2025-07-30 10:26:00] [Iter 2048/4650] R3[997/1200], Temp: 0.0690, Energy: -54.534846-0.002279j
[2025-07-30 10:26:10] [Iter 2049/4650] R3[998/1200], Temp: 0.0683, Energy: -54.604713+0.002288j
[2025-07-30 10:26:20] [Iter 2050/4650] R3[999/1200], Temp: 0.0676, Energy: -54.589426-0.001333j
[2025-07-30 10:26:30] [Iter 2051/4650] R3[1000/1200], Temp: 0.0670, Energy: -54.664977+0.001086j
[2025-07-30 10:26:40] [Iter 2052/4650] R3[1001/1200], Temp: 0.0663, Energy: -54.655252-0.001754j
[2025-07-30 10:26:50] [Iter 2053/4650] R3[1002/1200], Temp: 0.0657, Energy: -54.634938+0.002529j
[2025-07-30 10:27:00] [Iter 2054/4650] R3[1003/1200], Temp: 0.0650, Energy: -54.640913+0.001081j
[2025-07-30 10:27:11] [Iter 2055/4650] R3[1004/1200], Temp: 0.0644, Energy: -54.620948+0.003738j
[2025-07-30 10:27:21] [Iter 2056/4650] R3[1005/1200], Temp: 0.0638, Energy: -54.613413+0.001768j
[2025-07-30 10:27:31] [Iter 2057/4650] R3[1006/1200], Temp: 0.0631, Energy: -54.618677-0.001213j
[2025-07-30 10:27:41] [Iter 2058/4650] R3[1007/1200], Temp: 0.0625, Energy: -54.606911+0.000537j
[2025-07-30 10:27:51] [Iter 2059/4650] R3[1008/1200], Temp: 0.0618, Energy: -54.634258-0.001192j
[2025-07-30 10:28:01] [Iter 2060/4650] R3[1009/1200], Temp: 0.0612, Energy: -54.705288-0.001244j
[2025-07-30 10:28:11] [Iter 2061/4650] R3[1010/1200], Temp: 0.0606, Energy: -54.636461+0.001561j
[2025-07-30 10:28:21] [Iter 2062/4650] R3[1011/1200], Temp: 0.0600, Energy: -54.670661+0.000902j
[2025-07-30 10:28:31] [Iter 2063/4650] R3[1012/1200], Temp: 0.0593, Energy: -54.638084-0.001248j
[2025-07-30 10:28:42] [Iter 2064/4650] R3[1013/1200], Temp: 0.0587, Energy: -54.592921-0.000048j
[2025-07-30 10:28:52] [Iter 2065/4650] R3[1014/1200], Temp: 0.0581, Energy: -54.620853-0.000373j
[2025-07-30 10:29:02] [Iter 2066/4650] R3[1015/1200], Temp: 0.0575, Energy: -54.586782+0.000567j
[2025-07-30 10:29:12] [Iter 2067/4650] R3[1016/1200], Temp: 0.0569, Energy: -54.627193+0.001022j
[2025-07-30 10:29:22] [Iter 2068/4650] R3[1017/1200], Temp: 0.0563, Energy: -54.612570-0.001174j
[2025-07-30 10:29:32] [Iter 2069/4650] R3[1018/1200], Temp: 0.0557, Energy: -54.628460+0.000027j
[2025-07-30 10:29:42] [Iter 2070/4650] R3[1019/1200], Temp: 0.0551, Energy: -54.650959+0.002365j
[2025-07-30 10:29:52] [Iter 2071/4650] R3[1020/1200], Temp: 0.0545, Energy: -54.640035+0.001486j
[2025-07-30 10:30:02] [Iter 2072/4650] R3[1021/1200], Temp: 0.0539, Energy: -54.633899+0.001160j
[2025-07-30 10:30:13] [Iter 2073/4650] R3[1022/1200], Temp: 0.0533, Energy: -54.674571+0.000822j
[2025-07-30 10:30:23] [Iter 2074/4650] R3[1023/1200], Temp: 0.0527, Energy: -54.665320+0.001953j
[2025-07-30 10:30:33] [Iter 2075/4650] R3[1024/1200], Temp: 0.0521, Energy: -54.604950+0.001254j
[2025-07-30 10:30:43] [Iter 2076/4650] R3[1025/1200], Temp: 0.0516, Energy: -54.589775-0.001029j
[2025-07-30 10:30:53] [Iter 2077/4650] R3[1026/1200], Temp: 0.0510, Energy: -54.601149-0.000207j
[2025-07-30 10:31:03] [Iter 2078/4650] R3[1027/1200], Temp: 0.0504, Energy: -54.602346+0.000241j
[2025-07-30 10:31:13] [Iter 2079/4650] R3[1028/1200], Temp: 0.0498, Energy: -54.592023+0.001360j
[2025-07-30 10:31:23] [Iter 2080/4650] R3[1029/1200], Temp: 0.0493, Energy: -54.534431+0.000017j
[2025-07-30 10:31:33] [Iter 2081/4650] R3[1030/1200], Temp: 0.0487, Energy: -54.522971+0.000144j
[2025-07-30 10:31:44] [Iter 2082/4650] R3[1031/1200], Temp: 0.0481, Energy: -54.507286-0.003417j
[2025-07-30 10:31:54] [Iter 2083/4650] R3[1032/1200], Temp: 0.0476, Energy: -54.576300-0.003642j
[2025-07-30 10:32:04] [Iter 2084/4650] R3[1033/1200], Temp: 0.0470, Energy: -54.543769+0.000823j
[2025-07-30 10:32:14] [Iter 2085/4650] R3[1034/1200], Temp: 0.0465, Energy: -54.582326+0.000237j
[2025-07-30 10:32:24] [Iter 2086/4650] R3[1035/1200], Temp: 0.0459, Energy: -54.567917+0.002202j
[2025-07-30 10:32:34] [Iter 2087/4650] R3[1036/1200], Temp: 0.0454, Energy: -54.603469-0.000273j
[2025-07-30 10:32:44] [Iter 2088/4650] R3[1037/1200], Temp: 0.0448, Energy: -54.565050+0.000188j
[2025-07-30 10:32:54] [Iter 2089/4650] R3[1038/1200], Temp: 0.0443, Energy: -54.625111-0.001117j
[2025-07-30 10:33:05] [Iter 2090/4650] R3[1039/1200], Temp: 0.0438, Energy: -54.592533+0.002351j
[2025-07-30 10:33:15] [Iter 2091/4650] R3[1040/1200], Temp: 0.0432, Energy: -54.539663+0.000758j
[2025-07-30 10:33:25] [Iter 2092/4650] R3[1041/1200], Temp: 0.0427, Energy: -54.533346-0.001836j
[2025-07-30 10:33:35] [Iter 2093/4650] R3[1042/1200], Temp: 0.0422, Energy: -54.567627-0.000571j
[2025-07-30 10:33:45] [Iter 2094/4650] R3[1043/1200], Temp: 0.0416, Energy: -54.565845-0.000428j
[2025-07-30 10:33:55] [Iter 2095/4650] R3[1044/1200], Temp: 0.0411, Energy: -54.577669-0.000990j
[2025-07-30 10:34:05] [Iter 2096/4650] R3[1045/1200], Temp: 0.0406, Energy: -54.648469-0.001486j
[2025-07-30 10:34:15] [Iter 2097/4650] R3[1046/1200], Temp: 0.0401, Energy: -54.600486+0.002678j
[2025-07-30 10:34:25] [Iter 2098/4650] R3[1047/1200], Temp: 0.0396, Energy: -54.625184-0.001343j
[2025-07-30 10:34:36] [Iter 2099/4650] R3[1048/1200], Temp: 0.0391, Energy: -54.588944+0.000013j
[2025-07-30 10:34:46] [Iter 2100/4650] R3[1049/1200], Temp: 0.0386, Energy: -54.633117-0.000005j
[2025-07-30 10:34:56] [Iter 2101/4650] R3[1050/1200], Temp: 0.0381, Energy: -54.571669-0.001301j
[2025-07-30 10:35:06] [Iter 2102/4650] R3[1051/1200], Temp: 0.0376, Energy: -54.611330-0.000635j
[2025-07-30 10:35:16] [Iter 2103/4650] R3[1052/1200], Temp: 0.0371, Energy: -54.577677+0.000914j
[2025-07-30 10:35:26] [Iter 2104/4650] R3[1053/1200], Temp: 0.0366, Energy: -54.678319+0.002077j
[2025-07-30 10:35:36] [Iter 2105/4650] R3[1054/1200], Temp: 0.0361, Energy: -54.596086+0.002092j
[2025-07-30 10:35:46] [Iter 2106/4650] R3[1055/1200], Temp: 0.0356, Energy: -54.624460-0.001242j
[2025-07-30 10:35:56] [Iter 2107/4650] R3[1056/1200], Temp: 0.0351, Energy: -54.600599+0.000819j
[2025-07-30 10:36:07] [Iter 2108/4650] R3[1057/1200], Temp: 0.0346, Energy: -54.636908-0.000158j
[2025-07-30 10:36:17] [Iter 2109/4650] R3[1058/1200], Temp: 0.0342, Energy: -54.651898+0.001612j
[2025-07-30 10:36:27] [Iter 2110/4650] R3[1059/1200], Temp: 0.0337, Energy: -54.660329+0.002960j
[2025-07-30 10:36:37] [Iter 2111/4650] R3[1060/1200], Temp: 0.0332, Energy: -54.657997+0.000614j
[2025-07-30 10:36:47] [Iter 2112/4650] R3[1061/1200], Temp: 0.0327, Energy: -54.624036-0.000216j
[2025-07-30 10:36:57] [Iter 2113/4650] R3[1062/1200], Temp: 0.0323, Energy: -54.618522+0.002756j
[2025-07-30 10:37:07] [Iter 2114/4650] R3[1063/1200], Temp: 0.0318, Energy: -54.627603-0.001219j
[2025-07-30 10:37:17] [Iter 2115/4650] R3[1064/1200], Temp: 0.0314, Energy: -54.684329+0.002072j
[2025-07-30 10:37:27] [Iter 2116/4650] R3[1065/1200], Temp: 0.0309, Energy: -54.655729+0.000586j
[2025-07-30 10:37:37] [Iter 2117/4650] R3[1066/1200], Temp: 0.0305, Energy: -54.600546-0.001632j
[2025-07-30 10:37:48] [Iter 2118/4650] R3[1067/1200], Temp: 0.0300, Energy: -54.607027-0.002200j
[2025-07-30 10:37:58] [Iter 2119/4650] R3[1068/1200], Temp: 0.0296, Energy: -54.581972-0.000383j
[2025-07-30 10:38:08] [Iter 2120/4650] R3[1069/1200], Temp: 0.0291, Energy: -54.619097-0.003163j
[2025-07-30 10:38:18] [Iter 2121/4650] R3[1070/1200], Temp: 0.0287, Energy: -54.599583+0.001204j
[2025-07-30 10:38:28] [Iter 2122/4650] R3[1071/1200], Temp: 0.0282, Energy: -54.599851-0.001112j
[2025-07-30 10:38:38] [Iter 2123/4650] R3[1072/1200], Temp: 0.0278, Energy: -54.585954+0.000913j
[2025-07-30 10:38:48] [Iter 2124/4650] R3[1073/1200], Temp: 0.0274, Energy: -54.528052+0.001481j
[2025-07-30 10:38:58] [Iter 2125/4650] R3[1074/1200], Temp: 0.0270, Energy: -54.546205+0.000468j
[2025-07-30 10:39:08] [Iter 2126/4650] R3[1075/1200], Temp: 0.0265, Energy: -54.549720-0.001735j
[2025-07-30 10:39:19] [Iter 2127/4650] R3[1076/1200], Temp: 0.0261, Energy: -54.582147-0.005544j
[2025-07-30 10:39:29] [Iter 2128/4650] R3[1077/1200], Temp: 0.0257, Energy: -54.564928+0.003481j
[2025-07-30 10:39:39] [Iter 2129/4650] R3[1078/1200], Temp: 0.0253, Energy: -54.595397+0.000835j
[2025-07-30 10:39:49] [Iter 2130/4650] R3[1079/1200], Temp: 0.0249, Energy: -54.627462-0.000600j
[2025-07-30 10:39:59] [Iter 2131/4650] R3[1080/1200], Temp: 0.0245, Energy: -54.585446+0.000518j
[2025-07-30 10:40:09] [Iter 2132/4650] R3[1081/1200], Temp: 0.0241, Energy: -54.566594+0.000390j
[2025-07-30 10:40:19] [Iter 2133/4650] R3[1082/1200], Temp: 0.0237, Energy: -54.616424-0.002814j
[2025-07-30 10:40:29] [Iter 2134/4650] R3[1083/1200], Temp: 0.0233, Energy: -54.634520-0.001326j
[2025-07-30 10:40:39] [Iter 2135/4650] R3[1084/1200], Temp: 0.0229, Energy: -54.628100-0.000949j
[2025-07-30 10:40:50] [Iter 2136/4650] R3[1085/1200], Temp: 0.0225, Energy: -54.586979-0.001215j
[2025-07-30 10:41:00] [Iter 2137/4650] R3[1086/1200], Temp: 0.0221, Energy: -54.570604+0.000779j
[2025-07-30 10:41:10] [Iter 2138/4650] R3[1087/1200], Temp: 0.0217, Energy: -54.527121-0.000984j
[2025-07-30 10:41:20] [Iter 2139/4650] R3[1088/1200], Temp: 0.0213, Energy: -54.594329+0.000413j
[2025-07-30 10:41:30] [Iter 2140/4650] R3[1089/1200], Temp: 0.0210, Energy: -54.529635+0.001943j
[2025-07-30 10:41:40] [Iter 2141/4650] R3[1090/1200], Temp: 0.0206, Energy: -54.582187+0.001736j
[2025-07-30 10:41:50] [Iter 2142/4650] R3[1091/1200], Temp: 0.0202, Energy: -54.603922+0.000905j
[2025-07-30 10:42:00] [Iter 2143/4650] R3[1092/1200], Temp: 0.0199, Energy: -54.600514+0.001964j
[2025-07-30 10:42:11] [Iter 2144/4650] R3[1093/1200], Temp: 0.0195, Energy: -54.646853+0.000420j
[2025-07-30 10:42:21] [Iter 2145/4650] R3[1094/1200], Temp: 0.0191, Energy: -54.650058+0.000595j
[2025-07-30 10:42:31] [Iter 2146/4650] R3[1095/1200], Temp: 0.0188, Energy: -54.618540+0.001041j
[2025-07-30 10:42:41] [Iter 2147/4650] R3[1096/1200], Temp: 0.0184, Energy: -54.644394+0.000030j
[2025-07-30 10:42:51] [Iter 2148/4650] R3[1097/1200], Temp: 0.0181, Energy: -54.616423+0.000200j
[2025-07-30 10:43:01] [Iter 2149/4650] R3[1098/1200], Temp: 0.0177, Energy: -54.658341-0.000456j
[2025-07-30 10:43:11] [Iter 2150/4650] R3[1099/1200], Temp: 0.0174, Energy: -54.645882+0.004439j
[2025-07-30 10:43:21] [Iter 2151/4650] R3[1100/1200], Temp: 0.0170, Energy: -54.663298+0.000592j
[2025-07-30 10:43:31] [Iter 2152/4650] R3[1101/1200], Temp: 0.0167, Energy: -54.617434+0.001024j
[2025-07-30 10:43:42] [Iter 2153/4650] R3[1102/1200], Temp: 0.0164, Energy: -54.612651+0.004153j
[2025-07-30 10:43:52] [Iter 2154/4650] R3[1103/1200], Temp: 0.0160, Energy: -54.577612+0.000038j
[2025-07-30 10:44:02] [Iter 2155/4650] R3[1104/1200], Temp: 0.0157, Energy: -54.556672+0.000161j
[2025-07-30 10:44:12] [Iter 2156/4650] R3[1105/1200], Temp: 0.0154, Energy: -54.586120-0.000506j
[2025-07-30 10:44:22] [Iter 2157/4650] R3[1106/1200], Temp: 0.0151, Energy: -54.565585-0.001306j
[2025-07-30 10:44:32] [Iter 2158/4650] R3[1107/1200], Temp: 0.0147, Energy: -54.595682-0.001526j
[2025-07-30 10:44:42] [Iter 2159/4650] R3[1108/1200], Temp: 0.0144, Energy: -54.612425+0.000591j
[2025-07-30 10:44:52] [Iter 2160/4650] R3[1109/1200], Temp: 0.0141, Energy: -54.588761+0.001042j
[2025-07-30 10:45:03] [Iter 2161/4650] R3[1110/1200], Temp: 0.0138, Energy: -54.597497+0.000121j
[2025-07-30 10:45:13] [Iter 2162/4650] R3[1111/1200], Temp: 0.0135, Energy: -54.593269+0.000014j
[2025-07-30 10:45:23] [Iter 2163/4650] R3[1112/1200], Temp: 0.0132, Energy: -54.602229+0.003396j
[2025-07-30 10:45:33] [Iter 2164/4650] R3[1113/1200], Temp: 0.0129, Energy: -54.582545-0.002617j
[2025-07-30 10:45:43] [Iter 2165/4650] R3[1114/1200], Temp: 0.0126, Energy: -54.582320+0.001396j
[2025-07-30 10:45:53] [Iter 2166/4650] R3[1115/1200], Temp: 0.0123, Energy: -54.592434+0.000072j
[2025-07-30 10:46:03] [Iter 2167/4650] R3[1116/1200], Temp: 0.0120, Energy: -54.614930-0.000084j
[2025-07-30 10:46:13] [Iter 2168/4650] R3[1117/1200], Temp: 0.0118, Energy: -54.561829-0.000384j
[2025-07-30 10:46:23] [Iter 2169/4650] R3[1118/1200], Temp: 0.0115, Energy: -54.564205-0.001045j
[2025-07-30 10:46:34] [Iter 2170/4650] R3[1119/1200], Temp: 0.0112, Energy: -54.536684+0.001528j
[2025-07-30 10:46:44] [Iter 2171/4650] R3[1120/1200], Temp: 0.0109, Energy: -54.612796-0.000842j
[2025-07-30 10:46:54] [Iter 2172/4650] R3[1121/1200], Temp: 0.0107, Energy: -54.629219-0.001217j
[2025-07-30 10:47:04] [Iter 2173/4650] R3[1122/1200], Temp: 0.0104, Energy: -54.617018-0.001646j
[2025-07-30 10:47:14] [Iter 2174/4650] R3[1123/1200], Temp: 0.0101, Energy: -54.628072-0.002372j
[2025-07-30 10:47:24] [Iter 2175/4650] R3[1124/1200], Temp: 0.0099, Energy: -54.584437-0.001059j
[2025-07-30 10:47:34] [Iter 2176/4650] R3[1125/1200], Temp: 0.0096, Energy: -54.569512+0.001020j
[2025-07-30 10:47:44] [Iter 2177/4650] R3[1126/1200], Temp: 0.0094, Energy: -54.605240-0.000833j
[2025-07-30 10:47:55] [Iter 2178/4650] R3[1127/1200], Temp: 0.0091, Energy: -54.639303+0.001570j
[2025-07-30 10:48:05] [Iter 2179/4650] R3[1128/1200], Temp: 0.0089, Energy: -54.580489+0.002024j
[2025-07-30 10:48:15] [Iter 2180/4650] R3[1129/1200], Temp: 0.0086, Energy: -54.642870+0.002522j
[2025-07-30 10:48:25] [Iter 2181/4650] R3[1130/1200], Temp: 0.0084, Energy: -54.636961-0.001916j
[2025-07-30 10:48:35] [Iter 2182/4650] R3[1131/1200], Temp: 0.0081, Energy: -54.577629-0.002203j
[2025-07-30 10:48:45] [Iter 2183/4650] R3[1132/1200], Temp: 0.0079, Energy: -54.613498+0.001759j
[2025-07-30 10:48:55] [Iter 2184/4650] R3[1133/1200], Temp: 0.0077, Energy: -54.618421-0.001002j
[2025-07-30 10:49:05] [Iter 2185/4650] R3[1134/1200], Temp: 0.0074, Energy: -54.612367+0.000550j
[2025-07-30 10:49:16] [Iter 2186/4650] R3[1135/1200], Temp: 0.0072, Energy: -54.602062-0.000394j
[2025-07-30 10:49:26] [Iter 2187/4650] R3[1136/1200], Temp: 0.0070, Energy: -54.542412-0.000048j
[2025-07-30 10:49:36] [Iter 2188/4650] R3[1137/1200], Temp: 0.0068, Energy: -54.536907+0.000868j
[2025-07-30 10:49:46] [Iter 2189/4650] R3[1138/1200], Temp: 0.0066, Energy: -54.530784-0.002063j
[2025-07-30 10:49:56] [Iter 2190/4650] R3[1139/1200], Temp: 0.0064, Energy: -54.513445+0.001738j
[2025-07-30 10:50:06] [Iter 2191/4650] R3[1140/1200], Temp: 0.0062, Energy: -54.533124+0.001093j
[2025-07-30 10:50:16] [Iter 2192/4650] R3[1141/1200], Temp: 0.0060, Energy: -54.547552-0.004300j
[2025-07-30 10:50:26] [Iter 2193/4650] R3[1142/1200], Temp: 0.0058, Energy: -54.595143+0.001244j
[2025-07-30 10:50:36] [Iter 2194/4650] R3[1143/1200], Temp: 0.0056, Energy: -54.607189-0.001794j
[2025-07-30 10:50:46] [Iter 2195/4650] R3[1144/1200], Temp: 0.0054, Energy: -54.604382-0.001136j
[2025-07-30 10:50:57] [Iter 2196/4650] R3[1145/1200], Temp: 0.0052, Energy: -54.618362+0.002345j
[2025-07-30 10:51:07] [Iter 2197/4650] R3[1146/1200], Temp: 0.0050, Energy: -54.647201+0.000018j
[2025-07-30 10:51:17] [Iter 2198/4650] R3[1147/1200], Temp: 0.0048, Energy: -54.627542-0.000309j
[2025-07-30 10:51:27] [Iter 2199/4650] R3[1148/1200], Temp: 0.0046, Energy: -54.667162-0.002287j
[2025-07-30 10:51:37] [Iter 2200/4650] R3[1149/1200], Temp: 0.0045, Energy: -54.676983+0.000925j
[2025-07-30 10:51:47] [Iter 2201/4650] R3[1150/1200], Temp: 0.0043, Energy: -54.665099+0.000876j
[2025-07-30 10:51:57] [Iter 2202/4650] R3[1151/1200], Temp: 0.0041, Energy: -54.606076-0.001903j
[2025-07-30 10:52:07] [Iter 2203/4650] R3[1152/1200], Temp: 0.0039, Energy: -54.640496-0.000721j
[2025-07-30 10:52:17] [Iter 2204/4650] R3[1153/1200], Temp: 0.0038, Energy: -54.585710+0.001197j
[2025-07-30 10:52:28] [Iter 2205/4650] R3[1154/1200], Temp: 0.0036, Energy: -54.630121-0.002740j
[2025-07-30 10:52:38] [Iter 2206/4650] R3[1155/1200], Temp: 0.0035, Energy: -54.644234-0.000069j
[2025-07-30 10:52:48] [Iter 2207/4650] R3[1156/1200], Temp: 0.0033, Energy: -54.619092+0.001692j
[2025-07-30 10:52:58] [Iter 2208/4650] R3[1157/1200], Temp: 0.0032, Energy: -54.630945-0.000594j
[2025-07-30 10:53:08] [Iter 2209/4650] R3[1158/1200], Temp: 0.0030, Energy: -54.615982-0.000008j
[2025-07-30 10:53:18] [Iter 2210/4650] R3[1159/1200], Temp: 0.0029, Energy: -54.572474-0.001125j
[2025-07-30 10:53:28] [Iter 2211/4650] R3[1160/1200], Temp: 0.0027, Energy: -54.614501+0.000987j
[2025-07-30 10:53:38] [Iter 2212/4650] R3[1161/1200], Temp: 0.0026, Energy: -54.606788+0.001570j
[2025-07-30 10:53:48] [Iter 2213/4650] R3[1162/1200], Temp: 0.0025, Energy: -54.623850+0.000336j
[2025-07-30 10:53:59] [Iter 2214/4650] R3[1163/1200], Temp: 0.0023, Energy: -54.644808-0.002019j
[2025-07-30 10:54:09] [Iter 2215/4650] R3[1164/1200], Temp: 0.0022, Energy: -54.685834-0.000161j
[2025-07-30 10:54:19] [Iter 2216/4650] R3[1165/1200], Temp: 0.0021, Energy: -54.699447+0.001847j
[2025-07-30 10:54:29] [Iter 2217/4650] R3[1166/1200], Temp: 0.0020, Energy: -54.686681+0.000151j
[2025-07-30 10:54:39] [Iter 2218/4650] R3[1167/1200], Temp: 0.0019, Energy: -54.677819-0.002325j
[2025-07-30 10:54:49] [Iter 2219/4650] R3[1168/1200], Temp: 0.0018, Energy: -54.718652+0.002168j
[2025-07-30 10:54:59] [Iter 2220/4650] R3[1169/1200], Temp: 0.0016, Energy: -54.664842+0.002436j
[2025-07-30 10:55:09] [Iter 2221/4650] R3[1170/1200], Temp: 0.0015, Energy: -54.667055-0.000721j
[2025-07-30 10:55:19] [Iter 2222/4650] R3[1171/1200], Temp: 0.0014, Energy: -54.618600+0.000192j
[2025-07-30 10:55:29] [Iter 2223/4650] R3[1172/1200], Temp: 0.0013, Energy: -54.617875+0.001309j
[2025-07-30 10:55:40] [Iter 2224/4650] R3[1173/1200], Temp: 0.0012, Energy: -54.638134+0.000996j
[2025-07-30 10:55:50] [Iter 2225/4650] R3[1174/1200], Temp: 0.0012, Energy: -54.643770-0.000921j
[2025-07-30 10:56:00] [Iter 2226/4650] R3[1175/1200], Temp: 0.0011, Energy: -54.640141+0.000861j
[2025-07-30 10:56:10] [Iter 2227/4650] R3[1176/1200], Temp: 0.0010, Energy: -54.607347-0.000162j
[2025-07-30 10:56:20] [Iter 2228/4650] R3[1177/1200], Temp: 0.0009, Energy: -54.656771-0.001773j
[2025-07-30 10:56:30] [Iter 2229/4650] R3[1178/1200], Temp: 0.0008, Energy: -54.704055-0.000359j
[2025-07-30 10:56:40] [Iter 2230/4650] R3[1179/1200], Temp: 0.0008, Energy: -54.653013-0.000041j
[2025-07-30 10:56:50] [Iter 2231/4650] R3[1180/1200], Temp: 0.0007, Energy: -54.654062+0.001505j
[2025-07-30 10:57:01] [Iter 2232/4650] R3[1181/1200], Temp: 0.0006, Energy: -54.679497-0.002824j
[2025-07-30 10:57:11] [Iter 2233/4650] R3[1182/1200], Temp: 0.0006, Energy: -54.609319+0.000509j
[2025-07-30 10:57:21] [Iter 2234/4650] R3[1183/1200], Temp: 0.0005, Energy: -54.630231+0.000854j
[2025-07-30 10:57:31] [Iter 2235/4650] R3[1184/1200], Temp: 0.0004, Energy: -54.649826-0.001108j
[2025-07-30 10:57:41] [Iter 2236/4650] R3[1185/1200], Temp: 0.0004, Energy: -54.610017-0.001030j
[2025-07-30 10:57:51] [Iter 2237/4650] R3[1186/1200], Temp: 0.0003, Energy: -54.632140-0.000050j
[2025-07-30 10:58:01] [Iter 2238/4650] R3[1187/1200], Temp: 0.0003, Energy: -54.639877-0.002199j
[2025-07-30 10:58:11] [Iter 2239/4650] R3[1188/1200], Temp: 0.0002, Energy: -54.659359-0.000456j
[2025-07-30 10:58:21] [Iter 2240/4650] R3[1189/1200], Temp: 0.0002, Energy: -54.644911+0.001663j
[2025-07-30 10:58:32] [Iter 2241/4650] R3[1190/1200], Temp: 0.0002, Energy: -54.676189-0.000865j
[2025-07-30 10:58:42] [Iter 2242/4650] R3[1191/1200], Temp: 0.0001, Energy: -54.638807+0.002217j
[2025-07-30 10:58:52] [Iter 2243/4650] R3[1192/1200], Temp: 0.0001, Energy: -54.624494+0.000785j
[2025-07-30 10:59:02] [Iter 2244/4650] R3[1193/1200], Temp: 0.0001, Energy: -54.671034-0.000197j
[2025-07-30 10:59:12] [Iter 2245/4650] R3[1194/1200], Temp: 0.0001, Energy: -54.672920+0.001358j
[2025-07-30 10:59:22] [Iter 2246/4650] R3[1195/1200], Temp: 0.0000, Energy: -54.612376-0.002077j
[2025-07-30 10:59:32] [Iter 2247/4650] R3[1196/1200], Temp: 0.0000, Energy: -54.633569-0.002671j
[2025-07-30 10:59:42] [Iter 2248/4650] R3[1197/1200], Temp: 0.0000, Energy: -54.618645+0.001769j
[2025-07-30 10:59:52] [Iter 2249/4650] R3[1198/1200], Temp: 0.0000, Energy: -54.607802+0.000626j
[2025-07-30 11:00:03] [Iter 2250/4650] R3[1199/1200], Temp: 0.0000, Energy: -54.584911-0.000349j
[2025-07-30 11:00:03] RESTART #4 | Period: 2400
[2025-07-30 11:00:13] [Iter 2251/4650] R4[0/2400], Temp: 1.0000, Energy: -54.531126+0.001210j
[2025-07-30 11:00:23] [Iter 2252/4650] R4[1/2400], Temp: 1.0000, Energy: -54.563058+0.001977j
[2025-07-30 11:00:33] [Iter 2253/4650] R4[2/2400], Temp: 1.0000, Energy: -54.618929+0.001008j
[2025-07-30 11:00:43] [Iter 2254/4650] R4[3/2400], Temp: 1.0000, Energy: -54.623173+0.000087j
[2025-07-30 11:00:53] [Iter 2255/4650] R4[4/2400], Temp: 1.0000, Energy: -54.688485+0.000506j
[2025-07-30 11:01:03] [Iter 2256/4650] R4[5/2400], Temp: 1.0000, Energy: -54.629488-0.000275j
[2025-07-30 11:01:13] [Iter 2257/4650] R4[6/2400], Temp: 1.0000, Energy: -54.621731-0.000376j
[2025-07-30 11:01:23] [Iter 2258/4650] R4[7/2400], Temp: 1.0000, Energy: -54.599879+0.001564j
[2025-07-30 11:01:34] [Iter 2259/4650] R4[8/2400], Temp: 1.0000, Energy: -54.624958-0.001124j
[2025-07-30 11:01:44] [Iter 2260/4650] R4[9/2400], Temp: 1.0000, Energy: -54.633578-0.000279j
[2025-07-30 11:01:54] [Iter 2261/4650] R4[10/2400], Temp: 1.0000, Energy: -54.666561-0.000298j
[2025-07-30 11:02:04] [Iter 2262/4650] R4[11/2400], Temp: 0.9999, Energy: -54.639304-0.000267j
[2025-07-30 11:02:14] [Iter 2263/4650] R4[12/2400], Temp: 0.9999, Energy: -54.627173+0.003998j
[2025-07-30 11:02:24] [Iter 2264/4650] R4[13/2400], Temp: 0.9999, Energy: -54.565143-0.000875j
[2025-07-30 11:02:34] [Iter 2265/4650] R4[14/2400], Temp: 0.9999, Energy: -54.568138-0.001079j
[2025-07-30 11:02:45] [Iter 2266/4650] R4[15/2400], Temp: 0.9999, Energy: -54.562845-0.001134j
[2025-07-30 11:02:55] [Iter 2267/4650] R4[16/2400], Temp: 0.9999, Energy: -54.544327-0.001428j
[2025-07-30 11:03:05] [Iter 2268/4650] R4[17/2400], Temp: 0.9999, Energy: -54.542829+0.002380j
[2025-07-30 11:03:15] [Iter 2269/4650] R4[18/2400], Temp: 0.9999, Energy: -54.538579-0.000386j
[2025-07-30 11:03:25] [Iter 2270/4650] R4[19/2400], Temp: 0.9998, Energy: -54.586906+0.000152j
[2025-07-30 11:03:35] [Iter 2271/4650] R4[20/2400], Temp: 0.9998, Energy: -54.610577+0.000545j
[2025-07-30 11:03:45] [Iter 2272/4650] R4[21/2400], Temp: 0.9998, Energy: -54.601111-0.001165j
[2025-07-30 11:03:55] [Iter 2273/4650] R4[22/2400], Temp: 0.9998, Energy: -54.642210-0.000492j
[2025-07-30 11:04:05] [Iter 2274/4650] R4[23/2400], Temp: 0.9998, Energy: -54.621054-0.000169j
[2025-07-30 11:04:16] [Iter 2275/4650] R4[24/2400], Temp: 0.9998, Energy: -54.619304+0.000188j
[2025-07-30 11:04:26] [Iter 2276/4650] R4[25/2400], Temp: 0.9997, Energy: -54.601749-0.001713j
[2025-07-30 11:04:36] [Iter 2277/4650] R4[26/2400], Temp: 0.9997, Energy: -54.587888+0.000244j
[2025-07-30 11:04:46] [Iter 2278/4650] R4[27/2400], Temp: 0.9997, Energy: -54.610359-0.000156j
[2025-07-30 11:04:56] [Iter 2279/4650] R4[28/2400], Temp: 0.9997, Energy: -54.617038+0.001237j
[2025-07-30 11:05:06] [Iter 2280/4650] R4[29/2400], Temp: 0.9996, Energy: -54.616446+0.002894j
[2025-07-30 11:05:16] [Iter 2281/4650] R4[30/2400], Temp: 0.9996, Energy: -54.588125+0.000029j
[2025-07-30 11:05:26] [Iter 2282/4650] R4[31/2400], Temp: 0.9996, Energy: -54.595032-0.000981j
[2025-07-30 11:05:36] [Iter 2283/4650] R4[32/2400], Temp: 0.9996, Energy: -54.618624+0.000653j
[2025-07-30 11:05:47] [Iter 2284/4650] R4[33/2400], Temp: 0.9995, Energy: -54.660993+0.000122j
[2025-07-30 11:05:57] [Iter 2285/4650] R4[34/2400], Temp: 0.9995, Energy: -54.645027+0.001451j
[2025-07-30 11:06:07] [Iter 2286/4650] R4[35/2400], Temp: 0.9995, Energy: -54.628025+0.001500j
[2025-07-30 11:06:17] [Iter 2287/4650] R4[36/2400], Temp: 0.9994, Energy: -54.629854-0.000687j
[2025-07-30 11:06:27] [Iter 2288/4650] R4[37/2400], Temp: 0.9994, Energy: -54.656959+0.000164j
[2025-07-30 11:06:37] [Iter 2289/4650] R4[38/2400], Temp: 0.9994, Energy: -54.677421+0.000064j
[2025-07-30 11:06:47] [Iter 2290/4650] R4[39/2400], Temp: 0.9993, Energy: -54.681014+0.001098j
[2025-07-30 11:06:57] [Iter 2291/4650] R4[40/2400], Temp: 0.9993, Energy: -54.635771+0.001037j
[2025-07-30 11:07:07] [Iter 2292/4650] R4[41/2400], Temp: 0.9993, Energy: -54.572366-0.000403j
[2025-07-30 11:07:18] [Iter 2293/4650] R4[42/2400], Temp: 0.9992, Energy: -54.569130-0.000700j
[2025-07-30 11:07:28] [Iter 2294/4650] R4[43/2400], Temp: 0.9992, Energy: -54.601940+0.001093j
[2025-07-30 11:07:38] [Iter 2295/4650] R4[44/2400], Temp: 0.9992, Energy: -54.575870+0.000304j
[2025-07-30 11:07:48] [Iter 2296/4650] R4[45/2400], Temp: 0.9991, Energy: -54.640485+0.001288j
[2025-07-30 11:07:58] [Iter 2297/4650] R4[46/2400], Temp: 0.9991, Energy: -54.646213-0.000026j
[2025-07-30 11:08:08] [Iter 2298/4650] R4[47/2400], Temp: 0.9991, Energy: -54.635728+0.003082j
[2025-07-30 11:08:18] [Iter 2299/4650] R4[48/2400], Temp: 0.9990, Energy: -54.570346+0.002552j
[2025-07-30 11:08:28] [Iter 2300/4650] R4[49/2400], Temp: 0.9990, Energy: -54.644069-0.001447j
[2025-07-30 11:08:38] [Iter 2301/4650] R4[50/2400], Temp: 0.9989, Energy: -54.599242+0.001306j
[2025-07-30 11:08:49] [Iter 2302/4650] R4[51/2400], Temp: 0.9989, Energy: -54.525553-0.001146j
[2025-07-30 11:08:59] [Iter 2303/4650] R4[52/2400], Temp: 0.9988, Energy: -54.551018-0.000412j
[2025-07-30 11:09:09] [Iter 2304/4650] R4[53/2400], Temp: 0.9988, Energy: -54.584238-0.000761j
[2025-07-30 11:09:19] [Iter 2305/4650] R4[54/2400], Temp: 0.9988, Energy: -54.634553+0.001815j
[2025-07-30 11:09:29] [Iter 2306/4650] R4[55/2400], Temp: 0.9987, Energy: -54.594510+0.000067j
[2025-07-30 11:09:39] [Iter 2307/4650] R4[56/2400], Temp: 0.9987, Energy: -54.625527-0.003338j
[2025-07-30 11:09:49] [Iter 2308/4650] R4[57/2400], Temp: 0.9986, Energy: -54.579329+0.001245j
[2025-07-30 11:09:59] [Iter 2309/4650] R4[58/2400], Temp: 0.9986, Energy: -54.627784-0.000086j
[2025-07-30 11:10:10] [Iter 2310/4650] R4[59/2400], Temp: 0.9985, Energy: -54.594925-0.000373j
[2025-07-30 11:10:20] [Iter 2311/4650] R4[60/2400], Temp: 0.9985, Energy: -54.563364+0.000500j
[2025-07-30 11:10:30] [Iter 2312/4650] R4[61/2400], Temp: 0.9984, Energy: -54.624742+0.002614j
[2025-07-30 11:10:40] [Iter 2313/4650] R4[62/2400], Temp: 0.9984, Energy: -54.660789+0.002773j
[2025-07-30 11:10:50] [Iter 2314/4650] R4[63/2400], Temp: 0.9983, Energy: -54.621798+0.001616j
[2025-07-30 11:11:00] [Iter 2315/4650] R4[64/2400], Temp: 0.9982, Energy: -54.658967-0.001488j
[2025-07-30 11:11:10] [Iter 2316/4650] R4[65/2400], Temp: 0.9982, Energy: -54.641991+0.000474j
[2025-07-30 11:11:20] [Iter 2317/4650] R4[66/2400], Temp: 0.9981, Energy: -54.641929+0.001811j
[2025-07-30 11:11:30] [Iter 2318/4650] R4[67/2400], Temp: 0.9981, Energy: -54.627287-0.000144j
[2025-07-30 11:11:41] [Iter 2319/4650] R4[68/2400], Temp: 0.9980, Energy: -54.615234+0.001438j
[2025-07-30 11:11:51] [Iter 2320/4650] R4[69/2400], Temp: 0.9980, Energy: -54.659965+0.000566j
[2025-07-30 11:12:01] [Iter 2321/4650] R4[70/2400], Temp: 0.9979, Energy: -54.625987-0.001180j
[2025-07-30 11:12:11] [Iter 2322/4650] R4[71/2400], Temp: 0.9978, Energy: -54.603174-0.000310j
[2025-07-30 11:12:21] [Iter 2323/4650] R4[72/2400], Temp: 0.9978, Energy: -54.594814+0.001143j
[2025-07-30 11:12:31] [Iter 2324/4650] R4[73/2400], Temp: 0.9977, Energy: -54.653556-0.001447j
[2025-07-30 11:12:41] [Iter 2325/4650] R4[74/2400], Temp: 0.9977, Energy: -54.605569+0.002480j
[2025-07-30 11:12:51] [Iter 2326/4650] R4[75/2400], Temp: 0.9976, Energy: -54.564737+0.004036j
[2025-07-30 11:13:01] [Iter 2327/4650] R4[76/2400], Temp: 0.9975, Energy: -54.603085+0.000459j
[2025-07-30 11:13:12] [Iter 2328/4650] R4[77/2400], Temp: 0.9975, Energy: -54.620860+0.000917j
[2025-07-30 11:13:22] [Iter 2329/4650] R4[78/2400], Temp: 0.9974, Energy: -54.596900-0.000253j
[2025-07-30 11:13:32] [Iter 2330/4650] R4[79/2400], Temp: 0.9973, Energy: -54.613111+0.001712j
[2025-07-30 11:13:42] [Iter 2331/4650] R4[80/2400], Temp: 0.9973, Energy: -54.632666+0.002260j
[2025-07-30 11:13:52] [Iter 2332/4650] R4[81/2400], Temp: 0.9972, Energy: -54.601884+0.003678j
[2025-07-30 11:14:02] [Iter 2333/4650] R4[82/2400], Temp: 0.9971, Energy: -54.521213-0.000864j
[2025-07-30 11:14:12] [Iter 2334/4650] R4[83/2400], Temp: 0.9971, Energy: -54.589821-0.001685j
[2025-07-30 11:14:22] [Iter 2335/4650] R4[84/2400], Temp: 0.9970, Energy: -54.569957-0.000682j
[2025-07-30 11:14:33] [Iter 2336/4650] R4[85/2400], Temp: 0.9969, Energy: -54.546629+0.000894j
[2025-07-30 11:14:43] [Iter 2337/4650] R4[86/2400], Temp: 0.9968, Energy: -54.530787+0.000249j
[2025-07-30 11:14:53] [Iter 2338/4650] R4[87/2400], Temp: 0.9968, Energy: -54.525108-0.001745j
[2025-07-30 11:15:03] [Iter 2339/4650] R4[88/2400], Temp: 0.9967, Energy: -54.555150-0.000135j
[2025-07-30 11:15:13] [Iter 2340/4650] R4[89/2400], Temp: 0.9966, Energy: -54.605621+0.002548j
[2025-07-30 11:15:23] [Iter 2341/4650] R4[90/2400], Temp: 0.9965, Energy: -54.639125+0.001167j
[2025-07-30 11:15:33] [Iter 2342/4650] R4[91/2400], Temp: 0.9965, Energy: -54.648663+0.001869j
[2025-07-30 11:15:43] [Iter 2343/4650] R4[92/2400], Temp: 0.9964, Energy: -54.608530+0.000281j
[2025-07-30 11:15:53] [Iter 2344/4650] R4[93/2400], Temp: 0.9963, Energy: -54.635183+0.001844j
[2025-07-30 11:16:04] [Iter 2345/4650] R4[94/2400], Temp: 0.9962, Energy: -54.608478-0.003068j
[2025-07-30 11:16:14] [Iter 2346/4650] R4[95/2400], Temp: 0.9961, Energy: -54.645467-0.002338j
[2025-07-30 11:16:24] [Iter 2347/4650] R4[96/2400], Temp: 0.9961, Energy: -54.603960-0.001298j
[2025-07-30 11:16:34] [Iter 2348/4650] R4[97/2400], Temp: 0.9960, Energy: -54.617870-0.001863j
[2025-07-30 11:16:44] [Iter 2349/4650] R4[98/2400], Temp: 0.9959, Energy: -54.629189-0.000172j
[2025-07-30 11:16:54] [Iter 2350/4650] R4[99/2400], Temp: 0.9958, Energy: -54.627321-0.001552j
[2025-07-30 11:17:04] [Iter 2351/4650] R4[100/2400], Temp: 0.9957, Energy: -54.571868+0.001747j
[2025-07-30 11:17:14] [Iter 2352/4650] R4[101/2400], Temp: 0.9956, Energy: -54.629336-0.001789j
[2025-07-30 11:17:25] [Iter 2353/4650] R4[102/2400], Temp: 0.9955, Energy: -54.626648+0.000166j
[2025-07-30 11:17:35] [Iter 2354/4650] R4[103/2400], Temp: 0.9955, Energy: -54.608555-0.002012j
[2025-07-30 11:17:45] [Iter 2355/4650] R4[104/2400], Temp: 0.9954, Energy: -54.629999+0.001636j
[2025-07-30 11:17:55] [Iter 2356/4650] R4[105/2400], Temp: 0.9953, Energy: -54.657257-0.004850j
[2025-07-30 11:18:05] [Iter 2357/4650] R4[106/2400], Temp: 0.9952, Energy: -54.669655+0.000602j
[2025-07-30 11:18:15] [Iter 2358/4650] R4[107/2400], Temp: 0.9951, Energy: -54.659055-0.002171j
[2025-07-30 11:18:25] [Iter 2359/4650] R4[108/2400], Temp: 0.9950, Energy: -54.657596+0.001019j
[2025-07-30 11:18:35] [Iter 2360/4650] R4[109/2400], Temp: 0.9949, Energy: -54.656102+0.001229j
[2025-07-30 11:18:46] [Iter 2361/4650] R4[110/2400], Temp: 0.9948, Energy: -54.680675+0.001647j
[2025-07-30 11:18:56] [Iter 2362/4650] R4[111/2400], Temp: 0.9947, Energy: -54.693926+0.001016j
[2025-07-30 11:19:06] [Iter 2363/4650] R4[112/2400], Temp: 0.9946, Energy: -54.686276-0.000041j
[2025-07-30 11:19:16] [Iter 2364/4650] R4[113/2400], Temp: 0.9945, Energy: -54.643658+0.000919j
[2025-07-30 11:19:26] [Iter 2365/4650] R4[114/2400], Temp: 0.9944, Energy: -54.624369+0.002770j
[2025-07-30 11:19:36] [Iter 2366/4650] R4[115/2400], Temp: 0.9943, Energy: -54.649209+0.001559j
[2025-07-30 11:19:46] [Iter 2367/4650] R4[116/2400], Temp: 0.9942, Energy: -54.643408+0.000704j
[2025-07-30 11:19:56] [Iter 2368/4650] R4[117/2400], Temp: 0.9941, Energy: -54.571290-0.001007j
[2025-07-30 11:20:06] [Iter 2369/4650] R4[118/2400], Temp: 0.9940, Energy: -54.624621-0.001086j
[2025-07-30 11:20:16] [Iter 2370/4650] R4[119/2400], Temp: 0.9939, Energy: -54.577533-0.000058j
[2025-07-30 11:20:27] [Iter 2371/4650] R4[120/2400], Temp: 0.9938, Energy: -54.639606-0.000271j
[2025-07-30 11:20:37] [Iter 2372/4650] R4[121/2400], Temp: 0.9937, Energy: -54.612776-0.000601j
[2025-07-30 11:20:47] [Iter 2373/4650] R4[122/2400], Temp: 0.9936, Energy: -54.651645+0.001177j
[2025-07-30 11:20:57] [Iter 2374/4650] R4[123/2400], Temp: 0.9935, Energy: -54.616754-0.000490j
[2025-07-30 11:21:07] [Iter 2375/4650] R4[124/2400], Temp: 0.9934, Energy: -54.599444+0.000875j
[2025-07-30 11:21:17] [Iter 2376/4650] R4[125/2400], Temp: 0.9933, Energy: -54.638360-0.001201j
[2025-07-30 11:21:27] [Iter 2377/4650] R4[126/2400], Temp: 0.9932, Energy: -54.642937+0.001366j
[2025-07-30 11:21:37] [Iter 2378/4650] R4[127/2400], Temp: 0.9931, Energy: -54.675857-0.001025j
[2025-07-30 11:21:47] [Iter 2379/4650] R4[128/2400], Temp: 0.9930, Energy: -54.683021-0.001107j
[2025-07-30 11:21:58] [Iter 2380/4650] R4[129/2400], Temp: 0.9929, Energy: -54.620791-0.003453j
[2025-07-30 11:22:08] [Iter 2381/4650] R4[130/2400], Temp: 0.9928, Energy: -54.610729-0.000138j
[2025-07-30 11:22:18] [Iter 2382/4650] R4[131/2400], Temp: 0.9927, Energy: -54.585803+0.000728j
[2025-07-30 11:22:28] [Iter 2383/4650] R4[132/2400], Temp: 0.9926, Energy: -54.566179-0.001239j
[2025-07-30 11:22:38] [Iter 2384/4650] R4[133/2400], Temp: 0.9924, Energy: -54.569797-0.000754j
[2025-07-30 11:22:48] [Iter 2385/4650] R4[134/2400], Temp: 0.9923, Energy: -54.621442-0.000772j
[2025-07-30 11:22:58] [Iter 2386/4650] R4[135/2400], Temp: 0.9922, Energy: -54.651140-0.002887j
[2025-07-30 11:23:08] [Iter 2387/4650] R4[136/2400], Temp: 0.9921, Energy: -54.700520-0.000242j
[2025-07-30 11:23:18] [Iter 2388/4650] R4[137/2400], Temp: 0.9920, Energy: -54.675581+0.001902j
[2025-07-30 11:23:29] [Iter 2389/4650] R4[138/2400], Temp: 0.9919, Energy: -54.682258+0.000068j
[2025-07-30 11:23:39] [Iter 2390/4650] R4[139/2400], Temp: 0.9917, Energy: -54.669099-0.000425j
[2025-07-30 11:23:49] [Iter 2391/4650] R4[140/2400], Temp: 0.9916, Energy: -54.634505-0.001605j
[2025-07-30 11:23:59] [Iter 2392/4650] R4[141/2400], Temp: 0.9915, Energy: -54.633183-0.001652j
[2025-07-30 11:24:09] [Iter 2393/4650] R4[142/2400], Temp: 0.9914, Energy: -54.606812+0.002411j
[2025-07-30 11:24:19] [Iter 2394/4650] R4[143/2400], Temp: 0.9913, Energy: -54.613893+0.001349j
[2025-07-30 11:24:29] [Iter 2395/4650] R4[144/2400], Temp: 0.9911, Energy: -54.632182-0.001031j
[2025-07-30 11:24:40] [Iter 2396/4650] R4[145/2400], Temp: 0.9910, Energy: -54.665509+0.000260j
[2025-07-30 11:24:50] [Iter 2397/4650] R4[146/2400], Temp: 0.9909, Energy: -54.656308+0.003512j
[2025-07-30 11:25:00] [Iter 2398/4650] R4[147/2400], Temp: 0.9908, Energy: -54.644178-0.000529j
[2025-07-30 11:25:10] [Iter 2399/4650] R4[148/2400], Temp: 0.9906, Energy: -54.673792-0.003021j
[2025-07-30 11:25:20] [Iter 2400/4650] R4[149/2400], Temp: 0.9905, Energy: -54.688836-0.005041j
[2025-07-30 11:25:30] [Iter 2401/4650] R4[150/2400], Temp: 0.9904, Energy: -54.599360+0.001062j
[2025-07-30 11:25:40] [Iter 2402/4650] R4[151/2400], Temp: 0.9903, Energy: -54.605165+0.000530j
[2025-07-30 11:25:50] [Iter 2403/4650] R4[152/2400], Temp: 0.9901, Energy: -54.571113-0.000556j
[2025-07-30 11:26:00] [Iter 2404/4650] R4[153/2400], Temp: 0.9900, Energy: -54.592256+0.000351j
[2025-07-30 11:26:11] [Iter 2405/4650] R4[154/2400], Temp: 0.9899, Energy: -54.587929+0.000493j
[2025-07-30 11:26:21] [Iter 2406/4650] R4[155/2400], Temp: 0.9897, Energy: -54.625823-0.001494j
[2025-07-30 11:26:31] [Iter 2407/4650] R4[156/2400], Temp: 0.9896, Energy: -54.617490-0.007497j
[2025-07-30 11:26:41] [Iter 2408/4650] R4[157/2400], Temp: 0.9895, Energy: -54.620531+0.000166j
[2025-07-30 11:26:51] [Iter 2409/4650] R4[158/2400], Temp: 0.9893, Energy: -54.600537+0.000726j
[2025-07-30 11:27:01] [Iter 2410/4650] R4[159/2400], Temp: 0.9892, Energy: -54.606359-0.000780j
[2025-07-30 11:27:11] [Iter 2411/4650] R4[160/2400], Temp: 0.9891, Energy: -54.641355-0.000897j
[2025-07-30 11:27:21] [Iter 2412/4650] R4[161/2400], Temp: 0.9889, Energy: -54.593774-0.000806j
[2025-07-30 11:27:31] [Iter 2413/4650] R4[162/2400], Temp: 0.9888, Energy: -54.600379+0.000824j
[2025-07-30 11:27:42] [Iter 2414/4650] R4[163/2400], Temp: 0.9887, Energy: -54.581938-0.002519j
[2025-07-30 11:27:52] [Iter 2415/4650] R4[164/2400], Temp: 0.9885, Energy: -54.646363+0.001091j
[2025-07-30 11:28:02] [Iter 2416/4650] R4[165/2400], Temp: 0.9884, Energy: -54.603641-0.001030j
[2025-07-30 11:28:12] [Iter 2417/4650] R4[166/2400], Temp: 0.9882, Energy: -54.626762+0.002387j
[2025-07-30 11:28:22] [Iter 2418/4650] R4[167/2400], Temp: 0.9881, Energy: -54.623241-0.000236j
[2025-07-30 11:28:32] [Iter 2419/4650] R4[168/2400], Temp: 0.9880, Energy: -54.649451-0.000027j
[2025-07-30 11:28:42] [Iter 2420/4650] R4[169/2400], Temp: 0.9878, Energy: -54.642685-0.000205j
[2025-07-30 11:28:52] [Iter 2421/4650] R4[170/2400], Temp: 0.9877, Energy: -54.650870+0.000959j
[2025-07-30 11:29:02] [Iter 2422/4650] R4[171/2400], Temp: 0.9875, Energy: -54.619432+0.000244j
[2025-07-30 11:29:13] [Iter 2423/4650] R4[172/2400], Temp: 0.9874, Energy: -54.677382-0.001829j
[2025-07-30 11:29:23] [Iter 2424/4650] R4[173/2400], Temp: 0.9872, Energy: -54.690339-0.001162j
[2025-07-30 11:29:33] [Iter 2425/4650] R4[174/2400], Temp: 0.9871, Energy: -54.669807-0.001997j
[2025-07-30 11:29:43] [Iter 2426/4650] R4[175/2400], Temp: 0.9869, Energy: -54.649491-0.000247j
[2025-07-30 11:29:53] [Iter 2427/4650] R4[176/2400], Temp: 0.9868, Energy: -54.656573+0.001532j
[2025-07-30 11:30:03] [Iter 2428/4650] R4[177/2400], Temp: 0.9866, Energy: -54.625179+0.000818j
[2025-07-30 11:30:13] [Iter 2429/4650] R4[178/2400], Temp: 0.9865, Energy: -54.660169-0.000542j
[2025-07-30 11:30:23] [Iter 2430/4650] R4[179/2400], Temp: 0.9863, Energy: -54.666781-0.001147j
[2025-07-30 11:30:34] [Iter 2431/4650] R4[180/2400], Temp: 0.9862, Energy: -54.680685+0.000940j
[2025-07-30 11:30:44] [Iter 2432/4650] R4[181/2400], Temp: 0.9860, Energy: -54.595181-0.002498j
[2025-07-30 11:30:54] [Iter 2433/4650] R4[182/2400], Temp: 0.9859, Energy: -54.645604-0.000888j
[2025-07-30 11:31:04] [Iter 2434/4650] R4[183/2400], Temp: 0.9857, Energy: -54.644778-0.000385j
[2025-07-30 11:31:14] [Iter 2435/4650] R4[184/2400], Temp: 0.9856, Energy: -54.658417-0.001537j
[2025-07-30 11:31:24] [Iter 2436/4650] R4[185/2400], Temp: 0.9854, Energy: -54.664688+0.001171j
[2025-07-30 11:31:34] [Iter 2437/4650] R4[186/2400], Temp: 0.9853, Energy: -54.600519+0.002237j
[2025-07-30 11:31:44] [Iter 2438/4650] R4[187/2400], Temp: 0.9851, Energy: -54.614638-0.000022j
[2025-07-30 11:31:54] [Iter 2439/4650] R4[188/2400], Temp: 0.9849, Energy: -54.598521+0.000823j
[2025-07-30 11:32:05] [Iter 2440/4650] R4[189/2400], Temp: 0.9848, Energy: -54.606133-0.000639j
[2025-07-30 11:32:15] [Iter 2441/4650] R4[190/2400], Temp: 0.9846, Energy: -54.600499-0.001282j
[2025-07-30 11:32:25] [Iter 2442/4650] R4[191/2400], Temp: 0.9845, Energy: -54.572324+0.000540j
[2025-07-30 11:32:35] [Iter 2443/4650] R4[192/2400], Temp: 0.9843, Energy: -54.617247-0.002790j
[2025-07-30 11:32:45] [Iter 2444/4650] R4[193/2400], Temp: 0.9841, Energy: -54.629953-0.001668j
[2025-07-30 11:32:55] [Iter 2445/4650] R4[194/2400], Temp: 0.9840, Energy: -54.607145+0.000011j
[2025-07-30 11:33:05] [Iter 2446/4650] R4[195/2400], Temp: 0.9838, Energy: -54.633950+0.000988j
[2025-07-30 11:33:15] [Iter 2447/4650] R4[196/2400], Temp: 0.9836, Energy: -54.600334-0.003745j
[2025-07-30 11:33:26] [Iter 2448/4650] R4[197/2400], Temp: 0.9835, Energy: -54.656570-0.001457j
[2025-07-30 11:33:36] [Iter 2449/4650] R4[198/2400], Temp: 0.9833, Energy: -54.653744+0.000829j
[2025-07-30 11:33:46] [Iter 2450/4650] R4[199/2400], Temp: 0.9831, Energy: -54.649054-0.003693j
[2025-07-30 11:33:56] [Iter 2451/4650] R4[200/2400], Temp: 0.9830, Energy: -54.602407-0.000997j
[2025-07-30 11:34:06] [Iter 2452/4650] R4[201/2400], Temp: 0.9828, Energy: -54.585088-0.000810j
[2025-07-30 11:34:16] [Iter 2453/4650] R4[202/2400], Temp: 0.9826, Energy: -54.561280-0.001238j
[2025-07-30 11:34:26] [Iter 2454/4650] R4[203/2400], Temp: 0.9825, Energy: -54.634608-0.000764j
[2025-07-30 11:34:36] [Iter 2455/4650] R4[204/2400], Temp: 0.9823, Energy: -54.589198+0.001737j
[2025-07-30 11:34:46] [Iter 2456/4650] R4[205/2400], Temp: 0.9821, Energy: -54.566159-0.001953j
[2025-07-30 11:34:57] [Iter 2457/4650] R4[206/2400], Temp: 0.9819, Energy: -54.590221-0.001927j
[2025-07-30 11:35:07] [Iter 2458/4650] R4[207/2400], Temp: 0.9818, Energy: -54.615490-0.000003j
[2025-07-30 11:35:17] [Iter 2459/4650] R4[208/2400], Temp: 0.9816, Energy: -54.586912+0.001113j
[2025-07-30 11:35:27] [Iter 2460/4650] R4[209/2400], Temp: 0.9814, Energy: -54.584046+0.000230j
[2025-07-30 11:35:37] [Iter 2461/4650] R4[210/2400], Temp: 0.9812, Energy: -54.676231+0.000252j
[2025-07-30 11:35:47] [Iter 2462/4650] R4[211/2400], Temp: 0.9810, Energy: -54.634061-0.000096j
[2025-07-30 11:35:57] [Iter 2463/4650] R4[212/2400], Temp: 0.9809, Energy: -54.717265+0.000536j
[2025-07-30 11:36:07] [Iter 2464/4650] R4[213/2400], Temp: 0.9807, Energy: -54.665295-0.000269j
[2025-07-30 11:36:17] [Iter 2465/4650] R4[214/2400], Temp: 0.9805, Energy: -54.659927+0.001037j
[2025-07-30 11:36:28] [Iter 2466/4650] R4[215/2400], Temp: 0.9803, Energy: -54.641521-0.000367j
[2025-07-30 11:36:38] [Iter 2467/4650] R4[216/2400], Temp: 0.9801, Energy: -54.638408-0.002268j
[2025-07-30 11:36:48] [Iter 2468/4650] R4[217/2400], Temp: 0.9800, Energy: -54.594332-0.002887j
[2025-07-30 11:36:58] [Iter 2469/4650] R4[218/2400], Temp: 0.9798, Energy: -54.641533+0.001292j
[2025-07-30 11:37:08] [Iter 2470/4650] R4[219/2400], Temp: 0.9796, Energy: -54.683940+0.000372j
[2025-07-30 11:37:18] [Iter 2471/4650] R4[220/2400], Temp: 0.9794, Energy: -54.641452+0.000704j
[2025-07-30 11:37:28] [Iter 2472/4650] R4[221/2400], Temp: 0.9792, Energy: -54.667586-0.001758j
[2025-07-30 11:37:38] [Iter 2473/4650] R4[222/2400], Temp: 0.9790, Energy: -54.703170+0.001444j
[2025-07-30 11:37:48] [Iter 2474/4650] R4[223/2400], Temp: 0.9788, Energy: -54.643459-0.001895j
[2025-07-30 11:37:59] [Iter 2475/4650] R4[224/2400], Temp: 0.9787, Energy: -54.659929-0.001167j
[2025-07-30 11:38:09] [Iter 2476/4650] R4[225/2400], Temp: 0.9785, Energy: -54.621747-0.001937j
[2025-07-30 11:38:19] [Iter 2477/4650] R4[226/2400], Temp: 0.9783, Energy: -54.647221-0.003717j
[2025-07-30 11:38:29] [Iter 2478/4650] R4[227/2400], Temp: 0.9781, Energy: -54.647602+0.001300j
[2025-07-30 11:38:39] [Iter 2479/4650] R4[228/2400], Temp: 0.9779, Energy: -54.632560+0.001309j
[2025-07-30 11:38:49] [Iter 2480/4650] R4[229/2400], Temp: 0.9777, Energy: -54.613685-0.000551j
[2025-07-30 11:38:59] [Iter 2481/4650] R4[230/2400], Temp: 0.9775, Energy: -54.676096+0.001237j
[2025-07-30 11:39:10] [Iter 2482/4650] R4[231/2400], Temp: 0.9773, Energy: -54.593992-0.002090j
[2025-07-30 11:39:20] [Iter 2483/4650] R4[232/2400], Temp: 0.9771, Energy: -54.560826-0.000238j
[2025-07-30 11:39:30] [Iter 2484/4650] R4[233/2400], Temp: 0.9769, Energy: -54.568804-0.000210j
[2025-07-30 11:39:40] [Iter 2485/4650] R4[234/2400], Temp: 0.9767, Energy: -54.564787-0.000040j
[2025-07-30 11:39:50] [Iter 2486/4650] R4[235/2400], Temp: 0.9765, Energy: -54.539432-0.004620j
[2025-07-30 11:40:00] [Iter 2487/4650] R4[236/2400], Temp: 0.9763, Energy: -54.542649+0.000122j
[2025-07-30 11:40:10] [Iter 2488/4650] R4[237/2400], Temp: 0.9761, Energy: -54.589633+0.001149j
[2025-07-30 11:40:20] [Iter 2489/4650] R4[238/2400], Temp: 0.9759, Energy: -54.590014+0.001362j
[2025-07-30 11:40:30] [Iter 2490/4650] R4[239/2400], Temp: 0.9757, Energy: -54.586295+0.000143j
[2025-07-30 11:40:41] [Iter 2491/4650] R4[240/2400], Temp: 0.9755, Energy: -54.623547+0.001911j
[2025-07-30 11:40:51] [Iter 2492/4650] R4[241/2400], Temp: 0.9753, Energy: -54.582289-0.001410j
[2025-07-30 11:41:01] [Iter 2493/4650] R4[242/2400], Temp: 0.9751, Energy: -54.503456-0.003310j
[2025-07-30 11:41:11] [Iter 2494/4650] R4[243/2400], Temp: 0.9749, Energy: -54.588257+0.001154j
[2025-07-30 11:41:21] [Iter 2495/4650] R4[244/2400], Temp: 0.9747, Energy: -54.609954+0.000652j
[2025-07-30 11:41:31] [Iter 2496/4650] R4[245/2400], Temp: 0.9745, Energy: -54.634589-0.000923j
[2025-07-30 11:41:41] [Iter 2497/4650] R4[246/2400], Temp: 0.9743, Energy: -54.594284-0.000204j
[2025-07-30 11:41:51] [Iter 2498/4650] R4[247/2400], Temp: 0.9741, Energy: -54.567211+0.002884j
[2025-07-30 11:42:02] [Iter 2499/4650] R4[248/2400], Temp: 0.9739, Energy: -54.600203-0.002452j
[2025-07-30 11:42:12] [Iter 2500/4650] R4[249/2400], Temp: 0.9737, Energy: -54.591014+0.000464j
[2025-07-30 11:42:12] ✓ Checkpoint saved: checkpoint_iter_002500.pkl
[2025-07-30 11:42:22] [Iter 2501/4650] R4[250/2400], Temp: 0.9735, Energy: -54.602069+0.000349j
[2025-07-30 11:42:32] [Iter 2502/4650] R4[251/2400], Temp: 0.9733, Energy: -54.632634+0.002007j
[2025-07-30 11:42:42] [Iter 2503/4650] R4[252/2400], Temp: 0.9730, Energy: -54.644947+0.000557j
[2025-07-30 11:42:52] [Iter 2504/4650] R4[253/2400], Temp: 0.9728, Energy: -54.628938+0.000823j
[2025-07-30 11:43:02] [Iter 2505/4650] R4[254/2400], Temp: 0.9726, Energy: -54.633572+0.001318j
[2025-07-30 11:43:12] [Iter 2506/4650] R4[255/2400], Temp: 0.9724, Energy: -54.613156-0.004906j
[2025-07-30 11:43:22] [Iter 2507/4650] R4[256/2400], Temp: 0.9722, Energy: -54.667555+0.001292j
[2025-07-30 11:43:32] [Iter 2508/4650] R4[257/2400], Temp: 0.9720, Energy: -54.614541-0.000203j
[2025-07-30 11:43:43] [Iter 2509/4650] R4[258/2400], Temp: 0.9718, Energy: -54.622453-0.000827j
[2025-07-30 11:43:53] [Iter 2510/4650] R4[259/2400], Temp: 0.9715, Energy: -54.605701+0.000823j
[2025-07-30 11:44:03] [Iter 2511/4650] R4[260/2400], Temp: 0.9713, Energy: -54.636555+0.003539j
[2025-07-30 11:44:13] [Iter 2512/4650] R4[261/2400], Temp: 0.9711, Energy: -54.674977-0.000451j
[2025-07-30 11:44:23] [Iter 2513/4650] R4[262/2400], Temp: 0.9709, Energy: -54.670842+0.000054j
[2025-07-30 11:44:33] [Iter 2514/4650] R4[263/2400], Temp: 0.9707, Energy: -54.572435+0.002473j
[2025-07-30 11:44:43] [Iter 2515/4650] R4[264/2400], Temp: 0.9704, Energy: -54.603133+0.001641j
[2025-07-30 11:44:53] [Iter 2516/4650] R4[265/2400], Temp: 0.9702, Energy: -54.596802+0.004332j
[2025-07-30 11:45:04] [Iter 2517/4650] R4[266/2400], Temp: 0.9700, Energy: -54.583358+0.002378j
[2025-07-30 11:45:14] [Iter 2518/4650] R4[267/2400], Temp: 0.9698, Energy: -54.652188-0.000237j
[2025-07-30 11:45:24] [Iter 2519/4650] R4[268/2400], Temp: 0.9695, Energy: -54.676152-0.000066j
[2025-07-30 11:45:34] [Iter 2520/4650] R4[269/2400], Temp: 0.9693, Energy: -54.638939-0.000749j
[2025-07-30 11:45:44] [Iter 2521/4650] R4[270/2400], Temp: 0.9691, Energy: -54.654657-0.002706j
[2025-07-30 11:45:54] [Iter 2522/4650] R4[271/2400], Temp: 0.9689, Energy: -54.612486-0.002785j
[2025-07-30 11:46:04] [Iter 2523/4650] R4[272/2400], Temp: 0.9686, Energy: -54.636572-0.004486j
[2025-07-30 11:46:15] [Iter 2524/4650] R4[273/2400], Temp: 0.9684, Energy: -54.579126-0.001789j
[2025-07-30 11:46:25] [Iter 2525/4650] R4[274/2400], Temp: 0.9682, Energy: -54.604680-0.002659j
[2025-07-30 11:46:35] [Iter 2526/4650] R4[275/2400], Temp: 0.9680, Energy: -54.626251-0.003583j
[2025-07-30 11:46:45] [Iter 2527/4650] R4[276/2400], Temp: 0.9677, Energy: -54.582320-0.000960j
[2025-07-30 11:46:55] [Iter 2528/4650] R4[277/2400], Temp: 0.9675, Energy: -54.649206-0.002354j
[2025-07-30 11:47:05] [Iter 2529/4650] R4[278/2400], Temp: 0.9673, Energy: -54.608966+0.000371j
[2025-07-30 11:47:15] [Iter 2530/4650] R4[279/2400], Temp: 0.9670, Energy: -54.593875+0.001106j
[2025-07-30 11:47:25] [Iter 2531/4650] R4[280/2400], Temp: 0.9668, Energy: -54.603184+0.000861j
[2025-07-30 11:47:35] [Iter 2532/4650] R4[281/2400], Temp: 0.9666, Energy: -54.617793-0.001748j
[2025-07-30 11:47:46] [Iter 2533/4650] R4[282/2400], Temp: 0.9663, Energy: -54.637276-0.001471j
[2025-07-30 11:47:56] [Iter 2534/4650] R4[283/2400], Temp: 0.9661, Energy: -54.669189+0.000926j
[2025-07-30 11:48:06] [Iter 2535/4650] R4[284/2400], Temp: 0.9658, Energy: -54.576586-0.000884j
[2025-07-30 11:48:16] [Iter 2536/4650] R4[285/2400], Temp: 0.9656, Energy: -54.598978+0.002408j
[2025-07-30 11:48:26] [Iter 2537/4650] R4[286/2400], Temp: 0.9654, Energy: -54.582432-0.001418j
[2025-07-30 11:48:36] [Iter 2538/4650] R4[287/2400], Temp: 0.9651, Energy: -54.671804-0.001847j
[2025-07-30 11:48:46] [Iter 2539/4650] R4[288/2400], Temp: 0.9649, Energy: -54.673814+0.000632j
[2025-07-30 11:48:56] [Iter 2540/4650] R4[289/2400], Temp: 0.9646, Energy: -54.632891+0.000142j
[2025-07-30 11:49:06] [Iter 2541/4650] R4[290/2400], Temp: 0.9644, Energy: -54.619020-0.003879j
[2025-07-30 11:49:17] [Iter 2542/4650] R4[291/2400], Temp: 0.9642, Energy: -54.639905+0.001286j
[2025-07-30 11:49:27] [Iter 2543/4650] R4[292/2400], Temp: 0.9639, Energy: -54.633166+0.000933j
[2025-07-30 11:49:37] [Iter 2544/4650] R4[293/2400], Temp: 0.9637, Energy: -54.591713-0.001079j
[2025-07-30 11:49:47] [Iter 2545/4650] R4[294/2400], Temp: 0.9634, Energy: -54.580551+0.000756j
[2025-07-30 11:49:57] [Iter 2546/4650] R4[295/2400], Temp: 0.9632, Energy: -54.646666-0.000204j
[2025-07-30 11:50:07] [Iter 2547/4650] R4[296/2400], Temp: 0.9629, Energy: -54.625073-0.000469j
[2025-07-30 11:50:17] [Iter 2548/4650] R4[297/2400], Temp: 0.9627, Energy: -54.587465-0.000428j
[2025-07-30 11:50:27] [Iter 2549/4650] R4[298/2400], Temp: 0.9624, Energy: -54.575201+0.000076j
[2025-07-30 11:50:37] [Iter 2550/4650] R4[299/2400], Temp: 0.9622, Energy: -54.586713-0.000191j
[2025-07-30 11:50:48] [Iter 2551/4650] R4[300/2400], Temp: 0.9619, Energy: -54.638422-0.000550j
[2025-07-30 11:50:58] [Iter 2552/4650] R4[301/2400], Temp: 0.9617, Energy: -54.654879+0.001069j
[2025-07-30 11:51:08] [Iter 2553/4650] R4[302/2400], Temp: 0.9614, Energy: -54.546749-0.001458j
[2025-07-30 11:51:18] [Iter 2554/4650] R4[303/2400], Temp: 0.9612, Energy: -54.577596-0.001143j
[2025-07-30 11:51:28] [Iter 2555/4650] R4[304/2400], Temp: 0.9609, Energy: -54.646889+0.000803j
[2025-07-30 11:51:38] [Iter 2556/4650] R4[305/2400], Temp: 0.9607, Energy: -54.598272-0.000072j
[2025-07-30 11:51:48] [Iter 2557/4650] R4[306/2400], Temp: 0.9604, Energy: -54.645198+0.000669j
[2025-07-30 11:51:58] [Iter 2558/4650] R4[307/2400], Temp: 0.9602, Energy: -54.621292+0.002432j
[2025-07-30 11:52:08] [Iter 2559/4650] R4[308/2400], Temp: 0.9599, Energy: -54.611633+0.000680j
[2025-07-30 11:52:19] [Iter 2560/4650] R4[309/2400], Temp: 0.9597, Energy: -54.597395+0.001451j
[2025-07-30 11:52:29] [Iter 2561/4650] R4[310/2400], Temp: 0.9594, Energy: -54.607738+0.004386j
[2025-07-30 11:52:39] [Iter 2562/4650] R4[311/2400], Temp: 0.9591, Energy: -54.599305+0.000964j
[2025-07-30 11:52:49] [Iter 2563/4650] R4[312/2400], Temp: 0.9589, Energy: -54.629668-0.000641j
[2025-07-30 11:52:59] [Iter 2564/4650] R4[313/2400], Temp: 0.9586, Energy: -54.641743-0.001065j
[2025-07-30 11:53:09] [Iter 2565/4650] R4[314/2400], Temp: 0.9584, Energy: -54.641641-0.000762j
[2025-07-30 11:53:19] [Iter 2566/4650] R4[315/2400], Temp: 0.9581, Energy: -54.694179+0.000825j
[2025-07-30 11:53:30] [Iter 2567/4650] R4[316/2400], Temp: 0.9578, Energy: -54.683260+0.000154j
[2025-07-30 11:53:40] [Iter 2568/4650] R4[317/2400], Temp: 0.9576, Energy: -54.636114-0.000804j
[2025-07-30 11:53:50] [Iter 2569/4650] R4[318/2400], Temp: 0.9573, Energy: -54.660138-0.001290j
[2025-07-30 11:54:00] [Iter 2570/4650] R4[319/2400], Temp: 0.9570, Energy: -54.641375+0.000437j
[2025-07-30 11:54:10] [Iter 2571/4650] R4[320/2400], Temp: 0.9568, Energy: -54.656304-0.001441j
[2025-07-30 11:54:20] [Iter 2572/4650] R4[321/2400], Temp: 0.9565, Energy: -54.611064+0.000518j
[2025-07-30 11:54:30] [Iter 2573/4650] R4[322/2400], Temp: 0.9562, Energy: -54.628946+0.001053j
[2025-07-30 11:54:40] [Iter 2574/4650] R4[323/2400], Temp: 0.9560, Energy: -54.622088+0.002437j
[2025-07-30 11:54:50] [Iter 2575/4650] R4[324/2400], Temp: 0.9557, Energy: -54.598062+0.000784j
[2025-07-30 11:55:01] [Iter 2576/4650] R4[325/2400], Temp: 0.9554, Energy: -54.621888+0.000012j
[2025-07-30 11:55:11] [Iter 2577/4650] R4[326/2400], Temp: 0.9552, Energy: -54.644729-0.003234j
[2025-07-30 11:55:21] [Iter 2578/4650] R4[327/2400], Temp: 0.9549, Energy: -54.613246-0.000331j
[2025-07-30 11:55:31] [Iter 2579/4650] R4[328/2400], Temp: 0.9546, Energy: -54.644254+0.003548j
[2025-07-30 11:55:41] [Iter 2580/4650] R4[329/2400], Temp: 0.9543, Energy: -54.610539+0.000469j
[2025-07-30 11:55:51] [Iter 2581/4650] R4[330/2400], Temp: 0.9541, Energy: -54.605269-0.000221j
[2025-07-30 11:56:01] [Iter 2582/4650] R4[331/2400], Temp: 0.9538, Energy: -54.651143+0.000218j
[2025-07-30 11:56:11] [Iter 2583/4650] R4[332/2400], Temp: 0.9535, Energy: -54.651738-0.000702j
[2025-07-30 11:56:21] [Iter 2584/4650] R4[333/2400], Temp: 0.9532, Energy: -54.645328+0.000406j
[2025-07-30 11:56:32] [Iter 2585/4650] R4[334/2400], Temp: 0.9530, Energy: -54.651122+0.001696j
[2025-07-30 11:56:42] [Iter 2586/4650] R4[335/2400], Temp: 0.9527, Energy: -54.636712+0.000047j
[2025-07-30 11:56:52] [Iter 2587/4650] R4[336/2400], Temp: 0.9524, Energy: -54.569560+0.001342j
[2025-07-30 11:57:02] [Iter 2588/4650] R4[337/2400], Temp: 0.9521, Energy: -54.606815-0.003474j
[2025-07-30 11:57:12] [Iter 2589/4650] R4[338/2400], Temp: 0.9519, Energy: -54.588589-0.002922j
[2025-07-30 11:57:22] [Iter 2590/4650] R4[339/2400], Temp: 0.9516, Energy: -54.649490+0.000542j
[2025-07-30 11:57:32] [Iter 2591/4650] R4[340/2400], Temp: 0.9513, Energy: -54.666628+0.000696j
[2025-07-30 11:57:42] [Iter 2592/4650] R4[341/2400], Temp: 0.9510, Energy: -54.633340+0.002866j
[2025-07-30 11:57:52] [Iter 2593/4650] R4[342/2400], Temp: 0.9507, Energy: -54.608983-0.000657j
[2025-07-30 11:58:03] [Iter 2594/4650] R4[343/2400], Temp: 0.9504, Energy: -54.617421+0.001704j
[2025-07-30 11:58:13] [Iter 2595/4650] R4[344/2400], Temp: 0.9502, Energy: -54.610845-0.001078j
[2025-07-30 11:58:23] [Iter 2596/4650] R4[345/2400], Temp: 0.9499, Energy: -54.645414-0.001861j
[2025-07-30 11:58:33] [Iter 2597/4650] R4[346/2400], Temp: 0.9496, Energy: -54.626353+0.001878j
[2025-07-30 11:58:43] [Iter 2598/4650] R4[347/2400], Temp: 0.9493, Energy: -54.640511-0.000200j
[2025-07-30 11:58:53] [Iter 2599/4650] R4[348/2400], Temp: 0.9490, Energy: -54.543093-0.000498j
[2025-07-30 11:59:03] [Iter 2600/4650] R4[349/2400], Temp: 0.9487, Energy: -54.638537+0.001864j
[2025-07-30 11:59:13] [Iter 2601/4650] R4[350/2400], Temp: 0.9484, Energy: -54.626963+0.000904j
[2025-07-30 11:59:24] [Iter 2602/4650] R4[351/2400], Temp: 0.9481, Energy: -54.629624+0.001165j
[2025-07-30 11:59:34] [Iter 2603/4650] R4[352/2400], Temp: 0.9479, Energy: -54.572480+0.001522j
[2025-07-30 11:59:44] [Iter 2604/4650] R4[353/2400], Temp: 0.9476, Energy: -54.563164+0.002038j
[2025-07-30 11:59:54] [Iter 2605/4650] R4[354/2400], Temp: 0.9473, Energy: -54.524359-0.000798j
[2025-07-30 12:00:04] [Iter 2606/4650] R4[355/2400], Temp: 0.9470, Energy: -54.568351-0.000288j
[2025-07-30 12:00:14] [Iter 2607/4650] R4[356/2400], Temp: 0.9467, Energy: -54.522172-0.004194j
[2025-07-30 12:00:24] [Iter 2608/4650] R4[357/2400], Temp: 0.9464, Energy: -54.543332+0.002057j
[2025-07-30 12:00:34] [Iter 2609/4650] R4[358/2400], Temp: 0.9461, Energy: -54.580772+0.002844j
[2025-07-30 12:00:45] [Iter 2610/4650] R4[359/2400], Temp: 0.9458, Energy: -54.602843-0.000118j
[2025-07-30 12:00:55] [Iter 2611/4650] R4[360/2400], Temp: 0.9455, Energy: -54.589359+0.000029j
[2025-07-30 12:01:05] [Iter 2612/4650] R4[361/2400], Temp: 0.9452, Energy: -54.606369+0.000985j
[2025-07-30 12:01:15] [Iter 2613/4650] R4[362/2400], Temp: 0.9449, Energy: -54.614020-0.000960j
[2025-07-30 12:01:25] [Iter 2614/4650] R4[363/2400], Temp: 0.9446, Energy: -54.635869+0.003487j
[2025-07-30 12:01:35] [Iter 2615/4650] R4[364/2400], Temp: 0.9443, Energy: -54.556539+0.002572j
[2025-07-30 12:01:45] [Iter 2616/4650] R4[365/2400], Temp: 0.9440, Energy: -54.616981+0.000448j
[2025-07-30 12:01:55] [Iter 2617/4650] R4[366/2400], Temp: 0.9437, Energy: -54.638088+0.002825j
[2025-07-30 12:02:05] [Iter 2618/4650] R4[367/2400], Temp: 0.9434, Energy: -54.629310+0.001201j
[2025-07-30 12:02:16] [Iter 2619/4650] R4[368/2400], Temp: 0.9431, Energy: -54.601567-0.001406j
[2025-07-30 12:02:26] [Iter 2620/4650] R4[369/2400], Temp: 0.9428, Energy: -54.666178-0.001639j
[2025-07-30 12:02:36] [Iter 2621/4650] R4[370/2400], Temp: 0.9425, Energy: -54.670124-0.000599j
[2025-07-30 12:02:46] [Iter 2622/4650] R4[371/2400], Temp: 0.9422, Energy: -54.631762-0.000472j
[2025-07-30 12:02:56] [Iter 2623/4650] R4[372/2400], Temp: 0.9419, Energy: -54.615577-0.000177j
[2025-07-30 12:03:06] [Iter 2624/4650] R4[373/2400], Temp: 0.9416, Energy: -54.600012+0.000655j
[2025-07-30 12:03:16] [Iter 2625/4650] R4[374/2400], Temp: 0.9413, Energy: -54.693826+0.000513j
[2025-07-30 12:03:26] [Iter 2626/4650] R4[375/2400], Temp: 0.9410, Energy: -54.673501+0.000669j
[2025-07-30 12:03:36] [Iter 2627/4650] R4[376/2400], Temp: 0.9407, Energy: -54.661741+0.001732j
[2025-07-30 12:03:47] [Iter 2628/4650] R4[377/2400], Temp: 0.9403, Energy: -54.636256-0.000436j
[2025-07-30 12:03:57] [Iter 2629/4650] R4[378/2400], Temp: 0.9400, Energy: -54.644391-0.000149j
[2025-07-30 12:04:07] [Iter 2630/4650] R4[379/2400], Temp: 0.9397, Energy: -54.602313+0.000089j
[2025-07-30 12:04:17] [Iter 2631/4650] R4[380/2400], Temp: 0.9394, Energy: -54.619363-0.000019j
[2025-07-30 12:04:27] [Iter 2632/4650] R4[381/2400], Temp: 0.9391, Energy: -54.624037-0.001632j
[2025-07-30 12:04:37] [Iter 2633/4650] R4[382/2400], Temp: 0.9388, Energy: -54.689222-0.000145j
[2025-07-30 12:04:47] [Iter 2634/4650] R4[383/2400], Temp: 0.9385, Energy: -54.638482+0.000043j
[2025-07-30 12:04:57] [Iter 2635/4650] R4[384/2400], Temp: 0.9382, Energy: -54.692930+0.001865j
[2025-07-30 12:05:07] [Iter 2636/4650] R4[385/2400], Temp: 0.9378, Energy: -54.662614+0.000258j
[2025-07-30 12:05:18] [Iter 2637/4650] R4[386/2400], Temp: 0.9375, Energy: -54.553075+0.003508j
[2025-07-30 12:05:28] [Iter 2638/4650] R4[387/2400], Temp: 0.9372, Energy: -54.621926+0.001876j
[2025-07-30 12:05:38] [Iter 2639/4650] R4[388/2400], Temp: 0.9369, Energy: -54.628318+0.000577j
[2025-07-30 12:05:48] [Iter 2640/4650] R4[389/2400], Temp: 0.9366, Energy: -54.622681-0.002556j
[2025-07-30 12:05:58] [Iter 2641/4650] R4[390/2400], Temp: 0.9362, Energy: -54.599795+0.000077j
[2025-07-30 12:06:08] [Iter 2642/4650] R4[391/2400], Temp: 0.9359, Energy: -54.609612-0.002729j
[2025-07-30 12:06:18] [Iter 2643/4650] R4[392/2400], Temp: 0.9356, Energy: -54.567387+0.000689j
[2025-07-30 12:06:28] [Iter 2644/4650] R4[393/2400], Temp: 0.9353, Energy: -54.614071-0.000133j
[2025-07-30 12:06:38] [Iter 2645/4650] R4[394/2400], Temp: 0.9350, Energy: -54.548532+0.001099j
[2025-07-30 12:06:48] [Iter 2646/4650] R4[395/2400], Temp: 0.9346, Energy: -54.611776-0.000869j
[2025-07-30 12:06:59] [Iter 2647/4650] R4[396/2400], Temp: 0.9343, Energy: -54.633953+0.000295j
[2025-07-30 12:07:09] [Iter 2648/4650] R4[397/2400], Temp: 0.9340, Energy: -54.655223-0.000458j
[2025-07-30 12:07:19] [Iter 2649/4650] R4[398/2400], Temp: 0.9337, Energy: -54.594179-0.000637j
[2025-07-30 12:07:29] [Iter 2650/4650] R4[399/2400], Temp: 0.9333, Energy: -54.683074-0.000662j
[2025-07-30 12:07:39] [Iter 2651/4650] R4[400/2400], Temp: 0.9330, Energy: -54.607852-0.000180j
[2025-07-30 12:07:49] [Iter 2652/4650] R4[401/2400], Temp: 0.9327, Energy: -54.576723-0.002004j
[2025-07-30 12:07:59] [Iter 2653/4650] R4[402/2400], Temp: 0.9324, Energy: -54.618950+0.000848j
[2025-07-30 12:08:10] [Iter 2654/4650] R4[403/2400], Temp: 0.9320, Energy: -54.633948+0.002285j
[2025-07-30 12:08:20] [Iter 2655/4650] R4[404/2400], Temp: 0.9317, Energy: -54.597773-0.000535j
[2025-07-30 12:08:30] [Iter 2656/4650] R4[405/2400], Temp: 0.9314, Energy: -54.565900-0.001931j
[2025-07-30 12:08:40] [Iter 2657/4650] R4[406/2400], Temp: 0.9310, Energy: -54.565028-0.003231j
[2025-07-30 12:08:50] [Iter 2658/4650] R4[407/2400], Temp: 0.9307, Energy: -54.612761-0.000781j
[2025-07-30 12:09:00] [Iter 2659/4650] R4[408/2400], Temp: 0.9304, Energy: -54.620188-0.000662j
[2025-07-30 12:09:10] [Iter 2660/4650] R4[409/2400], Temp: 0.9300, Energy: -54.608359+0.001727j
[2025-07-30 12:09:20] [Iter 2661/4650] R4[410/2400], Temp: 0.9297, Energy: -54.549338+0.001673j
[2025-07-30 12:09:30] [Iter 2662/4650] R4[411/2400], Temp: 0.9294, Energy: -54.603283+0.001422j
[2025-07-30 12:09:41] [Iter 2663/4650] R4[412/2400], Temp: 0.9290, Energy: -54.643322-0.002692j
[2025-07-30 12:09:51] [Iter 2664/4650] R4[413/2400], Temp: 0.9287, Energy: -54.609473+0.001511j
[2025-07-30 12:10:01] [Iter 2665/4650] R4[414/2400], Temp: 0.9284, Energy: -54.635936+0.001277j
[2025-07-30 12:10:11] [Iter 2666/4650] R4[415/2400], Temp: 0.9280, Energy: -54.609983+0.000765j
[2025-07-30 12:10:21] [Iter 2667/4650] R4[416/2400], Temp: 0.9277, Energy: -54.640508+0.000097j
[2025-07-30 12:10:31] [Iter 2668/4650] R4[417/2400], Temp: 0.9273, Energy: -54.596460-0.000566j
[2025-07-30 12:10:41] [Iter 2669/4650] R4[418/2400], Temp: 0.9270, Energy: -54.614448-0.001523j
[2025-07-30 12:10:51] [Iter 2670/4650] R4[419/2400], Temp: 0.9267, Energy: -54.627221+0.001074j
[2025-07-30 12:11:01] [Iter 2671/4650] R4[420/2400], Temp: 0.9263, Energy: -54.567061+0.000630j
[2025-07-30 12:11:12] [Iter 2672/4650] R4[421/2400], Temp: 0.9260, Energy: -54.579244-0.000762j
[2025-07-30 12:11:22] [Iter 2673/4650] R4[422/2400], Temp: 0.9256, Energy: -54.570155+0.003461j
[2025-07-30 12:11:32] [Iter 2674/4650] R4[423/2400], Temp: 0.9253, Energy: -54.548865-0.001626j
[2025-07-30 12:11:42] [Iter 2675/4650] R4[424/2400], Temp: 0.9249, Energy: -54.612465-0.000398j
[2025-07-30 12:11:52] [Iter 2676/4650] R4[425/2400], Temp: 0.9246, Energy: -54.495921-0.000447j
[2025-07-30 12:12:02] [Iter 2677/4650] R4[426/2400], Temp: 0.9243, Energy: -54.558623+0.001487j
[2025-07-30 12:12:12] [Iter 2678/4650] R4[427/2400], Temp: 0.9239, Energy: -54.593290-0.002977j
[2025-07-30 12:12:22] [Iter 2679/4650] R4[428/2400], Temp: 0.9236, Energy: -54.595275-0.000947j
[2025-07-30 12:12:32] [Iter 2680/4650] R4[429/2400], Temp: 0.9232, Energy: -54.569491+0.001181j
[2025-07-30 12:12:43] [Iter 2681/4650] R4[430/2400], Temp: 0.9229, Energy: -54.589544-0.000155j
[2025-07-30 12:12:53] [Iter 2682/4650] R4[431/2400], Temp: 0.9225, Energy: -54.577064-0.000100j
[2025-07-30 12:13:03] [Iter 2683/4650] R4[432/2400], Temp: 0.9222, Energy: -54.597520+0.000476j
[2025-07-30 12:13:13] [Iter 2684/4650] R4[433/2400], Temp: 0.9218, Energy: -54.636458+0.000050j
[2025-07-30 12:13:23] [Iter 2685/4650] R4[434/2400], Temp: 0.9215, Energy: -54.574778-0.001023j
[2025-07-30 12:13:33] [Iter 2686/4650] R4[435/2400], Temp: 0.9211, Energy: -54.618246+0.001332j
[2025-07-30 12:13:43] [Iter 2687/4650] R4[436/2400], Temp: 0.9208, Energy: -54.603343-0.002542j
[2025-07-30 12:13:53] [Iter 2688/4650] R4[437/2400], Temp: 0.9204, Energy: -54.654649+0.001994j
[2025-07-30 12:14:03] [Iter 2689/4650] R4[438/2400], Temp: 0.9200, Energy: -54.619551-0.002498j
[2025-07-30 12:14:14] [Iter 2690/4650] R4[439/2400], Temp: 0.9197, Energy: -54.643992+0.000119j
[2025-07-30 12:14:24] [Iter 2691/4650] R4[440/2400], Temp: 0.9193, Energy: -54.664150-0.002055j
[2025-07-30 12:14:34] [Iter 2692/4650] R4[441/2400], Temp: 0.9190, Energy: -54.627245+0.001177j
[2025-07-30 12:14:44] [Iter 2693/4650] R4[442/2400], Temp: 0.9186, Energy: -54.686235+0.001516j
[2025-07-30 12:14:54] [Iter 2694/4650] R4[443/2400], Temp: 0.9183, Energy: -54.618900-0.002645j
[2025-07-30 12:15:04] [Iter 2695/4650] R4[444/2400], Temp: 0.9179, Energy: -54.632653+0.001003j
[2025-07-30 12:15:14] [Iter 2696/4650] R4[445/2400], Temp: 0.9175, Energy: -54.581555-0.000420j
[2025-07-30 12:15:24] [Iter 2697/4650] R4[446/2400], Temp: 0.9172, Energy: -54.598544+0.004343j
[2025-07-30 12:15:35] [Iter 2698/4650] R4[447/2400], Temp: 0.9168, Energy: -54.629596-0.000952j
[2025-07-30 12:15:45] [Iter 2699/4650] R4[448/2400], Temp: 0.9165, Energy: -54.618880+0.000244j
[2025-07-30 12:15:55] [Iter 2700/4650] R4[449/2400], Temp: 0.9161, Energy: -54.661456+0.001337j
[2025-07-30 12:16:05] [Iter 2701/4650] R4[450/2400], Temp: 0.9157, Energy: -54.726633+0.000512j
[2025-07-30 12:16:15] [Iter 2702/4650] R4[451/2400], Temp: 0.9154, Energy: -54.724559+0.001165j
[2025-07-30 12:16:25] [Iter 2703/4650] R4[452/2400], Temp: 0.9150, Energy: -54.670568-0.001517j
[2025-07-30 12:16:35] [Iter 2704/4650] R4[453/2400], Temp: 0.9146, Energy: -54.741376+0.000109j
[2025-07-30 12:16:45] [Iter 2705/4650] R4[454/2400], Temp: 0.9143, Energy: -54.739832+0.000024j
[2025-07-30 12:16:55] [Iter 2706/4650] R4[455/2400], Temp: 0.9139, Energy: -54.717664+0.000080j
[2025-07-30 12:17:06] [Iter 2707/4650] R4[456/2400], Temp: 0.9135, Energy: -54.699408+0.001907j
[2025-07-30 12:17:16] [Iter 2708/4650] R4[457/2400], Temp: 0.9132, Energy: -54.685625-0.000065j
[2025-07-30 12:17:26] [Iter 2709/4650] R4[458/2400], Temp: 0.9128, Energy: -54.630295-0.001586j
[2025-07-30 12:17:36] [Iter 2710/4650] R4[459/2400], Temp: 0.9124, Energy: -54.631295-0.000932j
[2025-07-30 12:17:46] [Iter 2711/4650] R4[460/2400], Temp: 0.9121, Energy: -54.575627-0.002742j
[2025-07-30 12:17:56] [Iter 2712/4650] R4[461/2400], Temp: 0.9117, Energy: -54.587676+0.000071j
[2025-07-30 12:18:06] [Iter 2713/4650] R4[462/2400], Temp: 0.9113, Energy: -54.579143+0.003058j
[2025-07-30 12:18:16] [Iter 2714/4650] R4[463/2400], Temp: 0.9109, Energy: -54.619752+0.002453j
[2025-07-30 12:18:26] [Iter 2715/4650] R4[464/2400], Temp: 0.9106, Energy: -54.614148-0.002840j
[2025-07-30 12:18:37] [Iter 2716/4650] R4[465/2400], Temp: 0.9102, Energy: -54.582341-0.003503j
[2025-07-30 12:18:47] [Iter 2717/4650] R4[466/2400], Temp: 0.9098, Energy: -54.563135+0.000877j
[2025-07-30 12:18:57] [Iter 2718/4650] R4[467/2400], Temp: 0.9095, Energy: -54.570484+0.002108j
[2025-07-30 12:19:07] [Iter 2719/4650] R4[468/2400], Temp: 0.9091, Energy: -54.573744+0.000767j
[2025-07-30 12:19:17] [Iter 2720/4650] R4[469/2400], Temp: 0.9087, Energy: -54.566271+0.000509j
[2025-07-30 12:19:27] [Iter 2721/4650] R4[470/2400], Temp: 0.9083, Energy: -54.625663+0.002476j
[2025-07-30 12:19:37] [Iter 2722/4650] R4[471/2400], Temp: 0.9079, Energy: -54.657780+0.000022j
[2025-07-30 12:19:47] [Iter 2723/4650] R4[472/2400], Temp: 0.9076, Energy: -54.629941+0.001488j
[2025-07-30 12:19:58] [Iter 2724/4650] R4[473/2400], Temp: 0.9072, Energy: -54.613257-0.001318j
[2025-07-30 12:20:08] [Iter 2725/4650] R4[474/2400], Temp: 0.9068, Energy: -54.626643+0.000155j
[2025-07-30 12:20:18] [Iter 2726/4650] R4[475/2400], Temp: 0.9064, Energy: -54.682964-0.002056j
[2025-07-30 12:20:28] [Iter 2727/4650] R4[476/2400], Temp: 0.9060, Energy: -54.670723-0.002145j
[2025-07-30 12:20:38] [Iter 2728/4650] R4[477/2400], Temp: 0.9057, Energy: -54.708124+0.000161j
[2025-07-30 12:20:48] [Iter 2729/4650] R4[478/2400], Temp: 0.9053, Energy: -54.670292-0.000927j
[2025-07-30 12:20:58] [Iter 2730/4650] R4[479/2400], Temp: 0.9049, Energy: -54.635104-0.000093j
[2025-07-30 12:21:08] [Iter 2731/4650] R4[480/2400], Temp: 0.9045, Energy: -54.694190+0.001384j
[2025-07-30 12:21:18] [Iter 2732/4650] R4[481/2400], Temp: 0.9041, Energy: -54.741346+0.000272j
[2025-07-30 12:21:28] [Iter 2733/4650] R4[482/2400], Temp: 0.9037, Energy: -54.760084-0.000623j
[2025-07-30 12:21:39] [Iter 2734/4650] R4[483/2400], Temp: 0.9034, Energy: -54.699548-0.000288j
[2025-07-30 12:21:49] [Iter 2735/4650] R4[484/2400], Temp: 0.9030, Energy: -54.708875-0.001209j
[2025-07-30 12:21:59] [Iter 2736/4650] R4[485/2400], Temp: 0.9026, Energy: -54.704188+0.000319j
[2025-07-30 12:22:09] [Iter 2737/4650] R4[486/2400], Temp: 0.9022, Energy: -54.708522+0.000489j
[2025-07-30 12:22:19] [Iter 2738/4650] R4[487/2400], Temp: 0.9018, Energy: -54.698981+0.001802j
[2025-07-30 12:22:29] [Iter 2739/4650] R4[488/2400], Temp: 0.9014, Energy: -54.664695+0.003378j
[2025-07-30 12:22:39] [Iter 2740/4650] R4[489/2400], Temp: 0.9010, Energy: -54.691411+0.000427j
[2025-07-30 12:22:49] [Iter 2741/4650] R4[490/2400], Temp: 0.9006, Energy: -54.705806-0.000292j
[2025-07-30 12:22:59] [Iter 2742/4650] R4[491/2400], Temp: 0.9002, Energy: -54.617355-0.001411j
[2025-07-30 12:23:10] [Iter 2743/4650] R4[492/2400], Temp: 0.8998, Energy: -54.650904-0.000395j
[2025-07-30 12:23:20] [Iter 2744/4650] R4[493/2400], Temp: 0.8994, Energy: -54.630214-0.000773j
[2025-07-30 12:23:30] [Iter 2745/4650] R4[494/2400], Temp: 0.8991, Energy: -54.643785+0.003584j
[2025-07-30 12:23:40] [Iter 2746/4650] R4[495/2400], Temp: 0.8987, Energy: -54.689963-0.002190j
[2025-07-30 12:23:50] [Iter 2747/4650] R4[496/2400], Temp: 0.8983, Energy: -54.679631+0.000618j
[2025-07-30 12:24:00] [Iter 2748/4650] R4[497/2400], Temp: 0.8979, Energy: -54.633178+0.002378j
[2025-07-30 12:24:10] [Iter 2749/4650] R4[498/2400], Temp: 0.8975, Energy: -54.583373+0.000546j
[2025-07-30 12:24:20] [Iter 2750/4650] R4[499/2400], Temp: 0.8971, Energy: -54.577971+0.002721j
[2025-07-30 12:24:30] [Iter 2751/4650] R4[500/2400], Temp: 0.8967, Energy: -54.671052+0.000374j
[2025-07-30 12:24:41] [Iter 2752/4650] R4[501/2400], Temp: 0.8963, Energy: -54.613153-0.001462j
[2025-07-30 12:24:51] [Iter 2753/4650] R4[502/2400], Temp: 0.8959, Energy: -54.640578-0.000252j
[2025-07-30 12:25:01] [Iter 2754/4650] R4[503/2400], Temp: 0.8955, Energy: -54.637604-0.000632j
[2025-07-30 12:25:11] [Iter 2755/4650] R4[504/2400], Temp: 0.8951, Energy: -54.661685+0.002502j
[2025-07-30 12:25:21] [Iter 2756/4650] R4[505/2400], Temp: 0.8947, Energy: -54.633927-0.000189j
[2025-07-30 12:25:31] [Iter 2757/4650] R4[506/2400], Temp: 0.8943, Energy: -54.603316+0.000727j
[2025-07-30 12:25:41] [Iter 2758/4650] R4[507/2400], Temp: 0.8939, Energy: -54.644446+0.000543j
[2025-07-30 12:25:51] [Iter 2759/4650] R4[508/2400], Temp: 0.8935, Energy: -54.555862-0.001145j
[2025-07-30 12:26:02] [Iter 2760/4650] R4[509/2400], Temp: 0.8931, Energy: -54.579129+0.000991j
[2025-07-30 12:26:12] [Iter 2761/4650] R4[510/2400], Temp: 0.8927, Energy: -54.539321-0.000625j
[2025-07-30 12:26:22] [Iter 2762/4650] R4[511/2400], Temp: 0.8923, Energy: -54.619768-0.001722j
[2025-07-30 12:26:32] [Iter 2763/4650] R4[512/2400], Temp: 0.8918, Energy: -54.625398-0.000224j
[2025-07-30 12:26:42] [Iter 2764/4650] R4[513/2400], Temp: 0.8914, Energy: -54.656463+0.001784j
[2025-07-30 12:26:52] [Iter 2765/4650] R4[514/2400], Temp: 0.8910, Energy: -54.580976-0.000480j
[2025-07-30 12:27:02] [Iter 2766/4650] R4[515/2400], Temp: 0.8906, Energy: -54.575471-0.001525j
[2025-07-30 12:27:12] [Iter 2767/4650] R4[516/2400], Temp: 0.8902, Energy: -54.570943+0.001035j
[2025-07-30 12:27:22] [Iter 2768/4650] R4[517/2400], Temp: 0.8898, Energy: -54.587866+0.000498j
[2025-07-30 12:27:33] [Iter 2769/4650] R4[518/2400], Temp: 0.8894, Energy: -54.642837-0.000565j
[2025-07-30 12:27:43] [Iter 2770/4650] R4[519/2400], Temp: 0.8890, Energy: -54.607917-0.002774j
[2025-07-30 12:27:53] [Iter 2771/4650] R4[520/2400], Temp: 0.8886, Energy: -54.605337-0.001153j
[2025-07-30 12:28:03] [Iter 2772/4650] R4[521/2400], Temp: 0.8882, Energy: -54.578762+0.003132j
[2025-07-30 12:28:13] [Iter 2773/4650] R4[522/2400], Temp: 0.8877, Energy: -54.599236-0.001096j
[2025-07-30 12:28:23] [Iter 2774/4650] R4[523/2400], Temp: 0.8873, Energy: -54.556438+0.001024j
[2025-07-30 12:28:33] [Iter 2775/4650] R4[524/2400], Temp: 0.8869, Energy: -54.558809+0.000142j
[2025-07-30 12:28:43] [Iter 2776/4650] R4[525/2400], Temp: 0.8865, Energy: -54.490213+0.002097j
[2025-07-30 12:28:53] [Iter 2777/4650] R4[526/2400], Temp: 0.8861, Energy: -54.532748+0.002115j
[2025-07-30 12:29:04] [Iter 2778/4650] R4[527/2400], Temp: 0.8857, Energy: -54.591752+0.001128j
[2025-07-30 12:29:14] [Iter 2779/4650] R4[528/2400], Temp: 0.8853, Energy: -54.546797-0.001191j
[2025-07-30 12:29:24] [Iter 2780/4650] R4[529/2400], Temp: 0.8848, Energy: -54.615615-0.000387j
[2025-07-30 12:29:34] [Iter 2781/4650] R4[530/2400], Temp: 0.8844, Energy: -54.612186+0.000564j
[2025-07-30 12:29:44] [Iter 2782/4650] R4[531/2400], Temp: 0.8840, Energy: -54.537770-0.001050j
[2025-07-30 12:29:54] [Iter 2783/4650] R4[532/2400], Temp: 0.8836, Energy: -54.563790+0.000340j
[2025-07-30 12:30:04] [Iter 2784/4650] R4[533/2400], Temp: 0.8832, Energy: -54.617402-0.000352j
[2025-07-30 12:30:15] [Iter 2785/4650] R4[534/2400], Temp: 0.8827, Energy: -54.602226+0.002005j
[2025-07-30 12:30:25] [Iter 2786/4650] R4[535/2400], Temp: 0.8823, Energy: -54.572710+0.000211j
[2025-07-30 12:30:35] [Iter 2787/4650] R4[536/2400], Temp: 0.8819, Energy: -54.582893+0.000307j
[2025-07-30 12:30:45] [Iter 2788/4650] R4[537/2400], Temp: 0.8815, Energy: -54.630029-0.001595j
[2025-07-30 12:30:55] [Iter 2789/4650] R4[538/2400], Temp: 0.8811, Energy: -54.632483-0.000652j
[2025-07-30 12:31:05] [Iter 2790/4650] R4[539/2400], Temp: 0.8806, Energy: -54.648448+0.000387j
[2025-07-30 12:31:15] [Iter 2791/4650] R4[540/2400], Temp: 0.8802, Energy: -54.610010+0.000567j
[2025-07-30 12:31:25] [Iter 2792/4650] R4[541/2400], Temp: 0.8798, Energy: -54.627121-0.000453j
[2025-07-30 12:31:35] [Iter 2793/4650] R4[542/2400], Temp: 0.8794, Energy: -54.632042+0.000892j
[2025-07-30 12:31:46] [Iter 2794/4650] R4[543/2400], Temp: 0.8789, Energy: -54.643437+0.001270j
[2025-07-30 12:31:56] [Iter 2795/4650] R4[544/2400], Temp: 0.8785, Energy: -54.571661+0.000571j
[2025-07-30 12:32:06] [Iter 2796/4650] R4[545/2400], Temp: 0.8781, Energy: -54.612118-0.000805j
[2025-07-30 12:32:16] [Iter 2797/4650] R4[546/2400], Temp: 0.8776, Energy: -54.584844-0.002388j
[2025-07-30 12:32:26] [Iter 2798/4650] R4[547/2400], Temp: 0.8772, Energy: -54.599305-0.001124j
[2025-07-30 12:32:36] [Iter 2799/4650] R4[548/2400], Temp: 0.8768, Energy: -54.622572-0.000666j
[2025-07-30 12:32:46] [Iter 2800/4650] R4[549/2400], Temp: 0.8764, Energy: -54.647984-0.001730j
[2025-07-30 12:32:56] [Iter 2801/4650] R4[550/2400], Temp: 0.8759, Energy: -54.606136+0.000447j
[2025-07-30 12:33:06] [Iter 2802/4650] R4[551/2400], Temp: 0.8755, Energy: -54.610594+0.000916j
[2025-07-30 12:33:17] [Iter 2803/4650] R4[552/2400], Temp: 0.8751, Energy: -54.595535-0.001098j
[2025-07-30 12:33:27] [Iter 2804/4650] R4[553/2400], Temp: 0.8746, Energy: -54.585704-0.000702j
[2025-07-30 12:33:37] [Iter 2805/4650] R4[554/2400], Temp: 0.8742, Energy: -54.604760-0.001002j
[2025-07-30 12:33:47] [Iter 2806/4650] R4[555/2400], Temp: 0.8738, Energy: -54.581167-0.000704j
[2025-07-30 12:33:57] [Iter 2807/4650] R4[556/2400], Temp: 0.8733, Energy: -54.626502-0.000715j
[2025-07-30 12:34:07] [Iter 2808/4650] R4[557/2400], Temp: 0.8729, Energy: -54.597926+0.000539j
[2025-07-30 12:34:17] [Iter 2809/4650] R4[558/2400], Temp: 0.8724, Energy: -54.595651+0.001766j
[2025-07-30 12:34:27] [Iter 2810/4650] R4[559/2400], Temp: 0.8720, Energy: -54.622591-0.000997j
[2025-07-30 12:34:38] [Iter 2811/4650] R4[560/2400], Temp: 0.8716, Energy: -54.648189+0.001528j
[2025-07-30 12:34:48] [Iter 2812/4650] R4[561/2400], Temp: 0.8711, Energy: -54.607353-0.002955j
[2025-07-30 12:34:58] [Iter 2813/4650] R4[562/2400], Temp: 0.8707, Energy: -54.504667-0.000934j
[2025-07-30 12:35:08] [Iter 2814/4650] R4[563/2400], Temp: 0.8703, Energy: -54.566489-0.001806j
[2025-07-30 12:35:18] [Iter 2815/4650] R4[564/2400], Temp: 0.8698, Energy: -54.600613+0.002022j
[2025-07-30 12:35:28] [Iter 2816/4650] R4[565/2400], Temp: 0.8694, Energy: -54.577611+0.001203j
[2025-07-30 12:35:38] [Iter 2817/4650] R4[566/2400], Temp: 0.8689, Energy: -54.623538+0.001038j
[2025-07-30 12:35:48] [Iter 2818/4650] R4[567/2400], Temp: 0.8685, Energy: -54.620411-0.000373j
[2025-07-30 12:35:59] [Iter 2819/4650] R4[568/2400], Temp: 0.8680, Energy: -54.512793+0.001598j
[2025-07-30 12:36:09] [Iter 2820/4650] R4[569/2400], Temp: 0.8676, Energy: -54.580299-0.001068j
[2025-07-30 12:36:19] [Iter 2821/4650] R4[570/2400], Temp: 0.8672, Energy: -54.630087+0.002094j
[2025-07-30 12:36:29] [Iter 2822/4650] R4[571/2400], Temp: 0.8667, Energy: -54.568327+0.001072j
[2025-07-30 12:36:39] [Iter 2823/4650] R4[572/2400], Temp: 0.8663, Energy: -54.547330-0.001554j
[2025-07-30 12:36:49] [Iter 2824/4650] R4[573/2400], Temp: 0.8658, Energy: -54.557436+0.001933j
[2025-07-30 12:36:59] [Iter 2825/4650] R4[574/2400], Temp: 0.8654, Energy: -54.673727+0.001990j
[2025-07-30 12:37:10] [Iter 2826/4650] R4[575/2400], Temp: 0.8649, Energy: -54.653139+0.000785j
[2025-07-30 12:37:20] [Iter 2827/4650] R4[576/2400], Temp: 0.8645, Energy: -54.548933+0.000003j
[2025-07-30 12:37:30] [Iter 2828/4650] R4[577/2400], Temp: 0.8640, Energy: -54.612078+0.001377j
[2025-07-30 12:37:40] [Iter 2829/4650] R4[578/2400], Temp: 0.8636, Energy: -54.633549+0.000422j
[2025-07-30 12:37:50] [Iter 2830/4650] R4[579/2400], Temp: 0.8631, Energy: -54.624234+0.001574j
[2025-07-30 12:38:00] [Iter 2831/4650] R4[580/2400], Temp: 0.8627, Energy: -54.708040+0.001437j
[2025-07-30 12:38:10] [Iter 2832/4650] R4[581/2400], Temp: 0.8622, Energy: -54.687872+0.000742j
[2025-07-30 12:38:20] [Iter 2833/4650] R4[582/2400], Temp: 0.8618, Energy: -54.626144-0.000244j
[2025-07-30 12:38:30] [Iter 2834/4650] R4[583/2400], Temp: 0.8613, Energy: -54.616911-0.000860j
[2025-07-30 12:38:41] [Iter 2835/4650] R4[584/2400], Temp: 0.8609, Energy: -54.579561-0.000229j
[2025-07-30 12:38:51] [Iter 2836/4650] R4[585/2400], Temp: 0.8604, Energy: -54.591454+0.001242j
[2025-07-30 12:39:01] [Iter 2837/4650] R4[586/2400], Temp: 0.8600, Energy: -54.581254-0.001003j
[2025-07-30 12:39:11] [Iter 2838/4650] R4[587/2400], Temp: 0.8595, Energy: -54.591026-0.001248j
[2025-07-30 12:39:21] [Iter 2839/4650] R4[588/2400], Temp: 0.8591, Energy: -54.535219-0.000573j
[2025-07-30 12:39:31] [Iter 2840/4650] R4[589/2400], Temp: 0.8586, Energy: -54.603436+0.001031j
[2025-07-30 12:39:41] [Iter 2841/4650] R4[590/2400], Temp: 0.8582, Energy: -54.602670-0.000216j
[2025-07-30 12:39:51] [Iter 2842/4650] R4[591/2400], Temp: 0.8577, Energy: -54.649802+0.000157j
[2025-07-30 12:40:01] [Iter 2843/4650] R4[592/2400], Temp: 0.8572, Energy: -54.635457+0.002487j
[2025-07-30 12:40:12] [Iter 2844/4650] R4[593/2400], Temp: 0.8568, Energy: -54.600273+0.002193j
[2025-07-30 12:40:22] [Iter 2845/4650] R4[594/2400], Temp: 0.8563, Energy: -54.622222-0.001289j
[2025-07-30 12:40:32] [Iter 2846/4650] R4[595/2400], Temp: 0.8559, Energy: -54.586019-0.001332j
[2025-07-30 12:40:42] [Iter 2847/4650] R4[596/2400], Temp: 0.8554, Energy: -54.577573-0.004224j
[2025-07-30 12:40:52] [Iter 2848/4650] R4[597/2400], Temp: 0.8549, Energy: -54.582960+0.000437j
[2025-07-30 12:41:02] [Iter 2849/4650] R4[598/2400], Temp: 0.8545, Energy: -54.553577+0.000631j
[2025-07-30 12:41:12] [Iter 2850/4650] R4[599/2400], Temp: 0.8540, Energy: -54.614210-0.000516j
[2025-07-30 12:41:22] [Iter 2851/4650] R4[600/2400], Temp: 0.8536, Energy: -54.690694-0.000317j
[2025-07-30 12:41:33] [Iter 2852/4650] R4[601/2400], Temp: 0.8531, Energy: -54.717763+0.001956j
[2025-07-30 12:41:43] [Iter 2853/4650] R4[602/2400], Temp: 0.8526, Energy: -54.688275-0.000289j
[2025-07-30 12:41:53] [Iter 2854/4650] R4[603/2400], Temp: 0.8522, Energy: -54.648365-0.001554j
[2025-07-30 12:42:03] [Iter 2855/4650] R4[604/2400], Temp: 0.8517, Energy: -54.655803-0.000685j
[2025-07-30 12:42:13] [Iter 2856/4650] R4[605/2400], Temp: 0.8512, Energy: -54.643853+0.001023j
[2025-07-30 12:42:23] [Iter 2857/4650] R4[606/2400], Temp: 0.8508, Energy: -54.678769+0.003079j
[2025-07-30 12:42:33] [Iter 2858/4650] R4[607/2400], Temp: 0.8503, Energy: -54.597246+0.000281j
[2025-07-30 12:42:43] [Iter 2859/4650] R4[608/2400], Temp: 0.8498, Energy: -54.620262-0.002007j
[2025-07-30 12:42:54] [Iter 2860/4650] R4[609/2400], Temp: 0.8494, Energy: -54.605106-0.001404j
[2025-07-30 12:43:04] [Iter 2861/4650] R4[610/2400], Temp: 0.8489, Energy: -54.599271+0.001392j
[2025-07-30 12:43:14] [Iter 2862/4650] R4[611/2400], Temp: 0.8484, Energy: -54.609686+0.001523j
[2025-07-30 12:43:24] [Iter 2863/4650] R4[612/2400], Temp: 0.8480, Energy: -54.645307-0.002057j
[2025-07-30 12:43:34] [Iter 2864/4650] R4[613/2400], Temp: 0.8475, Energy: -54.714906-0.000908j
[2025-07-30 12:43:44] [Iter 2865/4650] R4[614/2400], Temp: 0.8470, Energy: -54.635110-0.000580j
[2025-07-30 12:43:54] [Iter 2866/4650] R4[615/2400], Temp: 0.8465, Energy: -54.650536-0.002040j
[2025-07-30 12:44:04] [Iter 2867/4650] R4[616/2400], Temp: 0.8461, Energy: -54.597850+0.000411j
[2025-07-30 12:44:15] [Iter 2868/4650] R4[617/2400], Temp: 0.8456, Energy: -54.619874-0.000548j
[2025-07-30 12:44:25] [Iter 2869/4650] R4[618/2400], Temp: 0.8451, Energy: -54.580273+0.001842j
[2025-07-30 12:44:35] [Iter 2870/4650] R4[619/2400], Temp: 0.8447, Energy: -54.619954+0.002293j
[2025-07-30 12:44:45] [Iter 2871/4650] R4[620/2400], Temp: 0.8442, Energy: -54.561688+0.001819j
[2025-07-30 12:44:55] [Iter 2872/4650] R4[621/2400], Temp: 0.8437, Energy: -54.559775+0.000833j
[2025-07-30 12:45:05] [Iter 2873/4650] R4[622/2400], Temp: 0.8432, Energy: -54.613382-0.000626j
[2025-07-30 12:45:15] [Iter 2874/4650] R4[623/2400], Temp: 0.8428, Energy: -54.616108+0.001153j
[2025-07-30 12:45:25] [Iter 2875/4650] R4[624/2400], Temp: 0.8423, Energy: -54.552769+0.000852j
[2025-07-30 12:45:35] [Iter 2876/4650] R4[625/2400], Temp: 0.8418, Energy: -54.613783-0.000116j
[2025-07-30 12:45:45] [Iter 2877/4650] R4[626/2400], Temp: 0.8413, Energy: -54.628247-0.000143j
[2025-07-30 12:45:56] [Iter 2878/4650] R4[627/2400], Temp: 0.8408, Energy: -54.644471-0.001802j
[2025-07-30 12:46:06] [Iter 2879/4650] R4[628/2400], Temp: 0.8404, Energy: -54.598923+0.000869j
[2025-07-30 12:46:16] [Iter 2880/4650] R4[629/2400], Temp: 0.8399, Energy: -54.590877+0.002714j
[2025-07-30 12:46:26] [Iter 2881/4650] R4[630/2400], Temp: 0.8394, Energy: -54.616467+0.000424j
[2025-07-30 12:46:36] [Iter 2882/4650] R4[631/2400], Temp: 0.8389, Energy: -54.639785+0.000339j
[2025-07-30 12:46:46] [Iter 2883/4650] R4[632/2400], Temp: 0.8384, Energy: -54.654935-0.002835j
[2025-07-30 12:46:56] [Iter 2884/4650] R4[633/2400], Temp: 0.8380, Energy: -54.633172+0.000232j
[2025-07-30 12:47:06] [Iter 2885/4650] R4[634/2400], Temp: 0.8375, Energy: -54.637264-0.001389j
[2025-07-30 12:47:16] [Iter 2886/4650] R4[635/2400], Temp: 0.8370, Energy: -54.658707+0.000805j
[2025-07-30 12:47:27] [Iter 2887/4650] R4[636/2400], Temp: 0.8365, Energy: -54.639159+0.000593j
[2025-07-30 12:47:37] [Iter 2888/4650] R4[637/2400], Temp: 0.8360, Energy: -54.614319-0.000524j
[2025-07-30 12:47:47] [Iter 2889/4650] R4[638/2400], Temp: 0.8355, Energy: -54.626545+0.001179j
[2025-07-30 12:47:57] [Iter 2890/4650] R4[639/2400], Temp: 0.8351, Energy: -54.627978+0.001720j
[2025-07-30 12:48:07] [Iter 2891/4650] R4[640/2400], Temp: 0.8346, Energy: -54.626322-0.000429j
[2025-07-30 12:48:17] [Iter 2892/4650] R4[641/2400], Temp: 0.8341, Energy: -54.668134+0.000665j
[2025-07-30 12:48:27] [Iter 2893/4650] R4[642/2400], Temp: 0.8336, Energy: -54.640818-0.002034j
[2025-07-30 12:48:37] [Iter 2894/4650] R4[643/2400], Temp: 0.8331, Energy: -54.669043-0.001065j
[2025-07-30 12:48:47] [Iter 2895/4650] R4[644/2400], Temp: 0.8326, Energy: -54.669285-0.001904j
[2025-07-30 12:48:58] [Iter 2896/4650] R4[645/2400], Temp: 0.8321, Energy: -54.615456-0.003182j
[2025-07-30 12:49:08] [Iter 2897/4650] R4[646/2400], Temp: 0.8316, Energy: -54.627768+0.000807j
[2025-07-30 12:49:18] [Iter 2898/4650] R4[647/2400], Temp: 0.8311, Energy: -54.618633-0.001578j
[2025-07-30 12:49:28] [Iter 2899/4650] R4[648/2400], Temp: 0.8307, Energy: -54.632918+0.001177j
[2025-07-30 12:49:38] [Iter 2900/4650] R4[649/2400], Temp: 0.8302, Energy: -54.604725+0.002187j
[2025-07-30 12:49:48] [Iter 2901/4650] R4[650/2400], Temp: 0.8297, Energy: -54.649844+0.001110j
[2025-07-30 12:49:58] [Iter 2902/4650] R4[651/2400], Temp: 0.8292, Energy: -54.555318+0.002337j
[2025-07-30 12:50:08] [Iter 2903/4650] R4[652/2400], Temp: 0.8287, Energy: -54.512692-0.000140j
[2025-07-30 12:50:18] [Iter 2904/4650] R4[653/2400], Temp: 0.8282, Energy: -54.556085+0.000805j
[2025-07-30 12:50:29] [Iter 2905/4650] R4[654/2400], Temp: 0.8277, Energy: -54.580843+0.002428j
[2025-07-30 12:50:39] [Iter 2906/4650] R4[655/2400], Temp: 0.8272, Energy: -54.562080-0.002635j
[2025-07-30 12:50:49] [Iter 2907/4650] R4[656/2400], Temp: 0.8267, Energy: -54.533923-0.000224j
[2025-07-30 12:50:59] [Iter 2908/4650] R4[657/2400], Temp: 0.8262, Energy: -54.515286-0.001270j
[2025-07-30 12:51:09] [Iter 2909/4650] R4[658/2400], Temp: 0.8257, Energy: -54.546466-0.000347j
[2025-07-30 12:51:19] [Iter 2910/4650] R4[659/2400], Temp: 0.8252, Energy: -54.537964+0.000893j
[2025-07-30 12:51:29] [Iter 2911/4650] R4[660/2400], Temp: 0.8247, Energy: -54.549860+0.000711j
[2025-07-30 12:51:40] [Iter 2912/4650] R4[661/2400], Temp: 0.8242, Energy: -54.532973+0.000678j
[2025-07-30 12:51:50] [Iter 2913/4650] R4[662/2400], Temp: 0.8237, Energy: -54.543347-0.002357j
[2025-07-30 12:52:00] [Iter 2914/4650] R4[663/2400], Temp: 0.8232, Energy: -54.543422+0.000081j
[2025-07-30 12:52:10] [Iter 2915/4650] R4[664/2400], Temp: 0.8227, Energy: -54.548842+0.000229j
[2025-07-30 12:52:20] [Iter 2916/4650] R4[665/2400], Temp: 0.8222, Energy: -54.581346-0.001457j
[2025-07-30 12:52:30] [Iter 2917/4650] R4[666/2400], Temp: 0.8217, Energy: -54.570993+0.000779j
[2025-07-30 12:52:40] [Iter 2918/4650] R4[667/2400], Temp: 0.8212, Energy: -54.586381+0.000728j
[2025-07-30 12:52:50] [Iter 2919/4650] R4[668/2400], Temp: 0.8207, Energy: -54.568620+0.001072j
[2025-07-30 12:53:00] [Iter 2920/4650] R4[669/2400], Temp: 0.8202, Energy: -54.562322-0.000427j
[2025-07-30 12:53:11] [Iter 2921/4650] R4[670/2400], Temp: 0.8197, Energy: -54.571630-0.001666j
[2025-07-30 12:53:21] [Iter 2922/4650] R4[671/2400], Temp: 0.8192, Energy: -54.590300+0.000917j
[2025-07-30 12:53:31] [Iter 2923/4650] R4[672/2400], Temp: 0.8187, Energy: -54.601213+0.000790j
[2025-07-30 12:53:41] [Iter 2924/4650] R4[673/2400], Temp: 0.8182, Energy: -54.605951+0.000796j
[2025-07-30 12:53:51] [Iter 2925/4650] R4[674/2400], Temp: 0.8177, Energy: -54.582360-0.001596j
[2025-07-30 12:54:01] [Iter 2926/4650] R4[675/2400], Temp: 0.8172, Energy: -54.635454-0.002424j
[2025-07-30 12:54:11] [Iter 2927/4650] R4[676/2400], Temp: 0.8167, Energy: -54.603938+0.001542j
[2025-07-30 12:54:21] [Iter 2928/4650] R4[677/2400], Temp: 0.8162, Energy: -54.561245-0.000128j
[2025-07-30 12:54:31] [Iter 2929/4650] R4[678/2400], Temp: 0.8157, Energy: -54.580616+0.000408j
[2025-07-30 12:54:42] [Iter 2930/4650] R4[679/2400], Temp: 0.8152, Energy: -54.564954+0.000942j
[2025-07-30 12:54:52] [Iter 2931/4650] R4[680/2400], Temp: 0.8147, Energy: -54.666546+0.000388j
[2025-07-30 12:55:02] [Iter 2932/4650] R4[681/2400], Temp: 0.8142, Energy: -54.648080+0.000393j
[2025-07-30 12:55:12] [Iter 2933/4650] R4[682/2400], Temp: 0.8136, Energy: -54.608022-0.001801j
[2025-07-30 12:55:22] [Iter 2934/4650] R4[683/2400], Temp: 0.8131, Energy: -54.623301+0.000107j
[2025-07-30 12:55:32] [Iter 2935/4650] R4[684/2400], Temp: 0.8126, Energy: -54.642877-0.000924j
[2025-07-30 12:55:42] [Iter 2936/4650] R4[685/2400], Temp: 0.8121, Energy: -54.621365+0.001363j
[2025-07-30 12:55:52] [Iter 2937/4650] R4[686/2400], Temp: 0.8116, Energy: -54.621903-0.000445j
[2025-07-30 12:56:03] [Iter 2938/4650] R4[687/2400], Temp: 0.8111, Energy: -54.573041+0.001572j
[2025-07-30 12:56:13] [Iter 2939/4650] R4[688/2400], Temp: 0.8106, Energy: -54.590991+0.003386j
[2025-07-30 12:56:23] [Iter 2940/4650] R4[689/2400], Temp: 0.8101, Energy: -54.598618+0.003884j
[2025-07-30 12:56:33] [Iter 2941/4650] R4[690/2400], Temp: 0.8095, Energy: -54.557659+0.002374j
[2025-07-30 12:56:43] [Iter 2942/4650] R4[691/2400], Temp: 0.8090, Energy: -54.597719-0.000054j
[2025-07-30 12:56:53] [Iter 2943/4650] R4[692/2400], Temp: 0.8085, Energy: -54.529904+0.001622j
[2025-07-30 12:57:03] [Iter 2944/4650] R4[693/2400], Temp: 0.8080, Energy: -54.531128-0.000797j
[2025-07-30 12:57:13] [Iter 2945/4650] R4[694/2400], Temp: 0.8075, Energy: -54.551995+0.000768j
[2025-07-30 12:57:23] [Iter 2946/4650] R4[695/2400], Temp: 0.8070, Energy: -54.621146-0.000637j
[2025-07-30 12:57:34] [Iter 2947/4650] R4[696/2400], Temp: 0.8065, Energy: -54.605906+0.001114j
[2025-07-30 12:57:44] [Iter 2948/4650] R4[697/2400], Temp: 0.8059, Energy: -54.606495-0.000185j
[2025-07-30 12:57:54] [Iter 2949/4650] R4[698/2400], Temp: 0.8054, Energy: -54.601327-0.001761j
[2025-07-30 12:58:04] [Iter 2950/4650] R4[699/2400], Temp: 0.8049, Energy: -54.637071-0.003717j
[2025-07-30 12:58:14] [Iter 2951/4650] R4[700/2400], Temp: 0.8044, Energy: -54.623359+0.001388j
[2025-07-30 12:58:24] [Iter 2952/4650] R4[701/2400], Temp: 0.8039, Energy: -54.675809-0.000715j
[2025-07-30 12:58:34] [Iter 2953/4650] R4[702/2400], Temp: 0.8033, Energy: -54.686490-0.001289j
[2025-07-30 12:58:45] [Iter 2954/4650] R4[703/2400], Temp: 0.8028, Energy: -54.686421+0.001240j
[2025-07-30 12:58:55] [Iter 2955/4650] R4[704/2400], Temp: 0.8023, Energy: -54.656939+0.001060j
[2025-07-30 12:59:05] [Iter 2956/4650] R4[705/2400], Temp: 0.8018, Energy: -54.743614-0.001220j
[2025-07-30 12:59:15] [Iter 2957/4650] R4[706/2400], Temp: 0.8013, Energy: -54.695940-0.002280j
[2025-07-30 12:59:25] [Iter 2958/4650] R4[707/2400], Temp: 0.8007, Energy: -54.721507+0.001129j
[2025-07-30 12:59:35] [Iter 2959/4650] R4[708/2400], Temp: 0.8002, Energy: -54.712491-0.000215j
[2025-07-30 12:59:45] [Iter 2960/4650] R4[709/2400], Temp: 0.7997, Energy: -54.684630-0.000252j
[2025-07-30 12:59:55] [Iter 2961/4650] R4[710/2400], Temp: 0.7992, Energy: -54.639908+0.000436j
[2025-07-30 13:00:05] [Iter 2962/4650] R4[711/2400], Temp: 0.7986, Energy: -54.710172-0.000851j
[2025-07-30 13:00:16] [Iter 2963/4650] R4[712/2400], Temp: 0.7981, Energy: -54.601905-0.002688j
[2025-07-30 13:00:26] [Iter 2964/4650] R4[713/2400], Temp: 0.7976, Energy: -54.587236+0.000563j
[2025-07-30 13:00:36] [Iter 2965/4650] R4[714/2400], Temp: 0.7971, Energy: -54.592384-0.000159j
[2025-07-30 13:00:46] [Iter 2966/4650] R4[715/2400], Temp: 0.7965, Energy: -54.588764+0.001445j
[2025-07-30 13:00:56] [Iter 2967/4650] R4[716/2400], Temp: 0.7960, Energy: -54.597399+0.001372j
[2025-07-30 13:01:06] [Iter 2968/4650] R4[717/2400], Temp: 0.7955, Energy: -54.620852-0.000713j
[2025-07-30 13:01:16] [Iter 2969/4650] R4[718/2400], Temp: 0.7950, Energy: -54.674794+0.000041j
[2025-07-30 13:01:26] [Iter 2970/4650] R4[719/2400], Temp: 0.7944, Energy: -54.631353+0.002946j
[2025-07-30 13:01:36] [Iter 2971/4650] R4[720/2400], Temp: 0.7939, Energy: -54.645107-0.001221j
[2025-07-30 13:01:47] [Iter 2972/4650] R4[721/2400], Temp: 0.7934, Energy: -54.706996-0.000131j
[2025-07-30 13:01:57] [Iter 2973/4650] R4[722/2400], Temp: 0.7928, Energy: -54.682561-0.000713j
[2025-07-30 13:02:07] [Iter 2974/4650] R4[723/2400], Temp: 0.7923, Energy: -54.590251-0.002609j
[2025-07-30 13:02:17] [Iter 2975/4650] R4[724/2400], Temp: 0.7918, Energy: -54.591290+0.001744j
[2025-07-30 13:02:27] [Iter 2976/4650] R4[725/2400], Temp: 0.7912, Energy: -54.602567-0.002287j
[2025-07-30 13:02:37] [Iter 2977/4650] R4[726/2400], Temp: 0.7907, Energy: -54.630838-0.001643j
[2025-07-30 13:02:47] [Iter 2978/4650] R4[727/2400], Temp: 0.7902, Energy: -54.669745+0.001161j
[2025-07-30 13:02:57] [Iter 2979/4650] R4[728/2400], Temp: 0.7896, Energy: -54.651167-0.000705j
[2025-07-30 13:03:07] [Iter 2980/4650] R4[729/2400], Temp: 0.7891, Energy: -54.668631-0.002475j
[2025-07-30 13:03:18] [Iter 2981/4650] R4[730/2400], Temp: 0.7886, Energy: -54.674710-0.001135j
[2025-07-30 13:03:28] [Iter 2982/4650] R4[731/2400], Temp: 0.7880, Energy: -54.619328-0.000316j
[2025-07-30 13:03:38] [Iter 2983/4650] R4[732/2400], Temp: 0.7875, Energy: -54.652319+0.004213j
[2025-07-30 13:03:48] [Iter 2984/4650] R4[733/2400], Temp: 0.7870, Energy: -54.647709+0.000288j
[2025-07-30 13:03:58] [Iter 2985/4650] R4[734/2400], Temp: 0.7864, Energy: -54.673841-0.001149j
[2025-07-30 13:04:08] [Iter 2986/4650] R4[735/2400], Temp: 0.7859, Energy: -54.691651+0.000339j
[2025-07-30 13:04:18] [Iter 2987/4650] R4[736/2400], Temp: 0.7854, Energy: -54.676208-0.003213j
[2025-07-30 13:04:28] [Iter 2988/4650] R4[737/2400], Temp: 0.7848, Energy: -54.697157+0.000340j
[2025-07-30 13:04:38] [Iter 2989/4650] R4[738/2400], Temp: 0.7843, Energy: -54.646429+0.000070j
[2025-07-30 13:04:49] [Iter 2990/4650] R4[739/2400], Temp: 0.7837, Energy: -54.690632+0.000092j
[2025-07-30 13:04:59] [Iter 2991/4650] R4[740/2400], Temp: 0.7832, Energy: -54.685252+0.000714j
[2025-07-30 13:05:09] [Iter 2992/4650] R4[741/2400], Temp: 0.7827, Energy: -54.630405+0.001880j
[2025-07-30 13:05:19] [Iter 2993/4650] R4[742/2400], Temp: 0.7821, Energy: -54.652789-0.000887j
[2025-07-30 13:05:29] [Iter 2994/4650] R4[743/2400], Temp: 0.7816, Energy: -54.646687-0.000797j
[2025-07-30 13:05:39] [Iter 2995/4650] R4[744/2400], Temp: 0.7810, Energy: -54.570858-0.000726j
[2025-07-30 13:05:49] [Iter 2996/4650] R4[745/2400], Temp: 0.7805, Energy: -54.607652+0.002025j
[2025-07-30 13:05:59] [Iter 2997/4650] R4[746/2400], Temp: 0.7800, Energy: -54.614717+0.001359j
[2025-07-30 13:06:10] [Iter 2998/4650] R4[747/2400], Temp: 0.7794, Energy: -54.615887+0.001287j
[2025-07-30 13:06:20] [Iter 2999/4650] R4[748/2400], Temp: 0.7789, Energy: -54.649468+0.001612j
[2025-07-30 13:06:30] [Iter 3000/4650] R4[749/2400], Temp: 0.7783, Energy: -54.674288+0.000120j
[2025-07-30 13:06:30] ✓ Checkpoint saved: checkpoint_iter_003000.pkl
[2025-07-30 13:06:40] [Iter 3001/4650] R4[750/2400], Temp: 0.7778, Energy: -54.634747+0.000325j
[2025-07-30 13:06:50] [Iter 3002/4650] R4[751/2400], Temp: 0.7772, Energy: -54.591066-0.000915j
[2025-07-30 13:07:00] [Iter 3003/4650] R4[752/2400], Temp: 0.7767, Energy: -54.666047+0.001847j
[2025-07-30 13:07:10] [Iter 3004/4650] R4[753/2400], Temp: 0.7762, Energy: -54.668239+0.000139j
[2025-07-30 13:07:20] [Iter 3005/4650] R4[754/2400], Temp: 0.7756, Energy: -54.601237-0.002811j
[2025-07-30 13:07:30] [Iter 3006/4650] R4[755/2400], Temp: 0.7751, Energy: -54.622730+0.000702j
[2025-07-30 13:07:41] [Iter 3007/4650] R4[756/2400], Temp: 0.7745, Energy: -54.614288-0.000017j
[2025-07-30 13:07:51] [Iter 3008/4650] R4[757/2400], Temp: 0.7740, Energy: -54.659733-0.003340j
[2025-07-30 13:08:01] [Iter 3009/4650] R4[758/2400], Temp: 0.7734, Energy: -54.645360-0.001770j
[2025-07-30 13:08:11] [Iter 3010/4650] R4[759/2400], Temp: 0.7729, Energy: -54.616965-0.000445j
[2025-07-30 13:08:21] [Iter 3011/4650] R4[760/2400], Temp: 0.7723, Energy: -54.611392+0.000010j
[2025-07-30 13:08:31] [Iter 3012/4650] R4[761/2400], Temp: 0.7718, Energy: -54.635067+0.001953j
[2025-07-30 13:08:41] [Iter 3013/4650] R4[762/2400], Temp: 0.7712, Energy: -54.613591+0.000969j
[2025-07-30 13:08:51] [Iter 3014/4650] R4[763/2400], Temp: 0.7707, Energy: -54.669935+0.001149j
[2025-07-30 13:09:02] [Iter 3015/4650] R4[764/2400], Temp: 0.7701, Energy: -54.658578+0.000841j
[2025-07-30 13:09:12] [Iter 3016/4650] R4[765/2400], Temp: 0.7696, Energy: -54.665396+0.001180j
[2025-07-30 13:09:22] [Iter 3017/4650] R4[766/2400], Temp: 0.7690, Energy: -54.670552-0.000702j
[2025-07-30 13:09:32] [Iter 3018/4650] R4[767/2400], Temp: 0.7685, Energy: -54.597925+0.000733j
[2025-07-30 13:09:42] [Iter 3019/4650] R4[768/2400], Temp: 0.7679, Energy: -54.586282+0.000768j
[2025-07-30 13:09:52] [Iter 3020/4650] R4[769/2400], Temp: 0.7674, Energy: -54.619153+0.001576j
[2025-07-30 13:10:02] [Iter 3021/4650] R4[770/2400], Temp: 0.7668, Energy: -54.585369+0.001593j
[2025-07-30 13:10:12] [Iter 3022/4650] R4[771/2400], Temp: 0.7663, Energy: -54.609612-0.001185j
[2025-07-30 13:10:22] [Iter 3023/4650] R4[772/2400], Temp: 0.7657, Energy: -54.674402+0.001923j
[2025-07-30 13:10:33] [Iter 3024/4650] R4[773/2400], Temp: 0.7651, Energy: -54.712104+0.000094j
[2025-07-30 13:10:43] [Iter 3025/4650] R4[774/2400], Temp: 0.7646, Energy: -54.739205-0.000569j
[2025-07-30 13:10:53] [Iter 3026/4650] R4[775/2400], Temp: 0.7640, Energy: -54.681904+0.002053j
[2025-07-30 13:11:03] [Iter 3027/4650] R4[776/2400], Temp: 0.7635, Energy: -54.632132+0.002490j
[2025-07-30 13:11:13] [Iter 3028/4650] R4[777/2400], Temp: 0.7629, Energy: -54.608112+0.000308j
[2025-07-30 13:11:23] [Iter 3029/4650] R4[778/2400], Temp: 0.7624, Energy: -54.608416-0.002240j
[2025-07-30 13:11:33] [Iter 3030/4650] R4[779/2400], Temp: 0.7618, Energy: -54.606891-0.000360j
[2025-07-30 13:11:43] [Iter 3031/4650] R4[780/2400], Temp: 0.7612, Energy: -54.620785-0.003326j
[2025-07-30 13:11:53] [Iter 3032/4650] R4[781/2400], Temp: 0.7607, Energy: -54.625458-0.000218j
[2025-07-30 13:12:04] [Iter 3033/4650] R4[782/2400], Temp: 0.7601, Energy: -54.667896+0.000440j
[2025-07-30 13:12:14] [Iter 3034/4650] R4[783/2400], Temp: 0.7596, Energy: -54.647229-0.001213j
[2025-07-30 13:12:24] [Iter 3035/4650] R4[784/2400], Temp: 0.7590, Energy: -54.686628+0.001888j
[2025-07-30 13:12:34] [Iter 3036/4650] R4[785/2400], Temp: 0.7585, Energy: -54.651688+0.000676j
[2025-07-30 13:12:44] [Iter 3037/4650] R4[786/2400], Temp: 0.7579, Energy: -54.564501-0.001200j
[2025-07-30 13:12:54] [Iter 3038/4650] R4[787/2400], Temp: 0.7573, Energy: -54.547803+0.000767j
[2025-07-30 13:13:04] [Iter 3039/4650] R4[788/2400], Temp: 0.7568, Energy: -54.608998+0.001476j
[2025-07-30 13:13:14] [Iter 3040/4650] R4[789/2400], Temp: 0.7562, Energy: -54.573078-0.001584j
[2025-07-30 13:13:24] [Iter 3041/4650] R4[790/2400], Temp: 0.7556, Energy: -54.638436-0.001799j
[2025-07-30 13:13:35] [Iter 3042/4650] R4[791/2400], Temp: 0.7551, Energy: -54.664815-0.000623j
[2025-07-30 13:13:45] [Iter 3043/4650] R4[792/2400], Temp: 0.7545, Energy: -54.636361+0.003033j
[2025-07-30 13:13:55] [Iter 3044/4650] R4[793/2400], Temp: 0.7540, Energy: -54.612156-0.000132j
[2025-07-30 13:14:05] [Iter 3045/4650] R4[794/2400], Temp: 0.7534, Energy: -54.633426+0.000023j
[2025-07-30 13:14:15] [Iter 3046/4650] R4[795/2400], Temp: 0.7528, Energy: -54.619935+0.001463j
[2025-07-30 13:14:25] [Iter 3047/4650] R4[796/2400], Temp: 0.7523, Energy: -54.628613-0.000796j
[2025-07-30 13:14:35] [Iter 3048/4650] R4[797/2400], Temp: 0.7517, Energy: -54.577093-0.000428j
[2025-07-30 13:14:45] [Iter 3049/4650] R4[798/2400], Temp: 0.7511, Energy: -54.684068+0.001885j
[2025-07-30 13:14:55] [Iter 3050/4650] R4[799/2400], Temp: 0.7506, Energy: -54.645960+0.001434j
[2025-07-30 13:15:06] [Iter 3051/4650] R4[800/2400], Temp: 0.7500, Energy: -54.610977+0.000439j
[2025-07-30 13:15:16] [Iter 3052/4650] R4[801/2400], Temp: 0.7494, Energy: -54.623218-0.000606j
[2025-07-30 13:15:26] [Iter 3053/4650] R4[802/2400], Temp: 0.7489, Energy: -54.629530+0.000535j
[2025-07-30 13:15:36] [Iter 3054/4650] R4[803/2400], Temp: 0.7483, Energy: -54.649709-0.001635j
[2025-07-30 13:15:46] [Iter 3055/4650] R4[804/2400], Temp: 0.7477, Energy: -54.628661+0.001645j
[2025-07-30 13:15:56] [Iter 3056/4650] R4[805/2400], Temp: 0.7472, Energy: -54.649560+0.001571j
[2025-07-30 13:16:06] [Iter 3057/4650] R4[806/2400], Temp: 0.7466, Energy: -54.640541+0.001552j
[2025-07-30 13:16:16] [Iter 3058/4650] R4[807/2400], Temp: 0.7460, Energy: -54.614325-0.000017j
[2025-07-30 13:16:26] [Iter 3059/4650] R4[808/2400], Temp: 0.7455, Energy: -54.586852+0.000850j
[2025-07-30 13:16:37] [Iter 3060/4650] R4[809/2400], Temp: 0.7449, Energy: -54.601188-0.001405j
[2025-07-30 13:16:47] [Iter 3061/4650] R4[810/2400], Temp: 0.7443, Energy: -54.553728+0.002620j
[2025-07-30 13:16:57] [Iter 3062/4650] R4[811/2400], Temp: 0.7437, Energy: -54.542234-0.000075j
[2025-07-30 13:17:07] [Iter 3063/4650] R4[812/2400], Temp: 0.7432, Energy: -54.508478-0.000008j
[2025-07-30 13:17:17] [Iter 3064/4650] R4[813/2400], Temp: 0.7426, Energy: -54.518843-0.001612j
[2025-07-30 13:17:27] [Iter 3065/4650] R4[814/2400], Temp: 0.7420, Energy: -54.567930-0.001331j
[2025-07-30 13:17:37] [Iter 3066/4650] R4[815/2400], Temp: 0.7415, Energy: -54.639411-0.001593j
[2025-07-30 13:17:47] [Iter 3067/4650] R4[816/2400], Temp: 0.7409, Energy: -54.624137-0.000500j
[2025-07-30 13:17:57] [Iter 3068/4650] R4[817/2400], Temp: 0.7403, Energy: -54.593751-0.000081j
[2025-07-30 13:18:08] [Iter 3069/4650] R4[818/2400], Temp: 0.7397, Energy: -54.588890+0.000456j
[2025-07-30 13:18:18] [Iter 3070/4650] R4[819/2400], Temp: 0.7392, Energy: -54.637516-0.000862j
[2025-07-30 13:18:28] [Iter 3071/4650] R4[820/2400], Temp: 0.7386, Energy: -54.598813+0.000360j
[2025-07-30 13:18:38] [Iter 3072/4650] R4[821/2400], Temp: 0.7380, Energy: -54.635797+0.000745j
[2025-07-30 13:18:48] [Iter 3073/4650] R4[822/2400], Temp: 0.7374, Energy: -54.657277-0.001409j
[2025-07-30 13:18:58] [Iter 3074/4650] R4[823/2400], Temp: 0.7369, Energy: -54.590702-0.000299j
[2025-07-30 13:19:08] [Iter 3075/4650] R4[824/2400], Temp: 0.7363, Energy: -54.606320-0.001053j
[2025-07-30 13:19:18] [Iter 3076/4650] R4[825/2400], Temp: 0.7357, Energy: -54.580458-0.001011j
[2025-07-30 13:19:28] [Iter 3077/4650] R4[826/2400], Temp: 0.7351, Energy: -54.608809-0.001364j
[2025-07-30 13:19:38] [Iter 3078/4650] R4[827/2400], Temp: 0.7345, Energy: -54.575394+0.000397j
[2025-07-30 13:19:49] [Iter 3079/4650] R4[828/2400], Temp: 0.7340, Energy: -54.599131+0.000004j
[2025-07-30 13:19:59] [Iter 3080/4650] R4[829/2400], Temp: 0.7334, Energy: -54.532933+0.000375j
[2025-07-30 13:20:09] [Iter 3081/4650] R4[830/2400], Temp: 0.7328, Energy: -54.603255-0.001116j
[2025-07-30 13:20:19] [Iter 3082/4650] R4[831/2400], Temp: 0.7322, Energy: -54.557361-0.002506j
[2025-07-30 13:20:29] [Iter 3083/4650] R4[832/2400], Temp: 0.7316, Energy: -54.618400+0.000161j
[2025-07-30 13:20:39] [Iter 3084/4650] R4[833/2400], Temp: 0.7311, Energy: -54.605673-0.003406j
[2025-07-30 13:20:49] [Iter 3085/4650] R4[834/2400], Temp: 0.7305, Energy: -54.551266-0.000762j
[2025-07-30 13:20:59] [Iter 3086/4650] R4[835/2400], Temp: 0.7299, Energy: -54.560169+0.001114j
[2025-07-30 13:21:09] [Iter 3087/4650] R4[836/2400], Temp: 0.7293, Energy: -54.620285+0.000580j
[2025-07-30 13:21:20] [Iter 3088/4650] R4[837/2400], Temp: 0.7287, Energy: -54.602145+0.001742j
[2025-07-30 13:21:30] [Iter 3089/4650] R4[838/2400], Temp: 0.7282, Energy: -54.630606-0.001671j
[2025-07-30 13:21:40] [Iter 3090/4650] R4[839/2400], Temp: 0.7276, Energy: -54.636619+0.000222j
[2025-07-30 13:21:50] [Iter 3091/4650] R4[840/2400], Temp: 0.7270, Energy: -54.621705+0.000519j
[2025-07-30 13:22:00] [Iter 3092/4650] R4[841/2400], Temp: 0.7264, Energy: -54.594514-0.001287j
[2025-07-30 13:22:10] [Iter 3093/4650] R4[842/2400], Temp: 0.7258, Energy: -54.577288-0.000095j
[2025-07-30 13:22:20] [Iter 3094/4650] R4[843/2400], Temp: 0.7252, Energy: -54.588089-0.001065j
[2025-07-30 13:22:30] [Iter 3095/4650] R4[844/2400], Temp: 0.7247, Energy: -54.648258+0.000618j
[2025-07-30 13:22:41] [Iter 3096/4650] R4[845/2400], Temp: 0.7241, Energy: -54.654035-0.000449j
[2025-07-30 13:22:51] [Iter 3097/4650] R4[846/2400], Temp: 0.7235, Energy: -54.658867-0.000932j
[2025-07-30 13:23:01] [Iter 3098/4650] R4[847/2400], Temp: 0.7229, Energy: -54.686383-0.000841j
[2025-07-30 13:23:11] [Iter 3099/4650] R4[848/2400], Temp: 0.7223, Energy: -54.721077-0.002351j
[2025-07-30 13:23:21] [Iter 3100/4650] R4[849/2400], Temp: 0.7217, Energy: -54.673949+0.000122j
[2025-07-30 13:23:31] [Iter 3101/4650] R4[850/2400], Temp: 0.7211, Energy: -54.680779-0.002285j
[2025-07-30 13:23:41] [Iter 3102/4650] R4[851/2400], Temp: 0.7206, Energy: -54.727413-0.000706j
[2025-07-30 13:23:51] [Iter 3103/4650] R4[852/2400], Temp: 0.7200, Energy: -54.654515+0.000540j
[2025-07-30 13:24:02] [Iter 3104/4650] R4[853/2400], Temp: 0.7194, Energy: -54.721200-0.000468j
[2025-07-30 13:24:12] [Iter 3105/4650] R4[854/2400], Temp: 0.7188, Energy: -54.699063-0.000351j
[2025-07-30 13:24:22] [Iter 3106/4650] R4[855/2400], Temp: 0.7182, Energy: -54.673717+0.001096j
[2025-07-30 13:24:32] [Iter 3107/4650] R4[856/2400], Temp: 0.7176, Energy: -54.642972-0.001882j
[2025-07-30 13:24:42] [Iter 3108/4650] R4[857/2400], Temp: 0.7170, Energy: -54.664130+0.001988j
[2025-07-30 13:24:52] [Iter 3109/4650] R4[858/2400], Temp: 0.7164, Energy: -54.603097-0.000553j
[2025-07-30 13:25:02] [Iter 3110/4650] R4[859/2400], Temp: 0.7158, Energy: -54.559122-0.001465j
[2025-07-30 13:25:12] [Iter 3111/4650] R4[860/2400], Temp: 0.7153, Energy: -54.600383+0.001396j
[2025-07-30 13:25:22] [Iter 3112/4650] R4[861/2400], Temp: 0.7147, Energy: -54.613855-0.001089j
[2025-07-30 13:25:33] [Iter 3113/4650] R4[862/2400], Temp: 0.7141, Energy: -54.576898-0.001355j
[2025-07-30 13:25:43] [Iter 3114/4650] R4[863/2400], Temp: 0.7135, Energy: -54.621483-0.001144j
[2025-07-30 13:25:53] [Iter 3115/4650] R4[864/2400], Temp: 0.7129, Energy: -54.588531+0.000573j
[2025-07-30 13:26:03] [Iter 3116/4650] R4[865/2400], Temp: 0.7123, Energy: -54.636272-0.000957j
[2025-07-30 13:26:13] [Iter 3117/4650] R4[866/2400], Temp: 0.7117, Energy: -54.584672-0.001220j
[2025-07-30 13:26:23] [Iter 3118/4650] R4[867/2400], Temp: 0.7111, Energy: -54.607572-0.001858j
[2025-07-30 13:26:33] [Iter 3119/4650] R4[868/2400], Temp: 0.7105, Energy: -54.596250-0.001851j
[2025-07-30 13:26:43] [Iter 3120/4650] R4[869/2400], Temp: 0.7099, Energy: -54.670797-0.002771j
[2025-07-30 13:26:54] [Iter 3121/4650] R4[870/2400], Temp: 0.7093, Energy: -54.626859+0.001128j
[2025-07-30 13:27:04] [Iter 3122/4650] R4[871/2400], Temp: 0.7087, Energy: -54.690240+0.000800j
[2025-07-30 13:27:14] [Iter 3123/4650] R4[872/2400], Temp: 0.7081, Energy: -54.620934-0.000323j
[2025-07-30 13:27:24] [Iter 3124/4650] R4[873/2400], Temp: 0.7075, Energy: -54.659781+0.000122j
[2025-07-30 13:27:34] [Iter 3125/4650] R4[874/2400], Temp: 0.7069, Energy: -54.649615-0.000997j
[2025-07-30 13:27:44] [Iter 3126/4650] R4[875/2400], Temp: 0.7064, Energy: -54.616843-0.000869j
[2025-07-30 13:27:54] [Iter 3127/4650] R4[876/2400], Temp: 0.7058, Energy: -54.613112-0.001989j
[2025-07-30 13:28:04] [Iter 3128/4650] R4[877/2400], Temp: 0.7052, Energy: -54.608460-0.001731j
[2025-07-30 13:28:15] [Iter 3129/4650] R4[878/2400], Temp: 0.7046, Energy: -54.611977+0.000902j
[2025-07-30 13:28:25] [Iter 3130/4650] R4[879/2400], Temp: 0.7040, Energy: -54.613232+0.001487j
[2025-07-30 13:28:35] [Iter 3131/4650] R4[880/2400], Temp: 0.7034, Energy: -54.549188-0.000106j
[2025-07-30 13:28:45] [Iter 3132/4650] R4[881/2400], Temp: 0.7028, Energy: -54.619057-0.001010j
[2025-07-30 13:28:55] [Iter 3133/4650] R4[882/2400], Temp: 0.7022, Energy: -54.642542+0.000988j
[2025-07-30 13:29:05] [Iter 3134/4650] R4[883/2400], Temp: 0.7016, Energy: -54.699597-0.002293j
[2025-07-30 13:29:15] [Iter 3135/4650] R4[884/2400], Temp: 0.7010, Energy: -54.635696+0.000814j
[2025-07-30 13:29:25] [Iter 3136/4650] R4[885/2400], Temp: 0.7004, Energy: -54.600256-0.004112j
[2025-07-30 13:29:35] [Iter 3137/4650] R4[886/2400], Temp: 0.6998, Energy: -54.592627+0.002728j
[2025-07-30 13:29:46] [Iter 3138/4650] R4[887/2400], Temp: 0.6992, Energy: -54.546756-0.001050j
[2025-07-30 13:29:56] [Iter 3139/4650] R4[888/2400], Temp: 0.6986, Energy: -54.592127+0.000824j
[2025-07-30 13:30:06] [Iter 3140/4650] R4[889/2400], Temp: 0.6980, Energy: -54.578784+0.001127j
[2025-07-30 13:30:16] [Iter 3141/4650] R4[890/2400], Temp: 0.6974, Energy: -54.610262-0.000775j
[2025-07-30 13:30:26] [Iter 3142/4650] R4[891/2400], Temp: 0.6968, Energy: -54.609091-0.000813j
[2025-07-30 13:30:36] [Iter 3143/4650] R4[892/2400], Temp: 0.6962, Energy: -54.657643+0.001279j
[2025-07-30 13:30:46] [Iter 3144/4650] R4[893/2400], Temp: 0.6956, Energy: -54.626821+0.001311j
[2025-07-30 13:30:56] [Iter 3145/4650] R4[894/2400], Temp: 0.6950, Energy: -54.649950-0.000391j
[2025-07-30 13:31:06] [Iter 3146/4650] R4[895/2400], Temp: 0.6944, Energy: -54.661078-0.000062j
[2025-07-30 13:31:17] [Iter 3147/4650] R4[896/2400], Temp: 0.6938, Energy: -54.627997-0.001276j
[2025-07-30 13:31:27] [Iter 3148/4650] R4[897/2400], Temp: 0.6932, Energy: -54.626562+0.001564j
[2025-07-30 13:31:37] [Iter 3149/4650] R4[898/2400], Temp: 0.6926, Energy: -54.656458-0.000784j
[2025-07-30 13:31:47] [Iter 3150/4650] R4[899/2400], Temp: 0.6919, Energy: -54.627847-0.000595j
[2025-07-30 13:31:57] [Iter 3151/4650] R4[900/2400], Temp: 0.6913, Energy: -54.622202+0.001776j
[2025-07-30 13:32:07] [Iter 3152/4650] R4[901/2400], Temp: 0.6907, Energy: -54.700020+0.001259j
[2025-07-30 13:32:17] [Iter 3153/4650] R4[902/2400], Temp: 0.6901, Energy: -54.720381-0.000775j
[2025-07-30 13:32:27] [Iter 3154/4650] R4[903/2400], Temp: 0.6895, Energy: -54.729468+0.000434j
[2025-07-30 13:32:37] [Iter 3155/4650] R4[904/2400], Temp: 0.6889, Energy: -54.619253-0.000358j
[2025-07-30 13:32:48] [Iter 3156/4650] R4[905/2400], Temp: 0.6883, Energy: -54.650852-0.000902j
[2025-07-30 13:32:58] [Iter 3157/4650] R4[906/2400], Temp: 0.6877, Energy: -54.654744-0.001046j
[2025-07-30 13:33:08] [Iter 3158/4650] R4[907/2400], Temp: 0.6871, Energy: -54.640355-0.000159j
[2025-07-30 13:33:18] [Iter 3159/4650] R4[908/2400], Temp: 0.6865, Energy: -54.660178-0.000336j
[2025-07-30 13:33:28] [Iter 3160/4650] R4[909/2400], Temp: 0.6859, Energy: -54.633152-0.000146j
[2025-07-30 13:33:38] [Iter 3161/4650] R4[910/2400], Temp: 0.6853, Energy: -54.605113-0.000970j
[2025-07-30 13:33:48] [Iter 3162/4650] R4[911/2400], Temp: 0.6847, Energy: -54.675638+0.000037j
[2025-07-30 13:33:58] [Iter 3163/4650] R4[912/2400], Temp: 0.6841, Energy: -54.678170-0.000094j
[2025-07-30 13:34:08] [Iter 3164/4650] R4[913/2400], Temp: 0.6835, Energy: -54.701691-0.001534j
[2025-07-30 13:34:19] [Iter 3165/4650] R4[914/2400], Temp: 0.6828, Energy: -54.662011+0.000861j
[2025-07-30 13:34:29] [Iter 3166/4650] R4[915/2400], Temp: 0.6822, Energy: -54.642590+0.000380j
[2025-07-30 13:34:39] [Iter 3167/4650] R4[916/2400], Temp: 0.6816, Energy: -54.682160+0.000498j
[2025-07-30 13:34:49] [Iter 3168/4650] R4[917/2400], Temp: 0.6810, Energy: -54.657062-0.001017j
[2025-07-30 13:34:59] [Iter 3169/4650] R4[918/2400], Temp: 0.6804, Energy: -54.690970+0.000866j
[2025-07-30 13:35:09] [Iter 3170/4650] R4[919/2400], Temp: 0.6798, Energy: -54.682658-0.001359j
[2025-07-30 13:35:19] [Iter 3171/4650] R4[920/2400], Temp: 0.6792, Energy: -54.694606+0.001421j
[2025-07-30 13:35:29] [Iter 3172/4650] R4[921/2400], Temp: 0.6786, Energy: -54.686031+0.000800j
[2025-07-30 13:35:40] [Iter 3173/4650] R4[922/2400], Temp: 0.6780, Energy: -54.696093-0.000451j
[2025-07-30 13:35:50] [Iter 3174/4650] R4[923/2400], Temp: 0.6773, Energy: -54.728914-0.000503j
[2025-07-30 13:36:00] [Iter 3175/4650] R4[924/2400], Temp: 0.6767, Energy: -54.713716+0.001066j
[2025-07-30 13:36:10] [Iter 3176/4650] R4[925/2400], Temp: 0.6761, Energy: -54.708401+0.000056j
[2025-07-30 13:36:20] [Iter 3177/4650] R4[926/2400], Temp: 0.6755, Energy: -54.737643+0.000298j
[2025-07-30 13:36:30] [Iter 3178/4650] R4[927/2400], Temp: 0.6749, Energy: -54.711003-0.001574j
[2025-07-30 13:36:40] [Iter 3179/4650] R4[928/2400], Temp: 0.6743, Energy: -54.686836+0.000157j
[2025-07-30 13:36:50] [Iter 3180/4650] R4[929/2400], Temp: 0.6737, Energy: -54.718653-0.001739j
[2025-07-30 13:37:00] [Iter 3181/4650] R4[930/2400], Temp: 0.6731, Energy: -54.690104+0.000090j
[2025-07-30 13:37:11] [Iter 3182/4650] R4[931/2400], Temp: 0.6724, Energy: -54.678289-0.001280j
[2025-07-30 13:37:21] [Iter 3183/4650] R4[932/2400], Temp: 0.6718, Energy: -54.662732+0.000206j
[2025-07-30 13:37:31] [Iter 3184/4650] R4[933/2400], Temp: 0.6712, Energy: -54.650551+0.000554j
[2025-07-30 13:37:41] [Iter 3185/4650] R4[934/2400], Temp: 0.6706, Energy: -54.652336-0.001638j
[2025-07-30 13:37:51] [Iter 3186/4650] R4[935/2400], Temp: 0.6700, Energy: -54.605922+0.000559j
[2025-07-30 13:38:01] [Iter 3187/4650] R4[936/2400], Temp: 0.6694, Energy: -54.608908-0.001492j
[2025-07-30 13:38:11] [Iter 3188/4650] R4[937/2400], Temp: 0.6688, Energy: -54.599833+0.000439j
[2025-07-30 13:38:21] [Iter 3189/4650] R4[938/2400], Temp: 0.6681, Energy: -54.601039+0.002350j
[2025-07-30 13:38:32] [Iter 3190/4650] R4[939/2400], Temp: 0.6675, Energy: -54.534900+0.000178j
[2025-07-30 13:38:42] [Iter 3191/4650] R4[940/2400], Temp: 0.6669, Energy: -54.554613-0.001568j
[2025-07-30 13:38:52] [Iter 3192/4650] R4[941/2400], Temp: 0.6663, Energy: -54.581645+0.001672j
[2025-07-30 13:39:02] [Iter 3193/4650] R4[942/2400], Temp: 0.6657, Energy: -54.597398-0.001163j
[2025-07-30 13:39:12] [Iter 3194/4650] R4[943/2400], Temp: 0.6651, Energy: -54.600220-0.000627j
[2025-07-30 13:39:22] [Iter 3195/4650] R4[944/2400], Temp: 0.6644, Energy: -54.613301-0.000756j
[2025-07-30 13:39:32] [Iter 3196/4650] R4[945/2400], Temp: 0.6638, Energy: -54.593861-0.001826j
[2025-07-30 13:39:42] [Iter 3197/4650] R4[946/2400], Temp: 0.6632, Energy: -54.560852-0.002102j
[2025-07-30 13:39:52] [Iter 3198/4650] R4[947/2400], Temp: 0.6626, Energy: -54.608758-0.002327j
[2025-07-30 13:40:02] [Iter 3199/4650] R4[948/2400], Temp: 0.6620, Energy: -54.632777-0.000810j
[2025-07-30 13:40:13] [Iter 3200/4650] R4[949/2400], Temp: 0.6613, Energy: -54.596961-0.000085j
[2025-07-30 13:40:23] [Iter 3201/4650] R4[950/2400], Temp: 0.6607, Energy: -54.612448+0.004119j
[2025-07-30 13:40:33] [Iter 3202/4650] R4[951/2400], Temp: 0.6601, Energy: -54.640258+0.001503j
[2025-07-30 13:40:43] [Iter 3203/4650] R4[952/2400], Temp: 0.6595, Energy: -54.624662+0.001271j
[2025-07-30 13:40:53] [Iter 3204/4650] R4[953/2400], Temp: 0.6589, Energy: -54.635564+0.002184j
[2025-07-30 13:41:03] [Iter 3205/4650] R4[954/2400], Temp: 0.6582, Energy: -54.636970-0.000056j
[2025-07-30 13:41:13] [Iter 3206/4650] R4[955/2400], Temp: 0.6576, Energy: -54.658991+0.001011j
[2025-07-30 13:41:23] [Iter 3207/4650] R4[956/2400], Temp: 0.6570, Energy: -54.577089-0.000478j
[2025-07-30 13:41:34] [Iter 3208/4650] R4[957/2400], Temp: 0.6564, Energy: -54.673720+0.000406j
[2025-07-30 13:41:44] [Iter 3209/4650] R4[958/2400], Temp: 0.6558, Energy: -54.658529+0.002037j
[2025-07-30 13:41:54] [Iter 3210/4650] R4[959/2400], Temp: 0.6551, Energy: -54.625363+0.000891j
[2025-07-30 13:42:04] [Iter 3211/4650] R4[960/2400], Temp: 0.6545, Energy: -54.646715-0.000307j
[2025-07-30 13:42:14] [Iter 3212/4650] R4[961/2400], Temp: 0.6539, Energy: -54.636348+0.001717j
[2025-07-30 13:42:24] [Iter 3213/4650] R4[962/2400], Temp: 0.6533, Energy: -54.560683+0.000549j
[2025-07-30 13:42:34] [Iter 3214/4650] R4[963/2400], Temp: 0.6526, Energy: -54.654647+0.002026j
[2025-07-30 13:42:44] [Iter 3215/4650] R4[964/2400], Temp: 0.6520, Energy: -54.645875+0.000466j
[2025-07-30 13:42:54] [Iter 3216/4650] R4[965/2400], Temp: 0.6514, Energy: -54.588070+0.000910j
[2025-07-30 13:43:05] [Iter 3217/4650] R4[966/2400], Temp: 0.6508, Energy: -54.586488-0.001493j
[2025-07-30 13:43:15] [Iter 3218/4650] R4[967/2400], Temp: 0.6501, Energy: -54.643076+0.000335j
[2025-07-30 13:43:25] [Iter 3219/4650] R4[968/2400], Temp: 0.6495, Energy: -54.625551+0.000041j
[2025-07-30 13:43:35] [Iter 3220/4650] R4[969/2400], Temp: 0.6489, Energy: -54.584325-0.001407j
[2025-07-30 13:43:45] [Iter 3221/4650] R4[970/2400], Temp: 0.6483, Energy: -54.566587-0.001761j
[2025-07-30 13:43:55] [Iter 3222/4650] R4[971/2400], Temp: 0.6476, Energy: -54.588244+0.002738j
[2025-07-30 13:44:05] [Iter 3223/4650] R4[972/2400], Temp: 0.6470, Energy: -54.598981+0.001005j
[2025-07-30 13:44:15] [Iter 3224/4650] R4[973/2400], Temp: 0.6464, Energy: -54.642667-0.000906j
[2025-07-30 13:44:25] [Iter 3225/4650] R4[974/2400], Temp: 0.6458, Energy: -54.612706+0.001166j
[2025-07-30 13:44:36] [Iter 3226/4650] R4[975/2400], Temp: 0.6451, Energy: -54.686025+0.001299j
[2025-07-30 13:44:46] [Iter 3227/4650] R4[976/2400], Temp: 0.6445, Energy: -54.684315+0.001231j
[2025-07-30 13:44:56] [Iter 3228/4650] R4[977/2400], Temp: 0.6439, Energy: -54.686759+0.000070j
[2025-07-30 13:45:06] [Iter 3229/4650] R4[978/2400], Temp: 0.6433, Energy: -54.693762+0.000887j
[2025-07-30 13:45:16] [Iter 3230/4650] R4[979/2400], Temp: 0.6426, Energy: -54.761489-0.001533j
[2025-07-30 13:45:26] [Iter 3231/4650] R4[980/2400], Temp: 0.6420, Energy: -54.723730+0.000207j
[2025-07-30 13:45:36] [Iter 3232/4650] R4[981/2400], Temp: 0.6414, Energy: -54.761843-0.001268j
[2025-07-30 13:45:46] [Iter 3233/4650] R4[982/2400], Temp: 0.6408, Energy: -54.714462-0.001011j
[2025-07-30 13:45:56] [Iter 3234/4650] R4[983/2400], Temp: 0.6401, Energy: -54.700222-0.000284j
[2025-07-30 13:46:07] [Iter 3235/4650] R4[984/2400], Temp: 0.6395, Energy: -54.648288+0.000559j
[2025-07-30 13:46:17] [Iter 3236/4650] R4[985/2400], Temp: 0.6389, Energy: -54.706437-0.001578j
[2025-07-30 13:46:27] [Iter 3237/4650] R4[986/2400], Temp: 0.6382, Energy: -54.678671-0.001038j
[2025-07-30 13:46:37] [Iter 3238/4650] R4[987/2400], Temp: 0.6376, Energy: -54.609247+0.000379j
[2025-07-30 13:46:47] [Iter 3239/4650] R4[988/2400], Temp: 0.6370, Energy: -54.599858-0.000135j
[2025-07-30 13:46:57] [Iter 3240/4650] R4[989/2400], Temp: 0.6364, Energy: -54.647641-0.000234j
[2025-07-30 13:47:07] [Iter 3241/4650] R4[990/2400], Temp: 0.6357, Energy: -54.621120+0.000546j
[2025-07-30 13:47:17] [Iter 3242/4650] R4[991/2400], Temp: 0.6351, Energy: -54.610602+0.001168j
[2025-07-30 13:47:27] [Iter 3243/4650] R4[992/2400], Temp: 0.6345, Energy: -54.557348-0.000877j
[2025-07-30 13:47:38] [Iter 3244/4650] R4[993/2400], Temp: 0.6338, Energy: -54.591494-0.000244j
[2025-07-30 13:47:48] [Iter 3245/4650] R4[994/2400], Temp: 0.6332, Energy: -54.599382+0.001661j
[2025-07-30 13:47:58] [Iter 3246/4650] R4[995/2400], Temp: 0.6326, Energy: -54.561293-0.000666j
[2025-07-30 13:48:08] [Iter 3247/4650] R4[996/2400], Temp: 0.6319, Energy: -54.607371+0.000888j
[2025-07-30 13:48:18] [Iter 3248/4650] R4[997/2400], Temp: 0.6313, Energy: -54.656296+0.000504j
[2025-07-30 13:48:28] [Iter 3249/4650] R4[998/2400], Temp: 0.6307, Energy: -54.633789+0.000467j
[2025-07-30 13:48:38] [Iter 3250/4650] R4[999/2400], Temp: 0.6300, Energy: -54.635284+0.001090j
[2025-07-30 13:48:48] [Iter 3251/4650] R4[1000/2400], Temp: 0.6294, Energy: -54.598993-0.000333j
[2025-07-30 13:48:58] [Iter 3252/4650] R4[1001/2400], Temp: 0.6288, Energy: -54.596486+0.002057j
[2025-07-30 13:49:08] [Iter 3253/4650] R4[1002/2400], Temp: 0.6281, Energy: -54.593069+0.002930j
[2025-07-30 13:49:19] [Iter 3254/4650] R4[1003/2400], Temp: 0.6275, Energy: -54.552193+0.001740j
[2025-07-30 13:49:29] [Iter 3255/4650] R4[1004/2400], Temp: 0.6269, Energy: -54.575345+0.002951j
[2025-07-30 13:49:39] [Iter 3256/4650] R4[1005/2400], Temp: 0.6262, Energy: -54.641122+0.002751j
[2025-07-30 13:49:49] [Iter 3257/4650] R4[1006/2400], Temp: 0.6256, Energy: -54.605357+0.002651j
[2025-07-30 13:49:59] [Iter 3258/4650] R4[1007/2400], Temp: 0.6250, Energy: -54.588369-0.002478j
[2025-07-30 13:50:09] [Iter 3259/4650] R4[1008/2400], Temp: 0.6243, Energy: -54.645807+0.000070j
[2025-07-30 13:50:19] [Iter 3260/4650] R4[1009/2400], Temp: 0.6237, Energy: -54.629033-0.001570j
[2025-07-30 13:50:29] [Iter 3261/4650] R4[1010/2400], Temp: 0.6231, Energy: -54.621596-0.000086j
[2025-07-30 13:50:40] [Iter 3262/4650] R4[1011/2400], Temp: 0.6224, Energy: -54.685602-0.001269j
[2025-07-30 13:50:50] [Iter 3263/4650] R4[1012/2400], Temp: 0.6218, Energy: -54.688571-0.000059j
[2025-07-30 13:51:00] [Iter 3264/4650] R4[1013/2400], Temp: 0.6212, Energy: -54.597921-0.001552j
[2025-07-30 13:51:10] [Iter 3265/4650] R4[1014/2400], Temp: 0.6205, Energy: -54.647776+0.000706j
[2025-07-30 13:51:20] [Iter 3266/4650] R4[1015/2400], Temp: 0.6199, Energy: -54.580654-0.000473j
[2025-07-30 13:51:30] [Iter 3267/4650] R4[1016/2400], Temp: 0.6193, Energy: -54.634013+0.001147j
[2025-07-30 13:51:40] [Iter 3268/4650] R4[1017/2400], Temp: 0.6186, Energy: -54.667563+0.001453j
[2025-07-30 13:51:50] [Iter 3269/4650] R4[1018/2400], Temp: 0.6180, Energy: -54.603948+0.002437j
[2025-07-30 13:52:00] [Iter 3270/4650] R4[1019/2400], Temp: 0.6174, Energy: -54.580187-0.001191j
[2025-07-30 13:52:11] [Iter 3271/4650] R4[1020/2400], Temp: 0.6167, Energy: -54.595082-0.000031j
[2025-07-30 13:52:21] [Iter 3272/4650] R4[1021/2400], Temp: 0.6161, Energy: -54.624988+0.001771j
[2025-07-30 13:52:31] [Iter 3273/4650] R4[1022/2400], Temp: 0.6154, Energy: -54.627331-0.000375j
[2025-07-30 13:52:41] [Iter 3274/4650] R4[1023/2400], Temp: 0.6148, Energy: -54.613889-0.003278j
[2025-07-30 13:52:51] [Iter 3275/4650] R4[1024/2400], Temp: 0.6142, Energy: -54.649521-0.000664j
[2025-07-30 13:53:01] [Iter 3276/4650] R4[1025/2400], Temp: 0.6135, Energy: -54.615166+0.002506j
[2025-07-30 13:53:11] [Iter 3277/4650] R4[1026/2400], Temp: 0.6129, Energy: -54.615755-0.003131j
[2025-07-30 13:53:21] [Iter 3278/4650] R4[1027/2400], Temp: 0.6123, Energy: -54.606806+0.000301j
[2025-07-30 13:53:32] [Iter 3279/4650] R4[1028/2400], Temp: 0.6116, Energy: -54.613515-0.000704j
[2025-07-30 13:53:42] [Iter 3280/4650] R4[1029/2400], Temp: 0.6110, Energy: -54.691140+0.000072j
[2025-07-30 13:53:52] [Iter 3281/4650] R4[1030/2400], Temp: 0.6103, Energy: -54.635705+0.001011j
[2025-07-30 13:54:02] [Iter 3282/4650] R4[1031/2400], Temp: 0.6097, Energy: -54.679978-0.000288j
[2025-07-30 13:54:12] [Iter 3283/4650] R4[1032/2400], Temp: 0.6091, Energy: -54.589501+0.003463j
[2025-07-30 13:54:22] [Iter 3284/4650] R4[1033/2400], Temp: 0.6084, Energy: -54.561930-0.000723j
[2025-07-30 13:54:32] [Iter 3285/4650] R4[1034/2400], Temp: 0.6078, Energy: -54.612240-0.000243j
[2025-07-30 13:54:42] [Iter 3286/4650] R4[1035/2400], Temp: 0.6072, Energy: -54.591464+0.000888j
[2025-07-30 13:54:52] [Iter 3287/4650] R4[1036/2400], Temp: 0.6065, Energy: -54.610449+0.001870j
[2025-07-30 13:55:03] [Iter 3288/4650] R4[1037/2400], Temp: 0.6059, Energy: -54.663055-0.001978j
[2025-07-30 13:55:13] [Iter 3289/4650] R4[1038/2400], Temp: 0.6052, Energy: -54.638923+0.000449j
[2025-07-30 13:55:23] [Iter 3290/4650] R4[1039/2400], Temp: 0.6046, Energy: -54.693317+0.001304j
[2025-07-30 13:55:33] [Iter 3291/4650] R4[1040/2400], Temp: 0.6040, Energy: -54.651404+0.000810j
[2025-07-30 13:55:43] [Iter 3292/4650] R4[1041/2400], Temp: 0.6033, Energy: -54.658923+0.000565j
[2025-07-30 13:55:53] [Iter 3293/4650] R4[1042/2400], Temp: 0.6027, Energy: -54.684244-0.000612j
[2025-07-30 13:56:03] [Iter 3294/4650] R4[1043/2400], Temp: 0.6020, Energy: -54.668274+0.000386j
[2025-07-30 13:56:13] [Iter 3295/4650] R4[1044/2400], Temp: 0.6014, Energy: -54.647751+0.002421j
[2025-07-30 13:56:23] [Iter 3296/4650] R4[1045/2400], Temp: 0.6008, Energy: -54.642114-0.000366j
[2025-07-30 13:56:34] [Iter 3297/4650] R4[1046/2400], Temp: 0.6001, Energy: -54.646868-0.001187j
[2025-07-30 13:56:44] [Iter 3298/4650] R4[1047/2400], Temp: 0.5995, Energy: -54.648551-0.001239j
[2025-07-30 13:56:54] [Iter 3299/4650] R4[1048/2400], Temp: 0.5988, Energy: -54.608933+0.000695j
[2025-07-30 13:57:04] [Iter 3300/4650] R4[1049/2400], Temp: 0.5982, Energy: -54.594162+0.001203j
[2025-07-30 13:57:14] [Iter 3301/4650] R4[1050/2400], Temp: 0.5975, Energy: -54.585749-0.000085j
[2025-07-30 13:57:24] [Iter 3302/4650] R4[1051/2400], Temp: 0.5969, Energy: -54.568034-0.001211j
[2025-07-30 13:57:34] [Iter 3303/4650] R4[1052/2400], Temp: 0.5963, Energy: -54.595334+0.000065j
[2025-07-30 13:57:44] [Iter 3304/4650] R4[1053/2400], Temp: 0.5956, Energy: -54.610986+0.002538j
[2025-07-30 13:57:55] [Iter 3305/4650] R4[1054/2400], Temp: 0.5950, Energy: -54.620481+0.002689j
[2025-07-30 13:58:05] [Iter 3306/4650] R4[1055/2400], Temp: 0.5943, Energy: -54.654785-0.002342j
[2025-07-30 13:58:15] [Iter 3307/4650] R4[1056/2400], Temp: 0.5937, Energy: -54.633887-0.001367j
[2025-07-30 13:58:25] [Iter 3308/4650] R4[1057/2400], Temp: 0.5930, Energy: -54.638256-0.001201j
[2025-07-30 13:58:35] [Iter 3309/4650] R4[1058/2400], Temp: 0.5924, Energy: -54.618491-0.001865j
[2025-07-30 13:58:45] [Iter 3310/4650] R4[1059/2400], Temp: 0.5918, Energy: -54.545455+0.000026j
[2025-07-30 13:58:55] [Iter 3311/4650] R4[1060/2400], Temp: 0.5911, Energy: -54.575925+0.000658j
[2025-07-30 13:59:05] [Iter 3312/4650] R4[1061/2400], Temp: 0.5905, Energy: -54.588488+0.000349j
[2025-07-30 13:59:15] [Iter 3313/4650] R4[1062/2400], Temp: 0.5898, Energy: -54.536245-0.002004j
[2025-07-30 13:59:26] [Iter 3314/4650] R4[1063/2400], Temp: 0.5892, Energy: -54.573866-0.000141j
[2025-07-30 13:59:36] [Iter 3315/4650] R4[1064/2400], Temp: 0.5885, Energy: -54.532956+0.000916j
[2025-07-30 13:59:46] [Iter 3316/4650] R4[1065/2400], Temp: 0.5879, Energy: -54.553741-0.003647j
[2025-07-30 13:59:56] [Iter 3317/4650] R4[1066/2400], Temp: 0.5873, Energy: -54.565161-0.000726j
[2025-07-30 14:00:06] [Iter 3318/4650] R4[1067/2400], Temp: 0.5866, Energy: -54.591726+0.000600j
[2025-07-30 14:00:16] [Iter 3319/4650] R4[1068/2400], Temp: 0.5860, Energy: -54.617068+0.000570j
[2025-07-30 14:00:26] [Iter 3320/4650] R4[1069/2400], Temp: 0.5853, Energy: -54.600386+0.000863j
[2025-07-30 14:00:36] [Iter 3321/4650] R4[1070/2400], Temp: 0.5847, Energy: -54.620640+0.000479j
[2025-07-30 14:00:46] [Iter 3322/4650] R4[1071/2400], Temp: 0.5840, Energy: -54.606116+0.000744j
[2025-07-30 14:00:56] [Iter 3323/4650] R4[1072/2400], Temp: 0.5834, Energy: -54.605165-0.000967j
[2025-07-30 14:01:07] [Iter 3324/4650] R4[1073/2400], Temp: 0.5827, Energy: -54.622250-0.000526j
[2025-07-30 14:01:17] [Iter 3325/4650] R4[1074/2400], Temp: 0.5821, Energy: -54.582190+0.001464j
[2025-07-30 14:01:27] [Iter 3326/4650] R4[1075/2400], Temp: 0.5814, Energy: -54.592508+0.000191j
[2025-07-30 14:01:37] [Iter 3327/4650] R4[1076/2400], Temp: 0.5808, Energy: -54.650181+0.000594j
[2025-07-30 14:01:47] [Iter 3328/4650] R4[1077/2400], Temp: 0.5802, Energy: -54.671475-0.001865j
[2025-07-30 14:01:57] [Iter 3329/4650] R4[1078/2400], Temp: 0.5795, Energy: -54.637772-0.000240j
[2025-07-30 14:02:07] [Iter 3330/4650] R4[1079/2400], Temp: 0.5789, Energy: -54.617998+0.000590j
[2025-07-30 14:02:17] [Iter 3331/4650] R4[1080/2400], Temp: 0.5782, Energy: -54.583768-0.000437j
[2025-07-30 14:02:27] [Iter 3332/4650] R4[1081/2400], Temp: 0.5776, Energy: -54.613541+0.000565j
[2025-07-30 14:02:38] [Iter 3333/4650] R4[1082/2400], Temp: 0.5769, Energy: -54.603175+0.001732j
[2025-07-30 14:02:48] [Iter 3334/4650] R4[1083/2400], Temp: 0.5763, Energy: -54.600934+0.000227j
[2025-07-30 14:02:58] [Iter 3335/4650] R4[1084/2400], Temp: 0.5756, Energy: -54.622763-0.000914j
[2025-07-30 14:03:08] [Iter 3336/4650] R4[1085/2400], Temp: 0.5750, Energy: -54.580595-0.000168j
[2025-07-30 14:03:18] [Iter 3337/4650] R4[1086/2400], Temp: 0.5743, Energy: -54.666139+0.000190j
[2025-07-30 14:03:28] [Iter 3338/4650] R4[1087/2400], Temp: 0.5737, Energy: -54.609619-0.001286j
[2025-07-30 14:03:38] [Iter 3339/4650] R4[1088/2400], Temp: 0.5730, Energy: -54.667730-0.001221j
[2025-07-30 14:03:48] [Iter 3340/4650] R4[1089/2400], Temp: 0.5724, Energy: -54.661437-0.001869j
[2025-07-30 14:03:58] [Iter 3341/4650] R4[1090/2400], Temp: 0.5717, Energy: -54.630322+0.001403j
[2025-07-30 14:04:09] [Iter 3342/4650] R4[1091/2400], Temp: 0.5711, Energy: -54.593234-0.000358j
[2025-07-30 14:04:19] [Iter 3343/4650] R4[1092/2400], Temp: 0.5705, Energy: -54.627691-0.001529j
[2025-07-30 14:04:29] [Iter 3344/4650] R4[1093/2400], Temp: 0.5698, Energy: -54.641139+0.001087j
[2025-07-30 14:04:39] [Iter 3345/4650] R4[1094/2400], Temp: 0.5692, Energy: -54.656991+0.001280j
[2025-07-30 14:04:49] [Iter 3346/4650] R4[1095/2400], Temp: 0.5685, Energy: -54.661001-0.000861j
[2025-07-30 14:04:59] [Iter 3347/4650] R4[1096/2400], Temp: 0.5679, Energy: -54.672405+0.000539j
[2025-07-30 14:05:09] [Iter 3348/4650] R4[1097/2400], Temp: 0.5672, Energy: -54.618214+0.001349j
[2025-07-30 14:05:19] [Iter 3349/4650] R4[1098/2400], Temp: 0.5666, Energy: -54.561465+0.000912j
[2025-07-30 14:05:29] [Iter 3350/4650] R4[1099/2400], Temp: 0.5659, Energy: -54.610177+0.002963j
[2025-07-30 14:05:40] [Iter 3351/4650] R4[1100/2400], Temp: 0.5653, Energy: -54.583055+0.000218j
[2025-07-30 14:05:50] [Iter 3352/4650] R4[1101/2400], Temp: 0.5646, Energy: -54.603189-0.001728j
[2025-07-30 14:06:00] [Iter 3353/4650] R4[1102/2400], Temp: 0.5640, Energy: -54.563359+0.001621j
[2025-07-30 14:06:10] [Iter 3354/4650] R4[1103/2400], Temp: 0.5633, Energy: -54.637111-0.000883j
[2025-07-30 14:06:20] [Iter 3355/4650] R4[1104/2400], Temp: 0.5627, Energy: -54.643673+0.002935j
[2025-07-30 14:06:30] [Iter 3356/4650] R4[1105/2400], Temp: 0.5620, Energy: -54.613230-0.001178j
[2025-07-30 14:06:40] [Iter 3357/4650] R4[1106/2400], Temp: 0.5614, Energy: -54.565409+0.001268j
[2025-07-30 14:06:50] [Iter 3358/4650] R4[1107/2400], Temp: 0.5607, Energy: -54.600556+0.001517j
[2025-07-30 14:07:01] [Iter 3359/4650] R4[1108/2400], Temp: 0.5601, Energy: -54.635939+0.001367j
[2025-07-30 14:07:11] [Iter 3360/4650] R4[1109/2400], Temp: 0.5594, Energy: -54.627378+0.000899j
[2025-07-30 14:07:21] [Iter 3361/4650] R4[1110/2400], Temp: 0.5588, Energy: -54.668839-0.000497j
[2025-07-30 14:07:31] [Iter 3362/4650] R4[1111/2400], Temp: 0.5581, Energy: -54.653371+0.000239j
[2025-07-30 14:07:41] [Iter 3363/4650] R4[1112/2400], Temp: 0.5575, Energy: -54.628110+0.002593j
[2025-07-30 14:07:51] [Iter 3364/4650] R4[1113/2400], Temp: 0.5568, Energy: -54.636286-0.001401j
[2025-07-30 14:08:01] [Iter 3365/4650] R4[1114/2400], Temp: 0.5562, Energy: -54.684348+0.000210j
[2025-07-30 14:08:11] [Iter 3366/4650] R4[1115/2400], Temp: 0.5555, Energy: -54.660699-0.001958j
[2025-07-30 14:08:21] [Iter 3367/4650] R4[1116/2400], Temp: 0.5549, Energy: -54.615779+0.000387j
[2025-07-30 14:08:32] [Iter 3368/4650] R4[1117/2400], Temp: 0.5542, Energy: -54.586533-0.001706j
[2025-07-30 14:08:42] [Iter 3369/4650] R4[1118/2400], Temp: 0.5536, Energy: -54.578570-0.000348j
[2025-07-30 14:08:52] [Iter 3370/4650] R4[1119/2400], Temp: 0.5529, Energy: -54.585815+0.000171j
[2025-07-30 14:09:02] [Iter 3371/4650] R4[1120/2400], Temp: 0.5523, Energy: -54.594271+0.002286j
[2025-07-30 14:09:12] [Iter 3372/4650] R4[1121/2400], Temp: 0.5516, Energy: -54.676769+0.000897j
[2025-07-30 14:09:22] [Iter 3373/4650] R4[1122/2400], Temp: 0.5510, Energy: -54.629629-0.003566j
[2025-07-30 14:09:32] [Iter 3374/4650] R4[1123/2400], Temp: 0.5503, Energy: -54.703718-0.000818j
[2025-07-30 14:09:42] [Iter 3375/4650] R4[1124/2400], Temp: 0.5497, Energy: -54.761082-0.002039j
[2025-07-30 14:09:52] [Iter 3376/4650] R4[1125/2400], Temp: 0.5490, Energy: -54.706276-0.000212j
[2025-07-30 14:10:03] [Iter 3377/4650] R4[1126/2400], Temp: 0.5484, Energy: -54.713373+0.000690j
[2025-07-30 14:10:13] [Iter 3378/4650] R4[1127/2400], Temp: 0.5477, Energy: -54.685448+0.002051j
[2025-07-30 14:10:23] [Iter 3379/4650] R4[1128/2400], Temp: 0.5471, Energy: -54.766311+0.002496j
[2025-07-30 14:10:33] [Iter 3380/4650] R4[1129/2400], Temp: 0.5464, Energy: -54.719823-0.003296j
[2025-07-30 14:10:43] [Iter 3381/4650] R4[1130/2400], Temp: 0.5458, Energy: -54.694777+0.000037j
[2025-07-30 14:10:53] [Iter 3382/4650] R4[1131/2400], Temp: 0.5451, Energy: -54.622321+0.000175j
[2025-07-30 14:11:03] [Iter 3383/4650] R4[1132/2400], Temp: 0.5444, Energy: -54.666228+0.001967j
[2025-07-30 14:11:13] [Iter 3384/4650] R4[1133/2400], Temp: 0.5438, Energy: -54.684882+0.000445j
[2025-07-30 14:11:24] [Iter 3385/4650] R4[1134/2400], Temp: 0.5431, Energy: -54.693575+0.001460j
[2025-07-30 14:11:34] [Iter 3386/4650] R4[1135/2400], Temp: 0.5425, Energy: -54.666627-0.000685j
[2025-07-30 14:11:44] [Iter 3387/4650] R4[1136/2400], Temp: 0.5418, Energy: -54.660002+0.002594j
[2025-07-30 14:11:54] [Iter 3388/4650] R4[1137/2400], Temp: 0.5412, Energy: -54.670125+0.001208j
[2025-07-30 14:12:04] [Iter 3389/4650] R4[1138/2400], Temp: 0.5405, Energy: -54.613392-0.000534j
[2025-07-30 14:12:14] [Iter 3390/4650] R4[1139/2400], Temp: 0.5399, Energy: -54.686359-0.000009j
[2025-07-30 14:12:24] [Iter 3391/4650] R4[1140/2400], Temp: 0.5392, Energy: -54.690500-0.000618j
[2025-07-30 14:12:34] [Iter 3392/4650] R4[1141/2400], Temp: 0.5386, Energy: -54.658599-0.001927j
[2025-07-30 14:12:45] [Iter 3393/4650] R4[1142/2400], Temp: 0.5379, Energy: -54.650191-0.001015j
[2025-07-30 14:12:55] [Iter 3394/4650] R4[1143/2400], Temp: 0.5373, Energy: -54.649336-0.000629j
[2025-07-30 14:13:05] [Iter 3395/4650] R4[1144/2400], Temp: 0.5366, Energy: -54.670591-0.000500j
[2025-07-30 14:13:15] [Iter 3396/4650] R4[1145/2400], Temp: 0.5360, Energy: -54.629272+0.001268j
[2025-07-30 14:13:25] [Iter 3397/4650] R4[1146/2400], Temp: 0.5353, Energy: -54.635258-0.000597j
[2025-07-30 14:13:35] [Iter 3398/4650] R4[1147/2400], Temp: 0.5347, Energy: -54.610298-0.001275j
[2025-07-30 14:13:45] [Iter 3399/4650] R4[1148/2400], Temp: 0.5340, Energy: -54.652051-0.001560j
[2025-07-30 14:13:55] [Iter 3400/4650] R4[1149/2400], Temp: 0.5334, Energy: -54.652548-0.001169j
[2025-07-30 14:14:05] [Iter 3401/4650] R4[1150/2400], Temp: 0.5327, Energy: -54.672231-0.001911j
[2025-07-30 14:14:16] [Iter 3402/4650] R4[1151/2400], Temp: 0.5320, Energy: -54.644856+0.001060j
[2025-07-30 14:14:26] [Iter 3403/4650] R4[1152/2400], Temp: 0.5314, Energy: -54.679564-0.001139j
[2025-07-30 14:14:36] [Iter 3404/4650] R4[1153/2400], Temp: 0.5307, Energy: -54.600665-0.001899j
[2025-07-30 14:14:46] [Iter 3405/4650] R4[1154/2400], Temp: 0.5301, Energy: -54.652623-0.001953j
[2025-07-30 14:14:56] [Iter 3406/4650] R4[1155/2400], Temp: 0.5294, Energy: -54.702083+0.000293j
[2025-07-30 14:15:06] [Iter 3407/4650] R4[1156/2400], Temp: 0.5288, Energy: -54.651056+0.001350j
[2025-07-30 14:15:16] [Iter 3408/4650] R4[1157/2400], Temp: 0.5281, Energy: -54.619859-0.001246j
[2025-07-30 14:15:26] [Iter 3409/4650] R4[1158/2400], Temp: 0.5275, Energy: -54.581614-0.000164j
[2025-07-30 14:15:36] [Iter 3410/4650] R4[1159/2400], Temp: 0.5268, Energy: -54.617019+0.000040j
[2025-07-30 14:15:47] [Iter 3411/4650] R4[1160/2400], Temp: 0.5262, Energy: -54.599613+0.000520j
[2025-07-30 14:15:57] [Iter 3412/4650] R4[1161/2400], Temp: 0.5255, Energy: -54.596885+0.001367j
[2025-07-30 14:16:07] [Iter 3413/4650] R4[1162/2400], Temp: 0.5249, Energy: -54.581759-0.001063j
[2025-07-30 14:16:17] [Iter 3414/4650] R4[1163/2400], Temp: 0.5242, Energy: -54.598127+0.000629j
[2025-07-30 14:16:27] [Iter 3415/4650] R4[1164/2400], Temp: 0.5236, Energy: -54.647909-0.001302j
[2025-07-30 14:16:37] [Iter 3416/4650] R4[1165/2400], Temp: 0.5229, Energy: -54.591409+0.001138j
[2025-07-30 14:16:47] [Iter 3417/4650] R4[1166/2400], Temp: 0.5222, Energy: -54.582984-0.000146j
[2025-07-30 14:16:57] [Iter 3418/4650] R4[1167/2400], Temp: 0.5216, Energy: -54.592797-0.000621j
[2025-07-30 14:17:07] [Iter 3419/4650] R4[1168/2400], Temp: 0.5209, Energy: -54.583994+0.000310j
[2025-07-30 14:17:18] [Iter 3420/4650] R4[1169/2400], Temp: 0.5203, Energy: -54.628947-0.000830j
[2025-07-30 14:17:28] [Iter 3421/4650] R4[1170/2400], Temp: 0.5196, Energy: -54.602082-0.000614j
[2025-07-30 14:17:38] [Iter 3422/4650] R4[1171/2400], Temp: 0.5190, Energy: -54.603670-0.000175j
[2025-07-30 14:17:48] [Iter 3423/4650] R4[1172/2400], Temp: 0.5183, Energy: -54.568137-0.002085j
[2025-07-30 14:17:58] [Iter 3424/4650] R4[1173/2400], Temp: 0.5177, Energy: -54.612173+0.000906j
[2025-07-30 14:18:08] [Iter 3425/4650] R4[1174/2400], Temp: 0.5170, Energy: -54.555490-0.000961j
[2025-07-30 14:18:18] [Iter 3426/4650] R4[1175/2400], Temp: 0.5164, Energy: -54.597149-0.000060j
[2025-07-30 14:18:28] [Iter 3427/4650] R4[1176/2400], Temp: 0.5157, Energy: -54.576105-0.001310j
[2025-07-30 14:18:38] [Iter 3428/4650] R4[1177/2400], Temp: 0.5151, Energy: -54.561729+0.000287j
[2025-07-30 14:18:49] [Iter 3429/4650] R4[1178/2400], Temp: 0.5144, Energy: -54.580628-0.000698j
[2025-07-30 14:18:59] [Iter 3430/4650] R4[1179/2400], Temp: 0.5137, Energy: -54.596160-0.001574j
[2025-07-30 14:19:09] [Iter 3431/4650] R4[1180/2400], Temp: 0.5131, Energy: -54.607029-0.001183j
[2025-07-30 14:19:19] [Iter 3432/4650] R4[1181/2400], Temp: 0.5124, Energy: -54.625481-0.000905j
[2025-07-30 14:19:29] [Iter 3433/4650] R4[1182/2400], Temp: 0.5118, Energy: -54.599356-0.000118j
[2025-07-30 14:19:39] [Iter 3434/4650] R4[1183/2400], Temp: 0.5111, Energy: -54.639808-0.000108j
[2025-07-30 14:19:49] [Iter 3435/4650] R4[1184/2400], Temp: 0.5105, Energy: -54.644226+0.001143j
[2025-07-30 14:19:59] [Iter 3436/4650] R4[1185/2400], Temp: 0.5098, Energy: -54.672626+0.000971j
[2025-07-30 14:20:10] [Iter 3437/4650] R4[1186/2400], Temp: 0.5092, Energy: -54.652707-0.000234j
[2025-07-30 14:20:20] [Iter 3438/4650] R4[1187/2400], Temp: 0.5085, Energy: -54.616310+0.000964j
[2025-07-30 14:20:30] [Iter 3439/4650] R4[1188/2400], Temp: 0.5079, Energy: -54.620899+0.000424j
[2025-07-30 14:20:40] [Iter 3440/4650] R4[1189/2400], Temp: 0.5072, Energy: -54.649344-0.000371j
[2025-07-30 14:20:50] [Iter 3441/4650] R4[1190/2400], Temp: 0.5065, Energy: -54.639844-0.001620j
[2025-07-30 14:21:00] [Iter 3442/4650] R4[1191/2400], Temp: 0.5059, Energy: -54.596112+0.002334j
[2025-07-30 14:21:10] [Iter 3443/4650] R4[1192/2400], Temp: 0.5052, Energy: -54.652932+0.000460j
[2025-07-30 14:21:20] [Iter 3444/4650] R4[1193/2400], Temp: 0.5046, Energy: -54.647172-0.000045j
[2025-07-30 14:21:30] [Iter 3445/4650] R4[1194/2400], Temp: 0.5039, Energy: -54.648306+0.000352j
[2025-07-30 14:21:41] [Iter 3446/4650] R4[1195/2400], Temp: 0.5033, Energy: -54.627978+0.001775j
[2025-07-30 14:21:51] [Iter 3447/4650] R4[1196/2400], Temp: 0.5026, Energy: -54.618127+0.000634j
[2025-07-30 14:22:01] [Iter 3448/4650] R4[1197/2400], Temp: 0.5020, Energy: -54.601771+0.001342j
[2025-07-30 14:22:11] [Iter 3449/4650] R4[1198/2400], Temp: 0.5013, Energy: -54.611875-0.001398j
[2025-07-30 14:22:21] [Iter 3450/4650] R4[1199/2400], Temp: 0.5007, Energy: -54.626891-0.000562j
[2025-07-30 14:22:31] [Iter 3451/4650] R4[1200/2400], Temp: 0.5000, Energy: -54.563595+0.001027j
[2025-07-30 14:22:41] [Iter 3452/4650] R4[1201/2400], Temp: 0.4993, Energy: -54.586205-0.003259j
[2025-07-30 14:22:51] [Iter 3453/4650] R4[1202/2400], Temp: 0.4987, Energy: -54.623491+0.001175j
[2025-07-30 14:23:02] [Iter 3454/4650] R4[1203/2400], Temp: 0.4980, Energy: -54.561304+0.002023j
[2025-07-30 14:23:12] [Iter 3455/4650] R4[1204/2400], Temp: 0.4974, Energy: -54.578438+0.000834j
[2025-07-30 14:23:22] [Iter 3456/4650] R4[1205/2400], Temp: 0.4967, Energy: -54.569630+0.001451j
[2025-07-30 14:23:32] [Iter 3457/4650] R4[1206/2400], Temp: 0.4961, Energy: -54.646059+0.000395j
[2025-07-30 14:23:42] [Iter 3458/4650] R4[1207/2400], Temp: 0.4954, Energy: -54.570606-0.002144j
[2025-07-30 14:23:52] [Iter 3459/4650] R4[1208/2400], Temp: 0.4948, Energy: -54.568041-0.000932j
[2025-07-30 14:24:02] [Iter 3460/4650] R4[1209/2400], Temp: 0.4941, Energy: -54.557198+0.001382j
[2025-07-30 14:24:12] [Iter 3461/4650] R4[1210/2400], Temp: 0.4935, Energy: -54.626359-0.000334j
[2025-07-30 14:24:22] [Iter 3462/4650] R4[1211/2400], Temp: 0.4928, Energy: -54.619300-0.000402j
[2025-07-30 14:24:33] [Iter 3463/4650] R4[1212/2400], Temp: 0.4921, Energy: -54.654147+0.000308j
[2025-07-30 14:24:43] [Iter 3464/4650] R4[1213/2400], Temp: 0.4915, Energy: -54.583895-0.000168j
[2025-07-30 14:24:53] [Iter 3465/4650] R4[1214/2400], Temp: 0.4908, Energy: -54.560826+0.000978j
[2025-07-30 14:25:03] [Iter 3466/4650] R4[1215/2400], Temp: 0.4902, Energy: -54.560938+0.000221j
[2025-07-30 14:25:13] [Iter 3467/4650] R4[1216/2400], Temp: 0.4895, Energy: -54.609291+0.000594j
[2025-07-30 14:25:23] [Iter 3468/4650] R4[1217/2400], Temp: 0.4889, Energy: -54.543747+0.001104j
[2025-07-30 14:25:33] [Iter 3469/4650] R4[1218/2400], Temp: 0.4882, Energy: -54.547538-0.000386j
[2025-07-30 14:25:43] [Iter 3470/4650] R4[1219/2400], Temp: 0.4876, Energy: -54.630667+0.000541j
[2025-07-30 14:25:53] [Iter 3471/4650] R4[1220/2400], Temp: 0.4869, Energy: -54.670105-0.000609j
[2025-07-30 14:26:04] [Iter 3472/4650] R4[1221/2400], Temp: 0.4863, Energy: -54.654848+0.000317j
[2025-07-30 14:26:14] [Iter 3473/4650] R4[1222/2400], Temp: 0.4856, Energy: -54.595263+0.000416j
[2025-07-30 14:26:24] [Iter 3474/4650] R4[1223/2400], Temp: 0.4849, Energy: -54.712783+0.000744j
[2025-07-30 14:26:34] [Iter 3475/4650] R4[1224/2400], Temp: 0.4843, Energy: -54.678368+0.001990j
[2025-07-30 14:26:44] [Iter 3476/4650] R4[1225/2400], Temp: 0.4836, Energy: -54.663891+0.000315j
[2025-07-30 14:26:54] [Iter 3477/4650] R4[1226/2400], Temp: 0.4830, Energy: -54.609169+0.000467j
[2025-07-30 14:27:04] [Iter 3478/4650] R4[1227/2400], Temp: 0.4823, Energy: -54.653655+0.003688j
[2025-07-30 14:27:15] [Iter 3479/4650] R4[1228/2400], Temp: 0.4817, Energy: -54.632371+0.000261j
[2025-07-30 14:27:25] [Iter 3480/4650] R4[1229/2400], Temp: 0.4810, Energy: -54.638152-0.001305j
[2025-07-30 14:27:35] [Iter 3481/4650] R4[1230/2400], Temp: 0.4804, Energy: -54.608696-0.001328j
[2025-07-30 14:27:45] [Iter 3482/4650] R4[1231/2400], Temp: 0.4797, Energy: -54.635730+0.000255j
[2025-07-30 14:27:55] [Iter 3483/4650] R4[1232/2400], Temp: 0.4791, Energy: -54.653698+0.003030j
[2025-07-30 14:28:05] [Iter 3484/4650] R4[1233/2400], Temp: 0.4784, Energy: -54.702268-0.000325j
[2025-07-30 14:28:15] [Iter 3485/4650] R4[1234/2400], Temp: 0.4778, Energy: -54.662371-0.001085j
[2025-07-30 14:28:25] [Iter 3486/4650] R4[1235/2400], Temp: 0.4771, Energy: -54.641026+0.000491j
[2025-07-30 14:28:36] [Iter 3487/4650] R4[1236/2400], Temp: 0.4764, Energy: -54.601398-0.001341j
[2025-07-30 14:28:46] [Iter 3488/4650] R4[1237/2400], Temp: 0.4758, Energy: -54.570026-0.001152j
[2025-07-30 14:28:56] [Iter 3489/4650] R4[1238/2400], Temp: 0.4751, Energy: -54.577196+0.000089j
[2025-07-30 14:29:06] [Iter 3490/4650] R4[1239/2400], Temp: 0.4745, Energy: -54.576155+0.002194j
[2025-07-30 14:29:16] [Iter 3491/4650] R4[1240/2400], Temp: 0.4738, Energy: -54.659746+0.001213j
[2025-07-30 14:29:26] [Iter 3492/4650] R4[1241/2400], Temp: 0.4732, Energy: -54.705521-0.001480j
[2025-07-30 14:29:36] [Iter 3493/4650] R4[1242/2400], Temp: 0.4725, Energy: -54.719445-0.000625j
[2025-07-30 14:29:46] [Iter 3494/4650] R4[1243/2400], Temp: 0.4719, Energy: -54.729034-0.000507j
[2025-07-30 14:29:56] [Iter 3495/4650] R4[1244/2400], Temp: 0.4712, Energy: -54.645125+0.001184j
[2025-07-30 14:30:06] [Iter 3496/4650] R4[1245/2400], Temp: 0.4706, Energy: -54.620498+0.000264j
[2025-07-30 14:30:17] [Iter 3497/4650] R4[1246/2400], Temp: 0.4699, Energy: -54.628473+0.001262j
[2025-07-30 14:30:27] [Iter 3498/4650] R4[1247/2400], Temp: 0.4693, Energy: -54.603918+0.000306j
[2025-07-30 14:30:37] [Iter 3499/4650] R4[1248/2400], Temp: 0.4686, Energy: -54.654533+0.002474j
[2025-07-30 14:30:47] [Iter 3500/4650] R4[1249/2400], Temp: 0.4680, Energy: -54.605040-0.000448j
[2025-07-30 14:30:47] ✓ Checkpoint saved: checkpoint_iter_003500.pkl
[2025-07-30 14:30:57] [Iter 3501/4650] R4[1250/2400], Temp: 0.4673, Energy: -54.657078+0.000561j
[2025-07-30 14:31:07] [Iter 3502/4650] R4[1251/2400], Temp: 0.4666, Energy: -54.661077-0.003136j
[2025-07-30 14:31:17] [Iter 3503/4650] R4[1252/2400], Temp: 0.4660, Energy: -54.637756-0.000759j
[2025-07-30 14:31:27] [Iter 3504/4650] R4[1253/2400], Temp: 0.4653, Energy: -54.681529-0.000519j
[2025-07-30 14:31:37] [Iter 3505/4650] R4[1254/2400], Temp: 0.4647, Energy: -54.713156+0.000158j
[2025-07-30 14:31:48] [Iter 3506/4650] R4[1255/2400], Temp: 0.4640, Energy: -54.678526-0.000002j
[2025-07-30 14:31:58] [Iter 3507/4650] R4[1256/2400], Temp: 0.4634, Energy: -54.676360+0.000848j
[2025-07-30 14:32:08] [Iter 3508/4650] R4[1257/2400], Temp: 0.4627, Energy: -54.644822+0.000612j
[2025-07-30 14:32:18] [Iter 3509/4650] R4[1258/2400], Temp: 0.4621, Energy: -54.679098-0.000701j
[2025-07-30 14:32:28] [Iter 3510/4650] R4[1259/2400], Temp: 0.4614, Energy: -54.618708-0.002985j
[2025-07-30 14:32:38] [Iter 3511/4650] R4[1260/2400], Temp: 0.4608, Energy: -54.637552-0.000312j
[2025-07-30 14:32:48] [Iter 3512/4650] R4[1261/2400], Temp: 0.4601, Energy: -54.628422-0.001585j
[2025-07-30 14:32:58] [Iter 3513/4650] R4[1262/2400], Temp: 0.4595, Energy: -54.641359+0.001716j
[2025-07-30 14:33:08] [Iter 3514/4650] R4[1263/2400], Temp: 0.4588, Energy: -54.658765+0.001917j
[2025-07-30 14:33:19] [Iter 3515/4650] R4[1264/2400], Temp: 0.4582, Energy: -54.661131+0.002249j
[2025-07-30 14:33:29] [Iter 3516/4650] R4[1265/2400], Temp: 0.4575, Energy: -54.615094-0.000582j
[2025-07-30 14:33:39] [Iter 3517/4650] R4[1266/2400], Temp: 0.4569, Energy: -54.645600-0.001889j
[2025-07-30 14:33:49] [Iter 3518/4650] R4[1267/2400], Temp: 0.4562, Energy: -54.623013-0.001097j
[2025-07-30 14:33:59] [Iter 3519/4650] R4[1268/2400], Temp: 0.4556, Energy: -54.521163+0.000313j
[2025-07-30 14:34:09] [Iter 3520/4650] R4[1269/2400], Temp: 0.4549, Energy: -54.594186-0.000648j
[2025-07-30 14:34:19] [Iter 3521/4650] R4[1270/2400], Temp: 0.4542, Energy: -54.587449+0.001716j
[2025-07-30 14:34:29] [Iter 3522/4650] R4[1271/2400], Temp: 0.4536, Energy: -54.540064-0.000637j
[2025-07-30 14:34:40] [Iter 3523/4650] R4[1272/2400], Temp: 0.4529, Energy: -54.576711+0.000975j
[2025-07-30 14:34:50] [Iter 3524/4650] R4[1273/2400], Temp: 0.4523, Energy: -54.562203-0.001287j
[2025-07-30 14:35:00] [Iter 3525/4650] R4[1274/2400], Temp: 0.4516, Energy: -54.574263+0.001021j
[2025-07-30 14:35:10] [Iter 3526/4650] R4[1275/2400], Temp: 0.4510, Energy: -54.584666+0.000867j
[2025-07-30 14:35:20] [Iter 3527/4650] R4[1276/2400], Temp: 0.4503, Energy: -54.618366+0.001005j
[2025-07-30 14:35:30] [Iter 3528/4650] R4[1277/2400], Temp: 0.4497, Energy: -54.635010-0.001249j
[2025-07-30 14:35:40] [Iter 3529/4650] R4[1278/2400], Temp: 0.4490, Energy: -54.635480-0.000925j
[2025-07-30 14:35:50] [Iter 3530/4650] R4[1279/2400], Temp: 0.4484, Energy: -54.603863+0.000416j
[2025-07-30 14:36:00] [Iter 3531/4650] R4[1280/2400], Temp: 0.4477, Energy: -54.645479-0.001497j
[2025-07-30 14:36:11] [Iter 3532/4650] R4[1281/2400], Temp: 0.4471, Energy: -54.660121+0.002421j
[2025-07-30 14:36:21] [Iter 3533/4650] R4[1282/2400], Temp: 0.4464, Energy: -54.597394+0.000757j
[2025-07-30 14:36:31] [Iter 3534/4650] R4[1283/2400], Temp: 0.4458, Energy: -54.637421+0.001342j
[2025-07-30 14:36:41] [Iter 3535/4650] R4[1284/2400], Temp: 0.4451, Energy: -54.541869-0.002608j
[2025-07-30 14:36:51] [Iter 3536/4650] R4[1285/2400], Temp: 0.4445, Energy: -54.600902+0.000495j
[2025-07-30 14:37:01] [Iter 3537/4650] R4[1286/2400], Temp: 0.4438, Energy: -54.612922-0.002507j
[2025-07-30 14:37:11] [Iter 3538/4650] R4[1287/2400], Temp: 0.4432, Energy: -54.628440-0.000524j
[2025-07-30 14:37:21] [Iter 3539/4650] R4[1288/2400], Temp: 0.4425, Energy: -54.624058-0.000421j
[2025-07-30 14:37:31] [Iter 3540/4650] R4[1289/2400], Temp: 0.4419, Energy: -54.586478-0.000572j
[2025-07-30 14:37:42] [Iter 3541/4650] R4[1290/2400], Temp: 0.4412, Energy: -54.603528+0.000421j
[2025-07-30 14:37:52] [Iter 3542/4650] R4[1291/2400], Temp: 0.4406, Energy: -54.648625-0.000543j
[2025-07-30 14:38:02] [Iter 3543/4650] R4[1292/2400], Temp: 0.4399, Energy: -54.633123+0.002472j
[2025-07-30 14:38:12] [Iter 3544/4650] R4[1293/2400], Temp: 0.4393, Energy: -54.683236+0.000865j
[2025-07-30 14:38:22] [Iter 3545/4650] R4[1294/2400], Temp: 0.4386, Energy: -54.633401-0.000464j
[2025-07-30 14:38:32] [Iter 3546/4650] R4[1295/2400], Temp: 0.4380, Energy: -54.586825+0.000114j
[2025-07-30 14:38:42] [Iter 3547/4650] R4[1296/2400], Temp: 0.4373, Energy: -54.562619-0.001756j
[2025-07-30 14:38:52] [Iter 3548/4650] R4[1297/2400], Temp: 0.4367, Energy: -54.622657-0.000086j
[2025-07-30 14:39:03] [Iter 3549/4650] R4[1298/2400], Temp: 0.4360, Energy: -54.662024-0.001465j
[2025-07-30 14:39:13] [Iter 3550/4650] R4[1299/2400], Temp: 0.4354, Energy: -54.633214+0.001034j
[2025-07-30 14:39:23] [Iter 3551/4650] R4[1300/2400], Temp: 0.4347, Energy: -54.637410+0.000128j
[2025-07-30 14:39:33] [Iter 3552/4650] R4[1301/2400], Temp: 0.4341, Energy: -54.652993+0.000786j
[2025-07-30 14:39:43] [Iter 3553/4650] R4[1302/2400], Temp: 0.4334, Energy: -54.656366-0.000440j
[2025-07-30 14:39:53] [Iter 3554/4650] R4[1303/2400], Temp: 0.4328, Energy: -54.620244-0.000135j
[2025-07-30 14:40:03] [Iter 3555/4650] R4[1304/2400], Temp: 0.4321, Energy: -54.614735+0.000924j
[2025-07-30 14:40:13] [Iter 3556/4650] R4[1305/2400], Temp: 0.4315, Energy: -54.558320-0.002436j
[2025-07-30 14:40:23] [Iter 3557/4650] R4[1306/2400], Temp: 0.4308, Energy: -54.594029-0.000826j
[2025-07-30 14:40:34] [Iter 3558/4650] R4[1307/2400], Temp: 0.4302, Energy: -54.607730-0.000446j
[2025-07-30 14:40:44] [Iter 3559/4650] R4[1308/2400], Temp: 0.4295, Energy: -54.671973+0.000845j
[2025-07-30 14:40:54] [Iter 3560/4650] R4[1309/2400], Temp: 0.4289, Energy: -54.621792+0.001931j
[2025-07-30 14:41:04] [Iter 3561/4650] R4[1310/2400], Temp: 0.4283, Energy: -54.607884+0.000779j
[2025-07-30 14:41:14] [Iter 3562/4650] R4[1311/2400], Temp: 0.4276, Energy: -54.594290-0.000040j
[2025-07-30 14:41:24] [Iter 3563/4650] R4[1312/2400], Temp: 0.4270, Energy: -54.578371+0.000700j
[2025-07-30 14:41:34] [Iter 3564/4650] R4[1313/2400], Temp: 0.4263, Energy: -54.669862-0.000328j
[2025-07-30 14:41:44] [Iter 3565/4650] R4[1314/2400], Temp: 0.4257, Energy: -54.628103+0.001225j
[2025-07-30 14:41:55] [Iter 3566/4650] R4[1315/2400], Temp: 0.4250, Energy: -54.590159-0.000848j
[2025-07-30 14:42:05] [Iter 3567/4650] R4[1316/2400], Temp: 0.4244, Energy: -54.556163+0.003904j
[2025-07-30 14:42:15] [Iter 3568/4650] R4[1317/2400], Temp: 0.4237, Energy: -54.535968-0.000685j
[2025-07-30 14:42:25] [Iter 3569/4650] R4[1318/2400], Temp: 0.4231, Energy: -54.607014+0.001281j
[2025-07-30 14:42:35] [Iter 3570/4650] R4[1319/2400], Temp: 0.4224, Energy: -54.555988-0.001649j
[2025-07-30 14:42:45] [Iter 3571/4650] R4[1320/2400], Temp: 0.4218, Energy: -54.616925-0.000753j
[2025-07-30 14:42:55] [Iter 3572/4650] R4[1321/2400], Temp: 0.4211, Energy: -54.633847+0.001019j
[2025-07-30 14:43:05] [Iter 3573/4650] R4[1322/2400], Temp: 0.4205, Energy: -54.655443-0.000687j
[2025-07-30 14:43:15] [Iter 3574/4650] R4[1323/2400], Temp: 0.4198, Energy: -54.628640+0.002844j
[2025-07-30 14:43:26] [Iter 3575/4650] R4[1324/2400], Temp: 0.4192, Energy: -54.607389-0.000379j
[2025-07-30 14:43:36] [Iter 3576/4650] R4[1325/2400], Temp: 0.4186, Energy: -54.617771-0.001535j
[2025-07-30 14:43:46] [Iter 3577/4650] R4[1326/2400], Temp: 0.4179, Energy: -54.625420-0.000707j
[2025-07-30 14:43:56] [Iter 3578/4650] R4[1327/2400], Temp: 0.4173, Energy: -54.656673+0.000922j
[2025-07-30 14:44:06] [Iter 3579/4650] R4[1328/2400], Temp: 0.4166, Energy: -54.655255-0.001762j
[2025-07-30 14:44:16] [Iter 3580/4650] R4[1329/2400], Temp: 0.4160, Energy: -54.634318-0.002267j
[2025-07-30 14:44:26] [Iter 3581/4650] R4[1330/2400], Temp: 0.4153, Energy: -54.641644+0.001055j
[2025-07-30 14:44:36] [Iter 3582/4650] R4[1331/2400], Temp: 0.4147, Energy: -54.668935+0.002282j
[2025-07-30 14:44:46] [Iter 3583/4650] R4[1332/2400], Temp: 0.4140, Energy: -54.665661-0.002285j
[2025-07-30 14:44:57] [Iter 3584/4650] R4[1333/2400], Temp: 0.4134, Energy: -54.626226-0.001033j
[2025-07-30 14:45:07] [Iter 3585/4650] R4[1334/2400], Temp: 0.4127, Energy: -54.634006-0.002582j
[2025-07-30 14:45:17] [Iter 3586/4650] R4[1335/2400], Temp: 0.4121, Energy: -54.594925+0.001121j
[2025-07-30 14:45:27] [Iter 3587/4650] R4[1336/2400], Temp: 0.4115, Energy: -54.632204-0.001043j
[2025-07-30 14:45:37] [Iter 3588/4650] R4[1337/2400], Temp: 0.4108, Energy: -54.621224+0.003230j
[2025-07-30 14:45:47] [Iter 3589/4650] R4[1338/2400], Temp: 0.4102, Energy: -54.619028+0.004357j
[2025-07-30 14:45:57] [Iter 3590/4650] R4[1339/2400], Temp: 0.4095, Energy: -54.656941-0.000593j
[2025-07-30 14:46:07] [Iter 3591/4650] R4[1340/2400], Temp: 0.4089, Energy: -54.706763+0.000518j
[2025-07-30 14:46:17] [Iter 3592/4650] R4[1341/2400], Temp: 0.4082, Energy: -54.623427-0.000268j
[2025-07-30 14:46:28] [Iter 3593/4650] R4[1342/2400], Temp: 0.4076, Energy: -54.664513-0.000580j
[2025-07-30 14:46:38] [Iter 3594/4650] R4[1343/2400], Temp: 0.4070, Energy: -54.647753-0.001294j
[2025-07-30 14:46:48] [Iter 3595/4650] R4[1344/2400], Temp: 0.4063, Energy: -54.608169+0.000375j
[2025-07-30 14:46:58] [Iter 3596/4650] R4[1345/2400], Temp: 0.4057, Energy: -54.655456-0.000446j
[2025-07-30 14:47:08] [Iter 3597/4650] R4[1346/2400], Temp: 0.4050, Energy: -54.614326-0.000418j
[2025-07-30 14:47:18] [Iter 3598/4650] R4[1347/2400], Temp: 0.4044, Energy: -54.640259+0.000923j
[2025-07-30 14:47:28] [Iter 3599/4650] R4[1348/2400], Temp: 0.4037, Energy: -54.683356-0.000555j
[2025-07-30 14:47:38] [Iter 3600/4650] R4[1349/2400], Temp: 0.4031, Energy: -54.724330-0.000221j
[2025-07-30 14:47:48] [Iter 3601/4650] R4[1350/2400], Temp: 0.4025, Energy: -54.677519+0.001362j
[2025-07-30 14:47:59] [Iter 3602/4650] R4[1351/2400], Temp: 0.4018, Energy: -54.731005+0.002821j
[2025-07-30 14:48:09] [Iter 3603/4650] R4[1352/2400], Temp: 0.4012, Energy: -54.700192+0.001278j
[2025-07-30 14:48:19] [Iter 3604/4650] R4[1353/2400], Temp: 0.4005, Energy: -54.634433+0.001252j
[2025-07-30 14:48:29] [Iter 3605/4650] R4[1354/2400], Temp: 0.3999, Energy: -54.660924+0.000319j
[2025-07-30 14:48:39] [Iter 3606/4650] R4[1355/2400], Temp: 0.3992, Energy: -54.648132+0.001596j
[2025-07-30 14:48:49] [Iter 3607/4650] R4[1356/2400], Temp: 0.3986, Energy: -54.675454+0.000200j
[2025-07-30 14:48:59] [Iter 3608/4650] R4[1357/2400], Temp: 0.3980, Energy: -54.630327+0.000188j
[2025-07-30 14:49:10] [Iter 3609/4650] R4[1358/2400], Temp: 0.3973, Energy: -54.665899-0.002125j
[2025-07-30 14:49:20] [Iter 3610/4650] R4[1359/2400], Temp: 0.3967, Energy: -54.684402+0.000710j
[2025-07-30 14:49:30] [Iter 3611/4650] R4[1360/2400], Temp: 0.3960, Energy: -54.655612+0.001094j
[2025-07-30 14:49:40] [Iter 3612/4650] R4[1361/2400], Temp: 0.3954, Energy: -54.607441+0.000499j
[2025-07-30 14:49:50] [Iter 3613/4650] R4[1362/2400], Temp: 0.3948, Energy: -54.610046-0.002080j
[2025-07-30 14:50:00] [Iter 3614/4650] R4[1363/2400], Temp: 0.3941, Energy: -54.647508-0.002037j
[2025-07-30 14:50:10] [Iter 3615/4650] R4[1364/2400], Temp: 0.3935, Energy: -54.642436-0.002349j
[2025-07-30 14:50:20] [Iter 3616/4650] R4[1365/2400], Temp: 0.3928, Energy: -54.641760-0.001589j
[2025-07-30 14:50:30] [Iter 3617/4650] R4[1366/2400], Temp: 0.3922, Energy: -54.689433-0.000173j
[2025-07-30 14:50:41] [Iter 3618/4650] R4[1367/2400], Temp: 0.3916, Energy: -54.668731+0.001303j
[2025-07-30 14:50:51] [Iter 3619/4650] R4[1368/2400], Temp: 0.3909, Energy: -54.595343-0.002193j
[2025-07-30 14:51:01] [Iter 3620/4650] R4[1369/2400], Temp: 0.3903, Energy: -54.675704+0.000268j
[2025-07-30 14:51:11] [Iter 3621/4650] R4[1370/2400], Temp: 0.3897, Energy: -54.637442+0.001009j
[2025-07-30 14:51:21] [Iter 3622/4650] R4[1371/2400], Temp: 0.3890, Energy: -54.604234-0.000752j
[2025-07-30 14:51:31] [Iter 3623/4650] R4[1372/2400], Temp: 0.3884, Energy: -54.585706-0.000265j
[2025-07-30 14:51:41] [Iter 3624/4650] R4[1373/2400], Temp: 0.3877, Energy: -54.656276-0.000709j
[2025-07-30 14:51:51] [Iter 3625/4650] R4[1374/2400], Temp: 0.3871, Energy: -54.563976-0.012302j
[2025-07-30 14:52:02] [Iter 3626/4650] R4[1375/2400], Temp: 0.3865, Energy: -54.603615+0.001642j
[2025-07-30 14:52:12] [Iter 3627/4650] R4[1376/2400], Temp: 0.3858, Energy: -54.583915-0.000309j
[2025-07-30 14:52:22] [Iter 3628/4650] R4[1377/2400], Temp: 0.3852, Energy: -54.615288-0.000769j
[2025-07-30 14:52:32] [Iter 3629/4650] R4[1378/2400], Temp: 0.3846, Energy: -54.555340+0.003257j
[2025-07-30 14:52:42] [Iter 3630/4650] R4[1379/2400], Temp: 0.3839, Energy: -54.631959+0.000137j
[2025-07-30 14:52:52] [Iter 3631/4650] R4[1380/2400], Temp: 0.3833, Energy: -54.625110-0.000676j
[2025-07-30 14:53:02] [Iter 3632/4650] R4[1381/2400], Temp: 0.3826, Energy: -54.617050-0.001723j
[2025-07-30 14:53:12] [Iter 3633/4650] R4[1382/2400], Temp: 0.3820, Energy: -54.686563+0.000484j
[2025-07-30 14:53:22] [Iter 3634/4650] R4[1383/2400], Temp: 0.3814, Energy: -54.671007+0.000269j
[2025-07-30 14:53:32] [Iter 3635/4650] R4[1384/2400], Temp: 0.3807, Energy: -54.671791+0.000448j
[2025-07-30 14:53:43] [Iter 3636/4650] R4[1385/2400], Temp: 0.3801, Energy: -54.685471+0.000089j
[2025-07-30 14:53:53] [Iter 3637/4650] R4[1386/2400], Temp: 0.3795, Energy: -54.701918+0.000380j
[2025-07-30 14:54:03] [Iter 3638/4650] R4[1387/2400], Temp: 0.3788, Energy: -54.643659-0.002583j
[2025-07-30 14:54:13] [Iter 3639/4650] R4[1388/2400], Temp: 0.3782, Energy: -54.658216-0.000558j
[2025-07-30 14:54:23] [Iter 3640/4650] R4[1389/2400], Temp: 0.3776, Energy: -54.623584+0.002007j
[2025-07-30 14:54:33] [Iter 3641/4650] R4[1390/2400], Temp: 0.3769, Energy: -54.627188-0.000168j
[2025-07-30 14:54:43] [Iter 3642/4650] R4[1391/2400], Temp: 0.3763, Energy: -54.640242-0.000049j
[2025-07-30 14:54:53] [Iter 3643/4650] R4[1392/2400], Temp: 0.3757, Energy: -54.633867+0.000662j
[2025-07-30 14:55:04] [Iter 3644/4650] R4[1393/2400], Temp: 0.3750, Energy: -54.620400+0.000594j
[2025-07-30 14:55:14] [Iter 3645/4650] R4[1394/2400], Temp: 0.3744, Energy: -54.626814+0.001631j
[2025-07-30 14:55:24] [Iter 3646/4650] R4[1395/2400], Temp: 0.3738, Energy: -54.653872-0.000181j
[2025-07-30 14:55:34] [Iter 3647/4650] R4[1396/2400], Temp: 0.3731, Energy: -54.629236-0.001795j
[2025-07-30 14:55:44] [Iter 3648/4650] R4[1397/2400], Temp: 0.3725, Energy: -54.667834-0.001325j
[2025-07-30 14:55:54] [Iter 3649/4650] R4[1398/2400], Temp: 0.3719, Energy: -54.659233+0.000683j
[2025-07-30 14:56:04] [Iter 3650/4650] R4[1399/2400], Temp: 0.3712, Energy: -54.603072+0.000287j
[2025-07-30 14:56:14] [Iter 3651/4650] R4[1400/2400], Temp: 0.3706, Energy: -54.584402-0.001070j
[2025-07-30 14:56:25] [Iter 3652/4650] R4[1401/2400], Temp: 0.3700, Energy: -54.632780-0.002315j
[2025-07-30 14:56:35] [Iter 3653/4650] R4[1402/2400], Temp: 0.3693, Energy: -54.603188+0.002597j
[2025-07-30 14:56:45] [Iter 3654/4650] R4[1403/2400], Temp: 0.3687, Energy: -54.626928-0.000295j
[2025-07-30 14:56:55] [Iter 3655/4650] R4[1404/2400], Temp: 0.3681, Energy: -54.617162-0.000119j
[2025-07-30 14:57:05] [Iter 3656/4650] R4[1405/2400], Temp: 0.3674, Energy: -54.608979+0.000725j
[2025-07-30 14:57:15] [Iter 3657/4650] R4[1406/2400], Temp: 0.3668, Energy: -54.638964-0.000842j
[2025-07-30 14:57:25] [Iter 3658/4650] R4[1407/2400], Temp: 0.3662, Energy: -54.651906+0.000583j
[2025-07-30 14:57:35] [Iter 3659/4650] R4[1408/2400], Temp: 0.3655, Energy: -54.695337+0.000648j
[2025-07-30 14:57:45] [Iter 3660/4650] R4[1409/2400], Temp: 0.3649, Energy: -54.699309-0.000682j
[2025-07-30 14:57:56] [Iter 3661/4650] R4[1410/2400], Temp: 0.3643, Energy: -54.636038-0.000071j
[2025-07-30 14:58:06] [Iter 3662/4650] R4[1411/2400], Temp: 0.3636, Energy: -54.670579-0.000089j
[2025-07-30 14:58:16] [Iter 3663/4650] R4[1412/2400], Temp: 0.3630, Energy: -54.698017+0.000753j
[2025-07-30 14:58:26] [Iter 3664/4650] R4[1413/2400], Temp: 0.3624, Energy: -54.671180+0.001322j
[2025-07-30 14:58:36] [Iter 3665/4650] R4[1414/2400], Temp: 0.3618, Energy: -54.610375-0.001634j
[2025-07-30 14:58:46] [Iter 3666/4650] R4[1415/2400], Temp: 0.3611, Energy: -54.664498+0.001075j
[2025-07-30 14:58:56] [Iter 3667/4650] R4[1416/2400], Temp: 0.3605, Energy: -54.604560+0.001206j
[2025-07-30 14:59:06] [Iter 3668/4650] R4[1417/2400], Temp: 0.3599, Energy: -54.623806-0.000139j
[2025-07-30 14:59:16] [Iter 3669/4650] R4[1418/2400], Temp: 0.3592, Energy: -54.690274+0.000908j
[2025-07-30 14:59:26] [Iter 3670/4650] R4[1419/2400], Temp: 0.3586, Energy: -54.706200-0.001199j
[2025-07-30 14:59:37] [Iter 3671/4650] R4[1420/2400], Temp: 0.3580, Energy: -54.688507-0.000346j
[2025-07-30 14:59:47] [Iter 3672/4650] R4[1421/2400], Temp: 0.3574, Energy: -54.717642+0.000471j
[2025-07-30 14:59:57] [Iter 3673/4650] R4[1422/2400], Temp: 0.3567, Energy: -54.706801+0.000887j
[2025-07-30 15:00:07] [Iter 3674/4650] R4[1423/2400], Temp: 0.3561, Energy: -54.706161+0.001800j
[2025-07-30 15:00:17] [Iter 3675/4650] R4[1424/2400], Temp: 0.3555, Energy: -54.626704-0.000423j
[2025-07-30 15:00:27] [Iter 3676/4650] R4[1425/2400], Temp: 0.3549, Energy: -54.623543+0.000142j
[2025-07-30 15:00:37] [Iter 3677/4650] R4[1426/2400], Temp: 0.3542, Energy: -54.631974-0.001036j
[2025-07-30 15:00:47] [Iter 3678/4650] R4[1427/2400], Temp: 0.3536, Energy: -54.625436-0.002928j
[2025-07-30 15:00:57] [Iter 3679/4650] R4[1428/2400], Temp: 0.3530, Energy: -54.585955-0.002727j
[2025-07-30 15:01:08] [Iter 3680/4650] R4[1429/2400], Temp: 0.3524, Energy: -54.664865-0.001010j
[2025-07-30 15:01:18] [Iter 3681/4650] R4[1430/2400], Temp: 0.3517, Energy: -54.658347-0.002366j
[2025-07-30 15:01:28] [Iter 3682/4650] R4[1431/2400], Temp: 0.3511, Energy: -54.666594-0.001044j
[2025-07-30 15:01:38] [Iter 3683/4650] R4[1432/2400], Temp: 0.3505, Energy: -54.671035+0.000101j
[2025-07-30 15:01:48] [Iter 3684/4650] R4[1433/2400], Temp: 0.3499, Energy: -54.675150+0.000701j
[2025-07-30 15:01:58] [Iter 3685/4650] R4[1434/2400], Temp: 0.3492, Energy: -54.686761+0.001278j
[2025-07-30 15:02:08] [Iter 3686/4650] R4[1435/2400], Temp: 0.3486, Energy: -54.587519-0.000901j
[2025-07-30 15:02:18] [Iter 3687/4650] R4[1436/2400], Temp: 0.3480, Energy: -54.607179+0.001097j
[2025-07-30 15:02:28] [Iter 3688/4650] R4[1437/2400], Temp: 0.3474, Energy: -54.634335+0.000208j
[2025-07-30 15:02:39] [Iter 3689/4650] R4[1438/2400], Temp: 0.3467, Energy: -54.639877-0.000217j
[2025-07-30 15:02:49] [Iter 3690/4650] R4[1439/2400], Temp: 0.3461, Energy: -54.623226-0.001551j
[2025-07-30 15:02:59] [Iter 3691/4650] R4[1440/2400], Temp: 0.3455, Energy: -54.689378+0.002107j
[2025-07-30 15:03:09] [Iter 3692/4650] R4[1441/2400], Temp: 0.3449, Energy: -54.625151-0.002057j
[2025-07-30 15:03:19] [Iter 3693/4650] R4[1442/2400], Temp: 0.3442, Energy: -54.614789+0.000472j
[2025-07-30 15:03:29] [Iter 3694/4650] R4[1443/2400], Temp: 0.3436, Energy: -54.622878+0.000635j
[2025-07-30 15:03:39] [Iter 3695/4650] R4[1444/2400], Temp: 0.3430, Energy: -54.609562-0.000212j
[2025-07-30 15:03:49] [Iter 3696/4650] R4[1445/2400], Temp: 0.3424, Energy: -54.597748-0.000141j
[2025-07-30 15:03:59] [Iter 3697/4650] R4[1446/2400], Temp: 0.3418, Energy: -54.591433-0.000962j
[2025-07-30 15:04:10] [Iter 3698/4650] R4[1447/2400], Temp: 0.3411, Energy: -54.605039+0.001779j
[2025-07-30 15:04:20] [Iter 3699/4650] R4[1448/2400], Temp: 0.3405, Energy: -54.630955+0.001525j
[2025-07-30 15:04:30] [Iter 3700/4650] R4[1449/2400], Temp: 0.3399, Energy: -54.593758-0.001914j
[2025-07-30 15:04:40] [Iter 3701/4650] R4[1450/2400], Temp: 0.3393, Energy: -54.642589+0.001082j
[2025-07-30 15:04:50] [Iter 3702/4650] R4[1451/2400], Temp: 0.3387, Energy: -54.631092+0.002921j
[2025-07-30 15:05:00] [Iter 3703/4650] R4[1452/2400], Temp: 0.3380, Energy: -54.592855-0.000257j
[2025-07-30 15:05:10] [Iter 3704/4650] R4[1453/2400], Temp: 0.3374, Energy: -54.648089+0.003047j
[2025-07-30 15:05:20] [Iter 3705/4650] R4[1454/2400], Temp: 0.3368, Energy: -54.623612+0.000517j
[2025-07-30 15:05:31] [Iter 3706/4650] R4[1455/2400], Temp: 0.3362, Energy: -54.606738-0.000307j
[2025-07-30 15:05:41] [Iter 3707/4650] R4[1456/2400], Temp: 0.3356, Energy: -54.642888-0.000099j
[2025-07-30 15:05:51] [Iter 3708/4650] R4[1457/2400], Temp: 0.3349, Energy: -54.633115+0.000575j
[2025-07-30 15:06:01] [Iter 3709/4650] R4[1458/2400], Temp: 0.3343, Energy: -54.639793-0.000565j
[2025-07-30 15:06:11] [Iter 3710/4650] R4[1459/2400], Temp: 0.3337, Energy: -54.604847+0.000329j
[2025-07-30 15:06:21] [Iter 3711/4650] R4[1460/2400], Temp: 0.3331, Energy: -54.652057+0.000778j
[2025-07-30 15:06:31] [Iter 3712/4650] R4[1461/2400], Temp: 0.3325, Energy: -54.603430-0.000970j
[2025-07-30 15:06:41] [Iter 3713/4650] R4[1462/2400], Temp: 0.3319, Energy: -54.592999-0.004095j
[2025-07-30 15:06:51] [Iter 3714/4650] R4[1463/2400], Temp: 0.3312, Energy: -54.681805-0.002608j
[2025-07-30 15:07:02] [Iter 3715/4650] R4[1464/2400], Temp: 0.3306, Energy: -54.620807+0.000463j
[2025-07-30 15:07:12] [Iter 3716/4650] R4[1465/2400], Temp: 0.3300, Energy: -54.643422-0.002481j
[2025-07-30 15:07:22] [Iter 3717/4650] R4[1466/2400], Temp: 0.3294, Energy: -54.692428+0.002934j
[2025-07-30 15:07:32] [Iter 3718/4650] R4[1467/2400], Temp: 0.3288, Energy: -54.679779+0.001932j
[2025-07-30 15:07:42] [Iter 3719/4650] R4[1468/2400], Temp: 0.3282, Energy: -54.660551+0.001019j
[2025-07-30 15:07:52] [Iter 3720/4650] R4[1469/2400], Temp: 0.3276, Energy: -54.609033-0.000702j
[2025-07-30 15:08:02] [Iter 3721/4650] R4[1470/2400], Temp: 0.3269, Energy: -54.603940-0.002514j
[2025-07-30 15:08:12] [Iter 3722/4650] R4[1471/2400], Temp: 0.3263, Energy: -54.609174-0.000495j
[2025-07-30 15:08:22] [Iter 3723/4650] R4[1472/2400], Temp: 0.3257, Energy: -54.598081+0.000954j
[2025-07-30 15:08:33] [Iter 3724/4650] R4[1473/2400], Temp: 0.3251, Energy: -54.653006-0.000528j
[2025-07-30 15:08:43] [Iter 3725/4650] R4[1474/2400], Temp: 0.3245, Energy: -54.640303-0.001853j
[2025-07-30 15:08:53] [Iter 3726/4650] R4[1475/2400], Temp: 0.3239, Energy: -54.577778-0.000128j
[2025-07-30 15:09:03] [Iter 3727/4650] R4[1476/2400], Temp: 0.3233, Energy: -54.587395+0.000457j
[2025-07-30 15:09:13] [Iter 3728/4650] R4[1477/2400], Temp: 0.3227, Energy: -54.605807+0.000762j
[2025-07-30 15:09:23] [Iter 3729/4650] R4[1478/2400], Temp: 0.3220, Energy: -54.637764+0.001267j
[2025-07-30 15:09:33] [Iter 3730/4650] R4[1479/2400], Temp: 0.3214, Energy: -54.595500+0.000479j
[2025-07-30 15:09:43] [Iter 3731/4650] R4[1480/2400], Temp: 0.3208, Energy: -54.627570+0.000459j
[2025-07-30 15:09:53] [Iter 3732/4650] R4[1481/2400], Temp: 0.3202, Energy: -54.619686-0.001836j
[2025-07-30 15:10:04] [Iter 3733/4650] R4[1482/2400], Temp: 0.3196, Energy: -54.719575+0.001001j
[2025-07-30 15:10:14] [Iter 3734/4650] R4[1483/2400], Temp: 0.3190, Energy: -54.693747+0.000636j
[2025-07-30 15:10:24] [Iter 3735/4650] R4[1484/2400], Temp: 0.3184, Energy: -54.685046-0.000083j
[2025-07-30 15:10:34] [Iter 3736/4650] R4[1485/2400], Temp: 0.3178, Energy: -54.640275+0.000382j
[2025-07-30 15:10:44] [Iter 3737/4650] R4[1486/2400], Temp: 0.3172, Energy: -54.622161+0.000605j
[2025-07-30 15:10:54] [Iter 3738/4650] R4[1487/2400], Temp: 0.3165, Energy: -54.584390+0.002112j
[2025-07-30 15:11:04] [Iter 3739/4650] R4[1488/2400], Temp: 0.3159, Energy: -54.591090-0.002342j
[2025-07-30 15:11:15] [Iter 3740/4650] R4[1489/2400], Temp: 0.3153, Energy: -54.592335-0.000906j
[2025-07-30 15:11:25] [Iter 3741/4650] R4[1490/2400], Temp: 0.3147, Energy: -54.661884+0.000566j
[2025-07-30 15:11:35] [Iter 3742/4650] R4[1491/2400], Temp: 0.3141, Energy: -54.635169-0.000198j
[2025-07-30 15:11:45] [Iter 3743/4650] R4[1492/2400], Temp: 0.3135, Energy: -54.619198+0.000250j
[2025-07-30 15:11:55] [Iter 3744/4650] R4[1493/2400], Temp: 0.3129, Energy: -54.615429-0.001208j
[2025-07-30 15:12:05] [Iter 3745/4650] R4[1494/2400], Temp: 0.3123, Energy: -54.603480+0.000427j
[2025-07-30 15:12:15] [Iter 3746/4650] R4[1495/2400], Temp: 0.3117, Energy: -54.651480-0.000462j
[2025-07-30 15:12:25] [Iter 3747/4650] R4[1496/2400], Temp: 0.3111, Energy: -54.642663-0.001021j
[2025-07-30 15:12:35] [Iter 3748/4650] R4[1497/2400], Temp: 0.3105, Energy: -54.624886+0.001628j
[2025-07-30 15:12:46] [Iter 3749/4650] R4[1498/2400], Temp: 0.3099, Energy: -54.606467-0.000441j
[2025-07-30 15:12:56] [Iter 3750/4650] R4[1499/2400], Temp: 0.3093, Energy: -54.613612+0.000787j
[2025-07-30 15:13:06] [Iter 3751/4650] R4[1500/2400], Temp: 0.3087, Energy: -54.641937+0.000764j
[2025-07-30 15:13:16] [Iter 3752/4650] R4[1501/2400], Temp: 0.3081, Energy: -54.655282+0.001515j
[2025-07-30 15:13:26] [Iter 3753/4650] R4[1502/2400], Temp: 0.3074, Energy: -54.632440+0.000181j
[2025-07-30 15:13:36] [Iter 3754/4650] R4[1503/2400], Temp: 0.3068, Energy: -54.653370+0.000351j
[2025-07-30 15:13:46] [Iter 3755/4650] R4[1504/2400], Temp: 0.3062, Energy: -54.667787-0.000069j
[2025-07-30 15:13:56] [Iter 3756/4650] R4[1505/2400], Temp: 0.3056, Energy: -54.677235+0.000984j
[2025-07-30 15:14:06] [Iter 3757/4650] R4[1506/2400], Temp: 0.3050, Energy: -54.669159+0.001031j
[2025-07-30 15:14:17] [Iter 3758/4650] R4[1507/2400], Temp: 0.3044, Energy: -54.666186+0.000742j
[2025-07-30 15:14:27] [Iter 3759/4650] R4[1508/2400], Temp: 0.3038, Energy: -54.682227-0.002402j
[2025-07-30 15:14:37] [Iter 3760/4650] R4[1509/2400], Temp: 0.3032, Energy: -54.647484-0.000675j
[2025-07-30 15:14:47] [Iter 3761/4650] R4[1510/2400], Temp: 0.3026, Energy: -54.668952+0.001253j
[2025-07-30 15:14:57] [Iter 3762/4650] R4[1511/2400], Temp: 0.3020, Energy: -54.677092+0.001170j
[2025-07-30 15:15:07] [Iter 3763/4650] R4[1512/2400], Temp: 0.3014, Energy: -54.660996-0.001376j
[2025-07-30 15:15:17] [Iter 3764/4650] R4[1513/2400], Temp: 0.3008, Energy: -54.692466-0.000281j
[2025-07-30 15:15:27] [Iter 3765/4650] R4[1514/2400], Temp: 0.3002, Energy: -54.700571+0.001270j
[2025-07-30 15:15:37] [Iter 3766/4650] R4[1515/2400], Temp: 0.2996, Energy: -54.661038+0.002437j
[2025-07-30 15:15:47] [Iter 3767/4650] R4[1516/2400], Temp: 0.2990, Energy: -54.691098+0.001822j
[2025-07-30 15:15:58] [Iter 3768/4650] R4[1517/2400], Temp: 0.2984, Energy: -54.618124-0.000445j
[2025-07-30 15:16:08] [Iter 3769/4650] R4[1518/2400], Temp: 0.2978, Energy: -54.582372+0.001250j
[2025-07-30 15:16:18] [Iter 3770/4650] R4[1519/2400], Temp: 0.2972, Energy: -54.559543+0.000202j
[2025-07-30 15:16:28] [Iter 3771/4650] R4[1520/2400], Temp: 0.2966, Energy: -54.603318-0.001611j
[2025-07-30 15:16:38] [Iter 3772/4650] R4[1521/2400], Temp: 0.2960, Energy: -54.644236-0.000359j
[2025-07-30 15:16:48] [Iter 3773/4650] R4[1522/2400], Temp: 0.2954, Energy: -54.567659-0.000055j
[2025-07-30 15:16:58] [Iter 3774/4650] R4[1523/2400], Temp: 0.2948, Energy: -54.637698+0.000013j
[2025-07-30 15:17:08] [Iter 3775/4650] R4[1524/2400], Temp: 0.2942, Energy: -54.562316+0.000514j
[2025-07-30 15:17:18] [Iter 3776/4650] R4[1525/2400], Temp: 0.2936, Energy: -54.601262-0.001629j
[2025-07-30 15:17:29] [Iter 3777/4650] R4[1526/2400], Temp: 0.2931, Energy: -54.628680+0.000521j
[2025-07-30 15:17:39] [Iter 3778/4650] R4[1527/2400], Temp: 0.2925, Energy: -54.582180-0.001104j
[2025-07-30 15:17:49] [Iter 3779/4650] R4[1528/2400], Temp: 0.2919, Energy: -54.567061-0.001031j
[2025-07-30 15:17:59] [Iter 3780/4650] R4[1529/2400], Temp: 0.2913, Energy: -54.617241-0.001460j
[2025-07-30 15:18:09] [Iter 3781/4650] R4[1530/2400], Temp: 0.2907, Energy: -54.563177+0.000942j
[2025-07-30 15:18:19] [Iter 3782/4650] R4[1531/2400], Temp: 0.2901, Energy: -54.615263+0.002124j
[2025-07-30 15:18:29] [Iter 3783/4650] R4[1532/2400], Temp: 0.2895, Energy: -54.650658-0.001671j
[2025-07-30 15:18:40] [Iter 3784/4650] R4[1533/2400], Temp: 0.2889, Energy: -54.620859-0.000384j
[2025-07-30 15:18:50] [Iter 3785/4650] R4[1534/2400], Temp: 0.2883, Energy: -54.653721-0.001538j
[2025-07-30 15:19:00] [Iter 3786/4650] R4[1535/2400], Temp: 0.2877, Energy: -54.646257+0.001425j
[2025-07-30 15:19:10] [Iter 3787/4650] R4[1536/2400], Temp: 0.2871, Energy: -54.594617+0.002536j
[2025-07-30 15:19:20] [Iter 3788/4650] R4[1537/2400], Temp: 0.2865, Energy: -54.558858-0.000695j
[2025-07-30 15:19:30] [Iter 3789/4650] R4[1538/2400], Temp: 0.2859, Energy: -54.592750-0.002908j
[2025-07-30 15:19:40] [Iter 3790/4650] R4[1539/2400], Temp: 0.2853, Energy: -54.602007+0.000636j
[2025-07-30 15:19:50] [Iter 3791/4650] R4[1540/2400], Temp: 0.2847, Energy: -54.604356-0.001695j
[2025-07-30 15:20:00] [Iter 3792/4650] R4[1541/2400], Temp: 0.2842, Energy: -54.636276+0.001269j
[2025-07-30 15:20:11] [Iter 3793/4650] R4[1542/2400], Temp: 0.2836, Energy: -54.642215-0.001879j
[2025-07-30 15:20:21] [Iter 3794/4650] R4[1543/2400], Temp: 0.2830, Energy: -54.623800-0.000411j
[2025-07-30 15:20:31] [Iter 3795/4650] R4[1544/2400], Temp: 0.2824, Energy: -54.637882-0.000434j
[2025-07-30 15:20:41] [Iter 3796/4650] R4[1545/2400], Temp: 0.2818, Energy: -54.663025+0.000409j
[2025-07-30 15:20:51] [Iter 3797/4650] R4[1546/2400], Temp: 0.2812, Energy: -54.711123+0.000359j
[2025-07-30 15:21:01] [Iter 3798/4650] R4[1547/2400], Temp: 0.2806, Energy: -54.689297+0.000284j
[2025-07-30 15:21:11] [Iter 3799/4650] R4[1548/2400], Temp: 0.2800, Energy: -54.686267+0.000559j
[2025-07-30 15:21:21] [Iter 3800/4650] R4[1549/2400], Temp: 0.2794, Energy: -54.628961-0.000637j
[2025-07-30 15:21:31] [Iter 3801/4650] R4[1550/2400], Temp: 0.2789, Energy: -54.662677-0.001049j
[2025-07-30 15:21:42] [Iter 3802/4650] R4[1551/2400], Temp: 0.2783, Energy: -54.605396-0.001787j
[2025-07-30 15:21:52] [Iter 3803/4650] R4[1552/2400], Temp: 0.2777, Energy: -54.652172+0.000136j
[2025-07-30 15:22:02] [Iter 3804/4650] R4[1553/2400], Temp: 0.2771, Energy: -54.608298-0.001127j
[2025-07-30 15:22:12] [Iter 3805/4650] R4[1554/2400], Temp: 0.2765, Energy: -54.611200+0.000343j
[2025-07-30 15:22:22] [Iter 3806/4650] R4[1555/2400], Temp: 0.2759, Energy: -54.650282+0.001061j
[2025-07-30 15:22:32] [Iter 3807/4650] R4[1556/2400], Temp: 0.2753, Energy: -54.600440+0.000448j
[2025-07-30 15:22:42] [Iter 3808/4650] R4[1557/2400], Temp: 0.2748, Energy: -54.670864+0.000560j
[2025-07-30 15:22:52] [Iter 3809/4650] R4[1558/2400], Temp: 0.2742, Energy: -54.603391+0.002024j
[2025-07-30 15:23:02] [Iter 3810/4650] R4[1559/2400], Temp: 0.2736, Energy: -54.660101+0.001197j
[2025-07-30 15:23:13] [Iter 3811/4650] R4[1560/2400], Temp: 0.2730, Energy: -54.618461+0.001226j
[2025-07-30 15:23:23] [Iter 3812/4650] R4[1561/2400], Temp: 0.2724, Energy: -54.670227+0.001423j
[2025-07-30 15:23:33] [Iter 3813/4650] R4[1562/2400], Temp: 0.2718, Energy: -54.677332+0.001480j
[2025-07-30 15:23:43] [Iter 3814/4650] R4[1563/2400], Temp: 0.2713, Energy: -54.689661+0.000269j
[2025-07-30 15:23:53] [Iter 3815/4650] R4[1564/2400], Temp: 0.2707, Energy: -54.664672-0.001181j
[2025-07-30 15:24:03] [Iter 3816/4650] R4[1565/2400], Temp: 0.2701, Energy: -54.703144+0.001224j
[2025-07-30 15:24:13] [Iter 3817/4650] R4[1566/2400], Temp: 0.2695, Energy: -54.604732+0.001897j
[2025-07-30 15:24:23] [Iter 3818/4650] R4[1567/2400], Temp: 0.2689, Energy: -54.616032-0.000456j
[2025-07-30 15:24:33] [Iter 3819/4650] R4[1568/2400], Temp: 0.2684, Energy: -54.630676-0.000220j
[2025-07-30 15:24:44] [Iter 3820/4650] R4[1569/2400], Temp: 0.2678, Energy: -54.605335-0.000056j
[2025-07-30 15:24:54] [Iter 3821/4650] R4[1570/2400], Temp: 0.2672, Energy: -54.606403+0.000698j
[2025-07-30 15:25:04] [Iter 3822/4650] R4[1571/2400], Temp: 0.2666, Energy: -54.605482+0.001890j
[2025-07-30 15:25:14] [Iter 3823/4650] R4[1572/2400], Temp: 0.2660, Energy: -54.607724+0.000865j
[2025-07-30 15:25:24] [Iter 3824/4650] R4[1573/2400], Temp: 0.2655, Energy: -54.672554-0.000127j
[2025-07-30 15:25:34] [Iter 3825/4650] R4[1574/2400], Temp: 0.2649, Energy: -54.666092-0.000338j
[2025-07-30 15:25:44] [Iter 3826/4650] R4[1575/2400], Temp: 0.2643, Energy: -54.602253-0.000130j
[2025-07-30 15:25:54] [Iter 3827/4650] R4[1576/2400], Temp: 0.2637, Energy: -54.646008-0.002615j
[2025-07-30 15:26:05] [Iter 3828/4650] R4[1577/2400], Temp: 0.2631, Energy: -54.621009+0.000608j
[2025-07-30 15:26:15] [Iter 3829/4650] R4[1578/2400], Temp: 0.2626, Energy: -54.627027+0.000238j
[2025-07-30 15:26:25] [Iter 3830/4650] R4[1579/2400], Temp: 0.2620, Energy: -54.639918-0.000085j
[2025-07-30 15:26:35] [Iter 3831/4650] R4[1580/2400], Temp: 0.2614, Energy: -54.658424+0.001319j
[2025-07-30 15:26:45] [Iter 3832/4650] R4[1581/2400], Temp: 0.2608, Energy: -54.652533-0.001758j
[2025-07-30 15:26:55] [Iter 3833/4650] R4[1582/2400], Temp: 0.2603, Energy: -54.674210+0.002154j
[2025-07-30 15:27:05] [Iter 3834/4650] R4[1583/2400], Temp: 0.2597, Energy: -54.697870-0.000079j
[2025-07-30 15:27:15] [Iter 3835/4650] R4[1584/2400], Temp: 0.2591, Energy: -54.659921+0.000038j
[2025-07-30 15:27:25] [Iter 3836/4650] R4[1585/2400], Temp: 0.2585, Energy: -54.679770+0.000389j
[2025-07-30 15:27:36] [Iter 3837/4650] R4[1586/2400], Temp: 0.2580, Energy: -54.656154+0.001496j
[2025-07-30 15:27:46] [Iter 3838/4650] R4[1587/2400], Temp: 0.2574, Energy: -54.624635+0.002810j
[2025-07-30 15:27:56] [Iter 3839/4650] R4[1588/2400], Temp: 0.2568, Energy: -54.614813+0.001869j
[2025-07-30 15:28:06] [Iter 3840/4650] R4[1589/2400], Temp: 0.2563, Energy: -54.560921-0.001089j
[2025-07-30 15:28:16] [Iter 3841/4650] R4[1590/2400], Temp: 0.2557, Energy: -54.624160-0.000601j
[2025-07-30 15:28:26] [Iter 3842/4650] R4[1591/2400], Temp: 0.2551, Energy: -54.642411-0.002021j
[2025-07-30 15:28:36] [Iter 3843/4650] R4[1592/2400], Temp: 0.2545, Energy: -54.568224-0.001397j
[2025-07-30 15:28:46] [Iter 3844/4650] R4[1593/2400], Temp: 0.2540, Energy: -54.533309-0.001684j
[2025-07-30 15:28:57] [Iter 3845/4650] R4[1594/2400], Temp: 0.2534, Energy: -54.576333-0.001387j
[2025-07-30 15:29:07] [Iter 3846/4650] R4[1595/2400], Temp: 0.2528, Energy: -54.578149-0.000372j
[2025-07-30 15:29:17] [Iter 3847/4650] R4[1596/2400], Temp: 0.2523, Energy: -54.631757-0.002043j
[2025-07-30 15:29:27] [Iter 3848/4650] R4[1597/2400], Temp: 0.2517, Energy: -54.622709-0.000873j
[2025-07-30 15:29:37] [Iter 3849/4650] R4[1598/2400], Temp: 0.2511, Energy: -54.575212-0.000260j
[2025-07-30 15:29:47] [Iter 3850/4650] R4[1599/2400], Temp: 0.2506, Energy: -54.559601-0.001057j
[2025-07-30 15:29:57] [Iter 3851/4650] R4[1600/2400], Temp: 0.2500, Energy: -54.546653-0.001130j
[2025-07-30 15:30:07] [Iter 3852/4650] R4[1601/2400], Temp: 0.2494, Energy: -54.566069+0.000851j
[2025-07-30 15:30:17] [Iter 3853/4650] R4[1602/2400], Temp: 0.2489, Energy: -54.566269-0.000918j
[2025-07-30 15:30:27] [Iter 3854/4650] R4[1603/2400], Temp: 0.2483, Energy: -54.520352-0.001124j
[2025-07-30 15:30:38] [Iter 3855/4650] R4[1604/2400], Temp: 0.2477, Energy: -54.516961-0.001203j
[2025-07-30 15:30:48] [Iter 3856/4650] R4[1605/2400], Temp: 0.2472, Energy: -54.581819-0.000137j
[2025-07-30 15:30:58] [Iter 3857/4650] R4[1606/2400], Temp: 0.2466, Energy: -54.583517-0.000084j
[2025-07-30 15:31:08] [Iter 3858/4650] R4[1607/2400], Temp: 0.2460, Energy: -54.643257+0.000126j
[2025-07-30 15:31:18] [Iter 3859/4650] R4[1608/2400], Temp: 0.2455, Energy: -54.598098-0.001031j
[2025-07-30 15:31:28] [Iter 3860/4650] R4[1609/2400], Temp: 0.2449, Energy: -54.612005+0.000029j
[2025-07-30 15:31:38] [Iter 3861/4650] R4[1610/2400], Temp: 0.2444, Energy: -54.662732-0.002506j
[2025-07-30 15:31:48] [Iter 3862/4650] R4[1611/2400], Temp: 0.2438, Energy: -54.587808+0.000183j
[2025-07-30 15:31:58] [Iter 3863/4650] R4[1612/2400], Temp: 0.2432, Energy: -54.612461-0.000217j
[2025-07-30 15:32:09] [Iter 3864/4650] R4[1613/2400], Temp: 0.2427, Energy: -54.642874-0.001729j
[2025-07-30 15:32:19] [Iter 3865/4650] R4[1614/2400], Temp: 0.2421, Energy: -54.638283-0.002089j
[2025-07-30 15:32:29] [Iter 3866/4650] R4[1615/2400], Temp: 0.2415, Energy: -54.603405+0.000550j
[2025-07-30 15:32:39] [Iter 3867/4650] R4[1616/2400], Temp: 0.2410, Energy: -54.612008-0.000495j
[2025-07-30 15:32:49] [Iter 3868/4650] R4[1617/2400], Temp: 0.2404, Energy: -54.637365+0.000203j
[2025-07-30 15:32:59] [Iter 3869/4650] R4[1618/2400], Temp: 0.2399, Energy: -54.632458+0.000189j
[2025-07-30 15:33:09] [Iter 3870/4650] R4[1619/2400], Temp: 0.2393, Energy: -54.623493+0.000467j
[2025-07-30 15:33:19] [Iter 3871/4650] R4[1620/2400], Temp: 0.2388, Energy: -54.638960-0.000899j
[2025-07-30 15:33:29] [Iter 3872/4650] R4[1621/2400], Temp: 0.2382, Energy: -54.624903-0.001720j
[2025-07-30 15:33:40] [Iter 3873/4650] R4[1622/2400], Temp: 0.2376, Energy: -54.639019-0.000085j
[2025-07-30 15:33:50] [Iter 3874/4650] R4[1623/2400], Temp: 0.2371, Energy: -54.644015-0.001804j
[2025-07-30 15:34:00] [Iter 3875/4650] R4[1624/2400], Temp: 0.2365, Energy: -54.644912+0.002925j
[2025-07-30 15:34:10] [Iter 3876/4650] R4[1625/2400], Temp: 0.2360, Energy: -54.689273-0.000771j
[2025-07-30 15:34:20] [Iter 3877/4650] R4[1626/2400], Temp: 0.2354, Energy: -54.677370-0.000479j
[2025-07-30 15:34:30] [Iter 3878/4650] R4[1627/2400], Temp: 0.2349, Energy: -54.728720+0.002125j
[2025-07-30 15:34:40] [Iter 3879/4650] R4[1628/2400], Temp: 0.2343, Energy: -54.670011+0.000675j
[2025-07-30 15:34:50] [Iter 3880/4650] R4[1629/2400], Temp: 0.2337, Energy: -54.678887-0.000659j
[2025-07-30 15:35:00] [Iter 3881/4650] R4[1630/2400], Temp: 0.2332, Energy: -54.666759+0.000032j
[2025-07-30 15:35:11] [Iter 3882/4650] R4[1631/2400], Temp: 0.2326, Energy: -54.633177+0.000425j
[2025-07-30 15:35:21] [Iter 3883/4650] R4[1632/2400], Temp: 0.2321, Energy: -54.666517+0.000456j
[2025-07-30 15:35:31] [Iter 3884/4650] R4[1633/2400], Temp: 0.2315, Energy: -54.661031-0.000045j
[2025-07-30 15:35:41] [Iter 3885/4650] R4[1634/2400], Temp: 0.2310, Energy: -54.606985-0.001044j
[2025-07-30 15:35:51] [Iter 3886/4650] R4[1635/2400], Temp: 0.2304, Energy: -54.610520+0.000577j
[2025-07-30 15:36:01] [Iter 3887/4650] R4[1636/2400], Temp: 0.2299, Energy: -54.641084-0.002445j
[2025-07-30 15:36:11] [Iter 3888/4650] R4[1637/2400], Temp: 0.2293, Energy: -54.641388-0.001947j
[2025-07-30 15:36:21] [Iter 3889/4650] R4[1638/2400], Temp: 0.2288, Energy: -54.691749-0.000260j
[2025-07-30 15:36:32] [Iter 3890/4650] R4[1639/2400], Temp: 0.2282, Energy: -54.652769-0.000650j
[2025-07-30 15:36:42] [Iter 3891/4650] R4[1640/2400], Temp: 0.2277, Energy: -54.657829-0.000455j
[2025-07-30 15:36:52] [Iter 3892/4650] R4[1641/2400], Temp: 0.2271, Energy: -54.662223-0.002360j
[2025-07-30 15:37:02] [Iter 3893/4650] R4[1642/2400], Temp: 0.2266, Energy: -54.686064+0.000242j
[2025-07-30 15:37:12] [Iter 3894/4650] R4[1643/2400], Temp: 0.2260, Energy: -54.620731+0.000871j
[2025-07-30 15:37:22] [Iter 3895/4650] R4[1644/2400], Temp: 0.2255, Energy: -54.636722+0.000400j
[2025-07-30 15:37:32] [Iter 3896/4650] R4[1645/2400], Temp: 0.2249, Energy: -54.669737-0.000783j
[2025-07-30 15:37:42] [Iter 3897/4650] R4[1646/2400], Temp: 0.2244, Energy: -54.605305+0.001074j
[2025-07-30 15:37:52] [Iter 3898/4650] R4[1647/2400], Temp: 0.2238, Energy: -54.628983+0.001245j
[2025-07-30 15:38:03] [Iter 3899/4650] R4[1648/2400], Temp: 0.2233, Energy: -54.664045-0.000483j
[2025-07-30 15:38:13] [Iter 3900/4650] R4[1649/2400], Temp: 0.2228, Energy: -54.651720+0.000106j
[2025-07-30 15:38:23] [Iter 3901/4650] R4[1650/2400], Temp: 0.2222, Energy: -54.655193-0.000741j
[2025-07-30 15:38:33] [Iter 3902/4650] R4[1651/2400], Temp: 0.2217, Energy: -54.590117-0.000160j
[2025-07-30 15:38:43] [Iter 3903/4650] R4[1652/2400], Temp: 0.2211, Energy: -54.600144-0.002190j
[2025-07-30 15:38:53] [Iter 3904/4650] R4[1653/2400], Temp: 0.2206, Energy: -54.673778+0.000906j
[2025-07-30 15:39:03] [Iter 3905/4650] R4[1654/2400], Temp: 0.2200, Energy: -54.686838-0.000594j
[2025-07-30 15:39:13] [Iter 3906/4650] R4[1655/2400], Temp: 0.2195, Energy: -54.655979+0.000361j
[2025-07-30 15:39:23] [Iter 3907/4650] R4[1656/2400], Temp: 0.2190, Energy: -54.650176-0.000079j
[2025-07-30 15:39:34] [Iter 3908/4650] R4[1657/2400], Temp: 0.2184, Energy: -54.678193-0.000524j
[2025-07-30 15:39:44] [Iter 3909/4650] R4[1658/2400], Temp: 0.2179, Energy: -54.672074-0.001285j
[2025-07-30 15:39:54] [Iter 3910/4650] R4[1659/2400], Temp: 0.2173, Energy: -54.664705+0.002432j
[2025-07-30 15:40:04] [Iter 3911/4650] R4[1660/2400], Temp: 0.2168, Energy: -54.664444+0.000416j
[2025-07-30 15:40:14] [Iter 3912/4650] R4[1661/2400], Temp: 0.2163, Energy: -54.672630-0.001255j
[2025-07-30 15:40:24] [Iter 3913/4650] R4[1662/2400], Temp: 0.2157, Energy: -54.666917-0.002211j
[2025-07-30 15:40:34] [Iter 3914/4650] R4[1663/2400], Temp: 0.2152, Energy: -54.602528+0.000799j
[2025-07-30 15:40:44] [Iter 3915/4650] R4[1664/2400], Temp: 0.2146, Energy: -54.658735-0.000133j
[2025-07-30 15:40:54] [Iter 3916/4650] R4[1665/2400], Temp: 0.2141, Energy: -54.623386+0.000760j
[2025-07-30 15:41:05] [Iter 3917/4650] R4[1666/2400], Temp: 0.2136, Energy: -54.608923-0.001133j
[2025-07-30 15:41:15] [Iter 3918/4650] R4[1667/2400], Temp: 0.2130, Energy: -54.556603+0.002252j
[2025-07-30 15:41:25] [Iter 3919/4650] R4[1668/2400], Temp: 0.2125, Energy: -54.617021-0.000182j
[2025-07-30 15:41:35] [Iter 3920/4650] R4[1669/2400], Temp: 0.2120, Energy: -54.662796-0.000344j
[2025-07-30 15:41:45] [Iter 3921/4650] R4[1670/2400], Temp: 0.2114, Energy: -54.653665-0.001228j
[2025-07-30 15:41:55] [Iter 3922/4650] R4[1671/2400], Temp: 0.2109, Energy: -54.679078+0.000362j
[2025-07-30 15:42:05] [Iter 3923/4650] R4[1672/2400], Temp: 0.2104, Energy: -54.654325-0.001081j
[2025-07-30 15:42:15] [Iter 3924/4650] R4[1673/2400], Temp: 0.2098, Energy: -54.603005-0.001854j
[2025-07-30 15:42:26] [Iter 3925/4650] R4[1674/2400], Temp: 0.2093, Energy: -54.560919-0.001689j
[2025-07-30 15:42:36] [Iter 3926/4650] R4[1675/2400], Temp: 0.2088, Energy: -54.551261+0.001222j
[2025-07-30 15:42:46] [Iter 3927/4650] R4[1676/2400], Temp: 0.2082, Energy: -54.560380+0.001237j
[2025-07-30 15:42:56] [Iter 3928/4650] R4[1677/2400], Temp: 0.2077, Energy: -54.581616+0.000266j
[2025-07-30 15:43:06] [Iter 3929/4650] R4[1678/2400], Temp: 0.2072, Energy: -54.555599+0.000226j
[2025-07-30 15:43:16] [Iter 3930/4650] R4[1679/2400], Temp: 0.2066, Energy: -54.562932+0.001163j
[2025-07-30 15:43:26] [Iter 3931/4650] R4[1680/2400], Temp: 0.2061, Energy: -54.570332-0.000255j
[2025-07-30 15:43:36] [Iter 3932/4650] R4[1681/2400], Temp: 0.2056, Energy: -54.555894+0.001894j
[2025-07-30 15:43:46] [Iter 3933/4650] R4[1682/2400], Temp: 0.2050, Energy: -54.581935-0.000121j
[2025-07-30 15:43:56] [Iter 3934/4650] R4[1683/2400], Temp: 0.2045, Energy: -54.625275+0.001932j
[2025-07-30 15:44:07] [Iter 3935/4650] R4[1684/2400], Temp: 0.2040, Energy: -54.625565-0.001217j
[2025-07-30 15:44:17] [Iter 3936/4650] R4[1685/2400], Temp: 0.2035, Energy: -54.652956-0.002006j
[2025-07-30 15:44:27] [Iter 3937/4650] R4[1686/2400], Temp: 0.2029, Energy: -54.559452-0.001961j
[2025-07-30 15:44:37] [Iter 3938/4650] R4[1687/2400], Temp: 0.2024, Energy: -54.624101+0.000006j
[2025-07-30 15:44:47] [Iter 3939/4650] R4[1688/2400], Temp: 0.2019, Energy: -54.577691+0.000075j
[2025-07-30 15:44:57] [Iter 3940/4650] R4[1689/2400], Temp: 0.2014, Energy: -54.616461-0.000957j
[2025-07-30 15:45:07] [Iter 3941/4650] R4[1690/2400], Temp: 0.2008, Energy: -54.618918+0.000459j
[2025-07-30 15:45:17] [Iter 3942/4650] R4[1691/2400], Temp: 0.2003, Energy: -54.598949+0.001500j
[2025-07-30 15:45:28] [Iter 3943/4650] R4[1692/2400], Temp: 0.1998, Energy: -54.533610+0.000939j
[2025-07-30 15:45:38] [Iter 3944/4650] R4[1693/2400], Temp: 0.1993, Energy: -54.534355+0.000867j
[2025-07-30 15:45:48] [Iter 3945/4650] R4[1694/2400], Temp: 0.1987, Energy: -54.621761-0.002197j
[2025-07-30 15:45:58] [Iter 3946/4650] R4[1695/2400], Temp: 0.1982, Energy: -54.571279+0.001258j
[2025-07-30 15:46:08] [Iter 3947/4650] R4[1696/2400], Temp: 0.1977, Energy: -54.651966+0.002222j
[2025-07-30 15:46:18] [Iter 3948/4650] R4[1697/2400], Temp: 0.1972, Energy: -54.662368-0.000190j
[2025-07-30 15:46:28] [Iter 3949/4650] R4[1698/2400], Temp: 0.1967, Energy: -54.667601+0.000376j
[2025-07-30 15:46:38] [Iter 3950/4650] R4[1699/2400], Temp: 0.1961, Energy: -54.614080-0.000713j
[2025-07-30 15:46:48] [Iter 3951/4650] R4[1700/2400], Temp: 0.1956, Energy: -54.694867-0.001189j
[2025-07-30 15:46:58] [Iter 3952/4650] R4[1701/2400], Temp: 0.1951, Energy: -54.710558-0.002577j
[2025-07-30 15:47:09] [Iter 3953/4650] R4[1702/2400], Temp: 0.1946, Energy: -54.739593+0.002950j
[2025-07-30 15:47:19] [Iter 3954/4650] R4[1703/2400], Temp: 0.1941, Energy: -54.698261-0.000303j
[2025-07-30 15:47:29] [Iter 3955/4650] R4[1704/2400], Temp: 0.1935, Energy: -54.674004-0.002287j
[2025-07-30 15:47:39] [Iter 3956/4650] R4[1705/2400], Temp: 0.1930, Energy: -54.695121-0.000553j
[2025-07-30 15:47:49] [Iter 3957/4650] R4[1706/2400], Temp: 0.1925, Energy: -54.674074-0.002711j
[2025-07-30 15:47:59] [Iter 3958/4650] R4[1707/2400], Temp: 0.1920, Energy: -54.717498-0.001472j
[2025-07-30 15:48:09] [Iter 3959/4650] R4[1708/2400], Temp: 0.1915, Energy: -54.720824-0.004302j
[2025-07-30 15:48:19] [Iter 3960/4650] R4[1709/2400], Temp: 0.1910, Energy: -54.678304-0.002302j
[2025-07-30 15:48:30] [Iter 3961/4650] R4[1710/2400], Temp: 0.1905, Energy: -54.729747-0.001897j
[2025-07-30 15:48:40] [Iter 3962/4650] R4[1711/2400], Temp: 0.1899, Energy: -54.685272+0.000051j
[2025-07-30 15:48:50] [Iter 3963/4650] R4[1712/2400], Temp: 0.1894, Energy: -54.673849-0.002141j
[2025-07-30 15:49:00] [Iter 3964/4650] R4[1713/2400], Temp: 0.1889, Energy: -54.655793+0.001743j
[2025-07-30 15:49:10] [Iter 3965/4650] R4[1714/2400], Temp: 0.1884, Energy: -54.652412+0.000205j
[2025-07-30 15:49:20] [Iter 3966/4650] R4[1715/2400], Temp: 0.1879, Energy: -54.662055-0.001557j
[2025-07-30 15:49:30] [Iter 3967/4650] R4[1716/2400], Temp: 0.1874, Energy: -54.660151-0.000316j
[2025-07-30 15:49:40] [Iter 3968/4650] R4[1717/2400], Temp: 0.1869, Energy: -54.692199+0.002828j
[2025-07-30 15:49:50] [Iter 3969/4650] R4[1718/2400], Temp: 0.1864, Energy: -54.657621-0.000411j
[2025-07-30 15:50:01] [Iter 3970/4650] R4[1719/2400], Temp: 0.1858, Energy: -54.644591-0.001818j
[2025-07-30 15:50:11] [Iter 3971/4650] R4[1720/2400], Temp: 0.1853, Energy: -54.642529+0.002039j
[2025-07-30 15:50:21] [Iter 3972/4650] R4[1721/2400], Temp: 0.1848, Energy: -54.648478-0.001066j
[2025-07-30 15:50:31] [Iter 3973/4650] R4[1722/2400], Temp: 0.1843, Energy: -54.637448-0.000220j
[2025-07-30 15:50:41] [Iter 3974/4650] R4[1723/2400], Temp: 0.1838, Energy: -54.624228-0.000908j
[2025-07-30 15:50:51] [Iter 3975/4650] R4[1724/2400], Temp: 0.1833, Energy: -54.656259-0.001240j
[2025-07-30 15:51:01] [Iter 3976/4650] R4[1725/2400], Temp: 0.1828, Energy: -54.636609+0.001293j
[2025-07-30 15:51:11] [Iter 3977/4650] R4[1726/2400], Temp: 0.1823, Energy: -54.657450+0.000386j
[2025-07-30 15:51:21] [Iter 3978/4650] R4[1727/2400], Temp: 0.1818, Energy: -54.640565+0.000009j
[2025-07-30 15:51:32] [Iter 3979/4650] R4[1728/2400], Temp: 0.1813, Energy: -54.644213+0.000881j
[2025-07-30 15:51:42] [Iter 3980/4650] R4[1729/2400], Temp: 0.1808, Energy: -54.644864+0.002118j
[2025-07-30 15:51:52] [Iter 3981/4650] R4[1730/2400], Temp: 0.1803, Energy: -54.636651+0.002117j
[2025-07-30 15:52:02] [Iter 3982/4650] R4[1731/2400], Temp: 0.1798, Energy: -54.599746+0.000630j
[2025-07-30 15:52:12] [Iter 3983/4650] R4[1732/2400], Temp: 0.1793, Energy: -54.573501+0.000854j
[2025-07-30 15:52:22] [Iter 3984/4650] R4[1733/2400], Temp: 0.1788, Energy: -54.587903+0.000097j
[2025-07-30 15:52:32] [Iter 3985/4650] R4[1734/2400], Temp: 0.1783, Energy: -54.638746+0.000280j
[2025-07-30 15:52:42] [Iter 3986/4650] R4[1735/2400], Temp: 0.1778, Energy: -54.577183+0.000198j
[2025-07-30 15:52:52] [Iter 3987/4650] R4[1736/2400], Temp: 0.1773, Energy: -54.566051+0.000335j
[2025-07-30 15:53:03] [Iter 3988/4650] R4[1737/2400], Temp: 0.1768, Energy: -54.580636-0.001037j
[2025-07-30 15:53:13] [Iter 3989/4650] R4[1738/2400], Temp: 0.1763, Energy: -54.644882-0.000516j
[2025-07-30 15:53:23] [Iter 3990/4650] R4[1739/2400], Temp: 0.1758, Energy: -54.647818+0.001478j
[2025-07-30 15:53:33] [Iter 3991/4650] R4[1740/2400], Temp: 0.1753, Energy: -54.630727+0.000967j
[2025-07-30 15:53:43] [Iter 3992/4650] R4[1741/2400], Temp: 0.1748, Energy: -54.630563-0.000320j
[2025-07-30 15:53:53] [Iter 3993/4650] R4[1742/2400], Temp: 0.1743, Energy: -54.627746-0.000563j
[2025-07-30 15:54:03] [Iter 3994/4650] R4[1743/2400], Temp: 0.1738, Energy: -54.682200+0.000020j
[2025-07-30 15:54:13] [Iter 3995/4650] R4[1744/2400], Temp: 0.1733, Energy: -54.664606-0.002756j
[2025-07-30 15:54:23] [Iter 3996/4650] R4[1745/2400], Temp: 0.1728, Energy: -54.676593-0.001458j
[2025-07-30 15:54:34] [Iter 3997/4650] R4[1746/2400], Temp: 0.1723, Energy: -54.687457-0.001178j
[2025-07-30 15:54:44] [Iter 3998/4650] R4[1747/2400], Temp: 0.1718, Energy: -54.641656+0.000195j
[2025-07-30 15:54:54] [Iter 3999/4650] R4[1748/2400], Temp: 0.1713, Energy: -54.574845+0.000216j
[2025-07-30 15:55:04] [Iter 4000/4650] R4[1749/2400], Temp: 0.1708, Energy: -54.605425+0.001204j
[2025-07-30 15:55:04] ✓ Checkpoint saved: checkpoint_iter_004000.pkl
[2025-07-30 15:55:14] [Iter 4001/4650] R4[1750/2400], Temp: 0.1703, Energy: -54.586519+0.000391j
[2025-07-30 15:55:24] [Iter 4002/4650] R4[1751/2400], Temp: 0.1698, Energy: -54.534420+0.000042j
[2025-07-30 15:55:34] [Iter 4003/4650] R4[1752/2400], Temp: 0.1693, Energy: -54.622479+0.000831j
[2025-07-30 15:55:44] [Iter 4004/4650] R4[1753/2400], Temp: 0.1689, Energy: -54.644810-0.000372j
[2025-07-30 15:55:54] [Iter 4005/4650] R4[1754/2400], Temp: 0.1684, Energy: -54.652094+0.000982j
[2025-07-30 15:56:05] [Iter 4006/4650] R4[1755/2400], Temp: 0.1679, Energy: -54.624232-0.001403j
[2025-07-30 15:56:15] [Iter 4007/4650] R4[1756/2400], Temp: 0.1674, Energy: -54.634173-0.000170j
[2025-07-30 15:56:25] [Iter 4008/4650] R4[1757/2400], Temp: 0.1669, Energy: -54.637267-0.002183j
[2025-07-30 15:56:35] [Iter 4009/4650] R4[1758/2400], Temp: 0.1664, Energy: -54.626489+0.000895j
[2025-07-30 15:56:45] [Iter 4010/4650] R4[1759/2400], Temp: 0.1659, Energy: -54.624825-0.000244j
[2025-07-30 15:56:55] [Iter 4011/4650] R4[1760/2400], Temp: 0.1654, Energy: -54.606326+0.000792j
[2025-07-30 15:57:05] [Iter 4012/4650] R4[1761/2400], Temp: 0.1649, Energy: -54.617110+0.001289j
[2025-07-30 15:57:15] [Iter 4013/4650] R4[1762/2400], Temp: 0.1645, Energy: -54.671555+0.000364j
[2025-07-30 15:57:26] [Iter 4014/4650] R4[1763/2400], Temp: 0.1640, Energy: -54.641074-0.000909j
[2025-07-30 15:57:36] [Iter 4015/4650] R4[1764/2400], Temp: 0.1635, Energy: -54.663638-0.000296j
[2025-07-30 15:57:46] [Iter 4016/4650] R4[1765/2400], Temp: 0.1630, Energy: -54.637430-0.000415j
[2025-07-30 15:57:56] [Iter 4017/4650] R4[1766/2400], Temp: 0.1625, Energy: -54.621447+0.000428j
[2025-07-30 15:58:06] [Iter 4018/4650] R4[1767/2400], Temp: 0.1620, Energy: -54.622929-0.000278j
[2025-07-30 15:58:16] [Iter 4019/4650] R4[1768/2400], Temp: 0.1616, Energy: -54.604091+0.004425j
[2025-07-30 15:58:26] [Iter 4020/4650] R4[1769/2400], Temp: 0.1611, Energy: -54.669763+0.002249j
[2025-07-30 15:58:36] [Iter 4021/4650] R4[1770/2400], Temp: 0.1606, Energy: -54.656018+0.001926j
[2025-07-30 15:58:46] [Iter 4022/4650] R4[1771/2400], Temp: 0.1601, Energy: -54.623092-0.000833j
[2025-07-30 15:58:57] [Iter 4023/4650] R4[1772/2400], Temp: 0.1596, Energy: -54.654618+0.001393j
[2025-07-30 15:59:07] [Iter 4024/4650] R4[1773/2400], Temp: 0.1592, Energy: -54.660931+0.000707j
[2025-07-30 15:59:17] [Iter 4025/4650] R4[1774/2400], Temp: 0.1587, Energy: -54.686759+0.000552j
[2025-07-30 15:59:27] [Iter 4026/4650] R4[1775/2400], Temp: 0.1582, Energy: -54.615235-0.000975j
[2025-07-30 15:59:37] [Iter 4027/4650] R4[1776/2400], Temp: 0.1577, Energy: -54.634038+0.001131j
[2025-07-30 15:59:47] [Iter 4028/4650] R4[1777/2400], Temp: 0.1572, Energy: -54.641790-0.000063j
[2025-07-30 15:59:57] [Iter 4029/4650] R4[1778/2400], Temp: 0.1568, Energy: -54.593852-0.000383j
[2025-07-30 16:00:07] [Iter 4030/4650] R4[1779/2400], Temp: 0.1563, Energy: -54.618918+0.001002j
[2025-07-30 16:00:17] [Iter 4031/4650] R4[1780/2400], Temp: 0.1558, Energy: -54.595049-0.000955j
[2025-07-30 16:00:28] [Iter 4032/4650] R4[1781/2400], Temp: 0.1553, Energy: -54.592463+0.000367j
[2025-07-30 16:00:38] [Iter 4033/4650] R4[1782/2400], Temp: 0.1549, Energy: -54.579310+0.001086j
[2025-07-30 16:00:48] [Iter 4034/4650] R4[1783/2400], Temp: 0.1544, Energy: -54.543686-0.000047j
[2025-07-30 16:00:58] [Iter 4035/4650] R4[1784/2400], Temp: 0.1539, Energy: -54.551955+0.000367j
[2025-07-30 16:01:08] [Iter 4036/4650] R4[1785/2400], Temp: 0.1535, Energy: -54.619696+0.000395j
[2025-07-30 16:01:18] [Iter 4037/4650] R4[1786/2400], Temp: 0.1530, Energy: -54.678843+0.000611j
[2025-07-30 16:01:28] [Iter 4038/4650] R4[1787/2400], Temp: 0.1525, Energy: -54.632558-0.001804j
[2025-07-30 16:01:38] [Iter 4039/4650] R4[1788/2400], Temp: 0.1520, Energy: -54.652492+0.001744j
[2025-07-30 16:01:48] [Iter 4040/4650] R4[1789/2400], Temp: 0.1516, Energy: -54.686842+0.001882j
[2025-07-30 16:01:59] [Iter 4041/4650] R4[1790/2400], Temp: 0.1511, Energy: -54.672967-0.000136j
[2025-07-30 16:02:09] [Iter 4042/4650] R4[1791/2400], Temp: 0.1506, Energy: -54.656212-0.000571j
[2025-07-30 16:02:19] [Iter 4043/4650] R4[1792/2400], Temp: 0.1502, Energy: -54.677113+0.001070j
[2025-07-30 16:02:29] [Iter 4044/4650] R4[1793/2400], Temp: 0.1497, Energy: -54.627399-0.000974j
[2025-07-30 16:02:39] [Iter 4045/4650] R4[1794/2400], Temp: 0.1492, Energy: -54.611540+0.000181j
[2025-07-30 16:02:49] [Iter 4046/4650] R4[1795/2400], Temp: 0.1488, Energy: -54.632083+0.000929j
[2025-07-30 16:02:59] [Iter 4047/4650] R4[1796/2400], Temp: 0.1483, Energy: -54.647638+0.000381j
[2025-07-30 16:03:10] [Iter 4048/4650] R4[1797/2400], Temp: 0.1478, Energy: -54.623421-0.001195j
[2025-07-30 16:03:20] [Iter 4049/4650] R4[1798/2400], Temp: 0.1474, Energy: -54.627585+0.000140j
[2025-07-30 16:03:30] [Iter 4050/4650] R4[1799/2400], Temp: 0.1469, Energy: -54.644377+0.000924j
[2025-07-30 16:03:40] [Iter 4051/4650] R4[1800/2400], Temp: 0.1464, Energy: -54.704801+0.001355j
[2025-07-30 16:03:50] [Iter 4052/4650] R4[1801/2400], Temp: 0.1460, Energy: -54.673677-0.001601j
[2025-07-30 16:04:00] [Iter 4053/4650] R4[1802/2400], Temp: 0.1455, Energy: -54.714167-0.001731j
[2025-07-30 16:04:10] [Iter 4054/4650] R4[1803/2400], Temp: 0.1451, Energy: -54.692992-0.000394j
[2025-07-30 16:04:20] [Iter 4055/4650] R4[1804/2400], Temp: 0.1446, Energy: -54.639067-0.000048j
[2025-07-30 16:04:30] [Iter 4056/4650] R4[1805/2400], Temp: 0.1441, Energy: -54.611863+0.000682j
[2025-07-30 16:04:41] [Iter 4057/4650] R4[1806/2400], Temp: 0.1437, Energy: -54.613180+0.003179j
[2025-07-30 16:04:51] [Iter 4058/4650] R4[1807/2400], Temp: 0.1432, Energy: -54.532120+0.001123j
[2025-07-30 16:05:01] [Iter 4059/4650] R4[1808/2400], Temp: 0.1428, Energy: -54.590596-0.000372j
[2025-07-30 16:05:11] [Iter 4060/4650] R4[1809/2400], Temp: 0.1423, Energy: -54.609679-0.000127j
[2025-07-30 16:05:21] [Iter 4061/4650] R4[1810/2400], Temp: 0.1418, Energy: -54.574136+0.002902j
[2025-07-30 16:05:31] [Iter 4062/4650] R4[1811/2400], Temp: 0.1414, Energy: -54.563165+0.003418j
[2025-07-30 16:05:41] [Iter 4063/4650] R4[1812/2400], Temp: 0.1409, Energy: -54.550658+0.001223j
[2025-07-30 16:05:51] [Iter 4064/4650] R4[1813/2400], Temp: 0.1405, Energy: -54.578181+0.002992j
[2025-07-30 16:06:01] [Iter 4065/4650] R4[1814/2400], Temp: 0.1400, Energy: -54.624187+0.001679j
[2025-07-30 16:06:11] [Iter 4066/4650] R4[1815/2400], Temp: 0.1396, Energy: -54.609477+0.000707j
[2025-07-30 16:06:22] [Iter 4067/4650] R4[1816/2400], Temp: 0.1391, Energy: -54.563922+0.000502j
[2025-07-30 16:06:32] [Iter 4068/4650] R4[1817/2400], Temp: 0.1387, Energy: -54.590250+0.001119j
[2025-07-30 16:06:42] [Iter 4069/4650] R4[1818/2400], Temp: 0.1382, Energy: -54.578213+0.001809j
[2025-07-30 16:06:52] [Iter 4070/4650] R4[1819/2400], Temp: 0.1378, Energy: -54.575654+0.000014j
[2025-07-30 16:07:02] [Iter 4071/4650] R4[1820/2400], Temp: 0.1373, Energy: -54.616282+0.001393j
[2025-07-30 16:07:12] [Iter 4072/4650] R4[1821/2400], Temp: 0.1369, Energy: -54.549683+0.000928j
[2025-07-30 16:07:22] [Iter 4073/4650] R4[1822/2400], Temp: 0.1364, Energy: -54.622322-0.000679j
[2025-07-30 16:07:32] [Iter 4074/4650] R4[1823/2400], Temp: 0.1360, Energy: -54.595449-0.000863j
[2025-07-30 16:07:42] [Iter 4075/4650] R4[1824/2400], Temp: 0.1355, Energy: -54.607317+0.001256j
[2025-07-30 16:07:53] [Iter 4076/4650] R4[1825/2400], Temp: 0.1351, Energy: -54.606127+0.001565j
[2025-07-30 16:08:03] [Iter 4077/4650] R4[1826/2400], Temp: 0.1346, Energy: -54.595038+0.000992j
[2025-07-30 16:08:13] [Iter 4078/4650] R4[1827/2400], Temp: 0.1342, Energy: -54.612294-0.000701j
[2025-07-30 16:08:23] [Iter 4079/4650] R4[1828/2400], Temp: 0.1337, Energy: -54.615701-0.000553j
[2025-07-30 16:08:33] [Iter 4080/4650] R4[1829/2400], Temp: 0.1333, Energy: -54.592912-0.004507j
[2025-07-30 16:08:43] [Iter 4081/4650] R4[1830/2400], Temp: 0.1328, Energy: -54.594256-0.000102j
[2025-07-30 16:08:53] [Iter 4082/4650] R4[1831/2400], Temp: 0.1324, Energy: -54.623114+0.000741j
[2025-07-30 16:09:03] [Iter 4083/4650] R4[1832/2400], Temp: 0.1320, Energy: -54.651534+0.003072j
[2025-07-30 16:09:14] [Iter 4084/4650] R4[1833/2400], Temp: 0.1315, Energy: -54.672987+0.002251j
[2025-07-30 16:09:24] [Iter 4085/4650] R4[1834/2400], Temp: 0.1311, Energy: -54.682155+0.000748j
[2025-07-30 16:09:34] [Iter 4086/4650] R4[1835/2400], Temp: 0.1306, Energy: -54.715482-0.000861j
[2025-07-30 16:09:44] [Iter 4087/4650] R4[1836/2400], Temp: 0.1302, Energy: -54.635392-0.000260j
[2025-07-30 16:09:54] [Iter 4088/4650] R4[1837/2400], Temp: 0.1297, Energy: -54.620134-0.001518j
[2025-07-30 16:10:04] [Iter 4089/4650] R4[1838/2400], Temp: 0.1293, Energy: -54.604993+0.002334j
[2025-07-30 16:10:14] [Iter 4090/4650] R4[1839/2400], Temp: 0.1289, Energy: -54.589938-0.002181j
[2025-07-30 16:10:24] [Iter 4091/4650] R4[1840/2400], Temp: 0.1284, Energy: -54.630126+0.000087j
[2025-07-30 16:10:35] [Iter 4092/4650] R4[1841/2400], Temp: 0.1280, Energy: -54.615391-0.000568j
[2025-07-30 16:10:45] [Iter 4093/4650] R4[1842/2400], Temp: 0.1276, Energy: -54.632995+0.000638j
[2025-07-30 16:10:55] [Iter 4094/4650] R4[1843/2400], Temp: 0.1271, Energy: -54.665350+0.001342j
[2025-07-30 16:11:05] [Iter 4095/4650] R4[1844/2400], Temp: 0.1267, Energy: -54.618807-0.000841j
[2025-07-30 16:11:15] [Iter 4096/4650] R4[1845/2400], Temp: 0.1262, Energy: -54.628367+0.000320j
[2025-07-30 16:11:25] [Iter 4097/4650] R4[1846/2400], Temp: 0.1258, Energy: -54.591446+0.001158j
[2025-07-30 16:11:35] [Iter 4098/4650] R4[1847/2400], Temp: 0.1254, Energy: -54.631600-0.002161j
[2025-07-30 16:11:45] [Iter 4099/4650] R4[1848/2400], Temp: 0.1249, Energy: -54.607773+0.000420j
[2025-07-30 16:11:56] [Iter 4100/4650] R4[1849/2400], Temp: 0.1245, Energy: -54.622680+0.000919j
[2025-07-30 16:12:06] [Iter 4101/4650] R4[1850/2400], Temp: 0.1241, Energy: -54.616311-0.000119j
[2025-07-30 16:12:16] [Iter 4102/4650] R4[1851/2400], Temp: 0.1236, Energy: -54.670858+0.000402j
[2025-07-30 16:12:26] [Iter 4103/4650] R4[1852/2400], Temp: 0.1232, Energy: -54.603770+0.001899j
[2025-07-30 16:12:36] [Iter 4104/4650] R4[1853/2400], Temp: 0.1228, Energy: -54.573884+0.000023j
[2025-07-30 16:12:46] [Iter 4105/4650] R4[1854/2400], Temp: 0.1224, Energy: -54.584995-0.001296j
[2025-07-30 16:12:56] [Iter 4106/4650] R4[1855/2400], Temp: 0.1219, Energy: -54.567199+0.000523j
[2025-07-30 16:13:06] [Iter 4107/4650] R4[1856/2400], Temp: 0.1215, Energy: -54.606111+0.000081j
[2025-07-30 16:13:16] [Iter 4108/4650] R4[1857/2400], Temp: 0.1211, Energy: -54.567051-0.002096j
[2025-07-30 16:13:27] [Iter 4109/4650] R4[1858/2400], Temp: 0.1206, Energy: -54.567114+0.001308j
[2025-07-30 16:13:37] [Iter 4110/4650] R4[1859/2400], Temp: 0.1202, Energy: -54.557954+0.002029j
[2025-07-30 16:13:47] [Iter 4111/4650] R4[1860/2400], Temp: 0.1198, Energy: -54.572532+0.001400j
[2025-07-30 16:13:57] [Iter 4112/4650] R4[1861/2400], Temp: 0.1194, Energy: -54.635561-0.000798j
[2025-07-30 16:14:07] [Iter 4113/4650] R4[1862/2400], Temp: 0.1189, Energy: -54.599665+0.001568j
[2025-07-30 16:14:17] [Iter 4114/4650] R4[1863/2400], Temp: 0.1185, Energy: -54.638433+0.000501j
[2025-07-30 16:14:27] [Iter 4115/4650] R4[1864/2400], Temp: 0.1181, Energy: -54.670713+0.002126j
[2025-07-30 16:14:37] [Iter 4116/4650] R4[1865/2400], Temp: 0.1177, Energy: -54.691309+0.000462j
[2025-07-30 16:14:48] [Iter 4117/4650] R4[1866/2400], Temp: 0.1173, Energy: -54.680596-0.001749j
[2025-07-30 16:14:58] [Iter 4118/4650] R4[1867/2400], Temp: 0.1168, Energy: -54.670122+0.001680j
[2025-07-30 16:15:08] [Iter 4119/4650] R4[1868/2400], Temp: 0.1164, Energy: -54.684485+0.000023j
[2025-07-30 16:15:18] [Iter 4120/4650] R4[1869/2400], Temp: 0.1160, Energy: -54.674678-0.000614j
[2025-07-30 16:15:28] [Iter 4121/4650] R4[1870/2400], Temp: 0.1156, Energy: -54.681465-0.000245j
[2025-07-30 16:15:38] [Iter 4122/4650] R4[1871/2400], Temp: 0.1152, Energy: -54.698595-0.002135j
[2025-07-30 16:15:48] [Iter 4123/4650] R4[1872/2400], Temp: 0.1147, Energy: -54.725599-0.001335j
[2025-07-30 16:15:58] [Iter 4124/4650] R4[1873/2400], Temp: 0.1143, Energy: -54.744983-0.001937j
[2025-07-30 16:16:09] [Iter 4125/4650] R4[1874/2400], Temp: 0.1139, Energy: -54.687720+0.003597j
[2025-07-30 16:16:19] [Iter 4126/4650] R4[1875/2400], Temp: 0.1135, Energy: -54.697991-0.001093j
[2025-07-30 16:16:29] [Iter 4127/4650] R4[1876/2400], Temp: 0.1131, Energy: -54.652198-0.002989j
[2025-07-30 16:16:39] [Iter 4128/4650] R4[1877/2400], Temp: 0.1127, Energy: -54.698812-0.000152j
[2025-07-30 16:16:49] [Iter 4129/4650] R4[1878/2400], Temp: 0.1123, Energy: -54.671457+0.001175j
[2025-07-30 16:16:59] [Iter 4130/4650] R4[1879/2400], Temp: 0.1118, Energy: -54.627033-0.001375j
[2025-07-30 16:17:09] [Iter 4131/4650] R4[1880/2400], Temp: 0.1114, Energy: -54.589380+0.000477j
[2025-07-30 16:17:19] [Iter 4132/4650] R4[1881/2400], Temp: 0.1110, Energy: -54.617408+0.002699j
[2025-07-30 16:17:30] [Iter 4133/4650] R4[1882/2400], Temp: 0.1106, Energy: -54.628993+0.002048j
[2025-07-30 16:17:40] [Iter 4134/4650] R4[1883/2400], Temp: 0.1102, Energy: -54.639937+0.001715j
[2025-07-30 16:17:50] [Iter 4135/4650] R4[1884/2400], Temp: 0.1098, Energy: -54.603031+0.001521j
[2025-07-30 16:18:00] [Iter 4136/4650] R4[1885/2400], Temp: 0.1094, Energy: -54.633752+0.002004j
[2025-07-30 16:18:10] [Iter 4137/4650] R4[1886/2400], Temp: 0.1090, Energy: -54.594331+0.000392j
[2025-07-30 16:18:20] [Iter 4138/4650] R4[1887/2400], Temp: 0.1086, Energy: -54.597515-0.001825j
[2025-07-30 16:18:30] [Iter 4139/4650] R4[1888/2400], Temp: 0.1082, Energy: -54.564786-0.000104j
[2025-07-30 16:18:40] [Iter 4140/4650] R4[1889/2400], Temp: 0.1077, Energy: -54.551749-0.000775j
[2025-07-30 16:18:50] [Iter 4141/4650] R4[1890/2400], Temp: 0.1073, Energy: -54.584048-0.001351j
[2025-07-30 16:19:00] [Iter 4142/4650] R4[1891/2400], Temp: 0.1069, Energy: -54.616677+0.000858j
[2025-07-30 16:19:11] [Iter 4143/4650] R4[1892/2400], Temp: 0.1065, Energy: -54.616889-0.000471j
[2025-07-30 16:19:21] [Iter 4144/4650] R4[1893/2400], Temp: 0.1061, Energy: -54.579482-0.000256j
[2025-07-30 16:19:31] [Iter 4145/4650] R4[1894/2400], Temp: 0.1057, Energy: -54.610271-0.001283j
[2025-07-30 16:19:41] [Iter 4146/4650] R4[1895/2400], Temp: 0.1053, Energy: -54.550499+0.000147j
[2025-07-30 16:19:51] [Iter 4147/4650] R4[1896/2400], Temp: 0.1049, Energy: -54.577272-0.000497j
[2025-07-30 16:20:01] [Iter 4148/4650] R4[1897/2400], Temp: 0.1045, Energy: -54.502973-0.000172j
[2025-07-30 16:20:11] [Iter 4149/4650] R4[1898/2400], Temp: 0.1041, Energy: -54.549098-0.002081j
[2025-07-30 16:20:21] [Iter 4150/4650] R4[1899/2400], Temp: 0.1037, Energy: -54.589087-0.001498j
[2025-07-30 16:20:31] [Iter 4151/4650] R4[1900/2400], Temp: 0.1033, Energy: -54.583295+0.000010j
[2025-07-30 16:20:42] [Iter 4152/4650] R4[1901/2400], Temp: 0.1029, Energy: -54.587981+0.000464j
[2025-07-30 16:20:52] [Iter 4153/4650] R4[1902/2400], Temp: 0.1025, Energy: -54.593439-0.001275j
[2025-07-30 16:21:02] [Iter 4154/4650] R4[1903/2400], Temp: 0.1021, Energy: -54.622010+0.000256j
[2025-07-30 16:21:12] [Iter 4155/4650] R4[1904/2400], Temp: 0.1017, Energy: -54.648588+0.001948j
[2025-07-30 16:21:22] [Iter 4156/4650] R4[1905/2400], Temp: 0.1013, Energy: -54.661347+0.000495j
[2025-07-30 16:21:32] [Iter 4157/4650] R4[1906/2400], Temp: 0.1009, Energy: -54.652331+0.001084j
[2025-07-30 16:21:42] [Iter 4158/4650] R4[1907/2400], Temp: 0.1006, Energy: -54.625351+0.000378j
[2025-07-30 16:21:52] [Iter 4159/4650] R4[1908/2400], Temp: 0.1002, Energy: -54.633051-0.001386j
[2025-07-30 16:22:02] [Iter 4160/4650] R4[1909/2400], Temp: 0.0998, Energy: -54.664637+0.000535j
[2025-07-30 16:22:13] [Iter 4161/4650] R4[1910/2400], Temp: 0.0994, Energy: -54.712004-0.001518j
[2025-07-30 16:22:23] [Iter 4162/4650] R4[1911/2400], Temp: 0.0990, Energy: -54.614958-0.000747j
[2025-07-30 16:22:33] [Iter 4163/4650] R4[1912/2400], Temp: 0.0986, Energy: -54.612184+0.000384j
[2025-07-30 16:22:43] [Iter 4164/4650] R4[1913/2400], Temp: 0.0982, Energy: -54.676968-0.000386j
[2025-07-30 16:22:53] [Iter 4165/4650] R4[1914/2400], Temp: 0.0978, Energy: -54.660795-0.000435j
[2025-07-30 16:23:03] [Iter 4166/4650] R4[1915/2400], Temp: 0.0974, Energy: -54.649448+0.002485j
[2025-07-30 16:23:13] [Iter 4167/4650] R4[1916/2400], Temp: 0.0970, Energy: -54.667608+0.001078j
[2025-07-30 16:23:23] [Iter 4168/4650] R4[1917/2400], Temp: 0.0966, Energy: -54.637435-0.002229j
[2025-07-30 16:23:34] [Iter 4169/4650] R4[1918/2400], Temp: 0.0963, Energy: -54.674625+0.000873j
[2025-07-30 16:23:44] [Iter 4170/4650] R4[1919/2400], Temp: 0.0959, Energy: -54.676719-0.000146j
[2025-07-30 16:23:54] [Iter 4171/4650] R4[1920/2400], Temp: 0.0955, Energy: -54.654921-0.003149j
[2025-07-30 16:24:04] [Iter 4172/4650] R4[1921/2400], Temp: 0.0951, Energy: -54.598548-0.000063j
[2025-07-30 16:24:14] [Iter 4173/4650] R4[1922/2400], Temp: 0.0947, Energy: -54.634538-0.001727j
[2025-07-30 16:24:24] [Iter 4174/4650] R4[1923/2400], Temp: 0.0943, Energy: -54.635038-0.002587j
[2025-07-30 16:24:34] [Iter 4175/4650] R4[1924/2400], Temp: 0.0940, Energy: -54.624961+0.000899j
[2025-07-30 16:24:44] [Iter 4176/4650] R4[1925/2400], Temp: 0.0936, Energy: -54.659582+0.002614j
[2025-07-30 16:24:55] [Iter 4177/4650] R4[1926/2400], Temp: 0.0932, Energy: -54.695042+0.000536j
[2025-07-30 16:25:05] [Iter 4178/4650] R4[1927/2400], Temp: 0.0928, Energy: -54.656967+0.000065j
[2025-07-30 16:25:15] [Iter 4179/4650] R4[1928/2400], Temp: 0.0924, Energy: -54.659268+0.000191j
[2025-07-30 16:25:25] [Iter 4180/4650] R4[1929/2400], Temp: 0.0921, Energy: -54.624852-0.000623j
[2025-07-30 16:25:35] [Iter 4181/4650] R4[1930/2400], Temp: 0.0917, Energy: -54.652666+0.001879j
[2025-07-30 16:25:45] [Iter 4182/4650] R4[1931/2400], Temp: 0.0913, Energy: -54.628224+0.000493j
[2025-07-30 16:25:55] [Iter 4183/4650] R4[1932/2400], Temp: 0.0909, Energy: -54.594856+0.001508j
[2025-07-30 16:26:05] [Iter 4184/4650] R4[1933/2400], Temp: 0.0905, Energy: -54.598480+0.003505j
[2025-07-30 16:26:15] [Iter 4185/4650] R4[1934/2400], Temp: 0.0902, Energy: -54.607251-0.001278j
[2025-07-30 16:26:26] [Iter 4186/4650] R4[1935/2400], Temp: 0.0898, Energy: -54.609461+0.000963j
[2025-07-30 16:26:36] [Iter 4187/4650] R4[1936/2400], Temp: 0.0894, Energy: -54.581571-0.001304j
[2025-07-30 16:26:46] [Iter 4188/4650] R4[1937/2400], Temp: 0.0891, Energy: -54.600414+0.001024j
[2025-07-30 16:26:56] [Iter 4189/4650] R4[1938/2400], Temp: 0.0887, Energy: -54.648427-0.001037j
[2025-07-30 16:27:06] [Iter 4190/4650] R4[1939/2400], Temp: 0.0883, Energy: -54.579774-0.000160j
[2025-07-30 16:27:16] [Iter 4191/4650] R4[1940/2400], Temp: 0.0879, Energy: -54.580126+0.000295j
[2025-07-30 16:27:26] [Iter 4192/4650] R4[1941/2400], Temp: 0.0876, Energy: -54.589091-0.000765j
[2025-07-30 16:27:36] [Iter 4193/4650] R4[1942/2400], Temp: 0.0872, Energy: -54.550063+0.001285j
[2025-07-30 16:27:46] [Iter 4194/4650] R4[1943/2400], Temp: 0.0868, Energy: -54.574394-0.000352j
[2025-07-30 16:27:57] [Iter 4195/4650] R4[1944/2400], Temp: 0.0865, Energy: -54.545879-0.000407j
[2025-07-30 16:28:07] [Iter 4196/4650] R4[1945/2400], Temp: 0.0861, Energy: -54.567975-0.000733j
[2025-07-30 16:28:17] [Iter 4197/4650] R4[1946/2400], Temp: 0.0857, Energy: -54.592967-0.001184j
[2025-07-30 16:28:27] [Iter 4198/4650] R4[1947/2400], Temp: 0.0854, Energy: -54.603639+0.000286j
[2025-07-30 16:28:37] [Iter 4199/4650] R4[1948/2400], Temp: 0.0850, Energy: -54.597087+0.000696j
[2025-07-30 16:28:47] [Iter 4200/4650] R4[1949/2400], Temp: 0.0846, Energy: -54.569690+0.000446j
[2025-07-30 16:28:57] [Iter 4201/4650] R4[1950/2400], Temp: 0.0843, Energy: -54.640827+0.000110j
[2025-07-30 16:29:07] [Iter 4202/4650] R4[1951/2400], Temp: 0.0839, Energy: -54.638979+0.001173j
[2025-07-30 16:29:18] [Iter 4203/4650] R4[1952/2400], Temp: 0.0835, Energy: -54.632281-0.001233j
[2025-07-30 16:29:28] [Iter 4204/4650] R4[1953/2400], Temp: 0.0832, Energy: -54.669813-0.001595j
[2025-07-30 16:29:38] [Iter 4205/4650] R4[1954/2400], Temp: 0.0828, Energy: -54.626592-0.001720j
[2025-07-30 16:29:48] [Iter 4206/4650] R4[1955/2400], Temp: 0.0825, Energy: -54.641667-0.000808j
[2025-07-30 16:29:58] [Iter 4207/4650] R4[1956/2400], Temp: 0.0821, Energy: -54.664025-0.001053j
[2025-07-30 16:30:08] [Iter 4208/4650] R4[1957/2400], Temp: 0.0817, Energy: -54.662906-0.000395j
[2025-07-30 16:30:18] [Iter 4209/4650] R4[1958/2400], Temp: 0.0814, Energy: -54.631999-0.000784j
[2025-07-30 16:30:28] [Iter 4210/4650] R4[1959/2400], Temp: 0.0810, Energy: -54.642565+0.000435j
[2025-07-30 16:30:39] [Iter 4211/4650] R4[1960/2400], Temp: 0.0807, Energy: -54.664534-0.000327j
[2025-07-30 16:30:49] [Iter 4212/4650] R4[1961/2400], Temp: 0.0803, Energy: -54.672540+0.001073j
[2025-07-30 16:30:59] [Iter 4213/4650] R4[1962/2400], Temp: 0.0800, Energy: -54.674723+0.001351j
[2025-07-30 16:31:09] [Iter 4214/4650] R4[1963/2400], Temp: 0.0796, Energy: -54.683401+0.000642j
[2025-07-30 16:31:19] [Iter 4215/4650] R4[1964/2400], Temp: 0.0792, Energy: -54.694142+0.000416j
[2025-07-30 16:31:29] [Iter 4216/4650] R4[1965/2400], Temp: 0.0789, Energy: -54.658570+0.001247j
[2025-07-30 16:31:39] [Iter 4217/4650] R4[1966/2400], Temp: 0.0785, Energy: -54.653276-0.002097j
[2025-07-30 16:31:49] [Iter 4218/4650] R4[1967/2400], Temp: 0.0782, Energy: -54.637786-0.000660j
[2025-07-30 16:31:59] [Iter 4219/4650] R4[1968/2400], Temp: 0.0778, Energy: -54.600571-0.000230j
[2025-07-30 16:32:10] [Iter 4220/4650] R4[1969/2400], Temp: 0.0775, Energy: -54.689388+0.001527j
[2025-07-30 16:32:20] [Iter 4221/4650] R4[1970/2400], Temp: 0.0771, Energy: -54.654016-0.000163j
[2025-07-30 16:32:30] [Iter 4222/4650] R4[1971/2400], Temp: 0.0768, Energy: -54.661791-0.000739j
[2025-07-30 16:32:40] [Iter 4223/4650] R4[1972/2400], Temp: 0.0764, Energy: -54.696658-0.000081j
[2025-07-30 16:32:50] [Iter 4224/4650] R4[1973/2400], Temp: 0.0761, Energy: -54.681322+0.001305j
[2025-07-30 16:33:00] [Iter 4225/4650] R4[1974/2400], Temp: 0.0757, Energy: -54.623543+0.000820j
[2025-07-30 16:33:10] [Iter 4226/4650] R4[1975/2400], Temp: 0.0754, Energy: -54.675055+0.002295j
[2025-07-30 16:33:20] [Iter 4227/4650] R4[1976/2400], Temp: 0.0751, Energy: -54.689973-0.000414j
[2025-07-30 16:33:30] [Iter 4228/4650] R4[1977/2400], Temp: 0.0747, Energy: -54.719482-0.000750j
[2025-07-30 16:33:41] [Iter 4229/4650] R4[1978/2400], Temp: 0.0744, Energy: -54.675868-0.000656j
[2025-07-30 16:33:51] [Iter 4230/4650] R4[1979/2400], Temp: 0.0740, Energy: -54.668275-0.000318j
[2025-07-30 16:34:01] [Iter 4231/4650] R4[1980/2400], Temp: 0.0737, Energy: -54.716899+0.001576j
[2025-07-30 16:34:11] [Iter 4232/4650] R4[1981/2400], Temp: 0.0733, Energy: -54.654387+0.000506j
[2025-07-30 16:34:21] [Iter 4233/4650] R4[1982/2400], Temp: 0.0730, Energy: -54.633790-0.000788j
[2025-07-30 16:34:31] [Iter 4234/4650] R4[1983/2400], Temp: 0.0727, Energy: -54.650195+0.001734j
[2025-07-30 16:34:41] [Iter 4235/4650] R4[1984/2400], Temp: 0.0723, Energy: -54.675445+0.004199j
[2025-07-30 16:34:51] [Iter 4236/4650] R4[1985/2400], Temp: 0.0720, Energy: -54.666670-0.003545j
[2025-07-30 16:35:02] [Iter 4237/4650] R4[1986/2400], Temp: 0.0716, Energy: -54.619502-0.003227j
[2025-07-30 16:35:12] [Iter 4238/4650] R4[1987/2400], Temp: 0.0713, Energy: -54.598490-0.001346j
[2025-07-30 16:35:22] [Iter 4239/4650] R4[1988/2400], Temp: 0.0710, Energy: -54.611388+0.002413j
[2025-07-30 16:35:32] [Iter 4240/4650] R4[1989/2400], Temp: 0.0706, Energy: -54.603515-0.000129j
[2025-07-30 16:35:42] [Iter 4241/4650] R4[1990/2400], Temp: 0.0703, Energy: -54.630580+0.000028j
[2025-07-30 16:35:52] [Iter 4242/4650] R4[1991/2400], Temp: 0.0700, Energy: -54.652529+0.000007j
[2025-07-30 16:36:02] [Iter 4243/4650] R4[1992/2400], Temp: 0.0696, Energy: -54.652057-0.001276j
[2025-07-30 16:36:12] [Iter 4244/4650] R4[1993/2400], Temp: 0.0693, Energy: -54.702594-0.000299j
[2025-07-30 16:36:22] [Iter 4245/4650] R4[1994/2400], Temp: 0.0690, Energy: -54.668962-0.000196j
[2025-07-30 16:36:33] [Iter 4246/4650] R4[1995/2400], Temp: 0.0686, Energy: -54.728904+0.000063j
[2025-07-30 16:36:43] [Iter 4247/4650] R4[1996/2400], Temp: 0.0683, Energy: -54.670728+0.001923j
[2025-07-30 16:36:53] [Iter 4248/4650] R4[1997/2400], Temp: 0.0680, Energy: -54.730292+0.000766j
[2025-07-30 16:37:03] [Iter 4249/4650] R4[1998/2400], Temp: 0.0676, Energy: -54.631820+0.000522j
[2025-07-30 16:37:13] [Iter 4250/4650] R4[1999/2400], Temp: 0.0673, Energy: -54.691356+0.002055j
[2025-07-30 16:37:23] [Iter 4251/4650] R4[2000/2400], Temp: 0.0670, Energy: -54.687961-0.002693j
[2025-07-30 16:37:33] [Iter 4252/4650] R4[2001/2400], Temp: 0.0667, Energy: -54.614061-0.000322j
[2025-07-30 16:37:43] [Iter 4253/4650] R4[2002/2400], Temp: 0.0663, Energy: -54.588750+0.000999j
[2025-07-30 16:37:53] [Iter 4254/4650] R4[2003/2400], Temp: 0.0660, Energy: -54.646787-0.001013j
[2025-07-30 16:38:04] [Iter 4255/4650] R4[2004/2400], Temp: 0.0657, Energy: -54.603047-0.001495j
[2025-07-30 16:38:14] [Iter 4256/4650] R4[2005/2400], Temp: 0.0654, Energy: -54.615854-0.001592j
[2025-07-30 16:38:24] [Iter 4257/4650] R4[2006/2400], Temp: 0.0650, Energy: -54.561690+0.000741j
[2025-07-30 16:38:34] [Iter 4258/4650] R4[2007/2400], Temp: 0.0647, Energy: -54.612283+0.000633j
[2025-07-30 16:38:44] [Iter 4259/4650] R4[2008/2400], Temp: 0.0644, Energy: -54.583090+0.000806j
[2025-07-30 16:38:54] [Iter 4260/4650] R4[2009/2400], Temp: 0.0641, Energy: -54.568799+0.001110j
[2025-07-30 16:39:04] [Iter 4261/4650] R4[2010/2400], Temp: 0.0638, Energy: -54.569184-0.000746j
[2025-07-30 16:39:15] [Iter 4262/4650] R4[2011/2400], Temp: 0.0634, Energy: -54.588897-0.000420j
[2025-07-30 16:39:25] [Iter 4263/4650] R4[2012/2400], Temp: 0.0631, Energy: -54.563638+0.000109j
[2025-07-30 16:39:35] [Iter 4264/4650] R4[2013/2400], Temp: 0.0628, Energy: -54.622211-0.000964j
[2025-07-30 16:39:45] [Iter 4265/4650] R4[2014/2400], Temp: 0.0625, Energy: -54.552259+0.001086j
[2025-07-30 16:39:55] [Iter 4266/4650] R4[2015/2400], Temp: 0.0622, Energy: -54.642805+0.000732j
[2025-07-30 16:40:05] [Iter 4267/4650] R4[2016/2400], Temp: 0.0618, Energy: -54.612299-0.000430j
[2025-07-30 16:40:15] [Iter 4268/4650] R4[2017/2400], Temp: 0.0615, Energy: -54.566001+0.000531j
[2025-07-30 16:40:25] [Iter 4269/4650] R4[2018/2400], Temp: 0.0612, Energy: -54.606919+0.000869j
[2025-07-30 16:40:36] [Iter 4270/4650] R4[2019/2400], Temp: 0.0609, Energy: -54.628320-0.000142j
[2025-07-30 16:40:46] [Iter 4271/4650] R4[2020/2400], Temp: 0.0606, Energy: -54.556055-0.000403j
[2025-07-30 16:40:56] [Iter 4272/4650] R4[2021/2400], Temp: 0.0603, Energy: -54.598441-0.002087j
[2025-07-30 16:41:06] [Iter 4273/4650] R4[2022/2400], Temp: 0.0600, Energy: -54.612960-0.000554j
[2025-07-30 16:41:16] [Iter 4274/4650] R4[2023/2400], Temp: 0.0597, Energy: -54.594614+0.000592j
[2025-07-30 16:41:26] [Iter 4275/4650] R4[2024/2400], Temp: 0.0593, Energy: -54.620077+0.002719j
[2025-07-30 16:41:36] [Iter 4276/4650] R4[2025/2400], Temp: 0.0590, Energy: -54.602268+0.000225j
[2025-07-30 16:41:46] [Iter 4277/4650] R4[2026/2400], Temp: 0.0587, Energy: -54.639674+0.002445j
[2025-07-30 16:41:56] [Iter 4278/4650] R4[2027/2400], Temp: 0.0584, Energy: -54.613341-0.002015j
[2025-07-30 16:42:07] [Iter 4279/4650] R4[2028/2400], Temp: 0.0581, Energy: -54.631834+0.000069j
[2025-07-30 16:42:17] [Iter 4280/4650] R4[2029/2400], Temp: 0.0578, Energy: -54.605665-0.000215j
[2025-07-30 16:42:27] [Iter 4281/4650] R4[2030/2400], Temp: 0.0575, Energy: -54.610701+0.000506j
[2025-07-30 16:42:37] [Iter 4282/4650] R4[2031/2400], Temp: 0.0572, Energy: -54.561690-0.000901j
[2025-07-30 16:42:47] [Iter 4283/4650] R4[2032/2400], Temp: 0.0569, Energy: -54.601589-0.000415j
[2025-07-30 16:42:57] [Iter 4284/4650] R4[2033/2400], Temp: 0.0566, Energy: -54.667860-0.001717j
[2025-07-30 16:43:07] [Iter 4285/4650] R4[2034/2400], Temp: 0.0563, Energy: -54.621954-0.001340j
[2025-07-30 16:43:17] [Iter 4286/4650] R4[2035/2400], Temp: 0.0560, Energy: -54.619116+0.000061j
[2025-07-30 16:43:27] [Iter 4287/4650] R4[2036/2400], Temp: 0.0557, Energy: -54.634133-0.001938j
[2025-07-30 16:43:38] [Iter 4288/4650] R4[2037/2400], Temp: 0.0554, Energy: -54.640393+0.000750j
[2025-07-30 16:43:48] [Iter 4289/4650] R4[2038/2400], Temp: 0.0551, Energy: -54.630461-0.001037j
[2025-07-30 16:43:58] [Iter 4290/4650] R4[2039/2400], Temp: 0.0548, Energy: -54.667130+0.001317j
[2025-07-30 16:44:08] [Iter 4291/4650] R4[2040/2400], Temp: 0.0545, Energy: -54.648920-0.000467j
[2025-07-30 16:44:18] [Iter 4292/4650] R4[2041/2400], Temp: 0.0542, Energy: -54.637263+0.001165j
[2025-07-30 16:44:28] [Iter 4293/4650] R4[2042/2400], Temp: 0.0539, Energy: -54.605768+0.000674j
[2025-07-30 16:44:38] [Iter 4294/4650] R4[2043/2400], Temp: 0.0536, Energy: -54.563332+0.000160j
[2025-07-30 16:44:48] [Iter 4295/4650] R4[2044/2400], Temp: 0.0533, Energy: -54.556881-0.000174j
[2025-07-30 16:44:58] [Iter 4296/4650] R4[2045/2400], Temp: 0.0530, Energy: -54.586919+0.000912j
[2025-07-30 16:45:09] [Iter 4297/4650] R4[2046/2400], Temp: 0.0527, Energy: -54.538457+0.000420j
[2025-07-30 16:45:19] [Iter 4298/4650] R4[2047/2400], Temp: 0.0524, Energy: -54.607766-0.000206j
[2025-07-30 16:45:29] [Iter 4299/4650] R4[2048/2400], Temp: 0.0521, Energy: -54.639290-0.002274j
[2025-07-30 16:45:39] [Iter 4300/4650] R4[2049/2400], Temp: 0.0519, Energy: -54.674236+0.000324j
[2025-07-30 16:45:49] [Iter 4301/4650] R4[2050/2400], Temp: 0.0516, Energy: -54.707629+0.000961j
[2025-07-30 16:45:59] [Iter 4302/4650] R4[2051/2400], Temp: 0.0513, Energy: -54.671810-0.000078j
[2025-07-30 16:46:09] [Iter 4303/4650] R4[2052/2400], Temp: 0.0510, Energy: -54.653166-0.002536j
[2025-07-30 16:46:19] [Iter 4304/4650] R4[2053/2400], Temp: 0.0507, Energy: -54.579896+0.000887j
[2025-07-30 16:46:30] [Iter 4305/4650] R4[2054/2400], Temp: 0.0504, Energy: -54.608343-0.001499j
[2025-07-30 16:46:40] [Iter 4306/4650] R4[2055/2400], Temp: 0.0501, Energy: -54.594827-0.001157j
[2025-07-30 16:46:50] [Iter 4307/4650] R4[2056/2400], Temp: 0.0498, Energy: -54.567206-0.000067j
[2025-07-30 16:47:00] [Iter 4308/4650] R4[2057/2400], Temp: 0.0496, Energy: -54.606276+0.002367j
[2025-07-30 16:47:10] [Iter 4309/4650] R4[2058/2400], Temp: 0.0493, Energy: -54.588188+0.000942j
[2025-07-30 16:47:20] [Iter 4310/4650] R4[2059/2400], Temp: 0.0490, Energy: -54.579188-0.002080j
[2025-07-30 16:47:30] [Iter 4311/4650] R4[2060/2400], Temp: 0.0487, Energy: -54.607681-0.001620j
[2025-07-30 16:47:40] [Iter 4312/4650] R4[2061/2400], Temp: 0.0484, Energy: -54.600647+0.000289j
[2025-07-30 16:47:51] [Iter 4313/4650] R4[2062/2400], Temp: 0.0481, Energy: -54.670082-0.000773j
[2025-07-30 16:48:01] [Iter 4314/4650] R4[2063/2400], Temp: 0.0479, Energy: -54.672397-0.002001j
[2025-07-30 16:48:11] [Iter 4315/4650] R4[2064/2400], Temp: 0.0476, Energy: -54.707440+0.000161j
[2025-07-30 16:48:21] [Iter 4316/4650] R4[2065/2400], Temp: 0.0473, Energy: -54.652481+0.001934j
[2025-07-30 16:48:31] [Iter 4317/4650] R4[2066/2400], Temp: 0.0470, Energy: -54.656001+0.002458j
[2025-07-30 16:48:41] [Iter 4318/4650] R4[2067/2400], Temp: 0.0468, Energy: -54.659959+0.001995j
[2025-07-30 16:48:51] [Iter 4319/4650] R4[2068/2400], Temp: 0.0465, Energy: -54.633717+0.000024j
[2025-07-30 16:49:01] [Iter 4320/4650] R4[2069/2400], Temp: 0.0462, Energy: -54.633524-0.000900j
[2025-07-30 16:49:11] [Iter 4321/4650] R4[2070/2400], Temp: 0.0459, Energy: -54.649143+0.000522j
[2025-07-30 16:49:22] [Iter 4322/4650] R4[2071/2400], Temp: 0.0457, Energy: -54.652964-0.000234j
[2025-07-30 16:49:32] [Iter 4323/4650] R4[2072/2400], Temp: 0.0454, Energy: -54.574726-0.001013j
[2025-07-30 16:49:42] [Iter 4324/4650] R4[2073/2400], Temp: 0.0451, Energy: -54.588391-0.001677j
[2025-07-30 16:49:52] [Iter 4325/4650] R4[2074/2400], Temp: 0.0448, Energy: -54.611377-0.002064j
[2025-07-30 16:50:02] [Iter 4326/4650] R4[2075/2400], Temp: 0.0446, Energy: -54.575707+0.001444j
[2025-07-30 16:50:12] [Iter 4327/4650] R4[2076/2400], Temp: 0.0443, Energy: -54.637930-0.000257j
[2025-07-30 16:50:22] [Iter 4328/4650] R4[2077/2400], Temp: 0.0440, Energy: -54.624506+0.000517j
[2025-07-30 16:50:32] [Iter 4329/4650] R4[2078/2400], Temp: 0.0438, Energy: -54.614590-0.001043j
[2025-07-30 16:50:42] [Iter 4330/4650] R4[2079/2400], Temp: 0.0435, Energy: -54.571333+0.001781j
[2025-07-30 16:50:52] [Iter 4331/4650] R4[2080/2400], Temp: 0.0432, Energy: -54.616431+0.002348j
[2025-07-30 16:51:03] [Iter 4332/4650] R4[2081/2400], Temp: 0.0430, Energy: -54.644905+0.000915j
[2025-07-30 16:51:13] [Iter 4333/4650] R4[2082/2400], Temp: 0.0427, Energy: -54.673924+0.001168j
[2025-07-30 16:51:23] [Iter 4334/4650] R4[2083/2400], Temp: 0.0424, Energy: -54.629448-0.000936j
[2025-07-30 16:51:33] [Iter 4335/4650] R4[2084/2400], Temp: 0.0422, Energy: -54.628713-0.001020j
[2025-07-30 16:51:43] [Iter 4336/4650] R4[2085/2400], Temp: 0.0419, Energy: -54.709713-0.000405j
[2025-07-30 16:51:53] [Iter 4337/4650] R4[2086/2400], Temp: 0.0416, Energy: -54.711316+0.001404j
[2025-07-30 16:52:03] [Iter 4338/4650] R4[2087/2400], Temp: 0.0414, Energy: -54.641666-0.002309j
[2025-07-30 16:52:13] [Iter 4339/4650] R4[2088/2400], Temp: 0.0411, Energy: -54.631399-0.000721j
[2025-07-30 16:52:24] [Iter 4340/4650] R4[2089/2400], Temp: 0.0409, Energy: -54.623239+0.000955j
[2025-07-30 16:52:34] [Iter 4341/4650] R4[2090/2400], Temp: 0.0406, Energy: -54.659717+0.000965j
[2025-07-30 16:52:44] [Iter 4342/4650] R4[2091/2400], Temp: 0.0403, Energy: -54.622138-0.000856j
[2025-07-30 16:52:54] [Iter 4343/4650] R4[2092/2400], Temp: 0.0401, Energy: -54.638698-0.001276j
[2025-07-30 16:53:04] [Iter 4344/4650] R4[2093/2400], Temp: 0.0398, Energy: -54.640417-0.000060j
[2025-07-30 16:53:14] [Iter 4345/4650] R4[2094/2400], Temp: 0.0396, Energy: -54.604842+0.002187j
[2025-07-30 16:53:24] [Iter 4346/4650] R4[2095/2400], Temp: 0.0393, Energy: -54.595926+0.001380j
[2025-07-30 16:53:34] [Iter 4347/4650] R4[2096/2400], Temp: 0.0391, Energy: -54.532177+0.000034j
[2025-07-30 16:53:45] [Iter 4348/4650] R4[2097/2400], Temp: 0.0388, Energy: -54.568574-0.000130j
[2025-07-30 16:53:55] [Iter 4349/4650] R4[2098/2400], Temp: 0.0386, Energy: -54.603339-0.000901j
[2025-07-30 16:54:05] [Iter 4350/4650] R4[2099/2400], Temp: 0.0383, Energy: -54.641060-0.000360j
[2025-07-30 16:54:15] [Iter 4351/4650] R4[2100/2400], Temp: 0.0381, Energy: -54.586484-0.001031j
[2025-07-30 16:54:25] [Iter 4352/4650] R4[2101/2400], Temp: 0.0378, Energy: -54.620123-0.001618j
[2025-07-30 16:54:35] [Iter 4353/4650] R4[2102/2400], Temp: 0.0376, Energy: -54.671418-0.000359j
[2025-07-30 16:54:45] [Iter 4354/4650] R4[2103/2400], Temp: 0.0373, Energy: -54.605029-0.000087j
[2025-07-30 16:54:55] [Iter 4355/4650] R4[2104/2400], Temp: 0.0371, Energy: -54.610801-0.000308j
[2025-07-30 16:55:05] [Iter 4356/4650] R4[2105/2400], Temp: 0.0368, Energy: -54.582094+0.000501j
[2025-07-30 16:55:16] [Iter 4357/4650] R4[2106/2400], Temp: 0.0366, Energy: -54.592642+0.003396j
[2025-07-30 16:55:26] [Iter 4358/4650] R4[2107/2400], Temp: 0.0363, Energy: -54.657420+0.001384j
[2025-07-30 16:55:36] [Iter 4359/4650] R4[2108/2400], Temp: 0.0361, Energy: -54.646934-0.002439j
[2025-07-30 16:55:46] [Iter 4360/4650] R4[2109/2400], Temp: 0.0358, Energy: -54.657793-0.001562j
[2025-07-30 16:55:56] [Iter 4361/4650] R4[2110/2400], Temp: 0.0356, Energy: -54.722689+0.000617j
[2025-07-30 16:56:06] [Iter 4362/4650] R4[2111/2400], Temp: 0.0354, Energy: -54.690038+0.001184j
[2025-07-30 16:56:16] [Iter 4363/4650] R4[2112/2400], Temp: 0.0351, Energy: -54.656619-0.000122j
[2025-07-30 16:56:26] [Iter 4364/4650] R4[2113/2400], Temp: 0.0349, Energy: -54.661197+0.001423j
[2025-07-30 16:56:36] [Iter 4365/4650] R4[2114/2400], Temp: 0.0346, Energy: -54.669596-0.000022j
[2025-07-30 16:56:47] [Iter 4366/4650] R4[2115/2400], Temp: 0.0344, Energy: -54.704254-0.000965j
[2025-07-30 16:56:57] [Iter 4367/4650] R4[2116/2400], Temp: 0.0342, Energy: -54.724979+0.000432j
[2025-07-30 16:57:07] [Iter 4368/4650] R4[2117/2400], Temp: 0.0339, Energy: -54.703042-0.000497j
[2025-07-30 16:57:17] [Iter 4369/4650] R4[2118/2400], Temp: 0.0337, Energy: -54.743891-0.000297j
[2025-07-30 16:57:27] [Iter 4370/4650] R4[2119/2400], Temp: 0.0334, Energy: -54.730526-0.003081j
[2025-07-30 16:57:37] [Iter 4371/4650] R4[2120/2400], Temp: 0.0332, Energy: -54.699644-0.001551j
[2025-07-30 16:57:47] [Iter 4372/4650] R4[2121/2400], Temp: 0.0330, Energy: -54.682998+0.002943j
[2025-07-30 16:57:57] [Iter 4373/4650] R4[2122/2400], Temp: 0.0327, Energy: -54.637038+0.002153j
[2025-07-30 16:58:07] [Iter 4374/4650] R4[2123/2400], Temp: 0.0325, Energy: -54.631768+0.000454j
[2025-07-30 16:58:18] [Iter 4375/4650] R4[2124/2400], Temp: 0.0323, Energy: -54.573075-0.000020j
[2025-07-30 16:58:28] [Iter 4376/4650] R4[2125/2400], Temp: 0.0320, Energy: -54.597894-0.000827j
[2025-07-30 16:58:38] [Iter 4377/4650] R4[2126/2400], Temp: 0.0318, Energy: -54.569787+0.001541j
[2025-07-30 16:58:48] [Iter 4378/4650] R4[2127/2400], Temp: 0.0316, Energy: -54.608461+0.001033j
[2025-07-30 16:58:58] [Iter 4379/4650] R4[2128/2400], Temp: 0.0314, Energy: -54.632301+0.002161j
[2025-07-30 16:59:08] [Iter 4380/4650] R4[2129/2400], Temp: 0.0311, Energy: -54.631761+0.000797j
[2025-07-30 16:59:18] [Iter 4381/4650] R4[2130/2400], Temp: 0.0309, Energy: -54.559701+0.001791j
[2025-07-30 16:59:28] [Iter 4382/4650] R4[2131/2400], Temp: 0.0307, Energy: -54.548762+0.001122j
[2025-07-30 16:59:39] [Iter 4383/4650] R4[2132/2400], Temp: 0.0305, Energy: -54.591888-0.004448j
[2025-07-30 16:59:49] [Iter 4384/4650] R4[2133/2400], Temp: 0.0302, Energy: -54.575431-0.001673j
[2025-07-30 16:59:59] [Iter 4385/4650] R4[2134/2400], Temp: 0.0300, Energy: -54.594657-0.000326j
[2025-07-30 17:00:09] [Iter 4386/4650] R4[2135/2400], Temp: 0.0298, Energy: -54.585154-0.001570j
[2025-07-30 17:00:19] [Iter 4387/4650] R4[2136/2400], Temp: 0.0296, Energy: -54.615855-0.000642j
[2025-07-30 17:00:29] [Iter 4388/4650] R4[2137/2400], Temp: 0.0293, Energy: -54.639370+0.000471j
[2025-07-30 17:00:39] [Iter 4389/4650] R4[2138/2400], Temp: 0.0291, Energy: -54.630680+0.000829j
[2025-07-30 17:00:49] [Iter 4390/4650] R4[2139/2400], Temp: 0.0289, Energy: -54.693961+0.003114j
[2025-07-30 17:00:59] [Iter 4391/4650] R4[2140/2400], Temp: 0.0287, Energy: -54.656617+0.000945j
[2025-07-30 17:01:10] [Iter 4392/4650] R4[2141/2400], Temp: 0.0285, Energy: -54.698860+0.003469j
[2025-07-30 17:01:20] [Iter 4393/4650] R4[2142/2400], Temp: 0.0282, Energy: -54.664970-0.001006j
[2025-07-30 17:01:30] [Iter 4394/4650] R4[2143/2400], Temp: 0.0280, Energy: -54.636863+0.001001j
[2025-07-30 17:01:40] [Iter 4395/4650] R4[2144/2400], Temp: 0.0278, Energy: -54.623642-0.001399j
[2025-07-30 17:01:50] [Iter 4396/4650] R4[2145/2400], Temp: 0.0276, Energy: -54.637973+0.001811j
[2025-07-30 17:02:00] [Iter 4397/4650] R4[2146/2400], Temp: 0.0274, Energy: -54.683440-0.000094j
[2025-07-30 17:02:10] [Iter 4398/4650] R4[2147/2400], Temp: 0.0272, Energy: -54.625972-0.002469j
[2025-07-30 17:02:20] [Iter 4399/4650] R4[2148/2400], Temp: 0.0270, Energy: -54.658724-0.000820j
[2025-07-30 17:02:30] [Iter 4400/4650] R4[2149/2400], Temp: 0.0267, Energy: -54.658719-0.001264j
[2025-07-30 17:02:41] [Iter 4401/4650] R4[2150/2400], Temp: 0.0265, Energy: -54.655139+0.002104j
[2025-07-30 17:02:51] [Iter 4402/4650] R4[2151/2400], Temp: 0.0263, Energy: -54.605592+0.000141j
[2025-07-30 17:03:01] [Iter 4403/4650] R4[2152/2400], Temp: 0.0261, Energy: -54.688959+0.000610j
[2025-07-30 17:03:11] [Iter 4404/4650] R4[2153/2400], Temp: 0.0259, Energy: -54.621245+0.000114j
[2025-07-30 17:03:21] [Iter 4405/4650] R4[2154/2400], Temp: 0.0257, Energy: -54.579421+0.000366j
[2025-07-30 17:03:31] [Iter 4406/4650] R4[2155/2400], Temp: 0.0255, Energy: -54.663932+0.002450j
[2025-07-30 17:03:41] [Iter 4407/4650] R4[2156/2400], Temp: 0.0253, Energy: -54.634024+0.001771j
[2025-07-30 17:03:51] [Iter 4408/4650] R4[2157/2400], Temp: 0.0251, Energy: -54.605124+0.000636j
[2025-07-30 17:04:02] [Iter 4409/4650] R4[2158/2400], Temp: 0.0249, Energy: -54.590360+0.001342j
[2025-07-30 17:04:12] [Iter 4410/4650] R4[2159/2400], Temp: 0.0247, Energy: -54.613223-0.001140j
[2025-07-30 17:04:22] [Iter 4411/4650] R4[2160/2400], Temp: 0.0245, Energy: -54.708907+0.000416j
[2025-07-30 17:04:32] [Iter 4412/4650] R4[2161/2400], Temp: 0.0243, Energy: -54.656625+0.000603j
[2025-07-30 17:04:42] [Iter 4413/4650] R4[2162/2400], Temp: 0.0241, Energy: -54.622622+0.001639j
[2025-07-30 17:04:52] [Iter 4414/4650] R4[2163/2400], Temp: 0.0239, Energy: -54.623212-0.000228j
[2025-07-30 17:05:02] [Iter 4415/4650] R4[2164/2400], Temp: 0.0237, Energy: -54.607595-0.000130j
[2025-07-30 17:05:12] [Iter 4416/4650] R4[2165/2400], Temp: 0.0235, Energy: -54.681551-0.000042j
[2025-07-30 17:05:22] [Iter 4417/4650] R4[2166/2400], Temp: 0.0233, Energy: -54.657566+0.000057j
[2025-07-30 17:05:33] [Iter 4418/4650] R4[2167/2400], Temp: 0.0231, Energy: -54.641282-0.002669j
[2025-07-30 17:05:43] [Iter 4419/4650] R4[2168/2400], Temp: 0.0229, Energy: -54.650277+0.000177j
[2025-07-30 17:05:53] [Iter 4420/4650] R4[2169/2400], Temp: 0.0227, Energy: -54.643362-0.000886j
[2025-07-30 17:06:03] [Iter 4421/4650] R4[2170/2400], Temp: 0.0225, Energy: -54.744170-0.000288j
[2025-07-30 17:06:13] [Iter 4422/4650] R4[2171/2400], Temp: 0.0223, Energy: -54.733811+0.000410j
[2025-07-30 17:06:23] [Iter 4423/4650] R4[2172/2400], Temp: 0.0221, Energy: -54.670949-0.003073j
[2025-07-30 17:06:33] [Iter 4424/4650] R4[2173/2400], Temp: 0.0219, Energy: -54.601997-0.000582j
[2025-07-30 17:06:43] [Iter 4425/4650] R4[2174/2400], Temp: 0.0217, Energy: -54.581509-0.001447j
[2025-07-30 17:06:54] [Iter 4426/4650] R4[2175/2400], Temp: 0.0215, Energy: -54.591094+0.000453j
[2025-07-30 17:07:04] [Iter 4427/4650] R4[2176/2400], Temp: 0.0213, Energy: -54.630249-0.001051j
[2025-07-30 17:07:14] [Iter 4428/4650] R4[2177/2400], Temp: 0.0212, Energy: -54.679561-0.000634j
[2025-07-30 17:07:24] [Iter 4429/4650] R4[2178/2400], Temp: 0.0210, Energy: -54.629686+0.001343j
[2025-07-30 17:07:34] [Iter 4430/4650] R4[2179/2400], Temp: 0.0208, Energy: -54.655843+0.001617j
[2025-07-30 17:07:44] [Iter 4431/4650] R4[2180/2400], Temp: 0.0206, Energy: -54.646009+0.002931j
[2025-07-30 17:07:54] [Iter 4432/4650] R4[2181/2400], Temp: 0.0204, Energy: -54.619211+0.001627j
[2025-07-30 17:08:04] [Iter 4433/4650] R4[2182/2400], Temp: 0.0202, Energy: -54.653780+0.000748j
[2025-07-30 17:08:15] [Iter 4434/4650] R4[2183/2400], Temp: 0.0200, Energy: -54.613749-0.001036j
[2025-07-30 17:08:25] [Iter 4435/4650] R4[2184/2400], Temp: 0.0199, Energy: -54.612546-0.000004j
[2025-07-30 17:08:35] [Iter 4436/4650] R4[2185/2400], Temp: 0.0197, Energy: -54.572050+0.000428j
[2025-07-30 17:08:45] [Iter 4437/4650] R4[2186/2400], Temp: 0.0195, Energy: -54.658120-0.001656j
[2025-07-30 17:08:55] [Iter 4438/4650] R4[2187/2400], Temp: 0.0193, Energy: -54.608326+0.000449j
[2025-07-30 17:09:05] [Iter 4439/4650] R4[2188/2400], Temp: 0.0191, Energy: -54.627792+0.001067j
[2025-07-30 17:09:15] [Iter 4440/4650] R4[2189/2400], Temp: 0.0190, Energy: -54.635645-0.000405j
[2025-07-30 17:09:25] [Iter 4441/4650] R4[2190/2400], Temp: 0.0188, Energy: -54.576846-0.000102j
[2025-07-30 17:09:35] [Iter 4442/4650] R4[2191/2400], Temp: 0.0186, Energy: -54.587824-0.000340j
[2025-07-30 17:09:46] [Iter 4443/4650] R4[2192/2400], Temp: 0.0184, Energy: -54.697302+0.001249j
[2025-07-30 17:09:56] [Iter 4444/4650] R4[2193/2400], Temp: 0.0182, Energy: -54.684237-0.000099j
[2025-07-30 17:10:06] [Iter 4445/4650] R4[2194/2400], Temp: 0.0181, Energy: -54.649752+0.000984j
[2025-07-30 17:10:16] [Iter 4446/4650] R4[2195/2400], Temp: 0.0179, Energy: -54.703077-0.001604j
[2025-07-30 17:10:26] [Iter 4447/4650] R4[2196/2400], Temp: 0.0177, Energy: -54.696313-0.002319j
[2025-07-30 17:10:36] [Iter 4448/4650] R4[2197/2400], Temp: 0.0175, Energy: -54.714197-0.001134j
[2025-07-30 17:10:46] [Iter 4449/4650] R4[2198/2400], Temp: 0.0174, Energy: -54.748257+0.001382j
[2025-07-30 17:10:56] [Iter 4450/4650] R4[2199/2400], Temp: 0.0172, Energy: -54.761728-0.000928j
[2025-07-30 17:11:07] [Iter 4451/4650] R4[2200/2400], Temp: 0.0170, Energy: -54.729451-0.001611j
[2025-07-30 17:11:17] [Iter 4452/4650] R4[2201/2400], Temp: 0.0169, Energy: -54.686997-0.002188j
[2025-07-30 17:11:27] [Iter 4453/4650] R4[2202/2400], Temp: 0.0167, Energy: -54.653097+0.000558j
[2025-07-30 17:11:37] [Iter 4454/4650] R4[2203/2400], Temp: 0.0165, Energy: -54.692111+0.001926j
[2025-07-30 17:11:47] [Iter 4455/4650] R4[2204/2400], Temp: 0.0164, Energy: -54.687871+0.000570j
[2025-07-30 17:11:57] [Iter 4456/4650] R4[2205/2400], Temp: 0.0162, Energy: -54.707049+0.000665j
[2025-07-30 17:12:07] [Iter 4457/4650] R4[2206/2400], Temp: 0.0160, Energy: -54.708775-0.000648j
[2025-07-30 17:12:17] [Iter 4458/4650] R4[2207/2400], Temp: 0.0159, Energy: -54.627101+0.001511j
[2025-07-30 17:12:27] [Iter 4459/4650] R4[2208/2400], Temp: 0.0157, Energy: -54.635402+0.000139j
[2025-07-30 17:12:38] [Iter 4460/4650] R4[2209/2400], Temp: 0.0155, Energy: -54.600200-0.000630j
[2025-07-30 17:12:48] [Iter 4461/4650] R4[2210/2400], Temp: 0.0154, Energy: -54.559642+0.001700j
[2025-07-30 17:12:58] [Iter 4462/4650] R4[2211/2400], Temp: 0.0152, Energy: -54.566778-0.001020j
[2025-07-30 17:13:08] [Iter 4463/4650] R4[2212/2400], Temp: 0.0151, Energy: -54.597246-0.002486j
[2025-07-30 17:13:18] [Iter 4464/4650] R4[2213/2400], Temp: 0.0149, Energy: -54.648991-0.000815j
[2025-07-30 17:13:28] [Iter 4465/4650] R4[2214/2400], Temp: 0.0147, Energy: -54.606036-0.001290j
[2025-07-30 17:13:38] [Iter 4466/4650] R4[2215/2400], Temp: 0.0146, Energy: -54.634637-0.000312j
[2025-07-30 17:13:48] [Iter 4467/4650] R4[2216/2400], Temp: 0.0144, Energy: -54.622073+0.000038j
[2025-07-30 17:13:58] [Iter 4468/4650] R4[2217/2400], Temp: 0.0143, Energy: -54.657235-0.000262j
[2025-07-30 17:14:09] [Iter 4469/4650] R4[2218/2400], Temp: 0.0141, Energy: -54.620594-0.000321j
[2025-07-30 17:14:19] [Iter 4470/4650] R4[2219/2400], Temp: 0.0140, Energy: -54.643615-0.000292j
[2025-07-30 17:14:29] [Iter 4471/4650] R4[2220/2400], Temp: 0.0138, Energy: -54.635583+0.000156j
[2025-07-30 17:14:39] [Iter 4472/4650] R4[2221/2400], Temp: 0.0137, Energy: -54.673343+0.000845j
[2025-07-30 17:14:49] [Iter 4473/4650] R4[2222/2400], Temp: 0.0135, Energy: -54.651842-0.000818j
[2025-07-30 17:14:59] [Iter 4474/4650] R4[2223/2400], Temp: 0.0134, Energy: -54.661008+0.001016j
[2025-07-30 17:15:09] [Iter 4475/4650] R4[2224/2400], Temp: 0.0132, Energy: -54.609141+0.002575j
[2025-07-30 17:15:19] [Iter 4476/4650] R4[2225/2400], Temp: 0.0131, Energy: -54.637210+0.002428j
[2025-07-30 17:15:29] [Iter 4477/4650] R4[2226/2400], Temp: 0.0129, Energy: -54.681647+0.000654j
[2025-07-30 17:15:40] [Iter 4478/4650] R4[2227/2400], Temp: 0.0128, Energy: -54.663300+0.000406j
[2025-07-30 17:15:50] [Iter 4479/4650] R4[2228/2400], Temp: 0.0126, Energy: -54.644071+0.000466j
[2025-07-30 17:16:00] [Iter 4480/4650] R4[2229/2400], Temp: 0.0125, Energy: -54.632242+0.000788j
[2025-07-30 17:16:10] [Iter 4481/4650] R4[2230/2400], Temp: 0.0123, Energy: -54.646134+0.000311j
[2025-07-30 17:16:20] [Iter 4482/4650] R4[2231/2400], Temp: 0.0122, Energy: -54.646357+0.000004j
[2025-07-30 17:16:30] [Iter 4483/4650] R4[2232/2400], Temp: 0.0120, Energy: -54.673669+0.001191j
[2025-07-30 17:16:40] [Iter 4484/4650] R4[2233/2400], Temp: 0.0119, Energy: -54.680200+0.001468j
[2025-07-30 17:16:50] [Iter 4485/4650] R4[2234/2400], Temp: 0.0118, Energy: -54.634725+0.001858j
[2025-07-30 17:17:00] [Iter 4486/4650] R4[2235/2400], Temp: 0.0116, Energy: -54.649470+0.000078j
[2025-07-30 17:17:11] [Iter 4487/4650] R4[2236/2400], Temp: 0.0115, Energy: -54.625384+0.000122j
[2025-07-30 17:17:21] [Iter 4488/4650] R4[2237/2400], Temp: 0.0113, Energy: -54.615900-0.001743j
[2025-07-30 17:17:31] [Iter 4489/4650] R4[2238/2400], Temp: 0.0112, Energy: -54.634598-0.001219j
[2025-07-30 17:17:41] [Iter 4490/4650] R4[2239/2400], Temp: 0.0111, Energy: -54.626160-0.003623j
[2025-07-30 17:17:51] [Iter 4491/4650] R4[2240/2400], Temp: 0.0109, Energy: -54.708828-0.001453j
[2025-07-30 17:18:01] [Iter 4492/4650] R4[2241/2400], Temp: 0.0108, Energy: -54.702240-0.001339j
[2025-07-30 17:18:11] [Iter 4493/4650] R4[2242/2400], Temp: 0.0107, Energy: -54.698012+0.000541j
[2025-07-30 17:18:21] [Iter 4494/4650] R4[2243/2400], Temp: 0.0105, Energy: -54.606715-0.000017j
[2025-07-30 17:18:32] [Iter 4495/4650] R4[2244/2400], Temp: 0.0104, Energy: -54.627048-0.000317j
[2025-07-30 17:18:42] [Iter 4496/4650] R4[2245/2400], Temp: 0.0103, Energy: -54.573565-0.001824j
[2025-07-30 17:18:52] [Iter 4497/4650] R4[2246/2400], Temp: 0.0101, Energy: -54.616531+0.000555j
[2025-07-30 17:19:02] [Iter 4498/4650] R4[2247/2400], Temp: 0.0100, Energy: -54.621467+0.001183j
[2025-07-30 17:19:12] [Iter 4499/4650] R4[2248/2400], Temp: 0.0099, Energy: -54.538082+0.000834j
[2025-07-30 17:19:22] [Iter 4500/4650] R4[2249/2400], Temp: 0.0097, Energy: -54.580992-0.001691j
[2025-07-30 17:19:22] ✓ Checkpoint saved: checkpoint_iter_004500.pkl
[2025-07-30 17:19:32] [Iter 4501/4650] R4[2250/2400], Temp: 0.0096, Energy: -54.575523+0.000612j
[2025-07-30 17:19:42] [Iter 4502/4650] R4[2251/2400], Temp: 0.0095, Energy: -54.619361-0.001924j
[2025-07-30 17:19:52] [Iter 4503/4650] R4[2252/2400], Temp: 0.0094, Energy: -54.634143-0.000534j
[2025-07-30 17:20:03] [Iter 4504/4650] R4[2253/2400], Temp: 0.0092, Energy: -54.650424+0.000225j
[2025-07-30 17:20:13] [Iter 4505/4650] R4[2254/2400], Temp: 0.0091, Energy: -54.637766-0.000014j
[2025-07-30 17:20:23] [Iter 4506/4650] R4[2255/2400], Temp: 0.0090, Energy: -54.596813+0.001105j
[2025-07-30 17:20:33] [Iter 4507/4650] R4[2256/2400], Temp: 0.0089, Energy: -54.584868+0.000887j
[2025-07-30 17:20:43] [Iter 4508/4650] R4[2257/2400], Temp: 0.0087, Energy: -54.613601+0.000125j
[2025-07-30 17:20:53] [Iter 4509/4650] R4[2258/2400], Temp: 0.0086, Energy: -54.653415-0.001683j
[2025-07-30 17:21:03] [Iter 4510/4650] R4[2259/2400], Temp: 0.0085, Energy: -54.641062-0.000557j
[2025-07-30 17:21:13] [Iter 4511/4650] R4[2260/2400], Temp: 0.0084, Energy: -54.624103-0.001686j
[2025-07-30 17:21:24] [Iter 4512/4650] R4[2261/2400], Temp: 0.0083, Energy: -54.666354-0.002027j
[2025-07-30 17:21:34] [Iter 4513/4650] R4[2262/2400], Temp: 0.0081, Energy: -54.666402-0.000749j
[2025-07-30 17:21:44] [Iter 4514/4650] R4[2263/2400], Temp: 0.0080, Energy: -54.604351+0.000321j
[2025-07-30 17:21:54] [Iter 4515/4650] R4[2264/2400], Temp: 0.0079, Energy: -54.578756+0.000304j
[2025-07-30 17:22:04] [Iter 4516/4650] R4[2265/2400], Temp: 0.0078, Energy: -54.608247-0.002452j
[2025-07-30 17:22:14] [Iter 4517/4650] R4[2266/2400], Temp: 0.0077, Energy: -54.636736+0.001400j
[2025-07-30 17:22:24] [Iter 4518/4650] R4[2267/2400], Temp: 0.0076, Energy: -54.648933+0.003111j
[2025-07-30 17:22:34] [Iter 4519/4650] R4[2268/2400], Temp: 0.0074, Energy: -54.664621+0.001882j
[2025-07-30 17:22:45] [Iter 4520/4650] R4[2269/2400], Temp: 0.0073, Energy: -54.664680-0.000672j
[2025-07-30 17:22:55] [Iter 4521/4650] R4[2270/2400], Temp: 0.0072, Energy: -54.632740+0.000048j
[2025-07-30 17:23:05] [Iter 4522/4650] R4[2271/2400], Temp: 0.0071, Energy: -54.664413+0.001430j
[2025-07-30 17:23:15] [Iter 4523/4650] R4[2272/2400], Temp: 0.0070, Energy: -54.638947-0.001407j
[2025-07-30 17:23:25] [Iter 4524/4650] R4[2273/2400], Temp: 0.0069, Energy: -54.665991-0.002083j
[2025-07-30 17:23:35] [Iter 4525/4650] R4[2274/2400], Temp: 0.0068, Energy: -54.669721+0.002047j
[2025-07-30 17:23:45] [Iter 4526/4650] R4[2275/2400], Temp: 0.0067, Energy: -54.588339-0.001365j
[2025-07-30 17:23:55] [Iter 4527/4650] R4[2276/2400], Temp: 0.0066, Energy: -54.624755-0.000821j
[2025-07-30 17:24:05] [Iter 4528/4650] R4[2277/2400], Temp: 0.0065, Energy: -54.597731+0.000320j
[2025-07-30 17:24:16] [Iter 4529/4650] R4[2278/2400], Temp: 0.0064, Energy: -54.633340+0.000316j
[2025-07-30 17:24:26] [Iter 4530/4650] R4[2279/2400], Temp: 0.0063, Energy: -54.613613+0.000256j
[2025-07-30 17:24:36] [Iter 4531/4650] R4[2280/2400], Temp: 0.0062, Energy: -54.680997+0.000172j
[2025-07-30 17:24:46] [Iter 4532/4650] R4[2281/2400], Temp: 0.0061, Energy: -54.709259-0.000285j
[2025-07-30 17:24:56] [Iter 4533/4650] R4[2282/2400], Temp: 0.0060, Energy: -54.683858-0.000780j
[2025-07-30 17:25:06] [Iter 4534/4650] R4[2283/2400], Temp: 0.0059, Energy: -54.670233+0.000560j
[2025-07-30 17:25:16] [Iter 4535/4650] R4[2284/2400], Temp: 0.0058, Energy: -54.691932+0.000151j
[2025-07-30 17:25:26] [Iter 4536/4650] R4[2285/2400], Temp: 0.0057, Energy: -54.652559+0.001558j
[2025-07-30 17:25:36] [Iter 4537/4650] R4[2286/2400], Temp: 0.0056, Energy: -54.622809+0.001172j
[2025-07-30 17:25:47] [Iter 4538/4650] R4[2287/2400], Temp: 0.0055, Energy: -54.677396+0.000412j
[2025-07-30 17:25:57] [Iter 4539/4650] R4[2288/2400], Temp: 0.0054, Energy: -54.609457+0.000146j
[2025-07-30 17:26:07] [Iter 4540/4650] R4[2289/2400], Temp: 0.0053, Energy: -54.628113-0.000177j
[2025-07-30 17:26:17] [Iter 4541/4650] R4[2290/2400], Temp: 0.0052, Energy: -54.596274-0.000340j
[2025-07-30 17:26:27] [Iter 4542/4650] R4[2291/2400], Temp: 0.0051, Energy: -54.614534+0.000947j
[2025-07-30 17:26:37] [Iter 4543/4650] R4[2292/2400], Temp: 0.0050, Energy: -54.560840+0.000275j
[2025-07-30 17:26:47] [Iter 4544/4650] R4[2293/2400], Temp: 0.0049, Energy: -54.593014+0.001806j
[2025-07-30 17:26:57] [Iter 4545/4650] R4[2294/2400], Temp: 0.0048, Energy: -54.602826+0.001824j
[2025-07-30 17:27:07] [Iter 4546/4650] R4[2295/2400], Temp: 0.0047, Energy: -54.591440+0.000202j
[2025-07-30 17:27:18] [Iter 4547/4650] R4[2296/2400], Temp: 0.0046, Energy: -54.653556-0.000661j
[2025-07-30 17:27:28] [Iter 4548/4650] R4[2297/2400], Temp: 0.0045, Energy: -54.690354-0.002057j
[2025-07-30 17:27:38] [Iter 4549/4650] R4[2298/2400], Temp: 0.0045, Energy: -54.711277+0.000018j
[2025-07-30 17:27:48] [Iter 4550/4650] R4[2299/2400], Temp: 0.0044, Energy: -54.625070-0.001591j
[2025-07-30 17:27:58] [Iter 4551/4650] R4[2300/2400], Temp: 0.0043, Energy: -54.590898+0.000774j
[2025-07-30 17:28:08] [Iter 4552/4650] R4[2301/2400], Temp: 0.0042, Energy: -54.659314+0.000039j
[2025-07-30 17:28:18] [Iter 4553/4650] R4[2302/2400], Temp: 0.0041, Energy: -54.641874+0.000603j
[2025-07-30 17:28:28] [Iter 4554/4650] R4[2303/2400], Temp: 0.0040, Energy: -54.649335+0.000338j
[2025-07-30 17:28:39] [Iter 4555/4650] R4[2304/2400], Temp: 0.0039, Energy: -54.603046-0.000727j
[2025-07-30 17:28:49] [Iter 4556/4650] R4[2305/2400], Temp: 0.0039, Energy: -54.623804-0.002282j
[2025-07-30 17:28:59] [Iter 4557/4650] R4[2306/2400], Temp: 0.0038, Energy: -54.585659-0.002006j
[2025-07-30 17:29:09] [Iter 4558/4650] R4[2307/2400], Temp: 0.0037, Energy: -54.589505-0.000306j
[2025-07-30 17:29:19] [Iter 4559/4650] R4[2308/2400], Temp: 0.0036, Energy: -54.562291-0.001101j
[2025-07-30 17:29:29] [Iter 4560/4650] R4[2309/2400], Temp: 0.0035, Energy: -54.597813-0.000988j
[2025-07-30 17:29:39] [Iter 4561/4650] R4[2310/2400], Temp: 0.0035, Energy: -54.634841+0.000516j
[2025-07-30 17:29:49] [Iter 4562/4650] R4[2311/2400], Temp: 0.0034, Energy: -54.602443-0.000052j
[2025-07-30 17:29:59] [Iter 4563/4650] R4[2312/2400], Temp: 0.0033, Energy: -54.637125+0.001657j
[2025-07-30 17:30:10] [Iter 4564/4650] R4[2313/2400], Temp: 0.0032, Energy: -54.661387+0.001108j
[2025-07-30 17:30:20] [Iter 4565/4650] R4[2314/2400], Temp: 0.0032, Energy: -54.661172-0.000869j
[2025-07-30 17:30:30] [Iter 4566/4650] R4[2315/2400], Temp: 0.0031, Energy: -54.685924-0.001161j
[2025-07-30 17:30:40] [Iter 4567/4650] R4[2316/2400], Temp: 0.0030, Energy: -54.659415-0.000750j
[2025-07-30 17:30:50] [Iter 4568/4650] R4[2317/2400], Temp: 0.0029, Energy: -54.708396+0.001271j
[2025-07-30 17:31:00] [Iter 4569/4650] R4[2318/2400], Temp: 0.0029, Energy: -54.689588-0.000845j
[2025-07-30 17:31:10] [Iter 4570/4650] R4[2319/2400], Temp: 0.0028, Energy: -54.718337+0.001357j
[2025-07-30 17:31:20] [Iter 4571/4650] R4[2320/2400], Temp: 0.0027, Energy: -54.721599+0.000028j
[2025-07-30 17:31:30] [Iter 4572/4650] R4[2321/2400], Temp: 0.0027, Energy: -54.690720-0.001676j
[2025-07-30 17:31:41] [Iter 4573/4650] R4[2322/2400], Temp: 0.0026, Energy: -54.594389+0.000515j
[2025-07-30 17:31:51] [Iter 4574/4650] R4[2323/2400], Temp: 0.0025, Energy: -54.638585-0.002401j
[2025-07-30 17:32:01] [Iter 4575/4650] R4[2324/2400], Temp: 0.0025, Energy: -54.609742-0.000699j
[2025-07-30 17:32:11] [Iter 4576/4650] R4[2325/2400], Temp: 0.0024, Energy: -54.598899-0.001328j
[2025-07-30 17:32:21] [Iter 4577/4650] R4[2326/2400], Temp: 0.0023, Energy: -54.569572+0.000505j
[2025-07-30 17:32:31] [Iter 4578/4650] R4[2327/2400], Temp: 0.0023, Energy: -54.555358-0.000554j
[2025-07-30 17:32:41] [Iter 4579/4650] R4[2328/2400], Temp: 0.0022, Energy: -54.545779-0.000349j
[2025-07-30 17:32:51] [Iter 4580/4650] R4[2329/2400], Temp: 0.0022, Energy: -54.558647-0.001203j
[2025-07-30 17:33:02] [Iter 4581/4650] R4[2330/2400], Temp: 0.0021, Energy: -54.564869+0.000900j
[2025-07-30 17:33:12] [Iter 4582/4650] R4[2331/2400], Temp: 0.0020, Energy: -54.559597+0.001201j
[2025-07-30 17:33:22] [Iter 4583/4650] R4[2332/2400], Temp: 0.0020, Energy: -54.528726-0.000484j
[2025-07-30 17:33:32] [Iter 4584/4650] R4[2333/2400], Temp: 0.0019, Energy: -54.576123+0.000854j
[2025-07-30 17:33:42] [Iter 4585/4650] R4[2334/2400], Temp: 0.0019, Energy: -54.529747-0.000750j
[2025-07-30 17:33:52] [Iter 4586/4650] R4[2335/2400], Temp: 0.0018, Energy: -54.503089+0.001702j
[2025-07-30 17:34:02] [Iter 4587/4650] R4[2336/2400], Temp: 0.0018, Energy: -54.510457+0.000665j
[2025-07-30 17:34:12] [Iter 4588/4650] R4[2337/2400], Temp: 0.0017, Energy: -54.531395+0.002591j
[2025-07-30 17:34:22] [Iter 4589/4650] R4[2338/2400], Temp: 0.0016, Energy: -54.561771-0.001090j
[2025-07-30 17:34:33] [Iter 4590/4650] R4[2339/2400], Temp: 0.0016, Energy: -54.578456-0.001498j
[2025-07-30 17:34:43] [Iter 4591/4650] R4[2340/2400], Temp: 0.0015, Energy: -54.623858-0.001234j
[2025-07-30 17:34:53] [Iter 4592/4650] R4[2341/2400], Temp: 0.0015, Energy: -54.602533-0.001275j
[2025-07-30 17:35:03] [Iter 4593/4650] R4[2342/2400], Temp: 0.0014, Energy: -54.637397-0.001982j
[2025-07-30 17:35:13] [Iter 4594/4650] R4[2343/2400], Temp: 0.0014, Energy: -54.602297-0.001668j
[2025-07-30 17:35:23] [Iter 4595/4650] R4[2344/2400], Temp: 0.0013, Energy: -54.634569-0.000896j
[2025-07-30 17:35:33] [Iter 4596/4650] R4[2345/2400], Temp: 0.0013, Energy: -54.671291-0.000805j
[2025-07-30 17:35:43] [Iter 4597/4650] R4[2346/2400], Temp: 0.0012, Energy: -54.632278-0.000583j
[2025-07-30 17:35:53] [Iter 4598/4650] R4[2347/2400], Temp: 0.0012, Energy: -54.699274-0.001408j
[2025-07-30 17:36:04] [Iter 4599/4650] R4[2348/2400], Temp: 0.0012, Energy: -54.701223-0.000530j
[2025-07-30 17:36:14] [Iter 4600/4650] R4[2349/2400], Temp: 0.0011, Energy: -54.662620+0.000244j
[2025-07-30 17:36:24] [Iter 4601/4650] R4[2350/2400], Temp: 0.0011, Energy: -54.670636+0.000329j
[2025-07-30 17:36:34] [Iter 4602/4650] R4[2351/2400], Temp: 0.0010, Energy: -54.653778-0.000628j
[2025-07-30 17:36:44] [Iter 4603/4650] R4[2352/2400], Temp: 0.0010, Energy: -54.628959-0.002578j
[2025-07-30 17:36:54] [Iter 4604/4650] R4[2353/2400], Temp: 0.0009, Energy: -54.616534+0.000618j
[2025-07-30 17:37:04] [Iter 4605/4650] R4[2354/2400], Temp: 0.0009, Energy: -54.635734+0.000464j
[2025-07-30 17:37:14] [Iter 4606/4650] R4[2355/2400], Temp: 0.0009, Energy: -54.652445+0.000227j
[2025-07-30 17:37:24] [Iter 4607/4650] R4[2356/2400], Temp: 0.0008, Energy: -54.695351-0.000981j
[2025-07-30 17:37:35] [Iter 4608/4650] R4[2357/2400], Temp: 0.0008, Energy: -54.659548-0.001291j
[2025-07-30 17:37:45] [Iter 4609/4650] R4[2358/2400], Temp: 0.0008, Energy: -54.671715+0.001210j
[2025-07-30 17:37:55] [Iter 4610/4650] R4[2359/2400], Temp: 0.0007, Energy: -54.643285-0.000643j
[2025-07-30 17:38:05] [Iter 4611/4650] R4[2360/2400], Temp: 0.0007, Energy: -54.622556-0.000481j
[2025-07-30 17:38:15] [Iter 4612/4650] R4[2361/2400], Temp: 0.0007, Energy: -54.629839-0.000538j
[2025-07-30 17:38:25] [Iter 4613/4650] R4[2362/2400], Temp: 0.0006, Energy: -54.683089-0.000065j
[2025-07-30 17:38:35] [Iter 4614/4650] R4[2363/2400], Temp: 0.0006, Energy: -54.631749+0.000209j
[2025-07-30 17:38:45] [Iter 4615/4650] R4[2364/2400], Temp: 0.0006, Energy: -54.605642+0.001213j
[2025-07-30 17:38:56] [Iter 4616/4650] R4[2365/2400], Temp: 0.0005, Energy: -54.636492-0.001764j
[2025-07-30 17:39:06] [Iter 4617/4650] R4[2366/2400], Temp: 0.0005, Energy: -54.587838+0.000435j
[2025-07-30 17:39:16] [Iter 4618/4650] R4[2367/2400], Temp: 0.0005, Energy: -54.608741+0.001829j
[2025-07-30 17:39:26] [Iter 4619/4650] R4[2368/2400], Temp: 0.0004, Energy: -54.640940+0.001972j
[2025-07-30 17:39:36] [Iter 4620/4650] R4[2369/2400], Temp: 0.0004, Energy: -54.572725+0.000135j
[2025-07-30 17:39:46] [Iter 4621/4650] R4[2370/2400], Temp: 0.0004, Energy: -54.619612+0.001005j
[2025-07-30 17:39:56] [Iter 4622/4650] R4[2371/2400], Temp: 0.0004, Energy: -54.633846+0.000400j
[2025-07-30 17:40:06] [Iter 4623/4650] R4[2372/2400], Temp: 0.0003, Energy: -54.631118-0.000353j
[2025-07-30 17:40:16] [Iter 4624/4650] R4[2373/2400], Temp: 0.0003, Energy: -54.662637-0.000768j
[2025-07-30 17:40:26] [Iter 4625/4650] R4[2374/2400], Temp: 0.0003, Energy: -54.634948+0.000292j
[2025-07-30 17:40:37] [Iter 4626/4650] R4[2375/2400], Temp: 0.0003, Energy: -54.674996-0.000626j
[2025-07-30 17:40:47] [Iter 4627/4650] R4[2376/2400], Temp: 0.0002, Energy: -54.694934+0.000459j
[2025-07-30 17:40:57] [Iter 4628/4650] R4[2377/2400], Temp: 0.0002, Energy: -54.657699-0.000362j
[2025-07-30 17:41:07] [Iter 4629/4650] R4[2378/2400], Temp: 0.0002, Energy: -54.671056+0.000169j
[2025-07-30 17:41:17] [Iter 4630/4650] R4[2379/2400], Temp: 0.0002, Energy: -54.646296+0.001583j
[2025-07-30 17:41:27] [Iter 4631/4650] R4[2380/2400], Temp: 0.0002, Energy: -54.657183-0.000234j
[2025-07-30 17:41:37] [Iter 4632/4650] R4[2381/2400], Temp: 0.0002, Energy: -54.636533-0.000938j
[2025-07-30 17:41:47] [Iter 4633/4650] R4[2382/2400], Temp: 0.0001, Energy: -54.613501+0.000712j
[2025-07-30 17:41:57] [Iter 4634/4650] R4[2383/2400], Temp: 0.0001, Energy: -54.619985+0.000185j
[2025-07-30 17:42:08] [Iter 4635/4650] R4[2384/2400], Temp: 0.0001, Energy: -54.601202+0.000930j
[2025-07-30 17:42:18] [Iter 4636/4650] R4[2385/2400], Temp: 0.0001, Energy: -54.580682-0.001522j
[2025-07-30 17:42:28] [Iter 4637/4650] R4[2386/2400], Temp: 0.0001, Energy: -54.564033+0.001655j
[2025-07-30 17:42:38] [Iter 4638/4650] R4[2387/2400], Temp: 0.0001, Energy: -54.590686-0.001388j
[2025-07-30 17:42:48] [Iter 4639/4650] R4[2388/2400], Temp: 0.0001, Energy: -54.595007-0.000425j
[2025-07-30 17:42:58] [Iter 4640/4650] R4[2389/2400], Temp: 0.0001, Energy: -54.582020+0.002444j
[2025-07-30 17:43:08] [Iter 4641/4650] R4[2390/2400], Temp: 0.0000, Energy: -54.624445-0.000036j
[2025-07-30 17:43:18] [Iter 4642/4650] R4[2391/2400], Temp: 0.0000, Energy: -54.560947+0.001010j
[2025-07-30 17:43:28] [Iter 4643/4650] R4[2392/2400], Temp: 0.0000, Energy: -54.650428+0.001631j
[2025-07-30 17:43:39] [Iter 4644/4650] R4[2393/2400], Temp: 0.0000, Energy: -54.622843-0.001673j
[2025-07-30 17:43:49] [Iter 4645/4650] R4[2394/2400], Temp: 0.0000, Energy: -54.604019+0.001188j
[2025-07-30 17:43:59] [Iter 4646/4650] R4[2395/2400], Temp: 0.0000, Energy: -54.628306+0.001446j
[2025-07-30 17:44:09] [Iter 4647/4650] R4[2396/2400], Temp: 0.0000, Energy: -54.586286+0.000831j
[2025-07-30 17:44:19] [Iter 4648/4650] R4[2397/2400], Temp: 0.0000, Energy: -54.596494+0.001125j
[2025-07-30 17:44:29] [Iter 4649/4650] R4[2398/2400], Temp: 0.0000, Energy: -54.629373+0.001925j
[2025-07-30 17:44:39] [Iter 4650/4650] R4[2399/2400], Temp: 0.0000, Energy: -54.629158-0.000571j
[2025-07-30 17:44:39] ✅ Training completed | Restarts: 4
[2025-07-30 17:44:39] ============================================================
[2025-07-30 17:44:39] Training completed | Runtime: 47081.5s
[2025-07-30 17:44:56] ✓ Final state saved: checkpoints/final_GCNN.pkl
[2025-07-30 17:44:56] ============================================================
