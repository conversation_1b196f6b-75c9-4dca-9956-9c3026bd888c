[2025-08-07 19:47:09] 使用checkpoint文件: results/L=4/J2=0.00/J1=0.05/training/checkpoints/checkpoint_iter_000300.pkl
[2025-08-07 19:47:16] ✓ 从checkpoint加载参数: 300
[2025-08-07 19:47:16]   - 能量: -55.031578+0.001756j ± 0.084872
[2025-08-07 19:47:16] ================================================================================
[2025-08-07 19:47:16] 加载量子态: L=4, J2=0.00, J1=0.05, checkpoint=checkpoint_iter_000300
[2025-08-07 19:47:16] 设置样本数为: 1048576
[2025-08-07 19:47:16] 开始生成共享样本集...
[2025-08-07 19:49:48] 样本生成完成,耗时: 151.838 秒
[2025-08-07 19:49:48] ================================================================================
[2025-08-07 19:49:48] 开始计算自旋结构因子...
[2025-08-07 19:49:48] 初始化操作符缓存...
[2025-08-07 19:49:48] 预构建所有自旋相关操作符...
[2025-08-07 19:49:48] 开始计算自旋相关函数...
[2025-08-07 19:49:58] 自旋算符进度: 1/64, 当前算符: S_0 · S_0, 耗时: 10.060s
[2025-08-07 19:50:12] 自旋算符进度: 2/64, 当前算符: S_0 · S_1, 耗时: 13.238s
[2025-08-07 19:50:20] 自旋算符进度: 3/64, 当前算符: S_0 · S_2, 耗时: 8.245s
[2025-08-07 19:50:28] 自旋算符进度: 4/64, 当前算符: S_0 · S_3, 耗时: 8.224s
[2025-08-07 19:50:36] 自旋算符进度: 5/64, 当前算符: S_0 · S_4, 耗时: 8.248s
[2025-08-07 19:50:45] 自旋算符进度: 6/64, 当前算符: S_0 · S_5, 耗时: 8.230s
[2025-08-07 19:50:53] 自旋算符进度: 7/64, 当前算符: S_0 · S_6, 耗时: 8.223s
[2025-08-07 19:51:01] 自旋算符进度: 8/64, 当前算符: S_0 · S_7, 耗时: 8.242s
[2025-08-07 19:51:09] 自旋算符进度: 9/64, 当前算符: S_0 · S_8, 耗时: 8.266s
[2025-08-07 19:51:17] 自旋算符进度: 10/64, 当前算符: S_0 · S_9, 耗时: 8.222s
[2025-08-07 19:51:26] 自旋算符进度: 11/64, 当前算符: S_0 · S_10, 耗时: 8.242s
[2025-08-07 19:51:34] 自旋算符进度: 12/64, 当前算符: S_0 · S_11, 耗时: 8.225s
[2025-08-07 19:51:42] 自旋算符进度: 13/64, 当前算符: S_0 · S_12, 耗时: 8.240s
[2025-08-07 19:51:50] 自旋算符进度: 14/64, 当前算符: S_0 · S_13, 耗时: 8.222s
[2025-08-07 19:51:59] 自旋算符进度: 15/64, 当前算符: S_0 · S_14, 耗时: 8.247s
[2025-08-07 19:52:07] 自旋算符进度: 16/64, 当前算符: S_0 · S_15, 耗时: 8.224s
[2025-08-07 19:52:15] 自旋算符进度: 17/64, 当前算符: S_0 · S_16, 耗时: 8.245s
[2025-08-07 19:52:23] 自旋算符进度: 18/64, 当前算符: S_0 · S_17, 耗时: 8.234s
[2025-08-07 19:52:32] 自旋算符进度: 19/64, 当前算符: S_0 · S_18, 耗时: 8.221s
[2025-08-07 19:52:40] 自旋算符进度: 20/64, 当前算符: S_0 · S_19, 耗时: 8.238s
[2025-08-07 19:52:48] 自旋算符进度: 21/64, 当前算符: S_0 · S_20, 耗时: 8.223s
[2025-08-07 19:52:56] 自旋算符进度: 22/64, 当前算符: S_0 · S_21, 耗时: 8.223s
[2025-08-07 19:53:05] 自旋算符进度: 23/64, 当前算符: S_0 · S_22, 耗时: 8.250s
[2025-08-07 19:53:13] 自旋算符进度: 24/64, 当前算符: S_0 · S_23, 耗时: 8.220s
[2025-08-07 19:53:21] 自旋算符进度: 25/64, 当前算符: S_0 · S_24, 耗时: 8.228s
[2025-08-07 19:53:29] 自旋算符进度: 26/64, 当前算符: S_0 · S_25, 耗时: 8.244s
[2025-08-07 19:53:37] 自旋算符进度: 27/64, 当前算符: S_0 · S_26, 耗时: 8.222s
[2025-08-07 19:53:46] 自旋算符进度: 28/64, 当前算符: S_0 · S_27, 耗时: 8.242s
[2025-08-07 19:53:54] 自旋算符进度: 29/64, 当前算符: S_0 · S_28, 耗时: 8.221s
[2025-08-07 19:54:02] 自旋算符进度: 30/64, 当前算符: S_0 · S_29, 耗时: 8.228s
[2025-08-07 19:54:10] 自旋算符进度: 31/64, 当前算符: S_0 · S_30, 耗时: 8.247s
[2025-08-07 19:54:19] 自旋算符进度: 32/64, 当前算符: S_0 · S_31, 耗时: 8.244s
[2025-08-07 19:54:27] 自旋算符进度: 33/64, 当前算符: S_0 · S_32, 耗时: 8.220s
[2025-08-07 19:54:35] 自旋算符进度: 34/64, 当前算符: S_0 · S_33, 耗时: 8.212s
[2025-08-07 19:54:43] 自旋算符进度: 35/64, 当前算符: S_0 · S_34, 耗时: 8.250s
[2025-08-07 19:54:52] 自旋算符进度: 36/64, 当前算符: S_0 · S_35, 耗时: 8.229s
[2025-08-07 19:55:00] 自旋算符进度: 37/64, 当前算符: S_0 · S_36, 耗时: 8.248s
[2025-08-07 19:55:08] 自旋算符进度: 38/64, 当前算符: S_0 · S_37, 耗时: 8.247s
[2025-08-07 19:55:16] 自旋算符进度: 39/64, 当前算符: S_0 · S_38, 耗时: 8.228s
[2025-08-07 19:55:25] 自旋算符进度: 40/64, 当前算符: S_0 · S_39, 耗时: 8.243s
[2025-08-07 19:55:33] 自旋算符进度: 41/64, 当前算符: S_0 · S_40, 耗时: 8.223s
[2025-08-07 19:55:41] 自旋算符进度: 42/64, 当前算符: S_0 · S_41, 耗时: 8.223s
[2025-08-07 19:55:49] 自旋算符进度: 43/64, 当前算符: S_0 · S_42, 耗时: 8.247s
[2025-08-07 19:55:57] 自旋算符进度: 44/64, 当前算符: S_0 · S_43, 耗时: 8.230s
[2025-08-07 19:56:06] 自旋算符进度: 45/64, 当前算符: S_0 · S_44, 耗时: 8.245s
[2025-08-07 19:56:14] 自旋算符进度: 46/64, 当前算符: S_0 · S_45, 耗时: 8.241s
[2025-08-07 19:56:22] 自旋算符进度: 47/64, 当前算符: S_0 · S_46, 耗时: 8.225s
[2025-08-07 19:56:30] 自旋算符进度: 48/64, 当前算符: S_0 · S_47, 耗时: 8.246s
[2025-08-07 19:56:39] 自旋算符进度: 49/64, 当前算符: S_0 · S_48, 耗时: 8.221s
[2025-08-07 19:56:47] 自旋算符进度: 50/64, 当前算符: S_0 · S_49, 耗时: 8.220s
[2025-08-07 19:56:55] 自旋算符进度: 51/64, 当前算符: S_0 · S_50, 耗时: 8.234s
[2025-08-07 19:57:03] 自旋算符进度: 52/64, 当前算符: S_0 · S_51, 耗时: 8.214s
[2025-08-07 19:57:12] 自旋算符进度: 53/64, 当前算符: S_0 · S_52, 耗时: 8.220s
[2025-08-07 19:57:20] 自旋算符进度: 54/64, 当前算符: S_0 · S_53, 耗时: 8.218s
[2025-08-07 19:57:28] 自旋算符进度: 55/64, 当前算符: S_0 · S_54, 耗时: 8.252s
[2025-08-07 19:57:36] 自旋算符进度: 56/64, 当前算符: S_0 · S_55, 耗时: 8.223s
[2025-08-07 19:57:44] 自旋算符进度: 57/64, 当前算符: S_0 · S_56, 耗时: 8.245s
[2025-08-07 19:57:53] 自旋算符进度: 58/64, 当前算符: S_0 · S_57, 耗时: 8.239s
[2025-08-07 19:58:01] 自旋算符进度: 59/64, 当前算符: S_0 · S_58, 耗时: 8.208s
[2025-08-07 19:58:09] 自旋算符进度: 60/64, 当前算符: S_0 · S_59, 耗时: 8.344s
[2025-08-07 19:58:17] 自旋算符进度: 61/64, 当前算符: S_0 · S_60, 耗时: 8.221s
[2025-08-07 19:58:26] 自旋算符进度: 62/64, 当前算符: S_0 · S_61, 耗时: 8.225s
[2025-08-07 19:58:34] 自旋算符进度: 63/64, 当前算符: S_0 · S_62, 耗时: 8.231s
[2025-08-07 19:58:42] 自旋算符进度: 64/64, 当前算符: S_0 · S_63, 耗时: 8.222s
[2025-08-07 19:58:42] 自旋相关函数计算完成,总耗时 533.89 秒
[2025-08-07 19:58:42] 计算傅里叶变换...
[2025-08-07 19:58:43] 自旋结构因子计算完成
[2025-08-07 19:58:43] 自旋相关函数平均误差: 0.000654
