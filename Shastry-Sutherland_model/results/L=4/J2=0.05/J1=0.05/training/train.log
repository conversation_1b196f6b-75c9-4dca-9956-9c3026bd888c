[2025-09-03 14:33:26] ==================================================
[2025-09-03 14:33:26] GCNN for Shastry-Sutherland Model
[2025-09-03 14:33:26] ==================================================
[2025-09-03 14:33:26] System parameters:
[2025-09-03 14:33:26]   - System size: L=4, N=64
[2025-09-03 14:33:26]   - System parameters: J1=0.05, J2=0.05, Q=0.95
[2025-09-03 14:33:26] --------------------------------------------------
[2025-09-03 14:33:26] Model parameters:
[2025-09-03 14:33:26]   - Number of layers = 4
[2025-09-03 14:33:26]   - Number of features = 4
[2025-09-03 14:33:26]   - Total parameters = 12572
[2025-09-03 14:33:26] --------------------------------------------------
[2025-09-03 14:33:26] Training parameters:
[2025-09-03 14:33:26]   - Learning rate: 0.015
[2025-09-03 14:33:26]   - Total iterations: 2250
[2025-09-03 14:33:26]   - Annealing cycles: 4
[2025-09-03 14:33:26]   - Initial period: 150
[2025-09-03 14:33:26]   - Period multiplier: 2.0
[2025-09-03 14:33:26]   - Temperature range: 0.0-1.0
[2025-09-03 14:33:26]   - Samples: 16384
[2025-09-03 14:33:26]   - Discarded samples: 0
[2025-09-03 14:33:26]   - Chunk size: 2048
[2025-09-03 14:33:26]   - Diagonal shift: 0.2
[2025-09-03 14:33:26]   - Gradient clipping: 1.0
[2025-09-03 14:33:26]   - Checkpoint enabled: interval=250
[2025-09-03 14:33:26]   - Checkpoint directory: results/L=4/J2=0.05/J1=0.05/training/checkpoints
[2025-09-03 14:33:26] --------------------------------------------------
[2025-09-03 14:33:26] Device status:
[2025-09-03 14:33:26]   - Devices model: NVIDIA H200 NVL
[2025-09-03 14:33:26]   - Number of devices: 1
[2025-09-03 14:33:26]   - Sharding: True
[2025-09-03 14:33:26] ============================================================
[2025-09-03 14:34:30] [Iter 1/2250] R0[0/150], Temp: 1.0000, Energy: 1.999447-0.000204j
[2025-09-03 14:35:05] [Iter 2/2250] R0[1/150], Temp: 0.9999, Energy: 1.999181-0.000099j
[2025-09-03 14:36:05] [Iter 3/2250] R0[2/150], Temp: 0.9996, Energy: 1.999051+0.000136j
[2025-09-03 14:37:05] [Iter 4/2250] R0[3/150], Temp: 0.9990, Energy: 1.999250-0.000439j
[2025-09-03 14:38:05] [Iter 5/2250] R0[4/150], Temp: 0.9982, Energy: 1.999254-0.000183j
[2025-09-03 14:39:05] [Iter 6/2250] R0[5/150], Temp: 0.9973, Energy: 1.999246-0.000076j
[2025-09-03 14:40:05] [Iter 7/2250] R0[6/150], Temp: 0.9961, Energy: 1.998957+0.000103j
[2025-09-03 14:41:05] [Iter 8/2250] R0[7/150], Temp: 0.9946, Energy: 1.998956-0.000060j
[2025-09-03 14:42:05] [Iter 9/2250] R0[8/150], Temp: 0.9930, Energy: 1.998902-0.000206j
[2025-09-03 14:43:05] [Iter 10/2250] R0[9/150], Temp: 0.9911, Energy: 1.999033+0.000059j
[2025-09-03 14:44:05] [Iter 11/2250] R0[10/150], Temp: 0.9891, Energy: 1.999290-0.000128j
[2025-09-03 14:45:05] [Iter 12/2250] R0[11/150], Temp: 0.9868, Energy: 1.999120+0.000031j
[2025-09-03 14:46:06] [Iter 13/2250] R0[12/150], Temp: 0.9843, Energy: 1.998825-0.000083j
[2025-09-03 14:47:06] [Iter 14/2250] R0[13/150], Temp: 0.9816, Energy: 1.999158-0.000154j
[2025-09-03 14:48:06] [Iter 15/2250] R0[14/150], Temp: 0.9787, Energy: 1.999190+0.000149j
[2025-09-03 14:49:06] [Iter 16/2250] R0[15/150], Temp: 0.9755, Energy: 1.999101-0.000268j
[2025-09-03 14:50:06] [Iter 17/2250] R0[16/150], Temp: 0.9722, Energy: 1.999202+0.000319j
[2025-09-03 14:51:07] [Iter 18/2250] R0[17/150], Temp: 0.9686, Energy: 1.998889-0.000115j
[2025-09-03 14:52:07] [Iter 19/2250] R0[18/150], Temp: 0.9649, Energy: 1.998793+0.000291j
[2025-09-03 14:53:07] [Iter 20/2250] R0[19/150], Temp: 0.9609, Energy: 1.999006+0.000193j
[2025-09-03 14:54:07] [Iter 21/2250] R0[20/150], Temp: 0.9568, Energy: 1.999125-0.000048j
[2025-09-03 14:55:07] [Iter 22/2250] R0[21/150], Temp: 0.9524, Energy: 1.998981-0.000111j
[2025-09-03 14:56:08] [Iter 23/2250] R0[22/150], Temp: 0.9479, Energy: 1.999116+0.000183j
[2025-09-03 14:57:08] [Iter 24/2250] R0[23/150], Temp: 0.9431, Energy: 1.999019-0.000157j
[2025-09-03 14:58:08] [Iter 25/2250] R0[24/150], Temp: 0.9382, Energy: 1.998683+0.000124j
[2025-09-03 14:59:08] [Iter 26/2250] R0[25/150], Temp: 0.9330, Energy: 1.999059-0.000087j
[2025-09-03 15:00:08] [Iter 27/2250] R0[26/150], Temp: 0.9277, Energy: 1.998546+0.000095j
[2025-09-03 15:01:08] [Iter 28/2250] R0[27/150], Temp: 0.9222, Energy: 1.998572+0.000058j
[2025-09-03 15:02:08] [Iter 29/2250] R0[28/150], Temp: 0.9165, Energy: 1.998698+0.000057j
[2025-09-03 15:03:08] [Iter 30/2250] R0[29/150], Temp: 0.9106, Energy: 1.998665+0.000086j
[2025-09-03 15:04:09] [Iter 31/2250] R0[30/150], Temp: 0.9045, Energy: 1.999267-0.000061j
[2025-09-03 15:05:09] [Iter 32/2250] R0[31/150], Temp: 0.8983, Energy: 1.998054+0.000296j
[2025-09-03 15:06:09] [Iter 33/2250] R0[32/150], Temp: 0.8918, Energy: 1.998328+0.000239j
[2025-09-03 15:07:09] [Iter 34/2250] R0[33/150], Temp: 0.8853, Energy: 1.998597+0.000250j
[2025-09-03 15:08:09] [Iter 35/2250] R0[34/150], Temp: 0.8785, Energy: 1.998222+0.000189j
[2025-09-03 15:09:09] [Iter 36/2250] R0[35/150], Temp: 0.8716, Energy: 1.998565+0.000089j
[2025-09-03 15:10:09] [Iter 37/2250] R0[36/150], Temp: 0.8645, Energy: 1.998539-0.000280j
[2025-09-03 15:11:09] [Iter 38/2250] R0[37/150], Temp: 0.8572, Energy: 1.998555-0.000102j
[2025-09-03 15:12:09] [Iter 39/2250] R0[38/150], Temp: 0.8498, Energy: 1.998856-0.000649j
[2025-09-03 15:13:09] [Iter 40/2250] R0[39/150], Temp: 0.8423, Energy: 1.998125-0.000348j
[2025-09-03 15:14:09] [Iter 41/2250] R0[40/150], Temp: 0.8346, Energy: 1.998584-0.000021j
[2025-09-03 15:15:10] [Iter 42/2250] R0[41/150], Temp: 0.8267, Energy: 1.997920+0.000149j
[2025-09-03 15:16:10] [Iter 43/2250] R0[42/150], Temp: 0.8187, Energy: 1.998348-0.000193j
[2025-09-03 15:17:10] [Iter 44/2250] R0[43/150], Temp: 0.8106, Energy: 1.998173+0.000043j
[2025-09-03 15:18:10] [Iter 45/2250] R0[44/150], Temp: 0.8023, Energy: 1.997812+0.000109j
[2025-09-03 15:19:10] [Iter 46/2250] R0[45/150], Temp: 0.7939, Energy: 1.997833+0.000105j
[2025-09-03 15:20:10] [Iter 47/2250] R0[46/150], Temp: 0.7854, Energy: 1.997982-0.000166j
[2025-09-03 15:21:10] [Iter 48/2250] R0[47/150], Temp: 0.7767, Energy: 1.998744-0.000500j
[2025-09-03 15:22:10] [Iter 49/2250] R0[48/150], Temp: 0.7679, Energy: 1.997472+0.000230j
[2025-09-03 15:23:10] [Iter 50/2250] R0[49/150], Temp: 0.7590, Energy: 1.998039-0.000275j
[2025-09-03 15:24:10] [Iter 51/2250] R0[50/150], Temp: 0.7500, Energy: 1.997479-0.000231j
[2025-09-03 15:25:10] [Iter 52/2250] R0[51/150], Temp: 0.7409, Energy: 1.997314+0.000012j
[2025-09-03 15:26:10] [Iter 53/2250] R0[52/150], Temp: 0.7316, Energy: 1.997709+0.000369j
[2025-09-03 15:27:11] [Iter 54/2250] R0[53/150], Temp: 0.7223, Energy: 1.996855+0.000058j
[2025-09-03 15:28:11] [Iter 55/2250] R0[54/150], Temp: 0.7129, Energy: 1.997917-0.000033j
[2025-09-03 15:29:11] [Iter 56/2250] R0[55/150], Temp: 0.7034, Energy: 1.996978+0.000007j
[2025-09-03 15:30:11] [Iter 57/2250] R0[56/150], Temp: 0.6938, Energy: 1.996265+0.000340j
[2025-09-03 15:31:11] [Iter 58/2250] R0[57/150], Temp: 0.6841, Energy: 1.996646+0.000218j
[2025-09-03 15:32:11] [Iter 59/2250] R0[58/150], Temp: 0.6743, Energy: 1.997634+0.000009j
[2025-09-03 15:33:11] [Iter 60/2250] R0[59/150], Temp: 0.6644, Energy: 1.997141-0.000021j
[2025-09-03 15:34:11] [Iter 61/2250] R0[60/150], Temp: 0.6545, Energy: 1.995691+0.000117j
[2025-09-03 15:35:11] [Iter 62/2250] R0[61/150], Temp: 0.6445, Energy: 1.996155+0.000362j
[2025-09-03 15:36:11] [Iter 63/2250] R0[62/150], Temp: 0.6345, Energy: 1.995529+0.000023j
[2025-09-03 15:37:11] [Iter 64/2250] R0[63/150], Temp: 0.6243, Energy: 1.994660+0.000070j
[2025-09-03 15:38:12] [Iter 65/2250] R0[64/150], Temp: 0.6142, Energy: 1.995829+0.000094j
[2025-09-03 15:39:12] [Iter 66/2250] R0[65/150], Temp: 0.6040, Energy: 1.995646+0.000319j
[2025-09-03 15:40:12] [Iter 67/2250] R0[66/150], Temp: 0.5937, Energy: 1.995264+0.000287j
[2025-09-03 15:41:12] [Iter 68/2250] R0[67/150], Temp: 0.5834, Energy: 1.996318-0.000322j
[2025-09-03 15:42:12] [Iter 69/2250] R0[68/150], Temp: 0.5730, Energy: 1.994356-0.000050j
[2025-09-03 15:43:12] [Iter 70/2250] R0[69/150], Temp: 0.5627, Energy: 1.993077+0.000765j
[2025-09-03 15:44:12] [Iter 71/2250] R0[70/150], Temp: 0.5523, Energy: 1.993988-0.000112j
[2025-09-03 15:45:12] [Iter 72/2250] R0[71/150], Temp: 0.5418, Energy: 1.993987+0.000031j
[2025-09-03 15:46:12] [Iter 73/2250] R0[72/150], Temp: 0.5314, Energy: 1.994080-0.000097j
[2025-09-03 15:47:12] [Iter 74/2250] R0[73/150], Temp: 0.5209, Energy: 1.992186+0.000499j
[2025-09-03 15:48:13] [Iter 75/2250] R0[74/150], Temp: 0.5105, Energy: 1.993921-0.000388j
[2025-09-03 15:49:13] [Iter 76/2250] R0[75/150], Temp: 0.5000, Energy: 1.989988+0.000549j
[2025-09-03 15:50:13] [Iter 77/2250] R0[76/150], Temp: 0.4895, Energy: 1.992254-0.000247j
[2025-09-03 15:51:13] [Iter 78/2250] R0[77/150], Temp: 0.4791, Energy: 1.989475-0.000380j
[2025-09-03 15:52:13] [Iter 79/2250] R0[78/150], Temp: 0.4686, Energy: 1.990795-0.000483j
[2025-09-03 15:53:13] [Iter 80/2250] R0[79/150], Temp: 0.4582, Energy: 1.988506+0.000127j
[2025-09-03 15:54:13] [Iter 81/2250] R0[80/150], Temp: 0.4477, Energy: 1.988450-0.000085j
[2025-09-03 15:55:13] [Iter 82/2250] R0[81/150], Temp: 0.4373, Energy: 1.988386+0.000076j
[2025-09-03 15:56:13] [Iter 83/2250] R0[82/150], Temp: 0.4270, Energy: 1.989814-0.000254j
[2025-09-03 15:57:13] [Iter 84/2250] R0[83/150], Temp: 0.4166, Energy: 1.988105-0.000291j
[2025-09-03 15:58:13] [Iter 85/2250] R0[84/150], Temp: 0.4063, Energy: 1.987650+0.000071j
[2025-09-03 15:59:13] [Iter 86/2250] R0[85/150], Temp: 0.3960, Energy: 1.983420+0.000690j
[2025-09-03 16:00:14] [Iter 87/2250] R0[86/150], Temp: 0.3858, Energy: 1.981669+0.000597j
[2025-09-03 16:01:14] [Iter 88/2250] R0[87/150], Temp: 0.3757, Energy: 1.983615-0.000729j
[2025-09-03 16:02:14] [Iter 89/2250] R0[88/150], Temp: 0.3655, Energy: 1.979654-0.000487j
[2025-09-03 16:03:14] [Iter 90/2250] R0[89/150], Temp: 0.3555, Energy: 1.977696+0.000789j
[2025-09-03 16:04:14] [Iter 91/2250] R0[90/150], Temp: 0.3455, Energy: 1.976635+0.000322j
[2025-09-03 16:05:14] [Iter 92/2250] R0[91/150], Temp: 0.3356, Energy: 1.976466-0.000401j
[2025-09-03 16:06:14] [Iter 93/2250] R0[92/150], Temp: 0.3257, Energy: 1.972764-0.000090j
[2025-09-03 16:07:14] [Iter 94/2250] R0[93/150], Temp: 0.3159, Energy: 1.970772+0.001041j
[2025-09-03 16:08:14] [Iter 95/2250] R0[94/150], Temp: 0.3062, Energy: 1.968084-0.000291j
[2025-09-03 16:09:14] [Iter 96/2250] R0[95/150], Temp: 0.2966, Energy: 1.970683-0.001129j
[2025-09-03 16:10:14] [Iter 97/2250] R0[96/150], Temp: 0.2871, Energy: 1.962219+0.000548j
[2025-09-03 16:11:14] [Iter 98/2250] R0[97/150], Temp: 0.2777, Energy: 1.960658+0.000179j
[2025-09-03 16:12:15] [Iter 99/2250] R0[98/150], Temp: 0.2684, Energy: 1.952778+0.001188j
[2025-09-03 16:13:15] [Iter 100/2250] R0[99/150], Temp: 0.2591, Energy: 1.955472-0.000160j
[2025-09-03 16:14:15] [Iter 101/2250] R0[100/150], Temp: 0.2500, Energy: 1.950269-0.001734j
[2025-09-03 16:15:15] [Iter 102/2250] R0[101/150], Temp: 0.2410, Energy: 1.942685+0.000985j
[2025-09-03 16:16:15] [Iter 103/2250] R0[102/150], Temp: 0.2321, Energy: 1.940611-0.000655j
[2025-09-03 16:17:15] [Iter 104/2250] R0[103/150], Temp: 0.2233, Energy: 1.936234-0.001655j
[2025-09-03 16:18:16] [Iter 105/2250] R0[104/150], Temp: 0.2146, Energy: 1.924698+0.001259j
[2025-09-03 16:19:16] [Iter 106/2250] R0[105/150], Temp: 0.2061, Energy: 1.922043-0.002148j
[2025-09-03 16:20:16] [Iter 107/2250] R0[106/150], Temp: 0.1977, Energy: 1.910807+0.000461j
[2025-09-03 16:21:16] [Iter 108/2250] R0[107/150], Temp: 0.1894, Energy: 1.898791+0.001626j
[2025-09-03 16:22:16] [Iter 109/2250] R0[108/150], Temp: 0.1813, Energy: 1.888510-0.000262j
[2025-09-03 16:23:16] [Iter 110/2250] R0[109/150], Temp: 0.1733, Energy: 1.887161-0.001923j
[2025-09-03 16:24:17] [Iter 111/2250] R0[110/150], Temp: 0.1654, Energy: 1.867673+0.001998j
[2025-09-03 16:25:17] [Iter 112/2250] R0[111/150], Temp: 0.1577, Energy: 1.855703+0.000971j
[2025-09-03 16:26:17] [Iter 113/2250] R0[112/150], Temp: 0.1502, Energy: 1.834906+0.000513j
[2025-09-03 16:27:17] [Iter 114/2250] R0[113/150], Temp: 0.1428, Energy: 1.828890-0.002233j
[2025-09-03 16:28:17] [Iter 115/2250] R0[114/150], Temp: 0.1355, Energy: 1.810994-0.003403j
[2025-09-03 16:29:17] [Iter 116/2250] R0[115/150], Temp: 0.1284, Energy: 1.775617-0.001434j
[2025-09-03 16:30:17] [Iter 117/2250] R0[116/150], Temp: 0.1215, Energy: 1.753855+0.000452j
[2025-09-03 16:31:18] [Iter 118/2250] R0[117/150], Temp: 0.1147, Energy: 1.735776-0.001953j
[2025-09-03 16:32:18] [Iter 119/2250] R0[118/150], Temp: 0.1082, Energy: 1.701742-0.000655j
[2025-09-03 16:33:18] [Iter 120/2250] R0[119/150], Temp: 0.1017, Energy: 1.671554+0.000549j
[2025-09-03 16:34:18] [Iter 121/2250] R0[120/150], Temp: 0.0955, Energy: 1.617729-0.000796j
[2025-09-03 16:35:18] [Iter 122/2250] R0[121/150], Temp: 0.0894, Energy: 1.585877+0.000544j
[2025-09-03 16:36:18] [Iter 123/2250] R0[122/150], Temp: 0.0835, Energy: 1.527934+0.001578j
[2025-09-03 16:37:19] [Iter 124/2250] R0[123/150], Temp: 0.0778, Energy: 1.480114+0.001518j
[2025-09-03 16:38:19] [Iter 125/2250] R0[124/150], Temp: 0.0723, Energy: 1.439287-0.002717j
[2025-09-03 16:39:19] [Iter 126/2250] R0[125/150], Temp: 0.0670, Energy: 1.365797+0.002255j
[2025-09-03 16:40:19] [Iter 127/2250] R0[126/150], Temp: 0.0618, Energy: 1.303281+0.003299j
[2025-09-03 16:41:19] [Iter 128/2250] R0[127/150], Temp: 0.0569, Energy: 1.214056+0.002464j
[2025-09-03 16:42:19] [Iter 129/2250] R0[128/150], Temp: 0.0521, Energy: 1.126474+0.007168j
[2025-09-03 16:43:20] [Iter 130/2250] R0[129/150], Temp: 0.0476, Energy: 1.043131+0.003022j
[2025-09-03 16:44:20] [Iter 131/2250] R0[130/150], Temp: 0.0432, Energy: 0.953267-0.000204j
[2025-09-03 16:45:20] [Iter 132/2250] R0[131/150], Temp: 0.0391, Energy: 0.840741-0.003712j
[2025-09-03 16:46:20] [Iter 133/2250] R0[132/150], Temp: 0.0351, Energy: 0.722695-0.004316j
[2025-09-03 16:47:20] [Iter 134/2250] R0[133/150], Temp: 0.0314, Energy: 0.632606-0.001082j
[2025-09-03 16:48:20] [Iter 135/2250] R0[134/150], Temp: 0.0278, Energy: 0.455630+0.006890j
[2025-09-03 16:49:21] [Iter 136/2250] R0[135/150], Temp: 0.0245, Energy: 0.323559+0.004672j
