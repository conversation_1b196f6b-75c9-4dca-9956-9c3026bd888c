import jax
import jax.numpy as jnp
import netket as nk
import time
from netket.experimental.driver.vmc_srt import VMC_SRt
from ansatz.transformer1d import Transformer_Enc

seed = 0

# 1D Lattice
L = 100
J2 = 0.8

# Settings optimization
diag_shift = 1e-3
eta = 0.01
N_opt = 10000
N_samples = 3000
N_discard = 0

# Settings wave function
f = 1
heads = 8
d_model = f * heads
b = 4

#! End input

lattice = nk.graph.Chain(length=L, pbc=True, max_neighbor_order=2)

# Hilbert space of spins on the graph
hilbert = nk.hilbert.Spin(s=1/2, N=lattice.n_nodes, total_sz=0)

# Heisenberg spin hamiltonian
hamiltonian = nk.operator.Heisenberg(hilbert=hilbert, graph=lattice, J = [1.0, J2])

# compute the ground-state energy
if (L <= 16):
    evals = nk.exact.lanczos_ed(hamiltonian, compute_eigenvectors=False)
    print('The exact ground-state energy is E0 = ', evals[0]/(4*L))

# Variational wave function
wf = Transformer_Enc(d_model=d_model,
                    h=heads,
                    L=L//b,
                    b=b)

key = jax.random.PRNGKey(seed)
key, subkey = jax.random.split(key, num=2)

params = wf.init(subkey, jnp.zeros((1,lattice.n_nodes)))
init_samples = jnp.zeros((1,))

# Metropolis Local Sampling
sampler = nk.sampler.MetropolisExchange(hilbert=hilbert,
                                        graph=lattice,
                                        d_max=2,
                                        n_chains=N_samples,
                                        n_sweeps=lattice.n_nodes)

key, subkey = jax.random.split(key, 2)
sampler_seed = subkey

vstate = nk.vqs.MCState(sampler=sampler, 
                        model=wf, 
                        sampler_seed=sampler_seed,
                        n_samples=N_samples, 
                        n_discard_per_chain=N_discard,
                        variables=params)

print('Number of parameters = ', nk.jax.tree_size(vstate.parameters))

# Variational monte carlo driver
optimizer = nk.optimizer.Sgd(learning_rate=eta)

vmc = VMC_SRt(hamiltonian=hamiltonian,
            optimizer=optimizer,
            diag_shift=diag_shift,
            variational_state=vstate)

# Optimization
start = time.time()
vmc.run(out = 'ViT', n_iter = N_opt)
end = time.time()

print('The calculation took ',end-start,' seconds')
