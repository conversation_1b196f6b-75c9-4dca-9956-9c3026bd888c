import os
import re
import numpy as np
import matplotlib.pyplot as plt
from matplotlib.ticker import MaxNLocator, ScalarFormatter, MultipleLocator
import matplotlib.cm as cm
from matplotlib.colors import LinearSegmentedColormap
from itertools import cycle

# 固定 base_dir 为脚本文件父目录的上层目录下的 "results"
script_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(script_dir)
base_dir = os.path.join(parent_dir, "results")
print("Fixed results directory:", base_dir)

def parse_energy_log(file_path):
    """
    Parse the specified energy log file, extract iteration steps and energy (complex form)
    """
    iterations = []
    energies = []
    
    # 更新正则表达式以匹配当前train.log格式: [Iter X/Y] R[0-9][Y/Z], Temp: Z, Energy: A+Bj
    pattern = (r"\[Iter\s*(\d+)/\d+\]\s*R[0-9]\[\d+/\d+\],\s*Temp:\s*([+-]?\d+(?:\.\d+)?),\s*Energy:\s*"
               r"([+-]?\d+(?:\.\d+)?)([+-]\d+(?:\.\d+)?)j")
    
    try:
        with open(file_path, 'r') as f:
            content = f.read()
    except Exception as e:
        print(f"Error reading file {file_path}: {e}")
        return None, None

    for match in re.finditer(pattern, content):
        try:
            iter_num = int(match.group(1))
            energy_real = float(match.group(3))
            energy_imag = float(match.group(4))
            energy = complex(energy_real, energy_imag)
            
            iterations.append(iter_num)
            energies.append(energy)
        except ValueError as ve:
            print(f"Error parsing file {file_path}: {ve}")
            continue
    
    return iterations, energies

def extract_params_from_path(file_path):
    """
    Extract parameters L, J1, J2 from the file path and return a dictionary containing these parameters
    """
    parts = os.path.normpath(file_path).split(os.sep)
    params = {}
    
    for part in parts:
        # Check if it contains parameters like L=4, J1=0.03, J2=0.05
        for param_prefix in ["L=", "J1=", "J2="]:
            if part.startswith(param_prefix):
                param_name = param_prefix.strip("=")
                param_value = part[len(param_prefix):]
                try:
                    # Convert value to numeric type
                    params[param_name] = (
                        int(param_value) if param_value.isdigit() else 
                        float(param_value)
                    )
                except ValueError:
                    params[param_name] = param_value
    
    return params

def extract_label_from_path(file_path, params, type="full"):
    """
    Generate labels based on directory information in the log file path
    type="full": L=X, J2=Y, J1=Z
    type="j1_only": J1=Z
    """
    if type == "j1_only":
        j1_val = params.get("J1")
        if j1_val is not None:
            return f"J1={j1_val:.2f}"
        return os.path.basename(file_path)

    # Build label in the order L, J2, J1 (type="full")
    ordered_params = []
    for param_name in ["L", "J2", "J1"]:
        if param_name in params:
            value = params[param_name]
            if isinstance(value, int):
                ordered_params.append(f"{param_name}={value}")
            else:
                ordered_params.append(f"{param_name}={value:.2f}")
    
    label = ", ".join(ordered_params) if ordered_params else os.path.basename(file_path)
    return label


def create_distinct_colormap(n_colors):
    """
    Create a widely distributed color map
    """
    if n_colors <= 0: # Handle case with no colors
        return lambda x: (0,0,0,1) # return black
    if n_colors <= 10:
        return cm.get_cmap('tab10', n_colors)
    elif n_colors <= 20:
        return cm.get_cmap('tab20', n_colors)
    else:
        # For more than 20 colors, hsv can repeat. Consider a more robust generator if needed.
        # For now, let's cycle through tab20 colors if n_colors > 20 for better distinction than raw hsv
        if n_colors > 20:
             colors = plt.cm.get_cmap('tab20').colors
             return lambda i: colors[i % len(colors)]
        return cm.get_cmap('hsv', n_colors)


def get_param_value(curve_data_or_params, param_name):
    """
    Extract the value of the specified parameter from the curve label or parameter dictionary, for sorting
    curve_data_or_params can be (iterations, energies, label) or params_dict
    """
    if isinstance(curve_data_or_params, dict): # It's a params_dict
        val = curve_data_or_params.get(param_name)
        if val is not None:
            return val
        return float('inf')

    # It's curve_data (iterations, energies, label, ...)
    # The label is expected to be at index 2
    if len(curve_data_or_params) > 2:
        label = curve_data_or_params[2]
        pattern = rf"{param_name}=(\d+(?:\.\d+)?)"
        match = re.search(pattern, label)
        if match:
            value_str = match.group(1)
            return int(value_str) if value_str.isdigit() else float(value_str)
    return float('inf')


def calculate_adaptive_threshold(iterations, threshold_config="adaptive"):
    """
    计算自适应的稳定迭代阈值
    
    参数:
    - iterations: 迭代次数列表
    - threshold_config: 阈值配置，可以是数字（固定值）或"adaptive"（自适应）
    
    返回:
    - threshold: 计算得出的阈值
    """
    if isinstance(threshold_config, (int, float)):
        return threshold_config
    
    if not iterations:
        return 500  # 默认值
    
    max_iter = max(iterations)
    
    # 自适应阈值策略
    if max_iter <= 200:
        # 非常短的训练：取50%，但最少10个点
        threshold = max(max_iter * 0.5, min(10, max_iter * 0.1))
    elif max_iter <= 500:
        # 短训练：取70%
        threshold = max_iter * 0.7
    elif max_iter <= 1200:
        # 中等长度训练：取60%
        threshold = max_iter * 0.6
    elif max_iter <= 3000:
        # 较长训练：取50%或固定值
        threshold = max(max_iter * 0.5, 500)
    else:
        # 很长的训练：取固定值
        threshold = max(500, max_iter * 0.3)
    
    # 确保阈值合理：至少留下20个点用于计算平均值
    min_points_needed = 20
    threshold = min(threshold, max_iter - min_points_needed)
    threshold = max(threshold, max_iter * 0.1)  # 至少取10%
    
    return int(threshold)


def sort_curves_by_j1(curves_with_params):
    """
    Sort curves by J1 value from smallest to largest
    curves_with_params is a list of (iterations, normalized_energies, base_label, params_dict)
    """
    return sorted(curves_with_params, key=lambda x: get_param_value(x[3], "J1"))


def scan_and_plot_grouped_energy_logs(output_prefix="energy_summary", show_plot=True, 
                                     y_min_stable=None, y_max_stable=None, auto_adjust_x_stable=False,
                                     stable_iter_threshold="adaptive"):  # parameter: stable iteration threshold
    """
    Scan all train.log files in the fixed base_dir directory,
    Group by (L, J2) value to plot charts, each group chart contains three subplots:
    1. Energy vs. Iteration
    2. Energy (After Iter 600) vs. Iteration
    3. E_avg vs J1
    Images are saved in the results/L=<L_val>/J2=<J2_val>/ directory.
    """
    all_curves_data = []  # Store (iterations, normalized_energies, params_dict)
    
    for root, dirs, files in os.walk(base_dir):
        for file in files:
            if file == "train.log":
                file_path = os.path.join(root, file)
                iterations, energies = parse_energy_log(file_path)
                if iterations and energies:
                    params = extract_params_from_path(file_path)
                    L_val = params.get("L")
                    if L_val:
                        norm_factor = 4 * (L_val ** 2)
                    else:
                        norm_factor = 1 # Should not happen if we group by L
                        
                    normalized_energies = [e.real / norm_factor for e in energies]
                    all_curves_data.append({"iterations": iterations, 
                                            "norm_energies": normalized_energies, 
                                            "params": params,
                                            "file_path": file_path}) # Add file_path for label generation
    
    if not all_curves_data:
        print("No train.log files found.")
        return

    # Group curves by (L, J2) value
    LJ2_grouped_curves = {}
    for curve_data in all_curves_data:
        params = curve_data["params"]
        L_val = params.get("L")
        J2_val = params.get("J2")
        
        if L_val is not None and J2_val is not None:
            key = (L_val, J2_val)
            if key not in LJ2_grouped_curves:
                LJ2_grouped_curves[key] = []
            LJ2_grouped_curves[key].append(curve_data)

    # For each (L, J2) combination, draw a separate chart
    for (L_val, J2_val), curves_in_group in sorted(LJ2_grouped_curves.items()):
        
        # Sort curves in the current group by J1
        sorted_curves_for_LJ2 = sorted(curves_in_group, key=lambda x: get_param_value(x["params"], "J1"))

        if not sorted_curves_for_LJ2:
            continue

        fig, (ax1, ax2, ax3) = plt.subplots(3, 1, figsize=(14, 21)) # Adjusted figsize
        plt.subplots_adjust(right=0.85, hspace=0.3) # Make space for legend and between subplots

        # Create color map (based on number of J1)
        num_j1_values = len(sorted_curves_for_LJ2)
        cmap = create_distinct_colormap(num_j1_values)
        
        stable_curves_for_plot = [] # (stable_iterations, stable_energies, label_j1_only, params)
        e_avg_vs_j1_data = [] # (J1_val, stable_avg)

        # --- Subplot 1: Energy vs. Iteration ---
        ax1.set_title(f"Energy vs. Iteration (L={L_val}, J2={J2_val:.2f})", fontsize=16)
        ax1.set_xlabel("Iteration", fontsize=14)
        ax1.set_ylabel("Normalized Energy (Real)", fontsize=14)
        ax1.grid(True, linestyle="--", alpha=0.6)
        ax1.xaxis.set_major_locator(MaxNLocator(integer=True))

        for i, curve_data in enumerate(sorted_curves_for_LJ2):
            iterations = curve_data["iterations"]
            norm_energies = curve_data["norm_energies"]
            params = curve_data["params"]
            
            # Generate J1-based label
            label_j1_only = extract_label_from_path(curve_data["file_path"], params, type="j1_only")
            
            # Calculate adaptive threshold for this specific curve
            adaptive_threshold = calculate_adaptive_threshold(iterations, stable_iter_threshold)
            
            # Check if there are iteration points above the threshold
            has_stable_part = False
            stable_idx = 0  # Initialize to 0
            
            # Find the index of the first iteration point greater than the threshold
            for idx, iter_val in enumerate(iterations):
                if iter_val >= adaptive_threshold:
                    stable_idx = idx
                    has_stable_part = True
                    break
            
            # Calculate average energy (if there are points above the threshold)
            if has_stable_part:
                stable_iterations = iterations[stable_idx:]
                stable_energies = norm_energies[stable_idx:]
                stable_avg = np.mean(stable_energies)
                
                plot_label = f"{label_j1_only}, E_avg≈{stable_avg:.4f} (iter>{adaptive_threshold})"
                stable_curves_for_plot.append((stable_iterations, stable_energies, label_j1_only, params))
                e_avg_vs_j1_data.append((params.get("J1", float('nan')), stable_avg))
            else:
                plot_label = f"{label_j1_only}, No data after iter {adaptive_threshold}"
                e_avg_vs_j1_data.append((params.get("J1", float('nan')), np.nan))

            ax1.plot(iterations, norm_energies, linewidth=2, label=plot_label, color=cmap(i))
            
            # Remove vertical line marking
        
        ax1.legend(fontsize=10, loc="center left", bbox_to_anchor=(1.02, 0.5))

        # --- Subplot 2: Energy (After Adaptive Threshold) vs. Iteration ---
        # 为第二个子图计算代表性阈值（取当前组中的平均值或最常见值）
        if sorted_curves_for_LJ2:
            thresholds = [calculate_adaptive_threshold(cd["iterations"], stable_iter_threshold) for cd in sorted_curves_for_LJ2]
            representative_threshold = int(np.mean(thresholds))
        else:
            representative_threshold = 500
        ax2.set_title(f"Energy (After Adaptive Iter Threshold ≈{representative_threshold}) vs. Iteration (L={L_val}, J2={J2_val:.2f})", fontsize=16)
        ax2.set_xlabel("Iteration", fontsize=14)
        ax2.set_ylabel("Normalized Energy (Real)", fontsize=14)
        ax2.grid(True, linestyle="--", alpha=0.6)
        ax2.xaxis.set_major_locator(MaxNLocator(integer=True))

        if stable_curves_for_plot:
            # Re-sort stable_curves_for_plot by J1 for consistent coloring with cmap
            stable_curves_for_plot_sorted = sorted(stable_curves_for_plot, key=lambda x: get_param_value(x[3], "J1"))
            
            # Need to map original curve index to color, or re-create cmap based on stable curves.
            # For simplicity, let's find the original index for color mapping.
            original_j1_values = [get_param_value(cd["params"], "J1") for cd in sorted_curves_for_LJ2]

            # Find min and max y values for auto-scaling the y-axis
            y_min_auto = float('inf')
            y_max_auto = float('-inf')

            for stable_data in stable_curves_for_plot_sorted:
                stable_iters, stable_nrg, lbl_j1, params_stable = stable_data
                
                # Update min/max for auto-scaling
                if len(stable_nrg) > 0:
                    y_min_auto = min(y_min_auto, min(stable_nrg))
                    y_max_auto = max(y_max_auto, max(stable_nrg))
                
                current_j1 = get_param_value(params_stable, "J1")
                try:
                    color_idx = original_j1_values.index(current_j1)
                    color_to_use = cmap(color_idx)
                except ValueError: # Should not happen if logic is correct
                    color_to_use = "gray" 

                # 计算当前曲线的自适应阈值用于标签显示
                current_threshold = calculate_adaptive_threshold(stable_iters, stable_iter_threshold)
                stable_plot_label = f"{lbl_j1}, From iter {current_threshold}"
                ax2.plot(stable_iters, stable_nrg, linewidth=2, label=stable_plot_label, color=color_to_use)
            
            ax2.legend(fontsize=10, loc="center left", bbox_to_anchor=(1.02, 0.5))
            
            # Set auto y-axis range with 5% padding
            if y_min_auto != float('inf') and y_max_auto != float('-inf'):
                y_range = y_max_auto - y_min_auto
                if y_range == 0:  # Handle case where all values are the same
                    y_range = abs(y_min_auto) * 0.1 if y_min_auto != 0 else 0.1
                
                # Calculate appropriate y limits while ensuring they're multiples of 0.01
                padding = y_range * 0.05
                y_min_plot = y_min_auto - padding
                y_max_plot = y_max_auto + padding
                
                # Round to nearest 0.01 while ensuring all data is included
                y_min_plot = np.floor(y_min_plot * 100) / 100  # Round down to nearest 0.01
                y_max_plot = np.ceil(y_max_plot * 100) / 100   # Round up to nearest 0.01
                
                ax2.set_ylim(y_min_plot, y_max_plot)
                
                # Set y-axis tick interval to 0.01
                ax2.yaxis.set_major_locator(MultipleLocator(0.01))
                
                print(f"L={L_val}, J2={J2_val:.2f}: Auto-adjusted y-axis range to {y_min_plot:.2f} - {y_max_plot:.2f}")
            
            # Use user-specified y-axis limits if provided (overrides auto)
            if y_min_stable is not None or y_max_stable is not None:
                ax2.set_ylim(bottom=y_min_stable, top=y_max_stable)
                
                if auto_adjust_x_stable and y_min_stable is not None and y_max_stable is not None:
                    x_points = []
                    for iter_list, nrg_list, _, _ in stable_curves_for_plot_sorted:
                        for i_x, e_val in enumerate(nrg_list):
                            if (y_min_stable is None or e_val >= y_min_stable) and \
                               (y_max_stable is None or e_val <= y_max_stable):
                                x_points.append(iter_list[i_x])
                    if x_points:
                        x_min_adj, x_max_adj = min(x_points), max(x_points)
                        padding = (x_max_adj - x_min_adj) * 0.05 if x_max_adj > x_min_adj else x_max_adj * 0.05 + 1
                        ax2.set_xlim(x_min_adj - padding, x_max_adj + padding)
                        print(f"L={L_val}, J2={J2_val:.2f}: Adjusted x-axis range for specified y-range.")
                    else:
                        print(f"L={L_val}, J2={J2_val:.2f}: No data in specified y-range for x-axis adjustment.")

        else:
            ax2.text(0.5, 0.5, f"No data found after adaptive iteration threshold", 
                     horizontalalignment='center', verticalalignment='center', transform=ax2.transAxes)

        # --- Subplot 3: E_avg vs J1 ---
        ax3.set_title(f"Average Stable Energy vs. J1 (L={L_val}, J2={J2_val:.2f})", fontsize=16)
        ax3.set_xlabel("J1", fontsize=14)
        ax3.set_ylabel("Average Stable Energy (Normalized)", fontsize=14)
        ax3.grid(True, linestyle="--", alpha=0.6)

        if e_avg_vs_j1_data:
            # Sort by J1 for plotting
            e_avg_vs_j1_data_sorted = sorted(e_avg_vs_j1_data, key=lambda x: x[0])
            
            # Filter out NaN values
            valid_data = [(j1, e_avg) for j1, e_avg in e_avg_vs_j1_data_sorted if not np.isnan(e_avg)]
            
            if valid_data:
                j1_coords = [item[0] for item in valid_data]
                e_avg_coords = [item[1] for item in valid_data]
                
                # Determine y-axis limits for subplot 3
                y_min_e_avg = min(e_avg_coords)
                y_max_e_avg = max(e_avg_coords)
                
                # Calculate sensible range and padding
                y_range_e_avg = y_max_e_avg - y_min_e_avg
                if y_range_e_avg == 0:  # Handle case where all values are the same
                    y_range_e_avg = abs(y_min_e_avg) * 0.1 if y_min_e_avg != 0 else 0.1
                
                # Add padding (5% of range on each side)
                padding_e_avg = y_range_e_avg * 0.05
                
                # Round to nearest 0.01 while ensuring all data is included
                y_min_e_avg_plot = np.floor((y_min_e_avg - padding_e_avg) * 100) / 100  # Round down
                y_max_e_avg_plot = np.ceil((y_max_e_avg + padding_e_avg) * 100) / 100   # Round up
                
                ax3.set_ylim(y_min_e_avg_plot, y_max_e_avg_plot)
                
                # Set y-axis tick interval to 0.01
                ax3.yaxis.set_major_locator(MultipleLocator(0.01))
                
                # Use ScalarFormatter to maintain the same scale
                ax3.yaxis.set_major_formatter(ScalarFormatter())
                
                # Plot points and a connecting line
                ax3.plot(j1_coords, e_avg_coords, marker='o', linestyle='-', color='teal')
                
                # Add text labels
                for j1_val, e_avg_val in valid_data:
                    ax3.text(j1_val, e_avg_val, f" {e_avg_val:.4f}", fontsize=9, va='bottom')
            else:
                ax3.text(0.5, 0.5, "No valid average energy data to plot.", 
                         horizontalalignment='center', verticalalignment='center', transform=ax3.transAxes)
        else:
            ax3.text(0.5, 0.5, "No average energy data to plot.", 
                     horizontalalignment='center', verticalalignment='center', transform=ax3.transAxes)

        # --- Saving the Figure ---
        output_dir = os.path.join(base_dir, f"L={L_val}", f"J2={J2_val:.2f}")
        os.makedirs(output_dir, exist_ok=True)
        
        output_file_name = f"{output_prefix}_L{L_val}_J2_{J2_val:.2f}.png"
        output_file_path = os.path.join(output_dir, output_file_name)
        
        try:
            plt.savefig(output_file_path, dpi=300, bbox_inches='tight') # bbox_inches='tight' helps with legend
            print(f"Chart saved to: {output_file_path}")
        except Exception as e:
            print(f"Failed to save chart {output_file_path}: {e}")

        if show_plot:
            plt.show()
        plt.close(fig) # Close the figure to free memory

    if not LJ2_grouped_curves:
        print("No data found for grouping.")


if __name__ == "__main__":
    scan_and_plot_grouped_energy_logs(
        output_prefix="energy_analysis",  # Changed prefix
        show_plot=False, # Typically False for batch processing
        y_min_stable=None,  # Changed to None to auto-adjust
        y_max_stable=None,  # Changed to None to auto-adjust
        auto_adjust_x_stable=True,
        stable_iter_threshold="adaptive"  # Use adaptive threshold based on actual iteration counts
    )
    print("All chart processing completed.") 